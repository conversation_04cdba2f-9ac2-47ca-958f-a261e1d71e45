﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Common;
using System.Configuration;

namespace Web.Service
{
    public partial class GetWXOpenID : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        public void ProcessRequest(HttpContext context)
        {

            //context.Response.ContentType = "text/plain";
            //context.Response.Write("Hello World");

            //string postString = string.Empty;
            if (HttpContext.Current.Request.HttpMethod.ToUpper() != "GET")
            {

            }
            else
            {
                auth();
            }
        }

        private void auth()
        {
            //#region 获取关键参数
            //string token = ConfigurationManager.AppSettings["CorpToken"];//从配置文件获取Token   "ycAiSOXnJF"; //
            //string encodingAESKey = ConfigurationManager.AppSettings["EncodingAESKey"];//从配置文件获取EncodingAESKey   "mlYSndsylCAgto4Gdsxjbbi2hqaG42NJluE34yNTW2W"; //
            //string corpId = ConfigurationManager.AppSettings["CorpId"];//从配置文件获取corpId   "wx9824d60e1f9ee4ab"; //
            //#endregion

            #region 获取关键参数
            string token = "ycAiSOXnJF"; //ConfigurationManager.AppSettings["CorpToken"];//从配置文件获取Token  
            string encodingAESKey = "mlYSndsylCAgto4Gdsxjbbi2hqaG42NJluE34yNTW2W"; //ConfigurationManager.AppSettings["EncodingAESKey"];//从配置文件获取EncodingAESKey  
            string corpId = "wx9f1cde5f70b71497"; //ConfigurationManager.AppSettings["CorpId"];//从配置文件获取corpId  
            #endregion

            string echoString = HttpContext.Current.Request.QueryString["echoStr"];
            string signature = HttpContext.Current.Request.QueryString["msg_signature"];//企业号的 msg_signature  
            string timestamp = HttpContext.Current.Request.QueryString["timestamp"];
            string nonce = HttpContext.Current.Request.QueryString["nonce"];
            string sPath = HttpContext.Current.Server.MapPath("/");
            System.IO.File.WriteAllText(HttpContext.Current.Server.MapPath("/") + "a.txt", echoString + "," + signature + "," + timestamp + "," + nonce);

            sPath = sPath + echoString + "，" + signature + "，" + timestamp + "，" + nonce;
            string sSQL = " insert into T_Temp(MaterNo) values('" + sPath + "')";
            Common.DBHelper.ExecuteCommand(sSQL);

            
            string decryptEchoString = "";
            if (CheckSignature(token, signature, timestamp, nonce, corpId, encodingAESKey, echoString, ref decryptEchoString))
            {
                if (!string.IsNullOrEmpty(decryptEchoString))
                {
                    HttpContext.Current.Response.Write(decryptEchoString);
                    HttpContext.Current.Response.End();
                }
            }
        }

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }

        public bool CheckSignature(string token, string signature, string timestamp, string nonce, string corpId, string encodingAESKey, string echostr, ref string retEchostr)
        {
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(token, encodingAESKey, corpId);
            int result = wxcpt.VerifyURL(signature, timestamp, nonce, echostr, ref retEchostr);
            if (result != 0)
            {
                //LogTextHelper.Error("ERR: VerifyURL fail, ret: " + result);

                return false;
            }

            return true;
            //ret==0表示验证成功，retEchostr参数表示明文，用户需要将retEchostr作为get请求的返回参数，返回给企业号。  
            // HttpUtils.SetResponse(retEchostr);  
        }
    }
}