﻿using BLL;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Web;

namespace Web.Echart.Common
{
    public class TimerTaskManager
    {
        private readonly static ConcurrentDictionary<string, Timer> _timers = new ConcurrentDictionary<string, Timer>();

        // 私有静态实例  
        private static TimerTaskManager _instance;

        // 私有构造函数，防止外部直接实例化  
        private TimerTaskManager() { }

        // 公共静态方法获取实例  
        public static TimerTaskManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new TimerTaskManager();
                }
                return _instance;
            }
        }

        // 添加定时任务（支持异步回调）  
        public void AddTimerTask(string taskId, TimeSpan initialDelay, TimeSpan interval, string sLogin)
        {
            var timerInfo = new Timer(state => ExecuteTask(taskId, sLogin), null, initialDelay, interval);
            _timers.TryAdd(taskId, timerInfo);
        }

        private void ExecuteTask(string taskId, string sLogin)
        {
            DataTable dt = EchartBll.GetTemplateBase(taskId, "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", 20, 1, sLogin, "", "AddSample");
            if (dt.Rows.Count > 0)
            {
                if (dt.Rows[0]["Result"].ToString() == "OK")
                {
                    _instance.RemoveTimerTask(dt.Rows[0]["TaskNo"].ToString());
                }
            }
        }


        // 移除定时任务  
        public void RemoveTimerTask(string taskId)
        {
            if (_timers.TryRemove(taskId, out Timer timerInfo))
            {
                timerInfo.Dispose();
            }
        }

        // 移除所有定时任务  
        public void RemoveAllTimerTasks()
        {
            foreach (var timerInfo in _timers.Values)
            {
                timerInfo.Dispose(); // 释放定时器资源  
            }
            _timers.Clear(); // 清除字典中的引用  
        }
    }
}