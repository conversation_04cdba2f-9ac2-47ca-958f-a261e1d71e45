﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using DAL;


namespace BLL
{
    public class PrdAssistBll
    {

        /// <summary>
        /// 判断信息是否存在
        /// </summary>
        /// <param name="Kind"></param>
        /// <param name="KindList"></param>
        /// <param name="QT"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string JudgeObjectExist(string Kind, string KindList, string QT, string sComp, string sFlag)
        {
            return PrdAssistDal.JudgeObjectExist(Kind, KindList, QT, sComp, sFlag);
        }

        public static DataTable GetCompanyInfo(string LoginMan, string sComp, string sFlag)
        {
            return PrdAssistDal.GetCompanyInfo(LoginMan, sComp, sFlag);
        }

        public static DataTable GetPrdAssistStatusForNo(string No, string Ver, string sComp, string sFlag)
        {
            return PrdAssistDal.GetPrdAssistStatusForNo(No, Ver, sComp, sFlag);
            //return PrdAssistDal.GetCompanyInfo(LoginMan, sComp, sFlag);
        }

        /// <summary>
        /// 获取生产相关的基础信息
        /// </summary>
        /// <param name="No"></param>
        /// <param name="Name"></param>
        /// <param name="Item"></param>
        /// <param name="Status"></param>
        /// <param name="BDate"></param>
        /// <param name="EDate"></param>
        /// <param name="A"></param>
        /// <param name="B"></param>
        /// <param name="C"></param>
        /// <param name="D"></param>
        /// <param name="E"></param>
        /// <param name="Row"></param>
        /// <param name="num"></param>
        /// <param name="sInMan"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetPrdAssistInfo(string No, string Name, string Item, string Status, string BDate, string EDate, string A, string B, string C, string D, string E, int Row, int num, string sInMan, string sComp, string sFlag)
        {
            return PrdAssistDal.GetPrdAssistInfo(No, Name, Item, Status, BDate, EDate, A, B, C, D, E, Row, num, sInMan, sComp, sFlag);
        }

        ///  <summary>
        /// 返回仓库信息树形结构
        /// </summary>
        /// <param name="FNo"></param>
        /// <param name="FName"></param>
        /// <param name="CNo"></param>
        /// <param name="CName"></param>
        /// <param name="GX"></param>
        /// <param name="Row"></param>
        /// <param name="num"></param>
        /// <param name="Comp"></param>
        /// <returns></returns>
        public static DataTable GeWareHouseInfo(string FNo, string FName, string CNo, string CName, string GX, int Row, int num, string Comp)
        {
            return PrdAssistDal.GetWareHouseInfo(FNo, FName, CNo, CName, GX, Row, num, Comp);
        }



        /// <summary>
        /// 操作生产相关基础信息
        /// </summary>
        /// <param name="No"></param>
        /// <param name="Name"></param>
        /// <param name="Item"></param>
        /// <param name="A"></param>
        /// <param name="B"></param>
        /// <param name="C"></param>
        /// <param name="D"></param>
        /// <param name="E"></param>
        /// <param name="F"></param>
        /// <param name="G"></param>
        /// <param name="H"></param>
        /// <param name="I"></param>
        /// <param name="J"></param>
        /// <param name="K"></param>
        /// <param name="L"></param>
        /// <param name="Kind"></param>
        /// <param name="sComp"></param>
        /// <param name="InMan"></param>
        /// <param name="Remark"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>

        public static string OPPrdAssistInfo(string No, string Name, string Item, string A, string B, string C, string D, string E, string F, string G, string H, string I, string J, string K, string L, string Kind, string sComp, string InMan, string Remark, string sFlag)
        {
            return PrdAssistDal.OPPrdAssistInfo(No, Name, Item, A, B, C, D, E, F, G, H, I, J, K, L, Kind, sComp, InMan, Remark, sFlag);
        }



        /// <summary>
        /// 质量控制相关数据更新
        /// </summary>
        /// <param name="No"></param>
        /// <param name="Name"></param>
        /// <param name="Item"></param>
        /// <param name="A"></param>
        /// <param name="B"></param>
        /// <param name="C"></param>
        /// <param name="D"></param>
        /// <param name="E"></param>
        /// <param name="F"></param>
        /// <param name="G"></param>
        /// <param name="H"></param>
        /// <param name="I"></param>
        /// <param name="J"></param>
        /// <param name="K"></param>
        /// <param name="L"></param>
        /// <param name="Kind"></param>
        /// <param name="sComp"></param>
        /// <param name="InMan"></param>
        /// <param name="Remark"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string OPPrdAssistQCInfo(string No, string Name, string Item, string SNo, string A,
             string B, string C, string D, string E, string F, string G, string H, string I, string J, string K,
             string L, string M, string N, string O, string P, string Q, string R, string S, string T, string U, string V, string W,
             string X, string Kind, string sComp, string InMan, string Remark, string sFlag)
        {
            return PrdAssistDal.OPPrdAssistQCInfo(No, Name, Item, SNo, A, B, C, D, E, F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T, U, V, W, X, Kind, sComp, InMan, Remark, sFlag);

        }




        public static Dictionary<string, object> GenerateRecordFile(string No, string Name, string Item, string Status, string A, string B, string C, string D, string Remark, string sComp, string sInMan, string Flag)
        {
            return PrdAssistDal.GenerateRecordFile(No, Name, Item, Status, A, B, C, D, Remark, sComp, sInMan, Flag);
        }







    }
}

