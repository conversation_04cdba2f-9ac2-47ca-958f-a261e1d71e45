﻿using System;
using System.Collections;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Web.UI.WebControls;
using System.Xml.Linq;
using BLL;
using Newtonsoft.Json;
using System.Web.SessionState;
using Common;
using System.Collections.Generic;
using WXCommon;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using NPOI.HSSF.UserModel;
using System.Text;
using System.IO;
using grsvr6Lib;
using Web.appcode;
using static ICSharpCode.SharpZipLib.Zip.ExtendedUnixData;
using NPOI.SS.Formula.Functions;
using Org.BouncyCastle.Asn1.Ocsp;

namespace Web.Service
{
    /// <summary>
    /// PrdAssistAjax 的摘要说明
    /// </summary>
    public class PrdAssistAjax : IHttpHandler, IRequiresSessionState
    {
        string sAMFlag = string.Empty;

        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "text/plain";
            string Result = string.Empty;
            string Operate = context.Request.Params["OP"];//获取传入的操作过程
            string DataParams = context.Request.Params["Data"];//获取传入的Data值
            string sItem = context.Request.Params["Item"];
            sAMFlag = context.Request.Params["CFlag"];//获取传入的更新状态

            string sKind = context.Request.Params["CKind"];  // 接收所有类别
            string sStatus = context.Request.Params["CStatus"];  // 状态
            string sCNo = context.Request.Params["CNO"];  // 接收所有编号  
            string sMNo = context.Request.Params["CMNO"];  // 物料编码 
            string sPNo = context.Request.Params["CPNO"];  //  
            string sSPNo = context.Request.Params["CSPNO"];  //  
            string sCName = context.Request.Params["CNAME"];  // 接收所有名称
            string sCustNo = context.Request.Params["CCustNo"];
            string sAddNum = context.Request.Params["AddNum"];  // 数据加载的次数
            int slimit = 0;
            int spage = 0;


            switch (Operate)
            {

                case "GetPrdAssistInfo":  // 获取生产相关基础信息
                    slimit = int.Parse(context.Request.Params["limit"]);
                    spage = int.Parse(context.Request.Params["page"]);
                    GetPrdAssistInfo(DataParams, slimit, spage, sStatus, sCNo, sItem, sAMFlag);
                    break;


                case "OPPrdAssistInfo": //    对生产及相关基础信息进行增删改操作
                    Result = OPPrdAssistInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "OPPrdAssistQCInfo": //    对生产及相关基础信息进行增删改操作
                    Result = OPPrdAssistQCInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetOrderInfoByNo": //根据工单号，返回工单基本信息
                    GetOrderInfoByNo(DataParams);
                    break;

                case "GetWareHouseInfo": //获取仓库信息构造树形
                    slimit = 30;//int.Parse(context.Request.Params["limit"]);
                    spage = 1;//int.Parse(context.Request.Params["page"]);
                    GetWareHouseInfo(DataParams, slimit, spage, sAMFlag);
                    break;

                case "GetCompanyInfo": //获取仓库信息构造树形
                    GetCompanyInfo(sAMFlag);
                    break;
                case "GetPrdAssistStatusForNo": //跟新状态
                    GetPrdAssistStatusForNo(sCNo, sMNo, sAMFlag);
                    break;
                case "GenerateRecordFile":
                    GenerateRecordFile(DataParams);
                    break;
                case "DeviceInfoExcel":
                    DeviceInfoExcel(DataParams, context);
                    break;
            }
        }

        public string GetPrdAssistStatusForNo(string sNo, string sVer, string sFlag)
        {

            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
   


            if (HttpContext.Current.Session["LoginName"] != null)
            {
  
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            DataTable dt = PrdAssistBll.GetPrdAssistStatusForNo(sNo, sVer,sComp, sFlag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }

            HttpContext context = HttpContext.Current;
            string json = JsonConvert.SerializeObject(dt);

            context.Response.Write(json);
            Result = JsonConvert.SerializeObject(new { Msg = Message });
            return Result;
        }



        #region 获取公司基本信息，公司名称和公司编号
        /// <summary>
        /// 获取生产相关基础数据信息，如：不良原因、不良现象
        /// </summary>

        /// <param name="Flag"></param>
        public string GetCompanyInfo(string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;
            string sDEn = string.Empty;
            string sC = string.Empty;
            string sStr = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            DataTable dt = PrdAssistBll.GetCompanyInfo(sMan, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }

            HttpContext context = HttpContext.Current;
            string json = JsonConvert.SerializeObject(dt);

            context.Response.Write(json);
            Result = JsonConvert.SerializeObject(new { Msg = Message });
            return Result;
        }
        #endregion





        #region 获取和生产相关基础信息
        /// <summary>
        /// 获取生产相关基础数据信息，如：不良原因、不良现象
        /// </summary>
        /// <param name="Params"></param>
        /// <param name="rows"></param>
        /// <param name="page"></param>
        /// <param name="sStatus"></param>
        /// <param name="sCQNo"></param>
        /// <param name="sItmNo"></param>
        /// <param name="Flag"></param>
        public void GetPrdAssistInfo(string Params, int rows, int page, string sStatus, string sCQNo, string sItmNo, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sReturn = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;
            string sItem = string.Empty;
            string sName = string.Empty;
            string sSt = string.Empty;
            string sBDate = string.Empty;
            string sEDate = string.Empty;
            string sA = string.Empty;
            string sB = string.Empty;
            string sC = string.Empty;
            string sD = string.Empty;
            string sE = string.Empty;
            int iSumCount = 0;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return;
            }

            var AnonymousUser = new
            {
                No = String.Empty,
                Item = String.Empty,
                Name = String.Empty,
                Status = String.Empty,
                BDate = String.Empty,
                EDate = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
            };



            if (string.IsNullOrEmpty(Params) || (Params == null) || (Params == "null"))
            {
                sNo = ""; sItem = ""; sName = ""; sSt = ""; sBDate = ""; sEDate = ""; sA = ""; sB = ""; sC = ""; sD = ""; sE = "";
            }
            else
            {
                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

                sNo = Item.No; sItem = Item.Item; sName = Item.Name; sSt = Item.Status; sBDate = Item.BDate; sEDate = Item.EDate;
                sA = Item.A; sB = Item.B; sC = Item.C; sD = Item.D; sE = Item.E;
            }
            if (sBDate == "")
            {
                sBDate = "2021-01-01";
            }
            if (sEDate == "")
            {
                sEDate = "9999-12-30";
            }


            DataTable dt = PrdAssistBll.GetPrdAssistInfo(sNo, sName, sItem, sSt, sBDate, sEDate, sA, sB, sC, sD, sE, rows, page, sMan, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                iSumCount = int.Parse(dt.Rows[0]["NumCount"].ToString());

                string sJson = JsonConvert.SerializeObject(dt);
                sReturn = "{\"code\":0,\"msg\":\"\",\"count\":" + iSumCount + ",\"data\":" + sJson + "}";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;
            context.Response.Write(sReturn);
        }
        #endregion


        #region 获取仓库信息树形结构
        public void GetWareHouseInfo(string Params, int rows, int page, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sFNo = string.Empty;
            string sFName = string.Empty;
            string sCNo = string.Empty;
            string sCName = string.Empty;
            string sGX = string.Empty;
            string sFlag = string.Empty;
            int iSumCount = 0;

            HttpContext context = HttpContext.Current;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }


            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { FNo = String.Empty, FName = String.Empty, CNo = String.Empty, CName = String.Empty, GX = String.Empty, Flag = String.Empty, };

                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sFNo = Item.FNo; sFName = Item.FName; sCNo = Item.CNo; sCName = Item.CName; sGX = Item.GX; sFlag = Item.Flag;
            }
            else // 查询最近一个月录入的记录
            {

            }

            DataTable dt = PrdAssistBll.GeWareHouseInfo(sFNo, sFName, sCNo, sCName, sGX, rows, page, sComp); // FNo, FName, CNo, CName, GX, Row, num, Comp
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                iSumCount = int.Parse(dt.Rows[0]["NumCount"].ToString());
            }
            else
            {
                Message = "Error";
            }


            string json = JsonConvert.SerializeObject(dt);
            Result = "{\"code\":0,\"msg\":\"\",\"count\":" + iSumCount + ",\"data\":" + json + "}";
            // Result = "{\"data\":" + json + "}";

            //Result = JsonConvert.SerializeObject(new {data = json });

            context.Response.Write(Result);

            // return Result;


        }
        #endregion


        #region 对生产辅助相关的信息进行操作
        /// <summary>
        /// 
        /// </summary>
        /// <param name="Params">1、2、3新增、修改、删除不良现象；4、5、6新增、修改、删除不良原因；4、5、6新增、修改、删除部门信息</param>
        /// <returns></returns>
        public string OPPrdAssistInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;
            string sOKFlag = string.Empty;
            string sItemNo = string.Empty;
            string sSNO = string.Empty;


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                K = String.Empty,
                L = String.Empty,
                Kind = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            sNo = It.No;  // 初始这个编号
            sItemNo = It.Item;//内容编号
            sSNO = It.L;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            if (It.Flag == "1") // 新增不良编号
            {
                sNo = CreateBLXXNo();

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.Name, "", "", sComp, "1");
                if (sOKFlag == "Y")  // 判断不良描述是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "2") || (It.Flag == "3")) //修改，删除，已引用不可删除
            {
                sOKFlag = PrdAssistBll.JudgeObjectExist(It.Name, "", "", sComp, "3");
                if (sOKFlag == "Y")  // 判断不良描述是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "4")//新增不良原因
            {
                sNo = CreateBLYYNo();//自动生成不良原因编号

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.Name, "", "", sComp, "1");
                if (sOKFlag == "Y")  // 判断不良描述是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "5") || (It.Flag == "6")) //修改，删除，已引用不可删除
            {
                sOKFlag = PrdAssistBll.JudgeObjectExist(It.Name, "", "", sComp, "1");
                if (sOKFlag == "Y")  // 判断不良原因描述是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "7")//新增部门信息
            {
                sNo = CreateBMXXNo();//自动生成部门编号

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.Name, "", "", sComp, "7");
                if (sOKFlag == "Y")  // 判断部门名称存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "8") || (It.Flag == "9")) //修改，删除，已引用不可删除
            {
                sOKFlag = PrdAssistBll.JudgeObjectExist(It.Name, "", "", sComp, "1");
                if (sOKFlag == "Y")  // 判断部门信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "10")//新增清场计划
            {
                sNo = CreateQCJHNo();//自动生成清场计划

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.Name, "", "", sComp, "1");
                if (sOKFlag == "Y")  // 判断清产计划信息存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "10-2")//新增清场计划
            {
                //sNo = CreateQCJHNo();//自动生成清场计划

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.A, "", sComp, "10-2");
                if (sOKFlag == "Y")  // 判断清产计划信息存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "11") || (It.Flag == "12")) //修改，删除，已引用不可删除
            {
                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.A, "", sComp, "1");
                if (sOKFlag == "Y")  // 判断清场计划信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "10-1")//新增清场明细计划
            {
                sItemNo = CreateQCJHNo(It.No, It.A, "ItemNo");//自动生成清场计划明细项编号

                sSNO = CreateQCJHNo(It.No, It.A, "SNO");//自动生成清场计划明细项顺序号

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.A, It.Item, sComp, "1");
                if (sOKFlag == "Y")  // 判断清产计划明细信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "11-1") || (It.Flag == "12-1")) //修改，删除，已引用不可删除
            {
                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.A, It.Item, sComp, "1");

                if (sOKFlag == "Y")  // 判断清场计划信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "12")  //修改，删除，已引用不可删除
            {
                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.Name, "", sComp, "12");

                if (sOKFlag == "Y")  // 判断清场计划信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "13")//新增仓库
            {
                //sNo = CreateQCJHNo();//自动生成编号

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.Name, "", "", sComp, "1");
                if (sOKFlag == "Y")  // 判断仓库信息存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "15") //删除仓库，有下级子仓库的不可删除
            {
                sOKFlag = PrdAssistBll.JudgeObjectExist(sItemNo, "", "", sComp, "15");
                if (sOKFlag == "Y")  // 判断仓库信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "16")//新增清场记录
            {
                sNo = CreateQCJLNo();//自动生成清场记录编号

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.G, It.H, "", sComp, "16");
                if (sOKFlag == "N")  // 判断清场记录信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "17") || (It.Flag == "18")) //修改，删除，已引用不可删除
            {
                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, "", "", sComp, "1");
                if (sOKFlag == "Y")  // 判断清场记录信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "19")//新增设备记录
            {
                //sNo = CreateQCJLNo();//自动生成清场记录编号

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, "", "", sComp, "19");
                if (sOKFlag == "Y")  // 判断设备编号信息存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "22")//新增设备分类编号记录判断是否重复
            {
                //sNo = CreateQCJLNo();//自动生成清场记录编号

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, "", "", sComp, "22");
                if (sOKFlag == "Y")  // 判断设备编号信息存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "22-1")//设备分类明细
            {
                sItemNo = CreateSBFLNo(It.No, It.Kind, "DCItem");//自动生成清场计划明细项编号

                sSNO = CreateSBFLNo(It.No, It.Kind, "SNO");//自动生成清场计划明细项顺序号

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.A, It.Item, sComp, "1");
                if (sOKFlag == "Y")  // 判断设备分类明细信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "24")//设备分类明细
            {

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, "", "", sComp, "24");
                if (sOKFlag == "Y")  // 判断设备分类明细信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "25")//设备点检任务编号
            {
                sNo = CreateDJRWNo();

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.A, It.Item, sComp, "1");
                if (sOKFlag == "Y")  // 判断设备分类明细信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "28")//设备保养任务编号
            {
                sNo = CreateBYRWNo();

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.A, It.Item, sComp, "1");
                if (sOKFlag == "Y")  // 判断设备分类明细信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "28")//设备保养任务编号
            {
                sNo = CreateQPJHNo();

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.A, It.Item, sComp, "1");
                if (sOKFlag == "Y")  // 判断设备分类明细信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }


            //No: sQPNo, Name: sQPVer, Item: txtItemNo, A: sQPSpec, B: txtTechNode, C: txtTechStep, D: txtTSource, E: txtPFMEA, F: txtDevice, I: txtIDNO, J: txtProduct, K: txtProcess, L: txtProcessTX,
            //M: txtMeasurement, N: txtSampleNum, O: txtSampleFreq, P: txtControlWay, Q: txtResponsePlan, R: txtThisValue, S: txtIncludeOne, T: txtToValue, U: txtIncludeTwo, V: txtRangeKind, W: txtDataType, X: txtStatus,
            sOKFlag = PrdAssistBll.OPPrdAssistInfo(sNo, It.Name, sItemNo, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, It.K, sSNO, It.Kind, sComp, sLogin, It.Remark, It.Flag);
            if (sOKFlag.Length <= 6)
            {  // 6 后台返回插入记录的总数， 6 表示能插入十万级别的记录
                Message = "Success";
            }
            else
            {
                Message = sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }



            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion



        #region 对生产质量控制/IPQC巡查相关的信息进行操作
        /// <summary>
        /// 更新质量控制相关数据
        /// </summary>
        /// <param name="Params"></param>
        /// <returns></returns>
        public string OPPrdAssistQCInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;
            string sOKFlag = string.Empty;
            string sItemNo = string.Empty;
            string sSNO = string.Empty;
            string sVer = string.Empty;


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                SNO = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                K = String.Empty,
                L = String.Empty,
                M = String.Empty,
                N = String.Empty,
                O = String.Empty,
                P = String.Empty,
                Q = String.Empty,
                R = String.Empty,
                S = String.Empty,
                T = String.Empty,
                U = String.Empty,
                V = String.Empty,
                W = String.Empty,
                X = String.Empty,
                Kind = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            sNo = It.No;  // 初始这个编号
            //sVer = It.Name;
            sItemNo = It.Item;//内容编号
            sSNO = It.SNO; ;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            if (It.Flag == "30")//设备质量控制计划编号生成
            {
                sNo = CreateQPJHNo();

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.A, It.Item, sComp, "1");
                if (sOKFlag == "Y")  // 判断设备分类明细信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "30-2")//判断质量控制计划编号和版本是否重复
            {
                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.Name, "", sComp, "30-2");
                if (sOKFlag == "Y")  // 判断质量控制计划是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "30-1")//设备保养任务编号
            {
                sItemNo = CreateQPJHNo(It.No, It.Name, "ItemNo");//自动生成只控制计划计划明细项编号

                sSNO = CreateQPJHNo(It.No, It.Name, "SNo");//自动生成质量控制计划明细项顺序号

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.A, It.Item, sComp, "1");
                if (sOKFlag == "Y")  // 判断质量控制计划是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "32")//质量控制计划删除判断
            {
                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.Name, "", sComp, "32");
                if (sOKFlag == "Y")  // 判断质量控制计划是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "33")//质量控制执行编号
            {
                sNo = CreateQCZXNo();

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.A, It.Item, sComp, "1");
                if (sOKFlag == "Y")  // 判断设备分类明细信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "33-1")//质量控制执行结果保存，判断你是否有重复提交
            {
                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No,  It.Item, It.A, sComp, "33-1");
                if (sOKFlag == "Y")  // 判断设备分类明细信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "35")//判断质量控制执行表头及内容是否可以删除
            {

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.A, It.Item, sComp, "35");
                if (sOKFlag == "Y")  // 判断设备分类明细信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            if (It.Flag == "36")//设备保养任务编号
            {
                sNo = CreateXCJHNo();

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.A, It.Item, sComp, "1");
                if (sOKFlag == "Y")  // 判断设备分类明细信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            if (It.Flag == "36-2")//IPQC巡察计划升版任务编号，判断是否有重复的
            {
                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.Name, "", sComp, "36-2");
                if (sOKFlag == "Y")  // 判断设备分类明细信息是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            if (It.Flag == "36-1")//IPQC巡察计划明细编号
            {
                sItemNo = CreateXCJHNo(It.No, It.Name, "ItemNo");//自动生成只控制计划计划明细项编号

                sSNO = CreateXCJHNo(It.No, It.Name, "SNo");//自动生成质量控制计划明细项顺序号

                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.A, It.Item, sComp, "1");
                if (sOKFlag == "Y")  // 判断质量控制计划是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "38")  //删除，已引用不可删除
            {
                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.Name, "", sComp, "38");
                if (sOKFlag == "Y")  // 判断IPQC巡查计划有没有被引用否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "39")  //删除，已引用不可删除
            {
                sNo = CreateXCZXNo();
                sOKFlag = PrdAssistBll.JudgeObjectExist(It.No, It.A, "", sComp, "1");
                if (sOKFlag == "Y")  // 判断IPQC巡查计划有没有被引用否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "41")  //删除，已引用不可删除
            {
                //sNo = CreateXCZXNo();
                sOKFlag = PrdAssistBll.JudgeObjectExist(sNo, It.A, "", sComp, "41");
                if (sOKFlag == "Y")  // 判断IPQC巡查计划有没有被引用否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "42")  //新增试剂/质控品库，判断批号是否重复
            {

                sOKFlag = PrdAssistBll.JudgeObjectExist(sNo, "", "", sComp, "42");
                if (sOKFlag == "Y")  // 判断IPQC巡查计划有没有被引用否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "45")  //判断是否存在重复文件编号
            {
                //sNo = CreateXCZXNo();
                sOKFlag = PrdAssistBll.JudgeObjectExist(sNo, "", "", sComp, "45");
                if (sOKFlag == "Y")  // 判断IPQC巡查计划有没有被引用否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }

            sOKFlag = PrdAssistBll.OPPrdAssistQCInfo(sNo, It.Name, sItemNo, sSNO, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, It.K, It.L, It.M, It.N, It.O, It.P, It.Q, It.R, It.S, It.T, It.U, It.V, It.W, It.X, It.Kind, sComp, sLogin, It.Remark, It.Flag);
            if (sOKFlag.Length <= 6)
            {  // 6 后台返回插入记录的总数， 6 表示能插入十万级别的记录
                Message = "Success";
            }
            else
            {
                Message = sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }



            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion




        #region 根据工单号，返回工单基本信息
        public void GetOrderInfoByNo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;
            string sFlag = string.Empty;

            HttpContext context = HttpContext.Current;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }


            // No: sNo, Name: sName, Spec: sSpec,BT:sBT
            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { No = String.Empty, Flag = String.Empty, };

                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sNo = Item.No; sFlag = Item.Flag;
            }


            DataTable dt = OrderBll.GetOrderInfo(sNo, "", "", "", "", "", "", "", "", "", "", "", 200, 1, sMan, sComp, sFlag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "系统无此工单，请先到工单界面获取！";
            }

            string json = JsonConvert.SerializeObject(dt);

            Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });

            context.Response.Write(Result);

            // return Result;

        }
        #endregion



        #region 导出设备信息
        public void DeviceInfoExcel(string Params,HttpContext context)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sBDate = string.Empty;
            string sEDate = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
                return;
            }

            var AnonymousUser = new
            {
                No = String.Empty,
                Item = String.Empty,
                Name = String.Empty,
                Status = String.Empty,
                BDate = String.Empty,
                EDate = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                Flag= String.Empty
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            sBDate = Item.BDate;
            sEDate = Item.EDate;

            if (sBDate == "")
            {
                sBDate = "2018-01-01";
            }
            if (sEDate == "")
            {
                sEDate = "2999-12-30";
            }


            DataTable dt = PrdAssistBll.GetPrdAssistInfo(Item.No, Item.Item, Item.Name, Item.Status, sBDate, sEDate, Item.A, Item.B, Item.C, Item.D, Item.E, 50000, 1, sMan, sComp, Item.Flag);

            try
            {
                // 定义路径和文件名
                string path = Path.Combine("RecordFile", "DeviceInfo");
                string fileName = $"设备信息_{DateTime.Now:yyyyMMddHHmmss}.xls";
                string serverPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, path, fileName);

                // 确保目录存在
                Directory.CreateDirectory(Path.GetDirectoryName(serverPath));

                // 创建 Excel 工作簿
                //IWorkbook workbook = fileName.EndsWith(".xlsx") ? new XSSFWorkbook() : new HSSFWorkbook();
                IWorkbook workbook = null;
                //根据不同路径的文件名，生成不同版本的Excel工作簿
                if (fileName.IndexOf(".xlsx") > 0)
                {
                    workbook = new XSSFWorkbook();//新版Excel工作簿
                }
                else if (fileName.IndexOf(".xls") > 0)
                {
                    workbook = new HSSFWorkbook();//旧版Excel工作簿
                }

                // 创建 Sheet 并命名
                ISheet sheet = workbook.CreateSheet("设备信息");

                // 设置列宽
                int[] columnWidths = { 25, 25, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15 }; // 每列的宽度（单位：字符）
                for (int i = 0; i < columnWidths.Length; i++)
                {
                    sheet.SetColumnWidth(i, columnWidths[i] * 256); // 将字符宽度转换为 NPOI 的单位
                }

                // 创建表头样式
                IFont headerFont = workbook.CreateFont();
                headerFont.IsBold = true;
                headerFont.FontHeightInPoints = 8; // 字体大小

                ICellStyle headerStyle = workbook.CreateCellStyle();
                headerStyle.Alignment = HorizontalAlignment.Center;
                headerStyle.VerticalAlignment = VerticalAlignment.Center;
                headerStyle.SetFont(headerFont);

                // 创建表头行
                IRow headerRow = sheet.CreateRow(0);
                string[] headers = { "设备编号", "设备名称", "使用场景", "设备规格", "设备类型", "管理部门", "设备状态", "设备分类编码", "有效期(年)", "校准有效期", "点检要求", "保养要求", "备注", "创建人", "创建时间" };
                for (int i = 0; i < headers.Length; i++)
                {
                    ICell cell = headerRow.CreateCell(i);
                    cell.SetCellValue(headers[i]);
                    cell.CellStyle = headerStyle;
                }

                // 创建数据样式
                IFont dataFont = workbook.CreateFont();
                dataFont.IsBold = false;
                dataFont.FontHeightInPoints = 8; // 字体大小

                ICellStyle dataStyle = workbook.CreateCellStyle();
                dataStyle.Alignment = HorizontalAlignment.Center;
                dataStyle.VerticalAlignment = VerticalAlignment.Center;
                dataStyle.SetFont(dataFont);

                // 填充数据
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    IRow dataRow = sheet.CreateRow(i + 1); // 从第 1 行开始填充数据
                    string[] columns = { "DeviceNo", "DeviceName", "DeviceDesc", "DeviceSpec", "DeviceKind", "DeptName", "Status", "MaterNo", "InventoryCycle", "UseDate", "CheckReq", "MaintainReq", "Remark", "InMan", "InDate2" };
                    for (int j = 0; j < columns.Length; j++)
                    {
                        ICell cell = dataRow.CreateCell(j);
                        cell.SetCellValue(dt.Rows[i][columns[j]].ToString());
                        cell.CellStyle = dataStyle;
                    }
                }

                // 将数据写入文件
                using (FileStream fileStream = new FileStream(serverPath, FileMode.Create))
                {
                    workbook.Write(fileStream);
                }

                // 返回文件路径
                Result = JsonConvert.SerializeObject(new { FilePath = Path.Combine(path, fileName), Msg = "Success" });
            }
            catch (Exception ex)
            {
                // 记录异常信息
                Result = JsonConvert.SerializeObject(new { FilePath = "", Msg = "Error", Message = ex.Message });
            }
            finally
            {
                // 返回结果
                context.Response.Write(Result);
            }
        }
        #endregion






        #region 生成记录文件  设备点检、设备保养、IPQC 巡查、清场记录  PDF 文件生成
        public void GenerateRecordFile(string Params)
        {

            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string ReportPathFile = string.Empty;
            string path = string.Empty;
            string fileKind = string.Empty;
            HttpContext context = HttpContext.Current;
            if (context.Session["LoginName"] != null)
            {
                sLogin = context.Session["LoginName"].ToString();
                sManName = context.Session["FullName"].ToString();
                sComp = context.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
                return;
            }
            if (Params == "" || Params == null || Params == "null")
            {
                Message = "ParamsError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
                return;
            }
            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                Status = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty
            };
            var Item1 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item1), AnonymousUser);

            if (It.Flag == "100")
            {
                Result = PrdAssistBll.JudgeObjectExist(It.No, It.A, "", sComp, "48");
                Message = Result == "Y" ? "Y_EXIST" : "Success";
                context.Response.Write(JsonConvert.SerializeObject(new { Msg = Message }));
                return;
            }

            Dictionary<string, object> list = PrdAssistBll.GenerateRecordFile(It.No, It.Name, It.Item, It.Status, It.A, It.B, It.C, It.D, It.Remark, sComp, sLogin, It.Flag);

            Result = JsonConvert.SerializeObject(list);

            //如果是等于DHR就是DHR报表，否则就是汇总报表
            if (It.Flag == "DJ")//设备点检
            {
                ReportPathFile = context.Server.MapPath("/Template/" + sComp + "/LabelPrint/DeviceCheck.grf");//根据WEB服务器根目录寻址
                //文件存放位置
                path = "/RecordFile/DeviceCheck/";
                fileKind = "DeviceCheck";
            }
            else if (It.Flag == "BY")//设备保养
            {
                ReportPathFile = context.Server.MapPath("/Template/" + sComp + "/LabelPrint/DeviceMaintain.grf");//根据WEB服务器根目录寻址
                //文件存放位置
                path = "/RecordFile/DeviceMaintain/";
                fileKind = "DeviceMaintain";
            }
            else if (It.Flag == "XC") //IPQC巡查
            {
                ReportPathFile = context.Server.MapPath("/Template/" + sComp + "/LabelPrint/IPQC.grf");//根据WEB服务器根目录寻址
                //文件存放位置
                path = "/RecordFile/IPQC/";
                fileKind = "IPQC";
            }
            else if (It.Flag == "QC") //清场记录PDF
            {
                ReportPathFile = context.Server.MapPath("/Template/" + sComp + "/LabelPrint/CleanRecord.grf");//根据WEB服务器根目录寻址
                //文件存放位置
                path = "/RecordFile/CleanRecord/";
                fileKind = "CleanRecord";
            }
            else if (It.Flag == "DeviceUse") //设备使用记录PDF
            {
                ReportPathFile = context.Server.MapPath("/Template/" + sComp + "/LabelPrint/DeviceUse.grf");//根据WEB服务器根目录寻址
                //文件存放位置
                path = "/RecordFile/DeviceUse/";
                fileKind = "DeviceUse";
            }

            GridppReportServer Report = new GridppReportServer();

            bool Success = Report.LoadFromFile(ReportPathFile);

            if (!Success)
            {
                context.Response.Write(JsonConvert.SerializeObject(new { Msg = "ImportError" }));
                return;
            }

            Report.LoadDataFromXML(Result);

            //生成PDF文档数据
            IGRBinaryObject PDFDataObject = Report.ExportDirectToBinaryObject(GRExportType.gretPDF);

            //将生成的数据响应给客户端
            if (PDFDataObject.DataSize > 0)
            {
                ServerUtility.ResponseBinary(context, PDFDataObject, "application/pdf", "attachment", It.No, It.Name, sComp, sLogin, path, fileKind);
            }
            else
            {
                context.Response.Write(JsonConvert.SerializeObject(new { Msg = "CreateError" }));
                return;
            }

        }
        #endregion






























        #region  生成不良现象编号  BLXX001
        /// <summary>
        /// 获取最新的不良现象编号
        /// </summary>
        /// <returns></returns>
        public static string CreateBLXXNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_DefectsDesc where 1=1 ", "WNo");//  QS2103 00001  BLXX 001
            if (sMaxNo == "")
            {
                sNo = "BLXX" + CreateAllNo.GetZoreNumString(2) + "1";   // CreateBillNo 这个是带时间的流水号
            }
            else
            {
                string sTemp = sMaxNo.Substring(4, 3);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 3 - len;

                sNo = "BLXX" + CreateAllNo.GetZoreNumString(i) + iMax.ToString();  // CreateAllNo.CreateBillNo(4, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion



        #region  生成不良原因编号  BLYY001
        /// <summary>
        /// 获取最新的不良原因编号
        /// </summary>
        /// <returns></returns>
        public static string CreateBLYYNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_DefectsCause where 1=1 ", "CNo");//  QS2103 00001  BLXX 001
            if (sMaxNo == "")
            {
                sNo = "BLYY" + CreateAllNo.GetZoreNumString(2) + "1";   // CreateBillNo 这个是带时间的流水号
            }
            else
            {
                string sTemp = sMaxNo.Substring(4, 3);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 3 - len;

                sNo = "BLYY" + CreateAllNo.GetZoreNumString(i) + iMax.ToString();  // CreateAllNo.CreateBillNo(4, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion



        #region  生成部门编号  BM001
        /// <summary>
        /// 获取最新的部门编号
        /// </summary>
        /// <returns></returns>
        public static string CreateBMXXNo()
        {
            //string sDate = DateTime.Now.ToString("yyyy-MM");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_DeptInfo where 1=1 ", "DeptNo");//  QS2103 00001  BM 001
            if (sMaxNo == "")
            {
                sNo = "BMXX" + CreateAllNo.GetZoreNumString(2) + "1";   // CreateBillNo 这个是带时间的流水号
            }
            else
            {
                string sTemp = sMaxNo.Substring(4, 3);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 3 - len;

                sNo = "BMXX" + CreateAllNo.GetZoreNumString(i) + iMax.ToString();  // CreateAllNo.CreateBillNo(4, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion



        #region  生成清场计划编号  QCJH20230908001
        /// <summary>
        /// 获取最新的清场计划编号
        /// </summary>
        /// <returns>QCJH20230908001</returns>
        public static string CreateQCJHNo()
        {
            string sDate = DateTime.Now.ToString("yyyyMMdd");// 获取当前日期
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_ClearPlanHead where 1=1 and CONVERT(char(8),Indate,112) = CONVERT(char(8),getdate(),112) ", "CPNo");//  QS2103 00001  BM 001
            if (sMaxNo == "")
            {

                DateTime currentDate = DateTime.Now;
                sNo = "QCJH" + sDate + CreateAllNo.GetZoreNumString(2) + "1";   // CreateBillNo 这个是带时间的流水号
            }
            else
            {
                string sTemp = sMaxNo.Substring(12, 3);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 3 - len;

                sNo = "QCJH" + sDate + CreateAllNo.GetZoreNumString(i) + iMax.ToString();  // CreateAllNo.CreateBillNo(4, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion



        #region  生成清场记录编号  QCJL20230908001
        /// <summary>
        /// 获取最新的清场计划编号
        /// </summary>
        /// <returns>QCJH20230908001</returns>
        public static string CreateQCJLNo()
        {
            string sDate = DateTime.Now.ToString("yyyyMMdd");// 获取当前日期
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_OrderClearRec where 1=1 and CONVERT(char(8),Indate,112) = CONVERT(char(8),getdate(),112) ", "CRNo");//  QS2103 00001  BM 001
            if (sMaxNo == "")
            {

                DateTime currentDate = DateTime.Now;
                sNo = "QCJL" + sDate + CreateAllNo.GetZoreNumString(2) + "1";   // CreateBillNo 这个是带时间的流水号
            }
            else
            {
                string sTemp = sMaxNo.Substring(12, 3);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 3 - len;

                sNo = "QCJL" + sDate + CreateAllNo.GetZoreNumString(i) + iMax.ToString();  // CreateAllNo.CreateBillNo(4, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion


        #region  生成清场计划明细编号  QCJH20230908001
        /// <summary>
        /// 清场计划内容ItemNo编号和顺序号SNO
        /// </summary>
        /// <param name="CPNO"></param>
        /// <param name="CPVer"></param>
        /// 用于数值型字段
        /// <returns></returns>
        public static string CreateQCJHNo(string CPNO, string CPVer, string T)
        {
            //string sDate = DateTime.Now.ToString("yyyyMMdd");// 获取当前日期
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;



            string sMaxNo = DBHelper.GetMaxNo("T_ClearPlanDetail where 1=1 and  CPNo='" + CPNO + "' and  CPVer='" + CPVer + "'", "CAST(" + T + " as  int)");//  QS2103 00001  BM 001
            if (sMaxNo == "")
            {

                //DateTime currentDate = DateTime.Now;
                sNo = "1";   // CreateBillNo 这个是带时间的流水号
            }
            else
            {
                //string sTemp = sMaxNo.Substring(12, 3);
                iMax = int.Parse(sMaxNo) + 1;
                //int len = iMax.ToString().Length;
                //i = 3 - len;

                sNo = iMax.ToString();  // CreateAllNo.CreateBillNo(4, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion


        #region  生成设备分类明细编号或者设备分类序号的  SBFL20230908001
        /// <summary>
        /// 设备分类明细编号
        /// </summary>
        /// <param name="sMaterNo"></param>
        /// <param name="sKind"></param>
        /// <param name="T"></param>
        /// <returns></returns>
        public static string CreateSBFLNo(string sMaterNo, string sKind, string T)
        {
            //string sDate = DateTime.Now.ToString("yyyyMMdd");// 获取当前日期
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;



            string sMaxNo = DBHelper.GetMaxNo("T_DeviceClassDetail where 1=1 and MaterNo='" + sMaterNo + "' and  Kind='" + sKind + "'", "CAST(" + T + " as  int)");//  QS2103 00001  BM 001
            if (sMaxNo == "")
            {

                //DateTime currentDate = DateTime.Now;
                sNo = "1";   // CreateBillNo 这个是带时间的流水号
            }
            else
            {
                //string sTemp = sMaxNo.Substring(12, 3);
                iMax = int.Parse(sMaxNo) + 1;
                //int len = iMax.ToString().Length;
                //i = 3 - len;

                sNo = iMax.ToString();  // CreateAllNo.CreateBillNo(4, i) + iMax.ToString();
            }
            return sNo;

        }

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
        #endregion


        #region  生成点检任务编号  DJRW20230908001
        /// <summary>
        /// 获取最新的点检任务编号
        /// </summary>
        /// <returns>QCJH20230908001</returns>
        public static string CreateDJRWNo()
        {
            string sDate = DateTime.Now.ToString("yyyyMMdd");// 获取当前日期
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_DeviceCheckTaskHead where 1=1 and CONVERT(char(8),Indate,112) = CONVERT(char(8),getdate(),112) ", "TNo");//  QS2103 00001  BM 001
            if (sMaxNo == "")
            {

                DateTime currentDate = DateTime.Now;
                sNo = "DJRW" + sDate + CreateAllNo.GetZoreNumString(2) + "1";   // CreateBillNo 这个是带时间的流水号
            }
            else
            {
                string sTemp = sMaxNo.Substring(12, 3);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 3 - len;

                sNo = "DJRW" + sDate + CreateAllNo.GetZoreNumString(i) + iMax.ToString();  // CreateAllNo.CreateBillNo(4, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion

        #region  生成保养任务编号  WHRW20230908001
        /// <summary>
        /// 获取最新的保养任务编号
        /// </summary>
        /// <returns>QCJH20230908001</returns>
        public static string CreateBYRWNo()
        {
            string sDate = DateTime.Now.ToString("yyyyMMdd");// 获取当前日期
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_DeviceMaintainTaskHead where 1=1 and CONVERT(char(8),Indate,112) = CONVERT(char(8),getdate(),112) ", "TNo");//  QS2103 00001  BM 001
            if (sMaxNo == "")
            {

                DateTime currentDate = DateTime.Now;
                sNo = "BYRW" + sDate + CreateAllNo.GetZoreNumString(2) + "1";   // CreateBillNo 这个是带时间的流水号
            }
            else
            {
                string sTemp = sMaxNo.Substring(12, 3);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 3 - len;

                sNo = "BYRW" + sDate + CreateAllNo.GetZoreNumString(i) + iMax.ToString();  // CreateAllNo.CreateBillNo(4, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion

        #region  生成质量控制计划编号  QPJH20230908001
        /// <summary>
        /// 获取最新的质量控制计划编号
        /// </summary>
        /// <returns>QCJH20230908001</returns>
        public static string CreateQPJHNo()
        {
            string sDate = DateTime.Now.ToString("yyyyMMdd");// 获取当前日期
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_QAControlPlanHead where 1=1 and CONVERT(char(8),Indate,112) = CONVERT(char(8),getdate(),112) ", "QPNo");//  QS2103 00001  BM 001
            if (sMaxNo == "")
            {

                DateTime currentDate = DateTime.Now;
                sNo = "QPJH" + sDate + CreateAllNo.GetZoreNumString(2) + "1";   // CreateBillNo 这个是带时间的流水号
            }
            else
            {
                string sTemp = sMaxNo.Substring(12, 3);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 3 - len;

                sNo = "QPJH" + sDate + CreateAllNo.GetZoreNumString(i) + iMax.ToString();  // CreateAllNo.CreateBillNo(4, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion


        #region  生成质量控制计划明细项编号  QCJH20230908001
        /// <summary>
        /// 
        /// </summary>
        /// <param name="QPNO">质量控制计划编号</param>
        /// <param name="QPVer">质量控制计划版本</param>
        /// <param name="T">获取顺序号类型ItemNo/sNO</param>
        /// <returns></returns>
        public static string CreateQPJHNo(string QPNO, string QPVer, string T)
        {
            //string sDate = DateTime.Now.ToString("yyyyMMdd");// 获取当前日期
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;



            string sMaxNo = DBHelper.GetMaxNo("T_QAControlPlanDetail where 1=1 and  QPNo='" + QPNO + "' and  Ver='" + QPVer + "'", "CAST(" + T + " as  int)");//  顺序号
            if (sMaxNo == "")
            {

                //DateTime currentDate = DateTime.Now;
                sNo = "1";   // CreateBillNo 这个是带时间的流水号
            }
            else
            {
                //string sTemp = sMaxNo.Substring(12, 3);
                iMax = int.Parse(sMaxNo) + 1;
                //int len = iMax.ToString().Length;
                //i = 3 - len;

                sNo = iMax.ToString();  // CreateAllNo.CreateBillNo(4, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion






        #region  生成质量控制执行编号  QCZX20230908001
        /// <summary>
        /// 获取最新的质量控制计划编号
        /// </summary>
        /// <returns>QCJH20230908001</returns>
        public static string CreateQCZXNo()
        {
            string sDate = DateTime.Now.ToString("yyyyMMdd");// 获取当前日期
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_QAControlEXEHead where 1=1 and CONVERT(char(8),Indate,112) = CONVERT(char(8),getdate(),112) ", "ENo");//  QS2103 00001  BM 001
            if (sMaxNo == "")
            {

                DateTime currentDate = DateTime.Now;
                sNo = "QCZX" + sDate + CreateAllNo.GetZoreNumString(2) + "1";   // CreateBillNo 这个是带时间的流水号
            }
            else
            {
                string sTemp = sMaxNo.Substring(12, 3);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 3 - len;

                sNo = "QCZX" + sDate + CreateAllNo.GetZoreNumString(i) + iMax.ToString();  // CreateAllNo.CreateBillNo(4, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion





        #region  生成IPQC巡查计划编号  QPJH20230908001
        /// <summary>
        /// 获取最新的质量控制计划编号
        /// </summary>
        /// <returns>QCJH20230908001</returns>
        public static string CreateXCJHNo()
        {
            string sDate = DateTime.Now.ToString("yyyyMMdd");// 获取当前日期
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_InspectPlanHead where 1=1 and CONVERT(char(8),Indate,112) = CONVERT(char(8),getdate(),112) ", "IPNo");//  QS2103 00001  BM 001
            if (sMaxNo == "")
            {

                DateTime currentDate = DateTime.Now;
                sNo = "XCJH" + sDate + CreateAllNo.GetZoreNumString(2) + "1";   // CreateBillNo 这个是带时间的流水号
            }
            else
            {
                string sTemp = sMaxNo.Substring(12, 3);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 3 - len;

                sNo = "XCJH" + sDate + CreateAllNo.GetZoreNumString(i) + iMax.ToString();  // CreateAllNo.CreateBillNo(4, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion


        #region  生成质量IPQC巡查计划明细项编号  QCJH20230908001
        /// <summary>
        /// 
        /// </summary>
        /// <param name="QPNO">质量控制计划编号</param>
        /// <param name="QPVer">质量控制计划版本</param>
        /// <param name="T">获取顺序号类型ItemNo/sNO</param>
        /// <returns></returns>
        public static string CreateXCJHNo(string QPNO, string QPVer, string T)
        {
            //string sDate = DateTime.Now.ToString("yyyyMMdd");// 获取当前日期
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;



            string sMaxNo = DBHelper.GetMaxNo("T_InspectPlanDetail where 1=1 and  IPNo='" + QPNO + "' and  Ver='" + QPVer + "'", "CAST(" + T + " as  int)");//  顺序号
            if (sMaxNo == "")
            {

                //DateTime currentDate = DateTime.Now;
                sNo = "1";   // CreateBillNo 这个是带时间的流水号
            }
            else
            {
                //string sTemp = sMaxNo.Substring(12, 3);
                iMax = int.Parse(sMaxNo) + 1;
                //int len = iMax.ToString().Length;
                //i = 3 - len;

                sNo = iMax.ToString();  // CreateAllNo.CreateBillNo(4, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion



        #region  生成IPQC巡查执行编号  QPJH20230908001
        /// <summary>
        /// 获取最新的质量控制计划编号
        /// </summary>
        /// <returns>QCJH20230908001</returns>
        public static string CreateXCZXNo()
        {
            string sDate = DateTime.Now.ToString("yyyyMMdd");// 获取当前日期
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_InspectEXE where 1=1 and CONVERT(char(8),Indate,112) = CONVERT(char(8),getdate(),112) ", "ENo");//  QS2103 00001  BM 001
            if (sMaxNo == "")
            {

                DateTime currentDate = DateTime.Now;
                sNo = "XCZX" + sDate + CreateAllNo.GetZoreNumString(2) + "1";   // CreateBillNo 这个是带时间的流水号
            }
            else
            {
                string sTemp = sMaxNo.Substring(12, 3);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 3 - len;

                sNo = "XCZX" + sDate + CreateAllNo.GetZoreNumString(i) + iMax.ToString();  // CreateAllNo.CreateBillNo(4, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion















    }
}
