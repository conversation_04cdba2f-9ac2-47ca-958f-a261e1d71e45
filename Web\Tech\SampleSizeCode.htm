﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>样本量字码</title>
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/Tech.js" type="text/javascript"></script>
    <link href="../css/bootstrap.min.css" rel="stylesheet" />

    <link href="../css/XC.css" rel="stylesheet" />

    <script type="text/javascript">
        layui.use("table", function () {
            var table = layui.table;
            var form = layui.form;
            table.render({
                elem: "#SampleSizeCode",
                id: "SampleSizeCodeID",
                url: "../Service/TechAjax.ashx?OP=GetSampleSizeCodeInfo&CFlag=119",
                height: "full-50",
                cellMinWidth: 80,
                count: 50,
                limit: 20,
                limits: [10, 20, 30, 40, 50],
                cols: [[
                    { type: "numbers", width: 60, title: "序号" },
                    { field: "SCNo", title: "编号", sort: true },
                    { field: "Batch", title: "批量"},
                    { field: "IntervalHead", title: "批量区间头", sort: true },
                    { field: "IntervalTail", title: "批量区间尾", sort: true },
                    { field: "InspectLevel", title: "检验水平" },
                    { field: "SampleCode", title: "样本量字码", sort: true },
                    { field: 'op', title: '操作', toolbar: '#barDemo', fixed: 'right', width: 180 }
                ]],
                page: true
            })


            table.on("tool(SampleSizeCode)", function (obj) {
                var data = obj.data;
                var layEvent = obj.event;

                if (layEvent == "UP" || layEvent == "DOWN") {
                    var params = { SCNo: data.SCNo, PresentOrder: data.SeqNo, Flag: "2", OPFlag: layEvent }
                    var Data = JSON.stringify(params);
                    $.ajax({
                        url: "../Service/TechAjax.ashx?OP=OPSampleSizeCodeInfo&CFlag=2",
                        type: "POST",
                        data: {
                            Data: Data
                        },
                        success: function (data) {
                            var parsedJson = jQuery.parseJSON(data);

                            if (parsedJson != undefined && parsedJson != '' && parsedJson.msg == 'Success') {
                                layer.msg('移动成功！');

                                $("#SampleSizeCodeSearch").click()

                            } else {
                                layer.msg('移动失败，请重试！')
                            }
                        },
                        error: function () {
                            layer.msg('移动失败，请重试！')
                        }
                    })
                } else if (layEvent == "delete") {
                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("您确定要删除该记录吗？ 编号：")
                    //设置删除的对象
                    $("#hint-value").html(data.SCNo)

                    $("#txtDeleteKind").val(data.SCNo)
                }
            })

            //查询
            $("#SampleSizeCodeSearch").click(function () {
                table.reload("SampleSizeCodeID", {
                    method: "post",
                    url: "../Service/TechAjax.ashx?OP=GetSampleSizeCodeInfo&CFlag=119",
                    page: {
                        curr: 1
                    }
                })
            })


            //删除
            $("#DeleteSampleSizeCode").click(function () {

                var sSCNO = $("#txtDeleteKind").val()

                var params = { SCNo: sSCNO, PresentOrder: "", Flag: "3", OPFlag: "" }

                var Data = JSON.stringify(params);
                $.ajax({
                    url: "../Service/TechAjax.ashx?OP=OPSampleSizeCodeInfo&CFlag=3",
                    type: "POST",
                    data: {
                        Data: Data
                    },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.msg == 'Success') {
                            layer.msg('删除成功！');

                            closeDialog()
                            $("#SampleSizeCodeSearch").click()

                        } else {
                            layer.msg('删除失败，请重试！')
                        }
                    },
                    error: function () {
                        layer.msg('删除失败，请重试！')
                    }
                })
            })
        })

    </script>

    <script type="text/javascript">
        $(function () {
            $("#SampleSizeCodeAdd").click(function () {
                $("#ShowOne").css("display", "block");
                $("#ShowOne-fade").css("display", "block");
                $("#head-title1").html("新增样本量字码信息")
                ResetForm()
            })

            $("#batch").blur(function () {
                var batch = $("#batch").val();
                if (batch.indexOf('-') != -1) {
                    var arr = batch.split('-')
                    $("#batch_start").val(arr[0]);
                    $("#batch_end").val(arr[1]);
                } else if (batch.indexOf('~') != -1) {
                    var arr = batch.split('~')
                    $("#batch_start").val(arr[0]);
                    $("#batch_end").val(arr[1]);
                } else if (batch.indexOf('/') != -1) {
                    var arr = batch.split('/')
                    $("#batch_start").val(arr[0]);
                    $("#batch_end").val(arr[1]);
                }
            }).focus(function () {
                $("#batch_start").val("");
                $("#batch_end").val("");
            })

            
        })

        function ResetForm() {
            $("#batch").val("");
            $("#batch_start").val("");
            $("#batch_end").val("");
            $("#inspection_level").val("请选择检验水平")
            $("#sample_code").val("");
            $("#remark").val("");
        }

        function closeDialog() {
            $("#ShowOne").css("display", "none");
            $("#ShowOne-fade").css("display", "none");
            $("#ShowDel").css("display", "none");
            $("#ShowDel-fade").css("display", "none");
        }

        
    </script>

    <style>
        /*    .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: #bbbcc7;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=60);
        }*/

        #SamplingSizeCodeForm .form-group {
            margin-bottom: 25px
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        .layui-table th {
            font-size: 12px;
        }

        .layui-table td {
            font-size: 12px;
        }

        .XC-Form-block-Item span {
            width: 80px;
            line-height:30px
        }
    </style>
</head>
<body>
    <div style="margin:5px;text-align:right">
        <button type="button" id="SampleSizeCodeAdd" class="XC-Btn-md XC-Btn-Green XC-Size-xs">添加</button>
        <button type="button" id="SampleSizeCodeSearch" style="display:none" class="layui-btn layui-btn-sm">搜索</button>
    </div>
    <table class="layui-hide" id="SampleSizeCode" lay-filter="SampleSizeCode"></table>
    <script type="text/html" id="barDemo">
        <button type="button" id="edit" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="UP">上↑移</button>
        <button type="button" id="edit" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="DOWN">下↓移</button>
        <button type="button" id="del" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="delete">删除</button>
    </script>

    <!--弹出层-->
    <div class="XC-modal XC-modal-lg" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1"></span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body" style="padding: 20px; height: 370px">
            <form class="XC-Form-block">
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">批量<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <input class="XC-Input-block" id="batch" name="batch" placeholder="请输入批量" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">批量区间头<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <input class="XC-Input-block" id="batch_start" placeholder="批量区间头" name="batch_start" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">批量区间尾<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <input class="XC-Input-block" id="batch_end" name="batch_end" placeholder="批量区间尾" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">检验水平<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <select class="XC-Select-block" name="inspection_level" id="inspection_level" placeholder="请选择检验水平">
                        <option value="请选择检验水平" selected=selected>请选择检验水平</option>
                        <option value="特殊（S-1）">特殊（S-1）</option>
                        <option value="特殊（S-2）">特殊（S-2）</option>
                        <option value="特殊（S-3）">特殊（S-3）</option>
                        <option value="特殊（S-4）">特殊（S-4）</option>
                        <option value="一般（Ⅰ）">一般（Ⅰ）</option>
                        <option value="一般（Ⅱ）">一般（Ⅱ）</option>
                        <option value="一般（Ⅲ）">一般（Ⅲ）</option>
                    </select>
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">样本量字码<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <input class="XC-Input-block" id="sample_code" name="sample_code" placeholder="请输入样本量字码" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Textarea-block">备注</span>
                    <textarea class="XC-Textarea-block" id="remark" name="remark" placeholder="请输入备注"></textarea>
                </div>
            </form>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="SampleSizeCodeSubmit">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick='closeDialog()'>取消</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>


    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDeleteKind" name="txtDeleteKind" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="DeleteSampleSizeCode">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>

    <!--消息提示-->
    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>
</html>