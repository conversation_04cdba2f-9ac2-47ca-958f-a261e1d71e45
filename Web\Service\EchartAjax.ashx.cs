﻿using BLL;
using Common;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using System.Web;
using System.Web.Services;
using System.Web.SessionState;
using System.Web.UI.WebControls;
using static ICSharpCode.SharpZipLib.Zip.ExtendedUnixData;

namespace Web.Service
{
    /// <summary>
    /// EchartAjax 的摘要说明
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class EchartAjax : IHttpHandler, IRequiresSessionState
    {

        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "text/plain";
            string Result = string.Empty;
            string Operate = context.Request.Params["OP"];
            string DataParams = context.Request.Params["Data"];
            int slimit = 0;
            int spage = 0;

            switch (Operate)
            {
                case "GetTemplateBase":
                    slimit = int.Parse(context.Request.Params["limit"]);
                    spage = int.Parse(context.Request.Params["page"]);
                    GetTemplateBase(DataParams, slimit, spage);
                    break;
                case "TemplateBaseOP":
                    TemplateBaseOP(DataParams);
                    break;
                case "GetEchartData":
                    GetEchartData(DataParams);
                    break;
            }
        }

        public static void TemplateBaseOP(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            var sNo = string.Empty;

            HttpContext context = HttpContext.Current;

            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item), AnonymousUser);

            if (context.Session["LoginName"] != null)
            {
                sLogin = context.Session["LoginName"].ToString();
                sManName = context.Session["FullName"].ToString();
                sComp = context.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }


            Result = EchartBll.TemplateBaseOP(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.Remark, sLogin, sComp, It.Flag);

            Result = JsonConvert.SerializeObject(new { Msg = Result, Data = "" });

            context.Response.Write(Result);
        }

        public static void GetTemplateBase(string Params, int rows, int page)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;


            HttpContext context = HttpContext.Current;

            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                BDate = String.Empty,
                EDate = String.Empty,
                Status = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                K = String.Empty,
                L = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item), AnonymousUser);

            string sBDate = It.BDate == "" ? "2021-01-01" : It.BDate;
            string sEDate = It.EDate == "" ? "2999-12-30" : It.EDate;


            if (context.Session["LoginName"] != null)
            {
                sLogin = context.Session["LoginName"].ToString();
                sManName = context.Session["FullName"].ToString();
                sComp = context.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }


            var dt = EchartBll.GetTemplateBase(It.No, It.Name, It.Item, It.MNo, It.MName, It.Status, sBDate, sEDate, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, It.K, It.L, rows, page, sLogin, sComp, It.Flag);

            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                int count = Convert.ToInt32(dt.Rows[0]["numCount"].ToString());
                string Json = JsonConvert.SerializeObject(dt);
                Result = "{\"code\":0,\"msg\":\"\",\"count\":" + count + ",\"data\":" + Json + "}";
            }
            else
            {
                Message = "Error";
                string Json = JsonConvert.SerializeObject(dt);
                Result = "{\"code\":0,\"msg\":\"\",\"count\":0,\"data\":" + Json + "}";
            }

            context.Response.Write(Result);
        }


        public static void GetEchartData(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;


            HttpContext context = HttpContext.Current;

            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                Status = String.Empty,
                BDate = String.Empty,
                EDate = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                K = String.Empty,
                L = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item), AnonymousUser);

            //if (context.Session["LoginName"] != null)
            //{
            //    sLogin = context.Session["LoginName"].ToString();
            //    sManName = context.Session["FullName"].ToString();
            //    sComp = context.Session["CompanyNo"].ToString();
            //}
            //else
            //{
            //    Message = "LoginError";
            //    Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
            //    context.Response.Write(Result);
            //}

            string[] str = It.Flag.Split(',');
            Dictionary<string, List<DataTable>> list = new Dictionary<string, List<DataTable>>();
            List<DataTable> data = new List<DataTable>();

            foreach (string item in str)
            {
                data = EchartBll.GetEchartData(It.No, It.Name, It.Item, It.MNo, It.MName, It.Status, It.BDate, It.EDate, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, It.K, It.L, 20, 1, sLogin, sComp, item);
                list.Add(item, data);
            }

            Result = JsonConvert.SerializeObject(list);

            context.Response.Write(Result);
        }

        


        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}