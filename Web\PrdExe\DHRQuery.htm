﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>DHR查询</title>
    <script type="text/javascript" src="../js/jQuery-2.2.0.min.js"></script>

    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-select.min.css" rel="stylesheet" type="text/css" />
    <script src="../js/bootstrap.min.js" type="text/javascript"></script>
    <script src="../js/bootstrap-select.min.js" type="text/javascript"></script>

    <link href="../css/layuiM.css" rel="stylesheet" media="all" />
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/layer/layer.js" type="text/javascript"></script>

    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/XC.css" rel="stylesheet" />
    <script src="../js/Order.js"></script>

    <script type="text/javascript">
        $(function () {

            GetProcedureList()

        })
    </script>

    <script type="text/javascript">
        //获取当前所在的模块、菜单
        const currentTabId = sessionStorage.getItem("currentTabId");
        const ModuleName = currentTabId != null ? JSON.parse(currentTabId).ModuleName : null;

        layui.use("table", function () {
            var table = layui.table;
            var temp_table_list = [];
            var temp_all_list = [];


            table.render({
                elem: "#DHRQuery",
                id: "DHRQueryID",
                url: "../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=238",
                height: "full-70",
                cellMinWidth: 80,
                count: 50,
                limit: 20,
                limits: [10, 20, 30, 40, 50],
                cols: [[
                    { type: 'checkbox' },
                    { title: "工单编号", field: "OrderNo", width: 145, sort: true },
                    { title: "序列号", field: "SerialNo", width: 165, sort: true },
                    { title: "物料编码", field: "MaterNo", width: 165 },
                    { title: "物料描述", field: "ProductName", width: 165 },
                    { title: "工序编号", field: "ProcedureNo", width: 80, align: "center", sort: true },
                    { title: "工序版本", field: "ProcedureVer", width: 80, align: "center", sort: true },
                    { title: "工序名称", field: "ProcedureName", width: 80, align: "center" },
                    { title: "顺序号", field: "FlowOrder", width: 60, align: "center" },
                    { title: "文件路径", field: "RPath" },
                    { title: "文件名称", field: "FileName" },
                    { title: "操作人", field: "InMan", width: 80, align: "center" },
                    {
                        field: 'Remark', width: 80, title: '文件类型', width: 100, templet: function (d) {
                            if (d.Remark == 'DHR')
                                return '工序追溯表';
                            else if (d.Remark == 'HZ')
                                return '产品追溯表';
                            else if (d.Remark == 'WX')
                                return '制程不合格处理单';
                            else
                                return ''
                        }
                    },
                    { title: "时间", field: "InDate2", width: 130 },
                    { field: 'op', title: '操作', toolbar: '#barDemo', fixed: 'right', width: 120 }
                ]],
                page: true,
                done: function (res) {
                    temp_table_list = res.data;
                    temp_table_list.forEach(function (o) {
                        temp_all_list.forEach(function (selected) {
                            if (selected.FileNo === o.FileNo) {
                                o["LAY_CHECKED"] = true;
                                var index = o['LAY_TABLE_INDEX'];
                                $('.layui-table tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', true).next().addClass('layui-form-checked');
                            }
                        });
                    });
                }

            })


            $("#DHRQuery_open").click(function () {

                var sOrderNo = $("#txtSOrderNo").val()
                var sSerial = $("#txtSSerial").val()
                var sProcedure = $("#txtSProcedure").val()
                var sProcedureNo = "";
                //var sStartDate = $("#txtStartDate").val()
                //var sEndDate = $("#txtSEndDate").val()
                var sFileKind = $("#txtFileKind").val()

                if (sProcedure != null) {
                    const ProcNo = sProcedure.map(item => {
                        const match = item.match(/\((.*?)\)(.*)/);
                        return match ? match[1] : null;
                    }).filter(Boolean)
                    sProcedureNo = ProcNo.join(',');
                }

                var Data = '';
                var Params = { No: sSerial, Item: sOrderNo, Name: "", MNo: "", MName: sProcedureNo, Status: "", BDate: "", EDate: "", A: sFileKind, B: "", C: "", D: "" };
                var Data = JSON.stringify(Params);

                table.reload('DHRQueryID', {
                    method: 'post',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=238&Data=' + Data,
                    page: {
                        curr: 1
                    }
                });
            })

            table.on('checkbox(DHRQuery)', function (obj) {
                if (obj.checked) {
                    if (obj.type === 'one') {
                        var index = temp_all_list.findIndex(function (item) {
                            return item.FileNo == obj.data.FileNo;
                        });
                        if (index == -1) {
                            temp_all_list.push(obj.data);
                        }
                    } else {
                        temp_table_list.forEach(function (o) {
                            if (temp_all_list.findIndex(function (item) { return item.FileNo == o.FileNo; }) == -1) {
                                temp_all_list.push(o);
                            }
                        });
                    }
                } else {
                    if (obj.type === 'one') {
                        temp_all_list = temp_all_list.filter(function (item) {
                            return item.FileNo !== obj.data.FileNo;
                        });
                    } else {
                        for (var i = 0; i < temp_table_list.length; i++) {
                            for (var j = 0; j < temp_all_list.length; j++) {
                                if (temp_table_list[i].FileNo == temp_all_list[j].FileNo) {
                                    temp_all_list.splice(j, 1)
                                }
                            }
                        }
                    }
                }
            });

            $('#getSelected').on('click', function () {

                if (temp_all_list.length == 0) {
                    layer.msg("请选择要合并的文件")
                    return;
                }

                var fileNo = "";

                for (var i = 0; i < temp_all_list.length; i++) {
                    fileNo += (i > 0 ? "," : "") + "'" + temp_all_list[i].FileNo + "'";
                }

                DHRMerge("10", fileNo)
                temp_all_list = []
                $("#DHRQuery_open").click()
            });



            $("#DHRBatchMerge").click(function () {

                if ($("#txtSSerial").val() == "") {
                    layer.msg("请输入序列号")
                    return;
                }

                DHRMerge("9", "")
                temp_all_list = []
                $("#DHRQuery_open").click()
            })


            table.on('tool(DHRQuery)', function (obj) {
                var data = obj.data, layEvent = obj.event;

                if (layEvent == "view") {
                    var path = data.RPath + data.FileName
                    window.open(path)
                }

            })
        })

        function uniqueById(arr1, arr2) {
            // 合并两个数组
            const mergedArray = [...arr1, ...arr2];

            // 使用Map来跟踪已经见过的id
            const seenIds = new Map();

            // 过滤掉重复的对象
            const uniqueObjects = mergedArray.filter(obj => {
                // 检查id是否已经在Map中
                if (seenIds.has(obj.id)) {
                    return false; // 已经存在，过滤掉
                }
                seenIds.set(obj.id, true); // 记录下这个id
                return true; // 保留这个对象
            });

            return uniqueObjects;
        }


        function GetProcedureList() {
            var sSs = "工序";
            var keywords = encodeURI(sSs);
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/TechAjax.ashx?OP=GetProcedureList&CFlag=111-1&CKind=" + keywords,
                success: function (data) {
                    var parsedJson = eval(data).Msg;
                    $("#txtSProcedure").empty();
                    $("#txtSProcedure").append(parsedJson);
                    // 刷新下拉框
                    $('#txtSProcedure').selectpicker('refresh');
                }
            });
        }

        function GetBatchNoList() {
            var stxtSSerial = $("#txtSSerial").val();

            if (stxtSSerial == "") {
                return;
            }

            var Data = '';
            var Params = { No: stxtSSerial, Name: "", Item: "", MNo: "", MName: "", A: "", B: "", C: "", D: "", E: "", F: "", InMan: "", Remark: "", Flag: "8" };
            var Data = JSON.stringify(Params);

            $.ajax({
                url: "../Service/OrderAjax.ashx?OP=GetExportConditions&CFlag=8&Data=" + Data,
                data: {},
                type: "GET",
                success: function (res) {
                    var Json = jQuery.parseJSON(res);
                    var batchNo = Json[0]

                    var sKong = "<option value=''> </option>";

                    for (var i = 0; i < batchNo.length; i++) {
                        sKong += "<option value='" + batchNo[i].FileBatchNo + "'>" + batchNo[i].FileBatchNo + "</option>"
                    }


                    $("#txtSBatch").append(sKong);
                },
                error: function () {

                }
            })
        }

        function DHRMerge(Flag, FileNo) {
            $("#Doing").show()
            $("#DHRBatchMerge").attr("disabled", "disabled")
            $("#getSelected").attr("disabled", "disabled")

            var sSerial = $("#txtSSerial").val()
            var Data = '';
            var Params = { No: sSerial, Item: "", Name: ModuleName, MNo: "", MName: "", Status: "", BDate: "", EDate: "", A: FileNo, B: "", C: "", D: "", Flag: Flag };
            var Data = JSON.stringify(Params);

            $.ajax({
                url: "../Service/DHRAjax.ashx?OP=DHRMergeOP&CFlag=9",
                data: {
                    Data: Data
                },
                type: "POST",
                success: function (res) {
                    var parseJSON = jQuery.parseJSON(res);
                    if (parseJSON.Msg == "Success") {
                        layer.msg("合并成功")
                        window.open(parseJSON.Path)
                    } else if (parseJSON.Msg == "LoginError") {
                        layer.msg("登录超时，请重新登录！")
                    }
                    else if (parseJSON.Msg == "NullFile") {
                        layer.msg("未找到需要合并的文件！")
                    }
                    else if (parseJSON.Msg == "Error") {
                        layer.msg("合并失败！")
                    }
                    else {
                        layer.msg("系统出错")
                    }
                    $("#Doing").hide()
                    $("#DHRBatchMerge").removeAttr("disabled")
                    $("#getSelected").removeAttr("disabled")
                },
                error: function () {
                    layer.msg("系统出错")
                    $("#Doing").hide()
                    $("#DHRBatchMerge").removeAttr("disabled")
                    $("#getSelected").removeAttr("disabled")
                }
            })
        }
    </script>

    <style>

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }

        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }

        .dropdown-menu, .filter-option {
            font-size: 12px
        }
    </style>

</head>
<body>
    <div class="div_find">
        <label class="find_labela">工单</label> <input type="text" id="txtSOrderNo" class="find_input" />
        <label class="find_labela">序列号</label> <input type="text" id="txtSSerial" class="find_input" />
        <label class="find_labela">文件类型</label>
        <select class="find_input" id="txtFileKind">
            <option value=""></option>
            <option value="DHR">工序追溯表</option>
            <option value="HZ">产品追溯表</option>
            <option value="WX">制程不合格处理单</option>
        </select>
        <label class="find_labela">工序</label>
        <select class="selectpicker show-tick" id="txtSProcedure" multiple=multiple></select>
        <!--<label class="find_labela">时间</label><input type="datetime-local" id="txtStartDate" class="find_input" style="margin-right:0.5%" /> - <input type="datetime-local" id="txtSEndDate" class="find_input" />-->
        <input type="button" value="搜索" class="XC-Btn-Green XC-Size-xs XC-Btn-md" id="DHRQuery_open" />
        <input type="button" value="序列号合并" class="XC-Btn-Green XC-Size-xs XC-Btn-md" id="DHRBatchMerge" style="width:70px" />
        <input type="button" value="自定义合并" class="XC-Btn-Green XC-Size-xs XC-Btn-md" id="getSelected" style="width:80px" />
        <img src="../fonts/loading.gif" width="60px" height="12px" id="Doing" style="display:none;" />
    </div>


    <div class="wangid_conbox">
        <table class="layui-hide" id="DHRQuery" lay-filter="DHRQuery"></table>
        <script type="text/html" id="barDemo">
            <button id="view" lay-event="view" class="XC-Btn-md XC-Btn-Green XC-Size-xs">DHR</button>
        </script>
    </div>

</body>
</html>