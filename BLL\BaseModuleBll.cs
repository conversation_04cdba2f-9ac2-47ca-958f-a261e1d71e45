﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using DAL;

namespace BLL
{
    public class BaseModuleBll
    {
        /// <summary>
        /// 显示用户注册信息列表
        /// </summary>
        /// <param name="sConsSQL"></param>
        /// <param name="LoginMan"></param>
        /// <param name="Comp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable ShowUserRelationList(string sConsSQL, string LoginMan, string Comp, string sFlag)
        {
            return BaseModuleDal.ShowUserRelationList(sConsSQL, LoginMan, Comp, sFlag);
        }


        /// <summary>
        /// 获取用户基本信息
        /// </summary>
        /// <param name="sWhere"></param>
        /// <returns></returns>
        public static DataTable GetUserInfo(string sNo, string sName, string sDept, string sSex, string sComp, string sFlag)
        {
            return BaseModuleDal.GetUserInfo(sNo, sName, sDept, sSex, sComp, sFlag);
        }


        public static DataTable GetSysKind(string Kind, string sComp, string sFlag)
        {
            return BaseModuleDal.GetSysKind(Kind, sComp, sFlag);
        }

        /// <summary>
        /// 新增类别及项次，如菜单，系统类别。分级这种数据
        /// </summary>
        /// <param name="sKind"></param>
        /// <param name="sPINo"></param>
        /// <param name="sPName"></param>
        /// <param name="sIName"></param>
        /// <param name="sCode"></param>
        /// <returns></returns>
        public static string InsertOrUpdateSysKind(string sKind, string sPINo, string sPName, string sIName, string sCode, string sURL, string sSeq, string sComp, string sInMan, string sFlag)
        {
            return BaseModuleDal.InsertOrUpdateSysKind(sKind, sPINo, sPName, sIName, sCode, sURL,sSeq, sComp, sInMan, sFlag);
        }

        /// <summary>
        /// 判断类别是否存在
        /// </summary>
        /// <param name="Kind"></param>
        /// <param name="KindList"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string JudgeSysKindExist(string Kind, string KindList, string QT, string sComp, string sFlag)
        {
            return BaseModuleDal.JudgeSysKindExist(Kind, KindList,QT, sComp, sFlag);
        }


        /// <summary>
        /// 删除系统类别
        /// </summary>
        /// <param name="sKind"></param>
        /// <param name="sItem"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string DeleteSysKind(string sKind, string sItem, string sComp, string sInMan, string sFlag)
        {
            return BaseModuleDal.DeleteSysKind(sKind, sItem, sComp,sInMan, sFlag);
        }


        /// <summary>
        ///  获取供应商信息  (string SupplierNo, string SupplierEn, string sTJ, string sFS, int Row, int num, string LoginMan, string sComp, string sFlag)
        /// </summary>
        /// <param name="SupplierNo"></param>
        /// <param name="SupplierEn"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetSupplierInfo(string SupplierNo, string SupplierEn, string sTJ, string sFS, int Row, int num, string LoginMan, string sComp, string sFlag)
        {
            return BaseModuleDal.GetSupplierInfo(SupplierNo, SupplierEn, sTJ, sFS, Row, num, LoginMan, sComp, sFlag);
        }


        /// <summary>
        /// 获取客户信息/公司信息
        /// </summary>
        /// <param name="CustNo"></param>
        /// <param name="CustName"></param>
        /// <param name="Kind"></param>
        /// <param name="DQ"></param>
        /// <param name="BSC"></param>
        /// <param name="YD"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetCustInfo(string CustNo, string CustEn, string sTJ, string sFS, string sCKind, int Row, int num, string LoginMan, string sComp, string sFlag)
        {
            return BaseModuleDal.GetCustInfo(CustNo, CustEn, sTJ, sFS, sCKind, Row, num, LoginMan, sComp, sFlag);
        }


        /// <summary>
        /// 获取分页记录
        /// </summary>
        /// <param name="sConn">传入查询条件</param>
        /// <param name="Row">每页显示行</param>
        /// <param name="Total">共记录数</param>
        /// <param name="num">现在要显示第几页</param>
        /// <param name="LoginMan">当前登录人</param>
        /// <param name="sCPNOStr">公司代码查询条件</param>
        /// <returns></returns>
        public static DataTable GetPageCustInfo(string sYDNo, string sYD, string sCustNo, string sName, string sBSC, string sCity, int Row, int num, string LoginMan)
        {
            return BaseModuleDal.GetPageCustInfo(sYDNo, sYD, sCustNo, sName, sBSC, sCity, Row, num, LoginMan);

        }



        /// <summary>
        /// 获取功能菜单
        /// </summary>
        /// <param name="UserNo"></param>
        /// <param name="sComp">后面扩展用，根据不同公司显示不同菜单（可新增一个表，专门放公司使用菜单，从这个表获取菜单显示在界面）</param>
        /// <param name="sModel"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetMenu(string UserNo, string sModel, int sModelID, string sComp, string sFlag)
        {
            return BaseModuleDal.GetMenu(UserNo, sModel,sModelID, sComp, sFlag);
        }


        /// <summary>
        /// 删除模块或功能菜单
        /// </summary>
        /// <param name="ID"></param>
        /// <param name="Name"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string DeleteModelInfo(string ID, string Name, string sInMan, string sFlag)
        {
            return BaseModuleDal.DeleteModelInfo(ID, Name, sInMan, sFlag);
        }


        /// <summary>
        /// 获取角色信息
        /// </summary>
        /// <param name="UserNo"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetRoleInfo(string UserNo, string sComp, string sFlag)
        {
            return BaseModuleDal.GetRoleInfo(UserNo, sComp, sFlag);
        }



        /// <summary>
        /// 新增修改用户信息
        /// </summary>
        /// <param name="sKind"></param>
        /// <param name="sNo"></param>
        /// <param name="sName"></param>
        /// <param name="sDept"></param>
        /// <param name="sPwd"></param>
        /// <param name="sZW"></param>
        /// <param name="sSex"></param>
        /// <param name="sTelph"></param>
        /// <param name="sPhone"></param>
        /// <param name="sQQ"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string AddEditUser(string sKind, string sNo, string sName, string sDept, string sPwd, string sZW, string sSex, string sTelph, string sPhone, string sQQ, string sEmail, string sFile, string sPath, string sComp, string sRStr, string sInMan, string sFlag,string IP,string ModuleName)
        {
            return BaseModuleDal.AddEditUser(sKind, sNo, sName, sDept, sPwd, sZW, sSex, sTelph, sPhone, sQQ, sEmail, sFile, sPath, sComp, sRStr, sInMan, sFlag, IP, ModuleName);
        }

        /// <summary>
        /// 删除用户信息
        /// </summary>
        /// <param name="sNo"></param>
        /// <param name="sInMan"></param>
        /// <returns></returns>
        public static string DelUserInfo(string sNo, string sInMan, string IP, string sComp, string ModuleName)
        {
            return BaseModuleDal.DelUserInfo(sNo,sInMan,IP,sComp,ModuleName);
        }


        /// <summary>
        /// 插入角色对应的菜单
        /// </summary>
        /// <param name="sRoleID"></param>
        /// <param name="sComp"></param>
        /// <param name="sRStr"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string InsertRoleModule(string sRoleID, string sComp, string sRStr, string sFStr, string sInMan, string sFlag)
        {
            return BaseModuleDal.InsertRoleModule(sRoleID, sComp, sRStr,sFStr, sInMan, sFlag);
        }

        /// <summary>
        /// 添加角色信息
        /// </summary>
        /// <param name="sName"></param>
        /// <param name="sInMan"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string InsertRoleInfo(string sName,  string sInMan,string sComp, string sFlag)
        {
            return BaseModuleDal.InsertRoleInfo(sName, sInMan,sComp,  sFlag);
        }

        /// <summary>
        /// 删除角色
        /// </summary>
        /// <param name="sNo"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string DeleteRole(string sNo, string sInMan, string sFlag)
        {
            return BaseModuleDal.DeleteRole(sNo, sInMan, sFlag);
        }

        /// <summary>
        /// 获取物料分类
        /// </summary>
        /// <param name="Kind"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetMaterType(string Kind, string sComp, string sFlag)
        {
            return BaseModuleDal.GetMaterType(Kind, sComp, sFlag);
        }

        /// <summary>
        ///  添加物料分类
        /// </summary>
        /// <param name="KNo"></param>
        /// <param name="MName"></param>
        /// <param name="FKind"></param>
        /// <param name="Location"></param>
        /// <param name="Remark"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string AddEditMaterType(string MTID, string KNo, string MName, string FKID, string Location,string FType, string Remark, string sComp, string sInMan, string sFlag)
        {
            return BaseModuleDal.AddEditMaterType(MTID, KNo, MName, FKID, Location, FType, Remark, sComp, sInMan, sFlag);
        }

        /// <summary>
        /// 删除物料分类
        /// </summary>
        /// <param name="MTID"></param>
        /// <param name="sMater"></param>
        /// <param name="sNo"></param>
        /// <param name="sLoca"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string DelMaterType(string MTID, string sMater, string sNo, string sLoca, string sComp, string sInMan, string sFlag)
        {
            return BaseModuleDal.DelMaterType(MTID, sMater, sNo, sLoca, sComp, sInMan, sFlag);
        }

        /// <summary>
        ///  获取物料信息
        /// </summary>
        /// <param name="MaterNo"></param>
        /// <param name="MaterName"></param>
        /// <param name="Kind"></param>
        /// <param name="MaterSpec"></param>
        /// <param name="TechNo"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetMaterInfo(string MaterNo, string MaterName, string Kind, string MaterSpec, string BigType, int Row, int num, string sComp, string LoginMan, string sFlag)
        {
            return BaseModuleDal.GetMaterInfo(MaterNo, MaterName, Kind, MaterSpec, BigType, Row, num, sComp, LoginMan, sFlag);
        }

        //新增，修改物料信息
        public static string InsertOrUpdateMater(string No, string Name, string PN, string Spec, string wllb, string Kd, string Un, string Bt, string Ft, string lx, string cl, string cc, string xz, string na, string fov, string cg, string gq, string jj,
            string dm, string pt, string hd, string zj, string tx, string jk, string mtf, string qx, string gb, string bl, string bs, string xn, string gz, string rs, string sm, string date, string spl, string prd, string jz, string mz,
            string gl, string pldy, string tz, string C1, string C2, string C3, string C4, string C5, string C6, string C7, string C8, string C9, string C10, string Remark,string IP, string sComp, string sInMan, string sFlag)
        {
            //  No   Name   Spec   Un 单位    Kd  物料类别   Bt  大类  Ft  分类   lx 类型(Type)  cl  材料(Material)  cc 尺寸(Size)  xz  形状(Shape)   na  NA(F#)  fov   FOV  cg  传感器格式  gq  光圈
            //  jj     焦距   dm   -- 镀膜   pt   平坦度(Flatness)  波长范围   --- 镜头的   hd  厚度(Thickness)   (传感器)分辨率   --- 镜头的  
            // zj   光束直径  tx   图像大小   jk  接口  mtf MTF  qx   畸变  gb   光斑尺寸  bl   倍率  bs   放大倍数  xn   性能  gz    工作距离  rs  入射光直径  sm    扫描角度 rmk 备注

            return BaseModuleDal.InsertOrUpdateMater(No, Name, PN, Spec, wllb, Kd, Un, Bt, Ft, lx, cl, cc, xz, na, fov, cg, gq, jj, dm, pt, hd, zj, tx, jk, mtf, qx, gb, bl, bs, xn, gz, rs, sm, date, spl, prd, jz, mz, gl, pldy, tz, C1, C2, C3, C4, C5, C6, C7, C8, C9, C10, Remark, IP, sComp, sInMan, sFlag);
        }


        /// <summary>
        /// 获取可供物料
        /// </summary>
        /// <param name="MaterNo"></param>
        /// <param name="MaterName"></param>
        /// <param name="MaterSpec"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetCustMaterInfo(string MaterNo, string MaterName, string MaterSpec, string sStock, string sComp, string sFlag)
        {
            return BaseModuleDal.GetCustMaterInfo(MaterNo, MaterName, MaterSpec, sStock,sComp, sFlag);
        }

        /// <summary>
        /// 维护客供料信息
        /// </summary>
        /// <param name="MaterNo"></param>
        /// <param name="MName"></param>
        /// <param name="Spec"></param>
        /// <param name="Stock"></param>
        /// <param name="Remark"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string AddEditCustMater(string MaterNo, string MName, string Spec, string Stock, string Remark, string sComp, string sInMan, string sFlag)
        {
            return BaseModuleDal.AddEditCustMater(MaterNo, MName, Spec, Stock, Remark, sComp, sInMan, sFlag);
        }


        /// <summary>
        /// 删除客供物料
        /// </summary>
        /// <param name="sNo"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string DelCustMater(string sNo, string sComp, string sInMan, string sFlag)
        {
            return BaseModuleDal.DelCustMater(sNo,sComp, sInMan, sFlag);
        }


        /// <summary>
        /// 获取供应商编码
        /// </summary>
        /// <param name="sCode"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string GetSupplierNo(string sGrade, string sComp, string sFlag)
        {
            return BaseModuleDal.GetSupplierNo(sGrade, sComp, sFlag);
        }

        /// <summary>
        /// 维护供应商信息
        /// </summary>
        /// <param name="sNo"></param>
        /// <param name="sName"></param>
        /// <param name="sDesc"></param>
        /// <param name="sEn"></param>
        /// <param name="sKind"></param>
        /// <param name="sGrade"></param>
        /// <param name="sCode"></param>
        /// <param name="sPItem"></param>
        /// <param name="sPType"></param>
        /// <param name="sSL"></param>
        /// <param name="sBZ"></param>
        /// <param name="sCode"></param>
        /// <param name="sIndust"></param>
        /// <param name="sSF"></param>
        /// <param name="sCity"></param>
        /// <param name="sInCH"></param>
        /// <param name="sPhone"></param>
        /// <param name="sFax"></param>
        /// <param name="sAddr"></param>
        /// <param name="sWebsite"></param>
        /// <param name="sRemark"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string InsertOrUpdateSupplier(string sNo, string sName, string sDesc, string sEn, string sKind, string sGrade, string sCode, string sPItem, string sPType, string sSL, string sBZ, string sIndust, string sSF, string sCity, string sInCH, string sPhone, string sFax, string sAddr, string sEmail, string sWebsite, string sRemark, string sComp, string sInMan, string sFlag)
        {
            return BaseModuleDal.InsertOrUpdateSupplier(sNo, sName, sDesc, sEn, sKind, sGrade, sCode, sPItem, sPType, sSL, sBZ,sIndust, sSF, sCity, sInCH, sPhone, sFax, sAddr,sEmail, sWebsite, sRemark, sComp, sInMan, sFlag);
        }


        /// <summary>
        /// 返回BOM结构的树形
        /// </summary>
        /// <param name="FNo"></param>
        /// <param name="FName"></param>
        /// <param name="CNo"></param>
        /// <param name="CName"></param>
        /// <param name="GX"></param>
        /// <param name="Row"></param>
        /// <param name="num"></param>
        /// <param name="Comp"></param>
        /// <returns></returns>
        public static DataTable GetBOMInfo(string FNo, string FName, string CNo, string CName, string GX, string InStock, string sNo, string sA, string sB, string sC, string sD, int Row, int num, string Comp, string InMan, string Flag)
        {
            return BaseModuleDal.GetBOMInfo(FNo, FName, CNo, CName, GX, InStock, sNo,sA,sB, sC, sD, Row, num, Comp, InMan, Flag);
        }

        // 返回工作中心树形结构
        public static DataTable GetWorkCenterInfo(string FNo, string FName, string CNo, string CName, string GX, int Row, int num, string Comp)
        {
            return BaseModuleDal.GetWorkCenterInfo(FNo, FName, CNo, CName, GX, Row, num, Comp);
        }


        /// <summary>
        /// 新增，修改，删除BOM信息
        /// </summary>
        /// <param name="sFNo"></param>
        /// <param name="sFName"></param>
        /// <param name="sNewFNo"></param>
        /// <param name="sNewFName"></param>
        /// <param name="sCNo"></param>
        /// <param name="sCName"></param>
        /// <param name="sUseNum"></param>
        /// <param name="sGX"></param>
        /// <param name="sRemark"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string InsertOrUpdateBOM(string sFNo, string sFName, string sNewFNo, string sNewFName, string sCNo, string sCName, string sUseNum, string sGX, string sPack, string A, string B, string C, string D, string E, string F, string sRemark, string sComp, string sInMan, string sFlag,string IP)
        {
            return BaseModuleDal.InsertOrUpdateBOM(sFNo, sFName, sNewFNo, sNewFName, sCNo, sCName, sUseNum, sGX, sPack, A, B, C, D, E, F, sRemark, sComp, sInMan, sFlag, IP);
        }


        /// <summary>
        /// 新增，修改，删除客户信息
        /// </summary>
        /// <param name="sNo"></param>
        /// <param name="sName"></param>
        /// <param name="sEn"></param>
        /// <param name="sCode"></param>
        /// <param name="sPItem"></param>
        /// <param name="sPType"></param>
        /// <param name="sSL"></param>
        /// <param name="sBZ"></param>
        /// <param name="sInCH"></param>
        /// <param name="sPhone"></param>
        /// <param name="sFax"></param>
        /// <param name="sAddr"></param>
        /// <param name="sEmail"></param>
        /// <param name="sRemark"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string InsertOrUpdateCust(string sNo, string sName, string sEn, string sCode, string sPItem, string sPType, string sSL, string sBZ, string sInCH, string sPhone, string sFax, string sAddr, string sEmail, string sRemark, string sComp, string sInMan, string sFlag)
        {
            return BaseModuleDal.InsertOrUpdateCust(sNo, sName, sEn, sCode, sPItem, sPType, sSL, sBZ, sInCH, sPhone, sFax, sAddr, sEmail, sRemark, sComp, sInMan, sFlag);
        }


        /// <summary>
        /// 获取厂商识别码信息
        /// </summary>
        /// <param name="MCode"></param>
        /// <param name="Center"></param>
        /// <param name="BD"></param>
        /// <param name="ED"></param>
        /// <param name="BED"></param>
        /// <param name="EED"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetMCodetInfo(string MCode, string Center, string BD, string ED, string BED, string EED, string sComp, string sFlag)
        {
            return BaseModuleDal.GetMCodetInfo(MCode, Center, BD, ED, BED, EED, sComp, sFlag);
        }


        /// <summary> 
        /// 新增，修改，删除 厂商识别码 
        /// </summary>
        /// <param name="MCode"></param>
        /// <param name="Center"></param>
        /// <param name="PD"></param>
        /// <param name="ED"></param>
        /// <param name="InCharge"></param>
        /// <param name="Remark"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string OPMCodetInfo(string MCode, string Center, string MCodeType, string NowNum, string LNum, string PD, string ED, string InCharge, string Remark, string sComp, string sInMan, string sFlag)
        {
            return BaseModuleDal.OPMCodetInfo(MCode, Center, MCodeType, NowNum, LNum, PD, ED, InCharge, Remark, sComp, sInMan, sFlag);
        }


        /// <summary>
        /// 获取发号物料信息
        /// </summary>
        /// <param name="MNo"></param>
        /// <param name="MName"></param>
        /// <param name="Row"></param>
        /// <param name="num"></param>
        /// <param name="LoginMan"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetSerielMater(string PMNo, string PMName, string Model, string MNo, string MName, string A, string B, string C, string D, string BDate, string EDate, int Row, int num, string LoginMan, string sComp, string sFlag)
        {
            return BaseModuleDal.GetSerielMater(PMNo, PMName, Model, MNo, MName,A,B,C,D, BDate, EDate, Row, num, LoginMan, sComp, sFlag);
        }


        // 操作发号物料表的数据
        public static string OPSerielMater(string MNo, string MName, string Model, string S1, string S2, string S3, string S4, string S5, string S6, string S7, string IValue, string NValue, string UseFlag, string BNum, string sComp, string sInMan, string Remark, string sFlag)
        {
            return BaseModuleDal.OPSerielMater(MNo, MName, Model, S1, S2, S3, S4, S5, S6, S7, IValue, NValue, UseFlag, BNum, sComp, sInMan, Remark, sFlag);
        }


        // 获取已发放的序列号信息
        public static DataTable GetSerielInfo(string MNo, string Model, string Serial, string OrderNo, string BDate, string EDate, int Row, int num, string LoginMan, string sComp, string sFlag)
        {
            return BaseModuleDal.GetSerielInfo(MNo, Model, Serial, OrderNo, BDate, EDate, Row, num, LoginMan, sComp, sFlag);
        }


        // 发放序列号，或禁用一些序列号
        public static string OPSerielInfo(string OrderNo, string Serial, string MNo, string FNum, string BNum, string A, string B, string C, string sComp, string sInMan, string sOrderFlag, string sFlag)
        {
            return BaseModuleDal.OPSerielInfo(OrderNo, Serial, MNo, FNum, BNum,A,B,C, sComp, sInMan, sOrderFlag, sFlag);
        }


        // 对UDI码进行操作
        public static string OPUDIInfo(string DI, string MNo, string Spec, string S1, string S2, string S3, string S4, string S5, string S6, string S7, string S8, string S9, string S10, string S11, string S12, string S13, string S14, string S15, string S16, string S17, string S18, string sComp, string sInMan, string sFlag)
        {
            return BaseModuleDal.OPUDIInfo(DI, MNo, Spec, S1, S2, S3, S4, S5, S6, S7, S8, S9, S10, S11, S12, S13, S14, S15, S16, S17, S18, sComp, sInMan, sFlag);
        }


        // 操作产品编码对应的标贴
        public static string OPProductLabel(string FMNo, string FMName, string MNo, string MName, string S1, string S2, string S3, string S4, string S5, string S6, string S7, string S8, string S9, string S10, string UseFlag, string sComp, string sInMan, string Remark, string sFlag)
        {
            return BaseModuleDal.OPProductLabel(FMNo, FMName, MNo, MName, S1, S2, S3, S4, S5, S6, S7, S8, S9, S10, UseFlag, sComp, sInMan, Remark, sFlag);
        }


        // 获取打印标贴信息，如UDI信息，外箱标贴
        public static DataTable GetPrintLabelInfo(string MNo, string Model, string OrderNo, string Serial, string BDate, string EDate, int Row, int num, string LoginMan, string sComp, string sFlag)
        {
            return BaseModuleDal.GetPrintLabelInfo(MNo, Model, OrderNo, Serial, BDate, EDate, Row, num, LoginMan, sComp, sFlag);
        }

        // 操作公司基本信息
        public static string OPCompanyInfo(string CompID, string CName, string CJC, string CEN, string LMan, string Code, string Category, string BScope, string RAmount, string Number, string RAddr, string CAddr, string PAddr, string PCode, string CMan, string Phone, string Fax, string EMail, string Website, string APPID, string APPSECRET, string XKZ, string sComp, string sInMan, string Remark, string sFlag)
        {
            return BaseModuleDal.OPCompanyInfo(CompID, CName, CJC, CEN, LMan, Code, Category, BScope, RAmount, Number, RAddr, CAddr, PAddr, PCode, CMan, Phone, Fax, EMail, Website, APPID, APPSECRET, XKZ, sComp, sInMan, Remark, sFlag);
        }


        // 获取一些接口地址及法规信息
        public static DataTable GetURLAndBaseInfo(string No, string C1, string C2, string sComp, string sFlag)
        {
            return BaseModuleDal.GetURLAndBaseInfo(No, C1, C2, sComp, sFlag);
        }

        //获取物料批序号 
        public static DataTable GetBatchSerial(int limit, int page, string sMan, string MaterNo, string MaterName, string BatchNo, string Flag)
        {
            return BaseModuleDal.GetBatchSerial(limit, page, sMan, MaterNo, MaterName, BatchNo, Flag);
        }

        public static string OPBatchSerial(string BatchNo, string MaterNo, string MaterName, string Stock, string Comp, string Man, string OPFlag, string Flag, string BatchNoOld)
        {
            return BaseModuleDal.OPBatchSerial(BatchNo, MaterNo, MaterName, Stock, Comp, Man, OPFlag, Flag, BatchNoOld);
        }

        //获取物日志信息
        public static DataTable GetBaseInfoList(string No, string Name, string A, string B, string C, string D, string E, string F, string G, string H, string BDate, string EDate, int limit, int page, string sMan, string Flag)
        {
            return BaseModuleDal.GetBaseInfoList(No, Name, A, B, C, D, E, F, G, H, BDate, EDate, limit, page, sMan, Flag);
        }

        //对基础数据相关操作
        public static string OPBaseInfoList(string No, string Name, string A, string B, string C, string D, string E, string F, string G, string H,string sMan, string sComp, string Flag,string Remark, string IP)
        {
            return BaseModuleDal.OPBaseInfoList(No, Name, A, B, C, D, E, F, G, H, sMan, sComp, Flag, Remark, IP);
        }
























    }
}
