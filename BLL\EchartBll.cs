﻿using DAL;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Xml.Linq;

namespace BLL
{
    public class EchartBll
    {
        public static string TemplateBaseOP(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string Remark, string sLogin, string sComp, string Flag)
        {
            return EchartDal.TemplateBaseOP(No, Name, Item, MNo, MName, A, B, C, D, E, F, Remark, sLogin, sComp, Flag);
        }

        public static DataTable GetTemplateBase(string No, string Name, string Item, string MNo, string MName, string Status, string sBDate, string sEDate, string A, string B, string C, string D, string E, string F, string G, string H, string I, string J, string K, string L, int rows, int page, string sLogin, string sComp, string Flag)
        {
            return EchartDal.GetTemplateBase(No, Name, Item, MNo, MName, Status, sBDate, sEDate, A, B, C, D, E, F, G, H, I, J, K, L, rows, page, sLogin, sComp, Flag);
        }

        public static List<DataTable> GetEchartData(string No, string Name, string Item, string MNo, string MName, string Status, string sBDate, string sEDate, string A, string B, string C, string D, string E, string F, string G, string H, string I, string J, string K, string L, int rows, int page, string sLogin, string sComp, string Flag)
        {
            return EchartDal.GetEchartData(No, Name, Item, MNo, MName, Status, sBDate, sEDate, A, B, C, D, E, F, G, H, I, J, K, L, rows, page, sLogin, sComp, Flag);
        }

        public static string AddTask(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string Remark, string sLogin, string sComp, string Flag)
        {
            return EchartDal.AddTask(No, Name, Item, MNo, MName, A, B, C, D, E, F, Remark, sLogin, sComp, Flag);
        }
    }
}
