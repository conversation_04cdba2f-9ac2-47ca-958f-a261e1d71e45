﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html" ;=; charset="utf-8" />
    <title>物料主数据</title>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/XC.css" rel="stylesheet" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/BaseModule.js" type="text/javascript"></script>

    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=23&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=23&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        //$('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=25-1' });
                    }
                }
            })
        });


        function Sheet_onclick(num) {

            if ((num == 11) || (num == 12) || (num == 13) || (num == 14) || (num == 15) || (num == 16) || (num == 17)) { // 主界面查看信息时使用

                document.getElementById("DivSheet" + num).style.display = "block";
                document.getElementById("Sheet" + num).setAttribute("style", "background: #0c873d; color: white");
                for (var i = 11; i <= 17; i++) {
                    if (i != num) {
                        document.getElementById("DivSheet" + i).style.display = "none";
                        document.getElementById("Sheet" + i).setAttribute("style", "background: #f8f9f9; color: black");
                    }
                }
            }
            else if ((num == 31) || (num == 32) || (num == 33) || (num == 34) || (num == 35) || (num == 36) || (num == 37)) // 新增时使用
            {
                var Fla = $('#txtAddKind').val();

                document.getElementById("DivSheet" + num).style.display = "block";
                document.getElementById("Sheet" + num).setAttribute("style", "background: #0c873d; color: white");
                for (var i = 31; i <= 37; i++) {
                    if (i != num) {
                        document.getElementById("DivSheet" + i).style.display = "none";
                        document.getElementById("Sheet" + i).setAttribute("style", "background: #f8f9f9; color: black");
                    }
                }

                if ((num == 31) && (Fla == "1")) {      //Fla=="1"  表示新增
                    $('#txtBigType').val("光学元件");
                    $('#HeadDiv').hide();

                }
                else if ((num == 32) && (Fla == "1")) {  // 镜头
                    $('#txtBigType').val("镜头");
                    $('#HeadDiv').hide();

                }
                else if ((num == 33) && (Fla == "1")) {  // 工装
                    $('#txtBigType').val("工装");
                    $('#HeadDiv').hide();

                }
                else if ((num == 34) && (Fla == "1")) { //  抽样方案
                    $('#txtBigType').val("抽样方案");
                    $('#HeadDiv').hide();
                }
                else if ((num == 35) && (Fla == "1")) {  //  组件（元件+结构件
                    $('#txtBigType').val("组件");
                    $('#HeadDiv').hide();

                }
                else if ((num == 36) && (Fla == "1")) {  //  UDI产品
                    $('#HeadDiv').hide();
                    $('#txtBigType').val("UDI属性");

                }
                else if ((num == 37) && (Fla == "1")) {  //  通用属性
                    $('#HeadDiv').hide();

                }




            }  // 新增时使用


        }






    </script>

    <script type="text/javascript">


        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#mylist',
                id: 'MaterID',
                url: '../Service/BaseModuleAjax.ashx?OP=GetMaterInfo&sFlag=51',
                height: 'full-290',
                cellMinWidth: 80,
                count: 50, //数据总数 服务端获得
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    //{ type: 'checkbox' },
                    { field: 'MaterNo', title: '物料编码', width: 150, unresize: true, sort: true },
                    { field: 'MaterName', title: '物料名称', width: 350, sort: true },
                    { field: 'MaterDesc', title: '物料描述', width: 350, sort: true },
                    { field: 'MaterCategory', title: '物料类别', minWidth: 80, sort: true },
                    { field: 'MaterLB', title: '物料分组', minWidth: 80, sort: true },
                    //{ field: 'BigType', title: '大类', minWidth: 80 },
                    //{ field: 'FillMaterKind', title: '分类', width: 200 },
                    { field: 'MaterType', title: '类型(Type)', width: 200 },
                    { field: 'MaterSpec', title: '规格型号', width: 120 },
                    { field: 'MaterUnit', title: '单位', width: 60 },
                    { field: 'MaterKind', title: '产品类别', width: 90 },
                    { field: 'PurchCircle', title: '复检周期', width: 90 },
                    { field: 'InMan', title: '录入人', width: 110, edit: 'text' },
                    { field: 'InDate', title: '录入时间', width: 150 },
                    { field: 'op', title: '操作', width: 120, toolbar: '#barDemo', fixed: 'right' }
                ]],
                page: true,
                even: true
            });

            var num = 16;

            //document.getElementById("SheetN").style.display = "block";
            //document.getElementById("DivSheet11").style.display = "none";
            //document.getElementById("DivSheet12").style.display = "none";
            //document.getElementById("DivSheet13").style.display = "none";
            //document.getElementById("DivSheet14").style.display = "none";
            //document.getElementById("DivSheet15").style.display = "none";
            //document.getElementById("DivSheet16").style.display = "block";
            //document.getElementById("DivSheet17").style.display = "none"; // 通用属性

            document.getElementById("DivSheet" + num).style.display = "block";
            document.getElementById("Sheet" + num).setAttribute("style", "background-color:#0c873d; color: white;");
            for (var i = 11; i <= 17; i++) {
                if (i != num) {
                    document.getElementById("DivSheet" + i).style.display = "none";
                    document.getElementById("Sheet" + i).setAttribute("style", "background-color:#f8f9f9; color: block;");
                }
            }

            //监听是否选中操作
            table.on('checkbox(mylist)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="MaterSpec"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="MaterSpec"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);


            });


            //监听单元格编辑
            table.on('edit(mylist)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });

            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(mylist)', function (obj) {

                //var iSheet = 16;
                //document.getElementById("DivSheet" + iSheet).style.display = "block";
                //document.getElementById("Sheet" + iSheet).setAttribute("style", "width:80px; background-color:#56dcaf; color:white; text-align:center; font-size:12px;");
                //for (var i = 11; i <= 17; i++) {
                //    if (i != iSheet) {
                //        document.getElementById("DivSheet" + i).style.display = "none";
                //        document.getElementById("Sheet" + i).setAttribute("style", "width:80px; background-color:white; text-align:center; font-size:12px;");
                //    }
                //}

                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

                $('#txtFTypeW').val(obj.data.FillMaterKind); //  分类
                $('#txtMaterTypeW').val(obj.data.MaterType); //    类型(Type)
                if (obj.data.BigType == "光学元件888") { // 暂时不用
                    iSheet = 11;

                } else if (obj.data.BigType == "镜头888") {// 暂时不用
                    iSheet = 12;

                } else if (obj.data.BigType == "工装888") {// 暂时不用
                    iSheet = 13;

                } else if (obj.data.BigType == "抽样方案") {
                    iSheet = 14;

                    $('#txtSamplPlanW').val(obj.data.MLength); //
                    //$('#txtSpecialProtection9W').val(obj.data.SpecialProtection); //  颜色

                } else if (obj.data.BigType == "组件888") {  // 暂时不用
                    iSheet = 15;

                    //$('#txtMaterXH10W').val(obj.data.MaterXH); // 尺寸
                    //$('#txtTechNo10W').val(obj.data.TechNo); // 工艺
                }
                //else if (obj.data.BigType == "UDI属性") {  // (obj.data.BigType == "UDI产品")  {
                //   iSheet =16;
                //   $('#HeadDiv').hide();
                //}
                //  显示UDI属性
                $('#txtPDNameW').val(obj.data.PName);   //  产品名称 PName
                $('#txtSPNameW').val(obj.data.MaterXH);   //  商品名称 MaterXH
                $('#txtBLCPW').val(obj.data.Mount);   //  包类产品 Mount
                $('#txtYMLDMW').val(obj.data.TechNo);   //  原目录代码 TechNo
                $('#txtQXLBW').val(obj.data.MaterType);   //  器械类别 MaterType
                $('#txtFLBMW').val(obj.data.FillMaterKind);   //  分类编码 FillMaterKind
                $('#txtZCRCNW').val(obj.data.MHeight);   //  注册备案人 MHeight
                $('#txtZCRENW').val(obj.data.Diameter);   //  注册备案人 Diameter
                $('#txtZCBHW').val(obj.data.Distortion);   //  注册备案编号  Distortion
                $('#txtCPLBW').val(obj.data.MaterKind);   // 产品类别 MaterKind
                $('#txtAQXGW').val(obj.data.StockLacation);   //  安全相关信息  StockLacation
                $('#txtYCXW').val(obj.data.EfftMg);   //  一次性使用 EfftMg
                $('#txtCFSYCSW').val(obj.data.BatchMg);   //  重复使用次数  BatchMg
                $('#txtWJBZW').val(obj.data.KaifengMg);   //  无菌包装 KaifengMg
                $('#txtSYQMJW').val(obj.data.LOSHFlag);   //  使用前灭菌  LOSHFlag
                $('#txtMJFSW').val(obj.data.Coating);   //  灭菌方式  Coating
                $('#txtQTXXW').val(obj.data.StockPlace);   //  其他信息链接  StockPlace
                $('#txtYBBMW').val(obj.data.BatchNo);   // 医保编码 BatchNo
                $('#txtTSRQW').val(obj.data.LastOutDate);   //  退市日期  LastOutDate
                $('#txtPIPHW').val(obj.data.TUnit);   //  PI 批号  TUnit
                $('#txtPIXLHW').val(obj.data.LWUnit);   //  PI 序列号 LWUnit
                $('#txtPISCRQW').val(obj.data.HUnit);   //  PI 生产日期 HUnit
                $('#txtPISXRQW').val(obj.data.InspectMg);   //  PI 失效日期  InspectMg

                $('#txtZoneW').val(obj.data.SuplyNo);   //  销售区域
                $('#txtCPXW').val(obj.data.PrdLine);   //  ，产品线
                $('#txtJZW').val(obj.data.jz);   //  ，净重
                $('#txtMZW').val(obj.data.mz);   //  ，毛重
                $('#txtGLW').val(obj.data.gl);   //  ，功率
                $('#txtPLDYW').val(obj.data.pldy);   // ，频率与电压，
                $('#txtTZW').val(obj.data.tz);   //  体积
                $('#txtJSNoW').val(obj.data.JSNo); //  (3)	产品技术要求编号
                $('#txtPartNameW').val(obj.data.PartName); //  (4)	部件名称
                $('#txtPartSpecW').val(obj.data.PartSpec); //  (5)	部件型号
                $('#txtUseLDateW').val(obj.data.UseLDate); //  (6)	使用期限
                $('#txtCMIITIDW').val(obj.data.CMIITID); //  (7)	CMIIT ID
                $('#txtCSizeW').val(obj.data.CSize); //  (8)	尺寸
                $('#txtNoteW').val(obj.data.Note); // (9)	使用方法及注意事项
                $('#txtSYJJW').val(obj.data.SYJJ); //  (10)	适应症及禁忌症
                $('#txtStoreW').val(obj.data.Store); //  (11)	储存方法
                $('#txtTranspW').val(obj.data.Transp); // (12)	运输条件
                $('#txtSamplPlanW').val(obj.data.MLength); // MWidth 方案编号,MLength 方案名称


            });



            //监听行工具事件
            table.on('tool(mylist)', function (obj) { //注：tool 是工具条事件名，mylist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值
                if (layEvent === 'del') {

                    //obj.del(); //删除对应行（tr）的DOM结构
                    //向服务端发送删除指令
                    var sNo = data.MaterNo;
                    var sName = data.MaterName;

                    $('#ShowDel').css("display", "block");
                    $('#ShowDel-fade').css("display", "block");

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("您确定要删除该物料信息吗？ 物料编码：")
                    //设置删除的对象
                    $("#hint-value").html(sNo)

                    $("#MaterNo").val(sNo);
                    $("#MaterName").val(sName);

                } else if (layEvent === 'edit') {
                    $('#head-title1').html("修改物料信息");
                    $('#txtAddKind').val("2");
                    var iSheet = 36;  // 优先暂时这个sheet页

                    $('#txtBigType').val(data.BigType); //  大类
                    $('#txtMaterNo').val(data.MaterNo);
                    $('#txtMaterName').val(data.MaterName);
                    $('#txtMaterSpec').val(data.MaterSpec);
                    $('#txtUnit').val(data.MaterUnit); //
                    $('#txtKind').val(data.MaterCategory); //  物料类别
                    $('#txtRemark').val(data.Remark);
                    $('#txtZone').val(obj.data.SuplyNo);   //  销售区域
                    $('#txtCPX').val(obj.data.PrdLine);   //  ，产品线
                    $('#txtJZ').val(obj.data.jz);   //  ，净重
                    $('#txtMZ').val(obj.data.mz);   //  ，毛重
                    $('#txtGL').val(obj.data.gl);   //  ，功率
                    $('#txtPLDY').val(obj.data.pldy);   // ，频率与电压，
                    $('#txtTZ').val(obj.data.tz);   //  体积
                    $('#txtJSNo').val(obj.data.JSNo); //  (3)	产品技术要求编号
                    $('#txtPartName').val(obj.data.PartName); //  (4)	部件名称
                    $('#txtPartSpec').val(obj.data.PartSpec); //  (5)	部件型号
                    $('#txtUseLDate').val(obj.data.UseLDate); //  (6)	使用期限
                    $('#txtCMIITID').val(obj.data.CMIITID); //  (7)	CMIIT ID
                    $('#txtCSize').val(obj.data.CSize); //  (8)	尺寸
                    $('#txtNote').val(obj.data.Note); // (9)	使用方法及注意事项
                    $('#txtSYJJ').val(obj.data.SYJJ); //  (10)	适应症及禁忌症
                    $('#txtStore').val(obj.data.Store); //  (11)	储存方法
                    $('#txtTransp').val(obj.data.Transp); // (12)	运输条件


                    $('#txtFType').val(data.FillMaterKind); //  分类
                    $('#txtMaterType').val(data.MaterType); //    类型(Type)

                    $('#txtFTypeR').val(data.FillMaterKind);
                    $('#txtMaterTypeR').val(data.MaterType);
                    $('#txtSamplPlanR').val(data.MWidth); // MWidth 方案编号,MLength 方案名称

                    $('#txtMaterNo').attr({ "disabled": "disabled" });

                    if (data.BigType == "光学元件888") {
                        iSheet = 31;


                    } else if (data.BigType == "镜头888") {
                        iSheet = 32;

                    } else if (data.BigType == "工装888") {
                        iSheet = 33;
                        $('#txtStockLacation8').val(data.StockLacation); //
                    } else if (data.BigType == "抽样方案") {
                        iSheet = 34;

                    } else if (data.BigType == "组件") {
                        iSheet = 35;
                        $('#txtMaterXH10').val(data.MaterXH); //
                        $('#txtTechNo10').val(data.TechNo); //
                    }
                    //else if (data.BigType == "UDI属性") {   //  if (data.BigType == "UDI产品") {
                    //    iSheet = 36;
                    //}
                    $('#txtPDName').val(obj.data.PName);   //  产品名称 PName
                    $('#txtSPName').val(obj.data.MaterXH);   //  商品名称 MaterXH
                    $('#txtBLCP').val(obj.data.Mount);   //  包类产品 Mount
                    $('#txtYMLDM').val(obj.data.TechNo);   //  原目录代码 TechNo
                    $('#txtQXLB').val(obj.data.MaterType);   //  器械类别 MaterType
                    $('#txtFLBM').val(obj.data.FillMaterKind);   //  分类编码 FillMaterKind
                    $('#txtZCRCN').val(obj.data.MHeight);   //  注册备案人 中文 MHeight
                    $('#txtZCREN').val(obj.data.Diameter);   //  注册备案人 英文 Diameter
                    $('#txtZCBH').val(obj.data.Distortion);   //  注册备案编号  Distortion
                    $('#txtCPLB').val(obj.data.MaterKind);   // 产品类别 MaterKind
                    $('#txtAQXG').val(obj.data.StockLacation);   //  安全相关信息  StockLacation
                    $('#txtYCX').val(obj.data.EfftMg);   //  一次性使用 EfftMg
                    $('#txtCFSYCS').val(obj.data.BatchMg);   //  重复使用次数  BatchMg
                    $('#txtWJBZ').val(obj.data.KaifengMg);   //  无菌包装 KaifengMg
                    $('#txtSYQMJ').val(obj.data.LOSHFlag);   //  使用前灭菌  LOSHFlag
                    $('#txtMJFS').val(obj.data.Coating);   //  灭菌方式  Coating
                    $('#txtQTXX').val(obj.data.StockPlace);   //  其他信息链接  StockPlace
                    $('#txtYBBM').val(obj.data.BatchNo);   // 医保编码 BatchNo
                    $('#txtTSRQ').val(obj.data.LastOutDate);   //  退市日期  LastOutDate
                    $('#txtPIPH').val(obj.data.TUnit);   //  PI 批号  TUnit
                    $('#txtPIXLH').val(obj.data.LWUnit);   //  PI 序列号 LWUnit
                    $('#txtPISCRQ').val(obj.data.HUnit);   //  PI 生产日期 HUnit
                    $('#txtPISXRQ').val(obj.data.InspectMg);   //  PI 失效日期  InspectMg

                    GetMaterTypeList("分类");
                    GetTypeXLList(); // 获取小类
                    GetSamplingPlanList(); // 获取抽样方案

                    $('#ShowOne').css("display", "block");
                    $('#ShowOne-fade').css("display", "block");

                    document.getElementById("DivSheet" + iSheet).style.display = "block";
                    document.getElementById("Sheet" + iSheet).setAttribute("style", "background-color:#0c873d; color: white;");
                    for (var i = 31; i <= 37; i++) {
                        if (i != iSheet) {
                            document.getElementById("DivSheet" + i).style.display = "none";
                            document.getElementById("Sheet" + i).setAttribute("style", "background-color:#f8f9f9; color: block;");
                        }
                    }

                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }


            });



            //  查询 -- 物料信息
            $('#MaterBut_open').click(function () {
                var sNo = $("#txtSNo").val();  //编码
                var sName = encodeURI($("#txtSName").val());  // 名称
                var sSpec = encodeURI($("#txtSSpec").val());  // 规格
                var sBT = encodeURI($("#txtSBigType").val());  // 大类

                var Data = '';
                var Params = { No: sNo, Name: sName, Spec: sSpec, BT: sBT };
                var Data = JSON.stringify(Params);

                table.reload('MaterID', {
                    method: 'post',
                    url: '../Service/BaseModuleAjax.ashx?OP=GetMaterInfo&sFlag=51&Data=' + Data,
                    where: {
                        'No': sNo,
                        'name': sName,
                        'TJ': sBT,
                    }, page: {
                        curr: 1
                    }
                });
            });


            $('#MaterBut_Del').click(function () {
                var sNo = $("#MaterNo").val();
                var sName = $("#MaterName").val();
                var Flag = "3";
                var Data = '';
                var Params = { No: sNo, Name: sName, Flag: Flag };
                var Data = JSON.stringify(Params);
                $.ajax({
                    type: "POST",
                    url: "../Service/BaseModuleAjax.ashx?OP=AddEditMater&CFlag=3",
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('删除成功！');

                            $('#ShowDel').css("display", "none");
                            $('#ShowDel-fade').css("display", "none");

                            var sNo = $("#txtSNo").val();  //编码
                            var sName = encodeURI($("#txtSName").val());  // 名称
                            var sSpec = encodeURI($("#txtSSpec").val());  // 规格
                            var sBT = encodeURI($("#txtSBigType").val());  // 大类

                            var Data = '';
                            var Params = { No: sNo, Name: sName, Spec: sSpec, BT: sBT };
                            var Data = JSON.stringify(Params);

                            table.reload('MaterID', {
                                method: 'post',
                                url: '../Service/BaseModuleAjax.ashx?OP=GetMaterInfo&sFlag=51&Data=' + Data,
                                where: {
                                    'No': sNo,
                                    'name': sName,
                                    'TJ': sBT,
                                }, page: {
                                    curr: 1
                                }
                            });
                        } else {
                            layer.msg('删除失败，请重试！')
                        }
                    },
                    error: function (data) {
                        layer.msg('删除失败2，请重试！')
                    }
                });
            })


        });


        function openDialog(n) {  // 新增
            $('#head-title1').html("新增物料信息");
            //$('#txtBigType').val("光学元件");
            $('#txtAddKind').val("1");
            var iSheet = 36;  // 优先暂时这个sheet页

            $('#txtMaterNo').val("");
            $('#txtMaterName').val("");
            $('#txtMaterSpec').val("");
            $('#txtUnit').val(""); //
            $('#txtKind').val(""); //
            $('#txtFType').val(""); //  分类
            $('#txtMaterType').val(""); //    类型(Type)
            $('#txtABCKind').val(""); //  材料(Material)
            $('#txtMaterXH').val(""); //  尺寸(Size)
            $('#txtCoating').val(""); //   -- 镀膜
            $('#txtTUnit').val(""); //  形状(Shape)
            $('#txtMLength').val(""); //  焦距(Focal Length)
            $('#txtMThickness').val(""); //    厚度(Thickness)
            $('#txtMWidth').val(""); //  平坦度(Flatness)
            $('#txtLOSHFlag').val(""); //  NA(F#)
            $('#txtHUnit').val(""); //   FOV
            $('#txtMWidthT').val(""); //  波长范围   --- 镜头的
            $('#txtStockPlace').val(""); //  传感器格式
            $('#txtMThicknessT').val(""); //  (传感器)分辨率   --- 镜头的
            $('#txtMLengthT').val(""); //  焦距
            $('#txtStockLacation').val(""); //  光圈
            $('#txtInspectMg').val(""); //  光束直径
            $('#txtEfftMg').val(""); //  图像大小
            $('#txtMount').val(""); //  接口
            $('#txtTechNo').val(""); //    MTF
            $('#txtDistortion').val(""); //  畸变
            $('#txtLWUnit').val(""); //  光斑尺寸
            $('#txtKaifengMg').val(""); //  倍率
            $('#txtBatchMg').val(""); //  放大倍数
            $('#txtSpecialProtection').val(""); // 性能
            $('#txtMHeight').val(""); //    工作距离
            $('#txtDiameter').val(""); //   入射光直径
            $('#txtBatchNo').val(""); //  扫描角度
            $('#txtCoatingT').val(""); //  镀膜(Coating)
            $('#txtRemark').val(""); //  镀膜(Coating)

            $('#txtZone').val("");   //  销售区域
            $('#txtCPX').val("");   //  ，产品线
            $('#txtJZ').val("");   //  ，净重
            $('#txtMZ').val("");   //  ，毛重
            $('#txtGL').val("");   //  ，功率
            $('#txtPLDY').val("");   // ，频率与电压，
            $('#txtTZ').val("");   //  体积

            $('#txtFTypeR').val("");
            $('#txtMaterTypeR').val("");
            $('#txtSamplPlanR').val(""); // Note 方案编号,SYJJ 方案名称

            $('#txtMaterNo').removeAttr("disabled");

            GetMaterTypeList("分类");
            GetSamplingPlanList(); // 获取抽样方案

            $('#ShowOne').css("display", "block");
            $('#ShowOne-fade').css("display", "block");

            document.getElementById("DivSheet" + iSheet).style.display = "block";
            document.getElementById("Sheet" + iSheet).setAttribute("style", "background-color:#0c873d; color: white;");
            for (var i = 31; i <= 37; i++) {
                if (i != iSheet) {
                    document.getElementById("DivSheet" + i).style.display = "none";
                    document.getElementById("Sheet" + i).setAttribute("style", "background-color:#f8f9f9; color: block;");
                }
            }
        }


        function GetMaterTypeList(sZG) {  // 获取物料类别

            var keywords = encodeURI(sZG);  // 解决中文乱码的问题。
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/BaseModuleAjax.ashx?OP=GetMaterTypeList&CFlag=50-2&CKind=" + keywords,
                success: function (data) {
                    var parsedJson = eval(data).Msg;

                    var sKong = "<option value=''> </option>";
                    $("#txtFType").empty();
                    $("#txtFType").append(sKong + parsedJson);

                    $('#txtFType').val($('#txtFTypeR').val());
                }
            });
        }


        function GetTypeXLList() {  // 获取小类

            var sSs = $('#txtFTypeR').val();
            if (sSs == "") {
                sSs = $('#txtFType').val();
            }

            var keywords = encodeURI(sSs);  // 解决中文乱码的问题。
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/BaseModuleAjax.ashx?OP=GetMaterTypeList&CFlag=50-3&CKind=" + keywords,
                success: function (data) {
                    var parsedJson = eval(data).Msg;

                    var sKong = "<option value=''> </option>";
                    $("#txtMaterType").empty();
                    $("#txtMaterType").append(sKong + parsedJson);

                    $('#txtMaterType').val($('#txtMaterTypeR').val());
                }
            });
        }

        function GetSamplingPlanList() {  // 获取抽样方案

            var keywords = "";// encodeURI(sSs);  // 解决中文乱码的问题。
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/BaseModuleAjax.ashx?OP=GetSamplingPlanList&CFlag=60-1&CKind=" + keywords,
                success: function (data) {
                    var parsedJson = eval(data).Msg;

                    var sKong = "<option value=''> </option>";
                    $("#txtSamplPlan").empty();
                    $("#txtSamplPlan").append(sKong + parsedJson);

                    $('#txtSamplPlan').val($('#txtSamplPlanR').val());  // $('#txtSamplPlanR').val() 这个值，如果用名称，下拉选择不会显示，需要用编号，即控件的VALUE值

                }
            });
        }



        function closeDialog() {
            $('#ShowOne').css("display", "none");
            $('#ShowOne-fade').css("display", "none");
            $('#ShowDel').css("display", "none");
            $('#ShowDel-fade').css("display", "none");
        }



    </script>

    <script type="text/javascript">
        $(function () {

            $("#Toggle").click(function () {
                if ($("#Toggle").html() == "展开") {
                    $("#Toggle").html("收起");
                } else {
                    $("#Toggle").html("展开");
                }
                $('#open').fadeToggle(50);
            })
        })
    </script>

    <style type="text/css">

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }

        /*  .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: #bbbcc7;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=60);
        }*/

        .LabelDelBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #da542e;
        }

        .LabelAddBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #579fd8;
        }

        .table-tbs td {
            width: 70px;
            font-size: 12px;
            cursor: pointer;
            text-align: center;
            color: white
        }

        .table-tbs {
            width: 100%
        }

        .table-tbs-td {
            height: 30px;
            border-top: 1px solid #dee1e6;
            border-right: 1px solid #dee1e6;
            border-left: 1px solid #dee1e6;
        }


        .XCTYSZ input, .XCUDISX input, .XCCYFA input,
        .showOneHeaderTable input, .showOneHeaderTable select, .showOneHeaderTable textarea,
        .showTYSX input, .showTYSX select, showCYFA input, .showCYFA select, .showUDISX input, .showUDISX select {
            height: 28px;
            width: 90%;
            font-size: 12px;
            border: 1px solid #E5E5E5;
            border-radius: 2px;
            padding: 0px 5px;
            margin-left: 5px
        }

        .XCTYSZ td, .XCUDISX td, .XCCYFA td, .showTYSX td, .showCYFA td, .showUDISX td {
            font-size: 12px;
            color: #808080;
        }

        .showOneHeaderTable {
            width: 100%;
            border-color: #E5E5E5
        }

            .showOneHeaderTable td {
                padding: 5px;
                font-size: 12px;
                color: #808080;
            }

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left:0.5%;
        }
    </style>

</head>
<body>
    <div class="div_find">
        <p>
            <label class="find_labela">物料编码 </label><input type="text" id="txtSNo" class="find_input" />
            <label class="find_labela">物料描述 </label><input type="text" id="txtSName" class="find_input" />
            <label class="find_labela">规格型号 </label><input type="text" id="txtSSpec" class="find_input" />
            <label class="find_labela" style="display:none;">大&nbsp;&nbsp;&nbsp;&nbsp;类：</label><select class="find_input" id="txtSBigType" style="display:none;">
                <option></option>
                <option>UDI属性</option>
                <option>组件</option>
            </select>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="MaterBut_open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="javascript:openDialog(1)">添加</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="Toggle">展开</button>
        </p>

        <p id="open" style="margin-top:3px;display:none">
            <label class="find_labela">物料类别 </label><input type="text" id="txtFillMaterKind" class="find_input" />
        </p>
    </div>
    <!--<div style="text-align:right">-->
    <!--   <i class="down_i" ></i><a href="JavaScript:void(0)" onclick="openDialog(1)" class="add_a">导出</a>-->
    <!--<i class="add2_i" ></i><a href="JavaScript:void(0)" onclick="openDialog(1)" style="color: Blue">添加</a>
    </div>-->
    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="mylist" lay-filter="mylist"></table>
        <script type="text/html" id="barDemo">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="edit">修改</button>
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>
        </script>
    </div>

    <!-- 暂时sheet数据   -->
    <div class="XC-Tab1">
        <ul class="XC-tabs1">
            <li id="Sheet16" onclick="Sheet_onclick(16)">UDI属性</li>
            <li id="Sheet17" onclick="Sheet_onclick(17)">通用属性</li>
            <li id="Sheet14" onclick="Sheet_onclick(14)">抽样方案</li>
            <li id="Sheet15" onclick="Sheet_onclick(15999)"></li>
            <li id="Sheet11" onclick="Sheet_onclick(118888)"></li>
            <li id="Sheet12" onclick="Sheet_onclick(128888)"></li>
            <li id="Sheet13" onclick="Sheet_onclick(13888)"></li>
        </ul>
    </div>

    <div id="DivSheet11" style="height: auto; display: none;  padding-top: 5px;">
        <table cellspacing="0" cellpadding="0" border='0' style="width:98%; ">
            <tr style="height:30px;">
                <td style=" width:120px; text-align:right;">
                    分类:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtFTypeW" name="txtFTypeW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width: 120px; text-align: right;">
                    类型(Type):
                </td>
                <td>
                    <input type="text" class="form-control" id="txtMaterTypeW" name="txtMaterTypeW" style=" height:25px;" readonly=readonly />
                </td>
                <td style="width: 120px; text-align: right; ">
                    材料(Material):
                </td>
                <td>
                    <input type="text" class="form-control" id="txtABCKindW" name="txtABCKindW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width: 120px; text-align: right;">
                    尺寸(Size):
                </td>
                <td>
                    <input type="text" class="form-control" id="txtMaterXHW" name="txtMaterXHW" style=" height:25px;" readonly=readonly />
                </td>
            </tr>
            <tr style="height:30px;">
                <td style=" width: 120px; text-align: right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtCoatingW" name="txtCoatingW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width: 120px; text-align: right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtTUnitW" name="txtTUnitW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width: 120px; text-align: right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtMLengthW" name="txtMLengthW" style=" height:25px;" =/ readonly=readonly />
                </td>
                <td style=" width: 120px; text-align: right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtMThicknessW" name="txtMThicknessW" style=" height:25px;" readonly=readonly />
                </td>
            </tr>
            <tr style="height:30px;">
                <td style=" width: 120px; text-align: right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtMWidthW" name="txtMWidthW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                </td>
            </tr>
        </table>
    </div>
    <div id="DivSheet12" style="height: auto; display: none;  padding-top: 5px; color: #222222 ">
        <table cellspacing="0" cellpadding="0" border='0' style="width: 98%; ">
            <tr style="height:25px;">
                <td style=" width:100px; text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtLOSHFlagW" name="txtLOSHFlagW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtHUnitW" name="txtHUnitW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtMWidthTW" name="txtMWidthTW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtStockPlaceW" name="txtStockPlaceW" style=" height:25px;" readonly=readonly />
                </td>
            </tr>
            <tr>
                <td colspan="6" style=" height:5px;">
                </td>
            </tr>
            <tr style="height:25px;">
                <td style=" width:100px; text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtMThicknessTW" name="txtMThicknessTW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px; text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtMLengthTW" name="txtMLengthTW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtStockLacationW" name="txtStockLacationW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtInspectMgW" name="txtInspectMgW" style=" height:25px;" readonly=readonly />
                </td>
            </tr>
            <tr>
                <td colspan="6" style=" height:5px;">
                </td>
            </tr>
            <tr style="height:25px;">
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtEfftMgW" name="txtEfftMgW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtMountW" name="txtMountW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtTechNoW" name="txtTechNoW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDistortionW" name="txtDistortionW" style=" height:25px;" readonly=readonly />
                </td>
            </tr>
            <tr>
                <td colspan="6" style=" height:5px;">
                </td>
            </tr>
            <tr style="height:25px;">
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtLWUnitW" name="txtLWUnitW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtKaifengMgW" name="txtKaifengMgW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtBatchMgW" name="txtBatchMgW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtSpecialProtectionW" name="txtSpecialProtectionW" style=" height:25px;" readonly=readonly />
                </td>
            </tr>
            <tr>
                <td colspan="6" style=" height:5px;">
                </td>
            </tr>
            <tr style="height:25px;">
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtMHeightW" name="txtMHeightW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px; text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDiameterW" name="txtDiameterW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtBatchNoW" name="txtBatchNoW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtCoatingTW" name="txtCoatingTW" style=" height:25px;" readonly=readonly />
                </td>
            </tr>
            <tr>
                <td colspan="6" style=" height:5px;">
                </td>
            </tr>
            <tr style="height:25px;">
                <td style=" width:100px;text-align:right;">
                </td>
                <td colspan=3>
                    <input type="text" class="form-control" id="txtPNameW" name="txtPNameW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                </td>
            </tr>
        </table>
    </div>
    <div id="DivSheet13" style="height: auto; display: none;  padding-top: 5px; color: #4b545d ">
        <table cellspacing="0" cellpadding="0" border='0' style="width: 98%; ">
            <tr style="height:30px;">
                <td style=" width:100px; text-align:right;">
                    分类:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtStockLacation8W" name="txtStockLacation8W" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                </td>
            </tr>
        </table>
    </div>
    <!--抽样方案-->
    <div id="DivSheet14" style="height: auto; display: none;  padding-top: 5px; color: #4b545d ">
        <table cellspacing="0" cellpadding="0" border='0' style="width: 98%; " class="XCCYFA">
            <tr style="height:30px;">
                <td style=" width:100px; text-align:right;">
                    抽样方案
                </td>
                <td>
                    <input type="text" id="txtSamplPlanW" name="txtSamplPlanW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" id="txtSpecialProtection9W" name="txtSpecialProtection9W" style=" height:25px;display:none;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" id="txtMaterXH9W" name="txtMaterXH9W" style=" height: 25px; display: none;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                </td>
            </tr>
        </table>

    </div>
    <div id="DivSheet15" style="height: auto; display: none;  padding-top: 5px; color: #4b545d ">
        <table cellspacing="0" cellpadding="0" border='0' style="width: 98%; ">
            <tr style="height:30px;">
                <td style=" width:100px; text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtMaterXH10W" name="txtMaterXH10W" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtTechNo10W" name="txtTechNo10W" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                </td>
            </tr>
        </table>
    </div>
    <!--UDI属性-->
    <div id="DivSheet16" style="height: auto; display: none;  padding-top: 5px; color: #4b545d ">
        <table cellspacing="0" cellpadding="0" border='0' style="width: 98%; " class="XCUDISX">
            <tr style="height:25px;">
                <td style=" width:100px; text-align:right;">
                    产品名称
                </td>
                <td>
                    <input type="text" id="txtPDNameW" name="txtPDNameW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    商品名称
                </td>
                <td>
                    <input type="text" id="txtSPNameW" name="txtSPNameW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    包类产品
                </td>
                <td>
                    <input type="text" id="txtBLCPW" name="txtBLCPW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    产品代码
                </td>
                <td>
                    <input type="text" id="txtYMLDMW" name="txtYMLDMW" style=" height:25px;" readonly=readonly />
                </td>
            </tr>
            <tr>
                <td colspan="6" style=" height:5px;">
                </td>
            </tr>
            <tr style="height:25px;">
                <td style=" width:100px; text-align:right;">
                    器械类别
                </td>
                <td>
                    <input type="text" id="txtQXLBW" name="txtQXLBW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px; text-align:right;">
                    分类编码
                </td>
                <td>
                    <input type="text" id="txtFLBMW" name="txtFLBMW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    注册备案人CN
                </td>
                <td>
                    <input type="text" id="txtZCRCNW" name="txtZCRCNW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    注册备案人EN
                </td>
                <td>
                    <input type="text" id="txtZCRENW" name="txtZCRENW" style=" height:25px;" readonly=readonly />
                </td>
            </tr>
            <tr>
                <td colspan="6" style=" height:5px;">
                </td>
            </tr>
            <tr style="height:25px;">
                <td style=" width:100px;text-align:right;">
                    注册备案编号
                </td>
                <td>
                    <input type="text" id="txtZCBHW" name="txtZCBHW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    产品类别
                </td>
                <td>
                    <input type="text" id="txtCPLBW" name="txtCPLBW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    安全相关信息
                </td>
                <td>
                    <input type="text" id="txtAQXGW" name="txtAQXGW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    一次性使用
                </td>
                <td>
                    <input type="text" id="txtYCXW" name="txtYCXW" style=" height:25px;" readonly=readonly />
                </td>
            </tr>
            <tr>
                <td colspan="6" style=" height:5px;">
                </td>
            </tr>
            <tr style="height:25px;">
                <td style=" width:100px;text-align:right;">
                    重复使用次数
                </td>
                <td>
                    <input type="text" id="txtCFSYCSW" name="txtCFSYCSW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    无菌包装
                </td>
                <td>
                    <input type="text" id="txtWJBZW" name="txtWJBZW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    使用前灭菌
                </td>
                <td>
                    <input type="text" id="txtSYQMJW" name="txtSYQMJW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    灭菌方式
                </td>
                <td>
                    <input type="text" id="txtMJFSW" name="txtMJFSW" style=" height:25px;" readonly=readonly />
                </td>
            </tr>

            <tr>
                <td colspan="6" style=" height:5px;">
                </td>
            </tr>
            <tr style="height:25px;">
                <td style=" width:100px;text-align:right;">
                    其他信息链接
                </td>
                <td>
                    <input type="text" id="txtQTXXW" name="txtQTXXW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px; text-align:right;">
                    医保编码
                </td>
                <td>
                    <input type="text" id="txtYBBMW" name="txtYBBMW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    退市日期
                </td>
                <td>
                    <input type="text" id="txtTSRQW" name="txtTSRQW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    PI 批号
                </td>
                <td>
                    <input type="text" id="txtPIPHW" name="txtPIPHW" style=" height:25px;" readonly=readonly />
                </td>
            </tr>
            <tr>
                <td colspan="6" style=" height:5px;">
                </td>
            </tr>
            <tr style="height:25px;">
                <td style=" width:100px;text-align:right;">
                    PI序列号
                </td>
                <td>
                    <input type="text" id="txtPIXLHW" name="txtPIXLHW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px; text-align:right;">
                    PI 生产日期
                </td>
                <td>
                    <input type="text" id="txtPISCRQW" name="txtPISCRQW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    PI 失效日期
                </td>
                <td>
                    <input type="text" id="txtPISXRQW" name="txtPISXRQW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                </td>
            </tr>
        </table>
    </div>
    <!--通用属性-->
    <div id="DivSheet17" style="height: auto; display: none;  padding-top: 5px; color: #4b545d ">
        <table cellspacing="0" cellpadding="0" border='0' style="width: 98%; " class="XCTYSZ">
            <tr style="height:30px;">
                <td style=" width:100px; text-align:right;">
                    销售区域
                </td>
                <td>
                    <input type="text" id="txtZoneW" name="txtZoneW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    产品线
                </td>
                <td>
                    <input type="text" id="txtCPXW" name="txtCPXW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    净重
                </td>
                <td>
                    <input type="text" id="txtJZW" name="txtJZW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    毛重
                </td>
                <td>
                    <input type="text" id="txtMZW" name="txtMZW" style=" height:25px;" readonly=readonly />
                </td>
            </tr>
            <tr style="height:30px;">
                <td style=" width:100px; text-align:right;">
                    功率
                </td>
                <td>
                    <input type="text" id="txtGLW" name="txtGLW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    频率与电压
                </td>
                <td>
                    <input type="text" id="txtPLDYW" name="txtPLDYW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    体积
                </td>
                <td>
                    <input type="text" id="txtTZW" name="txtTZW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                </td>
            </tr>

            <tr style="height:30px;">
                <td style=" width:100px; text-align:right;">
                    技术要求编号
                </td>
                <td>
                    <input type="text" id="txtJSNoW" name="txtJSNoW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    部件名称
                </td>
                <td>
                    <input type="text" id="txtPartNameW" name="txtPartNameW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    部件型号
                </td>
                <td>
                    <input type="text" id="txtPartSpecW" name="txtPartSpecW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    使用期限(月)
                </td>
                <td>
                    <input type="text" id="txtUseLDateW" name="txtUseLDateW" style=" height:25px;" readonly=readonly />
                </td>
            </tr>
            <tr style="height:30px;">
                <td style=" width:100px; text-align:right;">
                    CMIIT ID
                </td>
                <td>
                    <input type="text" id="txtCMIITIDW" name="txtCMIITIDW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    尺寸
                </td>
                <td>
                    <input type="text" id="txtCSizeW" name="txtCSizeW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    注意事项
                </td>
                <td>
                    <input type="text" id="txtNoteW" name="txtNoteW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    禁忌症
                </td>
                <td>
                    <input type="text" id="txtSYJJW" name="txtSYJJW" style=" height:25px;" readonly=readonly />
                </td>
            </tr>
            <tr style="height:30px;">
                <td style=" width:100px; text-align:right;">
                    储存方法
                </td>
                <td>
                    <input type="text" id="txtStoreW" name="txtStoreW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                    运输条件
                </td>
                <td>
                    <input type="text" id="txtTranspW" name="txtTranspW" style=" height:25px;" readonly=readonly />
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                </td>
                <td style=" width:100px;text-align:right;">
                </td>
                <td>
                </td>
            </tr>

        </table>
    </div>


    <div class="XC-modal XC-modal-xl" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1">标题</span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <table cellspacing="0" cellpadding="0" border='0' class="showOneHeaderTable">
                    <tr>
                        <td style=" width:80px; text-align:right;">
                            物料编码<span class="XC-Font-Red">*</span>
                        </td>
                        <td>
                            <input type="text" id="txtMaterNo" name="txtMaterNo" style=" width:98.5%;" />
                        </td>
                        <td style=" width:80px; text-align:right;">
                            物料描述<span class="XC-Font-Red">*</span>
                        </td>
                        <td colspan=3>
                            <input type="text" id="txtMaterName" name="txtMaterName" style=" width:98.5%;" />
                        </td>
                    </tr>
                    <tr>
                        <td style=" width:80px; text-align:right;">
                            型号
                        </td>
                        <td>
                            <input type="text" id="txtMaterSpec" name="txtMaterSpec" style=" width:98.5%;" />
                        </td>
                        <td style=" width:80px; text-align:right;">
                            单位
                        </td>
                        <td>
                            <select id="txtUnit" style=" width:98.5%;">
                                <option></option>
                                <option>个</option>
                                <option>台</option>
                                <option>件</option>
                                <option>箱</option>
                                <option>斤</option>
                                <option>公斤</option>
                                <option>千克</option>
                                <option>两</option>
                                <option>克</option>
                                <option>毫克</option>
                                <option>吨</option>
                                <option>升</option>
                                <option>毫升</option>
                                <option>千米</option>
                                <option>米</option>
                                <option>分米</option>
                                <option>厘米</option>
                                <option>毫米</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td style=" width:80px; text-align:right;">
                            物料类别
                        </td>
                        <td>
                            <select id="txtKind" style=" width:98.5%;">
                                <option></option>
                                <option>原材料</option>
                                <option>半成品</option>
                                <option>成品</option>
                                <option>其他</option>
                            </select>
                        </td>
                        <td style=" width:80px; text-align:right;">
                            大类
                        </td>
                        <td>
                            <select id="txtBigType" disabled=disabled style=" width:98.5%;">
                                <option></option>
                                <option>组件</option>
                                <option>UDI属性</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td style=" width:80px; text-align:right;">
                            备注
                        </td>
                        <td colspan="4">
                            <textarea id="txtRemark" name="txtRemark" style=" width:99.3%;height:70px;"></textarea>
                        </td>

                    </tr>
                </table>
                <div class="XC-Tab2" style="margin-top: 10px;">
                    <ul class="XC-tabs2">
                        <li id="Sheet36" onclick="Sheet_onclick(36)">UDI属性</li>
                        <li id="Sheet37" onclick="Sheet_onclick(37)">通用属性</li>
                        <li id="Sheet34" onclick="Sheet_onclick(34)">抽样方案</li>
                        <li id="Sheet35" onclick="Sheet_onclick(35999)"></li>
                        <li id="Sheet31" onclick="Sheet_onclick(31888)"></li>
                        <li id="Sheet32" onclick="Sheet_onclick(32888)"></li>
                        <li id="Sheet33" onclick="Sheet_onclick(33888)"></li>
                    </ul>
                </div>
                <div id="lightBody" style="padding-top:10px;border:1px solid #E5E5E5;height:280px">
                    <div id="HeadDiv" style=" display:none;">
                        <table cellspacing="0" cellpadding="0" border='0'>
                            <tr style="height:30px; ">
                                <td style=" width:100px; text-align:right;">
                                    分类:
                                </td>
                                <td>
                                    <select style=" width:200px; " id="txtFType" onblur="GetTypeXLList()">
                                        <option></option>
                                    </select>
                                    <input type="text" class="form-control" id="txtFTypeR" name="txtFTypeR" style=" display:none;" />
                                </td>
                                <td style=" width:130px;text-align:right;">
                                    类型/类别(Type):
                                </td>
                                <td>
                                    <select style=" width:200px; " id="txtMaterType">
                                        <option></option>
                                    </select>
                                    <input type="text" class="form-control" id="txtMaterTypeR" name="txtMaterTypeR" style=" display:none;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div id="DivSheet31" style=" height:120px; display:none;">
                        <table cellspacing="0" cellpadding="0" border='0' style=" width:98%;">
                            <tr style="height:30px;">
                                <td style=" width:100px;text-align:right;">
                                    材料(Material):
                                </td>
                                <td>
                                    <select style=" width:200px;" id="txtABCKind">
                                        <option></option>
                                        <option value="玻璃类">玻璃类</option>
                                        <option value="红外材料">红外材料</option>
                                        <option value="蓝宝石">蓝宝石</option>
                                        <option value="金属">金属</option>
                                        <option value="非红外镜头">非红外镜头</option>
                                        <option value="复合材料">复合材料</option>
                                    </select>
                                </td>
                                <td style=" width:100px; text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtMaterXH" name="txtMaterXH" style=" height:25px;" />
                                </td>
                                <td style=" width:100px; text-align:right;">
                                </td>
                                <td>
                                    <select style=" width:200px; " id="txtCoating">
                                        <option></option>
                                        <option value="镀膜">镀膜</option>
                                        <option value="不镀膜">不镀膜</option>
                                    </select>
                                </td>
                            </tr>
                            <tr style="height:30px;">
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <select style=" width:200px; " id="txtTUnit">
                                        <option></option>
                                        <option>平凸</option>
                                        <option>平凹</option>
                                    </select>
                                </td>
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtMLength" name="txtMLength" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtMThickness" name="txtMThickness" style=" height:25px;" />
                                </td>
                            </tr>
                            <tr style="height:30px;">
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtMWidth" name="txtMWidth" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;" colspan=4>
                                    :
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div id="DivSheet32" style=" height:200px; display:none;">
                        <table cellspacing="0" cellpadding="0" border='0' style=" width:98%;">
                            <tr style="height:25px; ">
                                <td style=" width:100px; text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtLOSHFlag" name="txtLOSHFlag" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtHUnit" name="txtHUnit" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtMWidthT" name="txtMWidthT" style=" height:25px;" />
                                </td>
                            </tr>
                            <tr>
                                <td colspan="6" style=" height:5px;">
                                </td>
                            </tr>
                            <tr style="height:25px;">
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtStockPlace" name="txtStockPlace" style=" height:25px;" />
                                </td>
                                <td style=" width:100px; text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtMThicknessT" name="txtMThicknessT" style=" height:25px;" />
                                </td>
                                <td style=" width:100px; text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtMLengthT" name="txtMLengthT" style=" height:25px;" />
                                </td>
                            </tr>
                            <tr style="height:25px;">
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtStockLacation" name="txtStockLacation" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtInspectMg" name="txtInspectMg" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtEfftMg" name="txtEfftMg" style=" height:25px;" />
                                </td>
                            </tr>
                            <tr style="height:25px;">
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtMount" name="txtMount" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtTechNo" name="txtTechNo" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtDistortion" name="txtDistortion" style=" height:25px;" />
                                </td>
                            </tr>

                            <tr style="height:25px;">
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtLWUnit" name="txtLWUnit" style=" height:25px;" />
                                </td>
                                <td style=" width:100px; text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtKaifengMg" name="txtKaifengMg" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtBatchMg" name="txtBatchMg" style=" height:25px;" />
                                </td>
                            </tr>
                            <tr style="height:25px;">
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtSpecialProtection" name="txtSpecialProtection" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtMHeight" name="txtMHeight" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtDiameter" name="txtDiameter" style=" height:25px;" />
                                </td>
                            </tr>
                            <tr style="height:25px;">
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtBatchNo" name="txtBatchNo" style=" height:25px;" />
                                </td>
                                <td style=" width:100px; text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtCoatingT" name="txtCoatingT" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtPName" name="txtPName" style=" height:25px;" />
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div id="DivSheet33" style=" height:120px;display:none;">
                        <table cellspacing="0" cellpadding="0" border='0' style=" width:98%;">
                            <tr style="height:30px;">
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <select style=" width:200px;" id="txtStockLacation8">
                                        <option></option>
                                        <option value="结构件工装">结构件工装</option>
                                        <option value="光学件工装">光学件工装</option>
                                    </select>
                                </td>
                                <td style=" width:100px; text-align:right;">
                                </td>
                                <td>
                                </td>
                                <td style=" width:100px; text-align:right;">
                                </td>
                                <td>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div id="DivSheet34" style=" height:120px; display:none;">
                        <table cellspacing="0" cellpadding="0" border='0' style=" width:100%;" class="showCYFA">
                            <tr style="height:30px;">
                                <td style=" width:100px;text-align:right;">
                                    抽样方案
                                </td>
                                <td>
                                    <select style=" height:25px " id="txtSamplPlan">
                                        <option></option>
                                    </select>
                                    <input type="text" style=" height:25px;display:none;" id="txtSamplPlanR" name="txtSamplPlanR" />
                                </td>
                                <td style=" width:100px; text-align:right;">
                                </td>
                                <td>
                                    <input type="text" style=" height:25px;display: none;" id="txtMaterXH9" name="txtMaterXH9" />
                                </td>
                                <td style=" width:100px; text-align:right;">
                                </td>
                                <td>
                                    <input type="text" style=" height:25px;display: none;" id="txtSpecialProtection9" name="txtSpecialProtection9" />
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div id="DivSheet35" style=" height:120px; display:none;">
                        <table cellspacing="0" cellpadding="0" border='0' style=" width:98%;">
                            <tr style="height:30px; ">
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtMaterXH10" name="txtMaterXH10" style=" height:25px;" />
                                </td>
                                <td style=" width:100px; text-align:right;">
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="txtTechNo10" name="txtTechNo10" style=" height:25px;" />
                                </td>
                                <td style=" width:100px; text-align:right;">
                                </td>
                                <td>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div id="DivSheet36" style=" height:200px;">
                        <table cellspacing="0" cellpadding="0" border='0' style=" width:100%;" class="showUDISX">
                            <tr style="height:30px; ">
                                <td style=" width:100px; text-align:right;">
                                    产品名称
                                </td>
                                <td>
                                    <input type="text" style=" height:25px " id="txtPDName" name="txtPDName" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    商品名称
                                </td>
                                <td>
                                    <input type="text" style=" height:25px " id="txtSPName" name="txtSPName" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    包类产品
                                </td>
                                <td>
                                    <select style=" height:25px " id="txtBLCP">
                                        <option></option>
                                        <option value="是">是</option>
                                        <option value="否">否</option>
                                    </select>
                                </td>
                            </tr>

                            <tr style="height:30px;">
                                <td style=" width:100px;text-align:right;">
                                    产品代码
                                </td>
                                <td>
                                    <input type="text" style=" height:25px " id="txtYMLDM" name="txtYMLDM" />
                                </td>
                                <td style=" width:100px; text-align:right;">
                                    产品类别
                                </td>
                                <td>
                                    <select style=" height:25px " id="txtCPLB">
                                        <option value="耗材">耗材</option>
                                        <option value="设备">设备</option>
                                    </select>
                                </td>
                                <td style=" width:100px; text-align:right;">
                                    退市日期
                                </td>
                                <td>
                                    <input type="date" style=" height:25px " id="txtTSRQ" name="txtTSRQ" />
                                </td>
                            </tr>
                            <tr style="height:30px;">
                                <td style=" width:100px;text-align:right;">
                                    器械类别
                                </td>
                                <td>
                                    <select style=" height:25px " id="txtQXLB">
                                        <option></option>
                                        <option value="器械">器械</option>
                                        <option value="体外诊断试剂">体外诊断试剂</option>
                                    </select>
                                </td>
                                <td style=" width:100px; text-align:right;">
                                    分类编码
                                </td>
                                <td>
                                    <input type="text" style=" height:25px " id="txtFLBM" name="txtFLBM" />
                                </td>
                                <td style=" width:100px; text-align:right;">
                                    注册备案人CN
                                </td>
                                <td>
                                    <input type="text" style=" height:25px " id="txtZCRCN" name="txtZCRCN" />
                                </td>
                            </tr>
                            <tr style="height:30px;">
                                <td style=" width:100px;text-align:right;">
                                    注册备案人EN
                                </td>
                                <td>
                                    <input type="text" style=" height:25px " id="txtZCREN" name="txtZCREN" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    注册备案编号
                                </td>
                                <td>
                                    <input type="text" style=" height:25px " id="txtZCBH" name="txtZCBH" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    安全相关信息
                                </td>
                                <td>
                                    <select style=" height:25px " id="txtAQXG">
                                        <option></option>
                                        <option value="0 安全">0 安全</option>
                                        <option value="1 条件安全">1 条件安全</option>
                                        <option value="2 不安全">2 不安全</option>
                                        <option value="3 说明书或标签不包含MR安全性信息">3 说明书或标签不包含MR安全性信息</option>
                                    </select>
                                </td>
                            </tr>
                            <tr style="height:30px;">
                                <td style=" width:100px;text-align:right;">
                                    一次性使用
                                </td>
                                <td>
                                    <select style=" height:25px " id="txtYCX">
                                        <option></option>
                                        <option value="是">是</option>
                                        <option value="否">否</option>
                                    </select>
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    重复使用次数
                                </td>
                                <td>
                                    <input type="text" style=" height:25px " id="txtCFSYCS" name="txtCFSYCS" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    是否无菌包装
                                </td>
                                <td>
                                    <select style=" height:25px " id="txtWJBZ">
                                        <option></option>
                                        <option value="是">是</option>
                                        <option value="否">否</option>
                                    </select>
                                </td>
                            </tr>

                            <tr style="height:30px;">
                                <td style=" width:100px;text-align:right;">
                                    使用前灭菌
                                </td>
                                <td>
                                    <select style=" height:25px " id="txtSYQMJ">
                                        <option></option>
                                        <option value="是">是</option>
                                        <option value="否">否</option>
                                    </select>
                                </td>
                                <td style=" width:100px; text-align:right;">
                                    灭菌方式
                                </td>
                                <td>
                                    <input type="text" style=" height:25px " id="txtMJFS" name="txtMJFS" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    医保编码
                                </td>
                                <td>
                                    <input type="text" style=" height:25px " id="txtYBBM" name="txtYBBM" />
                                </td>
                            </tr>
                            <tr style="height:30px;">

                                <td style=" width:100px;text-align:right;">
                                    PI-批号
                                </td>
                                <td>
                                    <select style=" height:25px " id="txtPIPH">
                                        <option value="是">是</option>
                                        <option value="否">否</option>
                                    </select>
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    PI-序列号
                                </td>
                                <td>
                                    <select style=" height:25px " id="txtPIXLH">
                                        <option value="是">是</option>
                                        <option value="否">否</option>
                                    </select>
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    PI-生产日期
                                </td>
                                <td>
                                    <select style=" height:25px " id="txtPISCRQ">
                                        <option value="是">是</option>
                                        <option value="否">否</option>
                                    </select>
                                </td>
                            </tr>
                            <tr style="height:30px;">
                                <td style=" width:100px; text-align:right;">
                                    PI-失效日期
                                </td>
                                <td>
                                    <select style=" height:25px " id="txtPISXRQ">
                                        <option value="是">是</option>
                                        <option value="否">否</option>
                                    </select>
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    其他信息链接
                                </td>
                                <td colspan="3">
                                    <input type="text" style=" height:25px;width:94.6%" id="txtQTXX" name="txtQTXX" />
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div id="DivSheet37" style=" height:120px; display:none;">
                        <table cellspacing="0" cellpadding="0" border='0' style=" width:100%;" class="showTYSX">
                            <tr style="height:30px;">
                                <td style=" width:100px; text-align:right;">
                                    销售区域
                                </td>
                                <td>
                                    <input type="text" id="txtZone" name="txtZone" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    产品线
                                </td>
                                <td>
                                    <input type="text" id="txtCPX" name="txtCPX" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    净重
                                </td>
                                <td>
                                    <input type="text" id="txtJZ" name="txtJZ" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    毛重
                                </td>
                                <td>
                                    <input type="text" id="txtMZ" name="txtMZ" style=" height:25px;" />
                                </td>
                            </tr>
                            <tr style="height:30px;">
                                <td style=" width:100px; text-align:right;">
                                    功率
                                </td>
                                <td>
                                    <input type="text" id="txtGL" name="txtGL" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    频率与电压
                                </td>
                                <td>
                                    <input type="text" id="txtPLDY" name="txtPLDY" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    体积
                                </td>
                                <td>
                                    <input type="text" id="txtTZ" name="txtTZ" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                </td>
                            </tr>
                            <tr style="height:30px;">
                                <td style=" width:100px; text-align:right;">
                                    技术要求编号
                                </td>
                                <td>
                                    <input type="text" id="txtJSNo" name="txtJSNo" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    部件名称
                                </td>
                                <td>
                                    <input type="text" id="txtPartName" name="txtPartName" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    部件型号
                                </td>
                                <td>
                                    <input type="text" id="txtPartSpec" name="txtPartSpec" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    使用期限(月)
                                </td>
                                <td>
                                    <input type="text" id="txtUseLDate" name="txtUseLDate" style=" height:25px; " />
                                </td>
                            </tr>
                            <tr style="height:30px;">
                                <td style=" width:100px; text-align:right;">
                                    CMIIT ID
                                </td>
                                <td>
                                    <input type="text" id="txtCMIITID" name="txtCMIITID" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    尺寸
                                </td>
                                <td>
                                    <input type="text" id="txtCSize" name="txtCSize" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    注意事项
                                </td>
                                <td>
                                    <input type="text" id="txtNote" name="txtNote" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    禁忌症
                                </td>
                                <td>
                                    <input type="text" id="txtSYJJ" name="txtSYJJ" style=" height:25px;" />
                                </td>
                            </tr>
                            <tr style="height:30px;">
                                <td style=" width:100px; text-align:right;">
                                    储存方法
                                </td>
                                <td>
                                    <input type="text" id="txtStore" name="txtStore" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                    运输条件
                                </td>
                                <td>
                                    <input type="text" id="txtTransp" name="txtTransp" style=" height:25px;" />
                                </td>
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                </td>
                                <td style=" width:100px;text-align:right;">
                                </td>
                                <td>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="MaterSaveBtn">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="MaterSaveClose" onclick='closeDialog()'>关闭</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>

    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="MaterNo" name="MaterNo" />
                <input type="text" class="form-control" id="MaterName" name="MaterName" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="MaterBut_Del">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>


    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>
</html>