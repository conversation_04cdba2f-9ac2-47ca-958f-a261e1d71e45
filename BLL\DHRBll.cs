﻿using DAL;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using static System.Net.Mime.MediaTypeNames;

namespace BLL
{
    public class DHRBll
    {
        public static string JudgeObjectExist(string Kind, string KindList, string QT, string sComp, string sFlag)
        {
            return DHRDal.JudgeObjectExist(Kind, KindList, QT, sComp, sFlag);
        }
        public static DataTable GetExportConditions(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string Remark, string Flag, string sMan, string sComp, string IP)
        {
            return DHRDal.GetExportConditions(No, Name, Item, MNo, MName, A, B, C, D, E, F, Remark, Flag, sMan, sComp, IP);
        }

        public static Dictionary<string, object> GetExportData(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string Remark, string Flag, string sMan, string sComp, string IP)
        {
            return DHRDal.GetExportData(No, Name, Item, MNo, MName, A, B, C, D, E, F, Remark, Flag, sMan, sComp, IP);
        }

        public static DataTable CheckDHR(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string Remark, string Flag,int limit,int page, string sMan, string sComp, string IP)
        {
            return DHRDal.CheckDHR(No, Name, Item, MNo, MName, A, B, C, D, E, F, Remark, Flag, limit, page, sMan, sComp, IP);
        }

        public static Dictionary<string, DataTable> CreateDHRPDF(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string Remark, string Flag, string sMan, string sComp, string IP)
        {
            return DHRDal.CreateDHRPDF(No, Name, Item, MNo, MName, A, B, C, D, E, F, Remark, Flag, sMan, sComp, IP);
        }
    }
}
