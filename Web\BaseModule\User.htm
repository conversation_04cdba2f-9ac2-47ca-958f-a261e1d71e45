﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html" ;=; charset="utf-8" />
    <title>用户信息</title>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="../js/jQuery-2.2.0.min.js"></script>
    <script type="text/javascript" src="../js/bstable/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="../js/bstable/js/bootstrap-table.js"></script>
    <script type="text/javascript" src="../js/bstable/js/bootstrap-table-zh-CN.min.js"></script>
    <script type="text/javascript" src="../js/date/js/laydate.js"></script>
    <script type="text/javascript" src="../js/layer/layer.js"></script>
    <script src="../js/ajaxfileupload.js" type="text/javascript"></script>
    <script type="text/javascript" src="../js/BaseModule.js"></script>

    <link href="../css/XC.css" rel="stylesheet" />

    <script type="text/javascript">


        $(function () {

            var sCorpID = "we";  // GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&sFlag=10&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=10&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        $('#txtInMan').val(parsedJson.Man);
                        $('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=25-1' });
                    }
                }
            })
        });

    </script>

    <script type="text/javascript">
        !function () {
            laydate.skin('danlan'); //切换皮肤，请查看skins下面皮肤库
            laydate({ elem: '#demo' });
            laydate({ elem: '#demo1' }); //绑定元素
        }();
    </script>

    <script type="text/javascript">
        //获取当前所在的模块、菜单
        const currentTabId = sessionStorage.getItem("currentTabId");
        const ModuleName = currentTabId != null ? JSON.parse(currentTabId).ModuleName : null;

        $(function () {
            $('#Usertable').bootstrapTable({
                method: "get",
                striped: true,
                singleSelect: false,
                url: "../Service/BaseModuleAjax.ashx?OP=GetUserInfo&sFlag=30",
                dataType: "json",
                pagination: true, //分页
                pageSize: 10,
                pageNumber: 1,
                search: false, //显示搜索框
                height: window.screen.height - 300,
                contentType: "application/x-www-form-urlencoded",
                queryParams: null,
                columns: [
                    {
                        title: "用户编号",
                        field: 'LoginName',
                        //width: 100,
                        align: 'center',
                        valign: 'middle'
                    },
                    {
                        title: '用户名称',
                        field: 'FullName',
                        //width: 100,
                        align: 'center',
                        valign: 'middle'
                    },
                    {
                        title: '职位',
                        field: 'Kind',
                        align: 'center',
                        valign: 'middle'
                    },
                    {
                        title: '部门',
                        field: 'DeptName',
                        align: 'center'
                    },
                    {
                        title: '性别',
                        field: 'UserSex',
                        align: 'center'
                    },
                    {
                        title: 'QQ',
                        field: 'QYNo',
                        align: 'center'
                    },
                    {
                        title: '电话',
                        field: 'Phone',
                        align: 'center'
                    },
                    {
                        title: '公司',
                        field: 'CompanyNo',
                        align: 'center'
                    },
                    {
                        title: '录入人',
                        field: 'OperatorID',
                        //width: 50,
                        align: 'center'
                    },
                    {
                        title: '录入时间',
                        field: 'InDate',
                        align: 'center'
                    },
                    {
                        title: '操作',
                        field: 'opear',
                        align: 'center',
                        width: 170,
                        formatter: function (value, row) {
                            var e = '<button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="openDialog(\'' + row.LoginName + '\')">修改</button> ' +
                                ' <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" onclick="DelUser(\'' + row.LoginName + '\')">删除</button>' +
                                ' <button type="button" class="XC-Btn-md ' + (row.Status == "OK" ? "XC-Btn-Red" : "XC-Btn-Green") + ' XC-Size-xs" onclick="UserBanStart(\'' + row.LoginName + '\',\'' + row.Status + '\')">' + (row.Status == 'OK' ? "禁用" : "启用") + '</button>'
                            return e;
                        }
                    }
                ]
            });
        })

        function UserBanStart(user, status) {

            $('#ShowBanStart').css("display", "block");
            $('#ShowBanStart-fade').css("display", "block");

            $("#XC-Icon1").removeClass()
            $("#hint-value1").removeClass()

            $("#XC-Icon1").addClass("XC-Icon " + (status == "OK" ? 'XC-Btn-Red' : 'XC-Btn-Green'))
            $("#hint-value1").addClass((status == "OK" ? 'XC-Font-Red' : 'XC-Font-Green'))

            //设置删除的标题
            $("#hint-title1").html((status == "OK" ? '确定要禁用该账号吗？账号：' : '确定要启用该账号吗？账号：'))

            //设置删除的对象
            $("#hint-value1").html(user)

            $("#txtLogin").val(user)

            $("#txtFlag").val((status == "OK" ? '24-6' : '24-7'))

        }

        function BanAndStartOP() {
            var sflag = $("#txtFlag").val()
            var suser = $("#txtLogin").val()

            var Data = '';
            var Params = { Kind: "", No: suser, Name: "", Dept: "", Pwd: "", ZW: "", Sex: "", Telph: "", Phone: "", Email: "", QQ: "", File: "", Path: "", ModuleName: ModuleName, Comp: "", RoleStr: "", InMan: "", Flag: sflag };
            var Data = JSON.stringify(Params);

            $.ajax({
                type: "POST",
                url: "../Service/BaseModuleAjax.ashx?OP=AddEditUser&CFlag=" + sflag,
                data: { Data: Data },
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);

                    $('#ShowBanStart').css("display", "none");
                    $('#ShowBanStart-fade').css("display", "none");

                    layer.msg(sflag == "24-6" ? '禁用成功' : '启用成功')

                    $('#Usertable').bootstrapTable('refresh', { url: "../Service/BaseModuleAjax.ashx?OP=GetUserInfo&sFlag=30" });
                },
                error: function (data) {
                    $("#UserSaveBtn").removeAttr("disabled");
                    ErrorMessage("系统出错，请重试2！", 2000)
                }
            });
        }
    </script>


    <script type="text/javascript">
        function openlayer(id) {   // 这个方法是在本界面打开一个新的界面，放入一个层，不过有点慢
            layer.open({
                type: 2,
                title: '添加信息',
                shadeClose: true,
                shade: 0.5,
                skin: 'layui-layer-rim',
                //            maxmin: true,
                closeBtn: 1,
                area: ['98%', '92%'],
                shadeClose: true,
                closeBtn: 1,
                content: 'AddUser.htm'
                //iframe的url
            });
        }
    </script>


    <script type="text/javascript">
        function handleEvent(id) {

            alert(id);
        }
    </script>

    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

        })
    </script>


    <script type="text/javascript">

        

        function openDialog(n) {
            var sFlag = "";

            if (n == "1") {   // 点击新增用户
                sFlag = "25-1";  // 加载权限标识
                $("#txtAEFlag").val("24-3"); // 现在用户标识

                $("#txtK").html("新增用户信息")  // 这几个字不能随便修改，JS那边用到
                $("#UserSaveBtn").removeAttr("disabled");
                $("#InTable").empty();

                $('#txtUserNo').val("");
                $('#txtUserName').val("");
                $('#txtDept').val("");
                $('#txtPwd').val("");
                $('#txtCPwd').val("");
                $('#txtZW').val("");
                $('#txtSex').val("");
                $('#txtTelph').val("");
                $('#txtPhone').val("");
                $('#txtEmail').val("");
                $('#txtQQ').val("");
                $('#txtCompany').val("");

                $("#txtPath").val("");
                $("#txtFile").val("");
                $("#img1").attr("src", '');
                $("#img1").hide();
                $("#txtFName1").val("");
                $("#txtP1").val("");

                if (($('#txtInMan').val() == "wd") || ($('#txtInMan').val() == "wudong") || ($('#txtInMan').val() == "wd10")) {
                    $('#trComp').show();
                }
                else {
                    $('#trComp').hide();
                }

                $("#div_warning").html("");
                $("#div_warning").hide();
                $('#CH_SignFlag').prop("checked", "checked");

                $("#txtUserNo").removeAttr("disabled");

                GetDeptInfoList();
            }
            else {  // 修改用户信息
                sFlag = "25-2";// 加载权限标识
                $("#txtAEFlag").val("24-4"); // 修改用户标识

                $("#txtK").html("修改用户信息")
                $("#UserSaveBtn").removeAttr("disabled");
                $("#txtUserNo").attr({ "disabled": "disabled" });
                $("#InTable").empty();

                $("#div_warning").html("");
                $("#div_warning").hide();
                $('#CH_SignFlag').prop("checked", "checked");

                GetDeptInfoList();
            }

            // 加载用户权限
            $.ajax({  //ww-01
                url: "../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=" + sFlag + "&CNO=" + n,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    var iy = 0;
                    var sCon = "";
                    var sName = "";
                    var iID = 0;
                    var sStr = "";

                    for (var i = 0; i < parsedJson.length; i++) {
                        if (parsedJson[i].UserRole == "0") { // 新增或没有勾选的角色
                            sCon = sCon + "<td> <input type='checkbox' value=" + parsedJson[i].RoleID + " name='check' style=' margin:0px;vertical-align:middle'/> " + parsedJson[i].RoleName + " </td>";
                        }
                        else {  // 修改时，如果这个用户有角色，则勾选上
                            sCon = sCon + "<td> <input type='checkbox' value=" + parsedJson[i].RoleID + " name='check' checked ='checked' style='margin:0px;vertical-align:middle'/>" + parsedJson[i].RoleName + "</td>";
                        }

                        iy = (i + 1) % 5;
                        if ((iy == 0) && (i >= 1)) {
                            sStr = sStr + "<tr style='height:25px; width:20%;'>" + sCon + "</tr> ";
                            sCon = "";
                        }
                    }
                    if (iy > 0) {  // 说明最后一行是不满 5 个的，需要单独增加一行（一个tr）
                        sStr = sStr + "<tr style='height:25px; width:20%;'>" + sCon + "</tr> ";
                    }

                    $("#InTable").append(sStr);
                }

            })  //ww-01

            $('#ShowOne').css("display", "block");
            $('#ShowOne-fade').css("display", "block");
        }

        function closeDialog() {
            $('#ShowOne').css("display", "none");
            $('#ShowOne-fade').css("display", "none");
            $("#InTable").empty();
            $("#UserSaveBtn").removeAttr("disabled");
        }


        function GetDeptInfoList() {  // 下拉选择系统类别--部门信息
            var CNo = "";
            var CKind = encodeURI("部门信息");

            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/BaseModuleAjax.ashx?OP=GetDeptInfoList&CFlag=41&CMNO=" + CNo + "&CKind=" + CKind,
                success: function (data) {
                    var parsedJson = eval(data).Msg;

                    var sKong = "<option value=''> </option>";
                    $("#txtDept").empty();
                    $("#txtDept").append(sKong + parsedJson);
                    $('#txtDept').val($('#txtLDeptR').val());
                }
            });
        }





    </script>

    <style type="text/css">
        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        /*       .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: black;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=88);
        }*/

        #Usertable thead > th {
            background-color: red;
        }

        #Usertable tbody > tr:hover { /* 光标放上去的颜色 */
            background-color: #CCC;
        }

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }

            .find_input:focus {
                outline: none;
            }

        #ShowOne .XC-Span-Select-block {
            line-height: 30px;
        }

        #ShowOne input:focus, #ShowOne select:focus {
            outline: none
        }
    </style>


    <script type="text/javascript">
        $(function () {
            var $result = $('#events-result');
            $('#Usertable').bootstrapTable({
                search: true,
                pagination: false,
                pageSize: 5,
                pageList: [5, 10, 15, 20],
                showColumns: true,
                showRefresh: false,
                showToggle: true,
                locale: "zh-CN",
                striped: true

            }).on('all.bs.table', function (e, name, args) {
                console.log('Event:', name, ', data:', args);
            }).on('click-row.bs.table', function (e, row, $element) {
                var sKind = encodeURI(row.LoginName);

                $('#txtUserNo').val(row.LoginName);
                $('#txtUserName').val(row.FullName);
                $('#txtDept').val(row.DeptName); //
                $('#txtLDeptR').val(row.DeptNo);
                $('#txtPwd').val(row.Pwd);
                $('#txtCPwd').val(row.Pwd);
                $('#txtZW').val(row.Kind);
                $('#txtSex').val(row.UserSex);
                $('#txtTelph').val(row.WeiXinNo);
                $('#txtPhone').val(row.Phone);
                $('#txtEmail').val(row.SFCode);
                $('#txtQQ').val(row.QYNo);
                $('#txtCompany').val(row.CompanyNo);

                $("#txtPath").val(row.ImgPath);
                $("#txtFile").val(row.SignImg);
                $("#img1").attr("src", '');
                $("#img1").hide();
                $("#txtFName1").val("");
                $("#txtP1").val("");
                var arr = new Array();
                var sFStr = row.SignImg;  // ;R2201160002 IMAG7217.jpg;R2201160002 IMAG7223.jpg;R2201160002 IMAG7228.jpg;R2201160002 IMAG7232.jpg;R2201160002 IMAG7324.jpg
                arr = sFStr.split(';'); //注split可以用字符或字符串分割
                for (var i = 1; i < arr.length; i++) {
                    $("#txtFName" + i).val(arr[i]);
                }
                var arrP = new Array();
                var sFStr = row.ImgPath;  // ;/UpPicFile/C0008/IQCCheck/20220828/R2201160002 IMAG7217.jpg;/UpPicFile/C0008/IQCCheck/20220828/R2201160002 IMAG7223.jpg;/UpPicFile/C0008/IQCCheck/20220828/R2201160002 IMAG7228.jpg;/UpPicFile/C0008/IQCCheck/20220828/R2201160002 IMAG7232.jpg;/UpPicFile/C0008/IQCCheck/20220828/R2201160002 IMAG7324.jpg
                arrP = sFStr.split(';'); //注split可以用字符或字符串分割
                for (var i = 1; i < arrP.length; i++) {
                    $("#img" + i).attr("src", arrP[i]);
                    $("#img" + i).show();
                    $("#txtP" + i).val(arrP[i]);
                }

                if (($('#txtInMan').val() == "wd") || ($('#txtInMan').val() == "wudong") || ($('#txtInMan').val() == "wd10")) {
                    $('#trComp').show();
                }
                else {
                    $('#trComp').hide();
                }


                $('#txtDelNo').val(row.LoginName);
                $('#txtDelName').val(row.FullName);

                $("#hint-value").html("账号：" + row.LoginName + "，用户名：" + row.FullName)

                $result.text('Event: click-row.bs.table, data: ' + sKind);
            }).on('dbl-click-row.bs.table', function (e, row, $element) {
                $result.text('Event: dbl-click-row.bs.table, data: ' + JSON.stringify(row));
            }).on('search.bs.table', function (e, text) {
                $result.text('Event: search.bs.table, data: ' + text);
            });


        });

    </script>


    <script type="text/javascript">

        function DelUser(n) {
            $('#ShowDel').css("display", "block");
            $('#ShowDel-fade').css("display", "block");
            $("#XC-Icon").removeClass()
            $("#hint-value").removeClass()
            $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
            $("#hint-value").addClass("XC-Font-Red")
        }

        function closeDelDialog() {
            $('#ShowDel').css("display", "none");
            $('#ShowDel-fade').css("display", "none");
            $('#ShowBanStart').css("display", "none");
            $('#ShowBanStart-fade').css("display", "none");
        }

    </script>



</head>
<body>
    <div class="div_find">
        <p style=" height:18px;">
            <label class="find_labela">用户账号</label> <input type="text" id="LoginNo" class="find_input" />
            <label class="find_labela">用户名称</label><input type="text" id="LoginName" class="find_input" />
            <label class="find_labela">部门</label><input type="text" id="DeptName" class="find_input" />
            <label class="find_labela">性别</label>
            <select class="find_input" id="UserSex">
                <option>全部</option>
                <option>男</option>
                <option>女</option>
            </select>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="UserBut_open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="AddUse_Btn" onclick="openDialog(1)">添加</button>
            <!--<button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="UserBut_excel"><i class="down_i"></i>导出</button>-->
            <!--<span class="find_span" ><i class="i_open01"></i>展开</span>-->
            <!--<span class="find_span1"><i class="i_close01"></i>收起</span>-->
        </p>
        <!--<p id="open" style="display:none;height:18px;" ></p>-->
    </div>
    <div style="font-size:12px;">
        <table data-url="json/data_alae_list.json" id="Usertable" class="table_style" style="margin: 0 auto">
        </table>
    </div>

    <div id="light" style="display: none;position: absolute;top: 1%;left: 5%;right: 5%;width: 90%;height: 540px;border: 2px solid #21b6b4; background-color: white;z-index: 1002;overflow: auto;">
        <div id="lightHead" style="height:30px;">
            <label id="txtK" style=" padding:10px;font-size: 14px; color:White; "></label>
        </div>
        <div id="div_warning" role="alert" style="text-align: center; display: none; color: Red">
            <strong id="divsuccess" style="color: Red"></strong>
        </div>

    </div>

    <div class="XC-modal XC-modal-xl" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1">用户信息</span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <div style="display:flex">
                    <div style="width:100%">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">账号<span class="XC-Font-Red">*</span></span>
                                <input type="text" class="XC-Input-block" id="txtUserNo" name="txtUserNo" value="" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">部门</span>
                                <select class="XC-Select-block" id="txtDept">
                                    <option></option>
                                </select>
                                <input type="text" class="XC-Input-block" id="txtLDeptR" name="txtLDeptR" style=" display:none;" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">职位</span>
                                <input type="text" class="XC-Input-block" id="txtZW" name="txtZW" value="" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">手机</span>
                                <input type="text" class="XC-Input-block" id="txtPhone" name="txtPhone" value="" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">邮箱</span>
                                <input type="text" class="XC-Input-block" id="txtEmail" name="txtEmail" value="" />
                            </div>
                            <div class="XC-Form-block-Item" style="display:none">
                                <span class="XC-Span-Input-block">所属公司</span>
                                <input type="text" class="XC-Input-block" id="txtCompany" name="txtCompany" value="" />填写代码,如：C0010
                            </div>
                        </form>
                    </div>
                    <div style="width:100%">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">用户名<span class="XC-Font-Red">*</span></span>
                                <input type="text" class="XC-Input-block" id="txtUserName" name="txtUserName" value="" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">密码<span class="XC-Font-Red">*</span></span>
                                <input type="text" class="XC-Input-block" id="txtPwd" name="txtPwd" value="" style="width:50%" />&nbsp;
                                <input type="text" class="XC-Input-block" id="txtCPwd" name="txtCPwd" value="" style="width:50%" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Select-block">性别</span>
                                <select class="XC-Select-block" id="txtSex">
                                    <option value=""></option>
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">电话</span>
                                <input type="text" class="XC-Input-block" id="txtTelph" name="txtTelph" value="" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">QQ</span>
                                <input type="text" class="XC-Input-block" id="txtQQ" name="txtQQ" value="" />
                            </div>
                        </form>
                    </div>
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">角色<span class="XC-Font-Red">*</span></span>
                    <table id="InTable" style="width:100%;font-size:12px"></table>
                </div>

                <div style="display:flex">
                    <div style="width:100%">
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">签名图片<span class="XC-Font-Red">*</span></span>
                            <input type="checkbox" id="CH_SignFlag" checked="checked" style="margin:0px 10px 0px 10px;vertical-align:mi" />
                            <input type="file" id="UpFile" name="UpFile" value="" class="XC-Input-block" accept="image/*" style="padding-top:3px;padding-left:5px" />
                            <input type="button" id="btnSignImgload" value="上传" class="XC-Btn-md XC-Btn-Green XC-Size-xs" name="btnSignImgload" style="margin-left:5px" />
                            <input type="text" class="form-control" id="txtPath" name="txtPath" readonly="readonly" style=" display:none" />
                            <input type="text" class="form-control" id="txtFile" name="txtFile" readonly="readonly" style=" display:none" />
                        </div>
                        <span style="font-size:12px;color:red;display:inline-block;margin-left:12px">选择好签名图片后，请点击 “上传”，最后点击“保存”</span>
                    </div>

                    <div style="width:100%">
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block"></span>
                            <img id="img1" alt="" src="" style=" width:140px; height:65px; cursor:pointer;" />
                            <input type="text" class="form-control" id="txtFName1" readonly="readonly" style=" display:none" />
                            <input type="text" class="form-control" id="txtP1" readonly="readonly" style=" display:none" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtInName" name="txtInName" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="UserSaveBtn">保存</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="UserSaveClose" onclick='closeDialog()'>关闭</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>


    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDelDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title">确认删除该用户吗？</span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelName" name="txtDelName" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelNo" name="txtDelNo" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="User_Del_Btn">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDelDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>


    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowBanStart">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDelDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon1">!</span> <span id="hint-title1"></span><span id="hint-value1"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtLogin" name="txtLogin" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtFlag" name="txtFlag" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" onclick="BanAndStartOP()">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDelDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowBanStart-fade" class="black_overlay">
    </div>


    <!--消息提示-->
    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>
</html>