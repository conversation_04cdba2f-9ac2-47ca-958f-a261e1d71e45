﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>抽样方案</title>
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <script src="../js/ajaxfileupload.js" type="text/javascript"></script>
    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/Tech.js" type="text/javascript"></script>

    <link href="../css/XC.css" rel="stylesheet" />

    <script type="text/javascript">
        layui.use("table", function () {
            var table = layui.table;
            table.render({
                elem: "#SamplingPlan",
                id: "SamplingPlanID",
                url: "../Service/TechAjax.ashx?OP=GetSamplingPlan&CFlag=121",
                height: "full-50",
                cellMinHeight: 80,
                count: 50,
                limit: 20,
                limits: [10, 20, 30, 40, 50],
                cols: [[
                    { type: "numbers" },
                    { field: "SPNo", title: "抽样方案编号", width: 120, sort: true },
                    { field: "SPName", title: "抽样名称", width: 200, sort: true },
                    { field: "Type", title: "抽样类型" },
                    { field: "InspectLevel", title: "检验水平" },
                    { field: "Stringency", title: "严格度" },
                    { field: "Aql", title: "接收质量限" },
                    { field: "InMan", title: "创建人" },
                    { field: "InDate2", title: "创建时间" },
                    { field: "op", title: "操作", width: 180, toolbar: "#barDemo", fixed: 'right' }
                ]],
                page: true
            })

            $("#SamplingPlanSearch").click(function () {
                var stxtCYFANo = $("#txtCYFANo").val()
                var stxtCYFAName = $("#txtCYFAName").val()

                var Data = JSON.stringify({ MNo: stxtCYFANo, MName: stxtCYFAName })

                table.reload("SamplingPlanID", {
                    method: "POST",
                    url: "../Service/TechAjax.ashx?OP=GetSamplingPlan&CFlag=121&Data=" + Data,
                    page: {
                        curr: 1
                    }
                })
            })

            $("#SamplingPlanDel").click(function () {

                var Params = { SPNo: $("#txtDelSPNo").val(), Flag: "2" }
                var Data = JSON.stringify(Params)

                $.ajax({
                    url: "../Service/TechAjax.ashx?OP=OPSamplingPlan&CFlag=2",
                    type: "POST",
                    data: {
                        Data: Data
                    },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);
                        if (parsedJson != undefined && parsedJson != '' && parsedJson.msg == 'Success') {
                            layer.msg('删除成功！');
                            $("#SamplingPlanSearch").click()
                            closeDialog()
                        } else {
                            $("#div_warning").html("系统出错，请重试1！")
                            $("#div_warning").show();
                        }
                    },
                    error: function () {
                        $("#div_warning").html("系统出错，请重试2！")
                        $("#div_warning").show();
                    }
                })
            })

            table.on("tool(SamplingPlan)", function (obj) {
                var data = obj.data
                var event = obj.event
                if (event == "edit") {
                    $("#ShowOne").css("display", "block")
                    $("#ShowOne-fade").css("display", "block")

                    $("#txtAEFlag").val("Edit")
                    $("#head-title1").html("修改抽样方案")
                    $("#txtSPName").val(data.SPName);
                    $("#txtAql").val(data.Aql);
                    $("#txtInspectLevel").val(data.InspectLevel);
                    $("#txtStringency").val(data.Stringency);
                    $("#txtType").val(data.Type);
                    $("#txtCYFANoEdit").val(data.SPNo)
                    $("#div_warning").hide()

                } else if (event == "detail") {
                    $("#ShowTow").css("display", "block")
                    $("#ShowTow-fade").css("display", "block")
                    $("#head-title1").html("方案详情")

                    $("#tdCYFANo").html(data.SPNo)
                    $("#tdCYFAName").html(data.SPName)
                    $("#tdType").html(data.Type)
                    $("#tdInspectLevel").html(data.InspectLevel)
                    $("#tdStringency").html(data.Stringency)
                    $("#tdAql").html(data.Aql)
                    $("#tdInMan").html(data.InMan)
                    $("#tdInDate").html(data.InDate2)

                    var Data = JSON.stringify({ MNo: data.SPNo, MName: data.SPName })

                    table.render({
                        elem: "#SamplingPlanDetailTable",
                        id: "SamplingPlanDetailTableID",
                        url: "../Service/TechAjax.ashx?OP=GetSamplingPlan&CFlag=121-1&Data=" + Data,
                        //height: "full-50",
                        cellMinHeight: 80,
                        cols: [[
                            { type: "numbers" },
                            { field: "Batch", title: "批量" },
                            { field: "SampleCode", title: "样本量字码" },
                            { field: "AqlTwo", title: "接收质量限" },
                            { field: "StringencyTwo", title: "严格度" },
                            { field: "SampleSize", title: "样本量" },
                            { field: "RNum", title: "接收数" },
                            { field: "BNum", title: "拒收数" }
                        ]]
                    })
                } else if (event == "delete") {
                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("确定要删除该抽样方案吗？方案编号：")

                    //设置删除的对象
                    $("#hint-value").html(data.SPNo)

                    $("#txtDelSPNo").val(data.SPNo)
                }
            })
        })
    </script>

    <script type="text/javascript">
        $(function () {
            $("#SamplingPlanAdd").click(function () {
                $("#ShowOne").css("display", "block")
                $("#ShowOne-fade").css("display", "block")
                $("#head-title1").html("新增抽样方案")
                $("#txtAEFlag").val("Add")
            })

        })

        function resetForm() {
            $("#txtSPName").val("");
            $("#txtAql").val("");
            $("#txtInspectLevel").val("");
            $("#txtStringency").val("");
            $("#txtType").val("");
            $("#txtBatch").val("");
        }

        function closeDialog() {
            $("#ShowOne").css("display", "none")
            $("#ShowOne-fade").css("display", "none")
            $("#ShowTow").css("display", "none")
            $("#ShowTow-fade").css("display", "none")
            $("#ShowDel").css("display", "none")
            $("#ShowDel-fade").css("display", "none")
            resetForm()
        }
    </script>


    <style>
        .layui-table th {
            font-size: 12px;
        }

        .layui-table td {
            font-size: 12px;
        }

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }


        #ShowOne .XC-Form-block-Item span {
            width: 70px;
            line-height: 30px;
        }
    </style>
</head>
<body>
    <div class="div_find">
        <label class="find_labela">抽样方案编号</label> <input type="text" id="txtCYFANo" class="find_input" />
        <label class="find_labela">方案名称</label><input type="text" id="txtCYFAName" class="find_input" />
        <input type="button" value="搜索" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="SamplingPlanSearch" />
        <input type="button" value="添加" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="SamplingPlanAdd" />
    </div>
    <div class="wangid_conbox">
        <table class="layui-hide" id="SamplingPlan" lay-filter="SamplingPlan"></table>
        <script type="text/html" id="barDemo">
            <button id="SamplingPlanDetail" lay-event="detail" class="XC-Btn-md XC-Btn-Green XC-Size-xs">详情</button>
            <button id="SamplingPlanEdit" lay-event="edit" class="XC-Btn-md XC-Btn-Green XC-Size-xs">修改</button>
            <button id="SamplingPlanDelete" lay-event="delete" class="XC-Btn-md XC-Btn-Red XC-Size-xs">删除</button>
        </script>
    </div>


    <!--弹出层-->
    <div class="XC-modal XC-modal-lg" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1"></span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body" style="padding:20px;height:285px">
            <form class="XC-Form-block">
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">方案名称<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <input type="text" class="XC-Input-block" id="txtSPName" name="txtSPName" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">抽样类型<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <select class="XC-Select-block" name="txtType" id="txtType">
                        <option value="" selected=selected></option>
                        <option value="按国际标准抽样">按国际标准抽样</option>
                    </select>
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">检验水平<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <select class="XC-Select-block" name="txtInspectLevel" id="txtInspectLevel">
                        <option value="" selected=selected></option>
                        <option value="特殊（S-1）">特殊（S-1）</option>
                        <option value="特殊（S-2）">特殊（S-2）</option>
                        <option value="特殊（S-3）">特殊（S-3）</option>
                        <option value="特殊（S-4）">特殊（S-4）</option>
                        <option value="一般（Ⅰ）">一般（Ⅰ）</option>
                        <option value="一般（Ⅱ）">一般（Ⅱ）</option>
                        <option value="一般（Ⅲ）">一般（Ⅲ）</option>
                    </select>
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">严格度<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <select class="XC-Select-block" name="txtStringency" id="txtStringency">
                        <option value="" selected=selected></option>
                        <option value="正常检验">正常检验</option>
                        <option value="加严检验">加严检验</option>
                        <option value="放宽检验">放宽检验</option>
                    </select>
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">接收质量限<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <select class="XC-Select-block" id="txtAql" name="txtAql">
                        <option value="" selected="selected"></option>
                        <option value="0.010">0.010</option>
                        <option value="0.015">0.015</option>
                        <option value="0.025">0.025</option>
                        <option value="0.040">0.040</option>
                        <option value="0.065">0.065</option>
                        <option value="0.10"> 0.10</option>
                        <option value="0.15">0.15</option>
                        <option value="0.25">0.25</option>
                        <option value="0.40">0.40</option>
                        <option value="0.65">0.65</option>
                        <option value="1.0">1.0</option>
                        <option value="1.5">1.5</option>
                        <option value="2.5">2.5</option>
                        <option value="4.0">4.0</option>
                        <option value="6.5">6.5</option>
                        <option value="10">10</option>
                        <option value="15">15</option>
                        <option value="25">25</option>
                        <option value="40">40</option>
                        <option value="65">65</option>
                        <option value="100">100</option>
                        <option value="150">150</option>
                        <option value="250">250</option>
                        <option value="400">400</option>
                        <option value="650">650</option>
                        <option value="1000">1000</option>
                    </select>
                </div>

            </form>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtCYFANoEdit" name="txtCYFANoEdit" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="SamplingPlanSubmit">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDialog()">取消</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>

    <!--点检任务详情弹窗层-->
    <div class="XC-modal XC-modal-xl" id="ShowTow">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1">抽样方案详情</span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <div style="padding: 10px 0px; font-size: 15px;">基本信息</div>
                <table class="layui-table">
                    <tr>
                        <td>抽样方案编号：</td>
                        <td id="tdCYFANo"></td>
                        <td>抽样名称：</td>
                        <td id="tdCYFAName"></td>
                        <td>抽样类型：</td>
                        <td id="tdType"></td>
                        <td>检验水平：</td>
                        <td id="tdInspectLevel"></td>
                    </tr>
                    <tr>
                        <td>严格度：</td>
                        <td id="tdStringency"></td>
                        <td>接收质量限：</td>
                        <td id="tdAql"></td>
                        <td>创建人：</td>
                        <td id="tdInMan"></td>
                        <td>创建时间：</td>
                        <td id="tdInDate"></td>
                    </tr>
                </table>

                <table class="layui-hide" id="SamplingPlanDetailTable" lay-filter="SamplingPlanDetailTable"></table>
            </div>
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick='closeDialog()'>关闭</button>
            </div>
        </div>
    </div>
    <div id="ShowTow-fade" class="black_overlay">
    </div>

    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelSPNo" name="txtDelSPNo" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="SamplingPlanDel">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>

    <!--消息提示-->
    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>
</html>