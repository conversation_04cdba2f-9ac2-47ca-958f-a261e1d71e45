﻿<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SPC</title>
    <script src="./js/jquery-3.3.1.min.js"></script>
    <link href="../Echart/css/XC.css" rel="stylesheet" />
    <link href="../Echart/css/layui.css" rel="stylesheet" />
    <script src="../Echart/js/layui.js"></script>

    <script>
        var CY = [
            { Name: "X-R图：均值-极差控制图", value: "1", Path: "/Echart/html/HtmlPage5.html" },
            { Name: "X-S图：均值-标准差控制图", value: "2", Path: "/Echart/html/HtmlPage6.html" }]
        var WR = [
            { name: "规则 1：一点超过 ±3σ（通常是控制限）", value: "1" },
            { name: "规则 2：九点连续在中心线同一侧", value: "2" },
            { name: "规则 3：六点连续递增或递减", value: "3" },
            { name: "规则 4：十四点或更多交替上升和下降", value: "4" },
            { name: "规则 5：两点或两点以上连续距离中心线同一侧超过 ±2σ，但未超过 ±3σ", value: "5" },
            { name: "规则 6：四点或四点以上连续距离中心线同一侧超过 ±1σ，但未超过 ±2σ", value: "6" },
            { name: "规则 7：十五点或更多连续在距离中心线任一侧在 ±1σ 以内", value: "7" },
            { name: "规则 8：八点或更多连续在距离中心线任一侧超过 ±1σ", value: "8" }
        ]

        $(function () {
            var data = getQueryParams()
            $("#SNo").val(data.No)
            $("#SCNum").val(data.SC)
            var selectedCheckboxes = [];

            $("#ConditionCheckSubmit").click(function () {
                var cyvaleu = $("#controlType").val()
                var toplimit = $("#toplimit").val().trim()
                var floor = $("#floor").val().trim()
                var regex = /^[0-9]+$/;
                if (cyvaleu == "") {
                    layer.msg("请选择控制图")
                    return
                } else if (cyvaleu == "1") {
                    if (toplimit == "") {
                        layer.msg("请输入上限")
                        return
                    }
                    if (floor == "") {
                        layer.msg("请输入下限")
                        return
                    }
                    if (!regex.test(toplimit + floor)) {
                        layer.msg("上下限不能包含字符")
                        return
                    } 
                    if (parseInt(toplimit) < parseInt(floor)) {
                        layer.msg("上限必要大于下限")
                        return
                    }
                } 


                sessionStorage.clear()
                var path = CY.find(item => item.value == $("#controlType").val())
                $("#top-titile").html("(" + data.No + ")" + path.Name)
                $("#templet").prop("src", path.Path)
                sessionStorage.setItem("No", data.No)
                sessionStorage.setItem("SC", data.SC)
                sessionStorage.setItem("GJGZ", JSON.stringify(selectedCheckboxes))
                sessionStorage.setItem("toplimit", toplimit)
                sessionStorage.setItem("floor", floor)
                CloseModal()
                selectedCheckboxes = []
            })

            $("#GJGZ").on("change", "input[type=checkbox]", function () {
                var checkbox = $(this);
                if (checkbox.is(":checked")) {
                    // 如果复选框被选中，则将其值添加到数组中
                    selectedCheckboxes.push(WR.find(item => item.value == checkbox.val()))
                } else {
                    // 如果复选框被取消选中，则从数组中移除其值
                    var index = selectedCheckboxes.findIndex(item => item.value == checkbox.val());
                    if (index !== -1) {
                        selectedCheckboxes.splice(index, 1);
                    }
                }
            })
        })

        // 每秒更新时间
        setInterval(function () {
            var now = new Date();

            // 获取年、月、日
            var year = now.getFullYear();
            var month = (now.getMonth() + 1).toString().padStart(2, '0');
            var date = now.getDate().toString().padStart(2, '0');

            // 获取星期
            var daysOfWeek = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
            var dayOfWeek = daysOfWeek[now.getDay()];

            // 获取时、分、秒
            var hours = now.getHours().toString().padStart(2, '0');
            var minutes = now.getMinutes().toString().padStart(2, '0');
            var seconds = now.getSeconds().toString().padStart(2, '0');

            // 拼接成字符串
            var currentDateTimeString = year + '/' + month + '/' + date + ' ' + dayOfWeek + ' ' + hours + ':' + minutes + ':' + seconds;

            // 显示在网页上
            document.getElementById('currentDateTime').textContent = currentDateTimeString;
        }, 1000);

        //打开条件框
        function OpenModal() {
            $("#ShowOne").css("display", "block")
            $("#ShowOne-fade").css("display", "block")

            $("#controlType").empty()
            $("#controlType").append("<option></option>")
            CY.forEach(item => {
                $("#controlType").append("<option value='" + item.value + "'>" + item.Name + "</option>")
            })

            $("#GJGZ").empty()
            WR.forEach(item => {
                $("#GJGZ").append("<input style='vertical-align: middle;margin-right:5px' class='XC-Input-block' name='GJGZ' value='" + item.value + "' type='checkbox' /><span style='font-size: 12px'>" + item.name + "</span><br />")
            })

            $(".ItemOne").hide()
        }

        //关闭条件框
        function CloseModal() {
            $("#ShowOne").css("display", "none")
            $("#ShowOne-fade").css("display", "none")
            $("#controlType").val("");
        }

        function getQueryParams() {
            var params = {};
            var queryString = window.location.search.substring(1);
            var queryArray = queryString.split('&');
            for (var i = 0; i < queryArray.length; i++) {
                var pair = queryArray[i].split('=');
                params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
            }
            return params;
        }

        function ParameterConditions(event) {

            var CYValeu = $("#controlType").val();

            switch (CYValeu) {
                case "1":
                    $(".ItemOne").show()
                    break;
                case "2":
                    $(".ItemOne").show()
                    break;
                default:
                    $(".ItemOne").hide()
                    break;
            }
        }


        function GetRequestParams(txtsBoardNo, txtsBoardName, FieldNo, flag) {

            var Data = '';
            var Params = { No: txtsBoardNo, Name: txtsBoardName, Item: FieldNo, MNo: "", MName: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "", E: "", F: "", G: "", H: "", I: "", J: "", K: "", L: "", Remark: "", Flag: flag };
            var Data = JSON.stringify(Params);

            return Data
        }
    </script>


    <style>
        .layui-card-header {
            display: flex;
            flex-wrap: nowrap;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #d3e3fd;
        }

        .layui-card-body {
            height: 80%;
            padding: 0px
        }


        #templet {
            width: 100%;
            height: 100%
        }

        #ShowOne .XC-Span-Input-block {
            width: 85px;
        }

        .top-titile {
            font-weight: bold
        }
    </style>
</head>

<body>
    <div class="layui-bg-gray" style="padding: 0px;height:100vh">
        <div class="layui-card">
            <div class="layui-card-header">
                <div><button type="button" class="XC-Btn-Green XC-Btn-md XC-Size-sm" onclick="OpenModal()">设置</button></div>
                <div class="top-titile" id="top-titile">SPC</div>
                <div id="currentDateTime"></div>
            </div>
            <div class="layui-card-body" style="height:calc(100vh - 48px);">
                <iframe id="templet" src="" frameborder="0" data-id="0" style="display:block"></iframe>
            </div>
        </div>
    </div>

    <div class="XC-modal XC-modal-md" id="ShowOne" style="height:650px;width: 900px">
        <div class="XC-modal-head" style="background-color: green;color: white;">
            <span class="head-title" id="head-title1">设置条件</span>
            <span class="head-close" onclick="CloseModal()">X</span>
        </div>
        <div class="XC-modal-body">
            <form class="XC-Form-block">
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">编号</span>
                    <input class="XC-Input-block" id="SNo" disabled />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">样本容量</span>
                    <input class="XC-Input-block" id="SCNum" disabled />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">控制图</span>
                    <select class="XC-Input-block" id="controlType" onchange="ParameterConditions(event)">
                    </select>
                </div>
                <div class="XC-Form-block-Item ItemOne">
                    <span class="XC-Span-Input-block">规范上限(USL)</span>
                    <input class="XC-Input-block" id="toplimit" />
                </div>
                <div class="XC-Form-block-Item ItemOne">
                    <span class="XC-Span-Input-block">规范下限(LSL)</span>
                    <input class="XC-Input-block" id="floor" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">告警规则</span>
                    <div id="GJGZ"></div>
                </div>
            </form>
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="ConditionCheckSubmit">确定</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="BatchSerialClose"
                        onclick='CloseModal()'>
                    关闭
                </button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>


    <div id="loading" class="black_overlay" style="z-index: 1008; ">
        <div style="position: fixed;  top: 50%;  left: 50%;  transform: translate(-50%, -50%); ">
            <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
        </div>
        <div class="docs-icon-name" style="position: fixed;  top: 55%;  left: 50%;  transform: translate(-46.5%, -50%);font-size:12px ">正在加载中，请稍等！</div>
    </div>
</body>

</html>