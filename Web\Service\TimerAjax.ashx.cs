﻿using BLL;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.SessionState;
using Web.Echart.Common;

namespace Web.Service
{
    /// <summary>
    /// TimerAjax 用来处理定时请求
    /// </summary> 
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class TimerAjax : IHttpHandler, IRequiresSessionState
    {
        private readonly TimerTaskManager _timerTaskManager = TimerTaskManager.Instance;
        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "text/plain";

            string Result = string.Empty;
            string Message = "Success";
            string Operate = context.Request.Params["OP"];
            string DataParams = context.Request.Params["Data"];
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            DataTable dt = new DataTable();

            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item = JsonConvert.DeserializeObject(DataParams);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item), AnonymousUser);

            if (context.Session["LoginName"] != null)
            {
                sLogin = context.Session["LoginName"].ToString();
                sManName = context.Session["FullName"].ToString();
                sComp = context.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }


            if (Operate == "AddTask")
            {
                Result = EchartBll.AddTask(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.Remark, sLogin, sComp, It.Flag);

                //string taskId = It.No;

                //int interval = Convert.ToInt32(It.C) * 60;

                //DateTime startdate = DateTime.Parse(It.A);

                //while (startdate <= DateTime.Now)
                //{
                //    startdate = startdate.AddSeconds(interval);
                //}

                //TimeSpan initialDelay = startdate - DateTime.Now;

                //_timerTaskManager.AddTimerTask(taskId, initialDelay, TimeSpan.FromSeconds(interval), sLogin);

                Result = JsonConvert.SerializeObject(new { Msg = Result, Data = "" });
            }
            else if (Operate == "RemoveTask")
            {
               // _timerTaskManager.RemoveTimerTask(It.No);
                Result = EchartBll.AddTask(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.Remark, sLogin, sComp, It.Flag);
                Result = JsonConvert.SerializeObject(new { Msg = Result, Data = "" });
            }

            context.Response.Write(Result);
        }

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}