﻿<!DOCTYPE html
          PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>交付看板</title>
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/echarts.js"></script>

    <script>

        $(function () {
            init()
        })

        // 初始化函数
        function init() {
            const data = sessionStorage.getItem("KB0001");
            let dayCount = 0; //当天完成的数量
            let monthCount = 0;  //当月完成的数量
            var orderListData = [] //当天工单生产产量
            var dayProductData = [] //当天工各时间段产量

            // 校验并解析数据
            if (data) {
                try {
                    const jsonData = JSON.parse(data);
                    dayCount = jsonData[0] || [];
                    monthCount = jsonData[1] || [];
                    orderListData = jsonData[2] || [];
                    dayProductData = jsonData[3] || [];
                } catch (error) {
                    console.error("数据解析失败:", error);
                }
            }

            // 渲染已完成和未完成的图表
            RenderChart(dayProductData, 'dayProductCountEcharts', '当日各时段产量及累计产量情况');
            // 渲染工单列表
            RenderList(orderListData)
            // 显示当天、月数量
            updateProductionData(dayCount, monthCount)
        }

        /**
         * @param {Array} data - 图表数据
         * @param {string} chartId - 图表容器的ID
         * @param {string} title - 图表的标题
         */
        function RenderChart(data, chartId, title) {
            if (!data || data.length === 0) {
                console.warn(`没有数据提供给图表 ${chartId}`);
                return;
            }

            let sum = 0;

            const chart = echarts.init(document.getElementById(chartId));
            chart.setOption({
                title: {
                    text: title,
                    textStyle: {
                        color: "white", // 使用暖金色提升视觉吸引力
                        fontSize: "15px",// 增大标题字号
                        fontWeight: "500"
                    },
                    left: "center",
                    top: '1%', // 上移标题位置
                },

                grid: {
                    left: "4%", // 增加左侧边距
                    right: "2%",
                    bottom: "7%", // 增加底部边距
                    top: "12%",
                    containLabel: false, // 确保标签完全包含在网格内
                },

                tooltip: {
                    trigger: 'axis',
                },

                legend: {
                    data: ['累计产量', '产量'],
                    textStyle: {
                        color: "white",
                        fontWeight: "bold"
                    },
                    left: '1%',
                    top: '1%',
                },

                toolbox: {
                    show: true,
                    right: "1%",
                    feature: {
                        dataView: { readOnly: true },
                        magicType: { type: ['line', 'bar'] },
                        restore: { show: true }, // 隐藏还原按钮
                        saveAsImage: { show: true } // 隐藏保存图片按钮
                    },
                },
                xAxis: {
                    type: 'category',
                    axisLine: {
                        lineStyle: {
                            color: 'white',
                        }
                    },
                    axisTick: {
                        show: false,  // 是否显示坐标轴刻度。
                        lineStyle: {
                            color: 'white',
                        },
                    },
                    axisLabel: {
                        color: "#FFFFFF",
                        fontWeight: "bold",
                        formatter: '{value} 时' // 可根据需要添加单位
                    },
                    data: data.map(item => item.DayNum + " : 00")
                },

                yAxis: {
                    type: 'value',
                    axisLabel: {
                        color: "#FFFFFF",
                        fontWeight: "bold",
                        formatter: '{value}个' // 可根据需要添加单位
                    },
                    axisLine: {
                        show: false,
                        lineStyle: {
                            color: 'white',
                        },
                    },
                    axisTick: {
                        show: false,  // 是否显示坐标轴刻度。
                        lineStyle: {
                            color: 'white',
                        },
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(255,255,255,0.12)'
                        }
                    },
                    minInterval: 1 // 强制整数刻度
                },

                series: [
                    {
                        name: '累计产量',
                        type: 'bar',
                        data: data.map(item => {
                            sum += item.HourCount;
                            return sum;
                        }),
                        barWidth: 20,
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    {
                                        offset: 0,
                                        color: "#4ab2cf",
                                    },
                                    {
                                        offset: 1,
                                        color: "#5a99d2",
                                    },
                                ]),
                                barBorderRadius: [30, 30, 30, 30],
                                shadowColor: 'rgba(0,160,221,1)',
                                shadowBlur: 4,
                            },
                        },
                        label: {
                            show: true,
                            color: "white",
                            fontWeight: "bold",
                            position: 'top',
                            fontSize: 14,
                            formatter: (params) => params.value == 0 ? '' : params.value
                        },
                    },
                    {
                        name: '产量',
                        type: 'line',
                        data: data.map(item => item.HourCount),
                        smooth: true, //平滑曲线显示
                        showAllSymbol: true, //显示所有图形。
                        symbol: "circle", //标记的图形为实心圆
                        symbolSize: 10, //标记的大小
                        itemStyle: {
                            //折线拐点标志的样式
                            color: "#0e5299",
                        },
                        lineStyle: {
                            color: "#0e5299",
                        },
                        areaStyle: {
                            color: "rgba(5,140,255, 0.2)",
                        },
                        label: {
                            show: true,
                            color: "white",
                            fontWeight: "bold",
                            position: 'top',
                            fontSize: 14,
                            formatter: (params) => params.value == 0 ? '' : params.value
                        }
                    }
                ],
                animation: {
                    duration: 1000, // 动画时长
                    easing: 'easeInOutQuad' // 缓动效果
                },
            });

            // 窗口调整时重新渲染图表
            $(window).resize(() => chart.resize());
        }

        /**
         * @param {Array} data - 当天工单生产产量
         */
        function RenderList(data) {
            $("#order-list").empty()
            const str = data.map(item => `<div class="order-list-item"><div>${item.OrderNo}</div><div>${item.DayNum}</div><div>${item.OrderNum}</div><div>${item.OverNum}</div><div>${calculatePercentage(item.OverNum, item.OrderNum)}</div></div>`).join('');
            $("#order-list").append(str)
        }

        /**
        * @param {Array} dayCount    - 当天数据
        * @param {Array} monthCount  - 当月数据
        */
        function updateProductionData(dayCount, monthCount) {
            // 更新当天产量
            $("#DayNum").html(dayCount[0]?.sumDayNum ?? 0);

            // 更新当月产量
            $("#MonthNum").html(monthCount[0]?.sumMonthNum ?? 0);
        }

        /**
        * @param {Array} overCount   - 已完成数量
        * @param {Array} orderCount  - 总数量
        */
        function calculatePercentage(overCount, orderCount) {
            if (orderCount === 0) return "0.00%";
            const percentage = (overCount / orderCount) * 100;
            return `${percentage.toFixed(2)}%`;
        }
    </script>

    <style>
        * {
            padding: 0;
            margin: 0
        }

        #container {
            height: 100vh;
            background: #15181d;
            overflow: hidden;
            position: relative;
            color: #ffffff;
        }

        .container-header {
            margin: 10px 10px 0px 10px;
            height: 36%;
            display: flex
        }

        .container-body {
            height: 61.5%;
            margin: 0px 10px 0px 10px;
            background-color: #1b1e25
        }


        .header-left,
        .header-right,
        .header-left-top,
        .header-left-bottom {
            flex: 1;
        }

        .header-left {
            display: flex;
            padding: 0px 5px 10px 0px;
        }

        .header-left-top {
            padding: 10px;
            background-color: #1b1e25
        }

        .header-left-bottom {
            background-color: #1b1e25
        }

        .header-right {
            padding: 0px 0px 10px 5px;
        }

        .header-right-body {
            background-color: #1b1e25;
            height: 100%;
        }

        .order-list {
            margin: 10px 0px;
            height: 80%
        }


        .order-head {
            display: flex;
            color: white;
            font-size: 14px;
            font-weight: bold;
            border-bottom: 1px solid white;
        }

            .order-head > div {
                flex: 1;
                text-align: center;
                padding: 10px 0px;
            }

        .order-list-item {
            display: flex;
            font-size: 12px;
            font-weight: bold;
            color: #64b4ed;
            margin-bottom: 10px;
        }

            .order-list-item > div {
                flex: 1;
                text-align: center
            }

        .order-list {
            height: 80%;
            overflow: auto
        }


            /* 自定义滚动条样式 */
            .order-list::-webkit-scrollbar {
                width: 4px;
                height: 10px;
            }

            /* 滚动条滑块样式 */
            .order-list::-webkit-scrollbar-thumb {
                background-color: #888;
                border-radius: 5px;
            }

        .status {
            width: 100px;
            padding: 5px;
            float: left;
            position: relative;
            z-index: 9999;
            /* 设置一个较高的值 */
            margin: 5px 0px 0px 5px;
            font-size: 12px;
        }

            .status:focus {
                outline: none;
            }
    </style>

</head>


<body>
    <div id="container">
        <!--顶部-->
        <div class="container-header">
            <div class="header-left">
                <div class="header-left-top" style="display:flex;align-items:center">
                    <div style="flex: 1; text-align: center">
                        <div style="font-size: 18px; font-weight: 600">当日实际产量</div>
                        <div style="font-size: 35px; font-weight: bold; color: #64b4ed" id="DayNum"></div>
                    </div>
                </div>
                <div class="header-left-bottom" style="display:flex;align-items:center">
                    <div style="flex:1;text-align:center">
                        <div style="font-size: 18px; font-weight: 600">当月实际产量</div>
                        <div style="font-size: 35px; font-weight: bold; color: #64b4ed" id="MonthNum"></div>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="header-right-body">
                    <div style="font-size:15px;padding-top:5px;margin-left:10px;text-align: center;">当日订单完成情况</div>
                    <div style="margin: 0px 10px 10px 10px;height:90%">
                        <div class="order-head">
                            <div>订单ID</div>
                            <div>日实际产量</div>
                            <div>订单总数</div>
                            <div>总产量</div>
                            <div>进度</div>
                        </div>

                        <div class="order-list" id="order-list">

                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div class="container-body">
            <div id="dayProductCountEcharts" style="width: 100%; height: 100%;"></div>
        </div>
    </div>
</body>

</html>