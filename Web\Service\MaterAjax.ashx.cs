﻿using System;
using System.Collections;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml.Linq;
using BLL;
using Newtonsoft.Json;
using System.Web.SessionState;
using Common;
using System.Collections.Generic;
using WXCommon;

namespace Web.Service
{
    /// <summary>
    /// $codebehindclassname$ 的摘要说明
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class MaterAjax : IHttpHandler, IRequiresSessionState
    {

        string sAMFlag = string.Empty;

        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "text/plain";
            string Result = string.Empty;
            string Operate = context.Request.Params["OP"];
            string DataParams = context.Request.Params["Data"];
            string sItem = context.Request.Params["Item"];
            sAMFlag = context.Request.Params["CFlag"];
            string sKind = context.Request.Params["CKind"];  // 接收所有类别
            string sStatus = context.Request.Params["CStatus"];  // 状态
            string sCNo = context.Request.Params["CNO"];  // 接收所有编号  
            string sMNo = context.Request.Params["CMNO"];  // 物料编码 
            string sPNo = context.Request.Params["CPNO"];  // 采购单号
            string sSPNo = context.Request.Params["CSPNO"];  // 供应商编号
            string sCName = context.Request.Params["CNAME"];  // 接收所有名称
            string sCustNo = context.Request.Params["CCustNo"];
            string sAddNum = context.Request.Params["AddNum"];  // 数据加载的次数
            int slimit = 0;
            int spage = 0;


            switch (Operate)
            {

                case "GetMaterFlowInfo"://获取客户询价信息（采购项目需求） 
                    slimit = int.Parse(context.Request.Params["limit"]);
                    spage = int.Parse(context.Request.Params["page"]);
                    GetMaterFlowInfo(DataParams, slimit, spage, sStatus, sCNo, sItem, sAMFlag);
                    break;

                case "AddEditSH": //插入或更新收货信息
                    Result = AddEditSHInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetMaterBatchList": //根据物料编码，获取物料库存批次
                    Result = GetMaterBatchList(sMNo, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetPOutMX": //获取出库单明细  
                    Result = GetPOutMX(sCNo, sAMFlag);
                    break;


                case "CreatePackList":  //生成装箱清单，导出EXCEL  
                    Result = CreatePackList(DataParams, sCNo, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "OPPackList": // 操作（删除）装箱单信息：更新出库信息装箱单编号为空
                    Result = OPPackList(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "CreatePDOutList":  //生成出库统计表，导出EXCEL  
                    Result = CreatePDOutList(DataParams, sCNo, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;









            }
        }





        #region 获取物料流程相关信息（收货，IQC检验，入库，出库）
        public void GetMaterFlowInfo(string Params, int rows, int page, string sStatus, string sCQNo, string sItmNo, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sReturn = string.Empty;
            string sComp = string.Empty;
            string sPNo = string.Empty;
            string sMNo = string.Empty;
            string sMName = string.Empty;
            string sBDate = string.Empty;
            string sEDate = string.Empty;
            string sSt = string.Empty;
            string sA = string.Empty;
            string sB = string.Empty;
            string sC = string.Empty;
            string sD = string.Empty; 
            int iSumCount = 0;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return;
            }

            // var Params = { PPNo:sPNo,Item:sItem,CustNo:sCNo,CustEn:sEn,BDate:sBDate, EDate:sEDate,Status:sSt,Check:sCheck};
            var AnonymousUser = new
            {
                PNo = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                BDate = String.Empty,
                EDate = String.Empty,
                Status = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
            };


            if (string.IsNullOrEmpty(Params) || (Params == null) || (Params == "null"))
            {
                sPNo = "";
                sMNo = "";
                sMName = "";
                sBDate = "";
                sSt = "";
                sA = "";
                sB = "";
                sC = "";
                sD = "";
            }
            else
            {
                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sPNo = Item.PNo;
                sMNo = Item.MNo;
                sMName = Item.MName;
                sBDate = Item.BDate;
                sEDate = Item.EDate;
                sSt = Item.Status;
                sA = Item.A;
                sB = Item.B;
                sC = Item.C;
                sD = Item.D;
            }
            if (sBDate == "")
            {
                sBDate = "2021-01-01";
            }
            if (sEDate == "")
            {
                sEDate = "2999-12-30";
            }

            if (string.IsNullOrEmpty(sSt))
            {
                if (!string.IsNullOrEmpty(sStatus))
                {
                    sSt = sStatus;
                }
            }
            if (string.IsNullOrEmpty(sPNo))
            {
                if (!string.IsNullOrEmpty(sCQNo))
                {
                    sPNo = sCQNo;
                }
            }

            if (Flag == "13-1") // 点击出库单-查看出库单 按钮,出入PR和项次到过程查询数据
            {
                sA = sItmNo;
                Flag = "13";  // 存储过程 13 表示查询出库单信息
            }

            //if (string.IsNullOrEmpty(sD))  // 这个变量是给进销存物料查询使用的
            //{
            //    if (!string.IsNullOrEmpty(sItmNo))
            //    {
            //        sD = sItmNo;
            //    }
            //}


            // PONo, MNo, MName, BDate, EDate, Status, A, B, C, D, Row, num, sInMan, sComp, sFlag
            DataTable dt = MaterBll.GetMaterFlowInfo(sPNo, sMNo, sMName, sBDate, sEDate, sSt, sA, sB, sC, sD, rows, page, sMan, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                iSumCount = int.Parse(dt.Rows[0]["NumCount"].ToString());

                string sJson = JsonConvert.SerializeObject(dt);
                sReturn = "{\"code\":0,\"msg\":\"\",\"count\":" + iSumCount + ",\"data\":" + sJson + "}";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;
            context.Response.Write(sReturn);
        }
        #endregion


        #region 插入或更新收货信息
        public string AddEditSHInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            string sDNo = string.Empty;
            string sIQCNo = string.Empty;
            float fSNum = 0;
            string sSNum = string.Empty;


            var AnonymousUser = new
            {
                RNo = String.Empty,DNo = String.Empty, PONo = String.Empty, POItem = String.Empty, MNo = String.Empty, DNum = String.Empty,OldNum = String.Empty, Num = String.Empty,
                ZCNum = String.Empty,RBNum = String.Empty,FXNum = String.Empty,EffDate = String.Empty, SHNo = String.Empty, ck1 = String.Empty, ck2 = String.Empty, ck3 = String.Empty, 
                ck4 = String.Empty, ck5 = String.Empty, cQT = String.Empty,Remark = String.Empty, Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            if (It.Flag == "1") // 新增
            {
                sRNo = CreateSHNo();

                // 判断是否已收货完成，
                sOKFlag = MaterBll.JudgeSysKindExist(It.PONo, It.POItem, "", "", sComp, "10-1");
                if (sOKFlag == "Y")  //  
                {
                    Message = "Y_OVER";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                // 判断是否有待IQC检验的，如是，不给新增，直接修改数量即可。
                sOKFlag = MaterBll.JudgeSysKindExist(It.PONo, It.POItem, "", "", sComp, "10-2");
                if (sOKFlag == "Y")   
                {
                    Message = "Y_NOTIQC";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

            }
            else if ((It.Flag == "2") || (It.Flag == "3"))  //修改  删除  
            {
                sRNo = It.RNo;

                // 判断是否已IQC检验 
                sOKFlag = MaterBll.JudgeSysKindExist(sRNo, "", "", "", sComp, "10-3");
                if (sOKFlag == "Y")  // 判断是否已审核
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

            }
            else
            {
                sRNo = It.RNo;
            }
            sDNo = It.DNo;


            if (It.Flag == "4") // IQC录入信息
            {
                sDNo = CreateBatchNo();

                // 判断这个收货批次是否已IQC检验
                sOKFlag = MaterBll.JudgeSysKindExist(It.RNo, "", "", "", sComp, "10-3");
                if (sOKFlag == "Y")  //  
                {
                    Message = "Y_OVER";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

            }
            else if ((It.Flag == "5") || (It.Flag == "6"))  //修改  删除  
            {
                sDNo = It.DNo;  // 在IQC环境，这个是IQC入库编号，也是入库库存批次号

                //// 判断是否已IQC检验    --- 什么情况下，不给IQC修改，删除记录？？？？？
                //sOKFlag = MaterBll.JudgeSysKindExist(sRNo, "", "", sComp, "10-3");
                //if (sOKFlag == "Y")  // 判断是否已审核
                //{
                //    Message = "Y_EXIST";
                //    return JsonConvert.SerializeObject(new { Msg = Message });
                //}

            }

            if (It.Flag == "7") // 出库单信息录入
            {
                sRNo = CreateOutBillNo();

                // 判断这个PR单是否已出库完成
                sOKFlag = MaterBll.JudgeSysKindExist(It.DNo, "", "", "", sComp, "12-1");
                if (sOKFlag == "Y")  //  
                {
                    Message = "Y_OVER";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                // 判断出库数量是否大于PR的数量
                sOKFlag = MaterBll.JudgeSysKindExist(It.DNo, "", "", It.DNum, sComp, "12-2");
                if (sOKFlag == "N")  //  
                {
                    Message = "Y_NUM";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                // 判断出库数量是否大于库存数量
                sOKFlag = MaterBll.JudgeSysKindExist(It.DNo, "", It.MNo, It.DNum, sComp, "12-4");
                if (sOKFlag == "N")  //  说明出库数量大于当前库存数量（库存由两部分组成：原材料+组件入库）
                {
                    Message = "Y_DYSTOCK";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

            }
            else if ((It.Flag == "8-88") || (It.Flag == "9-99"))  //修改  20210612:修改暂时不使用了。 删除也不需要做什么判断，所以把这两个标识修改一些。
 
            {
                sRNo = It.RNo;

                // 判断这个PR单是否已出库完成
                sOKFlag = MaterBll.JudgeSysKindExist(It.DNo, "", "", "", sComp, "12-1");
                if (sOKFlag == "Y")  //  
                {
                    Message = "Y_OVER";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                // 判断修改后的数量，是否大于PR订单数量
                if (It.Flag == "8") {
                    sOKFlag = MaterBll.JudgeSysKindExist(It.DNo, "", sRNo, It.DNum, sComp, "12-3");
                    if (sOKFlag == "N")  // 判断是否已审核
                    {
                        Message = "Y_NUM";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }

            }

            if (It.Flag == "10") // 录入退货信息
            {
                sRNo = CreateReturnNo();

                string sZC = string.Empty;
                if (string.IsNullOrEmpty(It.FXNum)) {
                    sZC = "0";
                }
                else{
                    sZC = It.FXNum;
                }
                fSNum = float.Parse(It.Num) + float.Parse(sZC);  // 再次放行数量+退货数量 = 不良数量
                sSNum = fSNum.ToString();

                // 判断IQC退料是否还有退货的数量
                if (float.Parse(It.Num) > 0){
                    sOKFlag = MaterBll.JudgeSysKindExist(It.DNo, It.PONo, It.OldNum, sSNum, sComp, "15-1-1");  //  It.PONo:IQC不合格 还是 仓库不合格

                    if (sOKFlag == "N")  // 没有那么多退货数量了。 
                    {
                        Message = "Y_OVER";
                        return JsonConvert.SerializeObject(new { Msg = Message,Txt="退IQC料数量填写不正确" });
                    }
                }

                //判断退库存数量不能大于库存数量
                if (float.Parse(It.DNum) > 0)
                {
                    sOKFlag = MaterBll.JudgeSysKindExist(It.DNo, It.PONo, It.OldNum, It.DNum, sComp, "15-1-2");  //  It.PONo:IQC不合格 还是 仓库不合格
                    if (sOKFlag == "N")  // 没有那么多退货数量了。 
                    {
                        Message = "Y_OVER";
                        return JsonConvert.SerializeObject(new { Msg = Message, Txt = "退合格数量填写不正确" });
                    }
                }
                //判断退暂存仓数量不能大于暂存仓数量
                if (float.Parse(It.ZCNum) > 0)
                {
                    sOKFlag = MaterBll.JudgeSysKindExist(It.DNo, It.PONo, It.OldNum, It.ZCNum, sComp, "15-1-3");  //  It.PONo:IQC不合格 还是 仓库不合格
                    if (sOKFlag == "N")  // 没有那么多退货数量了。 
                    {
                        Message = "Y_OVER";
                        return JsonConvert.SerializeObject(new { Msg = Message, Txt = "退暂存仓数量填写不正确" });
                    }
                }

                //判断退让步接收数量不能大于让步接收数量
                if (float.Parse(It.RBNum) > 0)
                {
                    sOKFlag = MaterBll.JudgeSysKindExist(It.DNo, It.PONo, It.OldNum, It.RBNum, sComp, "15-1-4");  //  It.PONo:IQC不合格 还是 仓库不合格
                    if (sOKFlag == "N")  // 没有那么多退货数量了。 
                    {
                        Message = "Y_OVER";
                        return JsonConvert.SerializeObject(new { Msg = Message, Txt = "退让步接收数量填写不正确" });
                    }
                }

            }
            else if (It.Flag == "11")  //修改 20210821:暂时不需要修改，退错了，删除重新退货
            {
                sRNo = It.RNo;

                string sZC = string.Empty;
                if (string.IsNullOrEmpty(It.ZCNum)) {
                    sZC = "0";
                }
                else {
                    sZC = It.ZCNum;
                }
                fSNum = float.Parse(It.Num) + float.Parse(sZC);
                sSNum = fSNum.ToString();

                // 判断是否还有退货的数量
                sOKFlag = MaterBll.JudgeSysKindExist(It.DNo, It.PONo, It.OldNum, sSNum, sComp, "15-1");  //  It.PONo:IQC不合格 还是 仓库不合格
                if (sOKFlag == "N")  // 没有那么多退货数量了。 
                {
                    Message = "Y_OVER";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                //  
                sOKFlag = MaterBll.JudgeSysKindExist(sRNo, It.POItem, sRNo, It.DNum, sComp, "15-2");
                if (sOKFlag == "Y")  // 判断是否已审核
                {
                    Message = "Y_STATUS";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

            }
            else if ((It.Flag == "12") || (It.Flag == "13"))  //   删除  审核 
            {
                sOKFlag = MaterBll.JudgeSysKindExist(It.RNo, "", "", "", sComp, "15-2");
                if (sOKFlag == "Y")  // 判断是否已审核
                {
                    Message = "Y_STATUS";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }


            if (It.Flag == "14") //录入发料信息
            {
                sRNo = CreateFillNo();

                //判断发料库存数量不能大于库存数量
                if (float.Parse(It.DNum) > 0)
                {
                    sOKFlag = MaterBll.JudgeSysKindExist(It.DNo, "", "", It.DNum, sComp, "14-1");
                    if (sOKFlag == "N")  // 没有那么多发料数量了。 
                    {
                        Message = "Y_OVER";
                        return JsonConvert.SerializeObject(new { Msg = Message, Txt = "发料合格数量填写不正确" });
                    }
                }
                //判断发料暂存仓数量不能大于暂存仓数量
                if (float.Parse(It.ZCNum) > 0)
                {
                    sOKFlag = MaterBll.JudgeSysKindExist(It.DNo, "", "", It.ZCNum, sComp, "14-2"); 
                    if (sOKFlag == "N")  // 没有那么多发料数量了。 
                    {
                        Message = "Y_OVER";
                        return JsonConvert.SerializeObject(new { Msg = Message, Txt = "发料暂存仓数量填写不正确" });
                    }
                }

                //判断发料让步接收数量不能大于让步接收数量
                if (float.Parse(It.RBNum) > 0)
                {
                    sOKFlag = MaterBll.JudgeSysKindExist(It.DNo, "", "", It.RBNum, sComp, "14-3");
                    if (sOKFlag == "N")  // 没有那么多发料数量了。 
                    {
                        Message = "Y_OVER";
                        return JsonConvert.SerializeObject(new { Msg = Message, Txt = "发料让步接收数量填写不正确" });
                    }
                }


            }
            else if (It.Flag == "15")  // 修改发料信息。判断是否库存够减
            {
                var cNum = double.Parse(It.DNum) - double.Parse(It.OldNum);
                if (cNum>0)  // <= 0 不需要判断，因为 小于 0 表示要回退一些库存。
                {
                    sOKFlag = MaterBll.JudgeSysKindExist(It.DNo, "", "", cNum.ToString(), sComp, "14-1");  //  It.PONo:IQC不合格 还是 仓库不合格
                    if (sOKFlag == "N")  // 说明发料数量过大 ，不满足发料条件
                    {
                        Message = "Y_OVER";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }
            }
            else if (It.Flag == "17")  // 产品入库，判断产品编码是否存在
            {
                sRNo = CreateInStockNo();

                sOKFlag = MaterBll.JudgeSysKindExist(It.PONo, "", "", "", sComp, "17-1");  // 
                if (sOKFlag == "N")  // 不存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "20")  //零件报废
            {
                sRNo = CreateMaterFaultyNo();

                sOKFlag = MaterBll.JudgeSysKindExist(It.DNo, "", "", It.Num, sComp, "20-1");  // 
                if (sOKFlag == "N")  // 物料不存在，或库存不足
                {
                    Message = "Y_OVER";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "21")  // 修改报废信息。判断是否库存够减
            {
                var cNum = double.Parse(It.Num) - double.Parse(It.OldNum);
                if (cNum > 0)  // <= 0 不需要判断，因为 小于 0 表示要回退一些库存。
                {
                    sOKFlag = MaterBll.JudgeSysKindExist(It.DNo, "", "", cNum.ToString(), sComp, "20-1");  
                    if (sOKFlag == "N")  // 说明报废数量过大 ，不满足报废条件
                    {
                        Message = "Y_OVER";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }
            }


            if (It.Flag == "7")  // 出库管理，单独调用一个过程
            {
                sOKFlag = MaterBll.SaveProductOutInfo(sRNo, It.DNo, It.DNum, sLogin, sComp, It.Flag);
            }
            else
            {
                sOKFlag = MaterBll.AddEditSHInfo(sRNo, sDNo, It.PONo, It.POItem, It.DNum, It.OldNum, It.Num, It.ZCNum, It.RBNum, It.FXNum, It.EffDate, It.SHNo, It.ck1, It.ck2, It.ck3, It.ck4, It.ck5, It.cQT, sComp, sLogin, sManName, It.Remark, It.Flag);
            }

            if (sOKFlag.Length <= 2)
            {

                Message = "Success";

            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });

            return Result;
        }



        #region 根据物料编码，获取库存批次及物料嘻嘻
        public string GetMaterBatchList(string MNo, string sFlag)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sComp = string.Empty;
            string sBNo = string.Empty;
            string sTName = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            DataTable dt = MaterBll.GetMaterBatchList(MNo, sComp, sFlag);
            if (dt.Rows.Count > 0)
            {
                sTName = dt.Rows[0]["MaterName"].ToString();

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    sBNo = dt.Rows[i]["MaterBatch"].ToString();

                    Message += "<option value='" + sBNo + "'>" + sBNo + "</option>";
                }
            }
            else
            {
                Message = "Error";
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, NAME = sTName });

            return Result;

        }
        #endregion


        #region 获取出库明细信息
        public string GetPOutMX(string sNo, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sKFlag = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            DataTable dt = MaterBll.GetPOutMX(sNo, "", "", "", sMan, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        // 生成装箱清单，导出EXCEL
        public string CreatePackList(string Params, string PR, string sPRItem)
        {
            string Message = string.Empty;
            string Result = string.Empty;
            string sUserNo = string.Empty;
            string sComp = string.Empty;
            string sPStr = string.Empty;
            string sFlag = string.Empty;
            int j = 0;
            string sReturnFile = "\\ExcelFile\\ PackList " + DateTime.Now.ToString("yyyymmddss") + ".xls";
            string sFile = System.AppDomain.CurrentDomain.BaseDirectory + "\\Template\\PackingList.xls";
            string sExcelFilePath = System.AppDomain.CurrentDomain.BaseDirectory + sReturnFile;
            System.IO.File.Copy(sFile, sExcelFilePath, true);
            Microsoft.Office.Interop.Excel.Application app = new Microsoft.Office.Interop.Excel.Application();
            object missing = Type.Missing;
            Microsoft.Office.Interop.Excel.Workbook workbooks = null;



            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { RNo = String.Empty, Flag = String.Empty };
                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sPStr = Item.RNo;
                sFlag = Item.Flag;
            }
            else
            {

            }

            DataTable dt = MaterBll.CreatePackList(sPStr, sComp,sFlag);

            try
            {   // app.Cells[5 + i, 1] = i + 1;//序号    5,1  :第5行，第 1 列            5,6  第 5 行， 第 6 列
                workbooks = app.Workbooks.Open(sExcelFilePath, missing, missing, missing, "", "", missing, missing, missing, missing, missing, missing, missing, missing, missing);

                if (dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        app.Cells[8 + j, 1] = i+1;// 8,1  :第8行，第 1 列
                        app.Cells[8 + j, 2] = dt.Rows[i]["CustPN"].ToString();
                        app.Cells[8 + j, 4] = dt.Rows[i]["PONO"].ToString();
                        app.Cells[8 + j, 6] = dt.Rows[i]["OrderItemNo"].ToString();
                        app.Cells[8 + j, 8] = dt.Rows[i]["OutNum"].ToString();

                        j = j + 3;
                    }

                }

                app.Visible = true;
                app.ActiveWorkbook.Save();

                Message="Success";
            }
            catch (Exception ex)
            {
                Message="Errors";
            }
            finally
            {
                workbooks.Close(false, missing, missing);
                workbooks = null;
                app.Quit();
                app = null;

                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }


            Result = JsonConvert.SerializeObject(new { sFilePath = ".." + sReturnFile, Msg = Message });
            return Result;
        }



        #region 删除装箱单信息：更新出库信息装箱单编号为空
        public string OPPackList(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;


            var AnonymousUser = new
            {
                OutNo = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            sOKFlag = MaterBll.OPPackList(It.OutNo, sComp,It.Flag);

            if (sOKFlag.Length <= 2)
            {

                Message = "Success";

            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = It.OutNo });

            return Result;
        }
        #endregion



        // 生成出库统计表，导出EXCEL
        public string CreatePDOutList(string Params, string PR, string Flag)
        {
            string Message = string.Empty;
            string Result = string.Empty;
            string sMan = string.Empty;
            string sComp = string.Empty;
            string sPStr = string.Empty;
            string sPNo = string.Empty;
            string sMNo = string.Empty;
            string sMName = string.Empty;
            string sBDate = string.Empty;
            string sEDate = string.Empty;
            string sSt = string.Empty;
            string sA = string.Empty;
            string sB = string.Empty;
            string sC = string.Empty;
            string sD = string.Empty; 
            string sReturnFile = "\\ExcelFile\\ 发货统计报表 " + DateTime.Now.ToString("yyyymmddss") + ".xls";
            string sFile = System.AppDomain.CurrentDomain.BaseDirectory + "\\Template\\发货统计报表.xls";
            string sExcelFilePath = System.AppDomain.CurrentDomain.BaseDirectory + sReturnFile;
            System.IO.File.Copy(sFile, sExcelFilePath, true);
            Microsoft.Office.Interop.Excel.Application app = new Microsoft.Office.Interop.Excel.Application();
            object missing = Type.Missing;
            Microsoft.Office.Interop.Excel.Workbook workbooks = null;



            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sMan });
                return Result;
            }

            var AnonymousUser = new
            {
                PNo = String.Empty, MNo = String.Empty, MName = String.Empty, BDate = String.Empty, EDate = String.Empty, Status = String.Empty,  A = String.Empty,B = String.Empty,C = String.Empty, D = String.Empty,
            };


            if (string.IsNullOrEmpty(Params) || (Params == null) || (Params == "null"))
            {
                sPNo = "";
                sMNo = "";
                sMName = "";
                sBDate = "";
                sSt = "";
                sA = "";
                sB = "";
                sC = "";
                sD = "";
            }
            else
            {
                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sPNo = Item.PNo;
                sMNo = Item.MNo;
                sMName = Item.MName;
                sBDate = Item.BDate;
                sEDate = Item.EDate;
                sSt = Item.Status;
                sA = Item.A;
                sB = Item.B;
                sC = Item.C;
                sD = Item.D;
            }
            if (sBDate == "")
            {
                sBDate = "2021-01-01";
            }
            if (sEDate == "")
            {
                sEDate = "2999-12-30";
            }

            DataTable dt = MaterBll.GetMaterFlowInfo(sPNo, sMNo, sMName, sBDate, sEDate, sSt, sA, sB, sC, sD, 1, 50000, sMan, sComp, Flag);

            try
            {   // app.Cells[5 + i, 1] = i + 1;//序号    5,1  :第5行，第 1 列            5,6  第 5 行， 第 6 列
                workbooks = app.Workbooks.Open(sExcelFilePath, missing, missing, missing, "", "", missing, missing, missing, missing, missing, missing, missing, missing, missing);

                if (dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        app.Cells[2 + i, 1] = dt.Rows[i]["DeliveryDate"].ToString();// 2,1  :第2行，第 1 列
                        app.Cells[2 + i, 2] = dt.Rows[i]["ReceiveDate"].ToString();
                        app.Cells[2 + i, 3] = dt.Rows[i]["YCDate"].ToString();
                        app.Cells[2 + i, 4] = dt.Rows[i]["YCFlag"].ToString();
                        app.Cells[2 + i, 5] = dt.Rows[i]["DDate"].ToString();
                        app.Cells[2 + i, 6] = dt.Rows[i]["YCNum"].ToString();
                        app.Cells[2 + i, 7] = dt.Rows[i]["OutDate"].ToString();
                        app.Cells[2 + i, 8] = dt.Rows[i]["JT"].ToString();
                        app.Cells[2 + i, 9] = dt.Rows[i]["FHYC"].ToString();
                        app.Cells[2 + i, 10] = dt.Rows[i]["ShipTo"].ToString();
                        app.Cells[2 + i, 11] = dt.Rows[i]["CustNo"].ToString();
                        app.Cells[2 + i, 12] = dt.Rows[i]["PONO"].ToString();
                        app.Cells[2 + i, 13] = dt.Rows[i]["CustPN"].ToString();
                        app.Cells[2 + i, 14] = dt.Rows[i]["SupplierNo"].ToString();
                        app.Cells[2 + i, 15] = dt.Rows[i]["SupplierEn"].ToString();
                        app.Cells[2 + i, 16] = dt.Rows[i]["HTNo"].ToString();
                        app.Cells[2 + i, 17] = dt.Rows[i]["OutNum"].ToString();
                        app.Cells[2 + i, 18] = dt.Rows[i]["Price"].ToString();  
                        app.Cells[2 + i, 19] = dt.Rows[i]["Status"].ToString();
                        app.Cells[2 + i, 21] = dt.Rows[i]["Amount"].ToString();
                        

                    }

                }

                app.Visible = true;
                app.ActiveWorkbook.Save();

                Message = "Success";
            }
            catch (Exception ex)
            {
                Message = "Errors";
            }
            finally
            {
                workbooks.Close(false, missing, missing);
                workbooks = null;
                app.Quit();
                app = null;

                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }


            Result = JsonConvert.SerializeObject(new { sFilePath = ".." + sReturnFile, Msg = Message });
            return Result;
        }


















        private double parseFloat()
        {
            throw new NotImplementedException();
        }
        #endregion
























        #region  生成sh单号   R2102190001  
        public static string CreateSHNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_MaterReceive where convert(char(10),InDate,120)='" + sDate + "' ", "ReceiveNo");//   R210219 0001
            if (sMaxNo == "")
            {
                sNo = "R" + CreateAllNo.CreateBillNo(2, 3) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(7, 4);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 4 - len;

                sNo = "R" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion


        #region  生成sh单号   21041700001    
        public static string CreateBatchNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_InspectInfo where convert(char(10),InDate,120)='" + sDate + "' ", "InIQCNo");//   210417 00001
            if (sMaxNo == "")
            {
                sNo = CreateAllNo.CreateBillNo(2, 4) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(6, 5);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 5 - len;

                sNo = CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion


        #region  生成出库单单号   CK2104180001    CreateReturnNo
        public static string CreateOutBillNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_ProductOutInfo where convert(char(10),InDate,120)='" + sDate + "' ", "POutNo");//  CK210418 0001
            if (sMaxNo == "")
            {
                sNo = "CK"+CreateAllNo.CreateBillNo(2, 3) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(8, 4);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 4 - len;

                sNo = "CK" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion


        #region  生成退货单信息   R2104180001     
        public static string CreateReturnNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_ReturnMaterInfo where convert(char(10),InDate,120)='" + sDate + "' ", "ReturnNo");//  R210418 0001
            if (sMaxNo == "")
            {
                sNo = "T" + CreateAllNo.CreateBillNo(2, 3) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(7, 4);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 4 - len;

                sNo = "T" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion


        #region  生成发料编号   F2104230001  CreateInStockNo
        public static string CreateFillNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_MaterFillInfo where convert(char(10),InDate,120)='" + sDate + "' ", "FillNo");//  F210423 0001
            if (sMaxNo == "")
            {
                sNo = "F" + CreateAllNo.CreateBillNo(2, 3) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(7, 4);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 4 - len;

                sNo = "F" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion


        #region  生成发料编号   IS2104230001    
        public static string CreateInStockNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_ProductStockInfo where convert(char(10),InDate,120)='" + sDate + "' ", "InNo");//  IS210423 0001
            if (sMaxNo == "")
            {
                sNo = "IS" + CreateAllNo.CreateBillNo(2, 3) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(8, 4);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 4 - len;

                sNo = "IS" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion


        #region  生成零件报废编号   BF2104230001    
        public static string CreateMaterFaultyNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_MaterFaulty where convert(char(10),InDate,120)='" + sDate + "' ", "BFNo");//  BF210423 0001
            if (sMaxNo == "")
            {
                sNo = "BF" + CreateAllNo.CreateBillNo(2, 3) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(8, 4);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 4 - len;

                sNo = "BF" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion



        #region 生成合同编号  供应商编码-日期-序列号（A24-210108-01）
        public static string CreateHTNo(string sSNo)
        {
            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sStr = DateTime.Now.ToString("yyMMdd");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_PurchaseInfo where convert(char(10),InDate,120)='" + sDate + "' and SupplierNo='" + sSNo + "' ", "HTNo");//  A24-210108-01  PO2103 00001
            if (sMaxNo == "")
            {
                sNo = sSNo + "-" + sStr + "-01";
            }
            else
            {
                string sTemp = sMaxNo.Substring(11, 2);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                if (len == 1)
                {
                    sNo = sSNo + "-" + sStr + "-" + "0" + iMax.ToString();
                }
                else
                {
                    sNo = sSNo + "-" + sStr + "-" + iMax.ToString();
                }
            }
            return sNo;

        }
        #endregion















        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}
