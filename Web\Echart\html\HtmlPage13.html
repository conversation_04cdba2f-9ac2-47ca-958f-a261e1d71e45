﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
</head>
<body>

</body>
</html>
<!DOCTYPE html
          PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>产线看板</title>
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/echarts.js"></script>
    <script>
        $(function () {
            Init();
        });

        // 初始化函数
        function Init() {
            const data = sessionStorage.getItem("KB0004");
            let completedData = []; // 已完成数据
            let incompleteData = []; // 未完成数据
            let LineBodyName = []

            // 校验并解析数据
            if (data) {
                try {
                    const jsonData = JSON.parse(data);
                    completedData = jsonData[0] || [];
                    incompleteData = jsonData[1] || [];
                    LineBodyName = jsonData[2] || [];
                } catch (error) {
                    console.error("数据解析失败:", error);
                }
            }

            if (LineBodyName.length > 0) {
                $(".div_any_title").each(function (index, item) {
                    $(this).html(LineBodyName[0].FWName + item.innerHTML)
                })
            }

            // 渲染已完成和未完成的图表
            RenderChart(completedData, 'ChartOne');
            RenderChart(completedData, 'ChartTow', true); // 统计图
            RenderChart(incompleteData, 'ChartThree');
            RenderChart(incompleteData, 'ChartFour', true); // 统计图
        }

        /**
         * 通用图表渲染函数
         * @param {Array} data - 图表数据
         * @param {string} chartId - 图表容器的ID
         * @param {boolean} isTotal - 是否显示总计（默认为false）
         */
        function RenderChart(data, chartId, isTotal = false) {
            if (!data || data.length === 0) {
                console.warn(`没有数据提供给图表 ${chartId}`);
                return;
            }

            let Unit = [...new Set(data.map(item => item.WName))]; // 获取所有单元编号
            let order = [...new Set(data.map(item => item.OrderNo))]; // 获取所有订单编号
            let serie = [];



            // 如果需要显示总计
            if (isTotal) {
                const dataTotal = Unit.map((itemUnitNo) =>
                    data.filter(item => item.WName === itemUnitNo).reduce((sum, item) => sum + item.Num, 0)
                );
                serie.push({
                    name: '总计',
                    type: 'bar',
                    data: dataTotal,
                    label: {
                        show: true,
                        color: 'white',
                        fontWeight: "bold",
                        position: 'top',
                        formatter: (params) => params.value === 0 ? '' : params.value
                    },
                });
                order = ["总计"];
            } else {
                // 处理每个订单的数据
                order.forEach((itemOrder) => {
                    let ObjData = [];
                    Unit.forEach((itemUnit) => {
                        const filteredData = data.filter(f => f.OrderNo === itemOrder && f.WName === itemUnit);
                        ObjData.push(filteredData.length > 0 ? filteredData[0].Num : 0);
                    });

                    serie.push({
                        name: itemOrder,
                        type: 'bar',
                        stack: 'total',
                        label: {
                            show: true,
                            color: 'white',
                            fontWeight: "bold",
                            formatter: (params) => params.value === 0 ? '' : params.value
                        },
                        emphasis: { focus: 'series' },
                        data: ObjData
                    });
                });
            }

            // 初始化图表
            const chart = echarts.init(document.getElementById(chartId));
            chart.setOption({
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "shadow",
                    },
                },
                grid: {
                    left: "2%",
                    right: "2%",
                    bottom: "2%",
                    top: "15%",
                    containLabel: true,
                },
                legend: {
                    data: order,
                    right: 10,
                    top: 12,
                    textStyle: {
                        color: "#fff",
                        fontWeight: "bold"
                    },
                    itemWidth: 12,
                    itemHeight: 10,
                },
                xAxis: {
                    type: "category",
                    data: Unit,
                    axisLine: {
                        lineStyle: {
                            color: "white",
                        },
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#fff',
                            fontWeight: "bold"
                        },
                        rotate: 40
                    },
                    axisTick: {
                        show: true
                    }
                },
                yAxis: {
                    type: "value",
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: "rgba(255,255,255,0.3)",
                        },
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#fff',
                            fontWeight: "bold"
                        }
                    },
                    axisLine: {
                        show: false,
                    }
                },
                dataZoom: [
                    {
                        type: "inside",
                    },
                ],
                series: serie
            });

            // 窗口调整时重新渲染图表
            $(window).resize(() => chart.resize());
        }

    </script>

    <style>
        * {
            margin: 0;
            padding: 0;
            list-style: none;
            text-decoration: none;
        }

        .container-div {
            height: 100vh;
            background: #15181d;
            overflow: hidden;
            position: relative;
            color: #ffffff;
        }

        .container-top {
            height: 47.5%;
            display: flex;
        }

        .container-bottom {
            margin-top: 1.6%;
            height: 47.5%;
            display: flex;
        }


        .container-top-left, .container-bottom-left {
            width: 49.5%;
            height: 100%;
        }

        .container-top-right, .container-bottom-right {
            margin-left: 1%;
            width: 49.5%;
            height: 100%;
        }

        .left {
            float: left;
        }

        .div_any01 {
            width: 23%;
            margin-right: 2%;
        }

        .div_any_child {
            width: 100%;
            height: 330px;
            box-shadow: -5px 0px 10px #034c6a inset, 0px -10px 10px #034c6a inset, 5px 0px 10px #034c6a inset, 0px 10px 10px #034c6a inset;
            border: 1px solid #034c6a;
            box-sizing: border-box;
            position: relative;
            margin-top: 15px;
        }

        .div_any_title {
            background-color: #034c6a;
            border-radius: 18px;
            position: absolute;
            height: 25px;
            width: 60%;
            top: -15px;
            color: #ffffff;
            font-weight: bold;
            font-size: 16px;
            left: 20%;
            line-height: 25px;
            text-align: center;
        }

        .div_any_body {
            padding: 1% 0 1% 1%;
            width: 99%;
            height: 100%;
        }
    </style>
</head>


<body>
    <div class="container-div">
        <div class="container-top">
            <div class="container-top-left">
                <div class="left div_any01" style="width:100%;height:100%">
                    <div class="div_any_child" style="height: 100%;">
                        <div class="div_any_title">线体当天各工位已完成情况</div>
                        <div id="ChartOne" style="width:100%;height:100%"></div>
                    </div>
                </div>
            </div>
            <div class="container-top-right">
                <div class="left div_any01" style="width:100%;height:100%">
                    <div class="div_any_child" style="height: 100%;">
                        <div class="div_any_title">线体当天各工位生产中情况</div>
                        <div id="ChartThree" style="width:100%;height:100%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-bottom">
            <div class="container-bottom-left">
                <div class="left div_any01" style="width:100%;height:100%">
                    <div class="div_any_child" style="height: 100%;">
                        <div class="div_any_title">线体当天各工位已完成汇总</div>
                        <div id="ChartTow" style="width:100%;height:100%"></div>

                    </div>
                </div>
            </div>
            <div class="container-bottom-right">
                <div class="left div_any01" style="width:100%;height:100%">
                    <div class="div_any_child" style="height: 100%;">
                        <div class="div_any_title">线体当天各工位生产中汇总</div>
                        <div id="ChartFour" style="width:100%;height:100%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>