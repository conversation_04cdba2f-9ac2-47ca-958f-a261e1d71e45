﻿using System;
using System.Data;
using System.Data.SqlClient;
using Common;
using Model;
using System.Collections.Generic; // Added for List

namespace DAL
{
    /// <summary>
    /// 客户套餐管理数据访问层
    /// </summary>
    public class CompanyComboDal
    {
        /// <summary>
        /// 获取客户套餐信息列表
        /// </summary>
        public DataTable GetCustMenuInfo(string applyNo, string custNo, string custName, string status, int rows, int page, string loginUser, string companyNo, string menuNo = "", string nowVer = "")
        {
            string sql = @"
                WITH TempTable AS (
                    SELECT ROW_NUMBER() OVER(ORDER BY InDate DESC) AS RowNum, 
                           Id, ApplyNo, CustNo, CustName, AccountType, 
                           CONVERT(varchar(10), SettlementStartTime, 120) as SettlementStartTime,
                           CONVERT(varchar(10), SettlementEndTime, 120) as SettlementEndTime,
                           RegType, Code, 
                           CONVERT(varchar(10), EffectiveDate, 120) as EffectiveDate,
                           CONVERT(varchar(10), ExpiringDate, 120) as ExpiringDate,
                           SettlementCycle, MenuNo, MenuName, BasePrice, BUP, TVA, 
                           AUserPrice, AWOPrice, DFunction, SLA, DepthTrain, 
                           IMServices, CustomDev, InterfaceDev, OnsiteSV, 
                           CHMan, CONVERT(varchar(19), CHDate, 120) as CHDate,
                           NowVer, Status, CompanyNo, InMan,
                           CONVERT(varchar(19), InDate, 120) as InDate,
                           Remark,
                           (SELECT COUNT(1) FROM T_CustMenuInfo 
                            WHERE 1=1 
                            AND (@ApplyNo = '' OR ApplyNo LIKE '%' + @ApplyNo + '%')
                            AND (@CustNo = '' OR CustNo LIKE '%' + @CustNo + '%')
                            AND (@CustName = '' OR CustName LIKE '%' + @CustName + '%')
                            AND (@Status = '' OR Status = @Status)
                            AND (@MenuNo = '' OR MenuNo = @MenuNo)
                            AND (@NowVer = '' OR NowVer = CAST(@NowVer AS BIT))
                            AND CompanyNo = @CompanyNo) AS NumCount
                    FROM T_CustMenuInfo
                    WHERE 1=1 
                    AND (@ApplyNo = '' OR ApplyNo LIKE '%' + @ApplyNo + '%')
                    AND (@CustNo = '' OR CustNo LIKE '%' + @CustNo + '%')
                    AND (@CustName = '' OR CustName LIKE '%' + @CustName + '%')
                    AND (@Status = '' OR Status = @Status)
                    AND (@MenuNo = '' OR MenuNo = @MenuNo)
                    AND (@NowVer = '' OR NowVer = CAST(@NowVer AS BIT))
                    AND CompanyNo = @CompanyNo
                )
                SELECT * FROM TempTable 
                WHERE RowNum BETWEEN (@Page - 1) * @Rows + 1 AND @Page * @Rows
                ORDER BY InDate DESC";

            SqlParameter[] parameters = {
                new SqlParameter("@ApplyNo", SqlDbType.NVarChar) { Value = applyNo ?? "" },
                new SqlParameter("@CustNo", SqlDbType.NVarChar) { Value = custNo ?? "" },
                new SqlParameter("@CustName", SqlDbType.NVarChar) { Value = custName ?? "" },
                new SqlParameter("@Status", SqlDbType.NVarChar) { Value = status ?? "" },
                new SqlParameter("@MenuNo", SqlDbType.NVarChar) { Value = menuNo ?? "" },
                new SqlParameter("@NowVer", SqlDbType.NVarChar) { Value = nowVer ?? "" },
                new SqlParameter("@CompanyNo", SqlDbType.NVarChar) { Value = companyNo },
                new SqlParameter("@Rows", SqlDbType.Int) { Value = rows },
                new SqlParameter("@Page", SqlDbType.Int) { Value = page }
            };

            return DBHelper.GetDataTable(sql, CommandType.Text, parameters);
        }

        /// <summary>
        /// 生成申请编号
        /// </summary>
        public string CreateApplyNo()
        {
            string sDate = DateTime.Now.ToString("yyMMdd");
            string sNo = string.Empty;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_CustMenuInfo where SUBSTRING(ApplyNo, 2, 6) = '" + sDate + "'", "ApplyNo");
            if (string.IsNullOrEmpty(sMaxNo))
            {
                sNo = "A" + sDate + "0001";
            }
            else
            {
                string sTemp = sMaxNo.Substring(7, 4);
                iMax = int.Parse(sTemp) + 1;
                sNo = "A" + sDate + iMax.ToString().PadLeft(4, '0');
            }
            return sNo;
        }

        /// <summary>
        /// 添加客户套餐信息
        /// </summary>
        public string AddCustMenuInfo(CustMenuInfo menu)
        {
            try
            {
                var sqlParamsList = new List<KeyValuePair<string, SqlParameter[]>>();

                // 0. 获取该客户当前在用的套餐信息
                string getCurrentSql = @"
                    SELECT TOP 1 MenuNo, Remark 
                    FROM T_CustMenuInfo 
                    WHERE CustNo = @CustNo 
                    AND CompanyNo = @CompanyNo 
                    AND NowVer = 1";

                SqlParameter[] getCurrentParams = {
                    new SqlParameter("@CustNo", SqlDbType.NVarChar) { Value = menu.CustNo },
                    new SqlParameter("@CompanyNo", SqlDbType.NVarChar) { Value = menu.CompanyNo }
                };

                DataTable dtCurrent = DBHelper.GetDataTable(getCurrentSql, CommandType.Text, getCurrentParams);
                
                string versionChangeRemark = "";
                if (dtCurrent != null && dtCurrent.Rows.Count > 0)
                {
                    string oldMenuNo = dtCurrent.Rows[0]["MenuNo"].ToString();
                    string oldRemark = dtCurrent.Rows[0]["Remark"].ToString();

                    // 获取新旧套餐等级
                    var pricingDal = new ProductPricingDal();
                    int oldLevel = pricingDal.GetMenuLevel(oldMenuNo, menu.CompanyNo);
                    int newLevel = pricingDal.GetMenuLevel(menu.MenuNo, menu.CompanyNo);

                    // 判断是升版还是降版
                    string changeType = newLevel > oldLevel ? "升版" : "降版";
                    versionChangeRemark = oldRemark + $"{DateTime.Now.ToString("yyMMdd")}:{menu.InMan} {changeType}";

                    // 1. 将该客户之前的套餐"在用版本"设置为否，并更新备注
                    string updateSql = @"
                        UPDATE T_CustMenuInfo SET
                            NowVer = 0,
                            CHMan = @CHMan,
                            CHDate = @CHDate,
                            Remark = @Remark
                        WHERE CustNo = @CustNo 
                        AND CompanyNo = @CompanyNo
                        AND NowVer = 1";

                    SqlParameter[] updateParams = {
                        new SqlParameter("@CustNo", SqlDbType.NVarChar) { Value = menu.CustNo },
                        new SqlParameter("@CompanyNo", SqlDbType.NVarChar) { Value = menu.CompanyNo },
                        new SqlParameter("@CHMan", SqlDbType.NVarChar) { Value = menu.InMan },
                        new SqlParameter("@CHDate", SqlDbType.DateTime) { Value = menu.InDate },
                        new SqlParameter("@Remark", SqlDbType.NVarChar) { Value = versionChangeRemark }
                    };

                    sqlParamsList.Add(new KeyValuePair<string, SqlParameter[]>(updateSql, updateParams));
                }

                // 2. 插入新的套餐信息
                string insertSql = @"
                    INSERT INTO T_CustMenuInfo (
                        Id, ApplyNo, CustNo, CustName, AccountType, 
                        SettlementStartTime, SettlementEndTime, RegType, Code, 
                        EffectiveDate, ExpiringDate, SettlementCycle, MenuNo, MenuName, 
                        BasePrice, BUP, TVA, AUserPrice, AWOPrice, DFunction, 
                        SLA, DepthTrain, IMServices, CustomDev, InterfaceDev, 
                        OnsiteSV, NowVer, Status, CompanyNo, InMan, InDate, Remark
                    ) VALUES (
                        @Id, @ApplyNo, @CustNo, @CustName, @AccountType, 
                        @SettlementStartTime, @SettlementEndTime, @RegType, @Code, 
                        @EffectiveDate, @ExpiringDate, @SettlementCycle, @MenuNo, @MenuName, 
                        @BasePrice, @BUP, @TVA, @AUserPrice, @AWOPrice, @DFunction, 
                        @SLA, @DepthTrain, @IMServices, @CustomDev, @InterfaceDev, 
                        @OnsiteSV, @NowVer, @Status, @CompanyNo, @InMan, @InDate, @Remark
                    )";

                sqlParamsList.Add(new KeyValuePair<string, SqlParameter[]>(insertSql, CreateParameters(menu)));

                // 执行事务
                string result = DBHelper.ExecuteSqlTranWithParams(sqlParamsList);
                if (result.StartsWith("Err:"))
                {
                    return "添加失败：" + result.Substring(4);
                }
                return "";
            }
            catch (Exception ex)
            {
                return "添加失败：" + ex.Message;
            }
        }

        /// <summary>
        /// 添加客户套餐信息（带事务）
        /// </summary>
        public string AddCustMenuInfoWithTransaction(CustMenuInfo menu, List<KeyValuePair<string, SqlParameter[]>> additionalSqlParams)
        {
            try
            {
                var sqlParamsList = new List<KeyValuePair<string, SqlParameter[]>>(additionalSqlParams);

                // 将套餐信息插入语句添加到事务中
                string insertSql = @"
                    INSERT INTO T_CustMenuInfo (
                        Id, ApplyNo, CustNo, CustName, AccountType, 
                        SettlementStartTime, SettlementEndTime, RegType, Code, 
                        EffectiveDate, ExpiringDate, SettlementCycle, MenuNo, MenuName, 
                        BasePrice, BUP, TVA, AUserPrice, AWOPrice, DFunction, 
                        SLA, DepthTrain, IMServices, CustomDev, InterfaceDev, 
                        OnsiteSV, NowVer, Status, CompanyNo, InMan, InDate, Remark
                    ) VALUES (
                        @Id, @ApplyNo, @CustNo, @CustName, @AccountType, 
                        @SettlementStartTime, @SettlementEndTime, @RegType, @Code, 
                        @EffectiveDate, @ExpiringDate, @SettlementCycle, @MenuNo, @MenuName, 
                        @BasePrice, @BUP, @TVA, @AUserPrice, @AWOPrice, @DFunction, 
                        @SLA, @DepthTrain, @IMServices, @CustomDev, @InterfaceDev, 
                        @OnsiteSV, @NowVer, @Status, @CompanyNo, @InMan, @InDate, @Remark
                    )";

                sqlParamsList.Add(new KeyValuePair<string, SqlParameter[]>(insertSql, CreateParameters(menu)));

                // 执行事务
                string result = DBHelper.ExecuteSqlTranWithParams(sqlParamsList);
                if (result.StartsWith("Err:"))
                {
                    return "添加失败：" + result.Substring(4);
                }
                return "";
            }
            catch (Exception ex)
            {
                return "添加失败：" + ex.Message;
            }
        }

        /// <summary>
        /// 更新客户套餐信息
        /// </summary>
        public string UpdateCustMenuInfo(CustMenuInfo menu)
        {
            string sql = @"
                UPDATE T_CustMenuInfo SET
                    CustNo = @CustNo,
                    CustName = @CustName,
                    AccountType = @AccountType,
                    SettlementStartTime = @SettlementStartTime,
                    SettlementEndTime = @SettlementEndTime,
                    RegType = @RegType,
                    Code = @Code,
                    EffectiveDate = @EffectiveDate,
                    ExpiringDate = @ExpiringDate,
                    SettlementCycle = @SettlementCycle,
                    MenuNo = @MenuNo,
                    MenuName = @MenuName,
                    BasePrice = @BasePrice,
                    BUP = @BUP,
                    TVA = @TVA,
                    AUserPrice = @AUserPrice,
                    AWOPrice = @AWOPrice,
                    DFunction = @DFunction,
                    SLA = @SLA,
                    DepthTrain = @DepthTrain,
                    IMServices = @IMServices,
                    CustomDev = @CustomDev,
                    InterfaceDev = @InterfaceDev,
                    OnsiteSV = @OnsiteSV,
                    Status = @Status,
                    CHMan = @InMan,
                    CHDate = @InDate,
                    Remark = @Remark
                WHERE ApplyNo = @ApplyNo
                AND CompanyNo = @CompanyNo";

            try
            {
                int result = DBHelper.ExecuteCommand(sql, CreateParameters(menu));
                return result > 0 ? "" : "更新失败";
            }
            catch (Exception ex)
            {
                return "更新失败：" + ex.Message;
            }
        }

        /// <summary>
        /// 更新客户套餐状态
        /// </summary>
        public string UpdateCustMenuStatus(string applyNo, string status, string companyNo, string loginUser)
        {
            string sql = @"
                UPDATE T_CustMenuInfo SET
                    Status = @Status,
                    CHMan = @CHMan,
                    CHDate = @CHDate
                WHERE ApplyNo = @ApplyNo
                AND CompanyNo = @CompanyNo";

            try
            {
                SqlParameter[] parameters = {
                    new SqlParameter("@ApplyNo", SqlDbType.NVarChar) { Value = applyNo },
                    new SqlParameter("@Status", SqlDbType.NVarChar) { Value = status },
                    new SqlParameter("@CompanyNo", SqlDbType.NVarChar) { Value = companyNo },
                    new SqlParameter("@CHMan", SqlDbType.NVarChar) { Value = loginUser },
                    new SqlParameter("@CHDate", SqlDbType.DateTime) { Value = DateTime.Now }
                };

                int result = DBHelper.ExecuteCommand(sql, parameters);
                return result > 0 ? "" : "更新状态失败";
            }
            catch (Exception ex)
            {
                return "更新状态失败：" + ex.Message;
            }
        }

        /// <summary>
        /// 删除客户套餐信息
        /// </summary>
        public string DeleteCustMenuInfo(string applyNo, string companyNo, string loginUser)
        {
            string sql = @"
                DELETE FROM T_CustMenuInfo 
                WHERE ApplyNo = @ApplyNo 
                AND CompanyNo = @CompanyNo";

            try
            {
                SqlParameter[] parameters = {
                    new SqlParameter("@ApplyNo", SqlDbType.NVarChar) { Value = applyNo },
                    new SqlParameter("@CompanyNo", SqlDbType.NVarChar) { Value = companyNo }
                };

                int result = DBHelper.ExecuteCommand(sql, parameters);
                return result > 0 ? "" : "删除失败";
            }
            catch (Exception ex)
            {
                return "删除失败：" + ex.Message;
            }
        }

        /// <summary>
        /// 检查客户套餐是否已存在
        /// </summary>
        public bool CheckCustMenuExists(string custNo, string menuNo, string excludeApplyNo, string companyNo)
        {
            string sql = @"
                SELECT TOP 1 1 
                FROM T_CustMenuInfo WITH(NOLOCK)
                WHERE CustNo = @CustNo 
                AND MenuNo = @MenuNo 
                AND CompanyNo = @CompanyNo
                AND (@ExcludeApplyNo = '' OR ApplyNo != @ExcludeApplyNo)";

            SqlParameter[] parameters = {
                new SqlParameter("@CustNo", SqlDbType.NVarChar) { Value = custNo },
                new SqlParameter("@MenuNo", SqlDbType.NVarChar) { Value = menuNo },
                new SqlParameter("@CompanyNo", SqlDbType.NVarChar) { Value = companyNo },
                new SqlParameter("@ExcludeApplyNo", SqlDbType.NVarChar) { Value = excludeApplyNo ?? "" }
            };

            DataTable dt = DBHelper.GetDataTable(sql, CommandType.Text, parameters);
            return dt != null && dt.Rows.Count > 0;
        }

        /// <summary>
        /// 续费客户套餐信息
        /// </summary>
        public string RenewCustMenuInfo(string id, string applyNo, string custNo, string custName, string accountType, 
            DateTime settlementStartTime, DateTime settlementEndTime, string regType, string code, 
            DateTime effectiveDate, DateTime expiringDate, int settlementCycle, 
            string menuNo, string menuName, decimal basePrice, int bup, int tva, 
            decimal aUserPrice, string awoPrice, string dFunction, 
            bool sla, bool depthTrain, bool imServices, bool customDev, bool interfaceDev, bool onsiteSV, 
            bool nowVer, string status, string companyNo, string inMan, DateTime inDate, string remark,
            string originalApplyNo)
        {
            try
            {
                // 获取原记录以更新在用状态
                DataTable dt = GetCustMenuInfo(originalApplyNo, "", "", "", 1, 1, inMan, companyNo);
                if (dt == null || dt.Rows.Count == 0)
                {
                    return "找不到原申请记录";
                }

                var sqlParamsList = new List<KeyValuePair<string, SqlParameter[]>>();

                // 1. 将原记录的在用版本设置为否
                string updateOriginalSql = @"
                    UPDATE T_CustMenuInfo SET
                        NowVer = 0,
                        CHMan = @CHMan,
                        CHDate = @CHDate,
                        Remark = @Remark
                    WHERE ApplyNo = @ApplyNo 
                    AND CompanyNo = @CompanyNo";

                // 生成原记录的备注
                string originalRemark = dt.Rows[0]["Remark"].ToString();
                string newRemark = string.IsNullOrEmpty(originalRemark) 
                    ? $"{DateTime.Now.ToString("yyMMdd")}:{inMan} 续费" 
                    : originalRemark + $" | {DateTime.Now.ToString("yyMMdd")}:{inMan} 续费";

                SqlParameter[] updateParams = {
                    new SqlParameter("@ApplyNo", SqlDbType.NVarChar) { Value = originalApplyNo },
                    new SqlParameter("@CompanyNo", SqlDbType.NVarChar) { Value = companyNo },
                    new SqlParameter("@CHMan", SqlDbType.NVarChar) { Value = inMan },
                    new SqlParameter("@CHDate", SqlDbType.DateTime) { Value = inDate },
                    new SqlParameter("@Remark", SqlDbType.NVarChar) { Value = newRemark }
                };

                sqlParamsList.Add(new KeyValuePair<string, SqlParameter[]>(updateOriginalSql, updateParams));

                // 2. 创建套餐信息对象
                CustMenuInfo menu = new CustMenuInfo
                {
                    Id = id,
                    ApplyNo = applyNo,
                    CustNo = custNo,
                    CustName = custName,
                    AccountType = accountType,
                    SettlementStartTime = settlementStartTime,
                    SettlementEndTime = settlementEndTime,
                    RegType = regType,
                    Code = code,
                    EffectiveDate = effectiveDate,
                    ExpiringDate = expiringDate,
                    SettlementCycle = settlementCycle,
                    MenuNo = menuNo,
                    MenuName = menuName,
                    BasePrice = basePrice,
                    BUP = bup,
                    TVA = tva,
                    AUserPrice = aUserPrice,
                    AWOPrice = awoPrice,
                    DFunction = dFunction,
                    SLA = sla,
                    DepthTrain = depthTrain,
                    IMServices = imServices,
                    CustomDev = customDev,
                    InterfaceDev = interfaceDev,
                    OnsiteSV = onsiteSV,
                    NowVer = nowVer,
                    Status = status,
                    CompanyNo = companyNo,
                    InMan = inMan,
                    InDate = inDate,
                    Remark = remark
                };

                // 3. 将套餐信息插入语句添加到事务中
                string insertSql = @"
                    INSERT INTO T_CustMenuInfo (
                        Id, ApplyNo, CustNo, CustName, AccountType, 
                        SettlementStartTime, SettlementEndTime, RegType, Code, 
                        EffectiveDate, ExpiringDate, SettlementCycle, MenuNo, MenuName, 
                        BasePrice, BUP, TVA, AUserPrice, AWOPrice, DFunction, 
                        SLA, DepthTrain, IMServices, CustomDev, InterfaceDev, 
                        OnsiteSV, NowVer, Status, CompanyNo, InMan, InDate, Remark
                    ) VALUES (
                        @Id, @ApplyNo, @CustNo, @CustName, @AccountType, 
                        @SettlementStartTime, @SettlementEndTime, @RegType, @Code, 
                        @EffectiveDate, @ExpiringDate, @SettlementCycle, @MenuNo, @MenuName, 
                        @BasePrice, @BUP, @TVA, @AUserPrice, @AWOPrice, @DFunction, 
                        @SLA, @DepthTrain, @IMServices, @CustomDev, @InterfaceDev, 
                        @OnsiteSV, @NowVer, @Status, @CompanyNo, @InMan, @InDate, @Remark
                    )";

                sqlParamsList.Add(new KeyValuePair<string, SqlParameter[]>(insertSql, CreateParameters(menu)));

                // 4. 执行事务
                string result = DBHelper.ExecuteSqlTranWithParams(sqlParamsList);
                if (result.StartsWith("Err:"))
                {
                    return "续费失败：" + result.Substring(4);
                }
                return "";
            }
            catch (Exception ex)
            {
                return "续费失败：" + ex.Message;
            }
        }

        /// <summary>
        /// 创建SQL参数数组
        /// </summary>
        private SqlParameter[] CreateParameters(CustMenuInfo menu)
        {
            return new SqlParameter[] {
                new SqlParameter("@Id", SqlDbType.NVarChar) { Value = (object)menu.Id ?? DBNull.Value },
                new SqlParameter("@ApplyNo", SqlDbType.NVarChar) { Value = menu.ApplyNo },
                new SqlParameter("@CustNo", SqlDbType.NVarChar) { Value = menu.CustNo },
                new SqlParameter("@CustName", SqlDbType.NVarChar) { Value = menu.CustName },
                new SqlParameter("@AccountType", SqlDbType.NVarChar) { Value = (object)menu.AccountType ?? DBNull.Value },
                new SqlParameter("@SettlementStartTime", SqlDbType.DateTime) { Value = menu.SettlementStartTime.Year > 1900 ? (object)menu.SettlementStartTime : DBNull.Value },
                new SqlParameter("@SettlementEndTime", SqlDbType.DateTime) { Value = menu.SettlementEndTime.Year > 1900 ? (object)menu.SettlementEndTime : DBNull.Value },
                new SqlParameter("@RegType", SqlDbType.NVarChar) { Value = (object)menu.RegType ?? DBNull.Value },
                new SqlParameter("@Code", SqlDbType.NVarChar) { Value = (object)menu.Code ?? DBNull.Value },
                new SqlParameter("@EffectiveDate", SqlDbType.DateTime) { Value = menu.EffectiveDate.Year > 1900 ? (object)menu.EffectiveDate : DBNull.Value },
                new SqlParameter("@ExpiringDate", SqlDbType.DateTime) { Value = menu.ExpiringDate.Year > 1900 ? (object)menu.ExpiringDate : DBNull.Value },
                new SqlParameter("@SettlementCycle", SqlDbType.Int) { Value = menu.SettlementCycle },
                new SqlParameter("@MenuNo", SqlDbType.NVarChar) { Value = (object)menu.MenuNo ?? DBNull.Value },
                new SqlParameter("@MenuName", SqlDbType.NVarChar) { Value = (object)menu.MenuName ?? DBNull.Value },
                new SqlParameter("@BasePrice", SqlDbType.Decimal) { Value = menu.BasePrice },
                new SqlParameter("@BUP", SqlDbType.Int) { Value = menu.BUP },
                new SqlParameter("@TVA", SqlDbType.Int) { Value = menu.TVA },
                new SqlParameter("@AUserPrice", SqlDbType.Decimal) { Value = menu.AUserPrice },
                new SqlParameter("@AWOPrice", SqlDbType.NVarChar) { Value = (object)menu.AWOPrice ?? DBNull.Value },
                new SqlParameter("@DFunction", SqlDbType.NVarChar) { Value = (object)menu.DFunction ?? DBNull.Value },
                new SqlParameter("@SLA", SqlDbType.Bit) { Value = menu.SLA },
                new SqlParameter("@DepthTrain", SqlDbType.Bit) { Value = menu.DepthTrain },
                new SqlParameter("@IMServices", SqlDbType.Bit) { Value = menu.IMServices },
                new SqlParameter("@CustomDev", SqlDbType.Bit) { Value = menu.CustomDev },
                new SqlParameter("@InterfaceDev", SqlDbType.Bit) { Value = menu.InterfaceDev },
                new SqlParameter("@OnsiteSV", SqlDbType.Bit) { Value = menu.OnsiteSV },
                new SqlParameter("@NowVer", SqlDbType.Bit) { Value = menu.NowVer },
                new SqlParameter("@Status", SqlDbType.NVarChar) { Value = menu.Status },
                new SqlParameter("@CompanyNo", SqlDbType.NVarChar) { Value = menu.CompanyNo },
                new SqlParameter("@InMan", SqlDbType.NVarChar) { Value = menu.InMan },
                new SqlParameter("@InDate", SqlDbType.DateTime) { Value = menu.InDate },
                new SqlParameter("@Remark", SqlDbType.NVarChar) { Value = (object)menu.Remark ?? DBNull.Value }
            };
        }
    }
}
