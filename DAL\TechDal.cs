﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using Common;
using System.Text;
using System.Security.Policy;

namespace DAL
{
    public class TechDal
    {
        // 判断信息是否存在
        public static string JudgeObjectExist(string Kind, string KindList, string QT, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string sStatus = string.Empty;

            if (sFlag == "1") // 判断工序编号或名称是否存在
            {
                sSQL = " select InMan from T_WorkProcedure where CompanyNo= '" + sComp + "' and (ProcedureNo = '" + Kind + "' or ProcedureName = '" + KindList + "') ";
            }
            else if (sFlag == "3")  // 删除工序，判断是否已用于产品流程
            {
                sSQL = " select InMan from T_ProductFlow where ProcedureNo = '" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "3-1")  // 删除工序，判断是否已用于BOM
            {
                sSQL = " select InMan from T_BOMInfo where ProcedureNo = '" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "3-2")  // 删除工序，判断是否已用于产品对应标贴
            {
                sSQL = " select InMan from T_ProductAndLabel where ProcedureNo = '" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "4")  // 添加产品流程对应的工序，判断工序是否已存在
            {
                sSQL = " select InMan from T_ProductFlow where ProductNo='" + KindList + "'  and ProcedureNo = '" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "4-1")  // 判断该工艺代号是否已设置标准产品流程，如是，不给再设置
            {
                if (KindList.Substring(0, 4) == "1201")
                {  // 主机
                    sSQL = " select InMan from T_ProductFlow where TechNo='" + Kind + "' and ClassFlow='是' and ProductNo like '1201%' and CompanyNo= '" + sComp + "' and ProductNo<>'" + KindList + "' ";
                }
                else
                {  // 整机
                    sSQL = " select InMan from T_ProductFlow where TechNo='" + Kind + "' and ClassFlow='是' and CompanyNo= '" + sComp + "' and (ProductNo like '11%' or ProductNo like '%-CTO-%') and ProductNo<>'" + KindList + "'  ";
                }
            }
            else if (sFlag == "9")  // 判断产品流程是否被工序引用，如是，不能删除该产品流程
            {
                sSQL = " select InMan from T_OrderBaseFlow where  ProductNo='" + Kind + "' ";
            }
            else if (sFlag == "9-0")  // 产品工艺流程，判断工序是否禁用了
            {
                sSQL = " select InMan from T_FlowProcVer where No='" + Kind + "' and ProcedureNo+ProcedureVer='" + KindList + "' and Status='禁用' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "9-1")  // 判断新的工序版本是否存在，如果不存在，则不给拷贝
            {
                sSQL = " select InMan from T_ProductFlow where ProductNo='" + Kind + "' and ProcedureNo+ProcedureVer='" + KindList + "' and VerStatus<>'禁用' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "9-2")  // 产品工艺流程，判断添加的工艺文件是否重复
            {
                sSQL = " select InMan from T_FlowFile where ProductNo='" + Kind + "' and ProcedureNo+ProcedureVer='" + KindList + "' and FileNo+FileVer='" + QT + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "9-3")  // 产品工艺流程，判断添加的测试项目是否重复
            {
                sSQL = " select InMan from T_FlowTestItem where ProductNo='" + Kind + "' and ProcedureNo+ProcedureVer='" + KindList + "' and NameCH+DescCH='" + QT + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "10")  // 判断工作中心是否存在
            {
                sSQL = " select InMan from T_WorkCenter where FWNo='" + Kind + "' and WNo='" + KindList + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "12")  // 判断现在删除的工作中心下面还有没有其他内容，有不能删除
            {
                sSQL = " select TOP 1 InMan from T_WorkCenter where FWNo='" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "16")  // 判断工序行为是否存在--名称
            {
                sSQL = " select InMan from T_ProcedureAction where PAName='" + Kind + "'  ";
            }
            else if (sFlag == "18")  // 删除工序行为是，判断是否被引用了
            {
                sSQL = " select InMan from T_ProductFlow where ConditionNo='" + Kind + "'  ";
            }
            else if (sFlag == "22")  // 如果员工对应的作业单元已有生产记录，不能删除
            {
                sSQL = " select InMan from T_ProductFlow where ConditionNo='" + Kind + "'  ";
            }
            else if (sFlag == "25")  // 判断新增的异常代码是否存在
            {
                sSQL = " select InMan from T_ExceptionCode where ECode='" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "28-1")  // 测试项库，判断添加的测试项目是否重复
            {
                sSQL = " select InMan from T_TestItemBaseInfo where SpecNo<>'" + Kind + "' and TechNo+ProcedureNo='" + KindList + "' and NameCH+DescCH='" + QT + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "29")// 产品流程 判断典型流程是否被禁用
            {
                sSQL = " select InMan from T_ProductFlow where TechNo='" + Kind + "' and ClassFlow='是' and Status='禁用' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "29-1")// 产品流程 判断典型流程当前使用版本是否被禁用
            {
                sSQL = " select a.InMan from T_ProductFlow a join T_FlowProcVer b on a.ProductNo=b.No and a.ProcedureNo=b.ProcedureNo and a.ProcedureVer=b.ProcedureVer " +
                    " where TechNo='" + Kind + "' and ClassFlow='是' and b.Status='禁用' and a.CompanyNo= '" + sComp + "'";
            }
            else if (sFlag == "30-1")// 产品流程 判断当前工序的所有版本是否设置的抽样测试项
            {
                sSQL = "select case when COUNT(DISTINCT ProcedureVer) = COUNT(DISTINCT CASE WHEN TestInspectFlag = '是' THEN ProcedureVer END) then 'true' else 'false' end as result " +
                    "from T_FlowTestItem where ProductNo = '" + Kind + "' AND ProcedureNo = '" + KindList + "' AND CompanyNo='" + sComp + "'";
                var sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    return sdt.Rows[0]["result"].ToString() == "true" ? "Y" : "N";
                }
                return "N";
            }
            else if (sFlag == "30-2")// 产品流程 判断当前工序的工序行为有没有选择 送检抽检合并 
            {
                sSQL = " select InMan from  T_ProcedureAction where ProcAct='送检抽检合并' and PANo = '" + Kind + "' and CompanyNo = '" + sComp + "' ";
            }
            else if (sFlag == "30-3")// 产品流程 判断当前工序是否设置的抽样测试项
            {
                sSQL = " select InMan from T_FlowTestItem where ProcedureNo='" + KindList + "' and ProductNo='" + Kind + "' and TestInspectFlag = '是'  and CompanyNo = '" + sComp + "' ";
            }
            else if (sFlag == "30-4")// 产品流程 判断当前测试是否设置工序测试项抽样
            {
                sSQL = "  select InMan from T_ProductFlow where ProcedureNo='" + KindList + "' and ProductNo='" + Kind + "' and ProcedureVer = '" + QT + "'  and InspectType='工序测试项抽样' and CompanyNo = '" + sComp + "' ";
            }









            DataTable dt = DBHelper.GetDataTable(sSQL);
            if (dt.Rows.Count > 0)
            {
                sStatus = "Y";
            }
            else
            {
                sStatus = "N";
            }

            return sStatus;
        }


        // 获取工艺相关的信息
        public static DataTable GetTechInfo(string No, string Item, string Name, string MNo, string MName, string Status, string BDate, string EDate, string A, string B, string C, string D, int Row, int num, string sInMan, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string sChStr = string.Empty;
            string sNum = string.Empty;
            DataTable sdt = new DataTable();

            if (sFlag == "111-1")
            {
                sSQL = " select ProcedureNo,'('+ProcedureNo+')'+ProcedureName as PNName,ProcedureName from T_WorkProcedure where CompanyNo='" + sComp + "' order by ProcedureNo ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "111-1-1")
            {  // 返回序列号对应的工序，目前是不合格处理界面使用
                sSQL = " select ProcedureNo,'('+ProcedureNo+')'+ProcedureName as PNName,ProcedureName from T_SerialBaseFlow where SerialNo='" + Item + "' and CompanyNo='" + sComp + "' order by FlowOrder ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "111-1-2")
            {
                // 返回某个工单没有开始生产的工序，用于批量添加测试项设备
                sSQL = "select 0 NumCount, ProcedureNo,ProcedureName,'('+ProcedureNo+')'+ProcedureName as PNName,TechNo,ProductNo " +
                   "from T_OrderBaseFlow a join T_MaterInfo b  on a.ProductNo=b.MaterNo where OrderNo='" + Item + "' and a.ProductNo='" + MNo + "'";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "111-2")
            {
                sSQL = " select PANo,'('+PANo+')'+PAName as PNName,PAName from T_ProcedureAction where CompanyNo='" + sComp + "' order by PANo ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "111-3-1")  // 返回作业单元表里面的工厂，车间，线体
            {
                if (Item == "工厂")
                {
                    sSQL = " select WNo,WName,'('+WNo+')'+WName as PNName from T_WorkCenter where Kind='" + Item + "' and CompanyNo= '" + sComp + "' ";
                }
                else if (Item == "车间")
                {
                    sSQL = " select WNo,WName,'('+WNo+')'+WName as PNName from T_WorkCenter where Kind='" + Item + "' and FWNo= '" + No + "' and CompanyNo= '" + sComp + "' ";
                }
                else
                {
                    sSQL = " select WNo,WName,'('+WNo+')'+WName as PNName from T_WorkCenter where Kind='" + Item + "' and FWNo= '" + No + "' and CompanyNo= '" + sComp + "' ";
                }

                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "111-4")  // 获取工序版本下来拉
            {
                sSQL = " select ProcedureVer as PNName from T_FlowProcVer where No ='" + MNo + "' and ProcedureNo='" + Item + "' and CompanyNo='" + sComp + "' order by ProcedureVer ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "28-10")  // 获取产品编码+工序对应模板 下拉列表
            {
                sSQL = " select a.TemplateName,b.PathName,b.TPPath,a.MaterNo+'('+a.LabelVer+')' as Label from T_ProductAndLabel a join T_LabelTemplate b on a.TPNo=b.TPNo and a.TPVer=b.TPVer " +
                       " where a.PMaterNo='" + MNo + "' and a.ProcedureNo='" + Item + "' and a.CompanyNo='" + sComp + "' ";

                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "112-1")  // 获取产品编码+工序对应模板 下拉列表
            {
                sSQL = " select ActDesc from T_ProcedureActionDesc where CompanyNo='" + sComp + "'  and ActList='" + Item + "' and Status='启用'  ";

                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "113-1")  // 在产品流程中，按工序版本获取工艺文件
            {
                string sN = "0";
                string sStr = " select count(*) as Num from T_FlowFile where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and CompanyNo= '" + sComp + "' ";
                DataTable dtN = DBHelper.GetDataTable(sStr);
                if (dtN.Rows.Count > 0)
                {
                    sN = dtN.Rows[0]["Num"].ToString();
                }

                sSQL = " select " + sN + " as NumCount,*,convert(varchar(16),InDate,120) as InDate2 from T_FlowFile " +
                       " where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and CompanyNo= '" + sComp + "' ";

                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "113-2")  // 在产品流程中，按工序版本获取设备信息
            {
                string sN = "0";
                string sStr = " select count(*) as Num from T_FlowDivce where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and CompanyNo= '" + sComp + "' ";
                DataTable dtN = DBHelper.GetDataTable(sStr);
                if (dtN.Rows.Count > 0)
                {
                    sN = dtN.Rows[0]["Num"].ToString();
                }

                sSQL = " select " + sN + " as NumCount,*,convert(varchar(16),InDate,120) as InDate2 from T_FlowDivce " +
                       " where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and CompanyNo= '" + sComp + "' ";

                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "113-3")  // 在产品流程中，按工序版本获取质控品信息
            {
                string sN = "0";
                string sStr = " select count(*) as Num from T_FlowQuality where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and CompanyNo= '" + sComp + "' ";
                DataTable dtN = DBHelper.GetDataTable(sStr);
                if (dtN.Rows.Count > 0)
                {
                    sN = dtN.Rows[0]["Num"].ToString();
                }

                sSQL = " select " + sN + " as NumCount,*,convert(varchar(16),InDate,120) as InDate2 from T_FlowQuality " +
                       " where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and CompanyNo= '" + sComp + "' ";

                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "114-1")  // 在产品流程中，打印预览DHR报表
            {
                if (Item == "1")
                {  //设备及工装
                    sSQL = "select ROW_NUMBER() OVER (ORDER BY  MaterNo) AS  Id,DeviceName as DeviceName,MaterNo as DMaterNo,DeviceName as DeviceId," +
                          "UseDate as CalibrationValidity " +
                          "from T_FlowDivce WHERE ProductNo='" + No + "' and ProcedureNo='" + B + "' and ProcedureVer='" + C + "' ";
                }
                else if (Item == "2")
                {//试剂/质控品信息
                    sSQL = "select ROW_NUMBER() OVER (ORDER BY  MaterNo) AS  Id,DeviceName as DeviceName,MaterNo as QMaterNo,DeviceName as DeviceId," +
                          "UseDate as CalibrationValidity " +
                          "from T_FlowDivce WHERE ProductNo='" + No + "' and ProcedureNo='" + B + "' and ProcedureVer='" + C + "' ";
                }
                else if (Item == "3")
                {//追溯物料信息
                    SqlParameter[] parameters2 = {
                             new SqlParameter("@lnFNo", SqlDbType.NVarChar,30),
                             new SqlParameter("@lnFName", SqlDbType.NVarChar,200),
                             new SqlParameter("@lnCNo", SqlDbType.NVarChar,30),
                             new SqlParameter("@lnCName", SqlDbType.NVarChar,200),
                             new SqlParameter("@lnGX", SqlDbType.NVarChar,60),
                             new SqlParameter("@lnSotck", SqlDbType.NVarChar,20),
                             new SqlParameter("@lnNo", SqlDbType.NVarChar,20),
                             new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                             new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                             new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                             new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                             new SqlParameter("@lnRow", SqlDbType.Int),
                             new SqlParameter("@lnNum", SqlDbType.Int),
                             new SqlParameter("@lnComp", SqlDbType.NVarChar,10),
                             new SqlParameter("@lnMan", SqlDbType.NVarChar,30),
                             new SqlParameter("@lnFlag", SqlDbType.NVarChar,10),
                             new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
                    parameters2[0].Value = No;  // 产品编码
                    parameters2[1].Value = "";
                    parameters2[2].Value = "";
                    parameters2[3].Value = "";
                    parameters2[4].Value = A;  // 工序名称
                    parameters2[5].Value = "";
                    parameters2[6].Value = "";
                    parameters2[7].Value = "";
                    parameters2[8].Value = "";
                    parameters2[9].Value = "";
                    parameters2[10].Value = "";
                    parameters2[11].Value = 20000;
                    parameters2[12].Value = 1;
                    parameters2[13].Value = sComp;
                    parameters2[14].Value = sInMan;
                    parameters2[15].Value = "1-1";  // 1 预览
                    parameters2[16].Value = "";

                    DataSet DST = DBHelper.RunProcedureForDS("p_CreateBomForTree", parameters2);
                    sdt = DST.Tables[0];
                    return sdt;
                }
                else if (Item == "4")
                {//测试信息
                    sSQL = " select ROW_NUMBER() OVER (ORDER BY  SNo) AS Id,NameCH,DescCH,'' as Station,'' as ActualResult, " +
                           " '' as Conclusion,'" + sInMan + "' as Handlers,CONVERT(char(16),getdate(),120) as OperateTime " +
                           " from T_FlowTestItem WHERE ProductNo = '" + No + "' and ProcedureNo='" + B + "' and ProcedureVer='" + C + "' order by SNo ";
                }
                else if (Item == "5")
                {//基本信息   表头
                    sSQL = "select '' as SerialNo,a.ProductNo as MaterNo,a.ProductName as MaterName,a.ProcedureName,'' as BOMName,a.ProductVer as [Version], " +
                         " '' as Remark,'' as SpecificationsModel,'' as AccordingName,'' as ProduceBatch,'' as ProduceWorkOrderNo, " +
                         " c.FileNo,c.FileName,c.FileVer" +
                         " from T_ProductFlow a " +
                         " left join (select top 1 * from T_FlowFile where ProductNo='" + No + "' and ProcedureNo='" + B + "' and ProcedureVer='" + C + "' and FileNo<>'') c on a.ProductNo=c.ProductNo " +
                         " where a.ProductNo='" + No + "' and a.ProcedureNo='" + B + "' and a.ProcedureVer='" + C + "' ";
                }
                else if (Item == "6")
                {//检验方式    表头
                    sSQL = "select * from T_SerialBatchInspect WHERE 1=2";
                }
                else if (Item == "7")
                {//检验方式
                    sSQL = "select * from T_SerialBatchInspect WHERE 1=2";
                }

                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "115-1")  // 根据产品编码，工序，工序版本获取信息
            {
                sSQL = " select * from T_FlowProcVer where No='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and CompanyNo= '" + sComp + "' ";

                sdt = DBHelper.GetDataTable(sSQL);
            }
            else
            {
                SqlParameter[] parameters = {
                    new SqlParameter("@lnNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnItem", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnStatus", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,10),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
                parameters[0].Value = No;
                parameters[1].Value = Item;
                parameters[2].Value = Name;
                parameters[3].Value = MNo;
                parameters[4].Value = MName;
                parameters[5].Value = Status;
                parameters[6].Value = BDate;
                parameters[7].Value = EDate;
                parameters[8].Value = A; // A
                parameters[9].Value = B;
                parameters[10].Value = C;
                parameters[11].Value = D;
                parameters[12].Value = Row;
                parameters[13].Value = num;
                parameters[14].Value = sInMan;
                parameters[15].Value = sFlag;
                parameters[16].Value = "";

                DataSet DS = DBHelper.RunProcedureForDS("P_GetTechInfoForPage", parameters);
                sdt = DS.Tables[0];
            }

            return sdt;
        }


        // 操作工艺相关信息 
        public static string OPTechInfo(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string G, string H, string I, string J, string K, string L, string M, string N, string O, string P, string sComp, string InMan, string Remark, string sFlag, string IP)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sCHStr = string.Empty;
            string sSQL1 = string.Empty;
            string sstr = string.Empty;
            string sKind = string.Empty;
            string sProc = string.Empty;
            string sNum = string.Empty;
            string sSt = string.Empty;
            string sCHNo = string.Empty;
            int iMax = 0;
            int i = 0;
            string sMaxNo = string.Empty;
            DataTable sdt = new DataTable();

            //记录日志的参数
            string Message = string.Empty;      //日志信息
            string LogType = string.Empty;      //日志类型
            string NewValue = string.Empty;     //新数据
            string OldValue = string.Empty;     //旧数据
            string ModuleName = string.Empty;   //操作模块
            string TableName = string.Empty;    //表名
            string Condition = string.Empty;    //查询条件
            string Column = string.Empty;       //列名


            if (sFlag == "1")  // 新增工序信息  
            {
                sSQL = " insert into T_WorkProcedure(ProcedureNo,ProcedureName,CompanyNo,InMan,Remark) " +
                       " values('" + No + "','" + Name + "','" + sComp + "','" + InMan + "','" + Remark + "') ";
                Message = "添加工序，工序：(" + No + ")" + Name + "。";
                LogType = "添加";
                ModuleName = A;
                TableName = "T_WorkProcedure";
                Condition = "ProcedureNo='" + No + "'";
                Column = "*";
            }
            else if (sFlag == "2")  // 修改工序信息
            {
                sSQL = " update T_WorkProcedure set ProcedureName='" + Name + "',Remark='" + Remark + "' where ProcedureNo='" + No + "' ";
                Message = "修改工序，工序：(" + No + ")" + Name + "。";
                LogType = "修改";
                ModuleName = A;
                TableName = "T_WorkProcedure";
                Condition = "ProcedureNo='" + No + "'";
                Column = "*";
            }
            else if (sFlag == "3")  // 删除工序信息
            {
                sSQL = " delete T_WorkProcedure  where ProcedureNo='" + No + "'  ";
                Message = "删除工序，工序：(" + No + ")" + Name + "。";
                LogType = "删除";
                ModuleName = A;
                TableName = "T_WorkProcedure";
                Condition = "ProcedureNo='" + No + "'";
                Column = "*";
            }
            else if (sFlag == "4")  //新增加产品流程
            {
                // 自动产生顺序号
                iMax = 1;
                sMaxNo = DBHelper.GetMaxNo("T_ProductFlow where ProductNo='" + MNo + "' and CompanyNo='" + sComp + "' ", "FlowOrder");//
                if (sMaxNo == "")
                {
                    iMax = 1;
                }
                else
                {
                    iMax = int.Parse(sMaxNo) + 1;
                }

                // 20240901 ：新增的产品流程对应工序，默认是禁用
                sSQL = " insert into T_ProductFlow(ProductNo,ProductName,SectionNo,SectionName,ProcedureNo,ProcedureVer,VerDesc,VerStatus,ProcedureName,FlowOrder,ConditionNo,ConditionName,FlowKind,WorkUnit,InspectType,InspectNo,InspectScheme,Status,CompanyNo,InMan,Remark) " +
                        " values('" + MNo + "','" + MName + "','" + No + "','" + Name + "','" + No + "','" + Item + "','" + F + "','禁用','" + Name + "'," + iMax + ",'" + B + "','" + C + "','" + D + "','" + E + "','" + M + "','" + N + "','" + O + "','启用','" + sComp + "','" + InMan + "','" + Remark + "') ";

                // 如果工序版本不存在，则插入
                sSt = " select InMan from T_FlowProcVer where No='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' ";
                sdt = DBHelper.GetDataTable(sSt);
                if (sdt.Rows.Count == 0)
                {
                    sSQLs = " insert into T_FlowProcVer(No,ProcedureNo,ProcedureVer,ProcedureName,VerDesc,Status,CompanyNo,InMan,Remark)" +
                           " values('" + MNo + "','" + No + "','" + Item + "','" + Name + "','" + F + "','禁用','" + sComp + "','" + InMan + "','" + Remark + "') ";
                }
                else
                {  // 更新版本描述
                    sSQLs = " update T_FlowProcVer set VerDesc='" + F + "',Status='禁用' where No='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' ";
                }

                // 单独更新产品流程是否典型产品，针对产品编码更新  
                sSQL1 = " update T_ProductFlow set TechNo='" + H + "',Model='" + G + "',ClassFlow='" + I + "' where ProductNo='" + MNo + "' ";

                Message = "添加产品工艺路线，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "。";
                LogType = "添加";
                ModuleName = L;
                TableName = "T_ProductFlow";
                Condition = "ProductNo = '" + MNo + "' and ProcedureNo='" + No + "'";
                Column = "*";
            }
            else if (sFlag == "5")  //修改产品流程-b
            {
                sSQL = " update T_ProductFlow set ProductName='" + MName + "',FlowOrder='" + A + "',ProcedureVer='" + Item + "',VerStatus='" + J + "',VerDesc='" + F + "',ConditionNo='" + B + "',ConditionName='" + C + "',WorkUnit='" + E + "',InspectType='" + M + "',InspectNo='" + N + "',InspectScheme='" + O + "',Remark='" + Remark + "' " +
                       " where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' ";

                // 如果工序版本不存在，则插入
                sSt = " select InMan from T_FlowProcVer where No='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' ";
                sdt = DBHelper.GetDataTable(sSt);
                if (sdt.Rows.Count == 0)
                {
                    sSQLs = " insert into T_FlowProcVer(No,ProcedureNo,ProcedureVer,ProcedureName,VerDesc,Status,CompanyNo,InMan,Remark)" +
                           " values('" + MNo + "','" + No + "','" + Item + "','" + Name + "','" + F + "','启用','" + sComp + "','" + InMan + "','" + Remark + "') ";
                }
                else
                {  // 更新版本描述
                    sSQLs = " update T_FlowProcVer set VerDesc='" + F + "' where No='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' ";
                }

                // 单独更新产品流程是否典型产品，针对产品编码更新  
                sSQL1 = " update T_ProductFlow set TechNo='" + H + "',Model='" + G + "',ClassFlow='" + I + "' where ProductNo='" + MNo + "' ";

                Message = "修改产品工艺路线，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "。";
                LogType = "修改";
                ModuleName = L;
                TableName = "T_ProductFlow";
                Condition = "ProductNo = '" + MNo + "' and ProcedureNo='" + No + "'";
                Column = "*";
            }
            else if (sFlag == "6")  //调整顺序 -- 产品流程的
            {

                if (F == "up")
                {  // 上移：先判断准备上移的这个工序的上一个ID是多少。
                    sSt = " select top 1 FlowOrder,ProcedureNo from T_ProductFlow where ProductNo='" + MNo + "' and  FlowOrder < " + A + " order by FlowOrder desc ";
                    sdt = DBHelper.GetDataTable(sSt);
                    if (sdt.Rows.Count > 0)
                    {
                        sNum = sdt.Rows[0]["FlowOrder"].ToString();
                        sProc = sdt.Rows[0]["ProcedureNo"].ToString();
                    }
                    else
                    {
                        sNum = A;
                        sProc = "";
                    }

                    Message = "上移产品工艺路线，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "。";
                }
                else
                {  // 下移
                    sSt = " select top 1 FlowOrder,ProcedureNo from T_ProductFlow where ProductNo='" + MNo + "' and FlowOrder > " + A + " order by FlowOrder asc ";
                    sdt = DBHelper.GetDataTable(sSt);
                    if (sdt.Rows.Count > 0)
                    {
                        sNum = sdt.Rows[0]["FlowOrder"].ToString();
                        sProc = sdt.Rows[0]["ProcedureNo"].ToString();
                    }
                    else
                    {
                        sNum = A;
                        sProc = "";
                    }

                    Message = "下移产品工艺路线，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "。";
                }

                sSQLs = " update T_ProductFlow set FlowOrder='" + A + "' where ProductNo='" + MNo + "' and ProcedureNo='" + sProc + "' ";  // 更新上/ 下 一个工序
                sSQL = " update T_ProductFlow set FlowOrder='" + sNum + "' where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' ";  //更新本次工序的顺序

                // 其他的

                LogType = "移动";
                ModuleName = E;
            }
            else if (sFlag == "7")  //禁用  
            {
                sSQL = " update T_ProductFlow set Status='禁用' where ProductNo='" + MNo + "' ";
                Message = "禁用产品工艺路线，产品编码：" + MNo + "。";
                LogType = "禁用";
                ModuleName = E;
            }
            else if (sFlag == "8")  //启用
            {
                sSQL = " update T_ProductFlow set Status='启用' where ProductNo='" + MNo + "' ";  //  and ProcedureNo='" + No + "'
                Message = "启用产品工艺路线，产品编码：" + MNo + "。";
                LogType = "启用";
                ModuleName = E;
            }
            else if (sFlag == "9")  //删除
            {
                //sMaxNo = DBHelper.GetMaxNo("T_ProductFlowLog where convert(char(10),InDate,120)=convert(char(10),getdate(),120) ", "CHNo");//
                //if (sMaxNo == "")
                //{
                //    sCHNo = "CH" + CreateAllNo.CreateBillNo(2, 4) + "1";  // CH230910 00001
                //}
                //else
                //{
                //    string sTemp = sMaxNo.Substring(8, 5);
                //    iMax = int.Parse(sTemp) + 1;
                //    int len = iMax.ToString().Length;
                //    i = 5 - len;
                //    sCHNo = "CH" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
                //}

                //sCHStr = " insert Into T_ProductFlowLog(CHNo,ProductNo,ProductVer,ProcedureNo,ProcedureVer,PreValue,ChangeValue,V1,V2,V3,V4,V5,V6,V7,V8,V9,V10,ChangeKind,CompanyNo,InMan,Remark)" +
                //         " select '" + sCHNo + "',ProductNo,'','" + No + "','',Status,'删除','','','','','','','','','','','删除产品流程','" + sComp + "','" + InMan + "','' " +
                //         " from T_ProductFlow where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' ";


                sSQL = " delete T_ProductFlow  where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' ";

                Message = "删除产品工艺路线，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "。";
                LogType = "删除";
                ModuleName = E;
                TableName = "T_ProductFlow";
                Condition = "ProductNo = '" + MNo + "' and ProcedureNo='" + No + "'";
                Column = "*";
            }
            else if (sFlag == "9-0-1")  //产品流程-禁用工序对应版本
            {
                //sMaxNo = DBHelper.GetMaxNo("T_ProductFlowLog where convert(char(10),InDate,120)=convert(char(10),getdate(),120) ", "CHNo");//
                //if (sMaxNo == "")
                //{
                //    sCHNo = "CH" + CreateAllNo.CreateBillNo(2, 4) + "1";  // CH230910 00001
                //}
                //else
                //{
                //    string sTemp = sMaxNo.Substring(8, 5);
                //    iMax = int.Parse(sTemp) + 1;
                //    int len = iMax.ToString().Length;
                //    i = 5 - len;
                //    sCHNo = "CH" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
                //}

                //sCHStr = " insert Into T_ProductFlowLog(CHNo,ProductNo,ProductVer,ProcedureNo,ProcedureVer,PreValue,ChangeValue,V1,V2,V3,V4,V5,V6,V7,V8,V9,V10,ChangeKind,CompanyNo,InMan,Remark)" +
                //         " select '" + sCHNo + "','" + MNo + "','','" + No + "','" + Item + "',Status,'禁用','','','','','','','','','','','禁用工序版本','" + sComp + "','" + InMan + "','' " +
                //         " from T_FlowProcVer where No='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' ";

                sSQL = " update T_ProductFlow set VerStatus='禁用',VerDesc='" + F + "' where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' ";
                sSQLs = " update T_FlowProcVer set Status='禁用',VerDesc='" + F + "' where No='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' ";

                Message = "禁用产品工艺路线工序对应版本，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "。";
                LogType = "禁用";
                ModuleName = E;
            }
            else if (sFlag == "9-0-2")  //产品流程-启用工序对应版本
            {
                //sMaxNo = DBHelper.GetMaxNo("T_ProductFlowLog where convert(char(10),InDate,120)=convert(char(10),getdate(),120) ", "CHNo");//
                //if (sMaxNo == "")
                //{
                //    sCHNo = "CH" + CreateAllNo.CreateBillNo(2, 4) + "1";  // CH230910 00001
                //}
                //else
                //{
                //    string sTemp = sMaxNo.Substring(8, 5);
                //    iMax = int.Parse(sTemp) + 1;
                //    int len = iMax.ToString().Length;
                //    i = 5 - len;
                //    sCHNo = "CH" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
                //}

                //sCHStr = " insert Into T_ProductFlowLog(CHNo,ProductNo,ProductVer,ProcedureNo,ProcedureVer,PreValue,ChangeValue,V1,V2,V3,V4,V5,V6,V7,V8,V9,V10,ChangeKind,CompanyNo,InMan,Remark)" +
                //         " select '" + sCHNo + "','" + MNo + "','','" + No + "','" + Item + "',Status,'启用','','','','','','','','','','','启用工序版本','" + sComp + "','" + InMan + "','' " +
                //         " from T_FlowProcVer where No='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' ";

                sSQL = " update T_ProductFlow set VerStatus='启用',VerDesc='" + F + "' where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' ";
                sSQLs = " update T_FlowProcVer set Status='启用',VerDesc='" + F + "' where No='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' ";

                Message = "启用产品工艺路线工序对应版本，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "。";
                LogType = "启用";
                ModuleName = E;
            }
            else if (sFlag == "9-0-3")  //产品流程-复制工序对应版本对应的设备，工艺文件，测试项
            {
                // 做到最新的那个版本
                var Ver = string.Empty;
                string sStr = " select top 1 ProcedureVer from T_FlowProcVer where No='" + MNo + "' and ProcedureNo='" + No + "' " +
                              " and ProcedureVer<>'" + Item + "' and Status='启用' order by InDate desc ";
                DataTable dtN = DBHelper.GetDataTable(sStr);
                if (dtN.Rows.Count > 0) // 说明已存在历史版本
                {
                    Ver = dtN.Rows[0]["ProcedureVer"].ToString();
                }
                else
                {
                    Ver = "";
                }

                sCHStr = " insert into T_FlowDivce(ProductNo,ProcedureNo,ProcedureVer,ProcedureName,MaterNo,MaterVer,DeviceName,DeptName,DeviceKind,UseDate,Status,CompanyNo,InMan,Remark) " +
                   " select ProductNo,ProcedureNo,'" + Item + "',ProcedureName,MaterNo,MaterVer,DeviceName,DeptName,DeviceKind,UseDate,Status,'" + sComp + "','" + InMan + "',''  " +
                   " from T_FlowDivce where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Ver + "'  and CompanyNo= '" + sComp + "' ";

                // 文件要自己上传，因此不拷贝
                //sSQL = " insert into T_FlowFile(ProductNo,ProcedureNo,ProcedureVer,ProcedureName,FileNo,FileVer,FileName,DocName,FilePath,Status,CompanyNo,InMan,Remark) " +
                //       " select ProductNo,ProcedureNo,'" + Item + "',ProcedureName,FileNo,FileVer,FileName,DocName,FilePath,'启用','" + sComp + "','" + InMan + "',''  " +
                //       " from T_FlowFile  where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Ver + "' ";

                sSQLs = " insert into T_FlowTestItem(ProductNo,ProcedureNo,ProcedureVer,ProcedureName,SpecNo,SNo,NameCH,DescCH,StandardCH,ThisValue,IncludeOne,ToValue,IncludeTwo,RangeKind,ValueKind,SpecUnit,Status,CompanyNo,InMan,Remark) " +
                        " select ProductNo,ProcedureNo,'" + Item + "',ProcedureName,SpecNo,SNo,NameCH,DescCH,StandardCH,ThisValue,IncludeOne,ToValue,IncludeTwo,RangeKind,ValueKind,SpecUnit,'启用','" + sComp + "','" + InMan + "',''  " +
                        " from T_FlowTestItem where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Ver + "' and CompanyNo= '" + sComp + "' ";

                Message = "复制产品工艺路线工序对应版本，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "。";
                LogType = "复制";
                ModuleName = E;
            }
            else if (sFlag == "9-1-1")  //维护产品流程对应的设备-新增  
            {  // { No: sNo, Name: "", Item: sPVer, MNo: sPNo, MName: "", A: "", B: "", C: sFNo, D: sKind, E: sDName, F: "", Remark: "", Flag: sFlag };

                string sStr = " select InMan from T_FlowDivce where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' " +
                              " and MaterNo='" + C + "' and DeviceKind='" + D + "' and DeptName='" + E + "' and Status='" + F + "'  and CompanyNo= '" + sComp + "' ";
                DataTable dtN = DBHelper.GetDataTable(sStr);
                if (dtN.Rows.Count == 0) // 说明未选择了该设备
                {
                    sSQL = " insert into T_FlowDivce(ProductNo,ProcedureNo,ProcedureVer,ProcedureName,MaterNo,MaterVer,DeviceName,DeptName,DeviceKind,UseDate,Status,CompanyNo,InMan,Remark) " +
                           " select distinct '" + MNo + "','" + No + "','" + Item + "','" + Name + "',MaterNo,'',MaterName,DeptName,DeviceKind,'','','" + sComp + "','" + InMan + "',''  " +
                           " from T_DeviceClassHead where MaterNo='" + C + "' and CompanyNo= '" + sComp + "' ";
                }
                else
                { // 说明已有这个设备了
                    sSQL = "";
                }

                Message = "添加产品工艺路线设备信息，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "。";
                LogType = "添加";
                ModuleName = Remark;
                TableName = "T_FlowDivce";
                Condition = "ProductNo = '" + MNo + "' and ProcedureNo ='" + No + "' and ProcedureVer = '" + Item + "' and MaterNo = '" + C + "'";
                Column = "*";
            }
            else if (sFlag == "9-1-2")  //维护产品流程对应的设备-删除
            {
                sSQL = " delete T_FlowDivce where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and MaterNo='" + C + "' ";
                Message = "删除产品工艺路线设备信息，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "。";
                LogType = "删除";
                ModuleName = Remark;
                TableName = "T_FlowDivce";
                Condition = "ProductNo = '" + MNo + "' and ProcedureNo ='" + No + "' and ProcedureVer = '" + Item + "' and MaterNo = '" + C + "'";
                Column = "*";
            }
            else if (sFlag == "9-1-3")  //维护产品流程对应的质控品-选择
            {  // { No: sNo, Name: "", Item: sPVer, MNo: sPNo, MName: "", A: "", B: "", C: sFNo, D: sKind, E: sDName, F: "", Remark: "", Flag: sFlag };

                string sStr = " select InMan from T_FlowQuality where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' " +
                              " and MaterNo='" + C + "' and CompanyNo= '" + sComp + "' ";
                DataTable dtN = DBHelper.GetDataTable(sStr);
                if (dtN.Rows.Count == 0) // 说明未选择了该质控品
                {
                    sSQL = " insert into T_FlowQuality(ProductNo,ProcedureNo,ProcedureVer,ProcedureName,MaterNo,QualityName,DeptName,Spec,Status,CompanyNo,InMan) " +
                           " select '" + MNo + "','" + No + "','" + Item + "','" + Name + "',MaterNo,max(MaterName),'',max(Spec),max(Status),'" + sComp + "','" + InMan + "' " +
                           " from T_QCWarehouse where MaterNo='" + C + "' and CompanyNo= '" + sComp + "' group by MaterNo ";
                }
                else
                { // 说明已有这个设备了
                    sSQL = "";
                }
            }
            else if (sFlag == "9-1-4")  //维护产品流程对应的质控品-删除
            {
                sSQL = " delete T_FlowQuality where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and MaterNo='" + C + "' ";
            }
            else if (sFlag == "9-2-1")  //维护产品流程对应的工艺文件-新增
            {
                sSQL = " insert into T_FlowFile(ProductNo,ProcedureNo,ProcedureVer,ProcedureName,FileNo,FileVer,FileName,DocName,FilePath,Status,CompanyNo,InMan,Remark) " +
                       " values('" + MNo + "','" + No + "','" + Item + "','" + Name + "','" + C + "','" + D + "','" + E + "','" + B + "','" + A + "','启用','" + sComp + "','" + InMan + "','') ";

                Message = "添加产品工艺路线工艺文件信息，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "。";
                LogType = "添加";
                ModuleName = L;
                TableName = "T_FlowFile";
                Condition = "ProductNo = '" + MNo + "' and ProcedureNo ='" + No + "' and ProcedureVer = '" + Item + "' and FileNo = '" + C + "' and FileVer='" + D + "' ";
                Column = "*";
            }
            else if (sFlag == "9-2-2")  //维护产品流程对应的艺文件-修改
            {
                sSQL = " update T_FlowFile set FileName='" + E + "' " +
                       " where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and FileNo='" + C + "' and FileVer='" + D + "' ";

                Message = "修改产品工艺路线工艺文件信息，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "。";
                LogType = "修改";
                ModuleName = L;
                TableName = "T_FlowFile";
                Condition = "ProductNo = '" + MNo + "' and ProcedureNo ='" + No + "' and ProcedureVer = '" + Item + "' and FileNo = '" + C + "' and FileVer='" + D + "' ";
                Column = "*";
            }
            else if (sFlag == "9-2-3")  //维护产品流程对应的艺文件-删除
            {
                sSQL = " delete T_FlowFile where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and FileNo='" + C + "' and FileVer='" + D + "' ";

                Message = "删除产品工艺路线工艺文件信息，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "。";
                LogType = "删除";
                ModuleName = L;
                TableName = "T_FlowFile";
                Condition = "ProductNo = '" + MNo + "' and ProcedureNo ='" + No + "' and ProcedureVer = '" + Item + "' and FileNo = '" + C + "' and FileVer='" + D + "' ";
                Column = "*";
            }
            else if (sFlag == "9-2-4")  //维护产品流程对应的艺文件-选择
            {
                sSQL = " insert into T_FlowFile(ProductNo,ProcedureNo,ProcedureVer,ProcedureName,FileNo,FileVer,FileName,DocName,FilePath,Status,CompanyNo,InMan,Remark) " +
                       " select '" + MNo + "','" + No + "','" + Item + "','" + Name + "','" + C + "','" + D + "',FileName,DocName,FilePath,Status,CompanyNo,'" + InMan + "','' " +
                       " From T_ProcessFile where FileNo='" + C + "' and FileVer='" + D + "' and CompanyNo= '" + sComp + "' ";

                Message = "选择添加产品工艺路线工艺文件信息，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "。";
                LogType = "添加";
                ModuleName = L;
                TableName = "T_FlowFile";
                Condition = "ProductNo = '" + MNo + "' and ProcedureNo ='" + No + "' and ProcedureVer = '" + Item + "' and FileNo = '" + C + "' and FileVer='" + D + "' ";
                Column = "*";
            }
            else if (sFlag == "9-3-1")  //维护产品流程对应的测试项-新增
            {
                sMaxNo = DBHelper.GetMaxNo("T_FlowTestItem where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' ", "SNo");//  
                if (sMaxNo == "")
                {
                    sNum = "1";
                }
                else
                {
                    iMax = int.Parse(sMaxNo) + 1;
                    sNum = iMax.ToString();
                }

                sSQL = " insert into T_FlowTestItem(ProductNo,ProcedureNo,ProcedureVer,ProcedureName,SpecNo,SNo,NameCH,DescCH,StandardCH,ThisValue,IncludeOne,ToValue,IncludeTwo,RangeKind,ValueKind,SpecUnit,Status,CompanyNo,InMan,Remark) " +
                       " values('" + MNo + "','" + No + "','" + Item + "','" + Name + "','" + J + "','" + sNum + "','" + A + "','" + B + "','" + C + "','" + F + "','" + H + "','" + G + "','" + I + "','" + E + "','" + D + "','" + M + "','启用','" + sComp + "','" + InMan + "','') ";

                // 同时 给测试项库增加一个记录
                sMaxNo = DBHelper.GetMaxNo("T_TestItemBaseInfo where TechNo='" + MName + "' and ProcedureNo='" + No + "' ", "SNo");//  
                if (sMaxNo == "")
                {
                    sNum = "1";
                }
                else
                {
                    iMax = int.Parse(sMaxNo) + 1;
                    sNum = iMax.ToString();
                }

                sSQLs = " insert into T_TestItemBaseInfo(TechNo,ProcedureNo,ProcedureName,SpecNo,SNo,Model,NameCH,DescCH,StandardCH,ThisValue,IncludeOne,ToValue,IncludeTwo,RangeKind,ValueKind,SpecUnit,Status,CompanyNo,InMan) " +
                       " select top 1 '" + MName + "','" + No + "','" + Name + "','" + J + "','" + sNum + "',MaterSpec,'" + A + "','" + B + "','" + C + "','" + F + "','" + H + "','" + G + "','" + I + "','" + E + "','" + D + "','" + M + "','启用','" + sComp + "','" + InMan + "' " +
                       " from T_MaterInfo where MaterNo = '" + MNo + "' ";

                Message = "添加产品工艺路线测试项信息，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "。";
                LogType = "添加";
                ModuleName = K;
                TableName = "T_FlowTestItem";
                Condition = "ProductNo = '" + MNo + "' and ProcedureNo ='" + No + "' and ProcedureVer = '" + Item + "' and SpecNo = '" + J + "' ";
                Column = "*";
            }
            else if (sFlag == "9-3-2")  //维护产品流程对应的测试项-修改
            {
                sSQL = " update T_FlowTestItem set NameCH='" + A + "',DescCH='" + B + "',StandardCH='" + C + "',ThisValue='" + F + "',IncludeOne='" + H + "',ToValue='" + G + "',IncludeTwo='" + I + "',RangeKind='" + E + "',ValueKind='" + D + "',SpecUnit='" + M + "' " +
                       " where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and SpecNo='" + J + "' ";

                Message = "修改产品工艺路线测试项信息，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "。";
                LogType = "修改";
                ModuleName = K;
                TableName = "T_FlowTestItem";
                Condition = "ProductNo = '" + MNo + "' and ProcedureNo ='" + No + "' and ProcedureVer = '" + Item + "' and SpecNo = '" + J + "' ";
                Column = "*";
            }
            else if (sFlag == "9-3-3")  //维护产品流程对应的测试项-删除 
            {
                sSQL = " delete T_FlowTestItem where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and SpecNo='" + J + "'  ";

                Message = "删除产品工艺路线测试项信息，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "。";
                LogType = "删除";
                ModuleName = K;
                TableName = "T_FlowTestItem";
                Condition = "ProductNo = '" + MNo + "' and ProcedureNo ='" + No + "' and ProcedureVer = '" + Item + "' and SpecNo = '" + J + "' ";
                Column = "*";
            }
            else if (sFlag == "9-3-3-1")
            {
                sSQL = "delete T_FlowTestItem where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and SpecNo IN (" + J + ")  ";//删除工艺路线测试项-可删除多个

                Message = "批量删除产品工艺路线测试项信息，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "，编号：" + J + "。";
                LogType = "删除";
                ModuleName = K;
            }
            else if (sFlag == "9-3-4")  //维护产品流程对应的测试项-上移 / 下移
            {
                if (F == "up")
                {    // 上移：先判断准备上移的这个工序的上一个ID是多少。
                    sSt = " select top 1 SNo,SpecNo from T_FlowTestItem where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and SNo < " + A + " order by SNo desc ";
                    sdt = DBHelper.GetDataTable(sSt);
                    if (sdt.Rows.Count > 0)
                    {
                        sNum = sdt.Rows[0]["SNo"].ToString();
                        sProc = sdt.Rows[0]["SpecNo"].ToString(); // 测试项编号
                    }
                    else
                    {
                        sNum = A;
                    }

                    Message = "上移产品工艺路线测试项，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "，编号：" + J + "。";
                }
                else
                {  // 下移
                    sSt = " select top 1 SNo,SpecNo from T_FlowTestItem where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and SNo > " + A + " order by SNo asc ";
                    sdt = DBHelper.GetDataTable(sSt);
                    if (sdt.Rows.Count > 0)
                    {
                        sNum = sdt.Rows[0]["SNo"].ToString();
                        sProc = sdt.Rows[0]["SpecNo"].ToString(); // 测试项编号
                    }
                    else
                    {
                        sNum = A;
                    }

                    Message = "下移产品工艺路线测试项，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "，编号：" + J + "。";
                }

                sSQLs = " update T_FlowTestItem set SNo='" + A + "' where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and SpecNo='" + sProc + "' ";  // 更新上/ 下 一个测试项的
                sSQL = " update T_FlowTestItem set SNo='" + sNum + "' where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and SpecNo='" + J + "' ";  //更新本次测试项的顺序

                LogType = "移动";
                ModuleName = K;
            }
            else if (sFlag == "9-3-5")  //维护产品流程对应的测试项-插入
            {
                // 当前测试项顺序编号后的，都往后增加 1
                iMax = int.Parse(L) + 1;
                sNum = iMax.ToString();

                // 其他顺序好的，往后挪
                sSQLs = " update T_FlowTestItem set SNo=SNo+1 where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and SNo > " + L + " ";
                sSQL = " insert into T_FlowTestItem(ProductNo,ProcedureNo,ProcedureVer,ProcedureName,SpecNo,SNo,NameCH,DescCH,StandardCH,ThisValue,IncludeOne,ToValue,IncludeTwo,RangeKind,ValueKind,SpecUnit,Status,CompanyNo,InMan,Remark) " +
                       " values('" + MNo + "','" + No + "','" + Item + "','" + Name + "','" + J + "','" + sNum + "','" + A + "','" + B + "','" + C + "','" + F + "','" + H + "','" + G + "','" + I + "','" + E + "','" + D + "','" + M + "','启用','" + sComp + "','" + InMan + "','') ";

                // 同时 给测试项库增加一个记录
                sMaxNo = DBHelper.GetMaxNo("T_TestItemBaseInfo where TechNo='" + MName + "' and ProcedureNo='" + No + "' ", "SNo");//  
                if (sMaxNo == "")
                {
                    sNum = "1";
                }
                else
                {
                    iMax = int.Parse(sMaxNo) + 1;
                    sNum = iMax.ToString();
                }

                sSQL1 = " insert into T_TestItemBaseInfo(TechNo,ProcedureNo,ProcedureName,SpecNo,SNo,Model,NameCH,DescCH,StandardCH,ThisValue,IncludeOne,ToValue,IncludeTwo,RangeKind,ValueKind,SpecUnit,Status,CompanyNo,InMan) " +
                       " select top 1 '" + MName + "','" + No + "','" + Name + "','" + J + "','" + sNum + "',MaterSpec,'" + A + "','" + B + "','" + C + "','" + F + "','" + H + "','" + G + "','" + I + "','" + E + "','" + D + "','" + M + "','启用','" + sComp + "','" + InMan + "' " +
                       " from T_MaterInfo where MaterNo = '" + MNo + "' ";

                Message = "添加产品工艺路线测试项信息，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "。";
                LogType = "添加";
                ModuleName = K;
                TableName = "T_FlowTestItem";
                Condition = "ProductNo = '" + MNo + "' and ProcedureNo ='" + No + "' and ProcedureVer = '" + Item + "' and SpecNo = '" + J + "' ";
                Column = "*";
            }
            else if (sFlag == "9-3-6")  // 从测试项库选择对应测试项，增加到后面 -- 单个增加的
            {
                if (L == "")
                {
                    sMaxNo = DBHelper.GetMaxNo("T_FlowTestItem where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' ", "SNo");//  
                    if (sMaxNo == "")
                    {
                        sNum = "1";
                    }
                    else
                    {
                        iMax = int.Parse(sMaxNo) + 1;
                        sNum = iMax.ToString();
                    }

                }
                else
                {
                    // 当前测试项顺序编号后的，都往后增加 1
                    iMax = int.Parse(L) + 1;
                    sNum = iMax.ToString();
                    // 其他顺序好的，往后挪
                    sSQLs = " update T_FlowTestItem set SNo=SNo+1 where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and SNo > " + L + " ";
                }

                sSQL = " insert into T_FlowTestItem(ProductNo,ProcedureNo,ProcedureVer,ProcedureName,SpecNo,SNo,NameCH,DescCH,StandardCH,ThisValue,IncludeOne,ToValue,IncludeTwo,RangeKind,ValueKind,SpecUnit,Status,CompanyNo,InMan,Remark) " +
                       " select '" + MNo + "','" + No + "','" + Item + "','" + Name + "',SpecNo,'" + sNum + "',NameCH,DescCH,StandardCH,ThisValue,IncludeOne,ToValue,IncludeTwo,RangeKind,ValueKind,SpecUnit,'启用','" + sComp + "','" + InMan + "','' " +
                       " from T_TestItemBaseInfo where TechNo='" + MName + "' and ProcedureNo='" + No + "' and SpecNo='" + J + "'  ";

                Message = "选择添加产品工艺路线测试项信息，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "。";
                LogType = "添加";
                ModuleName = K;
                TableName = "T_FlowTestItem";
                Condition = "ProductNo = '" + MNo + "' and ProcedureNo ='" + No + "' and ProcedureVer = '" + Item + "' and SpecNo = '" + J + "' ";
                Column = "*";
            }
            else if (sFlag == "9-3-7")  // 从测试项库选择对应测试项，增加到后面-- 全部增加的
            {
                sMaxNo = DBHelper.GetMaxNo("T_FlowTestItem where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' ", "SNo");//  
                if (sMaxNo == "")
                {
                    sNum = "0";
                }
                else
                {
                    iMax = int.Parse(sMaxNo) + 1;
                    sNum = iMax.ToString();
                }

                sSQL = " insert into T_FlowTestItem(ProductNo,ProcedureNo,ProcedureVer,ProcedureName,SpecNo,SNo,NameCH,DescCH,StandardCH,ThisValue,IncludeOne,ToValue,IncludeTwo,RangeKind,ValueKind,SpecUnit,Status,CompanyNo,InMan,Remark) " +
                       " select '" + MNo + "','" + No + "','" + Item + "','" + Name + "',SpecNo,SNo+" + sNum + ",NameCH,DescCH,StandardCH,ThisValue,IncludeOne,ToValue,IncludeTwo,RangeKind,ValueKind,SpecUnit,'启用','" + sComp + "','" + InMan + "','' " +
                       " from T_TestItemBaseInfo where TechNo='" + MName + "' and ProcedureNo='" + No + "' " +
                       " and TechNo+ProcedureNo+SpecNo not in (select TechNo+ProcedureNo+SpecNo from T_FlowTestItem  where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "')";

                Message = "批量添加添加产品工艺路线测试项信息，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "。";
                LogType = "添加";
                ModuleName = K;
            }
            else if (sFlag == "9-3-8")  // 产品工艺流程，设置测试项涉及 或 不涉及
            {
                //sMaxNo = DBHelper.GetMaxNo("T_ProductFlowLog where convert(char(10),InDate,120)=convert(char(10),getdate(),120) ", "CHNo");//
                //if (sMaxNo == "")
                //{
                //    sCHNo = "CH" + CreateAllNo.CreateBillNo(2, 4) + "1";  // CH230910 00001
                //}
                //else
                //{
                //    string sTemp = sMaxNo.Substring(8, 5);
                //    iMax = int.Parse(sTemp) + 1;
                //    int len = iMax.ToString().Length;
                //    i = 5 - len;
                //    sCHNo = "CH" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
                //}

                //sCHStr = " insert Into T_ProductFlowLog(CHNo,ProductNo,ProductVer,ProcedureNo,ProcedureVer,PreValue,ChangeValue,V1,V2,V3,V4,V5,V6,V7,V8,V9,V10,ChangeKind,CompanyNo,InMan,Remark)" +
                //         " select '" + sCHNo + "','" + MNo + "','','" + No + "','" + Item + "',Status,'启用','设置前属性',NotNeed,SpecNo,'','','','','','','','设置涉及属性','" + sComp + "','" + InMan + "','' " +
                //         " from T_FlowTestItem where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and SpecNo='" + J + "' ";


                sSQL = " update T_FlowTestItem set NotNeed=case when NotNeed='' then '不涉及' else '' end  where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and SpecNo='" + J + "' ";

                Message = "设置产品工艺路线测试项是否涉及，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "，编号：" + J + "。";
                LogType = "修改";
                ModuleName = K;
            }
            else if (sFlag == "9-3-9")
            {
                sSQL = " update T_FlowTestItem set TestInspectFlag=case when TestInspectFlag='' then '是' else '' end  where ProductNo='" + MNo + "' and ProcedureNo='" + No + "' and ProcedureVer='" + Item + "' and SpecNo='" + J + "' ";

                Message = "设置产品工艺路线测试项是否抽样，产品编码：" + MNo + "，工序：(" + No + ")" + Name + "，版本：" + Item + "，编号：" + J + "。";
                LogType = "修改";
                ModuleName = K;
            }
            else if (sFlag == "10")  //新增工作中心 
            {
                sSQL = " insert into T_WorkCenter(FWNo,FWName,WNo,WName,Kind,TestIP,TestUser,TestPwd,EPath,UseFlag,DeptNo,DeptName,CompanyNo,InMan,Remark)" +
                       " values('" + No + "','" + Name + "','" + MNo + "','" + MName + "','" + A + "','" + B + "','" + C + "','" + D + "','" + E + "','是','" + G + "','" + H + "','" + sComp + "','" + InMan + "','" + Remark + "') ";

                Message = "添加工作中心，父编码：" + No + "，子编码：" + MNo + "，类别：" + A + "。";
                LogType = "添加";
                ModuleName = F;
                TableName = "T_WorkCenter";
                Condition = "FWNo = '" + No + "' and WNo ='" + MNo + "' ";
                Column = "*";
            }
            else if (sFlag == "11")  //修改工作中心 
            {
                sSQL = " update T_WorkCenter set FWName='" + Name + "',WName='" + MName + "',Kind='" + A + "',TestIP='" + B + "',TestUser='" + C + "',TestPwd='" + D + "',EPath='" + E + "',DeptNo='" + G + "',DeptName='" + H + "',Remark='" + Remark + "' " +
                       " where FWNo='" + No + "' and WNo='" + MNo + "' ";

                //只有等于车间才需更新部门
                if (A == "车间")
                {
                    sSQLs = " ;WITH TreeCTE AS (SELECT FWNo, WNo FROM T_WorkCenter WHERE FWNo = '" + MNo + "' UNION ALL SELECT t.FWNo, t.WNo from T_WorkCenter t JOIN TreeCTE cte ON t.FWNo = cte.WNo) " +
                    " update T_WorkCenter set DeptNo='" + G + "',DeptName='" + H + "' from T_WorkCenter a join TreeCTE b on a.FWNo=b.FWNo and a.WNo=b.WNo OPTION (MAXRECURSION 20); ";
                }

                Message = "修改工作中心，父编码：" + No + "，子编码：" + MNo + "。";
                LogType = "修改";
                ModuleName = F;
                TableName = "T_WorkCenter";
                Condition = "FWNo = '" + No + "' and WNo ='" + MNo + "' ";
                Column = "*";
            }
            else if (sFlag == "12")  //删除工作中心 
            {
                sSQL = " delete T_WorkCenter  where FWNo='" + No + "' and WNo='" + MNo + "' ";

                Message = "删除工作中心，父编码：" + No + "，子编码：" + MNo + "，类别：" + A + "。";
                LogType = "删除";
                ModuleName = F;
                TableName = "T_WorkCenter";
                Condition = "FWNo = '" + No + "' and WNo ='" + MNo + "' ";
                Column = "*";
            }
            else if (sFlag == "16")  //新增工序行为 { No: sNo, Name: sName, Item: sDesc, MNo: sTestItem, MName: sScanDevice,  A: sPLable, B: sSMater, C: sUpPack, D: "", E: "", F: "", Remark: sRemark, Flag: sFlag };
            {
                string sNo = string.Empty;
                string sls = string.Empty;

                sMaxNo = DBHelper.GetMaxNo("T_ProcedureAction where PANo not in ('GXXWWX01') ", "PANo");//GXXWWX01 固定给微信工序行为
                if (sMaxNo == "")
                {
                    sNo = "GXXW" + "001";
                }
                else
                {  //目前只支持3位流水号 sTNo.Substring(sTNo.Length - 2, 2)
                    sls = sMaxNo.Substring(sMaxNo.Length - 3, 3);
                    iMax = int.Parse(sls) + 1;

                    if (iMax <= 9)
                    {
                        sNo = "GXXW" + "00" + iMax.ToString();
                    }
                    else if ((iMax > 9) && (iMax <= 99))
                    {
                        sNo = "GXXW" + "0" + iMax.ToString();
                    }
                    else
                    {
                        sNo = "GXXW" + iMax.ToString();
                    }
                }

                sSQL = " insert into T_ProcedureAction(PANo,PAName,ProcAct,PADesc,MAC,PrintLable,ScanMater,UpPack,UpBatch,TestItem,ScanDevice,IsFrOther,CheckDHR,TestItemType,EPath,ScanZKP,ChangeFA,TestIP,TestUser,TestPwd,Status,CompanyNo,InMan,Remark)" +
                       " values('" + sNo + "','" + Name + "','" + E + "','" + Item + "','" + D + "','" + A + "','" + B + "','" + C + "','" + F + "','" + MNo + "','" + MName + "','" + G + "','" + O + "','" + H + "','" + K + "','" + I + "', " +
                       " '" + J + "','" + L + "','" + M + "','" + N + "','启用','" + sComp + "','" + InMan + "','" + Remark + "') ";

                Message = "添加工序行为，行为编号：" + sNo + "，行为名称：" + Name + "。";
                LogType = "添加";
                ModuleName = P;
                TableName = "T_ProcedureAction";
                Condition = "PANo = '" + sNo + "' ";
                Column = "*";
            }
            else if (sFlag == "17")
            {
                sSQL = " update T_ProcedureAction set PAName='" + Name + "',ProcAct='" + E + "',PADesc='" + Item + "',PrintLable='" + A + "',ScanMater='" + B + "',UpPack='" + C + "',UpBatch='" + F + "',TestItem='" + MNo + "', " +
                       " ScanDevice='" + MName + "',MAC='" + D + "',IsFrOther='" + G + "',CheckDHR='" + O + "',TestItemType='" + H + "',EPath='" + K + "',ScanZKP='" + I + "',ChangeFA='" + J + "',TestIP='" + L + "',TestUser='" + M + "',TestPwd='" + N + "',Remark='" + Remark + "' " +
                       " where PANo='" + No + "' ";

                Message = "修改工序行为，行为编号：" + No + "。";
                LogType = "修改";
                ModuleName = P;
                TableName = "T_ProcedureAction";
                Condition = "PANo = '" + No + "' ";
                Column = "*";
            }
            else if (sFlag == "18")
            {
                sSQL = " delete T_ProcedureAction where PANo='" + No + "' ";

                Message = "删除工序行为，行为编号：" + No + "，行为名称：" + Name + "。";
                LogType = "删除";
                ModuleName = P;
                TableName = "T_ProcedureAction";
                Condition = "PANo = '" + No + "' ";
                Column = "*";
            }
            else if (sFlag == "20")  //新增员工对应作业单元  WNo,WName from T_WorkCenter where CompanyNo='' and Kind='作业单元' and WNo in ()
            {
                sSQL = " insert into T_UserWorkCenter(UserNo,UserName,WorkCenter,CenterDesc,Status,CompanyNo,InMan,Remark)" +
                       " select '" + No + "','" + Name + "',WNo,WName,'启用','" + sComp + "','" + InMan + "','" + Remark + "' from T_WorkCenter where CompanyNo='" + sComp + "' " +
                       " and Kind='作业单元' and WNo in " + A + " and WNo not in (select WorkCenter from T_UserWorkCenter where UserNo='" + No + "')  ";

                Message = "添加员工对应作业单元，用户编号：" + No + "，单元编号：" + A + "。";
                LogType = "添加";
                ModuleName = F;
            }
            else if (sFlag == "21")  //修改员工对应作业单元  
            {
                //sSQL = " update T_UserWorkCenter set FWName='" + Name + "',WName='" + MName + "',Kind='" + A + "',Remark='" + Remark + "' where FWNo='" + No + "' and WNo='" + MNo + "' ";

                Message = "修改员工对应作业单元，用户编号：" + No + "，单元编号：" + A + "。";
                LogType = "修改";
                ModuleName = F;
                TableName = "T_UserWorkCenter";
                Condition = "UserNo = '" + No + "' and WorkCenter = '" + A + "' ";
                Column = "*";
            }
            else if (sFlag == "22")  //删除员工对应作业单元
            {
                sSQL = " delete T_UserWorkCenter  where UserNo='" + No + "' and WorkCenter='" + A + "' ";

                Message = "删除员工对应作业单元，用户编号：" + No + "，单元编号：" + A + "。";
                LogType = "删除";
                ModuleName = F;
                TableName = "T_UserWorkCenter";
                Condition = "UserNo = '" + No + "' and WorkCenter = '" + A + "' ";
                Column = "*";
            }
            else if (sFlag == "23")  //启用员工对应作业单元
            {
                sSQL = " update T_UserWorkCenter set Status='启用' where UserNo='" + No + "' and WorkCenter='" + A + "' ";

                Message = "启用员工对应作业单元，用户编号：" + No + "，单元编号：" + A + "。";
                LogType = "启用";
                ModuleName = F;
            }
            else if (sFlag == "24")  //禁用员工对应作业单元
            {
                sSQL = " update T_UserWorkCenter set Status='禁用' where UserNo='" + No + "' and WorkCenter='" + A + "' ";

                Message = "禁用员工对应作业单元，用户编号：" + No + "，单元编号：" + A + "。";
                LogType = "禁用";
                ModuleName = F;
            }
            else if (sFlag == "25")  //新增异常代码
            {
                sSQL = " insert into T_ExceptionCode(ECode,CompanyNo,EName,Status,InMan,Remark)" +
                       " values('" + No + "','" + sComp + "','" + Name + "','','" + InMan + "','" + Remark + "') ";
            }
            else if (sFlag == "26")  //修改员工对应作业单元  -- 不用修改
            {
                sSQL = " update T_ExceptionCode set EName='" + Name + "',Remark='" + Remark + "' where ECode='" + No + "' and CompanyNo='" + sComp + "' ";
            }
            else if (sFlag == "27")  //删除员工对应作业单元
            {
                sSQL = " delete T_ExceptionCode  where ECode='" + No + "' and CompanyNo='" + sComp + "' ";
            }
            else if (sFlag == "28-1")  //测试项库：新增测试项
            {
                sMaxNo = DBHelper.GetMaxNo("T_TestItemBaseInfo where TechNo='" + No + "' and ProcedureNo='" + Item + "' ", "SNo");//  
                if (sMaxNo == "")
                {
                    sNum = "1";
                }
                else
                {
                    iMax = int.Parse(sMaxNo) + 1;
                    sNum = iMax.ToString();
                }

                sSQL = " insert into T_TestItemBaseInfo(TechNo,ProcedureNo,ProcedureName,SpecNo,SNo,Model,NameCH,DescCH,StandardCH,ThisValue,IncludeOne,ToValue,IncludeTwo,RangeKind,ValueKind,SpecUnit,Status,CompanyNo,InMan,Remark) " +
                       " values('" + No + "','" + Item + "','" + Name + "','" + J + "','" + sNum + "','" + K + "','" + A + "','" + B + "','" + C + "','" + F + "','" + H + "','" + G + "','" + I + "','" + E + "','" + D + "','" + M + "','启用','" + sComp + "','" + InMan + "','') ";

                Message = "添加测试项，编号：" + J + "，工序：(" + Item + ")" + Name + "，产品代号：" + No + "。";
                LogType = "添加";
                ModuleName = P;
                TableName = "T_TestItemBaseInfo";
                Condition = "TechNo = '" + No + "' and ProcedureNo = '" + Item + "' and SpecNo = '" + J + "' ";
                Column = "*";
            }
            else if (sFlag == "28-2")  //测试项库：测试测试项
            {
                sSQL = " update T_TestItemBaseInfo set NameCH='" + A + "',DescCH='" + B + "',StandardCH='" + C + "',ThisValue='" + F + "',IncludeOne='" + H + "',ToValue='" + G + "',IncludeTwo='" + I + "',RangeKind='" + E + "',ValueKind='" + D + "' ,Model='" + K + "', " +
                       " ProcedureNo='" + Item + "',ProcedureName='" + Name + "',SpecUnit='" + M + "' " +
                       " where TechNo='" + No + "' and ProcedureNo='" + MNo + "' and SpecNo='" + J + "' ";

                Message = "修改测试项，编号：" + J + "。";
                LogType = "修改";
                ModuleName = P;
                TableName = "T_TestItemBaseInfo";
                Condition = "TechNo = '" + No + "' and ProcedureNo = '" + Item + "' and SpecNo = '" + J + "' ";
                Column = "*";
            }
            else if (sFlag == "28-3")  //测试项库：删除测试项 
            {
                sSQL = " delete T_TestItemBaseInfo where TechNo='" + No + "' and ProcedureNo='" + Item + "' and SpecNo='" + J + "' ";

                Message = "删除测试项，编号：" + J + "，产品代号：" + No + "，工序：(" + Item + ")" + Name + "。";
                LogType = "删除";
                ModuleName = P;
                TableName = "T_TestItemBaseInfo";
                Condition = "TechNo = '" + No + "' and ProcedureNo = '" + Item + "' and SpecNo = '" + J + "' ";
                Column = "*";
            }
            else if (sFlag == "28-4")  //测试项库：删除测试项 - 多个
            {
                sSQL = " delete T_TestItemBaseInfo where SpecNo in (" + J + ") ";

                Message = "批量删除测试项，编号：" + J + "。";
                LogType = "删除";
                ModuleName = P;
            }











            List<string> links = new List<string>();
            links.Add(sCHStr); // 日志
            links.Add(sSQLs);  // 先更新历史版本，在插入
            links.Add(sSQL);
            links.Add(sSQL1);


            try
            {
                //删除、修改操作需要获取操作前的数据
                if (LogType == "删除" || LogType == "修改")
                {
                    OldValue = LogHelper.GetValue(TableName, Condition, Column);
                }

                //DBHelper.ExecuteCommand(sSQL);
                iFlag = DBHelper.ExecuteSqlTranStr(links);

                //新增、修改操作需要获取操作后的数据
                if (LogType == "添加" || LogType == "修改")
                {
                    NewValue = LogHelper.GetValue(TableName, Condition, Column);
                }

                //记录操作日志
                if (!iFlag.Contains("Err"))
                {
                    LogHelper.LogInfo(LogType, Message, IP, sComp, InMan, ModuleName, NewValue, OldValue);
                }
                //记录错误日志
                else
                {
                    Message = Message + "法生错误，错误原因：" + iFlag;
                    sSQL = String.Join("/", links.ToArray());
                    LogHelper.LogError(LogType, Message, IP, sComp, InMan, ModuleName, sSQL);
                }

                sstr = "SQ_" + iMax.ToString();
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }



        //获取样本量字码
        public static DataTable GetSampleSizeCode(int limit, int page, string Flag, string InMan)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@lnNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnItem", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnStatus", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,10),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
            parameters[0].Value = "";
            parameters[1].Value = "";
            parameters[2].Value = "";
            parameters[3].Value = "";
            parameters[4].Value = "";
            parameters[5].Value = "";
            parameters[6].Value = "";
            parameters[7].Value = "";
            parameters[8].Value = "";
            parameters[9].Value = "";
            parameters[10].Value = "";
            parameters[11].Value = "";
            parameters[12].Value = limit;
            parameters[13].Value = page;
            parameters[14].Value = InMan;
            parameters[15].Value = Flag;
            parameters[16].Value = "";

            DataSet DS = DBHelper.RunProcedureForDS("P_GetTechInfoForPage", parameters);
            DataTable dt = DS.Tables[0];
            return dt;
        }

        public static string OPSampleSizeCodeInfo(string sSCNo, string sStrictness, string sBatch, string sBatch_start, string sBatch_end, string sInspection_level, string sSample_code, string sComp, string sMan, string sRemark, string sPresentOrder, string sOPFlag, string sFlag)
        {
            string sql = "";
            string newSCNo = "";
            string newPresentOrder = "";
            DataTable dt = null;
            string result = "";
            if (sFlag == "1")//添加
            {
                var sNum = "";
                var sMaxNo = DBHelper.GetMaxNo("T_SampleSizeCode", "SeqNo");
                if (sMaxNo == "")
                {
                    sNum = "1";
                }
                else
                {
                    sNum = (int.Parse(sMaxNo) + 1).ToString();
                }

                sql = "insert into T_SampleSizeCode(SCNo,Batch,IntervalHead,IntervalTail,InspectLevel,SampleCode,SeqNo,Status,CompanyNo,InMan,InDate,Remark) values('" + sSCNo + "','" + sBatch + "','" + sBatch_start + "','" + sBatch_end + "','" + sInspection_level + "','" + sSample_code + "'," + sNum + ",'正常','" + sComp + "','" + sMan + "',default,'" + sRemark + "')";
                int num1 = DBHelper.ExecuteCommand(sql);
                result = num1.ToString();
            }
            else if (sFlag == "2")//上下移动
            {
                if (sOPFlag == "UP")//上
                {
                    sql = "select top 1 SeqNo,SCNO from T_SampleSizeCode where  SeqNo < " + sPresentOrder + " order by SeqNo desc";
                    dt = DBHelper.GetDataTable(sql);
                    if (dt.Rows.Count > 0)
                    {
                        newSCNo = dt.Rows[0]["SCNO"].ToString();
                        newPresentOrder = dt.Rows[0]["SeqNo"].ToString();
                    }
                    else
                    {
                        newPresentOrder = sPresentOrder;
                    }
                }
                else if (sOPFlag == "DOWN")//下
                {
                    sql = "select top 1 SeqNo,SCNO from T_SampleSizeCode where  SeqNo > " + sPresentOrder + " order by SeqNo";
                    dt = DBHelper.GetDataTable(sql);
                    if (dt.Rows.Count > 0)
                    {
                        newSCNo = dt.Rows[0]["SCNO"].ToString();
                        newPresentOrder = dt.Rows[0]["SeqNo"].ToString();
                    }
                    else
                    {
                        newPresentOrder = sPresentOrder;
                    }
                }

                int num1 = DBHelper.ExecuteCommand("update T_SampleSizeCode set SeqNo=" + newPresentOrder + " where SCNo='" + sSCNo + "'");
                int num2 = DBHelper.ExecuteCommand("update T_SampleSizeCode set SeqNo=" + sPresentOrder + " where SCNo='" + newSCNo + "'");

                result = (num1 + num2).ToString();
            }
            else if (sFlag == "3")//删除
            {
                sql = "delete from T_SampleSizeCode where SCNo='" + sSCNo + "'";
                int num1 = DBHelper.ExecuteCommand(sql);
                result = num1.ToString();
            }

            return result;

        }

        //获取抽样标准
        public static DataTable GetSamplingStd(int limit, int page, string Flag, string InMan)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@lnNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnItem", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnStatus", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,10),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
            parameters[0].Value = "";
            parameters[1].Value = "";
            parameters[2].Value = "";
            parameters[3].Value = "";
            parameters[4].Value = "";
            parameters[5].Value = "";
            parameters[6].Value = "";
            parameters[7].Value = "";
            parameters[8].Value = "";
            parameters[9].Value = "";
            parameters[10].Value = "";
            parameters[11].Value = "";
            parameters[12].Value = limit;
            parameters[13].Value = page;
            parameters[14].Value = InMan;
            parameters[15].Value = Flag;
            parameters[16].Value = "";

            DataSet DS = DBHelper.RunProcedureForDS("P_GetTechInfoForPage", parameters);
            DataTable dt = DS.Tables[0];
            return dt;
        }

        public static string OPSamplingStd(string sSSNo, string sSampleCode, string sAql, string sStringency, string sSampleSize, string sRNum, string sBNum, string sComp, string sMan, string sRemark, string sPresentOrder, string sOPFlag, string sFlag)
        {
            string sql = "";
            string newSSNo = "";
            string newPresentOrder = "";
            DataTable dt = null;
            string result = "";
            if (sFlag == "1")//添加
            {
                var sNum = "";
                var sMaxNo = DBHelper.GetMaxNo("T_SamplingStd", "SeqNo");
                if (sMaxNo == "")
                {
                    sNum = "1";
                }
                else
                {
                    sNum = (int.Parse(sMaxNo) + 1).ToString();
                }
                sql = "insert into T_SamplingStd(SSNo,SampleCode,Aql,Stringency,SampleSize,RNum,BNum,SeqNo,Status,CompanyNo,InMan,InDate,Remark) values('" + sSSNo + "','" + sSampleCode + "','" + sAql + "','" + sStringency + "','" + sSampleSize + "','" + sRNum + "','" + sBNum + "'," + sNum + ",'正常','" + sComp + "','" + sMan + "',default,'" + sRemark + "')";
                int num1 = DBHelper.ExecuteCommand(sql);
                result = num1.ToString();
            }
            else if (sFlag == "2")//上下移动
            {
                if (sOPFlag == "UP")//上
                {
                    sql = "select top 1 SeqNo,SSNO from T_SamplingStd where  SeqNo < " + sPresentOrder + " order by SeqNo desc";
                    dt = DBHelper.GetDataTable(sql);
                    if (dt.Rows.Count > 0)
                    {
                        newSSNo = dt.Rows[0]["SSNO"].ToString();
                        newPresentOrder = dt.Rows[0]["SeqNo"].ToString();
                    }
                    else
                    {
                        newPresentOrder = sPresentOrder;
                    }
                }
                else if (sOPFlag == "DOWN")//下
                {
                    sql = "select top 1 SeqNo,SSNO from T_SamplingStd where  SeqNo > " + sPresentOrder + " order by SeqNo";
                    dt = DBHelper.GetDataTable(sql);
                    if (dt.Rows.Count > 0)
                    {
                        newSSNo = dt.Rows[0]["SSNO"].ToString();
                        newPresentOrder = dt.Rows[0]["SeqNo"].ToString();
                    }
                    else
                    {
                        newPresentOrder = sPresentOrder;
                    }
                }

                int num1 = DBHelper.ExecuteCommand("update T_SamplingStd set SeqNo=" + newPresentOrder + " where SSNO='" + sSSNo + "'");
                int num2 = DBHelper.ExecuteCommand("update T_SamplingStd set SeqNo=" + sPresentOrder + " where SSNO='" + newSSNo + "'");

                result = (num1 + num2).ToString();
            }
            else if (sFlag == "3")//删除
            {
                sql = "delete from T_SamplingStd where SSNo='" + sSSNo + "'";
                int num1 = DBHelper.ExecuteCommand(sql);
                result = num1.ToString();
            }

            return result;
        }


        public static DataTable GetSamplingPlan(int limit, int page, string sMan, string SPNo, string SPName, string Flag)
        {
            if (Flag == "121")
            {
                SqlParameter[] parameters = {
                    new SqlParameter("@lnNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnItem", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnStatus", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,10),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
                parameters[0].Value = SPNo;
                parameters[1].Value = SPName;
                parameters[2].Value = "";
                parameters[3].Value = "";
                parameters[4].Value = "";
                parameters[5].Value = "";
                parameters[6].Value = "";
                parameters[7].Value = "";
                parameters[8].Value = "";
                parameters[9].Value = "";
                parameters[10].Value = "";
                parameters[11].Value = "";
                parameters[12].Value = limit;
                parameters[13].Value = page;
                parameters[14].Value = sMan;
                parameters[15].Value = Flag;
                parameters[16].Value = "";

                DataSet DS = DBHelper.RunProcedureForDS("P_GetTechInfoForPage", parameters);
                DataTable dt = DS.Tables[0];
                return dt;
            }
            else
            {
                //获取方案详情
                return DBHelper.GetDataTable("select '0' numCount,* from T_SamplingPlan where SPNo='" + SPNo + "'");
            }
        }

        public static string OPSamplingPlan(string SPNo, string SPName, string Type, string InspectLevel, string Stringency, string Aql, string Comp, string InMan, string Flag, string OPFlag)
        {
            string sql = "";
            string result = "";
            if (Flag == "1")
            {
                DataTable dt = SamplingPlan(InspectLevel, Stringency, Aql);
                if (dt.Rows.Count == 0)
                {
                    //等于0说明没有找到对应的信息
                    return "0";
                }

                if (OPFlag == "Add")
                {
                    int num = 0;
                    foreach (DataRow item in dt.Rows)
                    {
                        var sNum = MaxNo();
                        sql = "insert into T_SamplingPlan(SPNo,SPName,[Type],InspectLevel,Stringency,Aql,SeqNo,Batch,IntervalHead,IntervalTail,SampleCode,AqlTwo,StringencyTwo,SampleSize,RNum,BNum,Status,CompanyNo,InMan,InDate) " +
                              "values('" + SPNo + "','" + SPName + "','" + Type + "','" + InspectLevel + "','" + Stringency + "','" + Aql + "'," + sNum + ",'" + item["Batch"] + "','" + item["IntervalHead"] + "','" + item["IntervalTail"] + "', " +
                              "'" + item["SampleCode"] + "','" + item["Aql"] + "','" + item["Stringency"] + "','" + item["SampleSize"] + "','" + item["RNum"] + "','" + item["BNum"] + "','启用','" + Comp + "','" + InMan + "',default)";
                        num += DBHelper.ExecuteCommand(sql);
                    }
                    result = num.ToString();
                }
                else if (OPFlag == "Edit")
                {
                    int num = 0;
                    foreach (DataRow item in dt.Rows)
                    {
                        sql = "update T_SamplingPlan set SPName='" + SPName + "',[Type]='" + Type + "',InspectLevel='" + InspectLevel + "',Stringency='" + Stringency + "',Aql='" + Aql + "',SampleCode='" + item["SampleCode"] + "',AqlTwo='" + item["Aql"] + "',StringencyTwo='" + item["Stringency"] + "', " +
                              "SampleSize='" + item["SampleSize"] + "',IntervalHead='" + item["IntervalHead"] + "',IntervalTail='" + item["IntervalTail"] + "',RNum='" + item["RNum"] + "',BNum='" + item["BNum"] + "' " +
                              "where SPNo='" + SPNo + "' and Batch = '" + item["Batch"] + "'";

                        num += DBHelper.ExecuteCommand(sql);
                    }
                    result = num.ToString();
                }
            }
            else if (Flag == "2")
            {
                sql = "delete from T_SamplingPlan where SPNo='" + SPNo + "'";
                int num = DBHelper.ExecuteCommand(sql);
                result = num.ToString();
            }
            return result;
        }

        /// <summary>
        /// 更具检验水平、严格度、接受质量限 获取相应的信息
        /// </summary>
        /// <returns></returns>
        public static DataTable SamplingPlan(string InspectLevel, string Stringency, string Aql)
        {
            string sSQL = string.Empty;
            sSQL = "select  a.SampleCode,Aql,a.Stringency,SampleSize,RNum,BNum,b.Batch,b.IntervalHead,b.IntervalTail from T_SamplingStd a " +
                  "left join T_SampleSizeCode b  on a.SampleCode=b.SampleCode where  InspectLevel='" + InspectLevel + "' and a.Stringency='" + Stringency + "' and Aql='" + Aql + "'";
            return DBHelper.GetDataTable(sSQL);
        }

        //获取最大编号
        public static string MaxNo()
        {
            var sMaxNo = DBHelper.GetMaxNo("T_SamplingPlan", "SeqNo");
            if (sMaxNo == "")
            {
                return "1";
            }
            else
            {
                return (int.Parse(sMaxNo) + 1).ToString();
            }
        }


        public static List<DataTable> GetExportConditions(string sNo, string A, string B, string C, string sInMan, string Comp, string Flag)
        {
            List<DataTable> list = new List<DataTable>();
            string sSQL = string.Empty;
            string sSavePath = string.Empty;

            sSQL = "select ProductNo, ProcedureName,ProcedureNo,ProcedureVer,FlowOrder,CompanyNo from T_ProductFlow where ProductNo='" + sNo + "' and ProcedureNo+ProcedureVer='" + A + "' ";

            list.Add(DBHelper.GetDataTable(sSQL));
            return list;
        }


        public static string InitializeMater(string sNo, string sSNo, string sMNo, string A, string B, string C, string D, string E, string F, string G, string sRemark, string sMan, string sFlag, string sComp, string IP)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@lnNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnSN", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnE", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnF", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnG", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRemark", SqlDbType.NVarChar,500),
                    new SqlParameter("@lnMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnFlag", SqlDbType.NVarChar,10),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
            parameters[0].Value = sNo;  // 工单号
            parameters[1].Value = sSNo;
            parameters[2].Value = sMNo;
            parameters[3].Value = A;
            parameters[4].Value = B;
            parameters[5].Value = C;
            parameters[6].Value = D;
            parameters[7].Value = E;
            parameters[8].Value = F;
            parameters[9].Value = G;
            parameters[10].Value = sRemark;
            parameters[11].Value = sMan;
            parameters[12].Value = sFlag;
            parameters[13].Value = "";


            string Message = sFlag == "BOM" ? "批量更新追溯物料，产品代号：" + A + "。" : "批量更新工艺路线，产品代号：" + A + "。";
            try
            {
                DataSet DS = DBHelper.RunProcedureForDS("P_InitializeMater", parameters);
                DataTable dt = DS.Tables[0];

                string backInfo = dt.Rows[0]["BackInfo"].ToString();
                string materNo = dt.Rows[0]["MaterNo"].ToString();

                if (backInfo == "物料获取数据成功")
                {
                    LogHelper.LogInfo("修改", Message + "物料编码：" + materNo, IP, sComp, sMan, G);
                    return "Success";
                }
                else
                {
                    LogHelper.LogError("修改", Message + "物料编码：" + materNo + "，发生错误，错误原因：" + backInfo, IP, sComp, sMan, G);
                    return "Error";
                }
            }
            catch (Exception ex)
            {
                LogHelper.LogError("修改", Message + "发生错误，错误原因：" + ex.Message.ToString(), IP, sComp, sMan, G);
                return "Error";
            }
        }


























    }
}
