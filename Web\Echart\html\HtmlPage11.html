﻿<!--<!DOCTYPE html
          PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title></title>
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/echarts.js"></script>
    <script>
        $(function () {
            init()
        })
        function init() {
            const data = sessionStorage.getItem("KB0008");
            let dayProcedureData = []; //当天工序产量
            var dayOrderProcedureData = [] //当天工单各工序产量
            var weekOrderProcedureData = [] //近七天工各工序产量

            // 校验并解析数据
            if (data) {
                try {
                    const jsonData = JSON.parse(data);
                    dayProcedureData = jsonData[0] || [];
                    dayOrderProcedureData = jsonData[1] || [];
                    weekOrderProcedureData = jsonData[2] || [];
                } catch (error) {
                    console.error("数据解析失败:", error);
                }
            }

            DayProcedureEcharts(dayProcedureData, 'dayProcedureEcharts', '');

            WeekOrderProcedureList(dayOrderProcedureData,"DayOrderProcedureList")

            WeekOrderProcedureList(weekOrderProcedureData, "WeekOrderProcedureList")
        }


        function DayProcedureEcharts(data,chartId,title) {
            if (!data || data.length == 0) {
                console.warn(`没有数据提供给图表 ${chartId}`);
                return;
            }

            const procedureName = data.map(item => item.ProcedureName)

            const procedureCount = data.map(item => item.ProcedureCount)

            const chart = echarts.init(document.getElementById(chartId));

            chart.setOption({
                backgroundColor: 'white',
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    top: '8%',
                    right: '2%',
                    left: '5%',
                    bottom: '9%'
                },
                xAxis: [{
                    type: 'category',
                    data: procedureName,
                    axisTick: {
                        show: false
                    },
                }],
                yAxis: [{
                    axisLabel: {
                        fontWeight: "bold",
                        formatter: '{value}个' // 可根据需要添加单位
                    },
                    axisLine: {
                        show: true
                    },
                }],
                series: [{
                    type: 'line',
                    data: procedureCount,
                    barWidth: '20px',
                    showSymbol: true,
                    symbol: 'circle',
                    symbolSize: 10,
                    itemStyle: {
                        color:"#15181d"
                    },
                    label: {
                        show: true,
                        fontWeight: "bold",
                        position: 'top',
                        formatter: (params) => params.value == 0 ? '' : params.value + '个'
                    }
                }]
            })

            // 窗口调整时重新渲染图表
            $(window).resize(() => chart.resize());
        }


        function WeekOrderProcedureList(data, tableId) {
            if (!data || data.length === 0) {
                console.warn(`没有数据提供给图表`);
                return;
            }

            let procedures = [...new Set(data.map(item => item.ProcedureName))];
            let orders = [...new Set(data.map(item => item.OrderNo))];

            // 使用对象嵌套结构提升内存效率
            const countMap = data.reduce((acc, item) => {
                acc[item.OrderNo] = acc[item.OrderNo] || {};
                acc[item.OrderNo][item.ProcedureName] = item.ProcedureCount;
                return acc;
            }, {});

            // 使用预存长度避免动态扩展
            const result = new Array(orders.length + 1);
            result[0] = ['工单', ...procedures];
            orders.forEach((orderNo, index) => {
                const row = new Array(procedures.length + 1);
                row[0] = orderNo;
                procedures.forEach((procedure, i) => {
                    row[i + 1] = (countMap[orderNo]?.[procedure]) || 0;
                });
                result[index + 1] = row;
            });

            renderScrollableTable(result, tableId);
        }

        function renderScrollableTable(data, tableId) {
            const thead = document.querySelector('#' + tableId + ' thead');
            const tbody = document.querySelector('#' + tableId + ' tbody');

            // 清空现有内容
            thead.innerHTML = '';
            tbody.innerHTML = '';

            // 创建表头
            const headerRow = document.createElement('tr');
            data[0].forEach(header => {
                const th = document.createElement('th');
                th.textContent = header;
                headerRow.appendChild(th);
            });
            thead.appendChild(headerRow);

            // 创建数据行
            data.slice(1).forEach(rowData => {
                const tr = document.createElement('tr');
                rowData.forEach(cell => {
                    const td = document.createElement('td');
                    td.textContent = cell;
                    tr.appendChild(td);
                });
                tbody.appendChild(tr);
            });

            // 同步列宽（重要！）
            //setTimeout(() => {
            //    const headerCells = thead.querySelectorAll('th');
            //    const bodyCells = tbody.querySelector('tr').cells;

            //    Array.from(headerCells).forEach((th, index) => {
            //        th.style.minWidth = `${bodyCells[index].offsetWidth}px`;
            //    });
            //}, 0);
        }
    </script>

    <style>
        * {
            margin: 0;
            padding: 0;
        }

        body {
            background: #F8F9FC;
            color: #1A1D26;
        }

        /* 看板容器 */
        .dashboard {
            height: calc(100vh - 20px);
            padding: 10px;
        }

        /* 上部区域 */
        .upper-section {
            display: flex;
            height: 40%;
        }

        .lower-section {
            margin-top: 10px;
            height: calc(60% - 10px);
        }

        .upper-section > .card {
            width: calc(50% - 5px);
        }

        .card-right {
            margin-left: 5px;
        }

        .card-left {
            margin-right: 5px;
        }

        /* 卡片通用样式 */
        .card {
            background: #FFFFFF;
            border-radius: var(--radius-lg);
            box-shadow: 0 4px 12px rgba(23, 43, 77, 0.08), 0 8px 24px rgba(23, 43, 77, 0.06);
            flex-direction: column;
            height: 100%
        }


        /* 卡片头部 */
        .card-header {
            padding: 10px 20px;
            border-bottom: 1px solid #E4E6EC;
            display: flex;
            justify-content: space-between;
            align-items: center;
            text-align: center;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #1A1D26;
        }

        /* 图表容器 */
        .chart-container {
           height:calc(100% - 65px);
           padding:10px;

        }

        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

            .data-table thead {
                position: sticky;
                top: 0;
                background: #FFFFFF;
                z-index: 1;
            }

            .data-table th,
            .data-table td {
                padding: 10px 10px;
                text-align: left;
                border-bottom: 1px solid #E4E6EC;
            }

            .data-table th {
                font-weight: 600;
                background: #F8F9FC;
            }

            .data-table tr:last-child td {
                border-bottom: none;
            }

        /* 滚动容器 */
        .scroll-container {
            height: calc(100% - 55px);
            overflow: auto;
        }
    </style>
</head>


<body>
    <div class="dashboard">
        <div class="upper-section">
            <div class="card card-left">
                <div class="card-header">
                    <h2 class="card-title">当前工序产量</h2>
                </div>
                <div class="chart-container">
                    <div id="dayProcedureEcharts" style="width: 100%; height: 100%;"></div>
                </div>
            </div>

            <div class="card card-right">
                <div class="card-header">
                    <h2 class="card-title">当天工单产量</h2>
                </div>
                <div class="scroll-container">
                    <table class="data-table" id="DayOrderProcedureList">
                        <thead></thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="lower-section">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">近七天工单产量</h2>
                </div>
                <div class="scroll-container">
                    <table class="data-table" id="WeekOrderProcedureList">
                        <thead></thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>

</html>-->


<!DOCTYPE html
          PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title></title>
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/echarts.js"></script>

    <script>
        $(function () {
            init()
        })
        function init() {
            const data = sessionStorage.getItem("KB0008");
            let dayProcedureData = []; //当天工序产量
            var dayOrderProcedureData = [] //当天工单各工序产量
            var weekOrderProcedureData = [] //近七天工各工序产量

            // 校验并解析数据
            if (data) {
                try {
                    const jsonData = JSON.parse(data);
                    dayProcedureData = jsonData[0] || [];
                    dayOrderProcedureData = jsonData[1] || [];
                    weekOrderProcedureData = jsonData[2] || [];
                } catch (error) {
                    console.error("数据解析失败:", error);
                }
            }

            DayProcedureEcharts(dayProcedureData, 'dayProcedureEcharts', '');

            WeekOrderProcedureList(dayOrderProcedureData, "DayOrderProcedureList")

            WeekOrderProcedureList(weekOrderProcedureData, "WeekOrderProcedureList")
        }


        function DayProcedureEcharts(data, chartId, title) {
            if (!data || data.length == 0) {
                console.warn(`没有数据提供给图表 ${chartId}`);
                return;
            }

            const procedureName = data.map(item => item.ProcedureName)

            const procedureCount = data.map(item => item.ProcedureCount)

            const chart = echarts.init(document.getElementById(chartId));

            chart.setOption({
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    top: '10%',
                    right: '2%',
                    left: '5%',
                    bottom: '9%'
                },
                xAxis: {
                    type: 'category',
                    data: procedureName,
                    axisLine: {
                        lineStyle: {
                            color: '#5bc0de'  // 设置 y 轴线颜色为黑色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#fff',
                            fontWeight: "bold"
                        }
                    },
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        show: true,  // 是否显示坐标轴轴线。
                        lineStyle: {
                            color: '#5bc0de'  // 设置 x 轴线颜色为黑色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#fff',
                            fontWeight: "bold"
                        }
                    },
                    minInterval: 1, // 设置成1保证坐标轴分割刻度显示成整数
                    axisTick: {
                        show: true,  // 是否显示坐标轴刻度。
                    },
                    splitLine: {
                        show: false
                    },
                },

                series: [{
                    type: 'line',
                    data: procedureCount,
                    barWidth: '20px',
                    showSymbol: true,
                    symbol: 'circle',
                    symbolSize: 10,
                    itemStyle: {
                        normal: {
                            color: '#5bc0de', //改变折线点的颜色

                        }
                    },
                    label: {
                        fontSize: "12",
                        show: true,
                        color: "white",
                        fontWeight: "bold"
                    },
                }]
            })

            // 窗口调整时重新渲染图表
            $(window).resize(() => chart.resize());
        }


        function WeekOrderProcedureList(data, tableId) {
            if (!data || data.length === 0) {
                console.warn(`没有数据提供给图表`);
                return;
            }

            let procedures = [...new Set(data.map(item => item.ProcedureName))];
            let orders = [...new Set(data.map(item => item.OrderNo))];

            // 使用对象嵌套结构提升内存效率
            const countMap = data.reduce((acc, item) => {
                acc[item.OrderNo] = acc[item.OrderNo] || {};
                acc[item.OrderNo][item.ProcedureName] = item.ProcedureCount;
                return acc;
            }, {});

            // 使用预存长度避免动态扩展
            const result = new Array(orders.length + 1);
            result[0] = ['工单', ...procedures];
            orders.forEach((orderNo, index) => {
                const row = new Array(procedures.length + 1);
                row[0] = orderNo;
                procedures.forEach((procedure, i) => {
                    row[i + 1] = (countMap[orderNo]?.[procedure]) || 0;
                });
                result[index + 1] = row;
            });

            renderScrollableTable(result, tableId);
        }

        function renderScrollableTable(data, tableId) {
            const thead = document.querySelector('#' + tableId + ' thead');
            const tbody = document.querySelector('#' + tableId + ' tbody');

            // 清空现有内容
            thead.innerHTML = '';
            tbody.innerHTML = '';

            // 创建表头
            const headerRow = document.createElement('tr');
            data[0].forEach(header => {
                const th = document.createElement('th');
                th.textContent = header;
                headerRow.appendChild(th);
            });
            thead.appendChild(headerRow);

            // 创建数据行
            data.slice(1).forEach(rowData => {
                const tr = document.createElement('tr');
                rowData.forEach(cell => {
                    const td = document.createElement('td');
                    td.textContent = cell;
                    tr.appendChild(td);
                });
                tbody.appendChild(tr);
            });
        }
    </script>

    <style>
        * {
            margin: 0;
            padding: 0;
        }

        .container-div {
            height: 100vh;
            width: 100%;
            background: #15181d;
            overflow: hidden;
            position: relative;
            color: #ffffff;
        }

        .container-top {
            display: flex;
            height: 44%;
        }

        .container-bottom {
            margin-top: 0.5%;
            height: 53%;
        }

        .container-top-left {
            width: 50%;
            height: 100%;
            margin-right: 0.5%;
        }

        .container-top-right {
            width: 50%;
            height: 100%;
        }

        .left {
            float: left;
        }

        .div_any01 {
            width: 23%;
            margin-right: 2%;
        }

        .div_any_child {
            width: 100%;
            height: 330px;
            box-shadow: -5px 0px 10px #034c6a inset, 0px -10px 10px #034c6a inset, 5px 0px 10px #034c6a inset, 0px 10px 10px #034c6a inset;
            border: 1px solid #034c6a;
            box-sizing: border-box;
            position: relative;
            margin-top: 15px;
        }

        .div_any_title {
            background-color: #034c6a;
            border-radius: 18px;
            position: absolute;
            height: 25px;
            width: 60%;
            top: -15px;
            color: #ffffff;
            font-weight: bold;
            font-size: 16px;
            left: 20%;
            line-height: 25px;
            text-align: center;
        }

        .div_any_table {
            margin: 2.5%;
            height: 91%;
            width: 95%;
            overflow: auto;
            border: 1px solid #5bc0de;
        }


            .div_any_table th, .div_any_table td {
                text-align: center;
            }

            /* 自定义滚动条样式 */
            .div_any_table::-webkit-scrollbar {
                display: none
            }

        .div_any_table-bottom {
            margin: 1%;
            height: 92%;
            width: 98%;
            overflow: auto;
            border: 1px solid #5bc0de;
        }

            .div_any_table-bottom > table {
                width: 100%;
                height: 100%;
                border-collapse: collapse;
                overflow-x: scroll;
            }

            .div_any_table-bottom th, .div_any_table-bottom td {
                text-align: center;
            }

            /* 自定义滚动条样式 */
            .div_any_table-bottom::-webkit-scrollbar {
                display: none
            }

        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            color: white;
            font-size: 15px;
        }

            .data-table thead {
                position: sticky;
                top: 0;
                background: #034c6a;
                z-index: 1;
            }

            .data-table td {
                padding: 5px 5px;
                border-bottom: 1px solid #5bc0de;
            }

            .data-table th {
                padding: 5px 5px;
                font-weight: bold;
            }


            .data-table tr:last-child td {
                border-bottom: none;
            }
    </style>
</head>


<body>
    <div class="container-div">
        <div class="container-top">
            <div class="container-top-left">
                <div class="div_any01" style="width:100%;height:96%">
                    <div class="div_any_child" style="height: 100%;">
                        <div class="div_any_title">当天工序产量</div>
                        <div id="dayProcedureEcharts" style="width:100%;height:100%"></div>
                    </div>
                </div>
            </div>
            <div class="container-top-right">
                <div class="div_any01" style="width:100%;height:96%">
                    <div class="div_any_child" style="height: 100%;">
                        <div class="div_any_title">当天工单产量</div>
                        <div class="div_any_table">
                            <table id="DayOrderProcedureList" class="data-table">
                                <thead></thead>
                                <tbody></tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div class="container-bottom">
            <div class="left div_any01" style="width:100%;height:100%">
                <div class="div_any_child" style="height: 100%;">
                    <div class="div_any_title">近七天工单产量</div>
                    <div class="div_any_table-bottom">
                        <table id="WeekOrderProcedureList" class="data-table">
                            <thead></thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>