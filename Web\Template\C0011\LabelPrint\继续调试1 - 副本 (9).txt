

select a.<PERSON><PERSON>, a.<PERSON>,a<PERSON>,a<PERSON>,b.<PERSON>,b.<PERSON>,b.<PERSON>,c.<PERSON>,c.<PERSON>,c.<PERSON>,d.<PERSON>, 
e.<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,e.<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,e.<PERSON>,e.<PERSON>,e.<PERSON>,e.<PERSON><PERSON><PERSON>,e.<PERSON>,e.<PERSON>,f.T<PERSON>,f.TPName 
from T_PrePrintSeriel a join T_SerielInfo b on a.SerielNo=b.SerielNo join T_ProductFlow c on b.MaterNo=c.ProductNo 
join T_ProcedureAction d on c.ConditionNo=d.<PERSON>No join T_ProductAndLabel e on c.ProductNo=e.PMater<PERSON>o and c.ProcedureNo=e.Procedure<PERSON>o 
join T_LabelTemplate f on e.TPNo=f.TPNo and e.TPVer=f.TPVer 
where f.Status='启用' and a.Flag='N' and e.AutoPrint='是' and c.WorkUnit like '%" + sWNo + "%'

select * from T_OrderInfo where OrderNo='WO22030' 2023-02-25 10:01:07.880

select *  from  T_PrePrintSeriel WHERE SerielNo= 'JF50C122002347'  JF50C122002857
select *  from  T_SerielInfo WHERE SerielNo= 'JF50C122002857' WA22020	DS50-30-00658
select *  from T_OrderSerial  WHERE SerialNo= 'JF50C122002857' 2023-02-28 09:53:14.110
select *  from T_OrderSerial  WHERE SerialNo like 'JF50C122002300%'  2023-02-28 09:13:00.330
select *  from T_SerialBaseFlow where SerialNo='JF50C122002857'  2023-02-28 09:53:14.007
select *  from T_SerialBOM where SerialNo='JF50C122002857' 
--update T_OrderSerial set SerialNo= 'JF50C122002956w' WHERE SerialNo= 'JF50C122002956'


update T_SerielInfo set OrderNo=b.OrderNo,MaterNo=b.MaterNo,MaterName=b.MaterName,Model=b.MaterSpec,Status='' 
from T_SerielInfo a join (select OrderNo,MaterNo,MaterName,MaterSpec,CompanyNo from T_OrderInfo where OrderNo='WO22030') b on a.CompanyNo=b.CompanyNo 
where a.SerielNo in (select top 1 a.SerielNo from T_PrePrintSeriel a join T_SerielInfo b on a.SerielNo=b.SerielNo where a.Flag='N' 
           and a.SerielNo not in (select SerialNo from T_OrderSerial where OrderNo='WO22030') order by a.SerielNo) 

select* from  T_SerielInfo a 
join (select OrderNo,MaterNo,MaterName,MaterSpec,CompanyNo from T_OrderInfo where OrderNo='WO22030') b on a.CompanyNo=b.CompanyNo 
where a.SerielNo in (select top 1 a.SerielNo from T_PrePrintSeriel a join T_SerielInfo b on a.SerielNo=b.SerielNo where a.Flag='N' 
           and a.SerielNo not in (select SerialNo from T_OrderSerial where OrderNo='WO22030') order by a.SerielNo) 


select top  2 a.SerielNo from T_PrePrintSeriel a join T_SerielInfo b on a.SerielNo=b.SerielNo where a.Flag='N'  
and a.SerielNo not in (select SerialNo from T_OrderSerial where OrderNo='WO22030')


select * from T_OrderSerial where OrderNo='WO22030' and CONVERT(char(10),InDate,120)='2023-02-28' ORDER BY InDate 

select *  from  T_PrePrintSeriel WHERE SerielNo= 'JF50C122002237'











select * from T_FlowInfo order by InDate desc
select * from  T_FlowInfo where FlowName='GP采购申请流程_电脑'
select * from T_FlowAudMan order by InDate desc
select * from  T_ObjectFieldConfig order by InDate desc



select * from  T_MaterStockOutMX ORDER BY InDate DESC
select * from  T_MaterOptLog ORDER BY InDate DESC
select * from  T_ProductStockInfo ORDER BY InDate DESC
select * from  T_PRInfo ORDER BY InDate DESC
select * from  T_ProductOutInfo ORDER BY InDate DESC

CE00-0630-00  *********
  2E00-0860-00   ********* 
  3E00-1230-00   *********
insert into T_OrderSerial(OrderNo,MaterNo,MaterName,OrderNum,SerialNo,OldOrderNo,InMan,Status,CompanyNo) 

select *  from  T_ProductOutInfo ORDER BY InDate DESC
select *  from  T_ProductOutInfo where POutNo IN ('CK2211080001','CK2211070001','CK2211060001')
select *  from T_MaterStockOutMX WHERE OutNo='CK2211080001'    22110800001    IS2211070001    

select *  from  T_ProductStockInfo where InNo IN ('IS2211070001')   --  组织入库
select *  from T_MaterStockOutMX WHERE OutNo='IS2211070001'       
select *  from  T_MaterStock where MaterBatch in ('22110700001','22110700002') 
select *  from T_MaterReceive WHERE ReceiveNo IN ('R2211070002','R2211070001')
select PurchPrice,*  from T_PurchaseItemInfo WHERE PurchNo IN ('PO2211070002','PO2211070001')
68.90
58.00

select *  from  T_MaterStock where MaterBatch in ('22110800001')  -- 库存
select *  from T_MaterStockOutMX WHERE OutNo='22110800001'  

CK2211080001
CK2211070001
CK2211060001







  insert into #TempNo(SNo)
  select POutNo from T_ProductOutInfo 
  where CompanyNo = @lcCompany and SaleOrderNo like @lnNo+'%' and CustNo like @lnB+'%' and SaleOrderNo like @lnC+'%'
  and CustPN like @lnItem+'%' and PONO like @lnD+'%' and MaterNo like @lnMNo+'%' and CustomerPN like @lnMName+'%' 
  and convert(char(10),InDate,120) >=@lnBDate and convert(char(10),InDate,120)<=@lnEDate  
  order by InDate desc
  SET @lcCount=@@ROWCOUNT   -- 记录总记录数

  
  -- 获取区间段的物料信息  
  select SNo into #BInfo4 from #TempNo where Item >= @lcBegNum and Item <=@lcEndNum

  select @lcCount as NumCount,a.*,convert(char(16),a.InDate,120) as InDate2,b.SignDesc
  from T_ProductOutInfo a left join T_PRInfo b on a.SaleOrderNo=b.PRNo and a.OrderItemNo=b.PRItem 
  where a.POutNo in (select SNo from #BInfo4)
  order by a.InDate desc

13-4

  insert into #TempNo(SNo,SNoT,SNoF,SumCount)
  select POutNo,SaleOrderNo+OrderItemNo,PPNo,OutNum from T_ProductOutInfo 
  where CompanyNo = @lcCompany and SaleOrderNo like @lnNo+'%' and CustNo like @lnB+'%' and SaleOrderNo like @lnC+'%'
  and CustPN like @lnItem+'%' and PONO like @lnD+'%' and MaterNo like @lnMNo+'%' and CustomerPN like @lnMName+'%' 
  and convert(char(10),InDate,120) >=@lnBDate and convert(char(10),InDate,120)<=@lnEDate  
  --AND POutNo='CK2211080001'
  order by InDate desc
  SET @lcCount=@@ROWCOUNT   -- 记录总记录数

  -- 获取区间段的物料信息  
  select SNo,SNoT,SNoF,SumCount into #BInfo412 from #TempNo where Item >= @lcBegNum and Item <=@lcEndNum

  -- 获取PR信息
  select PRNo,PRItem,ShipTo,DDate into #PRInfo
  from T_PRInfo where LTRIM(RTRIM(PRNo))+LTRIM(RTRIM(PRItem)) in (select SNoT from #BInfo412)

  --- 根据 PPNO 获取采购订单信息 SELECT *  FROM T_PurchaseItemInfo
  select a.QsNo as PRNo,a.PRItem, a.PPNo,a.PurchNo,a.PurchItem,a.HTCMan,a.SaleOrderNo,a.OrderItemNo,a.SupplierNo,b.SupplierEn 
  into #Purchase
  from T_PurchaseItemInfo a left join T_SupplierInfo b on a.SupplierNo=b.SupplierNo
  where LTRIM(RTRIM(QsNo))+LTRIM(RTRIM(PRItem)) in (select SNoT from #BInfo412)

  -- 下面是计算发货产品的单据，金额；发货产品可有组装入库产品或直接采购经IQC检验入库的  select *  from T_MaterStockOutMX 
  -- 1- 组装入库的
  -- 先看看发货了哪些产品
  select MaterBatch into #OutBatch from T_MaterStockOutMX where OutNo in (select SNo from #BInfo412)
  
  --1.1 再看看这些发货的产品是由那些原来料组装的，当时单价是多少  #OutBatch
  select distinct a.OutNo,a.MaterBatch,a.MaterNo,a.StockOutNum,isnull(d.PurchPrice,0) as PurchPrice,isnull(a.StockOutNum*d.PurchPrice,0) as PAmout 
  into #InBatch 
  from T_MaterStockOutMX a
  left join T_MaterStock b on a.MaterBatch=b.MaterBatch
  left join T_MaterReceive c on b.ReceiveNo=c.ReceiveNo
  left join T_PurchaseItemInfo d on c.PurchaseNo=d.PurchNo and c.PurchaseItem=d.PurchItem
  where a.OutNo in (select MaterBatch from #OutBatch) and a.StockOutNum>0

  -- 计算组装入库成品的金额
  select a.InNo,a.InNum,SUM(b.PAmout) as PAmount,SUM(b.PAmout)/a.InNum as ArgPrice 
  into #TempZZPPrice
  from T_ProductStockInfo a join #InBatch b on a.InNo=b.OutNo
  group by a.InNo,a.InNum

  -- 看看这些出库的产品，对应出库了哪些产品批次（组装入库的批次）,单价是多少
  select a.SNo,b.MaterBatch,c.InNum,c.ArgPrice,c.PAmount 
  into #ZZPPrice
  from #BInfo412 a join T_MaterStockOutMX b on a.SNo=b.OutNo join #TempZZPPrice c on b.MaterBatch=c.InNo
  
  
  --1.2- 看看IQC检验入库的这些产品，单价是多少
  select a.OutNo, b.MaterBatch,d.PurchPrice into #IQCPrice
  from T_MaterStockOutMX a
  left join T_MaterStock b on a.MaterBatch=b.MaterBatch
  left join T_MaterReceive c on b.ReceiveNo=c.ReceiveNo
  left join T_PurchaseItemInfo d on c.PurchaseNo=d.PurchNo and c.PurchaseItem=d.PurchItem
  where b.MaterBatch in (select MaterBatch from #OutBatch) 
  
  
  -- 看看出库的产品，对应的价格是多少
  select * into #TempPrdPrice from (
    select SNo as OutNo,MaterBatch,ArgPrice from #ZZPPrice
    union 
    select OutNo,MaterBatch,PurchPrice from #IQCPrice
  ) a
  
  -- 如果出库的产品有组装入库，也有IQC检验入库，价格取平均值
  select OutNo,AVG(ArgPrice) as avgPrice into #PrdPrice from #TempPrdPrice Group by OutNo

 
  --- 返回数据CASE WHEN PATINDEX('%[N-Zn-z]%', left(a.CustPN,1))=1 then '是' else '否' end as JT, -- '%[A-Za-z]%'
  select a.POutNo,a.OutDate,a.CustNo,a.PONO,a.CustomerPN,a.MaterNo,DATEDIFF(dd,b.DDate,g.ReceiveDate) as YCDate,
  DATEDIFF(dd,b.DDate,a.OutDate) as YCNum,a.CustPN,b.DDate,b.ShipTo,e.SupplierNo,e.SupplierEn,
  a.OutNum,d.avgPrice,a.OutNum*d.avgPrice as Amount,e.SaleOrderNo as HTNo,g.ReceiveDate
  from T_ProductOutInfo a
  join #PRInfo b on a.SaleOrderNo=b.PRNo and a.OrderItemNo=b.PRItem
  join #PrdPrice d on a.POutNo=d.OutNo
  left join #Purchase e on a.SaleOrderNo=e.PRNo and a.OrderItemNo=e.PRItem
  left join (select PurchaseNo,PurchaseItem,MAX(ReceiveDate) as ReceiveDate from T_MaterReceive group by PurchaseNo,PurchaseItem) g on e.PurchNo=g.PurchaseNo and e.PurchItem=g.PurchaseItem
 





select 'WO',aa.MaterNo,aa.MaterName,aa.OrderNum,bb.SerielNo as SerialNo,bb.OrderNo as OldOrderNo,'wudong' as InMan,'启用' as Status,aa.CompanyNo 
from (
  select OrderNo,MaterNo,MaterName,OrderNum,CompanyNo from T_OrderInfo where OrderNo='WO221002001' 
) aa
join (select TOP 2 a.SerielNo,a.CompanyNo,b.OrderNo from T_PrePrintSeriel a join T_SerielInfo b on a.SerielNo=b.SerielNo where a.Flag='N' and a.CompanyNo='C0010' ORDER BY a.SerielNo) bb on aa.CompanyNo=bb.CompanyNo
where bb.SerielNo not in (select SerialNo from T_OrderSerial)

insert into T_SerialBOM(SerialNo,FMaterNo,FMaterVer,MaterNo,MaterVer,MaterName,OrderNo,ProcedureNo,ProcedureName,ControlWay,UseNum,Pack,CompanyNo,InMan)
45676   WO221002001

select bb.SerielNo,aa.FMaterNo,aa.FMaterVer,aa.MaterNo,aa.MaterVer,aa.MaterName,aa.OrderNo,aa.ProcedureNo,aa.ProcedureName,aa.ControlWay,aa.UseNum,aa.Pack,aa.CompanyNo,'WUDONG' AS InMAN 
from (
select FMaterNo,FMaterVer,MaterNo,MaterVer,MaterName,OrderNo,ProcedureNo,ProcedureName,ControlWay,UseNum,Pack,CompanyNo
from T_OrderBOM WHERE OrderNo='WO221002001' and CompanyNo='C0010' 
) aa
join (select a.SerielNo,a.CompanyNo from T_PrePrintSeriel a join T_SerielInfo b on a.SerielNo=b.SerielNo where a.Flag='N' and a.CompanyNo='C0010') bb on aa.CompanyNo=bb.CompanyNo
where aa.ProcedureNo+aa.MaterNo+bb.SerielNo not in (select ProcedureNo+MaterNo+SerialNo from T_SerialBOM where CompanyNo='C0010')
order by bb.SerielNo,aa.MaterNo  

select ProcedureNo+MaterNo+SerialNo from T_SerialBOM


select bb.SerielNo,aa.OrderNo,aa.ProductNo,aa.ProductName,aa.SectionNo,aa.SectionName,aa.ProcedureNo,aa.ProcedureName,aa.FlowOrder+8 as FlowOrder,aa.ConditionNo,aa.ConditionName,aa.WorkUnit,aa.Status,aa.CompanyNo,'ZD' AS InMAN,'' AS Remark
from (
select OrderNo,ProductNo,ProductName,SectionNo,SectionName,ProcedureNo,ProcedureName,FlowOrder+8 as FlowOrder,ConditionNo,ConditionName,WorkUnit,Status,CompanyNo,'ZD' AS InMAN,'' AS Remark 
from T_OrderBaseFlow WHERE OrderNo='WO221002001' and CompanyNo='C0010'  
) aa
join (select a.SerielNo,a.CompanyNo from T_PrePrintSeriel a join T_SerielInfo b on a.SerielNo=b.SerielNo where a.Flag='N' and a.CompanyNo='C0010') bb on aa.CompanyNo=bb.CompanyNo
where bb.SerielNo+aa.ProcedureNo not in (select SerialNo+ProcedureNo from T_SerialBaseFlow where CompanyNo='C0010')
order by bb.SerielNo,aa.ProcedureNo


select *  from T_SerialBaseFlow
sp_helpindex T_SerialBaseFlow




select *  from T_OrderInfo  order by InDate desc   WO221002001
select *  from T_SerielInfo order by InDate desc
select *  from T_OrderSerial order by InDate desc  2022-10-04 14:17:46.633
select *  from T_SerialBOM order by InDate desc
select *  from T_SerialBaseFlow order by InDate desc
select *  from T_PrintRecInfo order by InDate desc
SELECT * FROM T_PrePrintSeriel  ORDER BY InDate DESC
select *  from T_PrintRecInfo  WHERE POBJ='TE-*********'

select * delete from T_OrderSerial where SerialNo='TE-*********'
select * delete from T_SerialBOM  where SerialNo='TE-*********'
select * delete from T_SerialBaseFlow  where SerialNo='TE-*********'

TE-*********

WO221002001	TE-*********	DS62-90-9089	CES-8T胶囊式内窥镜（蓝色1个装	6.00	2210020001	ZDDY	启用	C0010	2022-10-03 23:35:24.680
UPDATE T_SerielInfo SET OrderNo='2210020001' ,MaterNo='CW51-20-0112',MaterName='内窥镜组件',Model='CS-52' WHERE 
SerielNo='TE-*********'

SELECT * FROM T_PrePrintSeriel  ORDER BY InDate DESC
select *  from T_SerielInfo WHERE OrderNo='WO221002001'

---    UPDATE T_OrderInfo SET OrderNum=150 WHERE OrderNo='WO221002001'


select ProcedureNo+MaterNo+SerialNo from T_SerialBOM where CompanyNo='C0010' order by InDate DESC

128955-01264-00TE-*********
21DS62-90-9001TE-*********

SP_HELPINDEX T_SerialBOM  SerialNo, MaterNo, MaterVer, ProcedureNo

	
TE-*********


SELECT * FROM T_SerialBaseFlow WHERE SerialNo LIKE 'TE-21000010%'

select *  from T_OrderBaseFlow WHERE OrderNo='WO221002001' AND WorkUnit  DS62-90-9089
select *  from T_ProductFlow WHERE ProductNo='DS62-90-9089' AND WorkUnit

select *  from T_SerialBaseFlow order by InDate desc

update T_SerialBaseFlow set ConditionNo=b.ConditionNo,ConditionName=b.ConditionName from T_SerialBaseFlow a
join T_OrderBaseFlow b on a.OrderNo=b.OrderNo and a.ProcedureNo=b.ProcedureNo
where a.OrderNo='WO221002001'

update T_SerialBaseFlow set WorkUnit=b.workunit from T_SerialBaseFlow a
join T_OrderBaseFlow b on a.OrderNo=b.OrderNo and a.ProcedureNo=b.ProcedureNo
where a.OrderNo='WO221002001'



select *  from T_ProductBatchInfo WHERE CONVERT(CHAR(10),InDate,120)='2022-10-05' ORDER BY BatchNo,FlowOrder
select *  from T_MaterConsumeInfo WHERE CONVERT(CHAR(10),InDate,120)='2022-10-05' ORDER BY BatchNo 

select *  from T_MaterConsumeInfo WHERE CONVERT(CHAR(10),InDate,120)='2022-10-05' and ConsumeFlag='包装'
select *  from T_MaterConsumeInfo WHERE MaterBatchNo='1JF2210050001'
select *  from T_MaterConsumeInfo WHERE MaterBatchNo='1JF2210050002'

select *  from T_ProductBatchInfo WHERE CONVERT(CHAR(10),InDate,120)='2022-10-05' and BatchNo='TE-210000103' and ProcedureNo='12'
update  T_ProductBatchInfo set EndDate='',Status='生产中' WHERE CONVERT(CHAR(10),InDate,120)='2022-10-05' and 
BatchNo='TE-210000103' and ProcedureNo='12'

TE-210000103

select *  from T_MaterConsumeInfo WHERE MaterBatchNo='2JF2210050001'


select InMan from T_MaterConsumeInfo WHERE ConsumeFlag='包装' and BatchNo='TE-210000103'


TE-*********


select *  from T_MaterConsumeInfo WHERE CONVERT(CHAR(10),InDate,120)='2022-10-05' and BatchNo='TE-*********' 
and ConsumeBatch='E22100500100001'

select *  from T_SerialBOM WHERE SerialNo='TE-*********' AND MaterNo='DS62-90-9001'

select * delete from T_MaterConsumeInfo WHERE CONVERT(CHAR(10),InDate,120)='2022-10-05' and BatchNo='TE-*********' 
and MaterNo='8955-01264-00'

update T_SerialBOM set OverNum=0 WHERE SerialNo='TE-*********' AND MaterNo='8955-01264-00'


 select distinct a.UserNo,a.WorkCenter,a.CenterDesc,b.ProcedureNo,c.PrintLable,c.ScanMater,c.UpPack,c.ProcAct  
 from T_UserWorkCenter a  
 join T_ProductFlow b on a.WorkCenter=b.WorkUnit  
 left join T_ProcedureAction c on b.ConditionNo=c.PANo 
  where a.UserNo='wd10' and a.WorkCenter='U0010' and a.CompanyNo= 'C0010' 

select *  from T_ProductFlow where WorkUnit='U0010' and ProductNo='2727-01PZ-0001'


select * from T_LabelPrintRec WHERE CONVERT(CHAR(10),InDate,120)='2022-10-05'


 select * from T_SerialBaseFlow where SerialNo='TE-*********' and WorkUnit like '%U0008%' and CompanyNo= 'C0010'  
 and FlowOrder = (select min(FlowOrder) from T_SerialBaseFlow where SerialNo='TE-*********' and CompanyNo= 'C0010' 
 and FlowOrder > 1) 

 select * from T_ProductBatchInfo where BatchNo='TE-*********' and CompanyNo= 'C0010' order by FlowOrder desc 

 select *  from T_SerialBaseFlow where SerialNo='TE-*********' and ProcedureNo='20'

SELECT * FROM #WU
insert into T_SerialBaseFlow
select *  from #WU
UPDATE #WU SET SerialNo='TE-*********'
update  T_SerialBaseFlow set FlowOrder=4 where SerialNo='TE-*********' and ProcedureNo='25'

21  2  12  3   25   4









