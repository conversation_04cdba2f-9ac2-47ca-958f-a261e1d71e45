﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="UpdateFile.aspx.cs" Inherits="Web.User.UpdateFile" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head id="Head1" runat="server">
    <title>上传文件</title>
    <style type="text/css">
        body
        {
            margin-left: 0px;
            margin-top: 0px;
            margin-right: 0px;
            margin-bottom: 0px;
        }
        .STYLE1
        {
            font-size: 12px;
        }
        .STYLE3
        {
            font-size: 12px;
            font-weight: bold;
        }
        .STYLE4
        {
            color: #03515d;
            font-size: 12px;
        }
        #Text1
        {
            width: 79px;
        }
        #Text2
        {
            width: 82px;
        }
        #Text3
        {
            width: 147px;
        }
    </style>
    <script src="../Script/jquery-1.7.2.min.js"></script>
    <script src="../Script/common.js"></script>
    <link href="../Script/style.css" rel="stylesheet" />
    <link href="../Script/jqueryui/css/redmond/jquery-ui-1.10.3.custom.css" rel="stylesheet" />
    <script src="../Script/jqueryui/js/jquery-ui-1.10.3.custom.js"></script>
    <link href="../Script/jquery.jqGrid/css/ui.jqgrid.css" rel="stylesheet" />
    <script src="../Script/jquery.jqGrid/js/jquery.jqGrid.src.js"></script>
    <script src="../Script/jqueryui/i18n/jquery.ui.datepicker-zh-CN.js"></script>
    <script src="../Script/jquery.jqGrid/js/i18n/grid.locale-cn.js"></script>
    <script src="../js/Test.js" type="text/javascript"></script>
</head>
<body>
    <form id="form1" runat="server">
    <div style="width: 99%; " margin-left: 5px; float: left">
        <table style="width: 100%; " cellspacing="0" cellpadding="0">
            <tr>
               <td width="12"> 
                 <img src="images/tab_03.gif" width="12" height="30" />
               </td>
               <td width="2%" background="images/tab_05.gif">
                    <div align="center">
                      <img src="images/tb.gif" width="16" height="16"/>
                    </div>
               </td>
               <td width="95%"background="images/tab_05.gif">
                   <asp:Label ID="sMessage" Font-Bold="true" style="color:Red" runat="server" ></asp:Label>
               </td>
               <td width="2%" background="images/tab_07.gif">
                
               </td>
            </tr>
         </table> 
    </div>
    <div style="width: 99%; margin-left: 5px; float: left">
      
       <div id="divImport" style="margin-top: 15px;">
            <div class="frame">
               <asp:FileUpload  ID="fileImport2" Width="500px" class="file" runat="server" />
               <asp:Button  runat="server" ID="bntImport2"  Text="上传文件" onclick="bntImport2_Click"/>
            </div>
            <div id="rpt">
            </div>
      </div>
      <div id="progress" style="position: absolute; display: none">
          <div class="mask">
          </div>
          <div class="dlg">
              [
               <progress>正在上传，请稍候。。。</progress>
              ]
          </div>
      </div
         </div>               
    </div>
    </form>
</body>
</html>