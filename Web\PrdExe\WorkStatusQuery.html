﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>作业单元占用查询</title>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/XC.css" rel="stylesheet" />
    <link href="../js/skin/layer.css" rel="stylesheet" />
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <script src="../js/jQuery-2.2.0.min.js" type="text/javascript"></script>
    <script src="../layui/layui.js" type="text/javascript"></script>



    <script type="text/javascript">
        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#PrdSeriallist',
                id: 'PrdSeriallistID',
                url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=239',
                height: 'full-80',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'OrderNo', title: '工单号', width: 105, sort: true },
                    { field: 'BatchNo', title: '序列号', width: 125, sort: true },
                    { field: 'ProductNo', title: '产品编码', sort: true },
                    { field: 'RUnitNo', title: '作业单元', },
                    { field: 'ProcedureName', title: '工序名称', },
                    { field: 'UserNo', title: '作业员', },
                    { field: 'InDate2', title: '开始时间',  },
                    { field: 'Status', title: '生产状态', },
                ]],
                page: true
            });

            $("#PrdStatus_open").click(function () {
                var sSerial = $("#txtSSerial").val()
                var sWorkUnit = $("#txtSWorkUnit").val()

                var Data = '';
                var Params = { No: sSerial, Item: sWorkUnit, Name: "", MNo: "", MName: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "" };
                var Data = JSON.stringify(Params);

                table.reload("PrdSeriallistID", {
                    method: "POST",
                    url: "../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=239&Data=" + Data,
                    page: {
                        curr: 1
                    }
                })
            })

        })

    </script>

    <style>
        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }
    </style>
</head>
<body>
    <div class="div_find" style="width:99%">

        <p>
            <label class="find_labela">序列号</label><input type="text" id="txtSSerial" class="find_input" />
            <label class="find_labela">作业单元</label> <input type="text" id="txtSWorkUnit" class="find_input" />
            <input type="button" value="搜索" class="XC-Btn-Green XC-Btn-md XC-Size-xs" id="PrdStatus_open" />
        </p>
    </div>
    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="PrdSeriallist" lay-filter="PrdSeriallist"></table>
    </div>
</body>
</html>