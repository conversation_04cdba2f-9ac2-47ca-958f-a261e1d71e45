﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using System.Text;



namespace Common
{
    public class WeiXinInfo
    {
        #region  从数据库读取微信公众的CorpSecret    //toparty 这个是部门ID,即组ID；sTypeCode 是模块（应用,如：品牌显现）
        public static DataTable GetCorpSecret(string GroupID, string sTypeCode, string Company)  //
        {
            string sSQL = string.Empty;

            if (sTypeCode == "") // == “” 表示找公众号ID和组ID,
            {
                sSQL = " select CorpID,Secret,toparty,agentid,GZHKind,redirect_uri from T_SentWXConfig "+
                       " where toparty='" + GroupID + "' and GZHKind='" + Company + "' and NowUse = 1 ";
            }
            else // != ""  表示找应用ID 在数据表设置公众号的一个应用（模块），如： 品牌显现，是一个应用，一个应用又给很多组使用，只有一个可用
            {
                if (Company != "")  // 实现自动登录，刚开始无法判断是那个办事处的，所以分情况写代码
                {
                    sSQL = " select CorpID,Secret,toparty,agentid,GZHKind,redirect_uri from T_SentWXConfig " +
                           " where TypeCode='" + sTypeCode + "' and NowUse = 1 and BSC='" + Company + "' ";
                }
                else
                {
                    sSQL = " select CorpID,Secret,toparty,agentid,GZHKind,redirect_uri from T_SentWXConfig " +
                           " where TypeCode='" + sTypeCode + "' and NowUse = 1 ";
                }
            }


            DataTable sdt = DBHelper.GetDataTable(sSQL);

            return sdt;
        }

        #endregion




    }
}
