﻿
var FConfigInfo = function(){

  this.BSNo = '';
  this.BSName = '';
  this.FlowNo = '';
  this.FlowName = '';
  this.ObjectNo = '';
  this.ObjectName = '';
  this.FOne='';
  this.FTwo='';
  this.FThree='';
  this.FFour='';
  this.FFive='';
  this.FSix='';
  this.FSeven='';
  this.FEight='';
  this.FNine = '';
  this.FTen='';
  this.InMan = '';
  this.InManName = '';
  this.UseFlag = '';
  this.CompanyNo = '';
  this.NEFlag = '';
  this.Remark = '';//  上传图片路径


  var that = this;

  this.setData = function (data) {
    that.BSNo         = this.BSNo;
    that.BSName       = this.BSName;
    that.FlowNo         = this.FlowNo;
    that.FlowName       = this.FlowName;
    that.ObjectNo         = this.ObjectNo;
    that.ObjectName       = this.ObjectName;
    that.FOne             = this.FOne;
    that.FTwo             = this.FTwo;
    that.FThree           = this.FThree;
    that.FFour            = this.FFour;
    that.FFive            = this.FFive;
    that.FSix             = this.FSix;
    that.FSeven           = this.FSeven;
    that.FEight           = this.FEight;
    that.FNine            = this.FNine;
    that.FTen             = this.FTen;
    that.InMan            = this.InMan;
    that.InManName        = this.InManName;
    that.UseFlag          = this.UseFlag;
    that.CompanyNo        = this.CompanyNo;
    that.NEFlag           = this.NEFlag; 
    that.Remark           = this.Remark;  
  };

  this.getDataFromControl = function () {
    /*获取控件值*/
    that.BSNo = $.trim($('#txtBSApplyNo').val());
    that.BSName = $.trim($('#txtBSApplyName').val());
    that.FlowNo = $.trim($('#txtFlowNo').val());
    that.FlowName = $.trim($('#txtFlowName').val());
    that.ObjectNo = $.trim($('#txtObjectNo').val());
    that.ObjectName = $.trim($('#txtObjectName').val());
    that.FOne = $.trim($('#txtF01').val());
    that.FTwo = $.trim($('#txtF02').val());
    that.FThree = $.trim($('#txtF03').val());
    that.FFour = $.trim($('#txtF04').val());
    that.FFive = $.trim($('#txtF05').val()); 
    that.FSix = $.trim($('#txtF06').val());
    that.FSeven = $.trim($('#txtF07').val());
    that.FEight = $.trim($('#txtF08').val());
    that.FNine = $.trim($('#txtF08').val());
    that.FTen = $.trim($('#txtF10').val()); 
    that.InManName = $.trim($('#txtOperateName').val());
    that.InMan= $.trim($('#txtOperate').val());
    that.NEFlag = $.trim($('#txtNEFlag').val());
    that.Remark = $.trim($('#txtRemark').val());
  };

  this.setDataFromControl = function (data) {
    /*设置控件值*/
    $('#txtBSApplyNo').val(data.BSNo);
    $('#txtBSApplyName').val(data.BSName);
    $('#txtFlowNo').val(data.FlowNo);
    $('#txtFlowName').val(data.FlowName);
    $('#txtObjectNo').val(data.ObjectNo);
    $('#txtObjectName').val(data.ObjectName);
    $('#txtF01').val(data.F01);
    $('#txtF02').val(data.F02);
    $('#txtF03').val(data.F03);
    $('#txtF04').val(data.F04);
    $('#txtF05').val(data.F05);
    $('#txtF06').val(data.F06);
    $('#txtF07').val(data.F07);
    $('#txtF08').val(data.F08);
    $('#txtF09').val(data.F09);
    $('#txtF10').val(data.F10);
    $('#txtOperateName').val(data.OperateName);
    $('#txtOperate').val(data.Operate);
    $('#txtCompanyNo').val(data.CompanyNo);
    $('#txtNEFlag').val(data.NEFlag);
    $('#txtRemark').val(data.Remark);
  };

  this.clareDataFromControl = function (data)
  {
    $('#txtBSApplyNo').val('');
    $('#txtBSApplyName').val('');
    $('#txtFlowNo').val('');
    $('#txtFlowName').val('');
    $('#txtObjectNo').val('');
    $('#txtObjectName').val('');
    $('#txtF01').val('');
    $('#txtF02').val('');
    $('#txtF03').val('');
    $('#txtF04').val('');
    $('#txtF05').val('');
    $('#txtF06').val('');
    $('#txtF07').val('');
    $('#txtF08').val('');
    $('#txtF09').val('');
    $('#txtF10').val('');
    $('#txtOperateName').val('');
    $('#txtOperate').val('');
    $('#txtCompanyNo').val('');
    $('#txtNEFlag').val('');
    $('#txtRemark').val('');
  }

};


$(function() {


    //  字段一 选中事件 复选框
    $('#Fcheck1').click(function() {
        var Fcheck1 = $("#Fcheck1");  //字段一

        if (Fcheck1.is(':checked')) {
            $("#txtF01").removeAttr("disabled");
        }
        else {
            $("#txtF01").attr({ "disabled": "disabled" });
            $("#txtF01").val("");
        }

    });

    //  字段二 选中事件 复选框
    $('#Fcheck2').click(function() {
        var Fcheck2 = $("#Fcheck2");  //字段一

        if (Fcheck2.is(':checked')) {
            $("#txtF02").removeAttr("disabled");
        }
        else {
            $("#txtF02").attr({ "disabled": "disabled" });
            $("#txtF02").val("");
        }

    });

    //  字段 选中事件 复选框
    $('#Fcheck3').click(function() {
        var Fcheck3 = $("#Fcheck3");  //字段一

        if (Fcheck3.is(':checked')) {
            $("#txtF03").removeAttr("disabled");
        }
        else {
            $("#txtF03").attr({ "disabled": "disabled" });
            $("#txtF03").val("");
        }

    });

    //  字段 选中事件 复选框
    $('#Fcheck4').click(function() {
        var Fcheck4 = $("#Fcheck4");  //字段一

        if (Fcheck4.is(':checked')) {
            $("#txtF04").removeAttr("disabled");
        }
        else {
            $("#txtF04").attr({ "disabled": "disabled" });
            $("#txtF04").val("");
        }

    });

    //  字段 选中事件 复选框
    $('#Fcheck5').click(function() {
        var Fcheck5 = $("#Fcheck5");

        if (Fcheck5.is(':checked')) {
            $("#txtF05").removeAttr("disabled");
        }
        else {
            $("#txtF05").attr({ "disabled": "disabled" });
            $("#txtF05").val("");
        }

    });

    //  字段 选中事件 复选框
    $('#Fcheck6').click(function() {
        var Fcheck6 = $("#Fcheck6");

        if (Fcheck6.is(':checked')) {
            $("#txtF06").removeAttr("disabled");
        }
        else {
            $("#txtF06").attr({ "disabled": "disabled" });
            $("#txtF06").val("");
        }

    });

    //  字段 选中事件 复选框
    $('#Fcheck7').click(function() {
        var Fcheck7 = $("#Fcheck7");

        if (Fcheck7.is(':checked')) {
            $("#txtF07").removeAttr("disabled");
        }
        else {
            $("#txtF07").attr({ "disabled": "disabled" });
            $("#txtF07").val("");
        }

    });

    //  字段 选中事件 复选框
    $('#Fcheck8').click(function() {
        var Fcheck4 = $("#Fcheck8");  //字段一

        if (Fcheck4.is(':checked')) {
            $("#txtF08").removeAttr("disabled");
        }
        else {
            $("#txtF08").attr({ "disabled": "disabled" });
            $("#txtF08").val("");
        }

    });

    //  字段 选中事件 复选框
    $('#Fcheck9').click(function() {
        var Fcheck9 = $("#Fcheck9");  //字段一

        if (Fcheck9.is(':checked')) {
            $("#txtF09").removeAttr("disabled");
        }
        else {
            $("#txtF09").attr({ "disabled": "disabled" });
            $("#txtF09").val("");
        }

    });

    //  字段 选中事件 复选框
    $('#Fcheck10').click(function() {
        var Fcheck10 = $("#Fcheck10");  //字段一

        if (Fcheck10.is(':checked')) {
            $("#txtF10").removeAttr("disabled");
        }
        else {
            $("#txtF10").attr({ "disabled": "disabled" });
            $("#txtF10").val("");
        }

    });





    function EntityMsg(item, Mesage) {
        $("#div_warning").show();
        item.focus();
        $("#sMessage").html(Mesage);
    }

    $("#btnSubmit").click(function() {

        $("#div_warning").hide();
        $("#sMessage").val("");

        var Fcheck1 = $("#Fcheck1");
        var Fcheck2 = $("#Fcheck2");
        var Fcheck3 = $("#Fcheck3");
        var Fcheck4 = $("#Fcheck4");
        var Fcheck5 = $("#Fcheck5");
        var Fcheck6 = $("#Fcheck6");
        var Fcheck7 = $("#Fcheck7");
        var Fcheck8 = $("#Fcheck8");
        var Fcheck9 = $("#Fcheck9");
        var Fcheck10 = $("#Fcheck10");

        var ObjectName = $("#txtObjectName");
        var F01 = $("#txtF01");
        var F02 = $("#txtF02");
        var F03 = $("#txtF03");
        var F04 = $("#txtF04");
        var F05 = $("#txtF05");
        var F06 = $("#txtF06");
        var F07 = $("#txtF07");
        var F08 = $("#txtF08");
        var F09 = $("#txtF09");
        var F10 = $("#txtF10");


        if (ObjectName.val() == "") {
            EntityMsg(ObjectName, "请输入流程对象名称");
            return;
        }
        if (Fcheck1.is(':checked')) {
            if (F01.val() == "") {
                EntityMsg(F01, "请输入字段一对应的对象属性");
                return;
            }
        }
        if (Fcheck2.is(':checked')) {
            if (F02.val() == "") {
                EntityMsg(F02, "请输入字段二对应的对象属性");
                return;
            }
        }
        if (Fcheck3.is(':checked')) {
            if (F03.val() == "") {
                EntityMsg(F03, "请输入字段三对应的对象属性");
                return;
            }
        }
        if (Fcheck4.is(':checked')) {
            if (F04.val() == "") {
                EntityMsg(F04, "请输入字段四对应的对象属性");
                return;
            }
        }
        if (Fcheck5.is(':checked')) {
            if (F05.val() == "") {
                EntityMsg(F05, "请输入字段五对应的对象属性");
                return;
            }
        }
        if (Fcheck6.is(':checked')) {
            if (F06.val() == "") {
                EntityMsg(F06, "请输入字段六对应的对象属性");
                return;
            }
        }
        if (Fcheck7.is(':checked')) {
            if (F07.val() == "") {
                EntityMsg(F07, "请输入字段七对应的对象属性");
                return;
            }
        }
        if (Fcheck8.is(':checked')) {
            if (F08.val() == "") {
                EntityMsg(F08, "请输入字段八对应的对象属性");
                return;
            }
        }
        if (Fcheck9.is(':checked')) {
            if (F09.val() == "") {
                EntityMsg(F09, "请输入字段九对应的对象属性");
                return;
            }
        }
        if (Fcheck10.is(':checked')) {
            if (F10.val() == "") {
                EntityMsg(F10, "请输入字段十对应的对象属性");
                return;
            }
        }



        $("#btnSubmit").attr({ "disabled": "disabled" });
        ApplyOrder();

    });



    function ApplyOrder() {
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();
        var Data = new FConfigInfo();
        Data.getDataFromControl()
        var Paren = JSON.stringify(Data);

        $.ajax({
            type: "POST",
            url: "../Service/FlowConfigAjax.ashx?OP=AddObjectField&sFlag=1",
            data: { Data: Paren },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);
                //$("#div_warning").html("SMG2！"+data+" QQQQQQ"+parsedJson.Msg)
                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();  // 不需要清空
                    //DataControl.clareDataFromControl();
                    $("#div_warning").show();
                    $("#sMessage").html("保存成功");
                    //location.href = "../Flow/FConfigList.htm?sObjectNo=" + parsedJson.ObjectNo;  //先不转入列表界面，还要设置流程节点审批人
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#btnSubmit").removeAttr("disabled");
                    $("#loginError").html("您未登陆系统，请先登录！")
                    $("#loginError").show();
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'SentError') {
                    $("#btnSubmit").removeAttr("disabled");
                    $("#div_warning").html("发送微信出错，请线下通知！")
                    $("#div_warning").show();
                    location.href = "../ShopSigns/ShopSignsShow.htm?sApplyNo=" + parsedJson.ApplyNo;
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'NO') {
                    $("#btnSubmit").removeAttr("disabled");
                    $("#loginError").html("该业代无权限或无系统账号!")
                    $("#loginError").show();
                }
                else {
                    $("#btnSubmit").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#btnSubmit").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    }


    // 目标客户提报上传图片
    $('#btnUpload').click(function() {
        $("#sUpMessage").val("");
        var p = $("#txtObjectName");
        if (p.val() == "") {
            EntityMsg(p, "请先填写流程名称,再上传图片");
        }
        var arr = new Array();
        var sPath = $("#txtPath").val();
        arr = sPath.split(';/'); //注split可以用字符或字符串分割
        if (arr.length > 5) {
            //EntityMsg(p, "上传图片不能超过五个");
            $("#sUpMessage").html("上传图片不能超过五个！")
            return;
        }
        else {
            $("#Loading").show();
            $.ajaxFileUpload({
                url: '../Service/ApplyUpPicFile.aspx?CustNo=' + p + '&sFlag=2',
                fileElementId: 'UpImg1',
                dataType: 'json',
                success: function(data) {
                    // var parsedJson = jQuery.parseJSON(data);
                    var sPath = $("#txtPath").val();
                    $("#txtPath").val(sPath + ";" + data.Path);
                    $("#Loading").hide();
                    $("#sUpMessage").html("上传成功！")
                    //alert("上传成功");

                    //$("#imgs").append("<p  style='float: left;'><img style= 'max-width: 40%;max-height:50%;' src=" + data.Path + " class=img-responsive  alt="+data.Path+">  <input type='button' value='删' onclick='delTableRow(this)'  style='width:40px;height:25px;color:red;'/>  </p>");
                    $("#Myimgs").append("<p style='float: left;max-width: 48%'><a href=" + data.Path + " target='_blank'><img onclick='this.style.zoom=3' style=' max-width: 95%;max-height:35%' src=" + data.Path + " class=img-responsive  alt=" + data.Path + "> </a> <input type='button' value='删' onclick='delTableRow(this)'  style='width:50px;height:30px'/> </p>");

                }
            });
        }
    });



    // 新增：流程中使用的字段
    $('#btnAddNewOBF').click(function() {
        location.href = "../Flow/FConfigMX.htm?sFlag=1";
    });

    // 新增：填写流程的业务数据及提交申请
    $('#btnAddNewBSD').click(function() {
        location.href = "../Flow/BSDataApply.htm?sFlag=2";
    });



    // 查询:流程对象字段列表
    $('#btnListSumbit').click(function() {

        var OName = $("#txtScObjectName").val();
        var ONo = $("#txtScObjectNo").val();

        var SName = encodeURI(OName);  // 解决中文乱码的问题。

        var Data = '';
        var Params = { SName: SName, SNo: ONo };
        var Data = JSON.stringify(Params);

        $('#dgDataList').bootstrapTable('refresh', { url: '../Service/FlowConfigAjax.ashx?OP=FObjectList&Data=' + Data });
    });


    // 查询:显示自己提交的流程  
    $('#btnSearchBSList').click(function() {

        var BSName = $("#txtBSApplyName").val();
        var BDate = $("#txtBeginDate").val();
        var EDate = $("#txtEndDate").val();
       // var Status = $("#txtStatus").val();
        BSName = encodeURI(BSName);  // 解决中文乱码的问题。
       // Status = encodeURI(Status);  // 解决中文乱码的问题。

        var Data = '';
        var Params = { BSName: BSName,BDate:BDate,EDate:EDate };
        var Data = JSON.stringify(Params);

        $('#dgDataList').bootstrapTable('refresh', { url: '../Service/FlowConfigAjax.ashx?OP=ShowBSFlowList&sFlag=71&Data=' + Data });
    });

    // 查询按钮:审核列表
    $('#btnSearchAudList').click(function() {

        var BSName = $("#txtBSApplyName").val();
        var BDate = $("#txtBeginDate").val();
        var EDate = $("#txtEndDate").val();
        BSName = encodeURI(BSName);  // 解决中文乱码的问题。

        var Data = '';
        var Params = { BSName: BSName, BDate:BDate,EDate:EDate };
        var Data = JSON.stringify(Params);

        $('#dgDataList').bootstrapTable('refresh', { url: '../Service/FlowConfigAjax.ashx?OP=ShowAudList&SCFlag=2&Data=' + Data });
    });



    //  保存:保存流程申请时的业务数据
    $('#btnBSSave').click(function() {
        $("#btnBSSave").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();


        var FlowName = $("#txtFlowName");
        var BSApplyName = $("#txtBSApplyName");
        var F01 = $("#txtF01");
        var F02 = $("#txtF02");

        if (FlowName.val() == "") {
            $("#divsuccess").html("请选择要申请的流程！")
            $("#divsuccess").show();
            $("#btnBSSave").removeAttr("disabled");
            return;
        }
        if (BSApplyName.val() == "") {
            $("#divsuccess").html("请填写流程名称！")
            $("#divsuccess").show();
            $("#btnBSSave").removeAttr("disabled");
            return;
        }
        if (F01.val() == "") {
            $("#divsuccess").html("第一个字段内容必填！")
            $("#divsuccess").show();
            $("#btnBSSave").removeAttr("disabled");
            return;
        }
        if (F02.val() == "") {
            $("#divsuccess").html("第二个字段内容必填！")
            $("#divsuccess").show();
            $("#btnBSSave").removeAttr("disabled");
            return;
        }


        var Data = new FConfigInfo();
        Data.getDataFromControl()
        var Paren = JSON.stringify(Data);

        $.ajax({
            type: "POST",
            url: "../Service/FlowConfigAjax.ashx?OP=SaveBSFlowData&sFlag=1",
            data: { Data: Paren },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#btnBSSave").removeAttr("disabled");
                    $("#divsuccess").show();
                    $("#txtBSApplyNo").val(parsedJson.BSNo);
                    $('#dgBSDataList').bootstrapTable('refresh', { url: '../Service/FlowConfigAjax.ashx?OP=BSFlowDataList&BSNo=' + parsedJson.BSNo });  // 刷新数据
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#btnBSSave").removeAttr("disabled");
                    $("#loginError").html("您未登陆系统，请先登录！")
                    $("#loginError").show();
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'SentError') {
                    $("#btnBSSave").removeAttr("disabled");
                    $("#div_warning").html("发送微信出错，请线下通知！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'NO') {
                    $("#btnBSSave").removeAttr("disabled");
                    $("#loginError").html("该业代无权限或无系统账号!")
                    $("#loginError").show();
                }
                else {
                    $("#btnBSSave").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#btnBSSave").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });



    // 保存:提交流程申请
    $('#btnBSDataSubmit').click(function() {
        $("#btnBSDataSubmit").attr({ "disabled": "disabled" });
        var Note="";
        var BSApplyNo = $("#txtBSApplyNo").val();
        var FlowName = $("#txtFlowName").val();
        var Remark = $("#txtRemark").val();
        var PicPath = $("#txtPath").val();
        var PicPathV = $("#txtVideoPath").val();  // 视频路径
        var P1 = $("#txtPic1").val();
        var P2 = $("#txtPic3").val();
        var P3 = $("#txtPic2").val();
        var LoginName = $("#txtOperate").val();
        var FullName = $("#txtOperateName").val();
        var Status = $("#txtInFlag").val();


        if (BSApplyNo == "") {
            $("#divsuccess").html("无流程编号，请确认记录是否正确！")
            $("#divsuccess").show();
            $("#btnDelBSData").removeAttr("disabled");
            return;
        }

        // 判定是否都有审核人
        for (j = 1; j < 15; j++) {
            if (($("#NName"+j).html() != "")&&($("#AudMan"+j).html() == "")) {
               $("#sMessage").html("节点“"+$("#NName"+j).html()+"”无审核人！")
               $("#div_warning").show();
               $("#btnBSDataSubmit").removeAttr("disabled");
               return;
            }
        }


        //组合审批节点，传入前台
        for (j = 1; j < 15; j++) {
            if ($("#NName"+j).html() != "") {
                Note = Note + "^"+$("#NName"+j).html() + "AudMan:" + $("#AudMan"+j).html();
            }
            else{
               break;
            }
        }


        var Data = '';
        var Params = { BSApplyNo: BSApplyNo, FlowName: FlowName, Remark: Remark, LoginName: LoginName, FullName: FullName, PicPath: PicPath,PicPathV:PicPathV,Note:Note,Status:Status };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/FlowConfigAjax.ashx?OP=CommitFlow&sFlag=5",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);
                // $("#sSignMgs").html("SMG2！"+data+" QQQQQQ"+parsedJson.Msg) 
                // $("#sSignMgsDiv").show();
                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#div_warning").html("提交成功！")
                    $("#div_warning").show();
                    location.href = "../Flow/BSDataList.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#btnBSDataSubmit").removeAttr("disabled");
                    $("#div_warning").html("请重新登录！")
                    $("#div_warning").show();
                    location.href = "../LoginAuto.htm?sFlag=71";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'StatusError') {
                    $("#btnBSDataSubmit").removeAttr("disabled");
                    $("#div_warning").html("单据状态不正确！")
                    $("#div_warning").show();
                }
                 else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'SentError') {
                    $("#btnBSDataSubmit").removeAttr("disabled");
                    $("#div_warning").html("微信信息发送失败，请线下通知审核人！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#btnBSDataSubmit").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });






    //删除流程申请中已增加的业务数据
    $('#btnDelBSData').click(function() {
        $("#btnDelBSData").attr({ "disabled": "disabled" });
        var BSApplyNo = $("#txtBSApplyNo").val();
        var FlowName = $("#txtFlowName").val();
        var F01 = $("#txtF01").val();
        var F02 = $("#txtF02").val();
        var LoginName = $("#txtOperate").val();


        if (BSApplyNo == "") {
            $("#divsuccess").html("无流程编号，请确认记录是否正确！")
            $("#divsuccess").show();
            $("#btnDelBSData").removeAttr("disabled");
            return;
        }
        if (FlowName == "") {
            $("#divsuccess").html("请选择流程！")
            $("#divsuccess").show();
            $("#btnDelBSData").removeAttr("disabled");
            return;
        }
        if (F01 == "") {
            $("#divsuccess").html("请选择要删除的记录！")
            $("#divsuccess").show();
            $("#btnDelBSData").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { BSApplyNo: BSApplyNo, FlowName: FlowName, F01: F01, F02: F02, LoginName: LoginName };
        var Data = JSON.stringify(Params);


        $.ajax({
            type: "POST",
            url: "../Service/FlowConfigAjax.ashx?OP=DelBSData&MFlag=2",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);
                //$("#div_warning").html("SMG2！"+data+" QQQQQQ"+parsedJson.Msg)
                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $('#dgBSDataList').bootstrapTable('refresh', { url: '../Service/FlowConfigAjax.ashx?OP=BSFlowDataList&BSNo=' + parsedJson.BSNo });  // 刷新数据
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#btnDelBSData").removeAttr("disabled");
                    $("#div_warning").html("请重新登录！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#btnDelBSData").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });


    // 提交申请流程时，上传图片附件
    $('#btnSBUpload').click(function() {
        $("#sUpMessage").val("");
        var BSApplyNo = $("#txtBSApplyNo").val();
        var FlowName = $("#txtFlowName").val();
        var LoginName = $("#txtOperate").val();


        if (BSApplyNo == "") {
            $("#divsuccess").html("无流程编号，请确认记录是否正确！")
            $("#divsuccess").show();
            $("#btnDelBSData").removeAttr("disabled");
            return;
        }
        if (FlowName == "") {
            $("#divsuccess").html("请选择流程！")
            $("#divsuccess").show();
            $("#btnDelBSData").removeAttr("disabled");
            return;
        }


        $("#Loading").show();

        $.ajaxFileUpload({
            url: '../Service/ApplyUpPicFile.aspx?CustNo=' + BSApplyNo + '&sFlag=27',
            fileElementId: 'UpImg1',
            dataType: 'json',
            success: function(data) {
                //             var parsedJson = jQuery.parseJSON(data);
                $("#Loading").hide();
                $("#sUpMessage").html("上传成功！")

                var sPath = $("#txtPath").val();
                $("#txtPath").val(sPath + ";" + data.Path);
                var sPath1 = $("#txtPic1").val();
                var sPath2 = $("#txtPic2").val();
                var sPath3 = $("#txtPic3").val();
                if (sPath1 == "") {
                    $("#imgs1").attr("src", data.Path);
                    $("#txtPic1").val(data.Path);
                    $("#Btn1").css({ "display": "block" });
                }
                else if (sPath2 == "") {
                    $("#imgs2").attr("src", data.Path);
                    $("#txtPic2").val(data.Path);
                    $("#Btn2").css({ "display": "block" });
                }
                else if (sPath3 == "") {
                    $("#imgs3").attr("src", data.Path);
                    $("#txtPic3").val(data.Path);
                    $("#Btn3").css({ "display": "block" });
                }
            }
        });
    });


    // 提交申请流程时，上传视频附件
    $('#btnVideoUpload').click(function() {
        $("#sUpMessage").val("");
        var BSApplyNo = $("#txtBSApplyNo").val();
        var FlowName = $("#txtFlowName").val();
        var LoginName = $("#txtOperate").val();


        if (BSApplyNo == "") {
            $("#divsuccess").html("无流程编号，请确认记录是否正确！")
            $("#divsuccess").show();
            $("#btnDelBSData").removeAttr("disabled");
            return;
        }
        if (FlowName == "") {
            $("#divsuccess").html("请选择流程！")
            $("#divsuccess").show();
            $("#btnDelBSData").removeAttr("disabled");
            return;
        }

        $("#Loading").show();

        $.ajaxFileUpload({
            url: '../Service/ApplyUpPicFile.aspx?CustNo=' + BSApplyNo + '&sFlag=300',
            fileElementId: 'fileToUpload',
            dataType: 'json',
            success: function(data) {
                //             var parsedJson = jQuery.parseJSON(data);
                $("#Loading").hide();
                $("#sUpMessage").html("上传成功！")
                $("#ShowVideo").show();
                $("#txtVideoPath").val(data.Path);
                $("#Video1").attr("src", data.Path);
            }
        });
    });





    // 显示增加的流程节点
    $('#BtnAddNote').click(function() {
        var sNote = $("#txtShowNote").val();
        var sArr = sNote.split(",");
        var i = sArr.length; // 本节点输入框
        var j = i -1; // 用量获取上一节点的输入框
        $("#Aud").show(); 
        $("#txtAudMan").val('');

        if (i > 15) {
            $("#ShwoMax").html("最大只能设置15个审批节点！")
            return;
        }

       // 20171107 :不一定要在这里添加审批人，可以在流程申请的时候添加。
       // if ($("#noteMan"+j).val() == "") {
       //     $("#ShwoMax").html("请给上节点添加审核人！")
       //     return;
       // }


        // 显示对应的节点
        sNote = sNote + ',N0' + i;
        $("#Note" + i).show();


        $("#txtShowNote").val(sNote);


    });


    //删除已增加的流程节点
    $('#BtnDelNote').click(function() {
        var sNote = $("#txtShowNote").val();
        if (sNote!="")
        {
           // 把最后的字符删除，
           var sS = sNote.substring(0,sNote.length-4);
           $("#txtShowNote").val(sS);
           // 同时隐藏相应输入框
           var sTwo = sNote.slice(-2); // 获取后两位，
           var iTwo = parseInt(sTwo); //并转为数字
           
           // 隐藏相应输入框
           $("#Note" + iTwo).hide();
        }
    });


    //输入审核人，系统判定是否存在，并赋值
    $('#txtAudMan').blur(function () {

        var NNAME = $("#txtAudMan").val();
        
        if(NNAME=="")
        {
            return;
        }
        
        GetAudMan(NNAME,"");

    });
    
    //输入框的回车事件
    $('#txtAudMan').bind('keypress',function(event){ 
          
       if(event.keyCode == "13")      
       {  
          var NNAME = $("#txtAudMan").val();
        
          if(NNAME=="")
          {
            return;
          }
          
          GetAudMan(NNAME,"");
       }  

     });
    
    


    //提交这个流程需要那些审批节点，及审批人
    $('#btnAudSubmit').click(function() {
        $("#btnAudSubmit").attr({ "disabled": "disabled" });
        var Note="";
        var sFNo = $("#txtObjectNo").val();
        var sFName = $("#txtObjectName").val();
        var sSeqNo = $("#txtShowNote").val();
        var LoginName = $("#txtOperate").val();
        
        if (sSeqNo == "") {
            $("#AudMsg").html("请选择流程审批节点！")
            $("#div_AudMsg").show();
            $("#btnAudSubmit").removeAttr("disabled");
            return;
        }
        
        //审批节点及审批人。
        for (j = 1; j < 15; j++) {
            if ($("#noteTitle"+j).val() != "") {
                Note = Note + "^"+$("#noteTitle"+j).val() + "AudMan:" + $("#noteMan"+j).val();
            }
            else{
               break;
            }
        }


        //var Params = { FNo:sFNo,N1:note1,N2:note2,N3:note3,N4:note4,N5:note5,N6:note6,N7:note7,N8:note8,N9:note9,N10:note10,N11:note11,N12:note12,N13:note13,N14:note14,N15:note15,M1:Man1,M2:Man2,M3:Man3,M4:Man4,M5:Man5,M6:Man6,M7:Man7,M8:Man8,M9:Man9,M10:Man10,M11:Man11,M12:Man12,M13:Man13,M14:Man14,M15:Man15,Man:LoginName};
        var Params = {FNo:sFNo,FName:sFName, Note:Note,SeqNo:sSeqNo,Man:LoginName};
        var Data = JSON.stringify(Params);


        //var Data = new FConfigInfo();
        //Data.getDataFromControl()
        //var Paren = JSON.stringify(Data);

        $.ajax({
            type: "POST",
            url: "../Service/FlowConfigAjax.ashx?OP=SaveAudNote&MFlag=2",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);
                //$("#div_warning").html("SMG2！"+data+" QQQQQQ"+parsedJson.Msg)
                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#AudMsg").html("保存成功！")
                    $("#div_AudMsg").show();
                    location.href = "../Flow/FConfigList.htm";   

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#btnAudSubmit").removeAttr("disabled");
                    $("#div_warning").html("请重新登录！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#btnAudSubmit").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });

    //各个节点的审批人提交审批记录
    $('#btnShenHe').click(function() {
        $("#btnShenHe").attr({ "disabled": "disabled" });
        var sBSNo = $("#txtBSApplyNo").val();
        var sFlow = $("#txtFlowName").val();
        var sIsPass = $("#txtIsPass").val();
        var sAudDesc = $("#txtAudDesc").val();
        var LoginName= $("#txtOperate").val();
        var sStatus =  $("#txtStatus").val();
        
        if (sIsPass == "") {
            $("#sIsPass").html("请选择结论！")
            $("#div_AudMsg").show();
            $("#btnShenHe").removeAttr("disabled");
            return;
        }
        
        if (sIsPass == "不通过") {
          if(sAudDesc==""){
              $("#sIsPass").html("请填写不通过的理由！")
              $("#div_AudMsg").show();
              $("#btnShenHe").removeAttr("disabled");
              return;
            }
        }
        
        
        var Params = {BSNo:sBSNo,Flow:sFlow,IsPass:sIsPass, AudDesc:sAudDesc,InMan:LoginName,Status:sStatus};
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/FlowConfigAjax.ashx?OP=SaveAudRec&MFlag=8",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);
                //$("#div_warning").html("SMG2！"+data+" QQQQQQ"+parsedJson.Msg)
                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#AudMsg").html("提交成功！")
                    $("#div_AudMsg").show();
                    location.href = "../Flow/AudList.htm";   

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#btnShenHe").removeAttr("disabled");
                    $("#div_warning").html("请重新登录！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#btnShenHe").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });


    //催办 -- 
    $('#btnCB').click(function() {
        var sBSNo = $("#txtBSApplyNo").val();
        var sFlow = $("#txtFlowName").val();
        var LoginName= $("#txtOperate").val();
        var sStatus =  $("#txtStatus").val();
        var sAudDesc = $("#txtRemark").val();
        
        var Params = {BSNo:sBSNo,Flow:sFlow,LoginName:LoginName,Status:sStatus,AudDesc:sAudDesc};
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/FlowConfigAjax.ashx?OP=CBBill&MFlag=9",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);
                //$("#div_warning").html("SMG2！"+data+" QQQQQQ"+parsedJson.Msg)
                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#AudMsg").html("催办成功！")
                    $("#div_AudMsg").show();
                     location.href = "../Flow/BSDataList.htm";     

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#div_warning").html("请重新登录！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'StatusOver') {
                    $("#div_warning").html("单据已完成，不需要催办！") 
                    $("#div_warning").show();
                }else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'SentError') {
                    $("#div_warning").html("发送微信出错，请线下通知！")
                    $("#div_warning").show();
                     location.href = "../Flow/BSDataList.htm";   
                }
            },
            error: function(data) {
                $("#btnShenHe").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });



    //撤回-- 显示撤回原因输入框
    $('#btnCH').click(function() {
       if ($("#CHTxt").is(":hidden")) {
           $("#CHTxt").show();    //如果元素为隐藏,则将它显现
       } else {
          $("#CHTxt").hide();     //如果元素为显现,则将其隐藏
       }
    });

    //撤回-- 提交
    $('#btnCHCommit').click(function() {
        var sBSNo = $("#txtBSApplyNo").val();
        var sFlow = $("#txtFlowName").val();
        var LoginName= $("#txtOperate").val();
        var sStatus =  $("#txtStatus").val();
        var sCHDesc =  $("#txtCHDesc").val();
        
        var Params = {BSNo:sBSNo,Flow:sFlow,InMan:LoginName,CHDesc:sCHDesc,Status:sStatus};
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/FlowConfigAjax.ashx?OP=CHBill&MFlag=9",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);
                //$("#div_warning").html("SMG2！"+data+" QQQQQQ"+parsedJson.Msg)
                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#AudMsg").html("撤回成功！")
                    $("#div_AudMsg").show();
                    location.href = "../Flow/BSDataList.htm";   

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#div_warning").html("请重新登录！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'StatusOver') {
                    $("#div_warning").html("单据已完成，不需要撤回！") 
                    $("#div_warning").show();
                    location.href = "../Flow/BSDataList.htm";   
                }else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'SentError') {
                    $("#div_warning").html("发送微信出错，请线下通知！")
                    $("#div_warning").show();
                     location.href = "../Flow/BSDataList.htm";   
                }
            },
            error: function(data) {
                $("#btnShenHe").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });









    // 其他按钮方法






});


    
   // 隐藏显示
function txt_Onblur(SeqNo) {
    var NNAME = $("#txtIn"+SeqNo).val();
        
    if(NNAME=="")
    {  
        $("#txtIn"+SeqNo).val('');
        return;
    }
        
    GetAudManTwo(NNAME,SeqNo);       
}


function GetAudMan(sNo,sOB)
{  
        var Data = '';
        var Params = { NNAME: sNo };
        var Data = JSON.stringify(Params); 
        
        $.ajax({
            type: "POST",
            async: false,
            url: "../Service/FlowConfigAjax.ashx?OP=AddAudMan&MFlag=2",
            data: {Data: Data},
             success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson[0].ORight == "Success") {
                    var sNote = $("#txtShowNote").val();
                    var sArr = sNote.split(",");
                    var i = 10000; 
                    
                    var sANote = $("#txtNoteList").val(); 
                    var sTwo = sANote.slice(-2); // 获取后两位，
                    var iTwo = parseInt(sTwo); //并转为数字
                    
                    if((sANote==null)||(sANote=="")) {
                       i = sArr.length - 1; 
                    }
                    else {
                       i = iTwo;
                    }
                    
                    var sNowMan = $("#noteMan"+i).val();  // 目前已选择的审批人
                    var sCMan = parsedJson[0].FullName+'('+parsedJson[0].LoginName+')'; // 现在输入的审批人
                    var iD = sNowMan.indexOf(sCMan); // 先判断现在输入的审批人是否已在已选择的审批人里面。
                    if (iD <=0)
                    {
                       var sMan = sNowMan +'；'+sCMan;
                       $("#noteMan"+i).val(sMan);
                       $("#ShwoMax").html("");
                       $("#txtAudMan").val('');
                    }
                    else
                    {
                       $("#ShwoMax").html("已添加该审核人！");
                       $("#txtAudMan").val('');
                       $("#sMsg"+sOB).html('已添加该审核人');
                    }
                }
                else if (parsedJson[0].ORight == "NoUser"){  
                    $("#ShwoMax").html("在系统找不到该审批人，请确认！");
                }
                else
                {
                    $("#ShwoMax").html("");
                }
            },
          });
}


function GetAudManTwo(sName,sOB)
{  
        var Data = '';
        var Params = { NNAME: sName };
        var Data = JSON.stringify(Params); 
        
        $.ajax({
            type: "POST",
            async: false,
            url: "../Service/FlowConfigAjax.ashx?OP=AddAudMan&MFlag=2",
            data: {Data: Data},
             success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson[0].ORight == "Success") {
 
                    var sNowMan = $("#AudMan"+sOB).html();  // 目前已选择的审批人
                    var sCMan = parsedJson[0].FullName+'('+parsedJson[0].LoginName+')'; // 现在输入的审批人
                    
                    if ($("#txtInFlag").val()=="审核不通过") // 如果单据状态是“待提交”，还要看看是否审核不通过更新的，如果是，则添加审核人时打个标识“+”，这样提交单据时，提交审批人，只针对有 “+”的提交即可。
                    {
                           sCMan ="+"+sCMan;
                    }      
                    var iD = sNowMan.indexOf(sCMan); // 先判断现在输入的审批人是否已在已选择的审批人里面。
                    if (iD <=0)
                    {
                       var sMan = sNowMan +'；'+sCMan;          
                       $("#AudMan"+sOB).html(sMan);
                       $("#sMsg"+sOB).html('');
                    }
                    else
                    {
                       $("#AudMan"+sOB).val('');
                       $("#sMsg"+sOB).html('已添加该审核人');
                    }
                }
                else if (parsedJson[0].ORight == "NoUser"){  
                    $("#sMsg"+sOB).html('在系统找不到该审批人，请确认');
                }
                else
                {
                    $("#sMsg"+sOB).html("");
                }
            },
     });
}

