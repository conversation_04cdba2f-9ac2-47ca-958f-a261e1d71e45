﻿<!DOCTYPE html
          PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title></title>
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/echarts.js"></script>
    <script>

        $(function () {
            init()
        })

        // 初始化函数
        function init() {
            const data = sessionStorage.getItem("KB0007");
            let produceData = [];
            let serielData = [];
            var lineBodyData = []
            var deviceData = []

            // 校验并解析数据
            if (data) {
                try {
                    const jsonData = JSON.parse(data);
                    produceData = jsonData[0] || [];
                    serielData = jsonData[1] || [];
                    lineBodyData = jsonData[2] || [];
                    deviceData = jsonData[3] || [];
                } catch (error) {
                    console.error("数据解析失败:", error);
                }
            }



            const calcPercent = (bad, total) => total ? (100 - (bad / total * 100)) : 0;

            //日期编号
            const dayNum = produceData.map(item => item.DayNum);

            //当月主机产量
            const hostCount = produceData.map(item => item.HostCount);
            //当月主机直通率
            const hostPercentage = produceData.map(item => calcPercent(item.HostBadCount, item.HostCount));

            //当月整机产量
            const machineCount = produceData.map(item => item.MachineCount);
            //当月整机直通率
            const machinePercentage = produceData.map(item => calcPercent(item.MachineBadCount, item.MachineCount));

            //当月直通率
            const Percentage = produceData.map(item => calcPercent(item.SumBadCount, item.SumCount))



            computeProduce(produceData)
            computeUsage(deviceData, "DevicePercentage");
            computeUsage(lineBodyData, "LineBodyPercentage");

            RenderChart(hostCount, dayNum, "ChartOne", "bar", "当月主机产量")
            RenderChart(machineCount, dayNum, "ChartTow", "bar", "当月整机产量")
            RenderChart(hostPercentage, dayNum, "ChartFour", "line", "当月主机直通率")
            RenderChart(machinePercentage, dayNum, "ChartFive", "line", "当月整机直通率")
            RenderChart(Percentage, dayNum, "ChartSix", "line", "当月直通率")
            RenderChartPie(serielData, "ChartThree", "序列号分布", true)
        }



        function computeProduce(data) {
            // 使用单一 reduce 计算所有统计值
            const totals = data.reduce((acc, cur) => ({
                HostCount: acc.HostCount + cur.HostCount,
                MachineCount: acc.MachineCount + cur.MachineCount,
                HostBad: acc.HostBad + cur.HostBadCount,
                MachineBad: acc.MachineBad + cur.MachineBadCount,
                Total: acc.Total + cur.SumCount,
                TotalBad: acc.TotalBad + cur.SumBadCount
            }), {
                HostCount: 0, MachineCount: 0, HostBad: 0,
                MachineBad: 0, Total: 0, TotalBad: 0
            });

            // 统一百分比计算
            const calcPercent = (bad, total) => total ? (100 - (bad / total * 100)).toFixed(2) + "%" : "0.00%";

            $("#HostCount").text(totals.HostCount);
            $("#MachineCount").text(totals.MachineCount);
            $("#Percentage").text(calcPercent(totals.TotalBad, totals.Total));
            $("#HostPercentage").text(calcPercent(totals.HostBad, totals.HostCount));
            $("#MachinePercentage").text(calcPercent(totals.MachineBad, totals.MachineCount));
        }

        function computeUsage(data, targetId) {
            const [used = { value: 0 }, unused = { value: 1 }] = [
                data.find(item => item.name === "已使用"),
                data.find(item => item.name === "未使用")
            ];

            const percentage = used.value / (used.value + unused.value) * 100;
            $("#" + targetId).text(percentage.toFixed(2) + "%");
        }


        /**
         * 柱形图、折线图
         */
        function RenderChart(yData, xData, chartId, chartType, name) {
            if (!yData || yData.length === 0) {
                console.warn(`没有数据提供给图表 ${chartId}`);
                return;
            }

            let serie = []

            serie.push({
                name: name,
                type: chartType,
                data: yData,
                label: {
                    show: true,
                    fontSize: "11",
                    color: 'white',
                    fontWeight: "bold",
                    position: 'top',
                    formatter: function (params) {
                        if (chartType == "line") {
                            return params.value.toFixed(2) + "%"
                        } else {
                            return params.value + "台"
                        }
                    }
                },
            });


            // 初始化图表
            const chart = echarts.init(document.getElementById(chartId));
            chart.setOption({
                color: ['#5bc0de'],
                grid: {
                    left: '2%',
                    right: '2%',
                    bottom: '5%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'item',
                },
                calculable: true,
                xAxis: [
                    {
                        type: 'category',
                        data: xData,
                        axisLine: {
                            lineStyle: {
                                color: '#5bc0de'
                            },
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#fff',
                                fontWeight: "bold"
                            }
                        },
                        axisTick: {
                            show: false
                        }
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        axisLine: {
                            lineStyle: {
                                color: '#5bc0de'
                            },
                        },
                        splitLine: {
                            "show": false
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#fff',
                                fontWeight: "bold"
                            },
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#5bc0de'
                            },
                        }
                    }
                ],
                dataZoom: [
                    {
                        type: 'inside'
                    }
                ],
                series: serie
            });

            // 窗口调整时重新渲染图表
            $(window).resize(() => chart.resize());
        }

        /**
         * 饼图
         */
        function RenderChartPie(data, chartId, title, isColor = false) {
            if (!data || data.length === 0) {
                console.warn(`没有数据提供给图表 ${chartId}`);
                return;
            }

            if (isColor) {
                data = addItemStyle(data)
            }


            // 初始化图表
            const chart = echarts.init(document.getElementById(chartId));

            chart.setOption({
                tooltip: {
                    trigger: 'item'
                },
                series: [
                    {
                        name: title,
                        type: 'pie',
                        radius: '45%',
                        data: data,
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        label: {
                            show: true,
                            formatter(param) {
                                return (
                                    param.name + "\n\n" +
                                    param.value + "个\n\n" +
                                    param.percent + "%\n"
                                );
                            },
                            fontSize: 11,
                            backgroundColor: "transparent",
                            color: "white",
                            fontWeight: "bold",
                        },
                    }
                ]
            });

            // 窗口调整时重新渲染图表
            $(window).resize(() => chart.resize());
        }

        // 处理函数：动态添加 itemStyle
        function addItemStyle(data) {

            const colors = [
                '#5bc0de', '#0b3c51', "#ffffff", "#FF7F50", "#98FF98", "#00BFFF", "#FFA500", "#B2FFCC", "#87CEEB", "#7CCD7C",
                "#FFD700", "#FF99CC", "#E6E6FF", "#C7B8EA", "#A9A9A9", "#B8860B", "#FFE4B3", "#6495ED", "#8B4513",
                "#FF669D", "#4D4D4D", "#B5C59D", "#FF6347", "#008B8B", "#adff2f", "#ee82ee",
            ];

            // 使用 map 返回新数组
            return data.map((item, index) => ({
                ...item, // 保留原数据
                itemStyle: { color: colors[index] } // 动态添加 itemStyle，循环使用颜色
            }));
        }
    </script>

    <style>
        * {
            margin: 0;
            padding: 0;
            list-style: none;
            text-decoration: none;
        }

        .container-div {
            height: 100vh;
            background: #15181d;
            overflow: hidden;
            position: relative;
            color: #ffffff;
        }

        .container-top {
            height: 20%;
        }

        .container-center {
            height: 40%;
            display: flex;
            margin-top: 10px
        }

        .container-bottom {
            height: 40%;
            display: flex;
        }

        .container-center-left {
            width: 39.5%;
            height: 100%;
            margin-right: 0.5%;
        }

        .container-center-center {
            width: 39.5%;
            height: 100%;
            margin-right: 0.5%;
        }

        .container-center-right {
            width: 20%;
            height: 100%
        }

        .container-bottom-left {
            width: 33%;
            height: 100%;
            margin-right: 0.5%
        }

        .container-bottom-center {
            width: 33%;
            height: 100%;
            margin-right: 0.5%
        }

        .container-bottom-right {
            width: 33%;
            height: 100%
        }

        .left {
            float: left;
        }

        .div_any01 {
            width: 23%;
            margin-right: 2%;
        }

        .div_any_child {
            width: 100%;
            height: 330px;
            box-shadow: -5px 0px 10px #034c6a inset, 0px -10px 10px #034c6a inset, 5px 0px 10px #034c6a inset, 0px 10px 10px #034c6a inset;
            border: 1px solid #034c6a;
            box-sizing: border-box;
            position: relative;
            margin-top: 15px;
        }

        .div_any_title {
            background-color: #034c6a;
            border-radius: 18px;
            position: absolute;
            height: 25px;
            width: 60%;
            top: -15px;
            color: #ffffff;
            font-weight: bold;
            font-size: 16px;
            left: 20%;
            line-height: 25px;
            text-align: center;
        }

        .div_any_child-body {
            margin-top: 1.5%;
            margin-left: 1.5%;
            margin-right: 1.5%;
            height: 70%;
            width: 97%;
            display: flex;
            text-align:center;
        }

            .div_any_child-body > div {
                flex: 1;
                background-color: #0a374a
            }

        .div_any_child-body-title {
            margin: 20px 0px 0px 0px
        }

        .div_any_child-body-value {
            font-size: 30px;
            margin: 10px 0px 0px 0px
        }
    </style>
</head>


<body>
    <div class="container-div">
        <div class="container-top">
            <div class="left div_any01" style="width:100%;height:95%">
                <div class="div_any_child" style="height: 95%;">
                    <div class="div_any_title">统计信息</div>
                    <div class="div_any_child-body">
                        <div style="margin-right:0.5%">
                            <div class="div_any_child-body-title">当月主机累计数量</div>
                            <div class="div_any_child-body-value" id="HostCount"></div>
                        </div>
                        <div style="margin-right:0.5%">
                            <div class="div_any_child-body-title">当月整机累计数量</div>
                            <div class="div_any_child-body-value" id="MachineCount"></div>
                        </div>
                        <div style="margin-right:0.5%">
                            <div class="div_any_child-body-title">当月生产直通率</div>
                            <div class="div_any_child-body-value" id="Percentage"></div>
                        </div>
                        <div style="margin-right:0.5%">
                            <div class="div_any_child-body-title">当月主机直通率</div>
                            <div class="div_any_child-body-value" id="HostPercentage"></div>
                        </div>
                        <div style="margin-right:0.5%">
                            <div class="div_any_child-body-title">当月整机直通率</div>
                            <div class="div_any_child-body-value" id="MachinePercentage"></div>
                        </div>
                        <div style="margin-right:0.5%">
                            <div class="div_any_child-body-title">线体利用占比</div>
                            <div class="div_any_child-body-value" id="LineBodyPercentage"></div>
                        </div>
                        <div>
                            <div class="div_any_child-body-title">设备利用占比</div>
                            <div class="div_any_child-body-value" id="DevicePercentage"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-center">
            <div class="container-center-left">
                <div class="left div_any01" style="width:100%;height:96%">
                    <div class="div_any_child" style="height: 96%;">
                        <div class="div_any_title">当月主机产量</div>
                        <div id="ChartOne" style="width:100%;height:100%"></div>
                    </div>
                </div>
            </div>
            <div class="container-center-center">
                <div class="left div_any01" style="width:100%;height:96%">
                    <div class="div_any_child" style="height: 96%;">
                        <div class="div_any_title">当月整机产量</div>
                        <div id="ChartTow" style="width:100%;height:100%"></div>
                    </div>
                </div>
            </div>
            <div class="container-center-right">
                <div class="left div_any01" style="width:100%;height:96%">
                    <div class="div_any_child" style="height: 96%;">
                        <div class="div_any_title">序列号分布</div>
                        <div id="ChartThree" style="width:100%;height:100%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-bottom">
            <div class="container-bottom-left">
                <div class="left div_any01" style="width:100%;height:96%">
                    <div class="div_any_child" style="height: 96%;">
                        <div class="div_any_title">当月主机直通率</div>
                        <div id="ChartFour" style="width:100%;height:100%"></div>
                    </div>
                </div>
            </div>
            <div class="container-bottom-center">
                <div class="left div_any01" style="width:100%;height:96%">
                    <div class="div_any_child" style="height: 96%;">
                        <div class="div_any_title">当月整机直通率</div>
                        <div id="ChartFive" style="width:100%;height:100%"></div>
                    </div>
                </div>
            </div>
            <div class="container-bottom-right">
                <div class="left div_any01" style="width:100%;height:96%">
                    <div class="div_any_child" style="height: 96%;">
                        <div class="div_any_title">当月生产直通率</div>
                        <div id="ChartSix" style="width:100%;height:100%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>