﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" >
<head>
<meta http-equiv="Content-Type" content="text/html"; charset="utf-8" />
<title>DI发放</title>
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css"/>

    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/UDI.js" type="text/javascript"></script>
    <link href="../css/all.css" rel="stylesheet" />
    
    
    <script type="text/javascript">


        $(function() {

         // var h = $(window).height() - 55;
          var h = 130;
//          $('#dgDataList1').bootstrapTable('resetView', { height: h });
//          $('#dgDataList2').bootstrapTable('resetView', { height: h });
//          $('#dgDataList3').bootstrapTable('resetView', { height: h });
         // $('#dgDataList').bootstrapTable('hideColumn', 'Remark');  // 隐藏列


            var sCorpID = "we";  // GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&sFlag=10&CorpID=" + sCorpID,
                success: function(data) {
                    var parsedJson = jQuery.parseJSON(data);
                    //$('#Usertable').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetUserInfo&sFlag=10' });
                }
            })
        });






        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#DIInfolist',
                id: 'DIInfoID',
                url: '../Service/BaseModuleAjax.ashx?OP=GetSerielInfo&CFlag=27',
                height: 'full-80',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'ZXXSDYCPBS', title: '最小销售单元产品标识(DI码)', width: 220, sort: true },
                    { field: 'SPMC', title: '商品名称', width: 150 },
                    { field: 'SFWBLZTLCP', title: '是否为包类/组套类产品', width: 130 },
                    { field: 'GGXH', title: '规格型号', width: 150 },
                    { field: 'CPMS', title: '产品描述', width: 100 },
                    { field: 'CPHHHBH', title: '产品货号或编号', width: 150 },
                    { field: 'QXLB', title: '器械类别', width: 70 },
                    { field: 'YFLBM', title: '原分类编码', width: 70 },
                    { field: 'FLBM', title: '分类编码', width: 70 },
                    { field: 'YLQXZCRBARMC', title: '医疗器械注册人/备案人名称', width: 220 },
                    { field: 'YLQXZCRBARYWMC', title: '医疗器械注册人/备案人英文名称', width: 220 },
                    { field: 'ZCZBHHZBAPZBH', title: '注册证编号或者备案凭证编号', width: 220 },
                    { field: 'CPLB', title: '产品类别 ', width: 70 },
                    { field: 'CGZMRAQXGXX', title: '磁共振（MR）安全相关信息', width: 200 },
                    { field: 'SFBJWYCXSY', title: '是否标记为一次性使用', width: 200 },
                    { field: 'ZDCFSYCS', title: '最大重复使用次数', width: 180 },
                    { field: 'SFWWJBZ', title: '是否为无菌包装', width: 180 },
                    { field: 'SYQSFXYJXMJ', title: '使用前是否需要进行灭菌', width: 200 },
                    { field: 'MJFS', title: '灭菌方式', width: 150 },
                    { field: 'QTXXDWZLJ', title: ' 其他信息的网址链接', width: 180 },
                    { field: 'YBBM', title: '医保编码', width: 80 },
                    { field: 'TSRQ', title: '退市日期', width: 80 },
                    { field: 'SCBSSFBHPH', title: '是否包含批号', width: 120 },
                    { field: 'SCBSSFBHXLH', title: '是否包含序列号', width: 120 },
                    { field: 'SCBSSFBHSCRQ', title: '是否包含生产日期', width: 120 },
                    { field: 'SCBSSFBHSXRQ', title: '是否包含失效日期', width: 120 },
//                    { field: 'CPBSBMTXMC', title: '医疗器械唯一标识编码体系名称', width: 220 },
//                    { field: 'ZXXSDYZSYDYDSL', title: '最小销售单元中使用单元的数量', width: 220 },
//                    { field: 'SYDYCPBS', title: '使用单元产品标识', width: 120 },
//                    { field: 'CPBSFBRQ', title: '产品标识发布日期', minWidth: 120 },
//                    { field: 'CPBSZT', title: '产品标识载体', minWidth: 120 },
//                    { field: 'SFYZCBACPBSYZ', title: '是否与注册/备案产品标识一致', minWidth: 220 },
//                    { field: 'ZCBACPBS', title: '注册/备案产品标识', minWidth: 120 },
//                    { field: 'SFYBTZJBS', title: '是否有本体直接标识', minWidth: 120 },
//                    { field: 'BTCPBSYZXXSDYCPBSSFYZ', title: '本体产品标识与最小销售单元产品标识是否一致', minWidth: 250 },
//                    { field: 'BTCPBS', title: '本体产品标识', width: 100 },
//                    { field: 'CPMCTYMC', title: '产品名称/通用名称', width: 150 },
                    { field: 'Remark', title: '备注', width: 200 },
                    { field: 'InMan', title: '录入人', width: 80 },
                    { field: 'InDate', title: '录入时间', width: 150 },
                    { field: 'op', title: '操作', width: 120, toolbar: '#barDemo', fixed: 'right' }

                ]],
                page: true,
                //  done: function(res, curr, count) {
                //     var that = this.elem.next();
                //     res.data.forEach(function(item, index) {
                //         if (item.Flag == "0") { // 说明评估日期已到期
                //             $(".layui-table-body tbody tr[data-index='" + index + "']").css({ 'backgroundColor': "Pink" });
                //             $(".layui-table-body tbody tr[data-index='" + index + "']").find('td[data-field="LastContactTime"]').css({ 'color': "red" });
                //
                //         }
                //     });
                // }


            });




            //监听是否选中操作
            table.on('checkbox(DIInfolist)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);


            });


            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(DIInfolist)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

                $('#txtSDICode').val(obj.data.ZXXSDYCPBS);



            });

            //监听单元格编辑
            table.on('edit(DIInfolist)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });


            //监听行工具事件
            table.on('tool(DIInfolist)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值
                if (layEvent === 'del') {
                    layer.confirm('您确实要删除该DI信息么？', function (index) {

                     //向服务端发送禁用指令
                     var sDI = data.ZXXSDYCPBS;
                     var sFlag = "3";
                     var Data = '';
                     var Params = { MNo: "", Spec: "", DI: sDI, S1: "", S2: "", S3: "", S4: "", S5: "", S6: "", S7: "", S8: "", S9: "", S10: "", S11: "", S12: "", S13: "", S13: "", S15: "", S16: "", S17: "", S18: "", Flag: sFlag };
                     var Data = JSON.stringify(Params);
                     $.ajax({
                        type: "POST",
                        url: "../Service/UDIAjax.ashx?OP=OPUDIInfo&CFlag=3",
                        data: { Data: Data },
                        success: function(data) {
                            var parsedJson = jQuery.parseJSON(data);

                            if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                layer.msg('删除成功！');

                                $('#AddDIBut_open').click();  // 重新查询
                            } else {
                                layer.msg('删除失败，请重试！')
                            }
                        },
                        error: function(data) {
                            layer.msg('删除失败2，请重试！')
                        }
                     });
                  }); // 删除

                } else if (layEvent === 'edit') {
                    $('#txtK').html("修改DI信息");
                    $('#txtAEFlag').val("2");

                    $('#txtUDIMaterNo').val(data.CPHHHBH);   //  产品编码
                    $('#txtMaterNameW').val(data.CPMS);   //  产品描述
                    $('#txtSpecW').val(data.GGXH);   //  产品型号
                    $('#txtPDNameW').val(data.CPMCTYMC);   //  产品名称 PName
                    $('#txtSPNameW').val(data.SPMC);   //  商品名称 MaterXH
                    $('#txtBLCPW').val(data.SFWBLZTLCP);   //  包类产品 Mount
                    $('#txtYMLDMW').val(data.YFLBM);   //  原目录代码 TechNo
                    $('#txtQXLBW').val(data.QXLB);   //  器械类别 MaterType
                    $('#txtFLBMW').val(data.FLBM);   //  分类编码 FillMaterKind
                    $('#txtZCRCNW').val(data.YLQXZCRBARMC);   //  注册备案人 MHeight
                    $('#txtZCRENW').val(data.YLQXZCRBARYWMC);   //  注册备案人 Diameter  YLQXZCRBARYWMC
                    $('#txtZCBHW').val(data.ZCZBHHZBAPZBH);   //  注册备案编号  Distortion
                    $('#txtCPLBW').val(data.CPLB);   // 产品类别 MaterKind
                    $('#txtAQXGW').val(data.CGZMRAQXGXX);   //  安全相关信息  StockLacation
                    $('#txtYCXW').val(data.SFBJWYCXSY);   //  一次性使用 EfftMg
                    $('#txtCFSYCSW').val(data.ZDCFSYCS);   //  重复使用次数  BatchMg
                    $('#txtWJBZW').val(data.SFWWJBZ);   //  无菌包装 KaifengMg
                    $('#txtSYQMJW').val(data.SYQSFXYJXMJ);   //  使用前灭菌  LOSHFlag
                    $('#txtMJFSW').val(data.MJFS);   //  灭菌方式  Coating
                    $('#txtQTXXW').val(data.QTXXDWZLJ);   //  其他信息链接  StockPlace
                    $('#txtYBBMW').val(data.YBBM);   // 医保编码 BatchNo
                    $('#txtTSRQW').val(data.TSRQ);   //  退市日期  LastOutDate
                    $('#txtPIPHW').val(data.SCBSSFBHPH);   //  PI 批号  TUnit
                    $('#txtPIXLHW').val(data.SCBSSFBHXLH);   //  PI 序列号 LWUnit
                    $('#txtPISCRQW').val(data.SCBSSFBHSCRQ);   // PI 生产日期 HUnit
                    $('#txtPISXRQW').val(data.SCBSSFBHSXRQ);   //  PI 失效日期  InspectMg
                    
                   
                    $("#txtDICode").val(data.ZXXSDYCPBS);   //DI码
                    $("#txtDICodeOld").val(data.ZXXSDYCPBS);   //DI码-用于修改用
                    $("#txtMDI").val(data.DIone);   //中包装DI码
                    $("#txtWDI").val(data.DItwo);   //外包装DI码
                    $("#txtQTDI").val(data.DIthree);   //其他包装DI码   
                    
                    $("#txtCMDI").removeProp("checked"); 
                    $("#txtCWDI").removeProp("checked"); 
                    $("#txtCQTDI").removeProp("checked"); 
                
                    if (data.DIone!=""){
                        $("#txtCMDI").prop('checked', true);
                    }
                    if (data.DItwo!=""){
                        $("#txtCWDI").prop('checked', true);
                    }
                    if (data.DIthree!=""){
                        $("#txtCQTDI").prop('checked', true);
                    }
                    
                    $("#txtUDIMaterNo").attr({ "disabled": "disabled" });

                    document.getElementById('light').style.display = 'block';
                    document.getElementById('fade').style.display = 'block'; 
                } 




            });




            //  查询 -- DI信息
            $('#AddDIBut_open').click(function () {
               
                var sNo = $("#txtSDNo").val();  // DI码
                var sMNo = encodeURI($("#txtSMNo").val());  // 产品编码
                var sModel = encodeURI($("#txtSModel").val());  //型号
                var Data = '';
                var Params = { MNo: sMNo, Model: sModel,Serial:sNo,Order:"",BDate:"",EDate:""};
                var Data = JSON.stringify(Params);

                table.reload('DIInfoID', {
                    method: 'post',
                    url: '../Service/BaseModuleAjax.ashx?OP=GetSerielInfo&CFlag=27&Data=' + Data,
                    where: {
                        'No': sNo,
                        'name': sMNo
                    }, page: {
                        curr: 1
                    }
                });
            });






        });




        function addDICode() {  // 新增DI码
            $('#txtAEFlag').val("1");
            $('#txtK').html("新增DI信息");
                    $('#txtUDIMaterNo').val("");   //  产品编码
                    $('#txtMaterNameW').val("");   //  产品描述
                    $('#txtSpecW').val("");  //  产品型号
                    $('#txtPDNameW').val("");    //  产品名称 PName
                    $('#txtSPNameW').val("");  //  商品名称 MaterXH
                    $('#txtBLCPW').val("");     //  包类产品 Mount
                    $('#txtYMLDMW').val("");    //  原目录代码 TechNo
                    $('#txtQXLBW').val("");    //  器械类别 MaterType
                    $('#txtFLBMW').val("");    //  分类编码 FillMaterKind
                    $('#txtZCRCNW').val("");     //  注册备案人 MHeight
                    $('#txtZCRENW').val("");    //  注册备案人 Diameter  YLQXZCRBARYWMC
                    $('#txtZCBHW').val("");   //  注册备案编号  Distortion
                    $('#txtCPLBW').val("");   // 产品类别 MaterKind
                    $('#txtAQXGW').val("");    //  安全相关信息  StockLacation
                    $('#txtYCXW').val("");   //  一次性使用 EfftMg
                    $('#txtCFSYCSW').val("");    //  重复使用次数  BatchMg
                    $('#txtWJBZW').val("");   //  无菌包装 KaifengMg
                    $('#txtSYQMJW').val("");    //  使用前灭菌  LOSHFlag
                    $('#txtMJFSW').val("");    //  灭菌方式  Coating
                    $('#txtQTXXW').val("");     //  其他信息链接  StockPlace
                    $('#txtYBBMW').val("");   // 医保编码 BatchNo
                    $('#txtTSRQW').val("");    //  退市日期  LastOutDate
                    $('#txtPIPHW').val("");   //  PI 批号  TUnit
                    $('#txtPIXLHW').val("");   //  PI 序列号 LWUnit
                    $('#txtPISCRQW').val("");   // PI 生产日期 HUnit 
                    $('#txtPISXRQW').val("");   //  PI 失效日期  InspectMg
                    $("#txtDICode").val("");   //DI码
                    $("#txtDICodeOld").val("");   //DI码
                    $("#txtMDI").val("");   //中包装DI码
                    $("#txtWDI").val("");   //外包装DI码
                    $("#txtQTDI").val("");   //其他包装DI码
                    
                    $("#txtCMDI").removeProp("checked"); 
                    $("#txtCWDI").removeProp("checked"); 
                    $("#txtCQTDI").removeProp("checked"); 
                    
                    $("#txtUDIMaterNo").removeAttr("disabled");

                    document.getElementById('light').style.display = 'block';
                    document.getElementById('fade').style.display = 'block'; 


            $("#div_warning").html("");
            $("#div_warning").hide();
            
            
            document.getElementById('light').style.display = 'block';
            document.getElementById('fade').style.display = 'block'; 
        }


        function closeDialog() {
            document.getElementById('light').style.display = 'none';
            document.getElementById('fade').style.display = 'none';
        }





    </script>
   



<style type="text/css">  

.fixed-table-container thead th .th-inner {
    line-height: 10px;  /* 改变标题的高度 */
 }

.fixed-table-footer,
.fixed-table-header {
    height: 28px; /*cellHeight*/
}

.bootstrap-table .table > thead > tr {
  height: 28px; /*cellHeight*/
}


.black_overlay{ 
            display: none; 
            position: absolute; 
            top: 0%; 
            left: 0%; 
            width: 100%; 
            height: 100%; 
            background-color:#bbbcc7; 
            z-index:1001; 
            -moz-opacity: 0.8; 
            opacity: .80;
            filter: alpha(opacity=60);
        } 


.LabelDelBtn { 
   color:White; font-size:12px; font-weight:bold; cursor:pointer;border-radius: 5px;padding: 3px 5px;background: #da542e;
}

.LabelAddBtn { 
   color:White; font-size:12px; font-weight:bold; cursor:pointer;border-radius: 5px;padding: 3px 5px;background: #579fd8;
}



        .headDI {
            font-weight: bold;
            height: 30px;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            padding-left: 5px;
            line-height: 35px;
            margin-top: 5px;
        }



</style>  



</head>


<body>


    <div class="div_find">

        <p>
            <label class="find_labela">DI码：</label> <input type="text" id="txtSDNo" class="find_input" />
            <label class="find_labela">产品编码：</label><input type="text" id="txtSMNo" class="find_input" />
            <label class="find_labela">型号：</label><input type="text" id="txtSModel" class="find_input" />
            <input type="button" value="搜索" class="find_but" id="AddDIBut_open" />
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <input type="button" value="新增DI" class="find_but" id="AddDIBut_open" onclick="addDICode()" />

        </p>
        <p id="open" style="display:none;">
            <label class="find_labela">代码：</label><input type="text" id="txtCode" class="find_input" />
        </p>
    </div>
    <div style="text-align:right">
        <!-- <i class="add2_i" ></i><a href="JavaScript:void(0)" onclick="openDialog(1)" style="color: Blue">添加</a>-->
        <!--   <i class="down_i" ></i><a href="JavaScript:void(0)" onclick="ToExcdel(1)" class="add_a">导出</a> -->

        <input type="text" class="form-control" id="txtSDICode" name="txtSDICode" style=" height:25px; display:none;" readonly=readonly />

    </div>

    <div id="Loading" style="display: none; z-index: 1000; width:100%; text-align:right;">
        打印中... <img src="../fonts/loading.gif" width="60px" height="12px" />
    </div>

    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="DIInfolist" lay-filter="DIInfolist"></table>

        <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-xs" lay-event="edit">修改</a>
            <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
        </script>

    </div>


<div id="light" style="display: none;position: absolute;top: 1%;left: 1%;right: 1%;width: 98%;height: 99%;border: 2px solid #21b6b4; background-color: white;z-index: 1002;overflow: auto;">
     <div  id="lightHead" style="height:30px;">
         <label id="txtK" style=" padding:10px;font-size: 14px; color:White; "></label>
         <label style="float:right; margin:10px 10px 0 0; cursor:pointer;" onclick="closeDialog()">X</label>
     </div>
     <div id="div_warning" role="alert" style="text-align: center; display:none;color: Red ">
         <strong id="divsuccess" style="color: Red"></strong> 
     </div>

       <table class="lightTable" cellspacing="0" cellpadding="0" border='0' style="width:98%; ">
          <tr style="font-weight:bold;background-color: #F1F4F4;height:30px;">
             <td style="height: 30px; " colspan="3">
                 产品基本信息（Product Information） 
              </td>
              <td colspan="3">
                <input type="text" class="form-control" id="txtDICode" name="txtDICode" style=" height:25px; border:0px; background-color: #F1F4F4; font-weight:bold; color:Red;readonly=readonly " />
                <input type="text" class="form-control" id="txtDICodeOld" name="txtDICodeOld" style=" height:25px; border:0px; color:Red; display:none; "  readonly=readonly />
              </td>
          </tr>
          <tr style="height:27px;">
            <td style=" width:110px; text-align:right;font-weight:bold;">
                产品编码/货号：
            </td>
            <td>
               <input type="text" class="form-control" id="txtUDIMaterNo" name="txtUDIMaterNo"  placeholder="输入产品编码，按回车"  style=" height:25px;"/>
            </td>
            <td style=" width:110px;text-align:right;font-weight:bold;">
                型号：
            </td>
            <td>
               <input type="text" class="form-control" id="txtSpecW" name="txtSpecW" style=" height:25px; border:0px; background-color:White;"  readonly=readonly />
            </td>
            <td style=" width:110px;text-align:right;font-weight:bold;">
                包类产品：
            </td>
            <td>
               <input type="text" class="form-control" id="txtBLCPW" name="txtBLCPW" style=" height:25px; border:0px; background-color:White;"  readonly=readonly />
            </td>
          </tr>
          <tr style="height:27px;">
            <td style=" width:110px;text-align:right;font-weight:bold;">
               产品描述：
            </td>
            <td colspan="5">
               <input type="text" class="form-control" id="txtMaterNameW" name="txtMaterNameW" style=" height:25px; border:0px; background-color:White;"  readonly=readonly />
            </td> 
          </tr>
          <tr style="height:27px;">
            <td style=" width:100px; text-align:right;font-weight:bold;">
                产品名称：
            </td>
            <td colspan="5">
               <input type="text" class="form-control" id="txtPDNameW" name="txtPDNameW" style=" height:25px; border:0px; background-color:White;"  readonly=readonly />
            </td>
          </tr>
          <tr style="height:27px;">
            <td style=" width:100px;text-align:right;font-weight:bold;">
               商品名称：
            </td>
            <td colspan="5">
               <input type="text" class="form-control" id="txtSPNameW" name="txtSPNameW" style=" height:25px; border:0px; background-color:White;"  readonly=readonly />
            </td> 
          </tr>  
          <tr style="height:27px;">
            <td style=" width:100px;text-align:right;font-weight:bold;">
                原目录代码：
            </td>
            <td >
               <input type="text" class="form-control" id="txtYMLDMW" name="txtYMLDMW" style=" height:25px; border:0px; background-color:White;"  readonly=readonly />
            </td>
            <td style=" width:100px; text-align:right;font-weight:bold;">
               器械类别：
            </td>
            <td>
               <input type="text" class="form-control" id="txtQXLBW" name="txtQXLBW" style=" height:25px; border:0px; background-color:White;"  readonly=readonly />
            </td> 
            <td style=" width:100px; text-align:right;font-weight:bold;">
                分类编码：
            </td>
            <td>
               <input type="text" class="form-control" id="txtFLBMW" name="txtFLBMW" style=" height:25px; border:0px; background-color:White;"  readonly=readonly /> 
            </td>
          </tr>
          <tr style="height:27px;">
            <td style=" width:100px;text-align:right;font-weight:bold;">
                注册备案编号：
            </td>
            <td >
                <input type="text" class="form-control" id="txtZCBHW" name="txtZCBHW" style=" height:25px; border:0px; background-color:White;"  readonly=readonly /> 
            </td>
            <td style=" width:100px;text-align:right;font-weight:bold;">
                注册备案人CN：  
            </td>
            <td >
                <input type="text" class="form-control" id="txtZCRCNW" name="txtZCRCNW" style=" height:25px; border:0px; background-color:White;" readonly=readonly  />  
            </td>
             <td style=" width:100px;text-align:right;font-weight:bold;">
                注册备案人EN：
            </td>
            <td >
                <input type="text" class="form-control" id="txtZCRENW" name="txtZCRENW" style=" height:25px; border:0px; background-color:White;"  readonly=readonly /> 
            </td>
          </tr>
          <tr  style="height:27px;">
            <td style=" width:100px;text-align:right;font-weight:bold;">
               产品类别：
            </td>
            <td >
                <input type="text" class="form-control" id="txtCPLBW" name="txtCPLBW" style=" height:25px; border:0px; background-color:White;"  readonly=readonly />  
            </td>
            <td style=" width:100px;text-align:right;font-weight:bold;">
               安全相关信息： 
            </td>
            <td >
               <input type="text" class="form-control" id="txtAQXGW" name="txtAQXGW" style=" height:25px; border:0px; background-color:White;"  readonly=readonly />  
            </td>
            <td style=" width:100px;text-align:right;font-weight:bold;">
               一次性使用： 
            </td>
            <td >
                <input type="text" class="form-control" id="txtYCXW" name="txtYCXW" style=" height:25px; border:0px; background-color:White;" readonly=readonly  /> 
            </td>
          </tr>
          <tr  style="height:27px;">
            <td style=" width:100px;text-align:right;font-weight:bold;">
               无菌包装：
            </td>
            <td >
                <input type="text" class="form-control" id="txtWJBZW" name="txtWJBZW" style=" height:25px; border:0px; background-color:White;"  readonly=readonly /> 
            </td>
            <td style=" width:100px;text-align:right;font-weight:bold;">
               使用前灭菌：
            </td>
            <td >
               <input type="text" class="form-control" id="txtSYQMJW" name="txtSYQMJW" style=" height:25px; border:0px; background-color:White;"  readonly=readonly />  
            </td>
            <td style=" width:100px;text-align:right;font-weight:bold;">
                灭菌方式：
            </td>
            <td >
               <input type="text" class="form-control" id="txtMJFSW" name="txtMJFSW" style=" height:25px; border:0px; background-color:White;"  readonly=readonly />  
            </td>
          </tr>
          
          <tr style="height:27px;">
            <td style=" width:100px;text-align:right;font-weight:bold;">
                重复使用次数：
            </td>
            <td >
               <input type="text" class="form-control" id="txtCFSYCSW" name="txtCFSYCSW" style=" height:25px; border:0px; background-color:White;"  readonly=readonly /> 
            </td>
            <td style=" width:100px; text-align:right;font-weight:bold;">
               医保编码：
            </td>
            <td>
               <input type="text" class="form-control" id="txtYBBMW" name="txtYBBMW" style=" height:25px; border:0px; background-color:White;"  readonly=readonly /> 
            </td> 
            <td style=" width:100px;text-align:right;font-weight:bold;">
               退市日期：
            </td>
            <td >
                <input type="text" class="form-control" id="txtTSRQW" name="txtTSRQW" style=" height:25px; border:0px; background-color:White;"  readonly=readonly /> 
            </td>
          </tr> 
          <tr  style="font-weight:bold;background-color: #F1F4F4;height:30px;">
            <td style=" height:25px;" colspan="6">
               生产标识信息（Production Identifier Information）
            </td>
          </tr>
          <tr>
            <td colspan="6">
               <table class="lightTable" cellspacing="0" cellpadding="0" border='0' style="width:98%; height:28px;">
                  <tr style="height:25px;">
                     <td style=" width:100px;text-align:right;">
                         PI 批号：
                     </td>
                     <td>
                         <input type="text" class="form-control" id="txtPIPHW" name="txtPIPHW" style=" height:25px; border:0px; background-color:White;" readonly=readonly />
                     </td>
                     <td style=" width:100px;text-align:right;">
                         PI 序列号：
                     </td>
                     <td>
                         <input type="text" class="form-control" id="txtPIXLHW" name="txtPIXLHW" style=" height:25px; border:0px; background-color:White;" readonly=readonly />
                     </td>
                     <td style=" width:100px; text-align:right;">
                         PI 生产日期：
                     </td>
                     <td>
                         <input type="text" class="form-control" id="txtPISCRQW" name="txtPISCRQW" style=" height:25px; border:0px; background-color:White;" readonly=readonly />
                     </td>
                     <td style=" width:100px;text-align:right;">
                        PI 失效日期：
                     </td>
                     <td>
                        <input type="text" class="form-control" id="txtPISXRQW" name="txtPISXRQW" style=" height:25px; border:0px; background-color:White;" readonly=readonly />
                     </td>
                  </tr>
                </table>
            </td> 
          </tr>
          <tr  style="font-weight:bold;background-color: #F1F4F4;height:30px;">
            <td style=" height:25px;" colspan="6">
               其他级别DI码
            </td>
          </tr>
          <tr>
             <td colspan="2" style=" height:25px;">
                发放中包装DI码: <input  type="checkbox" id="txtCMDI" />
             </td> 
             <td colspan="2" style=" height:25px;">
                发放外包装DI码: <input  type="checkbox" id="txtCWDI" />
             </td> 
             <td colspan="2" style=" height:25px;">
                 发放其他级别DI码: <input  type="checkbox" id="txtCQTDI" />
             </td> 
          </tr>
          <tr>
             <td colspan="2" style=" height:25px;">
               <input type="text" class="form-control" id="txtMDI" name="txtMDI" style=" height:25px; border:0px; background-color:White;" />
             </td> 
             <td colspan="2" style=" height:25px;">
               <input type="text" class="form-control" id="txtWDI" name="txtWDI" style=" height:25px; border:0px; background-color:White;"  />
             </td> 
             <td colspan="2" style=" height:25px;">
               <input type="text" class="form-control" id="txtQTDI" name="txtQTDI" style=" height:25px; border:0px; background-color:White;" />
             </td> 
          </tr>
          
          <tr>
             <td colspan="6" align= "center" style=" height:10px;">

            </td> 
          </tr>
          <tr>
            <td colspan="6" align= "center">
               <input type="button" value="发放DI码" id="PubDI_But" class="find_input" style=" background:#56dcaf;color:White;" />
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
               <input type="button" value="关闭" id="ReDI_But" class="find_input" onclick='closeDialog()' style=" background:#56dcaf; color:White;" />
               &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </td> 
          </tr>
          
      </table>
      
     <div class="input-group" style="display:none; ">
                  <span class="input-group-addon">.</span>
                  <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
                  <span class="input-group-addon">.</span>
                  <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
                  <span class="input-group-addon">.</span>
                  <input type="text" class="form-control" id="txtInName" name="txtInName" />
      </div>

</div>

<div id="fade" class="black_overlay">
</div>  

   
   

</body>
</html>