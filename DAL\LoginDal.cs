﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using Common;
using System.Collections.Generic;

namespace DAL
{
    public static class LoginDal
    {
        /// <summary>
        /// 登录验证
        /// </summary>
        /// <param name="sLogin">账号</param>
        /// <param name="password">密码</param>
        /// <returns></returns>
        public static DataTable SDLogin(string sLogin, string password)
        {
            string sSQL = string.Empty;

            SqlParameter[] ps =
            {
                new SqlParameter("@LoginName",sLogin),
                new SqlParameter("@Pwd",password),
            };
            sSQL = "select LoginName,FullName,Pwd,Kind,BSC,CompanyNo,LogoPic,DaQu,BanShiChu,ZhuGuan,WeiXinNo,QyNo,ExpireTime,Status,ImgPath " +
                   "FROM T_SPUser Where LoginName = @LoginName AND Pwd = @Pwd ";
            DataTable dt = DBHelper.GetDataTable(sSQL, default, ps);

            return dt;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public static DataTable GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select LoginName,FullName,Pwd,Kind,BSC,CompanyNo,LogoPic,DaQu,BanShiChu,ZhuGuan,WeiXinNo,QyNo,ExpireTime,Status,ImgPath FROM T_SPUser ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DBHelper.GetDataTable(strSql.ToString());

        }

        /// <summary>
        /// 获取已生成的 Token ，先判断是否过期，
        /// </summary>
        /// <param name="CorpID">企业号唯一号码</param>
        /// <param name="sFlag"> 备用</param>
        /// <returns></returns>
        public static string GetExistToken(string CorpID, string sFlag)
        {
            string sSQL = string.Empty;
            string sToken = string.Empty;

            sSQL = "select Token,DATEDIFF(Minute,InDate,getdate()) as Cha from T_Token where CorpID='" + CorpID + "' ";
            DataTable sdt = DBHelper.GetDataTable(sSQL);
            if (sdt.Rows.Count > 0)
            {
                if (int.Parse(sdt.Rows[0]["Cha"].ToString()) < 100) // 120 分钟过期，目前只定义100 分钟就重新获取了
                {
                    sToken = sdt.Rows[0]["Token"].ToString();
                }
                else  //  
                {
                    sToken = "";
                }
            }
            else
            {
                sToken = "";
            }

            return sToken;
        }

        /// <summary>
        ///  插入TokenID
        /// </summary>
        /// <param name="CorpID"></param>
        /// <param name="Secret"></param>
        /// <param name="Token"></param>
        /// <param name="sFlag">1:插入或更新；2：表示清空</param>
        /// <returns></returns>
        public static int InsertUpdateToken(string CorpID, string Secret, string Token, string sFlag)
        {
            string sSQL = string.Empty;
            int iFlag = 0;

            if (sFlag == "1") // 更新或插入
            {
                sSQL = "select CorpID from T_Token where CorpID='" + CorpID + "' ";
                DataTable sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sSQL = "update T_Token set Token='" + Token + "',InDate=getdate() where CorpID='" + CorpID + "' ";
                }
                else
                {
                    sSQL = " insert into T_Token(CorpID,Secret,Token) values('" + CorpID + "','" + Secret + "','" + Token + "') ";
                }
            }
            else // 清空
            {
                sSQL = "update T_Token set Token='' where CorpID='" + CorpID + "' ";
            }

            try
            {
                iFlag = DBHelper.ExecuteCommand(sSQL);
            }
            catch (Exception ex)
            {
                iFlag = 0;

                string ss = ex.Message.ToString();

                throw;
            }

            return iFlag;
        }

        /// <summary>
        /// 返回登录人是否有操作模块的权限
        /// </summary>
        /// <param name="InMan"></param>
        /// <param name="Module"></param>
        /// <returns></returns>
        public static string JustOperateRight(string InMan, string Module)
        {
            string sSQL = string.Empty;
            string sBStr = string.Empty;

            // 判断登录人的身份
            sSQL = "select a.LoginName,a.RoleID,c.ModuleParent,c.ModuleName from T_SPUserRole a " +
                   "join T_SPRoleModule b on a.RoleID=b.RoleID " +
                   "join T_SPModule c on b.ModuleID=c.ModuleID " +
                   "where a.LoginName='" + InMan + "' and c.ModuleCode='" + Module + "' ";
            DataTable sdt = DBHelper.GetDataTable(sSQL);
            if (sdt.Rows.Count > 0)
            {
                sBStr = "YES";
            }
            else
            {
                sBStr = "NO";
            }

            return sBStr;
        }


        /// <summary>
        /// 插入用户手工填写的注册信息
        /// </summary>
        /// <param name="Phone"></param>
        /// <param name="WX"></param>
        /// <param name="CompName"></param>
        /// <param name="CMan"></param>
        /// <param name="HY"></param>
        /// <param name="Remark"></param>
        /// <param name="Code"></param>
        /// <param name="Kind"></param>
        /// <returns></returns>
        public static string InsertUserInfo(string Phone, string WX, string CompName, string sLogin, string CMan, string HY, string Remark, string Code, string Kind, string Comp)
        {
            string sFlag = string.Empty;
            string sstr = string.Empty;
            string sPw = Phone.Substring(7, 4);  //  13510232222

            string sSQL = " insert into T_SPUser(LoginName,FullName,Pwd,Kind,Supplier,CompanyNo,DaQu,BSC,BanShiChu,QYNo,WeiXinNo,Phone,SFCode,YHK,KHH,Remark,OperatorID,OperatorTime) " +
                          " values('" + sLogin + "','" + CMan + "','" + sPw + "','" + Kind + "','" + CompName + "','" + Comp + "','','','" + HY + "','','" + WX + "','" + Phone + "','','','','" + Remark + "','" + sLogin + "',getdate()) ";

            List<string> links = new List<string>();
            links.Add(sSQL);

            try
            {
                sFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                string ss = ex.Message.ToString();

                throw;
            }

            return sFlag + sstr;
        }












    }
}
