﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web.User
{
    public partial class UpdateFile : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {

        }


        protected void bntImport2_Click(object sender, EventArgs e)
        {
            string FilePath = fileImport2.PostedFile.FileName;


            string savePath = Server.MapPath(("TempUpFile\\") + fileImport2.FileName);//Server.MapPath 获得虚拟服务器相对路径
            fileImport2.SaveAs(savePath);   // 把本地文件放到指定服务器文件夹


            sMessage.Text = "上传成功！";
        }




    }
}
