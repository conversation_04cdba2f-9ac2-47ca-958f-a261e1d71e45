﻿<!DOCTYPE html
          PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title></title>
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/echarts.js"></script>

    <script>
        $(function () {

            var myChart1 = echarts.init(document.getElementById('myChart1'));
            var option1 = {
                title: {
                    text: '设备开动率',
                    textStyle: {
                        color: "white",
                        fontWeight: "lighter",
                        fontSize: "15px",
                        fontWeight: "500"
                    },
                    left: "center"
                },
                grid: {
                    top: "50%"
                },
                series: [
                    {
                        type: 'gauge',
                        startAngle: 90,
                        endAngle: -270,
                        pointer: {
                            show: false
                        },
                        progress: {
                            show: true,
                            overlap: false,
                            roundCap: true,
                            clip: false,
                            itemStyle: {
                                color: '#57a8e2',
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                width: 15
                            }
                        },
                        splitLine: {
                            show: false,
                            distance: 0,
                            length: 10
                        },
                        axisTick: {
                            show: false
                        },
                        axisLabel: {
                            show: false,
                            distance: 10
                        },
                        data: [
                            {
                                value: 90.50,
                                detail: {
                                    valueAnimation: true,
                                    offsetCenter: ['0%', '0%']
                                }
                            }
                        ],
                        detail: {
                            width: 50,
                            height: 14,
                            fontSize: 28,
                            color: '#02599b',
                            formatter: '{value}%'
                        }
                    }
                ]
            };

            myChart1.setOption(option1);


            var myChart2 = echarts.init(document.getElementById('myChart2'));
            var option2 = {
                title: {
                    text: '设备故障率',
                    textStyle: {
                        color: "white",
                        fontWeight: "lighter",
                        fontSize: "15px",
                        fontWeight: "500"
                    },
                    left: "center"
                },
                grid: {
                    top: "50%"
                },
                series: [
                    {
                        type: 'gauge',
                        startAngle: 90,
                        endAngle: -270,
                        pointer: {
                            show: false
                        },
                        progress: {
                            show: true,
                            overlap: false,
                            roundCap: true,
                            clip: false,
                            itemStyle: {
                                color: '#57a8e2',
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                width: 15
                            }
                        },
                        splitLine: {
                            show: false,
                            distance: 0,
                            length: 10
                        },
                        axisTick: {
                            show: false
                        },
                        axisLabel: {
                            show: false,
                            distance: 10
                        },
                        data: [
                            {
                                value: 8.5,
                                detail: {
                                    valueAnimation: true,
                                    offsetCenter: ['0%', '0%']
                                }
                            }
                        ],
                        detail: {
                            width: 50,
                            height: 14,
                            fontSize: 28,
                            color: '#02599b',
                            formatter: '{value}%'
                        }
                    }
                ]
            };

            myChart2.setOption(option2);


            var myChart3 = echarts.init(document.getElementById('myChart3'));

            var option3 = {
                title: {
                    text: '设备运行情况',
                    textStyle: {
                        color: "white",
                        fontWeight: "lighter",
                        fontSize: "15px",
                        fontWeight: "500"
                    },
                    left: "center"
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['作业时间', '维修时间'],
                    textStyle: {
                        color: "white"
                    },
                    left: "left"
                },
                grid: {
                    left: '3%',
                    right: '3%',
                    bottom: '10%',
                    containLabel: true
                },
                toolbox: {
                    show: true,
                    feature: {
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ['line', 'bar'] },
                        restore: { show: true },
                        saveAsImage: { show: true }
                    }
                },
                xAxis: [
                    {
                        type: 'category',
                        // prettier-ignore
                        data: ['MP-30WIFI测试工装', '注射泵压力检测板资源烧录工装', 'MP-30级联测试工装', '条码打印机', '安规仪器点检器', '医用耐压测试仪', '万用表', '电子天平', 'SYS-70压力自动校准工装', 'MD67 测量模块测试工装', '输注泵测试工装', 'HP80老化工装']
                    }
                ],

                dataZoom: [{
                    type: 'slider',
                    show: true, //flase直接隐藏图形
                    xAxisIndex: [0],
                    left: '9%', //滚动条靠左侧的百分比
                    right: '9%',
                    bottom: 20,
                    height: 2,
                    start: 0,//滚动条的起始位置
                    end: 50
                }],

                yAxis: [
                    {
                        type: 'value'
                    }
                ],
                series: [
                    {
                        name: '作业时间',
                        type: 'bar',
                        data: [
                            90, 8, 74, 23, 25, 76, 135, 162, 32, 20, 6, 50
                        ],
                        label: {
                            show: true,
                            position: 'top', // 标签位置
                            backgroundColor: "#1b1e25",
                            color: "white",
                        },
                    },
                    {
                        name: '维修时间',
                        type: 'bar',
                        data: [
                            20, 50, 89, 29, 28, 70, 175, 182, 48, 68, 6, 2
                        ],
                        label: {
                            show: true,
                            position: 'top', // 标签位置
                            backgroundColor: "#1b1e25",
                            color: "white"
                        },
                    },

                ]
            };

            myChart3.setOption(option3);

            $(window).resize(function () {
                myChart1.resize();
                myChart2.resize();
                myChart3.resize();
            })
        })


    </script>

    <style>
        * {
            padding: 0;
            margin: 0
        }

        #container {
            height: 100vh;
            background: #15181d;
            overflow: hidden;
            position: relative;
            color: #ffffff;
        }

        .container-header {
            height: 40%;
            display: flex;
            margin-bottom: 10px;
        }

        .container-body {
            background-color: #1b1e25;
            height: 57.5%;
            margin: 0px 10px 10px 10px;
        }

        .header-left {
            flex: 1;
            background-color: #1b1e25;
            margin-left: 10px;
            margin-right: 5px;
        }

        .header-right {
            flex: 1;
            background-color: #1b1e25;
            margin-right: 10px;
            margin-left: 5px;
        }
    </style>

</head>


<body>
    <div id="container">
        <!--顶部-->
        <div class="container-header">
            <div class="header-left">
                <div id="myChart1" style="width: 100%; height: 100%;"></div>
            </div>
            <div class="header-right">
                <div id="myChart2" style="width: 100%; height: 100%;"></div>
            </div>
        </div>
        <div class="container-body">
            <div id="myChart3" style="width: 100%; height: 100%;"></div>
        </div>
    </div>
</body>

</html>