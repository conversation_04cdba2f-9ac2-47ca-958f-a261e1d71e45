﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Model
{
    public class DhrEntity
    {
        public string EXENo { get; set; } = string.Empty;           // 执行编号
        public string OrderNo { get; set; } = string.Empty;         // 订单编号
        public string SerialNo { get; set; } = string.Empty;        // 序列号
        public string ProcedureName { get; set; } = string.Empty;   // 工序名称
        public string ProcedureNo { get; set; } = string.Empty;     // 工序编号
        public string ProcedureVer { get; set; } = string.Empty;    // 工序版本
        public int FlowOrder { get; set; } = 0;                     // 流程顺序
        public string ProductNo { get; set; } = string.Empty;       // 产品编号
        public string OPFlag { get; set; } = string.Empty;          // 操作标志
    }

    public class BaseInfo
    {
        public string MaterNo { get; set; } = string.Empty;         // 物料编号
        public string MaterName { get; set; } = string.Empty;       // 物料名称
        public string BOMEN { get; set; } = string.Empty;           // BOM 版本
        public string BOMNo { get; set; } = string.Empty;           // BOM 编号
        public string OrderNum { get; set; } = string.Empty;        // 订单数量
        public string OrderNo { get; set; } = string.Empty;         // 订单编号
        public string Remark { get; set; } = string.Empty;          // 备注
        public string SerielNo { get; set; } = string.Empty;        // 序列号
        public string Model { get; set; } = string.Empty;           // 型号
        public string ProcedureName { get; set; } = string.Empty;   // 工序名称
        public string ProcedureVer { get; set; } = string.Empty;    // 工序版本                    
        public string UDI { get; set; } = string.Empty;             // UDI 信息
        public string UDICode { get; set; } = string.Empty;         // UDI 编码
    }

    public class DeviceInfo
    {
        public string Id { get; set; } = string.Empty;              // 序号
        public string DeviceName { get; set; } = string.Empty;      // 设备名称
        public string MaterNo { get; set; } = string.Empty;         // 物料编号
        public string DeviceNo { get; set; } = string.Empty;        // 设备编号
        public string UseDate { get; set; } = string.Empty;         // 使用日期（格式：yyyy-MM-dd）
    }

    public class MaterialInfo
    {
        public string Id { get; set; } = string.Empty;              // 序号
        public string SerialNo { get; set; } = string.Empty;        // 序列号
        public string MaterBatchNo { get; set; } = string.Empty;    // 物料批次号
        public string MaterNo { get; set; } = string.Empty;         // 物料编号
        public string MaterName { get; set; } = string.Empty;       // 物料名称
        public string RUnitNo { get; set; } = string.Empty;         // 单位编号
        public string InDate { get; set; } = string.Empty;          // 入库日期（格式：yyyy-MM-dd）
        public string ImgPath { get; set; } = string.Empty;         // 图片路径（拼接后的完整路径）
    }

    public class TestItemInfo
    {
        public string Id { get; set; } = string.Empty;              // 序号
        public string NameCH { get; set; } = string.Empty;          // 测试项中文名称
        public string DescCH { get; set; } = string.Empty;          // 测试项描述
        public string RUnitNo { get; set; } = string.Empty;         // 单位编号
        public string ActualResult { get; set; } = string.Empty;    // 实际测试结果
        public string TestResult { get; set; } = string.Empty;      // 测试结果
        public string ImgPath { get; set; } = string.Empty;         // 图片路径
        public string InDate { get; set; } = string.Empty;          // 测试日期（格式：yyyy-MM-dd）
    }

    public class TestItemEditLog
    {
        public string Id { get; set; } = string.Empty;              // 序号
        public string NameCH { get; set; } = string.Empty;          // 测试项中文名称
        public string DescCH { get; set; } = string.Empty;          // 测试项描述
        public string ActualResult { get; set; } = string.Empty;    // 实际测试结果
        public string TestResult { get; set; } = string.Empty;      // 测试结果
        public string OldTestValue { get; set; } = string.Empty;    // 原测试值
        public string Remark { get; set; } = string.Empty;          // 修改原因
        public string ImgPath { get; set; } = string.Empty;         // 图片路径
        public string InDate { get; set; } = string.Empty;          // 操作日期
    }

    public class Inspection
    {
        public string InspectScheme { get; set; } = string.Empty;   // 检验方案
        public string OrderNums { get; set; } = string.Empty;       // 订单数量
        public string SampleSize { get; set; } = string.Empty;      // 样本数量
        public string RNum { get; set; } = string.Empty;            // 检验编号
        public string InspectLevel { get; set; } = string.Empty;    // 检验等级
        public string Stringency { get; set; } = string.Empty;      // 严格度
        public string Aql { get; set; } = string.Empty;             // AQL 值
        public string VerifyMode { get; set; } = string.Empty;      // 检验方式
    }

    //public class MaterialTraceInfo
    //{
    //    public int Id { get; set; } = 0;                            // 序号
    //    public string SerialNo { get; set; } = string.Empty;        // 批次号
    //    public string MaterNo { get; set; } = string.Empty;         // 物料编号
    //    public string MaterName { get; set; } = string.Empty;       // 物料名称
    //    public string ImgPath { get; set; } = string.Empty;         // 图片路径
    //    public string InDate { get; set; } = string.Empty;          // 入库日期
    //}

    public class ProductionProcessInfo
    {
        public string Id { get; set; } = string.Empty;              // 序号
        public string ProcedureName { get; set; } = string.Empty;   // 工序名称
        public string ImgPath { get; set; } = string.Empty;         // 图片路径
        public string EndDate { get; set; } = string.Empty;         // 完成日期
    }


    //public class BasicInfoHeader
    //{
    //    public string SerielNo { get; set; } = string.Empty;        // 序列号
    //    public string MaterNo { get; set; } = string.Empty;         // 物料编号
    //    public string MaterName { get; set; } = string.Empty;       // 物料名称
    //    public string BOMEN { get; set; } = string.Empty;           // BOM 版本
    //    public string Model { get; set; } = string.Empty;           // 型号
    //    public int OrderNum { get; set; } = 0;                      // 订单数量
    //    public string OrderNo { get; set; } = string.Empty;         // 订单编号
    //    public string UDI { get; set; } = string.Empty;             // UDI 信息
    //    public string UDICode { get; set; } = string.Empty;         // UDI 编码
    //}

    public class RepairHeader
    {
        public string OrderNo { get; set; } = string.Empty;             // 订单编号
        public string MaterNo { get; set; } = string.Empty;             // 物料编号
        public string BatchNo { get; set; } = string.Empty;             // 批次编号
        public string MaterName { get; set; } = string.Empty;           // 物料名称
        public string DeptName { get; set; } = string.Empty;            // 部门名称
        public string BadNum { get; set; } = string.Empty;              // 不良数量
        public string FDate { get; set; } = string.Empty;               // 故障发生日期
        public string Model { get; set; } = string.Empty;               // 型号
        public string OrderKind { get; set; } = string.Empty;           // 订单类型
        public string ProcedureName { get; set; } = string.Empty;       // 工序名称
        public string DealWay { get; set; } = string.Empty;             // 处理方式
        public string MaintainType { get; set; } = string.Empty;        // 维修类型
        public string FImagePath { get; set; } = string.Empty;          // 发现人图片路径
        public string ImagePath { get; set; } = string.Empty;           // 录入人员图片路径
        public string RepairConfirmation { get; set; } = string.Empty;  // 维修确认
        public string InDate { get; set; } = string.Empty;              // 录入日期
        public string BackProcedureName { get; set; } = string.Empty;   // 返还工序名称
    }

    public class BadPhenomenon
    {
        public string ECode { get; set; } = string.Empty;               // 不良现象代码
        public string EName { get; set; } = string.Empty;               // 不良现象名称
        public string EDesc { get; set; } = string.Empty;               // 不良现象描述
        public string XInMan { get; set; } = string.Empty;              // 录入人员姓名
        public string XInDate { get; set; } = string.Empty;             // 录入日期
    }
    public class BadCause
    {
        public string CNo { get; set; } = string.Empty;                 // 不良原因编号
        public string CName { get; set; } = string.Empty;               // 不良原因名称
        public string CDesc { get; set; } = string.Empty;               // 不良原因描述
        public string YInMan { get; set; } = string.Empty;              // 录入人员姓名
        public string YInDate { get; set; } = string.Empty;             // 录入日期
    }

    public class Material
    {
        public string MNo { get; set; } = string.Empty;                 // 物料编号
        public string ConsumeNum { get; set; } = string.Empty;          // 消耗数量
        public string MName { get; set; } = string.Empty;               // 物料名称
        public string MaterBatch { get; set; } = string.Empty;          // 物料批次
        public string DealSuggestion { get; set; } = string.Empty;      // 处理建议
    }

    public class OperationResult
    {
        public string SerialNo { get; set; }
        public string ProcedureName { get; set; }
        public string DHRType { get; set; }
        public string Path { get; set; }
        public string Msg { get; set; }
        public string ExceptionMessage { get; set; }
    }
}
