﻿using System;
using System.Data;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Xml.Linq;
using System.Collections.Generic;

namespace Model
{
    public class K3InStockEntity
    {
    }

    public class JsonObj
    {
        string formId;
        public string FormId
        {
            get { return formId; }
            set { formId = value; }
        }

        JsonData _data;
        public JsonData data
        {
            get { return _data; }
            set { _data = value; }
        }
    }
    public class JsonData {
        string needUpDateFields;
        public string NeedUpDateFields
        {
            get { return needUpDateFields; }
            set { needUpDateFields = value; }
        }
        string needReturnFields;
        public string NeedReturnFields
        {
            get { return needReturnFields; }
            set { needReturnFields = value; }
        }
        string isDeleteEntry;
        public string IsDeleteEntry
        {
            get { return isDeleteEntry; }
            set { isDeleteEntry = value; }
        }
        string subSystemId;
        public string SubSystemId
        {
            get { return subSystemId; }
            set { subSystemId = value; }
        }
        string isVerifyBaseDataField;
        public string IsVerifyBaseDataField
        {
            get { return isVerifyBaseDataField; }
            set { isVerifyBaseDataField = value; }
        }
        string isEntryBatchFill;
        public string IsEntryBatchFill
        {
            get { return isEntryBatchFill; }
            set { isEntryBatchFill = value; }
        }
        string validateFlag;
        public string ValidateFlag
        {
            get { return validateFlag; }
            set { validateFlag = value; }
        }
        string numberSearch;
        public string NumberSearch
        {
            get { return numberSearch; }
            set { numberSearch = value; }
        }
        string isAutoAdjustField;
        public string IsAutoAdjustField
        {
            get { return isAutoAdjustField; }
            set { isAutoAdjustField = value; }
        }
        string interationFlags;
        public string InterationFlags
        {
            get { return interationFlags; }
            set { interationFlags = value; }
        }
        string ignoreInterationFlag;
        public string IgnoreInterationFlag
        {
            get { return ignoreInterationFlag; }
            set { ignoreInterationFlag = value; }
        }
        string isControlPrecision;
        public string IsControlPrecision
        {
            get { return isControlPrecision; }
            set { isControlPrecision = value; }
        }
        string validateRepeatJson;
        public string ValidateRepeatJson
        {
            get { return validateRepeatJson; }
            set { validateRepeatJson = value; }
        }

        bool isAutoSubmitAndAudit;
        public bool IsAutoSubmitAndAudit
        {
            get { return isAutoSubmitAndAudit; }
            set { isAutoSubmitAndAudit = value; }
        }

        JsonModel model;//obj
        public JsonModel Model
        {
            get { return model; }
            set { model = value; }
        }
    }
    public class JsonModel {

        int fID;
        public int FID
        {
            get { return fID; }
            set { fID = value; }
        }
        JsonFNumber fBillType;
        public JsonFNumber FBillType
        {
            get { return fBillType; }
            set { fBillType = value; }
        }
        string fDate;
        public string FDate
        {
            get { return fDate; }
            set { fDate = value; }
        }
        JsonFNumber fStockOrgId;//obj
        public JsonFNumber FStockOrgId
        {
            get { return fStockOrgId; }
            set { fStockOrgId = value; }
        }

        JsonFNumber fFStockId0;//obj
        public JsonFNumber FStockId0
        {
            get { return fFStockId0; }
            set { fFStockId0 = value; }
        }

        JsonFNumber fPrdOrgId;//obj
        public JsonFNumber FPrdOrgId
        {
            get { return fPrdOrgId; }
            set { fPrdOrgId = value; }
        }

        JsonFNumber fFWorkShopId;//obj
        public JsonFNumber FWorkShopId
        {
            get { return fFWorkShopId; }
            set { fFWorkShopId = value; }
        }

        string fOwnerTypeId0;
        public string FOwnerTypeId0
        {
            get { return fOwnerTypeId0; }
            set { fOwnerTypeId0 = value; }
        }
        JsonFNumber fOwnerId0;//obj
        public JsonFNumber FOwnerId0
        {
            get { return fOwnerId0; }
            set { fOwnerId0 = value; }
        }

        JsonFNumber fFSTOCKERID;//obj
        public JsonFNumber FSTOCKERID
        {
            get { return fFSTOCKERID; }
            set { fFSTOCKERID = value; }
        }

        string fFDescription;
        public string FDescription
        {
            get { return fFDescription; }
            set { fFDescription = value; }
        }

        bool f_BHR_WMSBack;
        public bool F_BHR_WMSBack
        {
            get { return f_BHR_WMSBack; }
            set { f_BHR_WMSBack = value; }
        }
        bool f_BHR_NoWMS;
        public bool F_BHR_NoWMS
        {
            get { return f_BHR_NoWMS; }
            set { f_BHR_NoWMS = value; }
        }
        bool f_BHR_WMS;
        public bool F_BHR_WMS
        {
            get { return f_BHR_WMS; }
            set { f_BHR_WMS = value; }
        }
        bool f_BHR_ZDZF;
        public bool F_BHR_ZDZF
        {
            get { return f_BHR_ZDZF; }
            set { f_BHR_ZDZF = value; }
        }
        JsonEntity[] fEntity;
        public JsonEntity[] FEntity
        {
            get { return fEntity; }
            set { fEntity = value; }
        }
    }
    public class JsonEntity 
    {
        int fSrcEntryId;
        public int FSrcEntryId
        {
            get { return fSrcEntryId; }
            set { fSrcEntryId = value; }
        }
        bool fIsNew;
        public bool FIsNew
        {
            get { return fIsNew; }
            set { fIsNew = value; }
        }
        JsonFNumber fMaterialId;//obj
        public JsonFNumber FMaterialId
        {
            get { return fMaterialId; }
            set { fMaterialId = value; }
        }
        string fProductType;
        public string FProductType
        {
            get { return fProductType; }
            set { fProductType = value; }
        }
        string fInStockType;
        public string FInStockType
        {
            get { return fInStockType; }
            set { fInStockType = value; }
        }
        JsonFNumber fUnitID;//obj
        public JsonFNumber FUnitID
        {
            get { return fUnitID; }
            set { fUnitID = value; }
        }
        double fMustQty;
        public double FMustQty
        {
            get { return fMustQty; }
            set { fMustQty = value; }
        }
        double fRealQty;
        public double FRealQty
        {
            get { return fRealQty; }
            set { fRealQty = value; }
        }
        JsonFNumber fBaseUnitId;//obj
        public JsonFNumber FBaseUnitId
        {
            get { return fBaseUnitId; }
            set { fBaseUnitId = value; }
        }
        string fOwnerTypeId;
        public string FOwnerTypeId
        {
            get { return fOwnerTypeId; }
            set { fOwnerTypeId = value; }
        }
        JsonFNumber fOwnerId;//obj
        public JsonFNumber FOwnerId
        {
            get { return fOwnerId; }
            set { fOwnerId = value; }
        }
        JsonFNumber fStockId;//obj
        public JsonFNumber FStockId
        {
            get { return fStockId; }
            set { fStockId = value; }
        }

        JsonFNumber fBomId;
        public JsonFNumber FBomId
        {
            get { return fBomId; }
            set { fBomId = value; }
        }

        bool fISBACKFLUSH;
        public bool FISBACKFLUSH
        {
            get { return fISBACKFLUSH; }
            set { fISBACKFLUSH = value; }
        }
        JsonFdeptid fWorkShopId1;//obj
        public JsonFdeptid FWorkShopId1
        {
            get { return fWorkShopId1; }
            set { fWorkShopId1 = value; }
        }
        double fSecRealQty;
        public double FSecRealQty
        {
            get { return fSecRealQty; }
            set { fSecRealQty = value; }
        }
        string fMoBillNo;
        public string FMoBillNo
        {
            get { return fMoBillNo; }
            set { fMoBillNo = value; }
        }
        int fMoId;
        public int FMoId
        {
            get { return fMoId; }
            set { fMoId = value; }
        }
        int fMoEntryId;
        public int FMoEntryId
        {
            get { return fMoEntryId; }
            set { fMoEntryId = value; }
        }
        int fSrcInterId;
        public int FSrcInterId
        {
            get { return fSrcInterId; }
            set { fSrcInterId = value; }
        }
        int fMoEntrySeq;
        public int FMoEntrySeq
        {
            get { return fMoEntrySeq; }
            set { fMoEntrySeq = value; }
        }
        JsonFNumber fStockUnitId;//obj
        public JsonFNumber FStockUnitId
        {
            get { return fStockUnitId; }
            set { fStockUnitId = value; }
        }
        double fStockRealQty;
        public double FStockRealQty
        {
            get { return fStockRealQty; }
            set { fStockRealQty = value; }
        }
        bool fIsFinished;
        public bool FIsFinished
        {
            get { return fIsFinished; }
            set { fIsFinished = value; }
        }
        JsonFNumber fStockStatusId;//obj
        public JsonFNumber FStockStatusId
        {
            get { return fStockStatusId; }
            set { fStockStatusId = value; }
        }
        int fSrcEntrySeq;
        public int FSrcEntrySeq
        {
            get { return fSrcEntrySeq; }
            set { fSrcEntrySeq = value; }
        }
        int fMOMAINENTRYID;
        public int FMOMAINENTRYID
        {
            get { return fMOMAINENTRYID; }
            set { fMOMAINENTRYID = value; }
        }
        string fSrcBillType;
        public string FSrcBillType
        {
            get { return fSrcBillType; }
            set { fSrcBillType = value; }
        }
        string fSrcBillNo;
        public string FSrcBillNo
        {
            get { return fSrcBillNo; }
            set { fSrcBillNo = value; }
        }
        string fKeeperTypeId;
        public string FKeeperTypeId
        {
            get { return fKeeperTypeId; }
            set { fKeeperTypeId = value; }
        }
        double fBasePrdRealQty;
        public double FBasePrdRealQty
        {
            get { return fBasePrdRealQty; }
            set { fBasePrdRealQty = value; }
        }
        JsonFNumber fKeeperId;//obj
        public JsonFNumber FKeeperId
        {
            get { return fKeeperId; }
            set { fKeeperId = value; }
        }
        string fProduceDate;
        public string FProduceDate
        {
            get { return fProduceDate; }
            set { fProduceDate = value; }
        }
        string fExpiryDate;
        public string FExpiryDate
        {
            get { return fExpiryDate; }
            set { fExpiryDate = value; }
        }
        List<JsonSerial> fSerialSubEntity;//snobj
        public List<JsonSerial> FSerialSubEntity
        {
            get { return fSerialSubEntity; }
            set { fSerialSubEntity = value; }
        }
        JsonLink[] fEntity_Link;
        public JsonLink[] FEntity_Link
        {
            get { return fEntity_Link; }
            set { fEntity_Link = value; }
        }
        JsonFlowId fBFLowId;
        public JsonFlowId FBFLowId
        {
            get { return fBFLowId; }
            set { fBFLowId = value; }
        }
    }
    public class JsonFNumber
    {
        string fNumber;
        public string FNumber
        {
            get { return fNumber; }
            set { fNumber = value; }
        }
    }
    public class JsonSerial {
        string fSerialNo;

        public string FSerialNo
        {
            get { return fSerialNo; }
            set { fSerialNo = value; }
        }
        int fQty;

        public int FQty
        {
            get { return fQty; }
            set { fQty = value; }
        }
        string fSerialNote;

        public string FSerialNote
        {
            get { return fSerialNote; }
            set { fSerialNote = value; }
        }
    }
    public class JsonLink
    {

        string fEntity_Link_FRuleId;
        public string FEntity_Link_FRuleId
        {
            get { return fEntity_Link_FRuleId; }
            set { fEntity_Link_FRuleId = value; }
        }
        string fEntity_Link_FSTableName;
        public string FEntity_Link_FSTableName
        {
            get { return fEntity_Link_FSTableName; }
            set { fEntity_Link_FSTableName = value; }
        }
        string fEntity_Link_FSBillId;
        public string FEntity_Link_FSBillId
        {
            get { return fEntity_Link_FSBillId; }
            set { fEntity_Link_FSBillId = value; }
        }
        string fEntity_Link_FSId;
        public string FEntity_Link_FSId
        {
            get { return fEntity_Link_FSId; }
            set { fEntity_Link_FSId = value; }
        }
        string fEntity_Link_FFlowId;
        public string FEntity_Link_FFlowId
        {
            get { return fEntity_Link_FFlowId; }
            set { fEntity_Link_FFlowId = value; }
        }
        string fEntity_Link_FFlowLineId;
        public string FEntity_Link_FFlowLineId
        {
            get { return fEntity_Link_FFlowLineId; }
            set { fEntity_Link_FFlowLineId = value; }
        }
        string fEntity_Link_FBasePrdRealQtyOld;
        public string FEntity_Link_FBasePrdRealQtyOld
        {
            get { return fEntity_Link_FBasePrdRealQtyOld; }
            set { fEntity_Link_FBasePrdRealQtyOld = value; }
        }
        string fEntity_Link_FBasePrdRealQty;
        public string FEntity_Link_FBasePrdRealQty
        {
            get { return fEntity_Link_FBasePrdRealQty; }
            set { fEntity_Link_FBasePrdRealQty = value; }
        }
    }
    public class JsonFlowId
    {
        string fID;

        public string FID
        {
            get { return fID; }
            set { fID = value; }
        }
    }

    public class JsonFdeptid
    {
        int fDEPTID;
        public int FDEPTID
        {
            get { return fDEPTID; }
            set { fDEPTID = value; }
        }
    }

}
