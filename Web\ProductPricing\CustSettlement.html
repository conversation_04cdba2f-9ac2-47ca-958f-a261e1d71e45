﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>客户结算管理</title>

    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <link href="../css/orderinfo.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <script src="../layui/layui.js" type="text/javascript"></script>
    <link href="../js/skin/layer.css" rel="stylesheet" />

    <script src="../js/layer/layer.js" type="text/javascript"></script>
    <script src="../js/PrdAssist.js" type="text/javascript"></script>


    <link href="../css/XC.css" rel="stylesheet" />


    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        //$('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=28-1' });
                    }
                }
            })
        });

    </script>

    <script type="text/javascript">

        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#Companylist',
                id: 'CompanylistID',
                url: '../Service/CompanyPricing.ashx?OP=GetCompanyList',
                height: 'full-80',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'CompID', title: '公司ID', width: 80, sort: true },
                    { field: 'CompName', title: '公司名称', width: 180, sort: true },
                    { field: 'NameJC', title: '公司简称', width: 100, sort: true },
                    { field: 'LegalMan', title: '法人代表', width: 70 },
                    { field: 'OrganizationCode', title: '组织机构代码', minWidth: 180 },
                    { field: 'Category', title: '注册类型', minWidth: 90 },
                    { field: 'BusinessScope', title: '经营范围', minWidth: 200 },
                    { field: 'RegAmount', title: '注册资金', minWidth: 70 },
                    { field: 'TaxNumber', title: '税务登记证号', minWidth: 180 },
                    { field: 'RegAddr', title: '注册地址', minWidth: 200 },
                    { field: 'CompAddr', title: '公司地址', width: 200 },
                    { field: 'PAddr', title: '生产地址', width: 200 },
                    { field: 'PostalCode', title: '邮政编码', width: 70 },
                    { field: 'CMan', title: '联系人', width: 70 },
                    { field: 'Phone', title: '电话', minWidth: 100 },
                    { field: 'Fax', title: '传真', minWidth: 100 },
                    { field: 'EMail', title: 'EMAIL', minWidth: 200 },
                    { field: 'APPID', title: 'APPID', minWidth: 200 },
                    { field: 'APPSECRET', title: 'APPSECRET', minWidth: 200 },
                    { field: 'Status', title: '状态', width: 80 },
                    { field: 'Remark', title: '备注', width: 200 },
                    { field: 'InMan', title: '录入人', width: 80 },
                    { field: 'InDate', title: '录入时间', width: 150 },
                    { field: 'op', title: '操作', width: 100, toolbar: '#barDemo', fixed: 'right' }
                ]],
                page: true,
            });




            //监听是否选中操作
            table.on('checkbox(QCControlPlanHeadlist)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);


            });


            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(QCControlPlanHeadlist)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

            });

            //监听单元格编辑
            table.on('edit(QCControlPlanHeadlist)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });


            //监听行工具事件
            table.on('tool(Companylist)', function (obj) { //注：tool 是工具条事件名，Companylist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值
                if (layEvent === 'detail') {
                    $('#head-title2').html("客户套餐信息详情");

                    $("#txtCompID").val(data.CompID);
                    $("#txtCompName").val(data.CompName);
                    $("#txtNameJC").val(data.NameJC);
                    $("#txtRemark").val(data.Remark);

                    //显示客户当前套餐和历史套餐信息
                    ShowCurrentMenuInfo(data.CompID);
                    ShowHistoryMenuInfo(data.CompID);

                    $('#ShowTow').css("display", "block")
                    $('#ShowTow-fade').css("display", "block")
                }


            });


            //  查询表头 --
            $('#QCControlPlanBut_open').click(function () {

                var sCPNo = $("#txtSQPNo").val();  //
                var sCPVer = $("#txtSQPVer").val();  //
                var sSpec = $("#txtSQPSpec").val();


                var Data = '';
                var Params = { No: sCPNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: sCPVer, B: sSpec, C: "", D: "", E: "" };
                var Data = JSON.stringify(Params);

                table.reload('QCControlPlanHeadlistID', {
                    method: 'post',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=19&Data=' + Data,
                    where: {
                        'No': sCPNo,
                        'A': sCPVer,
                        'B': sSpec
                    }, page: {
                        curr: 1
                    }
                });
            });



        });

        function openDialog(n) {
            if (n == 1) { // 新增表头弹窗层显示
                $('#head-title1').html("新增质量控制计划信息");
                $('#txtAEFlag').val("30");


                $("#txtQPNo").val("");
                $("#txtQPVer").val("1.0");
                $("#txtQPRemark").val("");
                $("#txtQPSpec").val("");

                $("#txtQPNo").attr({ "disabled": "disabled" });
                $("#txtQPVer").removeAttr("disabled");

                $("#div_warning").html("");
                $("#div_warning").hide();

                $('#ShowOne').css("display", "block")
                $('#ShowOne-fade').css("display", "block")
            }
            else if (n == 2)//新增明细弹窗层显示
            {
                $('#head-title3').html("新增质量控制计划明细");
                $('#txtAEFlag').val("30-1");

                //$("#txtQPDNoADD").val(data.CPNo); // 序号
                //$("#txtQPDVerADD").val(data.CPVer);//描述
                //$("#txtDRemarkADD").val(data.Remark);//字段类型

                $("#txtQPDNoADD").val($("#txtQPDNo").val()); // 质量控制计划编号
                $("#txtQPDVerADD").val($("#txtQPDVer").val());//质量控制计划版本
                $("#txtQPDSpecADD").val($("#txtQPDSpec").val());//质量控制计划版本
                $("#txtDRemarkADD").val($("#txtDRemark").val());//备注

                $("#txtItemNo").val(""); // 内容编号
                $("#txtTechNode").val(""); // 工艺节点
                $("#txtTechStep").val(""); // 工艺步骤
                $("#txtTSource").val(""); // 来源
                $("#txtPFMEA").val(""); // PFMEA关联

                $("#txtDevice").val(""); // 工装及夹具
                $("#txtIDNO").val(""); // ID/NO
                $("#txtProduct").val(""); // 产品
                $("#txtProcess").val(""); // 过程
                $("#txtProcessTX").val(""); // 过程特性/公差

                $("#txtMeasurement").val(""); // 测量技术
                $("#txtSampleNum").val(""); // 样品数量
                $("#txtSampleFreq").val(""); // 样品频度
                $("#txtControlWay").val(""); // 控制方法
                $("#txtResponsePlan").val(""); // 反应计划

                //$("#txtThisValue").val(""); // 下限
                //$("#txtIncludeOne").val(""); // 是否包含下限
                $("#txtToValue").val(""); // 上限
                $("#txtIncludeTwo").val(""); // 是否包含上限
                $("#txtRangeKind").val(""); // 类别

                $("#txtDataType").val(""); // 数据类型
                $("#txtStatus").val(""); // 状态
                $("#txtDRemarkAdd").val(""); // 备注


                $("#txtIncludeOne").removeProp("checked");
                $("#txtIncludeTwo").removeProp("checked");


                $("#div_warningDetailAdd").html("");
                $("#div_warningDetailAdd").hide();



                $('#ShowThree').css("display", "block")
            }
        }

        function closeDialog(s) {
            if (s == 1) {
                $('#ShowThree').css("display", "none")
            }
            else {
                $('#ShowOne').css("display", "none")
                $('#ShowOne-fade').css("display", "none")

                $('#ShowTow').css("display", "none")
                $('#ShowTow-fade').css("display", "none")
            }
        }


        /// 显示质量控制计划明细项信息
        ///daiwanshan
        function ShowQCControlPlanDetail(sQPNo, sVer) {

            var sCPNo = $("#txtQPDNo").val();  //
            var sCPVer = encodeURI($("#txtQPDVer").val());  //


            var Data = '';
            var Params = { No: sQPNo, CPVer: sVer, Item: "", Status: "", BDate: "", EDate: "", A: sVer, B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#QCControlPlanDetailList',
                    id: 'QCControlPlanDetailID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=19-1&Data=' + Data,
                    height: '300',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers' },
                        { field: 'ItemNo', title: '内容编号', width: 100 },
                        { field: 'TechNode', title: '工艺节点', width: 100 },
                        { field: 'TechStep', title: '工艺步骤', width: 100 },
                        { field: 'TSource', title: '来源', width: 100 },
                        { field: 'PFMEA', title: 'PFMEA关联', width: 100 },
                        { field: 'Device', title: '工装及夹具', width: 100 },
                        { field: 'IDNO', title: 'ID/NO', width: 100 },
                        { field: 'Product', title: '产品', width: 100 },

                        { field: 'Process', title: '过程', width: 100 },
                        { field: 'ProcessTX', title: '过程特性/公差', width: 100 },
                        { field: 'Measurement', title: '测量技术', width: 100 },
                        { field: 'SampleNum', title: '样品数量', width: 100 },
                        { field: 'SampleFreq', title: '样品频度', width: 100 },
                        { field: 'ControlWay', title: '控制方法', width: 100 },
                        { field: 'ResponsePlan', title: '反应计划', width: 100 },
                        { field: 'ThisValue', title: '下限', width: 100 },


                        { field: 'IncludeOne', title: '是否包含下限', width: 100 },
                        { field: 'ToValue', title: '上限', width: 100 },
                        { field: 'IncludeTwo', title: '是否包含上限', width: 100 },
                        { field: 'RangeKind', title: '类别', width: 100 },
                        { field: 'DataType', title: '数据类型', width: 100 },
                        { field: 'Status', title: '状态', width: 100 },
                        { field: 'Remark', title: '备注', width: 100 },

                        { field: 'SNo', title: '序号', width: 40 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate', title: '录入时间', width: 150 },
                        { field: 'op', title: '操作', width: 230, toolbar: '#barDemo_Detail', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(QCControlPlanDetailList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值


                    if (layEvent == 'detailEdit') {
                        $('#txtAEFlag').val("31-1");
                        $("#head-title3").html("修改质量控制计划明细");

                        $("#txtQPDNoADD").val($("#txtQPDNo").val()); // 质量控制计划编号
                        $("#txtQPDVerADD").val($("#txtQPDVer").val());//质量控制计划版本
                        $("#txtQPDSpecADD").val($("#txtQPDSpec").val());//质量控制计划版本
                        $("#txtDRemarkADD").val($("#txtDRemark").val());//备注

                        $("#txtItemNo").val(data.ItemNo); // 内容编号
                        $("#txtTechNode").val(data.TechNode); // 工艺节点
                        $("#txtTechStep").val(data.TechStep); // 工艺步骤
                        $("#txtTSource").val(data.TSource); // 来源
                        $("#txtPFMEA").val(data.PFMEA); // PFMEA关联

                        $("#txtDevice").val(data.Device); // 工装及夹具
                        $("#txtIDNO").val(data.IDNO); // ID/NO
                        $("#txtProduct").val(data.Product); // 产品
                        $("#txtProcess").val(data.Process); // 过程
                        $("#txtProcessTX").val(data.ProcessTX); // 过程特性/公差

                        $("#txtMeasurement").val(data.Measurement); // 测量技术
                        $("#txtSampleNum").val(data.SampleNum); // 样品数量
                        $("#txtSampleFreq").val(data.SampleFreq); // 样品频度
                        $("#txtControlWay").val(data.ControlWay); // 控制方法
                        $("#txtResponsePlan").val(data.ResponsePlan); // 反应计划

                        $("#txtThisValue").val(data.ThisValue); // 下限
                        //$("#txtIncludeOne").val(data.IncludeOne); // 是否包含下限
                        $("#txtToValue").val(data.ToValue); // 上限
                        //$("#txtIncludeTwo").val(data.IncludeTwo); // 是否包含上限
                        $("#txtRangeKind").val(data.RangeKind); // 类别

                        $("#txtDataType").val(data.DataType); // 数据类型
                        $("#txtStatus").val(data.Status); // 状态
                        $("#txtDRemarkAdd").val(data.Remark); // 备注


                        $("#txtIncludeOne").removeProp("checked");
                        $("#txtIncludeTwo").removeProp("checked");

                        if (data.IncludeOne == "是") {
                            $("#txtIncludeOne").prop('checked', true);
                        }
                        if (data.IncludeTwo == "是") {
                            $("#txtIncludeTwo").prop('checked', true);
                        }


                        $("#div_warningDetailAdd").html("");

                        //$("#txtWOFileNo").attr({ "disabled": "disabled" });
                        //$("#txtWOFileVer").attr({ "disabled": "disabled" });


                        $('#ShowThree').css("display", "block")

                    }
                    else if (layEvent == 'detailDel') {

                        $("#ShowDel").css("display", "block")
                        $("#ShowDel-fade").css("display", "block")

                        $("#XC-Icon").removeClass()
                        $("#hint-value").removeClass()
                        $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                        $("#hint-value").addClass("XC-Font-Red")

                        //设置删除的标题
                        $("#hint-title").html("您确定要删除该质量控制计划明细项吗？内容编号：")

                        //设置删除的对象
                        $("#hint-value").html(data.ItemNo)

                        $("#txtDelQPNo").val($("#txtQPDNo").val())
                        $("#txtDelVer").val($("#txtQPDVer").val())
                        $("#txtDelItemNo").val(data.ItemNo)
                        $("#txtDelOP").val("planItem")

                    }
                    else if (layEvent == 'detailUpm')// 上↑移
                    {
                        var sNo = $("#txtQPDNo").val();
                        var sPVer = $("#txtQPDVer").val();
                        var sItemNo = data.ItemNo;
                        var sSNO = data.SNo;

                        var sFlag = "31-2";
                        var Data = '';
                        var Params = { No: sNo, Name: sPVer, Item: sItemNo, SNO: sSNO, A: "", B: "", C: "", D: "", E: "", F: "up", G: "", H: "", I: "", J: "", K: "", L: "", Remark: "", Flag: sFlag };
                        var Data = JSON.stringify(Params);
                        $.ajax({
                            type: "POST",
                            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
                            data: { Data: Data },
                            success: function (data) {
                                var parsedJson = jQuery.parseJSON(data);

                                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                    layer.msg('移动成功！');
                                    ShowQCControlPlanDetail(sNo, sPVer);  // 重新查询


                                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                                    layer.msg('该质量控制计划项已禁用，不能操作！');
                                }
                                else {
                                    layer.msg('移动失败，请重试！')
                                }
                            },
                            error: function (data) {
                                layer.msg('移动失败2，请重试！')
                            }
                        });

                    }
                    else if (layEvent == 'detailDown')// 下↓移
                    {
                        var sNo = $("#txtQPDNo").val();
                        var sPVer = $("#txtQPDVer").val();
                        var sItemNo = data.ItemNo;
                        var sSNO = data.SNo;

                        var sFlag = "31-2";
                        var Data = '';
                        var Params = { No: sNo, Name: sPVer, Item: sItemNo, SNO: sSNO, A: "", B: "", C: "", D: "", E: "", F: "down", G: "", H: "", I: "", J: "", K: "", L: sSNO, Remark: "", Flag: sFlag };
                        var Data = JSON.stringify(Params);
                        $.ajax({
                            type: "POST",
                            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
                            data: { Data: Data },
                            success: function (data) {
                                var parsedJson = jQuery.parseJSON(data);

                                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                    layer.msg('移动成功！');

                                    ShowQCControlPlanDetail(sNo, sPVer);  // 重新查询

                                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                                    layer.msg('该质量控制计划项已禁用，不能操作！');
                                }
                                else {
                                    layer.msg('移动失败，请重试！')
                                }
                            },
                            error: function (data) {
                                layer.msg('移动失败2，请重试！')
                            }
                        });

                    }



                });



            });  // layui.use('table', function () {


        }


        function btn_QCControlPlanDetailDel() {
            var delOP = $("#txtDelOP").val()

            if (delOP == "plan") {
                //向服务端发送禁用指令
                var sCPNo = $("#txtDelQPNo").val();
                var sCPVer = $("#txtDelVer").val();
                var sFlag = "32";

                var Data = '';
                var Params = {
                    No: sCPNo, Name: sCPVer, Item: "", A: "", B: "", C: "", D: "", E: "", F: "", I: "", J: "", K: "", L: "", Kind: "", Remark: "", Flag: sFlag
                };
                var Data = JSON.stringify(Params);
                $.ajax({
                    type: "POST",
                    url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('删除成功！');

                            closeDelDialog()

                            $('#QCControlPlanBut_open').click();  // 重新查询


                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                            layer.msg('该质量控制计划已被质量控制执行任务引用，不能删除！');

                        }
                        else {
                            layer.msg('删除失败，请重试！');
                            $('#QCControlPlanBut_open').click();  // 重新查询
                        }
                    },
                    error: function (data) {
                        layer.msg('删除失败2，请重试！');
                        $('#QCControlPlanBut_open').click();  // 重新查询
                    }
                });

            } else if (delOP=="planItem") {
                //向服务端发送删除指令 A: sPVer, B: sPath, C: sFile, D: sFNo, E: sFVer, F: sFName,

                //var sWONo = data.CPNo;
                //var sNo = data.CPNo;
                //var sPVer = data.CPVer;
                var sNo = $("#txtDelQPNo").val();
                var sPVer = $("#txtDelVer").val();
                var sItemNo = $("#txtDelItemNo").val();
                //var sFVer = data.FileVer;
                var sFlag = "32-1";

                var Data = '';
                var Params = { No: sNo, Name: sPVer, Item: sItemNo, SNO: "", A: "", B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);

                $.ajax({
                    type: "POST",
                    url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('删除成功！');
                            //$('#Fin_SearchOpen').click();
                            //obj.del(); //删除对应行（tr）的DOM结构，并更新缓存
                            //layer.close(index);
                            ShowQCControlPlanDetail(sNo, sPVer);  // 重新查询

                            closeDelDialog()

                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                            layer.msg('该文件已使用，不能操作！');
                            // $('#Fin_SearchOpen').click();
                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                            layer.msg('该工序版本已禁用，不能操作！');
                            // $('#Fin_SearchOpen').click();
                        }
                        else {
                            layer.msg('删除失败，请重试！');
                            // $('#Fin_SearchOpen').click();
                        }
                    },
                    error: function (data) {
                        layer.msg('删除失败2，请重试！');
                        // $('#Fin_SearchOpen').click();
                    }
                });
            }
        }

        function closeDelDialog() {
            $('#ShowDel').css("display", "none")
            $('#ShowDel-fade').css("display", "none")
        }

            /// 显示客户当前套餐信息
            function ShowCurrentMenuInfo(compID) {
                var Data = '';
                var Params = { CustNo: compID, NowVer: 1 };
                var Data = JSON.stringify(Params);

                // 监听当前套餐表格的工具条事件
                layui.use('table', function () {
                    var table = layui.table;
                    table.on('tool(CurrentMenuList)', function (obj) {
                        var data = obj.data;
                        if (obj.event === 'settlement') {
                            // 处理结算按钮点击事件
                            layer.msg('正在处理套餐结算，申请编码：' + data.ApplyNo);
                            
                            // 这里可以添加打开结算页面或执行结算操作的代码
                            $('#head-title1').html("套餐结算");
                            $("#txtApplyNo").val(data.ApplyNo);
                            $("#txtCustName").val(data.CustName);
                            $("#txtMenuName").val(data.MenuName);
                            $("#txtBasePrice").val(data.BasePrice);
                            $("#txtSettlementCycle").val(data.SettlementCycle);
                            $("#txtSettlementStartTime").val(data.SettlementStartTime ? data.SettlementStartTime.substring(0, 10) : '');
                            $("#txtSettlementEndTime").val(data.SettlementEndTime ? data.SettlementEndTime.substring(0, 10) : '');
                            
                            // 显示结算弹窗
                            $("#settlementForm").show();
                            $("#ShowOne").css("display", "block");
                            $("#ShowOne-fade").css("display", "block");
                        }
                    });
                });

                //获取套餐信息并同时更新详情和列表
                $.ajax({
                    type: "POST",
                    url: '../Service/CompanyCombo.ashx?OP=GetCustMenuList',
                    data: { Data: Data },
                    success: function(result) {
                        var parsedJson = jQuery.parseJSON(result);
                        if (parsedJson.code == 0 && parsedJson.data && parsedJson.data.length > 0) {
                            var currentMenu = parsedJson.data[0];

                            // 设置当前套餐详情
                            $("#viewApplyNo").text(currentMenu.ApplyNo || '');
                            $("#viewCustName").text(currentMenu.CustName || '');
                            $("#viewAccountType").text(currentMenu.AccountType || '');
                            $("#viewSettlementCycle").text(currentMenu.SettlementCycle || '');
                            $("#viewEffectiveDate").text(currentMenu.EffectiveDate ? currentMenu.EffectiveDate.substring(0, 10) : '');
                            $("#viewExpiringDate").text(currentMenu.ExpiringDate ? currentMenu.ExpiringDate.substring(0, 10) : '');
                            $("#viewMenuName").text(currentMenu.MenuName || '');
                            $("#viewBasePrice").text(currentMenu.BasePrice || '0');
                            $("#viewBUP").text(currentMenu.BUP || '0');
                            $("#viewTVA").text(currentMenu.TVA || '0');
                            $("#viewAUserPrice").text(currentMenu.AUserPrice || '0');
                            
                            // 格式化工单超量价格
                            try {
                                var priceData = JSON.parse(currentMenu.AWOPrice);
                                var formattedPrice = priceData.map(function(item, index) {
                                    if (index === priceData.length - 1) {
                                        return item.start + '以上: ' + item.price + '元/工单';
                                    } else {
                                        return item.start + '-' + item.end + ': ' + item.price + '元/工单';
                                    }
                                }).join('，');
                                $("#viewAWOPrice").text(formattedPrice);
                            } catch (e) {
                                $("#viewAWOPrice").text(currentMenu.AWOPrice || '');
                            }
                            
                            $("#viewDFunction").text(currentMenu.DFunction || '');
                            $("#viewRemark").text(currentMenu.Remark || '');

                            // 设置服务选项标签
                            var serviceOptions = {
                                'viewSLA': { value: currentMenu.SLA, text: '标准在线支持' },
                                'viewDepthTrain': { value: currentMenu.DepthTrain, text: '深度培训' },
                                'viewIMServices': { value: currentMenu.IMServices, text: '实施服务' },
                                'viewCustomDev': { value: currentMenu.CustomDev, text: '高级定制开发' },
                                'viewInterfaceDev': { value: currentMenu.InterfaceDev, text: '特定接口开发' },
                                'viewOnsiteSV': { value: currentMenu.OnsiteSV, text: '专人驻场服务' }
                            };

                            Object.keys(serviceOptions).forEach(function(key) {
                                var option = serviceOptions[key];
                                if (option.value === true || option.value === "是") {
                                    $("#" + key).text(option.text).addClass('active-service');
                                } else {
                                    $("#" + key).text('').removeClass('active-service');
                                }
                            });

                            // 显示当前套餐详情区域
                            $("#currentMenuDetail").show();

                            // 使用相同的数据更新表格
                            layui.use('table', function () {
                                var table = layui.table;
                                // 直接使用API返回的数据渲染表格
                                table.render({
                                    elem: '#CurrentMenuList',
                                    id: 'CurrentMenuListID',
                                    data: parsedJson.data, // 直接使用返回的数据
                                    height: 'auto',
                                    cellMinWidth: 80,
                                    count: parsedJson.data.length, // 使用实际数据长度
                                    limit: 20,
                                    limits: [10, 20, 30, 40, 50],
                                    cols: [[
                                        { type: 'numbers' },
                                        { field: 'ApplyNo', title: '申请编码', width: 120, sort: true },
                                        { field: 'CustNo', title: '客户编码', width: 120 },
                                        { field: 'CustName', title: '客户名称', width: 180 },
                                        { field: 'AccountType', title: '结算方式', width: 100 },
                                        { field: 'MenuName', title: '套餐名称', width: 120 },
                                        { field: 'SettlementStartTime', title: '结算开始时间', width: 120 },
                                        { field: 'SettlementEndTime', title: '结算结束时间', width: 120 },
                                        { field: 'RegType', title: '注册类型', width: 100 },
                                        { field: 'Code', title: '注册码', width: 120 },
                                        { field: 'EffectiveDate', title: '生效日期', width: 120 },
                                        { field: 'ExpiringDate', title: '失效日期', width: 120 },
                                        { field: 'SettlementCycle', title: '结算周期(天)', width: 100 },
                                        { field: 'BasePrice', title: '基础价格(月费)', width: 120 },
                                        { field: 'BUP', title: '包含活跃用户数', width: 120 },
                                        { field: 'TVA', title: '包含工单数', width: 120 },
                                        { field: 'AUserPrice', title: '额外用户单价', width: 120 },
                                        { field: 'AWOPrice', title: '工单超量价格', width: 120 },
                                        { field: 'DFunction', title: '核心功能差异', width: 200 },
                                        { field: 'SLA', title: '标准在线支持', width: 120, templet: function(d) { return d.SLA ? '是' : '否'; } },
                                        { field: 'DepthTrain', title: '深度培训', width: 120, templet: function(d) { return d.DepthTrain ? '是' : '否'; } },
                                        { field: 'IMServices', title: '实施服务', width: 120, templet: function(d) { return d.IMServices ? '是' : '否'; } },
                                        { field: 'CustomDev', title: '高级定制开发', width: 120, templet: function(d) { return d.CustomDev ? '是' : '否'; } },
                                        { field: 'InterfaceDev', title: '特定接口开发', width: 120, templet: function(d) { return d.InterfaceDev ? '是' : '否'; } },
                                        { field: 'OnsiteSV', title: '专人驻场服务', width: 120, templet: function(d) { return d.OnsiteSV ? '是' : '否'; } },
                                        { field: 'NowVer', title: '在用版本', width: 80, templet: function(d) { return d.NowVer ? '是' : '否'; } },
                                        { field: 'Status', title: '状态', width: 100 },
                                        { field: 'CHMan', title: '变更人', width: 100 },
                                        { field: 'CHDate', title: '变更时间', width: 150 },
                                        { field: 'InMan', title: '登录账号', width: 100 },
                                        { field: 'InDate', title: '创建时间', width: 150 },
                                        { field: 'Remark', title: '备注', width: 200 },
                                        { title: '操作', width: 100, templet: '#currentMenuBarDemo', fixed: 'right' }
                                    ]],
                                    page: true
                                });
                            });
                        }
                    },
                    error: function() {
                        layer.msg('获取当前套餐信息失败，请重试！');
                    }
                });
            }

            /// 显示客户历史套餐信息
            function ShowHistoryMenuInfo(compID) {
                var Data = '';
                var Params = { CustNo: compID, NowVer: 0 };
                var Data = JSON.stringify(Params);

                // 监听历史套餐表格的工具条事件
                layui.use('table', function () {
                    var table = layui.table;
                    table.on('tool(HistoryMenuList)', function (obj) {
                        var data = obj.data;
                        if (obj.event === 'settlement') {
                            // 处理结算按钮点击事件
                            layer.msg('正在处理历史套餐结算，申请编码：' + data.ApplyNo);
                            
                            // 这里可以添加打开结算页面或执行结算操作的代码
                            $('#head-title1').html("历史套餐结算");
                            $("#txtApplyNo").val(data.ApplyNo);
                            $("#txtCustName").val(data.CustName);
                            $("#txtMenuName").val(data.MenuName);
                            $("#txtBasePrice").val(data.BasePrice);
                            $("#txtSettlementCycle").val(data.SettlementCycle);
                            $("#txtSettlementStartTime").val(data.SettlementStartTime ? data.SettlementStartTime.substring(0, 10) : '');
                            $("#txtSettlementEndTime").val(data.SettlementEndTime ? data.SettlementEndTime.substring(0, 10) : '');
                            
                            // 显示结算弹窗
                            $("#settlementForm").show();
                            $("#ShowOne").css("display", "block");
                            $("#ShowOne-fade").css("display", "block");
                        }
                    });
                });

                //历史套餐信息
                layui.use('table', function () {
                    var table = layui.table,
                        form = layui.form;
                                table.render({
                        elem: '#HistoryMenuList',
                        id: 'HistoryMenuListID',
                        url: '../Service/CompanyCombo.ashx?OP=GetCustMenuList&Data=' + Data,
                        height: 'auto',
                        cellMinWidth: 80,
                        count: 50,
                        limit: 20,
                        limits: [10, 20, 30, 40, 50],
                                    cols: [[
                                        { type: 'numbers' },
                                        { field: 'ApplyNo', title: '申请编码', width: 120, sort: true },
                                        { field: 'CustNo', title: '客户编码', width: 120 },
                                        { field: 'CustName', title: '客户名称', width: 180 },
                                        { field: 'AccountType', title: '结算方式', width: 100 },
                                        { field: 'MenuName', title: '套餐名称', width: 120 },
                                        { field: 'SettlementStartTime', title: '结算开始时间', width: 120 },
                                        { field: 'SettlementEndTime', title: '结算结束时间', width: 120 },
                                        { field: 'RegType', title: '注册类型', width: 100 },
                                        { field: 'Code', title: '注册码', width: 120 },
                                        { field: 'EffectiveDate', title: '生效日期', width: 120 },
                                        { field: 'ExpiringDate', title: '失效日期', width: 120 },
                                        { field: 'SettlementCycle', title: '结算周期(天)', width: 100 },
                                        { field: 'BasePrice', title: '基础价格(月费)', width: 120 },
                                        { field: 'BUP', title: '包含活跃用户数', width: 120 },
                                        { field: 'TVA', title: '包含工单数', width: 120 },
                                        { field: 'AUserPrice', title: '额外用户单价', width: 120 },
                                        { field: 'AWOPrice', title: '工单超量价格', width: 120 },
                                        { field: 'DFunction', title: '核心功能差异', width: 200 },
                                        { field: 'SLA', title: '标准在线支持', width: 120, templet: function(d) { return d.SLA ? '是' : '否'; } },
                                        { field: 'DepthTrain', title: '深度培训', width: 120, templet: function(d) { return d.DepthTrain ? '是' : '否'; } },
                                        { field: 'IMServices', title: '实施服务', width: 120, templet: function(d) { return d.IMServices ? '是' : '否'; } },
                                        { field: 'CustomDev', title: '高级定制开发', width: 120, templet: function(d) { return d.CustomDev ? '是' : '否'; } },
                                        { field: 'InterfaceDev', title: '特定接口开发', width: 120, templet: function(d) { return d.InterfaceDev ? '是' : '否'; } },
                                        { field: 'OnsiteSV', title: '专人驻场服务', width: 120, templet: function(d) { return d.OnsiteSV ? '是' : '否'; } },
                                        { field: 'NowVer', title: '在用版本', width: 80, templet: function(d) { return d.NowVer ? '是' : '否'; } },
                                        { field: 'Status', title: '状态', width: 100 },
                                        { field: 'CHMan', title: '变更人', width: 100 },
                                        { field: 'CHDate', title: '变更时间', width: 150 },
                                        { field: 'InMan', title: '登录账号', width: 100 },
                                        { field: 'InDate', title: '创建时间', width: 150 },
                                        { field: 'Remark', title: '备注', width: 200 },
                            { title: '操作', width: 100, templet: '#historyMenuBarDemo', fixed: 'right' }
                        ]],
                        page: true
                    });
                });
            }
    </script>


    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

        })



    </script>

    <style type="text/css">
        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }

        #ShowOne .XC-Form-block-Item span, #ShowTow .XC-Form-block-Item span {
            width: 96px;
        }

        #ShowThree .XC-Form-block-Item span {
            width: 96px;
        }

        /* 服务标签样式 */
        .service-tag {
            display: inline-block;
            margin: 2px 8px 2px 0;
            padding: 4px 12px;
            border-radius: 3px;
            font-size: 12px;
            color: #999;
            background-color: #f5f5f5;
            transition: all 0.3s ease;
        }

        .service-tag.active-service {
            color: #fff;
            background-color: #009688;
        }

        /* 当前套餐详情样式 */
        #currentMenuDetail {
            background-color: #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        #currentMenuDetail table td {
            padding: 12px;
            line-height: 1.6;
            color: #666;
        }

        #currentMenuDetail table td[style*="text-align:right"] {
            color: #333;
            font-weight: 500;
        }

        #currentMenuDetail table td span:not(.service-tag) {
            color: #333;
        }

        #currentMenuDetail h4 {
            color: #333;
            font-size: 16px;
            font-weight: 500;
        }

        /* 表格间隔背景色 */
        #currentMenuDetail table tr:nth-child(even) {
            background-color: #fafafa;
        }
    </style>

</head>
<body>
    <div class="div_find">

        <p>
            <label class="find_labela">质量控制计划编号</label> <input type="text" id="txtSQPNo" class="find_input" />
            <label class="find_labela">版本</label><input type="text" id="txtSQPVer" class="find_input" />
            <label class="find_labela">型号</label><input type="text" id="txtSQPSpec" class="find_input" />
            <!--<label class="find_labela">描述：</label><input type="text" id="txtSCPTxt" class="find_input" />-->
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="QCControlPlanBut_open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="openDialog(1)">添加</button>
        </p>
    </div>

    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="Companylist" lay-filter="Companylist"></table>

        <script type="text/html" id="barDemo">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detail">详情</button>
        </script>

        <!-- 当前套餐列表操作列模板 -->
        <script type="text/html" id="currentMenuBarDemo">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="settlement">结算</button>
        </script>

        <!-- 历史套餐列表操作列模板 -->
        <script type="text/html" id="historyMenuBarDemo">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="settlement">结算</button>
        </script>

    </div>

    <!--质量控制计划表头新增和修改增弹层-->
    <div class="XC-modal XC-modal-md" id="ShowOne" style="height: 350px;">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1">修改质量控制计划</span>
            <span class="head-close" onclick="closeDialog(2)">X</span>
        </div>
        <div class="XC-modal-body">
            <form class="XC-Form-block">
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">质量控制计划编号</span>
                    <input type="text" class="XC-Input-block" id="txtQPNo" name="txtQPNo" readonly=readonly placeholder="系统自动产生" value="" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">版本<span class="XC-Font-Red">*</span></span>
                    <input type="text" class="XC-Input-block" id="txtQPVer" name="txtQPVer" value="" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">型号<span class="XC-Font-Red">*</span></span>
                    <input type="text" class="XC-Input-block" id="txtQPSpec" name="txtQPSpec" value="" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Textarea-block">备注</span>
                    <textarea type="text" class="XC-Textarea-block" id="txtQPRemark" name="txtQPRemark" value=""></textarea>
                </div>
                <div class="XC-Form-block-Item" style="display:none">
                    <span class="XC-Span-Input-block"></span>
                    <input type="text" class="XC-Input-block" id="txtQPVerOld" name="txtQPVerOld" value="" />
                </div>
            </form>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="QCControlPlanHeadSave_Btn">提交</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="QCControlPlanHeadSaveClose" onclick='closeDialog(2)'>关闭</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>



    <!--客户套餐信息详情弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowTow">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title2">客户套餐信息详情</span>
            <span class="head-close" onclick="closeDialog(2)">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <!-- 当前套餐详情展示 -->
                <div id="currentMenuDetail" style="border: 1px solid #ddd; padding: 15px; margin-bottom: 20px; border-radius: 4px;">
                        <h4 style="margin-top: 0; margin-bottom: 15px; padding-bottom: 8px; border-bottom: 1px solid #eee;">当前套餐详情</h4>
                        <table cellspacing="20" cellpadding="20" border='0' style="width:100%;">
                            <tr>
                                <td style="width:120px; text-align:right;">申请编码：</td>
                                <td style="width:25%"><span id="viewApplyNo"></span></td>
                                <td style="width:120px; text-align:right;">客户名称：</td>
                                <td style="width:25%"><span id="viewCustName"></span></td>
                            </tr>
                            <tr>
                                <td style="text-align:right;">结算方式：</td>
                                <td><span id="viewAccountType"></span></td>
                                <td style="text-align:right;">结算周期：</td>
                                <td><span id="viewSettlementCycle"></span>天</td>
                            </tr>
                            <tr>
                                <td style="text-align:right;">生效日期：</td>
                                <td><span id="viewEffectiveDate"></span></td>
                                <td style="text-align:right;">失效日期：</td>
                                <td><span id="viewExpiringDate"></span></td>
                            </tr>
                            <tr>
                                <td style="text-align:right;">套餐名称：</td>
                                <td><span id="viewMenuName"></span></td>
                                <td style="text-align:right;">基础价格：</td>
                                <td><span id="viewBasePrice"></span>元/月</td>
                            </tr>
                            <tr>
                                <td style="text-align:right;">包含用户数：</td>
                                <td><span id="viewBUP"></span></td>
                                <td style="text-align:right;">包含工单数：</td>
                                <td><span id="viewTVA"></span></td>
                            </tr>
                            <tr>
                                <td style="text-align:right;">额外用户单价：</td>
                                <td><span id="viewAUserPrice"></span>元/人</td>
                                <td style="text-align:right;">工单超量价格：</td>
                                <td><span id="viewAWOPrice"></span></td>
                            </tr>
                            <tr>
                                <td style="text-align:right;">核心功能差异：</td>
                                <td colspan="3"><span id="viewDFunction"></span></td>
                            </tr>
                            <tr>
                                <td style="text-align:right;">服务选项：</td>
                                <td colspan="3">
                                    <span id="viewSLA" class="service-tag"></span>
                                    <span id="viewDepthTrain" class="service-tag"></span>
                                    <span id="viewIMServices" class="service-tag"></span>
                                    <span id="viewCustomDev" class="service-tag"></span>
                                    <span id="viewInterfaceDev" class="service-tag"></span>
                                    <span id="viewOnsiteSV" class="service-tag"></span>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align:right;">备注：</td>
                                <td colspan="3"><span id="viewRemark"></span></td>
                            </tr>
                        </table>
                    </div>

                <div style="border-bottom: solid 1px #ccc;padding-bottom:12px;margin-bottom:12px;">
                    <span style="font-weight: bold; ">当前套餐信息</span>
                    </div>

                <div class="wangid_conbox">
                    <table class="layui-hide" id="CurrentMenuList" lay-filter="CurrentMenuList"></table>
                    </div>

                <div style="border-bottom: solid 1px #ccc;padding-bottom:12px;margin-bottom:12px;margin-top:20px;">
                    <span style="font-weight: bold; ">历史套餐信息</span>
                </div>

                <div class="wangid_conbox">
                    <table class="layui-hide" id="HistoryMenuList" lay-filter="HistoryMenuList"></table>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowTow-fade" class="black_overlay">
    </div>


    <!--质量控制计划明细弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowThree">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title3">质量控制计划新增</span>
            <span class="head-close" onclick="closeDialog(1)">X</span>
        </div>

        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <div style="display:flex">
                    <div style="width:100%">
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">质量控制计划编号</span>
                            <input type="text" class="XC-Input-block" id="txtQPDNoADD" name="txtQPDNoADD" readonly=readonly placeholder="系统自动产生" value="" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">型号</span>
                            <input type="text" class="XC-Input-block" id="txtQPDSpecADD" name="txtQPDSpecADD" readonly="readonly" value="" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">工艺节点</span>
                            <input type="text" class="XC-Input-block" id="txtTechNode" name="txtTechNode" value="" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">来源</span>
                            <input type="text" class="XC-Input-block" id="txtTSource" name="txtTSource" value="" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">工装及夹具</span>
                            <input type="text" class="XC-Input-block" id="txtDevice" name="txtDevice" value="" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">产品</span>
                            <input type="text" class="XC-Input-block" id="txtProduct" name="txtProduct" value="" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">过程特性/公差</span>
                            <input type="text" class="XC-Input-block" id="txtProcessTX" name="txtProcessTX" value="" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">样品数量</span>
                            <input type="text" class="XC-Input-block" id="txtSampleNum" name="txtSampleNum" value="" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">控制方法</span>
                            <input type="text" class="XC-Input-block" id="txtControlWay" name="txtControlWay" value="" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">下限</span>
                            <input type="text" class="XC-Input-block" id="txtThisValue" name="txtThisValue" value="" style="width:40%" />
                            <span class="XC-Span-Input-block">是否包含下限</span>
                            <input type="checkbox" id="txtIncludeOne" name="txtIncludeOne" value="" style="margin: 0px;" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Select-block" style="line-height:30px;">数据类型<span class="XC-Font-Red">*</span></span>
                            <select class="XC-Select-block" id="txtDataType">
                                <option></option>
                                <option>布尔型</option>
                                <option>文本型</option>
                                <option>数值型</option>
                                <option>日期型</option>
                            </select>
                        </div>
                        <div class="XC-Form-block-Item" style="display:none">
                            <span class="XC-Span-Select-block">类别</span>
                            <input type="text" class="XC-Input-block" id="txtRangeKind" name="txtRangeKind" style="height: 30px; display: none; " />
                            <!--<select class="XC-Select-block" id="txtRangeKind">
                                <option></option>
                                <option>等于</option>
                                <option>介于</option>
                                <option>小于</option>
                                <option>大于等于</option>
                                <option>小于等于</option>
                            </select>-->
                        </div>
                    </div>
                    <div style="width:100%">
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">版本</span>
                            <input type="text" class="XC-Input-block" id="txtQPDVerADD" name="txtQPDVerADD" readonly=readonly value="" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">内容编号</span>
                            <input type="text" class="XC-Input-block" id="txtItemNo" name="txtItemNo" readonly=readonly placeholder="系统自动产生" value="" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">工艺步骤</span>
                            <input type="text" class="XC-Input-block" id="txtTechStep" name="txtTechStep" value="" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">PFMEA关联</span>
                            <input type="text" class="XC-Input-block" id="txtPFMEA" name="txtPFMEA" value="" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">ID/NO</span>
                            <input type="text" class="XC-Input-block" id="txtIDNO" name="txtIDNO" value="" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">过程</span>
                            <input type="text" class="XC-Input-block" id="txtProcess" name="txtProcess" value="" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">测量技术</span>
                            <input type="text" class="XC-Input-block" id="txtMeasurement" name="txtMeasurement" value="" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">样品频度</span>
                            <input type="text" class="XC-Input-block" id="txtSampleFreq" name="txtSampleFreq" value="" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">反应计划</span>
                            <input type="text" class="XC-Input-block" id="txtResponsePlan" name="txtResponsePlan" value="" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">上限</span>
                            <input type="text" class="XC-Input-block" id="txtToValue" name="txtToValue" value="" style="width:40%" />
                            <span class="XC-Span-Input-block">是否包含上限</span>
                            <input type="checkbox" id="txtIncludeTwo" name="txtIncludeTwo" value="" style="margin:0px;" />
                        </div>
                        <div class="XC-Form-block-Item">
                            <span class="XC-Span-Input-block">内容备注</span>
                            <input type="text" class="XC-Input-block" id="txtDRemarkAdd" name="txtDRemarkAdd" value="" />
                        </div>
                        <div class="XC-Form-block-Item" style="display:none">
                            <span class="XC-Span-Input-block">状态</span>
                            <input type="text" class="XC-Input-block" id="txtStatus" name="txtStatus" value="" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="btn_QCControlPlanDetailSave_Btn">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="btn_QCControlPlanHeadDetailSaveClose" onclick='closeDialog(1)'>关闭</button>
            </div>
        </div>

    </div>


    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel" style="z-index:1003">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDelDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
            </div>
        </div>

        <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelQPNo" name="txtDelQPNo" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelVer" name="txtDelVer" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelItemNo" name="txtDelItemNo" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelOP" name="txtDelOP" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" onclick="btn_QCControlPlanDetailDel()">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDelDialog()">取消</button>
            </div>
        </div>
    </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay" style="z-index:1002">
    </div>

    <!--消息提示-->
    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>

</body>
</html>