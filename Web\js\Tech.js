﻿
//选中的作业单元 
var selectedCheckboxes = []

$(function () {

    function EntityMsg(item, Mesage) {
        $("#div_warning").show();
        item.focus();
        $("#sMessage").html(Mesage);

    }
    // 重新登录
    $('#BtnRelationLogin').click(function () {
        var Num = parseInt(Math.random() * 1000);
        location.href = "../Login.htm?CFlag=30&RA" + Num;

    });









    //  根据客户编码获取简称
    $('#txtBCustNo').blur(function () {
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();
        var sCNo = $('#txtBCustNo').val();
        var Flag = "41-2";
        var time = new Date();
        var sM = time.getMonth() + 1;
        if (sM < 10) {
            sM = "0" + sM
        }
        var sD = time.getDate()
        if (sD < 10) {
            sD = "0" + sD
        }

        var s1 = time.getFullYear() + "-" + sM + "-" + sD;
        $('#txtPPDate').val(s1);

        if (sCNo == "") {
            $("#div_warning").html("请输入客户编码！")
            $("#divsuccess").show();
            return;
        }


        var Data = '';
        var Params = { CNo: sCNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetCustInfoByNo&CFlag=41-2",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson.length > 0) {
                    // var sStr = eval(parsedJson.json)[0];
                    //$('#txtRNo').val(row.ReceiveNo);
                    $('#txtNameEn').val(parsedJson[0].CustEn);
                    $('#txtPPNo').val(parsedJson[0].CustNo);
                    $('#txtPPNo').focus();


                } else {
                    $("#txtNameEn").html("系统不存在该客户信息");
                }
            },
            error: function (data) {
                $("#div_warning2").html("系统出错，请重试2！")
                $("#div_warning2").show();
            }
        });

    });



    //  保存:工序信息
    $('#ProcSave_Btn').click(function () {
        $("#ProcSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();

        var sNo = $("#txtProcNo").val();  //工序
        var sName = $("#txtProcName").val();
        var sRemark = $("#txtRemark").val();
        var sFlag = $("#txtAEFlag").val();  // 1 标识新增， 2 标识修改

        if (sNo == "") {
            ErrorMessage("请输入工序编号！", 2000)
            $("#ProcSave_Btn").removeAttr("disabled");
            return;
        }
        if (sName == "") {
            ErrorMessage("请输入工序名称！", 2000)
            $("#ProcSave_Btn").removeAttr("disabled");
            return;
        }



        var Data = '';
        var Params = { No: sNo, Name: sName, Item: "", MNo: "", MName: "", A: ModuleName, B: "", C: "", D: "", E: "", F: "", Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/TechAjax.ashx?OP=OPTechInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#ProcSave_Btn").removeAttr("disabled");

                    $("#txtSPMNo").val(sNo);  //工序
                    $('#ProcBut_open').click();
                    $("#txtSPMNo").val("");  //工序

                    layer.msg("提交成功！")

                    closeDialog()

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#ProcSave_Btn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#ProcSave_Btn").removeAttr("disabled");
                    ErrorMessage("工序编号或名称在系统已存在，请确认！", 2000)
                } else {
                    $("#ProcSave_Btn").removeAttr("disabled");
                    ErrorMessage(parsedJson.Msg, 2000)
                }
            },
            error: function (data) {
                $("#ProcSave_Btn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });



    //  保存:产品流程信息
    $('#ProductFlowSave_Btn').click(function () {
        $("#ProductFlowSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();


        var sPNo = $("#txtProductNo").val();
        var sPName = $("#txtProductName").val();
        var sModel = $("#txtModel").val();
        var sTech = $("#txtTechNo").val();
        var sTNo = $("#txtProcNo").val();  //工序  (05)总装
        var sPVer = $("#txtProcedureVer").val();  //工序版本
        var sVerDesc = $("#txtVerDesc").val();  //版本描述
        var sNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
        var sName = sTNo.substr(sTNo.indexOf(")") + 1, sTNo.length);
        var sOrder = $("#txtFlowOrder").val();  // 现在新增时，有后台自动产生
        var sUnit = $("#txtCWUnit").val();  // 作业单元
        var sTCNo = $("#txtConditionNo").val();   // 工序行为  (GXXW002)通用包装行为  
        var sCNo = ""; // 工序行为编号
        var sCName = ""; // 工序行为名称
        var sKind = "正常"; // 正常工序
        var sFlag = $("#txtAEFlag").val();  // 4 标识新增，5 标识修改
        var sVerStatus = $("#txtVerStatus").val()
        var sClassFlag = "";
        var sPlanName = $('#txtSamplPlan option:selected').text(); //抽样方案名称
        var sPlanNo = $('#txtSamplPlan').val();                  //抽样方案编号
        var sSamplWay = $('#txtSamplWay').val();


        if (sPNo == "") {
            $("#div_warning").html("请输入产品编号！")
            $("#div_warning").show();
            $("#ProductFlowSave_Btn").removeAttr("disabled");
            return;
        }
        if (sPName == "") {
            $("#div_warning").html("请输入产品名称！")
            $("#div_warning").show();
            $("#ProductFlowSave_Btn").removeAttr("disabled");
            return;
        }

        if (sNo == "") {
            $("#div_warning").html("请输入工序编号！")
            $("#div_warning").show();
            $("#ProductFlowSave_Btn").removeAttr("disabled");
            return;
        }
        if (sName == "") {
            $("#div_warning").html("请输入工序名称！")
            $("#div_warning").show();
            $("#ProductFlowSave_Btn").removeAttr("disabled");
            return;
        }

        if (sUnit == "") {
            $("#div_warning").html("请输入作业单元！")
            $("#div_warning").show();
            $("#ProductFlowSave_Btn").removeAttr("disabled");
            return;
        }
        if (sPVer == "") {
            $("#div_warning").html("请输入工序版本！")
            $("#div_warning").show();
            $("#ProductFlowSave_Btn").removeAttr("disabled");
            return;
        }

        //        if (sOrder == "") {
        //            $("#div_warning").html("请输入顺序编号！")
        //            $("#div_warning").show();
        //            $("#OrderFlowSave_Btn").removeAttr("disabled");
        //            return;
        //        }
        //        if (isNaN(sOrder)) {
        //            $("#div_warning").html("顺序编号需要整数！")
        //            $("#div_warning").show();
        //            $("#OrderFlowSave_Btn").removeAttr("disabled");
        //            return;
        //        }
        if ((sTCNo == "") || (sTCNo == "null") || (sTCNo == null)) {
            $("#div_warning").html("请选择工序行为！")
            $("#div_warning").show();
            $("#ProductFlowSave_Btn").removeAttr("disabled");
            return;
        }
        sCNo = sTCNo.substr(1, sTCNo.indexOf(")") - 1);  // (GXXW002)通用包装行为  
        sCName = sTCNo.substr(sTCNo.indexOf(")") + 1, sTCNo.length);

        if ($("#CH_ClassFlag").is(':checked')) {
            sClassFlag = "是";
        }
        else {
            sClassFlag = "否";
        }


        var Data = '';
        var Params = { No: sNo, Name: sName, Item: sPVer, MNo: sPNo, MName: sPName, A: sOrder, B: sCNo, C: sCName, D: sKind, E: sUnit, F: sVerDesc, G: sModel, H: sTech, I: sClassFlag, J: sVerStatus, K: "", L: ModuleName, M: sSamplWay, N: sPlanNo, O: sPlanName, Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/TechAjax.ashx?OP=OPTechInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#div_warning").html("提交成功！")
                    $("#div_warning").show();
                    $("#ProductFlowSave_Btn").removeAttr("disabled");
                    if (sFlag == "4") {
                        $("#txtFlowOrder").val(parsedJson.SeqNo);
                    }
                    $("#txtOldProcVer").val(sPVer);  // 用户不能修改的，主要用于新增工艺文件使用

                    $("#txtSPNo").val(sPNo);  //产品编号
                    $('#ProductBut_open').click();
                    $("#txtSPNo").val("");  //产品编号

                    ShowProcFileInfo(sPNo, sNo, sPVer); // 显示工艺文件 
                    ShowProcDeviceInfo(sPNo, sNo, sPVer); // 显示设备信息
                    ShowProcQualityInfo(sPNo, sNo, sPVer);// 显示质控品
                    ShowTestItemInfo(sPNo, sNo, sPVer); // 显示测试信息

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#ProductFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#ProductFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("该产品流程已存在该工序，请确认！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTDX') {
                    $("#ProductFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("该产品代码已设置标准流程，请确认！")
                    $("#div_warning").show();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_NOTEST') {
                    $("#ProductFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("工序测试项抽样必须先设置抽样的测试项，请切换工序版本确认所有工序版本都已设置！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_FAFSNULL') {
                    $("#ProductFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("抽样工序行为必须设置抽样方案和检验方式，请确认！")
                    $("#div_warning").show();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_FAFSNOTNULL') {
                    $("#ProductFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("该工序没有设置的是非抽样工序行为，不能设置抽样方案和检验方式，请确认！")
                    $("#div_warning").show();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_ISTEST') {
                    $("#ProductFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("工序抽样的检验方式下无需设置测试项，请确认并取消所有工序版本的测试项！")
                    $("#div_warning").show();
                }
                else {
                    $("#ProductFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html(parsedJson.Msg);
                    $("#div_warning").show();
                }
            },
            error: function (data) {
                $("#ProductFlowSave_Btn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });

    // 新增产品流程工序状态下，禁用工序版本
    $('#Disable_Btn').click(function () {

        var sPNo = $("#txtProductNo").val();
        var sTNo = $("#txtProcNo").val();  //工序  (05)总装
        var sPVer = $("#txtProcedureVer").val();  //工序版本
        var sNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，05
        var sName = sTNo.substr(sTNo.indexOf(")") + 1);  //工序  (05)总装 截取字符串，总装
        var sDesc = $("#txtVerDesc").val(); // 版本说明
        var sFlag = "9-0-1";

        var sStr = "你即将禁用的工序是：" + sTNo + "，版本是：" + sPVer + "请确认!";
        var sCF = confirm(sStr);

        if (sCF) {

            var Data = '';
            var Params = { No: sNo, Name: sName, Item: sPVer, MNo: sPNo, MName: "", A: "", B: "", C: "", D: "", E: ModuleName, F: sDesc, Remark: "", Flag: sFlag };
            var Data = JSON.stringify(Params);

            $.ajax({
                type: "POST",
                url: "../Service/TechAjax.ashx?OP=OPTechInfo&CFlag=" + sFlag,
                data: { Data: Data },
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);

                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                        $("#div_warning").html("禁用成功！")
                        $("#div_warning").show();
                        $("#Disable_Btn").removeAttr("disabled");

                        $("#txtVerStatus").val("禁用");  //工序版本状态
                        $("#txtSPNo").val(sPNo);  //产品编号
                        $('#ProductBut_open').click();
                        $("#txtSPNo").val("");  //产品编号


                    } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                        $("#Disable_Btn").removeAttr("disabled");
                        $("#div_warning").html("您未登陆系统，请先登录！")
                        $("#div_warning").show();
                    } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST8888') {
                        $("#Disable_Btn").removeAttr("disabled");
                        $("#div_warning").html("该工序版本已禁用，无需重复操作，请确认！")
                        $("#div_warning").show();
                    } else {
                        $("#Disable_Btn").removeAttr("disabled");
                        $("#div_warning").html(parsedJson.Msg);
                        $("#div_warning").show();
                    }
                },
                error: function (data) {
                    $("#Disable_Btn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试2！")
                    $("#div_warning").show();
                }
            });
        } // if (sCF) 
    });

    // 新增产品流程工序状态下，启用工序版本
    $('#Enable_Btn').click(function () {

        var sPNo = $("#txtProductNo").val();
        var sTNo = $("#txtProcNo").val();  //工序  (05)总装
        var sPVer = $("#txtProcedureVer").val();  //工序版本
        var sNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
        var sName = sTNo.substr(sTNo.indexOf(")") + 1);  //工序  (05)总装 截取字符串，总装
        var sDesc = $("#txtVerDesc").val(); // 版本说明
        var sFlag = "9-0-2";

        var sStr = "你即将启用的工序是：" + sTNo + "，版本是：" + sPVer + "请确认!";
        var sCF = confirm(sStr);

        if (sCF) {

            var Data = '';
            var Params = { No: sNo, Name: sName, Item: sPVer, MNo: sPNo, MName: "", A: "", B: "", C: "", D: "", E: ModuleName, F: sDesc, Remark: "", Flag: sFlag };
            var Data = JSON.stringify(Params);

            $.ajax({
                type: "POST",
                url: "../Service/TechAjax.ashx?OP=OPTechInfo&CFlag=" + sFlag,
                data: { Data: Data },
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);

                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                        $("#div_warning").html("启用成功！")
                        $("#div_warning").show();
                        $("#Enable_Btn").removeAttr("disabled");

                        $("#txtVerStatus").val("启用");  //工序版本状态
                        $("#txtSPNo").val(sPNo);  //产品编号
                        $('#ProductBut_open').click();
                        $("#txtSPNo").val("");  //产品编号


                    } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                        $("#Enable_Btn").removeAttr("disabled");
                        $("#div_warning").html("您未登陆系统，请先登录！")
                        $("#div_warning").show();
                    } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST8888') {
                        $("#Enable_Btn").removeAttr("disabled");
                        $("#div_warning").html("该工序版本已禁用，无需重复操作，请确认！")
                        $("#div_warning").show();
                    } else {
                        $("#Enable_Btn").removeAttr("disabled");
                        $("#div_warning").html(parsedJson.Msg);
                        $("#div_warning").show();
                    }
                },
                error: function (data) {
                    $("#Disable_Btn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试2！")
                    $("#div_warning").show();
                }
            });
        } // if (sCF) 
    });


    // 新增产品流程工序状态下，把旧工序版本的信息，拷贝到新工序版本 
    $('#Copy_Btn').click(function () {

        var sPNo = $("#txtProductNo").val();
        var sTNo = $("#txtProcNo").val();  //工序  (05)总装
        var sPVer = $("#txtProcedureVer").val();  //工序版本
        var sNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
        var sName = sTNo.substr(sTNo.indexOf(")") + 1);  //工序  (05)总装 截取字符串，总装
        var sDesc = $("#txtVerDesc").val(); // 版本说明
        var sFlag = "9-0-3";

        if ($('#txtFlowOrder').val() == "") {
            layer.msg('请先保存产品工艺流程信息！');
            return;
        }

        var sStr = "你即将把上版本的工序信息，拷贝到该版本上：" + sPVer + "请确认!";
        var sCF = confirm(sStr);

        if (sCF) {

            var Data = '';
            var Params = { No: sNo, Name: sName, Item: sPVer, MNo: sPNo, MName: "", A: "", B: "", C: "", D: "", E: ModuleName, F: sDesc, Remark: "", Flag: sFlag };
            var Data = JSON.stringify(Params);

            $.ajax({
                type: "POST",
                url: "../Service/TechAjax.ashx?OP=OPTechInfo&CFlag=" + sFlag,
                data: { Data: Data },
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);

                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                        $("#div_warning").html("拷贝成功！")
                        $("#div_warning").show();
                        $("#Copy_Btn").removeAttr("disabled");

                        $("#txtVerStatus").val("启用");  //工序版本状态
                        $("#txtSPNo").val(sPNo);  //产品编号
                        $('#ProductBut_open').click();
                        $("#txtSPNo").val("");  //产品编号

                        ShowProcFileInfo(sPNo, sNo, sPVer); // 显示工艺文件 
                        ShowProcDeviceInfo(sPNo, sNo, sPVer); // 显示设备信息
                        ShowTestItemInfo(sPNo, sNo, sPVer); // 显示测试信息

                    } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                        $("#Copy_Btn").removeAttr("disabled");
                        $("#div_warning").html("您未登陆系统，请先登录！")
                        $("#div_warning").show();
                    } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXSITstu') {
                        $("#Copy_Btn").removeAttr("disabled");
                        $("#div_warning").html("该工序版本不存在：输入版本后，点保存，再复制！")
                        $("#div_warning").show();
                    } else {
                        $("#Copy_Btn").removeAttr("disabled");
                        $("#div_warning").html(parsedJson.Msg);
                        $("#div_warning").show();
                    }
                },
                error: function (data) {
                    $("#Disable_Btn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试2！")
                    $("#div_warning").show();
                }
            });
        } // if (sCF) 
    });


    // 新增产品流程工序状态下，点击重置，可再次录入其他工序
    $('#Reset_Btn').click(function () {
        var sFlag = $("#txtAEFlag").val();  // 4 标识新增，5 标识修改

        if (sFlag == "4") {  //新增
            $("#txtProcNo").val("");
            $("#txtProcedureVer").val("");
            $("#txtFlowOrder").val("");
            $("#txtConditionNo").val("");
            $("#txtVerDesc").val("");
            $("#txtCWUnit").val("");
        }
        else {
            $("#div_warning").html("只能在新增工序操作下，才可以重置，重新录入新的工序！")
            $("#div_warning").show();
        }

    });



    //  根据工序版本，获取相关信息  
    $('#txtProcedureVer').blur(function () {
        $("#div_warning").hide();

        var sPNo = $("#txtProductNo").val();
        var sTNo = $("#txtProcNo").val();  //工序  (05)总装
        var sProcNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
        var sVer = $("#txtProcedureVer").val();

        if (sVer == "") {
            $("#div_warning").html("请输入工序版本！")
            $("#div_warning").show();
            return;
        }

        ShowProcFileInfo(sPNo, sProcNo, sVer); // 显示工艺文件 
        ShowProcDeviceInfo(sPNo, sProcNo, sVer); // 显示设备信息
        ShowProcQualityInfo(sPNo, sProcNo, sVer); // 显示质控品
        ShowTestItemInfo(sPNo, sProcNo, sVer); // 显示测试信息
        ShowProductFlowBaseInfo(sPNo, sProcNo, sVer); // 显示基本信息

    });




    // 产品流程-上传工艺文件
    $('#btnPrdFileload').click(function () {
        $("#div_warningFile").val("");
        var p = $("#txtFPNo");
        if (p.val() == "") {
            $("#div_warningFile").html("无法获取产品编码！")
            $("#div_warningFile").show();
            return;
        }

        var sTNo = $("#txtFProNo").val();  //工序  (05)总
        var sNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
        var sName = sTNo.substr(sTNo.indexOf(")") + 1, sTNo.length);
        var sPVer = $("#txtFProVer").val();  //工序版本
        var sStr = p.val() + " " + sNo + " " + sPVer;

        var arr = new Array();
        var sPath = $("#txtPath").val();
        var sUpFile = $("#UpFile").val();  // ,用下面这种方式，解决中文乱码问题。
        arr = sPath.split(';/'); //注split可以用字符或字符串分割
        if (arr.length > 1) {
            $("#div_warningFile").html("每次只能上传一份文件！")
            $("#div_warningFile").show();
            return;
        }
        else {
            $("#Loading").show();
            $.ajaxFileUpload({
                url: '../Service/ApplyUpPicFile.aspx?NNo=' + sStr + '&sFlag=20&sUpFile=' + encodeURI(sUpFile) + '&ModuleName=' + encodeURI(ModuleName),
                fileElementId: 'UpFile',
                dataType: 'json',
                success: function (data) {
                    if (data.Msg == "Success") {
                        //var parsedJson = jQuery.parseJSON(data);
                        data = eval(data);
                        var sPath = $("#txtPath").val();
                        $("#txtPath").val(sPath + ";" + data.Path);
                        var sFile = $("#txtFile").val();
                        $("#txtFile").val(sFile + ";" + data.FileName);

                        $("#UpFile").val("");
                        $("#Loading").hide();

                        $("#div_warningFile").html("文件上传成功")
                        $("#div_warningFile").show();
                    //alert("上传成功");
                    } else {
                        $("#div_warningFile").html("文件上传失败")
                        $("#div_warningFile").show();
                    }
                }
            });
        }
    });




    //  保存:产品流程工序版本对应的工艺文件信息
    $('#FileSave_Btn').click(function () {
        $("#FileSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warningFile").hide();


        var sPNo = $("#txtFPNo").val();
        var sTNo = $("#txtFProNo").val();  //工序  (05)总
        var sNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
        var sName = sTNo.substr(sTNo.indexOf(")") + 1, sTNo.length);
        var sPVer = $("#txtFProVer").val();  //工序版本
        var sPath = $("#txtPath").val();  //  文件路径
        var sFile = $("#txtFile").val();  // 文件名称-带扩展名
        var sFNo = $("#txtFileNo").val();   // 文件编码
        var sFVer = $("#txtFileVer").val();   // 文件版本
        var sFName = $("#txtFileName").val();   // 文件名称-显示报表上的
        var sFlag = $("#txtAddKind").val();  //  新增 9-2-1，9-2-2 标识修改

        if (sPath == "") {
            $("#div_warningFile").html("请选择工艺文件！")
            $("#div_warningFile").show();
            $("#FileSave_Btn").removeAttr("disabled");
            return;
        }
        if (sFNo == "") {
            $("#div_warningFile").html("请填写文件编号！")
            $("#div_warningFile").show();
            $("#FileSave_Btn").removeAttr("disabled");
            return;
        }

        if (sFVer == "") {
            $("#div_warningFile").html("请填写文件版本！")
            $("#div_warningFile").show();
            $("#FileSave_Btn").removeAttr("disabled");
            return;
        }
        if (sFName == "") {
            $("#div_warningFile").html("请填写文件名称！")
            $("#div_warningFile").show();
            $("#FileSave_Btn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { No: sNo, Name: sName, Item: sPVer, MNo: sPNo, MName: "", A: sPath, B: sFile, C: sFNo, D: sFVer, E: sFName, F: "", G: "", H: "", I: "", J: "", K: "", L: ModuleName, Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/TechAjax.ashx?OP=OPTechInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#div_warningFile").html("提交成功！")
                    $("#div_warningFile").show();
                    $("#FileSave_Btn").removeAttr("disabled");

                    $('#File_SearchOpen').click();

                    document.getElementById('Div_File').style.display = 'none';

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#FileSave_Btn").removeAttr("disabled");
                    $("#div_warningFile").html("您未登陆系统，请先登录！")
                    $("#div_warningFile").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#FileSave_Btn").removeAttr("disabled");
                    $("#div_warningFile").html("该工艺文件编码+版本已存在，请确认！")
                    $("#div_warningFile").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                    layer.msg('该工序版本已禁用，不能操作！');
                }
                else {
                    $("#FileSave_Btn").removeAttr("disabled");
                    $("#div_warningFile").html(parsedJson.Msg);
                    $("#div_warningFile").show();
                }
            },
            error: function (data) {
                $("#FileSave_Btn").removeAttr("disabled");
                $("#div_warningFile").html("系统出错，请重试2！")
                $("#div_warningFile").show();
            }
        });
    });



    //  产品工艺流程：测试项，如果是“数字类型”时，显示可设置上下限 
    $('#txtValueKind').change(function () {
        $("#div_warningTestItem").hide();

        var sKind = $("#txtValueKind").val();

        if (sKind == "数值类型") {
            $("#div_QJ").show();
        }
        else {
            $("#div_QJ").hide();
        }
        $("#txtRangeKind").val("");
        $("#txtDownV").val("");
        $("#txtUpV").val("");
        $("#CH_Down").removeProp("checked"); //设置为不选中状态
        $("#CH_Up").removeProp("checked"); //设置为不选中状态
    });


    //  产品工艺流程：测试项，如果是“数字类型”时，设置上下限
    $('#txtRangeKind').blur(function () {

        ChangeTestItemVal();
    });

    //  产品工艺流程：测试项，如果是“数字类型”时，设置上下限
    $('#txtRangeKind').change(function () {

        ChangeTestItemVal();
    });


    //  产品流程:批量把测试项库数据保存给产品流程
    $('#CHGetTestItem_AddAll').click(function () {
        $("#CHGetTestItem_AddAll").attr({ "disabled": "disabled" });
        $("#L_GetTestItem").hide();


        var sTechNo = $("#txtTechNo").val();
        var sTNo = $("#txtTProcNo").val(); // (05)总装 
        var sNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
        var sName = sTNo.substr(sTNo.indexOf(")") + 1, sTNo.length);  //工序  (05)总装 截取字符串，字符位置
        var sPNo = $("#txtTPNo").val();  // 产品编码
        var sPVer = $("#txtTProVer").val(); // 工序版本 
        var sFlag = "9-3-7";

        var Data = '';
        var Params = { No: sNo, Name: sName, Item: sPVer, MNo: sPNo, MName: sTechNo, A: "", B: "", C: "", D: "", E: "", F: "", G: "", H: "", I: "", J: "", K: ModuleName, L: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/TechAjax.ashx?OP=OPTechInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    layer.msg('添加成功！');

                    $("#CHGetTestItem_AddAll").removeAttr("disabled")

                    $('#CHGetTestItem_SearchOpen').click();
                    $('#TestItem_SearchOpen').click();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                    layer.msg('该工序版本已禁用，不能操作.');
                    $("#CHGetTestItem_AddAll").removeAttr("disabled")
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    layer.msg('该测试项已选择，无需再选。');
                    $("#CHGetTestItem_AddAll").removeAttr("disabled")
                }
                else {
                    layer.msg('已添加，无需重复添加。');
                    $("#CHGetTestItem_AddAll").removeAttr("disabled")
                }
            },
            error: function (data) {
                layer.msg('添加失败2，请重试！');
                $("#CHGetTestItem_AddAll").removeAttr("disabled")
            }
        });

    });

    //  产品流程保存:产品流程工序版本对应的测试项信息
    $('#TestItemSave_Btn').click(function () {
        $("#TestItemSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warningTestItem").hide();

        var sTechNo = $("#txtTechNo").val(); // 工艺代号 产品代号
        var sPNo = $("#txtTIPNo").val();
        var sTNo = $("#txtTIProNo").val();  //工序  (05)总
        var sNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
        var sName = sTNo.substr(sTNo.indexOf(")") + 1, sTNo.length);
        var sPVer = $("#txtTIProVer").val();  //工序版本
        var sNCH = $("#txtNameCH").val();  //  检验项目
        var sDCH = $("#txtDescCH").val();  // 检验条件说明
        var sSCH = $("#txtStandardCH").val();   // 检验要求
        var sVKind = $("#txtValueKind").val();   // 数据类型
        var sRKind = $("#txtRangeKind").val();   // 请填写区间值
        var sUnit = $("#txtUnit").val();   // 请填写区间值
        var sDV = $("#txtDownV").val();   // 下限
        var sUV = $("#txtUpV").val();   // 上限值
        var sSpecNo = $("#txtSpecNo").val(); // 测试项编号
        var sSeqNo = $("#txtSeqNo").val();   // 当前测试项的顺序编号，点击网格某个记录的“增加”时使用
        var sFlag = $("#txtAddKind").val();  //  新增 9-3-1，9-3-2 标识修改
        var sDF = "";
        var sUF = "";


        if (sTechNo == "") {
            $("#div_warningTestItem").html("该产品编码无产品代号，请到物料数据维护！")
            $("#div_warningTestItem").show();
            $("#TestItemSave_Btn").removeAttr("disabled");
            return;
        }
        if ($("#CH_Down").is(':checked')) {  //  包含下限值
            sDF = "1";
        }
        if ($("#CH_Up").is(':checked')) {  // 包含上限值
            sUF = "1";
        }


        if (sRKind == "大于") {
            if (sDV == "") {
                $("#div_warningTestItem").html("请填写大于值！")
                $("#div_warningTestItem").show();
                $("#TestItemSave_Btn").removeAttr("disabled");
                return;
            }
        }
        if (sRKind == "小于") {
            if (sUV == "") {
                $("#div_warningTestItem").html("请填写小于值！")
                $("#div_warningTestItem").show();
                $("#TestItemSave_Btn").removeAttr("disabled");
                return;
            }
        }
        if (sRKind == "介于") {
            if ((sDV == "") || (sUV == "")) {
                $("#div_warningTestItem").html("请填写区间值！")
                $("#div_warningTestItem").show();
                $("#TestItemSave_Btn").removeAttr("disabled");
                return;
            }
        }
        if (sRKind == "等于") {
            if (sUV == "") {
                $("#div_warningTestItem").html("请填写等于值！")
                $("#div_warningTestItem").show();
                $("#TestItemSave_Btn").removeAttr("disabled");
                return;
            }
        }


        if (sNCH == "") {
            $("#div_warningTestItem").html("请输入检验项目！")
            $("#div_warningTestItem").show();
            $("#TestItemSave_Btn").removeAttr("disabled");
            return;
        }
        if (sDCH == "") {
            $("#div_warningTestItem").html("请输入检验条件说明！")
            $("#div_warningTestItem").show();
            $("#TestItemSave_Btn").removeAttr("disabled");
            return;
        }
        if ((sVKind == "") || (sVKind == "null") || (sVKind == null)) {
            $("#div_warningTestItem").html("请选择数据类型！")
            $("#div_warningTestItem").show();
            $("#TestItemSave_Btn").removeAttr("disabled");
            return;
        }

        if (sDV != "") {
            if (isNaN(sDV)) {
                $("#div_warningTestItem").html("请填写数值！")
                $("#div_warningTestItem").show();
                $("#TestItemSave_Btn").removeAttr("disabled");
                return;
            }
        }
        if (sUV != "") {
            if (isNaN(sUV)) {
                $("#div_warningTestItem").html("请填写数值！")
                $("#div_warningTestItem").show();
                $("#TestItemSave_Btn").removeAttr("disabled");
                return;
            }
        }


        var Data = '';
        var Params = { No: sNo, Name: sName, Item: sPVer, MNo: sPNo, MName: sTechNo, A: sNCH, B: sDCH, C: sSCH, D: sVKind, E: sRKind, F: sDV, G: sUV, H: sDF, I: sUF, J: sSpecNo, K: ModuleName, L: sSeqNo, M: sUnit, N: "", O: "", P: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/TechAjax.ashx?OP=OPTechInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#div_warningTestItem").html("提交成功！")
                    $("#div_warningTestItem").show();
                    $("#TestItemSave_Btn").removeAttr("disabled");

                    $('#TestItem_SearchOpen').click();

                    document.getElementById('Div_TestItem').style.display = 'none';

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#TestItemSave_Btn").removeAttr("disabled");
                    $("#div_warningTestItem").html("您未登陆系统，请先登录！")
                    $("#div_warningTestItem").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#TestItemSave_Btn").removeAttr("disabled");
                    $("#div_warningTestItem").html("该测试记录已存在，请确认！")
                    $("#div_warningTestItem").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                    layer.msg('该工序版本已禁用，不能操作！');
                }
                else {
                    $("#TestItemSave_Btn").removeAttr("disabled");
                    $("#div_warningTestItem").html(parsedJson.Msg);
                    $("#div_warningTestItem").show();
                }
            },
            error: function (data) {
                $("#TestItemSave_Btn").removeAttr("disabled");
                $("#div_warningTestItem").html("系统出错，请重试2！")
                $("#div_warningTestItem").show();
            }
        });
    });







    //  根据物料编码获取物料信息  
    $('#txtProductNo').blur(function () {
        $("#div_warning").hide();
        $("#divsuccess").hide();

        var sNo = $("#txtProductNo").val();
        var Flag = "33-99";

        if (sNo == "") {
            $("#div_warning").html("请输入物料编码！")
            $("#divsuccess").show();
            return;
        }

        var Data = '';
        var Params = { No: sNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetMaterInfoByNo&CFlag=" + Flag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $("#txtProductName").val(sStr.MaterName);
                    $("#txtModel").val(sStr.MaterSpec);
                    $("#txtTechNo").val(sStr.TechNo);
                    //$("#txtPModel").val(sStr.MaterSpec);

                }
                else {
                    $("#txtProductNo").val("");
                    $("#txtModel").val("");
                    $("#txtTechNo").val("");
                    $("#txtProductName").val("");
                    $("#div_warning").html("系统无此产品编码，请确认！")
                    $("#div_warning").show();
                }
            },
            error: function (data) {
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });




    //  保存:工作中心
    $('#WCTSaveBtn').click(function () {
        $("#WCTSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").html("");

        var sFNo = $("#txtFWNo").val();   // FWNo,FWName,WNo,WName,Kind
        var sFName = $("#txtFWName").val();
        var sCNo = $("#txtWNo").val();
        var sCName = $("#txtWName").val();
        var sFKind = $("#txtFKind").val();
        var sKind = $("#txtKind").val();
        var sTIP = $("#txtTIP").val(); // 测试工装电脑IP
        var sTUSER = $("#txtTUSER").val();// 访问工装电脑账号
        var sTPWD = $("#txtTPWD").val();// 访问工装电脑密码
        var sEPath = $("#txtEPath").val();// 服务器路径
        var sRemark = $("#txtRemark").val();  //  
        var sDeptNo = $("#txtDept").val();  //获取部门编号
        // 获取部门名称，使用正则匹配 ) 后的内容
        var sDeptNameMatch = $("#txtDept option:selected").text().match(/\)(.*)/);
        var sDeptName = sDeptNameMatch ? sDeptNameMatch[1] : "";
        var sFlag = $("#txtAEFlag").val();  // 10 新增   11修改


        if (sFNo == "") {
            ErrorMessage("请输入上级工作中心编码！", 2000)
            $("#WCTSaveBtn").removeAttr("disabled");
            return;
        }
        if (sFName == "") {
            ErrorMessage("请输入上级工作中心名称！", 2000)
            $("#WCTSaveBtn").removeAttr("disabled");
            return;
        }

        if (sCNo == "") {
            ErrorMessage("请输入子工作中心编码！", 2000)
            $("#WCTSaveBtn").removeAttr("disabled");
            return;
        }

        //if (sCName == "") {
        //    $("#div_warning").html("获取不到子零件名称，光标放在子编码内再离开！")
        //    $("#div_warning").show();
        //    $("#WCTSaveBtn").removeAttr("disabled");
        //    return;
        //}

        if (sKind == "") {
            ErrorMessage("请选择类别！", 2000)
            $("#WCTSaveBtn").removeAttr("disabled");
            return;
        }

        if (sFKind == sKind) {
            ErrorMessage("类别选择有误，不能和父级类别一样", 2000)
            $("#WCTSaveBtn").removeAttr("disabled");
            return;
        }
        if (sFKind == "车间") {
            if (sKind == "工厂") {
                ErrorMessage("车间下不能有工厂", 2000)
                $("#WCTSaveBtn").removeAttr("disabled");
                return;
            }
        }
        if (sFKind == "线体") {
            if ((sKind == "工厂") || (sKind == "车间")) {
                ErrorMessage("线体下不能有工厂或车间", 2000)
                $("#WCTSaveBtn").removeAttr("disabled");
                return;
            }
        }
        if (sFKind == "工段") {
            if ((sKind == "工厂") || (sKind == "车间") || (sKind == "线体")) {
                ErrorMessage("线体下不能有工厂或车间或线体", 2000)
                $("#WCTSaveBtn").removeAttr("disabled");
                return;
            }
        }
        if (sKind == "车间") {
            if (sDeptNo == "" || sDeptNo == "") {
                ErrorMessage("请选择部门！", 2000)
                $("#WCTSaveBtn").removeAttr("disabled");
                return;
            }
        }

        var Data = '';
        var Params = { No: sFNo, Name: sFName, Item: "", MNo: sCNo, MName: sCName, A: sKind, B: sTIP, C: sTUSER, D: sTPWD, E: sEPath, F: ModuleName, G: sDeptNo, H: sDeptName, Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/TechAjax.ashx?OP=OPTechInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#WCTSaveBtn").removeAttr("disabled");
                    $("#divsuccess").show();

                    closeDialog()

                    $('#WCTBut_open').click();


                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#WCTSaveBtn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#WCTSaveBtn").removeAttr("disabled");
                    $("#txtCNo").val("");
                    $("#txtCName").val("");
                    ErrorMessage("上层工作中心下已有下层工作中心，请确认", 2000)
                }
                else {
                    $("#WCTSaveBtn").removeAttr("disabled");
                    ErrorMessage("系统出错，请重试1！", 2000)
                }
            },
            error: function (data) {
                $("#WCTSaveBtn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试1！", 2000)
            }
        });
    });


    //  保存:工序行为信息
    $('#ProcActionSave_Btn').click(function () {
        $("#ProcActionSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();

        var sNo = $("#txtPANo").val();  //工序行为 PANo,PAName,PADesc,PrintLable,ScanMater,UpPack
        var sName = $("#txtPAName").val();
        var sAct = $("#txtProcAct").val();
        var sMAC = $("#txtMAC").val();
        var sDesc = $("#txtPADesc").val();
        var sPLable = "否";
        var sSMater = "否";
        var sUpPack = "否";
        var sUpBatch = "否";
        var sTestItem = "否";
        var sScanDevice = "否";
        var sScanZKP = "否";  // 质控品
        var sIsFrOther = "否";
        var sTType = "";  // 放下面赋值
        var sChangeFA = "否";  // 作业执行是否自动更改检验方案  
        var sCheckDHR = "否";
        var sEPath = $("#txtEPath").val(); // EXCEL测试项的存放路径
        var sIP = $("#txtTIP").val(); // 测试工装IP
        var sUSER = $("#txtTUSER").val(); // 登录测试工装的账号（如访问某个电脑共享文件时，需要输入账号密码）
        var sPWD = $("#txtTPWD").val(); // 登录测试工装的密码
        var sRemark = $("#txtRemark").val();
        var sFlag = $("#txtAEFlag").val();  // 16 标识新增， 17 标识修改

        if (sName == "") {
            $("#div_warning").html("请输入行为描述！")
            $("#div_warning").show();
            $("#ProcActionSave_Btn").removeAttr("disabled");
            return;
        }

        if (sAct == "") {
            $("#div_warning").html("请选择工序行为！")
            $("#div_warning").show();
            $("#ProcActionSave_Btn").removeAttr("disabled");
            return;
        }

        if ($("#txtPrintLable").is(':checked')) {
            sPLable = "是";
        }
        if ($("#txtScanMater").is(':checked')) {
            sSMater = "是";
        }
        if ($("#txtUpPack").is(':checked')) {
            sUpPack = "是";
        }
        if ($("#txtUpBatch").is(':checked')) {
            sUpBatch = "是";
        }
        if ($("#txtKeyInTestItem").is(':checked')) {
            sTestItem = "是";
        }
        if ($("#txtScanDevice").is(':checked')) {
            sScanDevice = "是";
        }
        if ($("#txtScanZKP").is(':checked')) {
            sScanZKP = "是";
        }
        if ($("#txtIsFrOther").is(':checked')) {
            sIsFrOther = "是";
            sTType = $("#txtTestItemType").val();

            //if ((sIP == "") || (sUSER == "") || (sPWD == "")) {  // 240312 :修改到作业单元那边维护了
            //    $("#div_warning").html("请设置第三方的IP，账号密码！")
            //    $("#div_warning").show();
            //    $("#ProcActionSave_Btn").removeAttr("disabled");
            //    return;
            //}
        }
        if ($("#txtChangeFA").is(':checked')) {
            sChangeFA = "是";
        }
        if ($("#txtCheckDHR").is(':checked')) {
            sCheckDHR = "是";
        }

        // 如选择了“批量作业工序行为”，就不需选择 是否生成上层包装 和 是否生成上成检验批号 。因为作业执行时会根据“批量作业工序行为”自动产生一个批次号，表示该工序可以多个序列号一起作业
        if (sAct == "批量作业工序行为") {
            if ((sUpPack == "是") || sUpBatch == "是") {
                $("#div_warning").html("选择了“批量作业工序行为”，无需选择生产上层批次！")
                $("#div_warning").show();
                $("#ProcActionSave_Btn").removeAttr("disabled");
                return;
            }
        }

        // 如选择了“送检抽检合并”，就不需选择 是否生成上层包装 和 是否生成上成检验批号 。因为作业执行时会根据“送检抽检合并”自动产生一个批次号，表示该工序可以多个序列号一起作业
        if (sAct == "送检抽检合并") {
            if ((sUpPack == "是") || sUpBatch == "是") {
                $("#div_warning").html("选择了“送检抽检合并”，无需选择生产上层批次！")
                $("#div_warning").show();
                $("#ProcActionSave_Btn").removeAttr("disabled");
                return;
            }
        }

        var Data = '';
        var Params = { No: sNo, Name: sName, Item: sDesc, MNo: sTestItem, MName: sScanDevice, A: sPLable, B: sSMater, C: sUpPack, D: sMAC, E: sAct, F: sUpBatch, G: sIsFrOther, H: sTType, I: sScanZKP, J: sChangeFA, K: sEPath, L: sIP, M: sUSER, N: sPWD, O: sCheckDHR, P: ModuleName, Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/TechAjax.ashx?OP=OPTechInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#ProcActionSave_Btn").removeAttr("disabled");

                    $("#txtSPMNo").val(sNo);  //工序
                    $('#ProcAcBut_open').click();
                    $("#txtSPMNo").val("");  //工序

                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#ProcActionSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#ProcActionSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("工序行为名称已存在，请确认！")
                    $("#div_warning").show();
                } else {
                    $("#ProcActionSave_Btn").removeAttr("disabled");
                    $("#div_warning").html(parsedJson.Msg);
                    $("#div_warning").show();
                }
            },
            error: function (data) {
                $("#ProcActionSave_Btn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });

    $("#InTable").on("change", "input[type=checkbox]", function () {
        var checkbox = $(this);
        if (checkbox.is(":checked")) {
            // 如果复选框被选中，则将其值添加到数组中
            selectedCheckboxes.push(checkbox.val());
        } else {
            // 如果复选框被取消选中，则从数组中移除其值
            var index = selectedCheckboxes.indexOf(checkbox.val());
            if (index !== -1) {
                selectedCheckboxes.splice(index, 1);
            }
        }
    })

    //  产品工艺流程、员工对应作业单元 ：作业单元，选择工厂后
    $('#txtGC').change(function () {

        var sFNo = encodeURI($("#txtGC").val());
        $("#txtCJ").empty();
        $("#txtXT").empty();
        ChoogeUnit("", sFNo, "工厂", selectedCheckboxes);  // 显示这个工厂对应的作业单元
        GetWorkUnitConList("车间", sFNo);  // 显示这个工厂下的车间
    });

    //  产品工艺流程：作业单元，选择车间后 encodeURI(Kind);
    $('#txtCJ').change(function () {

        var sFNo = encodeURI($("#txtGC").val());
        var sWNo = encodeURI($("#txtCJ").val());
        $("#txtXT").empty();

        ChoogeUnit(sFNo, sWNo, "车间", selectedCheckboxes);  // 显示这个车间对应的作业单元
        GetWorkUnitConList("线体", sWNo);  // 显示这个车间下的线体
    });

    //  产品工艺流程：作业单元，选择线体后，直接显示作业单元
    $('#txtXT').change(function () {
        var sFNo = encodeURI($("#txtCJ").val());
        var sWNo = encodeURI($("#txtXT").val());

        ChoogeUnit(sFNo, sWNo, "线体", selectedCheckboxes);  // 显示这个线体对应的作业单元
    });


    // 选择对应的作业单元 -- 产品流程选择
    $('#CUnit_SaveBtn').click(function () {
        $("#CUnit_SaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning2").hide();
        $("#divsuccess2").hide();
        $("#txtCWUnit").val("");


        var sUnitStr = selectedCheckboxes.join(";");  //

        if (sUnitStr == "") {
            $("#div_warning2").html("请选择对应的作业单元！")
            $("#div_warning2").show();
            $("#CUnit_SaveBtn").removeAttr("disabled");
            return;
        }

        $("#txtCWUnit").val(sUnitStr);
        $("#InTable").empty();
        document.getElementById('Div_WUnit').style.display = 'none';
        selectedCheckboxes = []
        $("#CUnit_SaveBtn").removeAttr("disabled");
        $("#txtGC").val("")
        $("#txtCJ").val("")
        $("#txtXT").val("")
    });


    // 选择对应的作业单元 -- 员工对应作业单元选择
    $('#UserWCUnit_SaveBtn').click(function () {
        $("#UserWCUnit_SaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning2").hide();
        $("#divsuccess2").hide();
        $("#txtCWUnit").val("");
        var sT = selectedCheckboxes.map(item => "'" + item.substring(1, item.lastIndexOf(')')) + "'");
        var sUnitStr = sT.join(",");  //
        var sUnitName = selectedCheckboxes.join(";")

        sUnitStr = '(' + sUnitStr + ')';  // 返回后台查询的

        if (sUnitStr == "") {
            $("#div_warning2").html("请选择对应的作业单元！")
            $("#div_warning2").show();
            $("#UserWCUnit_SaveBtn").removeAttr("disabled");
            return;
        }

        $("#txtCWUnit").val(sUnitStr);
        $("#txtCWUnitName").val(sUnitName);
        $("#InTable").empty();
        document.getElementById('Div_WUnit').style.display = 'none';
        selectedCheckboxes = []
        $("#UserWCUnit_SaveBtn").removeAttr("disabled");
        $("#txtGC").val("")
        $("#txtCJ").val("")
        $("#txtXT").val("")
    });



    //  根据用户编码获取用户名称
    $('#txtWCUserNo').blur(function () {
        $("#div_warning").hide();
        $("#divsuccess").hide();

        var sNo = $("#txtWCUserNo").val();
        var Flag = "10-99";

        if (sNo == "") {
            $("#div_warning").html("请输入员工编号！")
            $("#divsuccess").show();
            return;
        }

        var Data = '';
        var Params = { No: sNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetUserInfoByNo&CFlag=" + Flag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $("#txtWCUserName").val(sStr.FullName);
                }
                else {
                    $("#txtWCUserName").val("");
                    $("#div_warning").html("请确认用户是否存在或被禁用！")
                    $("#div_warning").show();
                }
            },
            error: function (data) {
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });



    //  保存:员工对应工作中心
    $('#WorkCenterSave_Btn').click(function () {
        $("#WorkCenterSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();


        var sUNo = $("#txtWCUserNo").val();
        var sUName = $("#txtWCUserName").val();
        var sWCNo = $("#txtCWUnit").val();    // ('U0001','U0005','U0006','U0010')
        var sRemark = $("#txtRemark").val();  //  
        var sFlag = $("#txtAEFlag").val();  // 20 新增


        if (sUNo == "") {
            $("#div_warning").html("请输入员工编号！")
            $("#div_warning").show();
            $("#WorkCenterSave_Btn").removeAttr("disabled");
            return;
        }
        if (sUName == "") {
            $("#div_warning").html("没有员工名称！")
            $("#div_warning").show();
            $("#WorkCenterSave_Btn").removeAttr("disabled");
            return;
        }

        if (sWCNo == "") {
            $("#div_warning").html("请选择作业单元！")
            $("#div_warning").show();
            $("#WorkCenterSave_Btn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { No: sUNo, Name: sUName, Item: "", MNo: "", MName: "", A: sWCNo, B: "", C: "", D: "", E: "", F: ModuleName, Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/TechAjax.ashx?OP=OPTechInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#WorkCenterSave_Btn").removeAttr("disabled");
                    $("#divsuccess").show();
                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                    $("#txtSUserNo").val(sUNo);  //用户编码
                    $('#WorkCenterBut_open').click();  // 重新查询
                    $("#txtSUserNo").val("");  //用户编码

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#WorkCenterSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                }
                else {
                    $("#WorkCenterSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function (data) {
                $("#WorkCenterSave_Btn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });




    //  保存:异常代码
    $('#ECodeSave_Btn').click(function () {
        $("#ECodeSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();

        var sNo = $("#txtECode").val();  //异常代码
        var sName = $("#txtEName").val();
        var sRemark = $("#txtRemark").val();
        var sFlag = $("#txtAEFlag").val();  // 1 标识新增， 2 标识修改

        if (sNo == "") {
            $("#div_warning").html("请输入异常代码！")
            $("#divsuccess").show();
            $("#ECodeSave_Btn").attr({ "disabled": "disabled" });
            return;
        }
        if (sName == "") {
            $("#div_warning").html("请输入代码名称！")
            $("#divsuccess").show();
            $("#ECodeSave_Btn").attr({ "disabled": "disabled" });
            return;
        }



        var Data = '';
        var Params = { No: sNo, Name: sName, Item: "", MNo: "", MName: "", A: "", B: "", C: "", D: "", E: "", F: "", Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/TechAjax.ashx?OP=OPTechInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#ECodeSave_Btn").removeAttr("disabled");

                    $("#txtSECode").val(sNo);  //异常代码
                    $('#ECodeBut_open').click();
                    $("#txtSECode").val("");  //工序

                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#ECodeSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#ECodeSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("异常代码已存在，请确认！")
                    $("#div_warning").show();
                } else {
                    $("#ECodeSave_Btn").removeAttr("disabled");
                    $("#div_warning").html(parsedJson.Msg);
                    $("#div_warning").show();
                }
            },
            error: function (data) {
                $("#ECodeSave_Btn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });



    //  测试项库：测试项，如果是“数字类型”时，显示可设置上下限  （和上面的产品流程建立测试项类似）
    $('#txtBaseValueKind').change(function () {
        $("#div_warningTestItem").hide();

        var sKind = $("#txtBaseValueKind").val();

        if (sKind == "数值类型") {
            $("#div_QJ").show();
        }
        else {
            $("#div_QJ").hide();
        }
        $("#txtBaseRangeKind").val("");
        $("#txtDownV").val("");
        $("#txtUpV").val("");
        $("#CH_Down").removeProp("checked"); //设置为不选中状态
        $("#CH_Up").removeProp("checked"); //设置为不选中状态
    });


    //  测试项库：测试项，如果是“数字类型”时，设置上下限
    $('#txtBaseRangeKind').blur(function () {

        ChangeBaseTestItemVal();
    });

    //  测试项库：测试项，如果是“数字类型”时，设置上下限
    $('#txtBaseRangeKind').change(function () {

        ChangeBaseTestItemVal();
    });


    //  测试项库：保存:产品流程工序版本对应的测试项信息
    $('#TestItemBaseSave_Btn').click(function () {
        $("#TestItemBaseSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warningTestItem").hide();


        var sTech = $("#txtTechNo").val();
        var sModel = $("#txtModel").val();
        var sUnit = $("#txtUnit").val();
        var sTNo = $("#txtProcNo").val();  //工序  (05)总
        var sNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
        var sName = sTNo.substr(sTNo.indexOf(")") + 1, sTNo.length);
        var sNCH = $("#txtNameCH").val();  //  检验项目
        var sDCH = $("#txtDescCH").val();  // 检验条件说明  --- 检验要求
        var sSCH = $("#txtStandardCH").val();   // 231101暂时不用： 检验要求
        var sVKind = $("#txtBaseValueKind").val();   // 数据类型
        var sRKind = $("#txtBaseRangeKind").val();   // 请填写区间值
        var sDV = $("#txtDownV").val();   // 下限
        var sUV = $("#txtUpV").val();   // 上限值
        var sSpecNo = $("#txtSpecNo").val(); // 测试项编号
        var sSeqNo = $("#txtSeqNo").val();   //  唯一编号
        var sFlag = $("#txtAEFlag").val();  //  新增 28-1，28-2 标识修改
        var sGXNo = $("#txtEditKind").val();
        var sDF = "";
        var sUF = "";

        if ($("#CH_Down").is(':checked')) {  //  包含下限值
            sDF = "1";
        }
        if ($("#CH_Up").is(':checked')) {  // 包含上限值
            sUF = "1";
        }


        if (sRKind == "大于") {
            if (sDV == "") {
                $("#TestItemBaseSave_Btn").removeAttr("disabled");
                ErrorMessage("请填写大于值！", 2000)
                return;
            }
        }
        if (sRKind == "小于") {
            if (sUV == "") {
                $("#TestItemBaseSave_Btn").removeAttr("disabled");
                ErrorMessage("请填写小于值！", 2000)
                return;
            }
        }
        if (sRKind == "介于") {
            if ((sDV == "") || (sUV == "")) {
                $("#TestItemBaseSave_Btn").removeAttr("disabled");
                ErrorMessage("请填写区间值！", 2000)
                return;
            }
        }
        if (sRKind == "等于") {
            if (sUV == "") {
                $("#TestItemBaseSave_Btn").removeAttr("disabled");
                ErrorMessage("请填写等于值！", 2000)
                return;
            }
        }


        if (sNCH == "") {
            $("#TestItemBaseSave_Btn").removeAttr("disabled");
            ErrorMessage("请输入检验项目！", 2000)
            return;
        }
        if (sDCH == "") {
            $("#TestItemBaseSave_Btn").removeAttr("disabled");
            ErrorMessage("请输入检验条件说明！", 2000)
            return;
        }
        if ((sVKind == "") || (sVKind == "null") || (sVKind == null)) {
            $("#TestItemBaseSave_Btn").removeAttr("disabled");
            ErrorMessage("请选择数据类型！", 2000)
            return;
        }

        if (sDV != "") {
            if (isNaN(sDV)) {
                $("#TestItemBaseSave_Btn").removeAttr("disabled");
                ErrorMessage("请填写数值！", 2000)
                return;
            }
        }
        if (sUV != "") {
            if (isNaN(sUV)) {
                $("#TestItemBaseSave_Btn").removeAttr("disabled");
                ErrorMessage("请填写数值！", 2000)
                return;
            }
        }

        var Data = '';
        var Params = { No: sTech, Name: sName, Item: sNo, MNo: sGXNo, MName: "", A: sNCH, B: sDCH, C: sSCH, D: sVKind, E: sRKind, F: sDV, G: sUV, H: sDF, I: sUF, J: sSpecNo, K: sModel, L: sSeqNo, M: sUnit, N: "", O: "", P: ModuleName, Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);


        $.ajax({
            type: "POST",
            url: "../Service/TechAjax.ashx?OP=OPTechInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#TestItemBaseSave_Btn").removeAttr("disabled");

                    $('#BaseTestItemBut_open').click();



                    $("#txtModel").val("");
                    $("#txtNameCH").val("");  //  检验项目  
                    $("#txtDescCH").val(""); //  检验描述
                    $("#txtStandardCH").val("");  //  要求 -- 231029：不需要维护这个，和描述一起即可
                    $("#txtBaseValueKind").val("");  // 数据类型
                    $("#txtUnit").val("");  // 单位
                    $("#div_QJ").hide();

                    $("#CH_Down").hide();
                    $("#L_BHD").hide();
                    $("#txtDownV").hide();
                    $("#L_Down").hide();
                    $("#L_Deng").hide();
                    $("#L_Up").hide();
                    $("#txtUpV").hide();
                    $("#CH_Up").hide();
                    $("#L_BHU").hide();

                    if (sFlag == "28-2") {
                        $('#ShowOne').css("display", "none");
                        $('#ShowTow-fade').css("display", "none");
                    }
                    layer.msg("提交成功！")
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#TestItemBaseSave_Btn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#TestItemBaseSave_Btn").removeAttr("disabled");
                    ErrorMessage("该初始记录已存在，请确认！", 2000)
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                    layer.msg('该工序版本已禁用，不能操作！');
                }
                else {
                    $("#TestItemBaseSave_Btn").removeAttr("disabled");
                    ErrorMessage(parsedJson.Msg, 2000)
                }
            },
            error: function (data) {
                $("#TestItemBaseSave_Btn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });



    //保存样本量字码
    $("#SampleSizeCodeSubmit").click(function () {
        var sBatch = $("#batch").val();
        var sBatch_start = $("#batch_start").val();
        var sBatch_end = $("#batch_end").val();
        var sInspection_level = $("#inspection_level").val();
        var sSample_code = $("#sample_code").val();
        var sRemark = $("#remark").val();

        if (sBatch == "") {
            ErrorMessage("请输入批量！", 2000)
            return;
        } else if (sBatch_start == "") {
            ErrorMessage("请输入批量区间头！", 2000)
            return;
        } else if (sBatch_end == "") {
            ErrorMessage("请输入批量区间尾！", 2000)
            return;
        } else if (sInspection_level == "请选择检验水平" || sInspection_level == null) {
            ErrorMessage("请输入检验水平！", 2000)
            return;
        } else if (sSample_code == "") {
            ErrorMessage("请输入样本量字码！", 2000)
            return;
        }

        var Params = { SCNo: '', Batch: sBatch, Batch_start: sBatch_start, Batch_end: sBatch_end, Inspection_level: sInspection_level, Sample_code: sSample_code, Remark: sRemark, Flag: "1" }

        var Data = JSON.stringify(Params)

        $.ajax({
            url: "../Service/TechAjax.ashx?OP=OPSampleSizeCodeInfo&CFlag=1",
            type: "POST",
            data: {
                Data: Data
            },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.msg == 'Success') {
                    $("#ShowOne").css("display", "none");
                    $("#ShowOne-fade").css("display", "none");
                    layer.msg('新增成功！');
                    $("#SampleSizeCodeSearch").click()
                } else {
                    ErrorMessage("系统出错，请重试1！", 2000)
                }
            },
            error: function () {
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        })
    })


    //保存抽样标准
    $("#SamplingStdSubmit").click(function () {
        var sSampleCode = $("#sampleCode").val();
        var sStringency = $("#stringency").val();
        var sAql = $("#aql").val();
        var sSampleSize = $("#sampleSize").val();
        var sRNum = $("#rNum").val();
        var sBNum = $("#bNum").val();
        var sRemark = $("#remark").val();


        if (sSampleCode == "" || sSampleCode == null) {
            ErrorMessage("请输入样本量字码！", 2000)
            return;
        } else if (sStringency == "" || sStringency == null) {
            ErrorMessage("请输入严格度！", 2000)
            return;
        } else if (sAql == "" || sAql == null) {
            ErrorMessage("请输入接收质量限！", 2000)
            return;
        } else if (sSampleSize == "" || sSampleSize == null) {
            ErrorMessage("请输入样本量！", 2000)
            return;
        } else if (sRNum == "" || sRNum == null) {
            ErrorMessage("请输入接收数！", 2000)
            return;
        } else if (sBNum == "" || sBNum == null) {
            ErrorMessage("请输入拒收数！", 2000)
            return;
        }


        var Params = { SSNo: '', SampleCode: sSampleCode, Aql: sAql, Stringency: sStringency, SampleSize: sSampleSize, RNum: sRNum, BNum: sBNum, Remark: sRemark, Flag: "1" }

        var Data = JSON.stringify(Params)

        $.ajax({
            url: "../Service/TechAjax.ashx?OP=OPSamplingStd&CFlag=1",
            type: "POST",
            data: {
                Data: Data
            },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.msg == 'Success') {

                    $("#ShowOne").css("display", "none");
                    $("#ShowOne-fade").css("display", "none");

                    layer.msg('新增成功！');
                    $("#SamplingStdSearch").click()
                } else {
                    ErrorMessage("系统出错，请重试1！", 2000)
                }
            },
            error: function () {
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        })
    })


    //保存抽样方案
    $("#SamplingPlanSubmit").click(function () {
        var sSPNo = $("#txtCYFANoEdit").val();
        var sSPName = $("#txtSPName").val();
        var sType = $("#txtType").val();
        var sAql = $("#txtAql").val();
        var sInspectLevel = $("#txtInspectLevel").val();
        var sStringency = $("#txtStringency").val();
        var sOPFlag = $("#txtAEFlag").val();

        if (sSPName == "") {
            ErrorMessage("请输入方案名称", 2000)
            return;
        } else if (sType == "" || sType == null) {
            ErrorMessage("请选择抽样类型", 2000)
            return;
        } else if (sInspectLevel == "" || sInspectLevel == null) {
            ErrorMessage("请选择检验水平", 2000)
            return;
        } else if (sStringency == "" || sStringency == null) {
            ErrorMessage("请选择严格度", 2000)
            return;
        } else if (sAql == "" || sAql == null) {
            ErrorMessage("请选择接收质量限", 2000)
            return;
        }


        var Params = { SPNo: sSPNo, SPName: sSPName, Type: sType, InspectLevel: sInspectLevel, Stringency: sStringency, Aql: sAql, OPFlag: sOPFlag, Flag: "1" }

        var Data = JSON.stringify(Params)

        $.ajax({
            url: "../Service/TechAjax.ashx?OP=OPSamplingPlan&CFlag=1",
            type: "POST",
            data: {
                Data: Data
            },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.msg == 'Success') {

                    closeDialog()

                    if (sOPFlag == "Add") {
                        layer.msg('新增成功！');
                    } else {
                        layer.msg('修改成功！');
                    }

                    $("#SamplingPlanSearch").click()

                    resetForm()
                } else {
                    ErrorMessage("未找到对应信息", 2000)
                }
            },
            error: function () {
                ErrorMessage("系统出错，请重试", 2000)
            }
        })
    })



    //拷贝文件2--临时测试使用的
    $('#COPYTwo_Btn').click(function () {

        var sPath = $("#txtEPath").val();
        var sSN = $("#txtMAC").val();  // 临时使用
        var sIP = $("#txtTIP").val();
        var sZH = $("#txtTUSER").val(); // 账号
        var sPWD = $("#txtTPWD").val();

        var Data = '';
        var Params = { SPNo: sSN, SPName: "", Path: sPath, A: sIP, B: sZH, C: sPWD, OPFlag: "", Flag: "2" }
        var Data = JSON.stringify(Params)

        $.ajax({
            type: "POST",
            url: "../Service/TechAjax.ashx?OP=COPYFILE&CFlag=2",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#COPYONE_Btn").removeAttr("disabled");
                    $("#L_SaveMgs").html("您未登陆系统，请先登录！");
                    //location.href = "Login.htm";
                }
            },
            error: function (data) {
                $("#COPYONE_Btn").removeAttr("disabled");
                $("#L_SaveMgs").html("系统出错，请重试2！");
            }
        });
    });




































    // 这里增加其他按钮，空间事件方法











});




// 用这个方法，主要是保证勾选不需要人勾，不设置disabled=disabled  ，这样用户看见的勾选比较清晰点。  2019  
function CheckBox_OnClick(ob) {
    var id = $(ob).attr("id");
    var sCD = id.substr(2, id.length);  // CB0  CB1  CB2


    if ($('#CB' + sCD).is(':checked')) {
        // $('#CB' + sCD).prop("checked", "checked");
        $('#CB' + sCD).removeProp("checked"); //设置为不选中状态
    }
    else {
        $('#CB' + sCD).prop("checked", "checked");
    }


}

// 产品流程，添加测试项时，选择了数值类型，区间范围类别变更时触发
function ChangeTestItemVal() {

    $("#div_warningTestItem").hide();

    var sKind = $("#txtRangeKind").val();

    if (sKind == "") {
        $("#div_warningTestItem").html("请选择区间类别！")
        $("#div_warningTestItem").show();
        return;
    }

    $("#txtDownV").val("");
    $("#txtUpV").val("");
    $("#CH_Down").removeProp("checked"); //设置为不选中状态
    $("#CH_Up").removeProp("checked"); //设置为不选中状态

    $("#CH_Down").hide();
    $("#L_BHD").hide();
    $("#txtDownV").hide();
    $("#L_Down").hide();
    $("#L_Deng").hide();
    $("#L_Up").hide();
    $("#txtUpV").hide();
    $("#CH_Up").hide();
    $("#L_BHU").hide();
    if (sKind == "大于") {
        $("#CH_Down").show();
        $("#L_BHD").show();
        $("#txtDownV").show();
        $("#L_Down").show();
    }
    else if (sKind == "小于") {
        $("#L_Up").show();
        $("#txtUpV").show();
        $("#CH_Up").show();
        $("#L_BHU").show();
    }
    else if (sKind == "介于") {
        $("#CH_Down").show();
        $("#L_BHD").show();
        $("#txtDownV").show();
        $("#L_Down").show();
        $("#L_Up").show();
        $("#txtUpV").show();
        $("#CH_Up").show();
        $("#L_BHU").show();
    }
    else if (sKind == "等于") {
        $("#L_Deng").show();
        $("#txtUpV").show();
    }

}


// 测试项库，添加测试项时，选择了数值类型，区间范围类别变更时触发
function ChangeBaseTestItemVal() {

    $("#div_warningTestItem").hide();

    var sKind = $("#txtBaseRangeKind").val();

    if (sKind == "") {
        $("#div_warningTestItem").html("请选择区间类别！")
        $("#div_warningTestItem").show();
        return;
    }

    $("#txtDownV").val("");
    $("#txtUpV").val("");
    $("#CH_Down").removeProp("checked"); //设置为不选中状态
    $("#CH_Up").removeProp("checked"); //设置为不选中状态

    $("#CH_Down").hide();
    $("#L_BHD").hide();
    $("#txtDownV").hide();
    $("#L_Down").hide();
    $("#L_Deng").hide();
    $("#L_Up").hide();
    $("#txtUpV").hide();
    $("#CH_Up").hide();
    $("#L_BHU").hide();
    if (sKind == "大于") {
        $("#CH_Down").show();
        $("#L_BHD").show();
        $("#txtDownV").show();
        $("#L_Down").show();
    }
    else if (sKind == "小于") {
        $("#L_Up").show();
        $("#txtUpV").show();
        $("#CH_Up").show();
        $("#L_BHU").show();
    }
    else if (sKind == "介于") {
        $("#CH_Down").show();
        $("#L_BHD").show();
        $("#txtDownV").show();
        $("#L_Down").show();
        $("#L_Up").show();
        $("#txtUpV").show();
        $("#CH_Up").show();
        $("#L_BHU").show();
    }
    else if (sKind == "等于") {
        $("#L_Deng").show();
        $("#txtUpV").show();
    }

}


//消息提示
function SuccessMessage(text, time) {
    var Message = $('#Success');
    Message.html(text)
    Message.show()
    setTimeout(function () {
        Message.hide()
    }, time);
}
function ErrorMessage(text, time) {
    var Message = $('#Error');
    Message.html(text)
    Message.show()
    setTimeout(function () {
        Message.hide()
    }, time);
}
function WarningMessage(text, time) {
    var Message = $('#Warning');
    Message.html(text)
    Message.show()
    setTimeout(function () {
        Message.hide()
    }, time);
}


