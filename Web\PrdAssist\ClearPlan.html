﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>清场计划</title>

    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <link href="../css/orderinfo.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <script src="../layui/layui.js" type="text/javascript"></script>
    <link href="../js/skin/layer.css" rel="stylesheet" />

    <script src="../js/layer/layer.js" type="text/javascript"></script>
    <script src="../js/PrdAssist.js" type="text/javascript"></script>



    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        //$('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=28-1' });
                    }
                }
            })
        });



    </script>


    <script type="text/javascript">


        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#ClearPlanHeadlist',
                id: 'ClearPlanHeadlistID',
                url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=13',
                height: 'full-80',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'CPNo', title: '清场计划编号', width: 200, sort: true },
                    { field: 'CPVer', title: '版本', width: 100, sort: true },
                    //{ field: 'ItemNo', title: '清场项目编号', width: 200 },
                    //{ field: 'CPTxt', title: '清场项目描述', width: 200 },
                    //{ field: 'Type', title: '类型', width: 200 },
                    { field: 'Remark', title: '备注', width: 300 },
                    { field: 'InMan', title: '创建人', width: 90 },
                    { field: 'InDate2', title: '创建时间', width: 200 },
                    { field: 'op', title: '操作', width: 180, toolbar: '#barDemo', fixed: 'right' }
                ]],
                page: true,


            });




            //监听是否选中操作
            table.on('checkbox(ClearPlanHeadlist)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);


            });


            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(ClearPlanHeadlist)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

            });

            //监听单元格编辑
            table.on('edit(ClearPlanHeadlist)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });


            //监听行工具事件
            table.on('tool(ClearPlanHeadlist)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值
                if (layEvent === 'del') {
                    layer.confirm('您确定要删除该清场计划么？', function (index) {


                        //向服务端发送禁用指令
                        var sCPNo = data.CPNo;
                        var sCPVer = data.CPVer;
                        var sFlag = "12";

                        var Data = '';
                        var Params = { No: sCPNo, Name: sCPVer, A: sCPVer, B: "", C: "", D: "", E: "", F: "", I: "", J: "", K: "", L: "", Kind: "", Remark: "", Flag: sFlag };
                        var Data = JSON.stringify(Params);
                        $.ajax({
                            type: "POST",
                            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
                            data: { Data: Data },
                            success: function (data) {
                                var parsedJson = jQuery.parseJSON(data);

                                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                    layer.msg('删除成功！');

                                    $('#ClearPlanBut_open').click();  // 重新查询

                                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                                    layer.msg('该清场计划已维护清场记录，不能删除！');

                                }
                                else {
                                    layer.msg('删除失败，请重试！');
                                    $('#ClearPlanBut_open').click();  // 重新查询
                                }
                            },
                            error: function (data) {
                                layer.msg('删除失败2，请重试！');
                                $('#ClearPlanBut_open').click();  // 重新查询
                            }
                        });

                    }); // 删除

                }
                else if (layEvent === 'edit') {
                    $('#L_ShowClearPlanHead').html("修改清场计划信息");
                    $('#txtAEFlag').val("11");

                    $("#txtCPNo").val(data.CPNo);
                    $("#txtCPVer").val(data.CPVer);
                    //$("#txtCPTxt").val(data.CPTxt);
                    //$("#txtWKind").val(data.WKind);
                    $("#txtRemark").val(data.Remark);


                    $("#txtCPNo").attr({ "disabled": "disabled" });
                    $("#txtCPVer").attr({ "disabled": "disabled" });

                    $("#div_warning").html("");
                    $("#div_warning").hide();

                    document.getElementById('light').style.display = 'block';
                    document.getElementById('fade').style.display = 'block';

                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }
                else if (layEvent === 'upgrade')
                {
                    $('#L_ShowClearPlanHead').html("升级清场计划信息");
                    $('#txtAEFlag').val("10-2");

                    $("#txtCPNo").val(data.CPNo);
                    $("#txtCPVer").val(data.CPVer);
                    //$("#txtCPTxt").val(data.CPTxt);
                    //$("#txtWKind").val(data.WKind);
                    $("#txtRemark").val(data.Remark);

                    $("#txtCPVerOld").val(data.CPVer);

                    $("#txtCPNo").attr({ "disabled": "disabled" });                   
                    $("#txtCPVer").removeAttr("disabled");

                    $("#div_warning").html("");
                    $("#div_warning").hide();

                    document.getElementById('light').style.display = 'block';
                    document.getElementById('fade').style.display = 'block';

                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }
                else if (layEvent === 'detail')//显示明细页面
                {
                    $('#L_ShowClearPlanDetail').html("清场计划信息详情");
                    //$('#txtAEFlag').val("11");

                    $("#txtCPDNo").val(data.CPNo);
                    $("#txtCPDVer").val(data.CPVer);
                    //$("#txtCPTxt").val(data.CPTxt);
                    //$("#txtWKind").val(data.WKind);
                    $("#txtDRemark").val(data.Remark);


                    $("#txtCPNo").attr({ "disabled": "disabled" });

                    $("#div_warningDetail").html("");
                    $("#div_warningDetail").hide();
                    //弹窗显示清场计划明细页
                    ShowClearPlanDetail(data.CPNo, data.CPVer);

                    document.getElementById('div_ClearPlanDetail').style.display = 'block';
                    document.getElementById('fade').style.display = 'block';

                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }


            });


            //  查询表头 --
            $('#ClearPlanBut_open').click(function () {

                var sCPNo = $("#txtSCPNo").val();  //
                var sCPVer = encodeURI($("#txtSCPVer").val());  //


                var Data = '';
                var Params = { No: sCPNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: sCPVer, B: "", C: "", D: "", E: "" };
                var Data = JSON.stringify(Params);

                table.reload('ClearPlanHeadlistID', {
                    method: 'post',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=13&Data=' + Data,
                    where: {
                        'No': sCPNo,
                        'name': sCPVer
                    }, page: {
                        curr: 1
                    }
                });
            });



        });




        function openDialog(n) { 
            if (n == 1) { // 新增表头弹窗层显示
                $('#L_ShowClearPlanHead').html("新增清场计划信息");
                $('#txtAEFlag').val("10");


                $("#txtCPNo").val("");
                $("#txtCPVer").val("1.0");
                $("#txtRemark").val("");

                $("#div_warning").html("");
                $("#div_warning").hide();

                $("#txtCPVer").removeAttr("disabled");

                document.getElementById('light').style.display = 'block';
                document.getElementById('fade').style.display = 'block';
            }
            else if (n == 2)//新增明细弹窗层显示
            {
                $('#L_ShowClearPlanDetailAdd').html("新增清场计划明细");
                $('#txtAEFlag').val("10-1");

                //$("#txtCPDNoADD").val(data.CPNo); // 序号
                //$("#txtCPDVerADD").val(data.CPVer);//描述
                //$("#txtDRemarkADD").val(data.Remark);//字段类型

                $("#txtCPDNoADD").val($("#txtCPDNo").val()); // 清场计划编号
                $("#txtCPDVerADD").val($("#txtCPDVer").val());//清场计划版本
                $("#txtDRemarkADD").val($("#txtDRemark").val());//备注

                $("#txtItemNo").val(""); // 序号
                $("#txtCPTxt").val("");//描述
                $("#txtType").val("");//字段类型

                $("#div_warningDetailAdd").html("");
                $("#div_warningDetailAdd").hide();

                document.getElementById('div_ClearPlanDetailAdd').style.display = 'block';
                document.getElementById('ClearPlanDetail_fade').style.display = 'block';
            }
        }

        function closeDialog(s) {
            if (s == 1)
            {
                document.getElementById('div_ClearPlanDetailAdd').style.display = 'none';
                document.getElementById('ClearPlanDetail_fade').style.display = 'none';
            }
            else
            {
                document.getElementById('light').style.display = 'none';
                document.getElementById('div_ClearPlanDetail').style.display = 'none';
                document.getElementById('div_ClearPlanDetailAdd').style.display = 'none';
                document.getElementById('fade').style.display = 'none';
            }
        }


        /// 显示清场计划明细项信息
        ///daiwanshan
        function ShowClearPlanDetail(sCPNo, sCPVer) {

            var sCPNo = $("#txtCPDNo").val();  //
            var sCPVer = encodeURI($("#txtCPDVer").val());  //


            var Data = '';
            var Params = { No: sCPNo, CPVer: sCPVer, Item: "", Status: "", BDate: "", EDate: "", A: sCPVer, B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#ClearPlanDetailList',
                    id: 'ClearPlanDetailID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=13-1&Data=' + Data,
                    height: '260',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers' },
                        { field: 'SNO', title: '序号', width: 40 },
                        { field: 'ItemNo', title: '内容编号', width: 100 },
                        { field: 'CPTxt', title: '清场内容', width: 230 },
                        { field: 'Type', title: '数据类型', width: 100 },                        
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate2', title: '录入时间', width: 150 },
                        { field: 'op', title: '操作', width: 230, toolbar: '#barDemo_Detail', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(ClearPlanDetailList)', function (obj)
                { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值


                    if (layEvent == 'detailEdit')
                    {
                        $('#txtAEFlag').val("11-1");
                        $("#L_ShowClearPlanDetailAdd").html("修改清场计划明细");

                        $("#txtCPDNoADD").val($("#txtCPDNo").val()); // 清场计划编号
                        $("#txtCPDVerADD").val($("#txtCPDVer").val());//清场计划版本
                        $("#txtDRemarkADD").val($("#txtDRemark").val());//备注

                        $("#txtItemNo").val(data.ItemNo); // 序号
                        $("#txtCPTxt").val(data.CPTxt);//清场内容
                        $("#txtType").val(data.Type);//字段类型

                        $("#div_warningDetailAdd").html("");

                        //$("#txtWOFileNo").attr({ "disabled": "disabled" });
                        //$("#txtWOFileVer").attr({ "disabled": "disabled" });

                        var scrollTop = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
                        document.getElementById('div_ClearPlanDetailAdd').style.top = (scrollTop + 30) + "px";

                        document.getElementById('div_ClearPlanDetailAdd').style.display = 'block';
                        document.getElementById('ClearPlanDetail_fade').style.display = 'block';
                        
                    }
                    else if (layEvent == 'detailDel')
                    {

                        layer.confirm('真的删除清场计划项么', function (index) {
                            //向服务端发送删除指令 A: sPVer, B: sPath, C: sFile, D: sFNo, E: sFVer, F: sFName,

                            //var sWONo = data.CPNo;
                            //var sNo = data.CPNo;
                            //var sPVer = data.CPVer;
                            var sNo = $("#txtCPDNo").val();
                            var sPVer = $("#txtCPDVer").val();
                            var sItemNo = data.ItemNo;
                            //var sFVer = data.FileVer;
                            var sFlag = "12-1";

                            var Data = '';
                            var Params = { No: sNo, Name: "", Item: sItemNo, MNo: "", MName: "", A: sPVer, B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: sFlag };
                            var Data = JSON.stringify(Params);

                            $.ajax({
                                type: "POST",
                                url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag ,
                                data: { Data: Data },
                                success: function (data) {
                                    var parsedJson = jQuery.parseJSON(data);

                                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                        layer.msg('删除成功！');
                                        //$('#Fin_SearchOpen').click();
                                        obj.del(); //删除对应行（tr）的DOM结构，并更新缓存
                                        layer.close(index);
                                    }
                                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                                        layer.msg('该文件已使用，不能操作！');
                                        // $('#Fin_SearchOpen').click();
                                    } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                                        layer.msg('该工序版本已禁用，不能操作！');
                                        // $('#Fin_SearchOpen').click();
                                    }
                                    else {
                                        layer.msg('删除失败，请重试！');
                                        // $('#Fin_SearchOpen').click();
                                    }
                                },
                                error: function (data) {
                                    layer.msg('删除失败2，请重试！');
                                    // $('#Fin_SearchOpen').click();
                                }
                            });

                        }); // 删除
                    }
                    else if (layEvent == 'detailUpm')// 上↑移
                    {  
                        var sNo = $("#txtCPDNo").val();
                        var sPVer = $("#txtCPDVer").val();
                        var sItemNo = data.ItemNo;
                        var sCPTxt = data.CPTxt;
                        var sSNO = data.SNO;

                        var sFlag = "11-2";
                        var Data = '';
                        var Params = { No: sNo, Name: sCPTxt, Item: sItemNo, MNo: sSNO, MName: "", A: sPVer, B: "", C: "", D: "", E: "", F: "up", G: "", H: "", I: "", J: "", K: "", L: sSNO, Remark: "", Flag: sFlag };
                        var Data = JSON.stringify(Params);
                        $.ajax({
                            type: "POST",
                            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
                            data: { Data: Data },
                            success: function (data) {
                                var parsedJson = jQuery.parseJSON(data);

                                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                    layer.msg('移动成功！');
                                    ShowClearPlanDetail(sNo, sPVer);  // 重新查询
                                    

                                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                                    layer.msg('该清场计划项已禁用，不能操作！');
                                }
                                else {
                                    layer.msg('移动失败，请重试！')
                                }
                            },
                            error: function (data) {
                                layer.msg('移动失败2，请重试！')
                            }
                        });

                    }
                    else if (layEvent == 'detailDown')// 下↓移
                    {  
                        var sNo = $("#txtCPDNo").val();
                        var sPVer = $("#txtCPDVer").val();
                        var sItemNo = data.ItemNo;
                        var sCPTxt = data.CPTxt;
                        var sSNO = data.SNO;

                        var sFlag = "11-2";
                        var Data = '';
                        var Params = { No: sNo, Name: sCPTxt, Item: sItemNo, MNo: sSNO, MName: "", A: sPVer, B: "", C: "", D: "", E: "", F: "down", G: "", H: "", I: "", J: "", K: "", L: sSNO, Remark: "", Flag: sFlag };
                        var Data = JSON.stringify(Params);
                        $.ajax({
                            type: "POST",
                            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
                            data: { Data: Data },
                            success: function (data) {
                                var parsedJson = jQuery.parseJSON(data);

                                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success')
                                {
                                    layer.msg('移动成功！');

                                    ShowClearPlanDetail(sNo, sPVer);  // 重新查询

                                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                                    layer.msg('该清场计划项已禁用，不能操作！');
                                }
                                else {
                                    layer.msg('移动失败，请重试！')
                                }
                            },
                            error: function (data) {
                                layer.msg('移动失败2，请重试！')
                            }
                        });

                    } 



                });



            });  // layui.use('table', function () {


        }

    </script>


    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

        })



    </script>

    <style type="text/css">

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: white;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=60);
        }
    </style>

</head>
<body>
    <div class="div_find">

        <p>
            <label class="find_labela">清场计划编号：</label> <input type="text" id="txtSCPNo" class="find_input" />
            <label class="find_labela">版本：</label><input type="text" id="txtSCPVer" class="find_input" />
            <!--<label class="find_labela">描述：</label><input type="text" id="txtSCPTxt" class="find_input" />-->
            <input type="button" value="搜索" class="find_but" id="ClearPlanBut_open">
        </p>
    </div>
    <div style="text-align:right">
        <i class="add2_i"></i><a href="JavaScript:void(0)" onclick="openDialog(1)" style="color: Blue">添加</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <!-- <i class="down_i" ></i><a href="JavaScript:void(0)" onclick="openDialog(1)" class="add_a">导出</a> -->
    </div>

    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="ClearPlanHeadlist" lay-filter="ClearPlanHeadlist"></table>

        <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
            <a class="layui-btn layui-btn-xs" lay-event="upgrade">升版</a>
            <!--<a class="layui-btn layui-btn-xs" lay-event="edit">修改</a>-->
            <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
        </script>

    </div>
    <!--清场计划表头新增和修改增弹层-->
    <div id="light" style="display: none;position: absolute;top: 1%;left: 5%;right: 5%;width: 90%;height: 420px;border: 2px solid #21b6b4; background-color: white;z-index: 1002;overflow: auto;">
        <div style="background-color:#26d0a1; height:40px;">
            <label id="L_ShowClearPlanHead" style=" padding:5px;font-size: 14px; color:White; ">修改清场计划</label>
            <label onclick="closeDialog(2)" style="float:right; margin:5px 5px 0 0; cursor:pointer;">X</label>
        </div>
        <div id="div_warning" role="alert" style="text-align: center; display:none;color: Red ">
            <strong id="divsuccess" style="color: Red"></strong>
        </div>
        <table cellspacing="0" cellpadding="0" border='0' style="width:99%; ">

            <tr style=" height:40px;">
                <td style=" width:100px; text-align:right;">
                    清场计划编号:
                </td>
                <td colspan="4">
                    <input type="text" class="form-control" id="txtCPNo" name="txtCPNo" readonly=readonly placeholder="系统自动产生" style="height:30px;" />
                </td>
            </tr>

            <tr style=" height:40px;">
                <td style=" width:100px; text-align:right;">
                    版本:
                </td>
                <td colspan="4">
                    <input type="text" class="form-control" id="txtCPVer" name="txtCPVer" style="height:30px;" />
                </td>
            </tr>

            <tr>
                <td style=" width:180px; text-align:right;">
                    备注:
                </td>
                <td colspan="4">
                    <textarea class="form-control" id="txtRemark" name="txtRemark" style="height:100px;"> </textarea>
                </td>
            </tr>
            <tr style=" height:40px;">
                <td style=" width:100px; text-align:right;">
                    
                </td>
                <td colspan="4">
                    <input type="text" class="form-control" id="txtCPVerOld" name="txtCPVerOld" style="height: 30px; display: none; " />
                </td>
            </tr>
        </table>


        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <br />
        <div align="center">
            <input type='button' id="ClearPlanHeadSave_Btn" value='保存' style="width: 50px; height: 30px;" />
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <input type='button' id="ClearPlanHeadSaveClose" value='关闭' onclick='closeDialog(2)' style="width: 50px; height: 30px;" />
        </div>


    </div>


    <!--清场计划明细弹窗-->
    <div id="div_ClearPlanDetail" style="display: none;position: absolute;top: 1%;left: 2%;right: 2%;width: 96%;height: 650px;border: 2px solid #21b6b4; background-color: white;z-index: 1002;overflow: auto;">
        <div style="background-color:#26d0a1; height:40px;">
            <label id="L_ShowClearPlanDetail" style=" padding:5px;font-size: 14px; color:White; ">清场计划详情</label>
            <label onclick="closeDialog(2)" style="float:right; margin:5px 5px 0 0; cursor:pointer;">X</label>
        </div>
        <div id="div_warningDetail" role="alert" style="text-align: center; display:none;color: Red ">
            <strong id="divsuccessDetail" style="color: Red"></strong>
        </div>
        <div style=" font-weight: bold; padding: 5px; height: 35px; line-height: 25px; margin-top: 5px; border: 0px; border-bottom: solid 1px #ccc;">
            清场计划
        </div>
        <table cellspacing="0" cellpadding="0" border='0' style="width:99%; ">

            <tr style=" height:40px;">
                <td style=" width:100px; text-align:right;">
                    清场计划编号:
                </td>
                <td colspan="4">
                    <input type="text" class="form-control" id="txtCPDNo" name="txtCPDNo" readonly=readonly placeholder="系统自动产生" style="height:30px;" />
                </td>
            </tr>

            <tr style=" height:40px;">
                <td style=" width:100px; text-align:right;">
                    版本:
                </td>
                <td colspan="4">
                    <input type="text" class="form-control" id="txtCPDVer" name="txtCPDDVer" readonly=readonly style="height:30px;" />
                </td>
            </tr>

            <tr>
                <td style=" width:180px; text-align:right;">
                    备注:
                </td>
                <td colspan="4">
                    <textarea class="form-control" id="txtDRemark" name="txtDRemark" readonly=readonly style="height:100px;"> </textarea>
                </td>
            </tr>
        </table>
        <br />

        <div style=" font-weight: bold; padding: 5px; height: 35px; line-height: 25px; margin-top: 5px; border: 0px; border-bottom: solid 1px #ccc;">
            清场计划明细
            <div style="float: right;">
                <input type="button" value="添加" id="ClearPlanDetailAdd_Btn" style="width:65px;background-color: #56dcaf;color: white;border: 0px;border-radius: 5px;" onclick='openDialog(2)' />
                <!--<input type="button" value="刷新" id="TestItem_SearchOpen" style="width: 65px;background-color: #56dcaf;color: white;border: 0px;border-radius: 5px;" />-->
            </div>
        </div>
        <table cellspacing="0" cellpadding="0" border='0' style="width:99%; ">
            <tr>
                <td colspan="4">
                </td>
            </tr>
            <tr style=" height:20px;">
                <td style=" width: 180px; height: 20px; text-align: right;">
                </td>
                <td colspan="3">
                    <!--<div style="text-align:right">
                    <i class="add2_i"></i><a href="JavaScript:void(0)" onclick="openDialog(2)" style="color: Blue">添加</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-->
                    <!-- <i class="down_i" ></i><a href="JavaScript:void(0)" onclick="openDialog(1)" class="add_a">导出</a> -->
                    <!--</div>-->
                </td>
            </tr>
            <tr style=" height:40px;">
                <td colspan="4">

                    <div class="wangid_conbox">
                        <!-- 下面写内容   -->
                        <table class="layui-hide" id="ClearPlanDetailList" lay-filter="ClearPlanDetailList"></table>

                        <script type="text/html" id="barDemo_Detail">
                            <a class="layui-btn layui-btn-xs" lay-event="detailEdit">修改</a>
                            <a class="layui-btn layui-btn-xs" lay-event="detailUpm">上↑移</a>
                            <a class="layui-btn layui-btn-xs" lay-event="detailDown">下↓移</a>
                            <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="detailDel">删除</a>
                        </script>

                    </div>
                </td>
            </tr>

        </table>

        <div id="ClearPlanDetail_fade" class="black_overlay">
        </div>

    </div>

    <!--清场计划明细项修改和新增弹窗-->
    <div id="div_ClearPlanDetailAdd" style="display: none;position: absolute;top: 1%;left: 2%;right: 2%;width: 96%;height: 550px;border: 2px solid #21b6b4; background-color: white;z-index: 1500;overflow: auto;">
        <div style="background-color:#26d0a1; height:40px;">
            <label id="L_ShowClearPlanDetailAdd" style=" padding:5px;font-size: 14px; color:White; ">清场计划新增</label>
            <label onclick="closeDialog(1)" style="float:right; margin:5px 5px 0 0; cursor:pointer;">X</label>
        </div>
        <div id="div_warningDetailAdd" role="alert" style="text-align: center; display:none;color: Red ">
            <strong id="divsuccessDatailAdd" style="color: Red"></strong>
        </div>
        <table cellspacing="0" cellpadding="0" border='0' style="width:99%; ">

            <tr style=" height:40px;">
                <td style=" width:100px; text-align:right;">
                    清场计划编号:
                </td>
                <td colspan="4">
                    <input type="text" class="form-control" id="txtCPDNoADD" name="txtCPDNoADD" readonly=readonly placeholder="系统自动产生" style="height:30px;" />
                </td>
            </tr>

            <tr style=" height:40px;">
                <td style=" width:100px; text-align:right;">
                    版本:
                </td>
                <td colspan="4">
                    <input type="text" class="form-control" id="txtCPDVerADD" name="txtCPDVerADD" readonly=readonly style="height:30px;" />
                </td>
            </tr>

            <tr>
                <td style=" width:180px; text-align:right;">
                    备注:
                </td>
                <td colspan="4">
                    <textarea class="form-control" id="txtDRemarkADD" name="txtDRemarkADD" readonly=readonly style="height:100px;"> </textarea>
                </td>
            </tr>

            <tr style=" height:40px;">
                <td style=" width:100px; text-align:right;">
                    序号:
                </td>
                <td colspan="4">
                    <input type="text" class="form-control" id="txtItemNo" name="txtItemNo" readonly=readonly placeholder="系统自动产生" style="height:30px;" />
                </td>
            </tr>

            <tr style=" height:40px;">
                <td style=" width:100px; text-align:right;">
                    清场内容:
                </td>
                <td colspan="4">
                    <input type="text" class="form-control" id="txtCPTxt" name="txtCPTxt" style="height:30px;" />
                </td>
            </tr>

            <tr style=" height:40px;">
                <td style=" width:100px; text-align:right;">
                    数据类型:
                </td>
                <!--<td colspan="4">
                    <input type="text" class="form-control" id="txtType" name="txtType" readonly=readonly style="height:30px;" />
                </td>-->
                <td colspan="4">
                    <select class="form-control" id="txtType">
                        <option></option>
                        <option>布尔型</option>
                        <option>文本型</option>
                        <option>数值型</option>
                        <option>日期型</option>
                    </select>
                </td>
            </tr>

        </table>
        <div align="center">
            <input type='button' id="ClearPlanDetailSave_Btn" value='保存' style="width: 50px; height: 30px;" />
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <input type='button' id="ClearPlanHeadDetailSaveClose" value='关闭' onclick='closeDialog(1)' style="width: 50px; height: 30px;" />
        </div>
    </div>

    <div id="fade" class="black_overlay">
    </div>

</body>
</html>