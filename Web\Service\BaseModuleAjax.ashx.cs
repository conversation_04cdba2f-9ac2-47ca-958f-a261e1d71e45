﻿using System;
using System.Collections;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml.Linq;
using BLL;
using Newtonsoft.Json;
using System.Web.SessionState;
using Common;
using System.Collections.Generic;
using WXCommon;
using System.IO;
using System.Text.RegularExpressions;
using System.Text;

namespace Web.Service
{
    /// <summary>
    /// $codebehindclassname$ 的摘要说明
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class BaseModuleAjax : IHttpHandler, IRequiresSessionState
    {

        string sAMFlag = string.Empty;
        string IP = string.Empty;

        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "text/plain";
            string Result = string.Empty;
            string Operate = context.Request.Params["OP"];
            string DataParams = context.Request.Params["Data"];
            string sItem = context.Request.Params["Item"];
            sAMFlag = context.Request.Params["CFlag"];
            string sKind = context.Request.Params["CKind"];
            string sStatus = context.Request.Params["CStatus"];  // 状态
            string sSupplier = context.Request.Params["CSupplier"];
            string sCNo = context.Request.Params["CNO"];  // 接收所有编号  
            string sMNo = context.Request.Params["CMNO"];  // 物料编码 
            string sMName = context.Request.Params["sMName"];
            string sBNo = context.Request.Params["sBNo"];
            IP = context.Request.ServerVariables.Get("Remote_Addr").ToString().Trim();  //获取IP 地址
            int slimit = 0;
            int spage = 0;

            switch (Operate)
            {

                case "ApplyLogin":  // 登录 -- 自动登录-企业号
                    Result = ApplyLogin(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "JustRight": //判断是否有操作权限
                    Result = JustOperateRight(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "SDLogin":  // 手动登录  
                    Result = SDLogin(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetUserInfo": //获取用户基本信息
                    Result = ApplyGetUserInfo(DataParams);
                    break;

                case "GetUserInfoByNo": //根据用户编号获取用户信息
                    GetUserInfoByNo(DataParams);
                    break;

                case "GetUserRnList": //获取注册信息（测试使用）
                    Result = GetUserRnList(DataParams);
                    break;

                case "RelationList": // 注册信息
                    Result = GetUserRelationList(DataParams, sAMFlag);
                    break;

                case "GetSysKind": //查找系统类别
                    Result = GetSysKind(sKind, sAMFlag);
                    break;

                case "GetSysKindList": //查找系统类别项次  
                    Result = GetSysKind(sKind, sAMFlag);
                    break;

                case "GetSysTypeChoogeList": //给系统类别下拉选择
                    Result = GetSysTypeChoogeList(sKind, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetDeptInfoList": //给部门下来信息
                    Result = GetDeptInfoList(sKind, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "AddSysKind": //插入或更新系统类别
                    Result = InsertUpdateSysKind(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "DelSysKind": //删除大类 
                    Result = DelSysKind(DataParams);  // 1 删除大类  2 删除小类
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "DelSysKindList": //删除 小类
                    Result = DelSysKind(DataParams);  // 1 删除大类  2 删除小类  
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetMenu": //获取菜单信息 
                    Result = GetMenu(sAMFlag);
                    break;

                case "GetModule": //获取系统功能模块  
                    Result = GetModule(sKind, sAMFlag);
                    break;

                case "DelModel": //删除模块或菜单 
                    Result = DelModel(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetRoleInfo": //获取角色信息  
                    Result = GetRoleInfo(sCNo, sAMFlag);
                    break;

                case "AddEditUser": //新增修改用户  
                    Result = AddEditUser(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "DelUserInfo": //删除用户信息
                    Result = DelUserInfo(sCNo, sMName);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetMenuForRole": //获取菜单信息     
                    Result = GetMenuForRole(sAMFlag);
                    break;

                case "InsertRoleModule": //插入角色对应的模块
                    Result = InsertRoleModule(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetRoleHaveMenu": //获取该角色具有哪些材质菜单（权限）
                    Result = GetRoleHaveMenu(sAMFlag, sCNo);
                    break;

                case "AddRole": //新增角色  
                    Result = AddRole(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "DelRoleInfo": //删除角色
                    Result = DelRoleInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetMaterType": //获取物料分类
                    Result = GetMaterType(sKind, sAMFlag);
                    break;

                case "GetMaterTypeList": //获取物料分下拉列表    
                    Result = GetMaterTypeList(sKind, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "AddEditMaterType": //插入或更新物料分类
                    Result = AddEditMaterType(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "DelMaterType": //删除物料分类     
                    Result = DelMaterType(DataParams);  // 
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetMaterInfo": //获取物料信息
                    slimit = int.Parse(context.Request.Params["limit"]);
                    spage = int.Parse(context.Request.Params["page"]);
                    GetMaterInfo(DataParams, slimit, spage, sAMFlag);
                    break;

                case "GetMaterInfoByNo": //根据物料编码返回物料基本信息
                    GetMaterInfoByNo(DataParams);
                    break;

                case "AddEditMater": //插入或更新物料信息
                    Result = AddEditMater(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "GetCustMaterInfo": //获取客供物料  
                    Result = GetCustMaterInfo(DataParams);
                    break;

                case "AddEditCMater": //插入或更新客供物料  
                    Result = AddEditCMater(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "DelCustMater": //删除客供物料   
                    Result = DelCustMater(DataParams);  // 
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "AddEditSupplier": //插入或更供应商信息   
                    Result = AddEditSupplier(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetSupplierNo": //获取供应商编码
                    Result = GetSupplierNo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetSupplierInfoByNo": //按供应商编码获供应商信息
                    GetSupplierInfoByNo(DataParams);
                    break;

                case "GetSupplierInfo": //获取供应商信息
                    slimit = int.Parse(context.Request.Params["limit"]);
                    spage = int.Parse(context.Request.Params["page"]);
                    GetSupplierInfo(DataParams, slimit, spage, sAMFlag);
                    break;

                case "GetBOMInfo": //获取BOM信息构造树形
                    slimit = 500;//int.Parse(context.Request.Params["limit"]);
                    spage = 1;//int.Parse(context.Request.Params["page"]);
                    GetBOMInfo(DataParams, slimit, spage, sAMFlag);
                    break;

                case "GetWorkCenterInfo": //获取工作中心信息构造树形
                    slimit = 300;//int.Parse(context.Request.Params["limit"]);
                    spage = 1;//int.Parse(context.Request.Params["page"]);
                    GetWorkCenterInfo(DataParams, slimit, spage, sAMFlag);
                    break;

                case "AddEditBOM": //插入或更BOM信息   
                    Result = AddEditBOM(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetCust": //获取客户信息
                    slimit = int.Parse(context.Request.Params["limit"]);
                    spage = int.Parse(context.Request.Params["page"]);
                    GetCustInfo(DataParams, slimit, spage, sAMFlag);
                    break;

                case "AddEditCust": //插入或更客户信息   
                    Result = AddEditCust(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetCustNo": //获取客户编码 
                    Result = GetCustNo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetCustInfoByNo": //根据客户编码返回客户信息
                    GetCustInfoByNo(DataParams, sCNo, sAMFlag);
                    break;

                case "GetUserExcel":
                    Result = UserInfoToExcel(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetMCodetInfo": //获取厂商识别码
                    Result = GetMCodetInfo(DataParams);
                    break;

                case "OPMCodetInfo": //插入或更新 删除 厂商识别码
                    Result = OPMCodetInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "GetSerielMater": //获取发号物料信息
                    slimit = int.Parse(context.Request.Params["limit"]);
                    spage = int.Parse(context.Request.Params["page"]);
                    GetSerielMater(DataParams, slimit, spage, sAMFlag);
                    break;

                case "OPSerielMater": //插入或更新 删除 序列号物料
                    Result = OPSerielMater(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetViewSerialNo": //获取预览的序列号
                    Result = GetViewSerialNo(DataParams, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "OPSerielInfo": //发放序列号  
                    Result = OPSerielInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetSerielInfo": //获取发号信息
                    slimit = int.Parse(context.Request.Params["limit"]);
                    spage = int.Parse(context.Request.Params["page"]);
                    GetSerielInfo(DataParams, sCNo, slimit, spage, sAMFlag);
                    break;


                case "GetProductLabel": //获取产品编码对应的标贴
                    slimit = int.Parse(context.Request.Params["limit"]);
                    spage = int.Parse(context.Request.Params["page"]);
                    GetProductLabel(DataParams, sMNo, slimit, spage, sAMFlag);
                    break;

                case "OPProductLabel": //插入或更新 删除 产品编码对应的标贴
                    Result = OPProductLabel(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetLabelByMNo": //通过产品编码获取其对应的标贴
                    Result = GetLabelByMNo(DataParams, sMNo, sAMFlag);
                    break;


                case "OPCompanyInfo": //插入或更新 删除 公司基本信息
                    Result = OPCompanyInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "GetCompanyInfo": //获取公司基本信息
                    slimit = int.Parse(context.Request.Params["limit"]);
                    spage = int.Parse(context.Request.Params["page"]);
                    GetCompanyInfo(DataParams, slimit, spage, sAMFlag);
                    break;


                case "GetCompanyList": //获取用户表的下拉列表    
                    Result = GetCompanyList(sKind, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "GetModuleForComp": //获取模块下有哪些公司
                    Result = GetModuleForComp(sKind, sItem, sAMFlag);
                    break;

                case "GetLabelTempleteList": //获取标贴模板列表
                    Result = GetLabelTempleteList(sCNo, sAMFlag);
                    break;


                case "LabelTemplateToExcel":  // 标贴模板信息导出EXCEL
                    Result = LabelTemplateToExcel(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "GetMCodeTypeList": //给厂商识别码类别下拉
                    Result = GetMCodeTypeList(sKind, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "GetSamplingPlanList": //获取抽样方案   
                    Result = GetSamplingPlanList(sKind, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetBatchSerial"://获取物料批序号
                    slimit = Convert.ToInt32(context.Request.Params["limit"]);
                    spage = Convert.ToInt32(context.Request.Params["page"]);
                    GetBatchSerial(slimit, spage, sAMFlag, DataParams);
                    break;

                case "OPBatchSerial": //操作物料批序号
                    Result = OPBatchSerial(sAMFlag, DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "BatchSerialToExcel"://物料批序号导入
                    BatchSerialToExcel(context);
                    break;

                case "GetLogInfoList":
                    slimit = Convert.ToInt32(context.Request.Params["limit"]);
                    spage = Convert.ToInt32(context.Request.Params["page"]);
                    GetBaseInfoList(slimit, spage, sAMFlag, DataParams);
                    break;
                case "GetSoftwareVersionInfoList":
                    slimit = Convert.ToInt32(context.Request.Params["limit"]);
                    spage = Convert.ToInt32(context.Request.Params["page"]);
                    GetBaseInfoList(slimit, spage, sAMFlag, DataParams);
                    break;
                case "OPSoftwareVersionInfoList":
                    OPBaseInfoList(DataParams);
                    break;












            }
        }


        #region 判断用户是否有操作权限
        public string JustOperateRight(string Params)
        {
            string Result = string.Empty;
            string Message = "YES";
            string sInMan = string.Empty;

            HttpContext.Current.Session["AMFlag"] = sAMFlag;
            //HttpContext.Current.Session["CCorpID"] = sCCorpID; //"wx9f1cde5f70b71497"; 

            //--@_@--   测试使用，发布到测试系统需要屏蔽 -- 不需要调用微信接口（调用微信接口需要用微信调用的，）
            //HttpContext.Current.Session["LoginName"] = "wudong";  // YD001      YD002     wd      wudong001
            //HttpContext.Current.Session["FullName"] = "吴东";  //   业代一    业代E二      WD_冰冻   吴东
            //HttpContext.Current.Session["CompanyNo"] = "C0001";


            if (HttpContext.Current.Session["LoginName"] != null)  // HttpContext.Current.Session["LoginName"] != null
            {
                sInMan = HttpContext.Current.Session["LoginName"].ToString();

            }
            else
            {
                Message = "loginError";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }


            //if (sAMFlag == "1") // 销售代表陈列执行
            //{
            //    Message = ShopSignsApplyBll.JustOperateRight(sInMan, "CLZX"); // CLZX 这种编号是功能菜单的编号，固定的。
            //}
            //else if (sAMFlag == "2") // 主管陈列检查
            //{
            //    Message = ShopSignsApplyBll.JustOperateRight(sInMan, "CLJC");
            //}
            //else if (sAMFlag == "3") // 店招办事处审批（列表） 
            //{
            //    Message = ShopSignsApplyBll.JustOperateRight(sInMan, "DZBSCSP");
            //}

            Result = JsonConvert.SerializeObject(new { Msg = Message, Man = sInMan });

            return Result;
        }
        #endregion


        #region 登录逻辑
        public string ApplyLogin(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string ssCode = string.Empty;
            string sUserID = string.Empty;
            string sSQL = string.Empty;
            string sNo = string.Empty;
            string sFlag = string.Empty;
            string sSQLs = string.Empty;
            string sUpdate = string.Empty;

            string HttpUrl = System.Configuration.ConfigurationManager.AppSettings["HttpUrl"];

            var AnonymousUser = new { User = String.Empty, Pwd = String.Empty, Code = String.Empty, State = String.Empty, sLFlag = String.Empty, Add = String.Empty };
            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            HttpContext.Current.Session.Timeout = 1440;

            HttpContext.Current.Session["LoginName"] = null;
            HttpContext.Current.Session["FullName"] = null;
            HttpContext.Current.Session["CompanyNo"] = null;
            HttpContext.Current.Session["WeiXinNo"] = null;
            HttpContext.Current.Session["QYNo"] = null;

            DataTable Dt = WeiXinInfo.GetCorpSecret("", "PPXX", "");  // 这里会取得多个记录，所以“Secret” 是随机的，如果无法自动登录（无法自动获得USERID），
            string CorpID = Dt.Rows[0]["CorpID"].ToString();          // 那就是这里有问题。这种情况发生，那么在公众号不能设置多个组。
            string Secret = Dt.Rows[0]["Secret"].ToString();
            string toparty = Dt.Rows[0]["toparty"].ToString();
            string sGZH = Dt.Rows[0]["GZHKind"].ToString();
            string sURI = Dt.Rows[0]["redirect_uri"].ToString();

            WXCommon.TokenHelper token = new TokenHelper();
            string stokens = token.GetTokenString("", CorpID, Secret, toparty, sGZH);
            string iToken = WXCommon.TokenHelper.GetJsonValue(stokens, "access_token");

            ssCode = Item.Code;

            if (!string.IsNullOrEmpty(ssCode))
            {
                sUserID = token.GetCode(iToken, ssCode);


                if (Item.Add == "70") // 自动加载的
                {
                    if (sUserID == "")
                    {
                        sUserID = HttpContext.Current.Session["GETQYNo"].ToString();
                    }

                    sSQLs = " WeiXinNo = '" + sUserID + "' ";
                    sNo = Item.Code + "，" + Item.State + "，" + iToken + "#0170 " + sUserID + " BZ" + Item.sLFlag;
                }
                else  // 点击确定按钮的，需要更新微信号
                {
                    sUserID = HttpContext.Current.Session["GETQYNo"].ToString();
                    sSQLs = " LoginName = '" + Item.User + "' ";
                    sNo = Item.Code + "，" + Item.State + "，" + iToken + "#0180 " + Item.User + " BZ" + Item.sLFlag;

                    sUpdate = " update T_SPUser set WeiXinNo = '" + sUserID + "' where LoginName =  '" + Item.User + "' ";
                    Common.DBHelper.ExecuteCommand(sUpdate);
                }

                //sSQL = " insert into T_Temp(MaterNo) values('" + sNo + "')";
                //Common.DBHelper.ExecuteCommand(sSQL);


                /*登录逻辑*/
                //DataTable dt = LoginBll.GetList(" LoginName='" + Item.User.Trim() + "' And Pwd='" + Item.Pwd.Trim() + "' ");
                DataTable dt = LoginBll.GetList(sSQLs); // 使用微信获取的USERID自动登录
                if (dt.Rows.Count > 0)
                {
                    Message = "Success";

                    sFlag = HttpContext.Current.Session["AMFlag"].ToString();

                    HttpContext.Current.Session["LoginName"] = dt.Rows[0]["LoginName"].ToString();
                    HttpContext.Current.Session["FullName"] = dt.Rows[0]["FullName"].ToString();
                    HttpContext.Current.Session["CompanyNo"] = dt.Rows[0]["CompanyNo"].ToString();
                    HttpContext.Current.Session["WeiXinNo"] = dt.Rows[0]["WeiXinNo"].ToString();
                    HttpContext.Current.Session["QYNo"] = dt.Rows[0]["QYNo"].ToString();
                    // HttpContext.Current.Session.Timeout = 20;
                    //MessageBox.Redirect(this, "main.html");

                    sNo = "登录用户：" + dt.Rows[0]["FullName"].ToString() + "，" + "#02 " + sUserID + "BZ:" + sFlag;
                    sSQL = " insert into T_Temp(MaterNo) values('" + sNo + "')";
                    Common.DBHelper.ExecuteCommand(sSQL);
                }
                else
                {
                    HttpContext.Current.Session["GETQYNo"] = sUserID;
                    //sNo = "#03 " + sUserID;
                    //sSQL = " insert into T_Temp(MaterNo) values('" + sNo + "')";
                    //Common.DBHelper.ExecuteCommand(sSQL);

                    Message = "NoUser";
                }
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, CorpID = CorpID, URI = sURI, tokens = iToken, UserID = sUserID, Flag = sFlag, Update = sUpdate });
            return Result;
        }
        #endregion


        #region 手动登录逻辑
        public string SDLogin(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sFlag = string.Empty;
            string sNo = string.Empty;
            string sSQL = string.Empty;
            string sPw = string.Empty;


            HttpContext.Current.Session["LoginName"] = null;
            HttpContext.Current.Session["FullName"] = null;
            HttpContext.Current.Session["CompanyNo"] = null;
            HttpContext.Current.Session["WeiXinNo"] = null;
            HttpContext.Current.Session["QYNo"] = null;


            var AnonymousUser = new { User = String.Empty, Pwd = String.Empty, };
            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            sLogin = Item.User.Trim();
            sPw = Item.Pwd.Trim();

            if (!string.IsNullOrEmpty(sLogin))
            {
                /*登录逻辑*/
                //DataTable dt = LoginBll.GetList(" LoginName='" + Item.User.Trim() + "' And Pwd='" + Item.Pwd.Trim() + "' ");
                DataTable dt = LoginBll.GetList(" LoginName = '" + sLogin + "' and Pwd = '" + sPw + "'  "); // 使用微信获取的USERID自动登录
                if (dt.Rows.Count > 0)
                {
                    Message = "Success";

                    if ((HttpContext.Current.Session["AMFlag"] == "null") || (HttpContext.Current.Session["AMFlag"] == null))
                    {
                        HttpContext.Current.Session["AMFlag"] = sAMFlag;
                    }
                    sFlag = HttpContext.Current.Session["AMFlag"].ToString();

                    HttpContext.Current.Session["LoginName"] = dt.Rows[0]["LoginName"].ToString();
                    HttpContext.Current.Session["FullName"] = dt.Rows[0]["FullName"].ToString();
                    HttpContext.Current.Session["CompanyNo"] = dt.Rows[0]["CompanyNo"].ToString();
                    HttpContext.Current.Session["WeiXinNo"] = dt.Rows[0]["WeiXinNo"].ToString();
                    HttpContext.Current.Session["QYNo"] = dt.Rows[0]["QYNo"].ToString();

                    // 插入日志
                    sNo = "登录6： 登录用户：" + dt.Rows[0]["FullName"].ToString() + "，" + "#02 " + sLogin + "BZ:" + sFlag;
                    sSQL = " insert into T_Temp(MaterNo) values('" + sNo + "')";
                    Common.DBHelper.ExecuteCommand(sSQL);
                }
                else  // 获取不到用户id
                {
                    Message = "NoUser";
                }
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, UserID = sLogin, Flag = sFlag });
            return Result;
        }
        #endregion


        #region 获取用户信息
        public string ApplyGetUserInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sUserNo = string.Empty;
            string sName = string.Empty;
            string sKind = string.Empty;
            string sDept = string.Empty;
            string sSex = string.Empty;
            string sComp = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { UserNo = String.Empty, UserName = String.Empty, Dept = String.Empty, Sex = String.Empty };
                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sUserNo = Item.UserNo.Trim();
                sName = Item.UserName.Trim();
                sDept = Item.Dept.Trim();
                sSex = Item.Sex;
                if (sSex == "全部")
                {
                    sSex = "";
                }
            }
            else
            {

            }

            DataTable dt = BaseModuleBll.GetUserInfo(sUserNo, sName, sDept, sSex, sComp, "1");
            if (dt.Rows.Count > 0)
            {
                Message = "Success";

                // 增加操作权限列
                //DataColumn ORight = new DataColumn("ORight", typeof(string));
                //ORight.DefaultValue = Message;  //HttpContext.Current.Session["LoginName"].ToString();
                //dt.Columns.Add(ORight); // 增加一列
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;

        }
        #endregion



        #region 根据用户编码获取用户信息
        public void GetUserInfoByNo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;
            string sFlag = string.Empty;

            HttpContext context = HttpContext.Current;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }


            // No: sNo, Name: sName, Spec: sSpec,BT:sBT
            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { No = String.Empty, Flag = String.Empty, };

                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sNo = Item.No; sFlag = Item.Flag;
            }


            DataTable dt = BaseModuleBll.GetUserInfo(sNo, "", "", "", sComp, "10-99");
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "请确认用户是否存在或被禁用。";
            }

            string json = JsonConvert.SerializeObject(dt);

            Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });

            context.Response.Write(Result);

            // return Result;

        }
        #endregion


        // 导出用户信息到EXCEL
        public string UserInfoToExcel(string Params)
        {
            string Message = string.Empty;
            string Result = string.Empty;
            string sUserNo = string.Empty;
            string sName = string.Empty;
            string sKind = string.Empty;
            string sDept = string.Empty;
            string sSex = string.Empty;
            string sComp = string.Empty;
            string sReturnFile = "\\ExcelFile\\UserInfo " + DateTime.Now.ToString("yyyymmddss") + ".xls";
            string sFile = System.AppDomain.CurrentDomain.BaseDirectory + "\\Template\\UserInfo.xls";
            string sExcelFilePath = System.AppDomain.CurrentDomain.BaseDirectory + sReturnFile;
            System.IO.File.Copy(sFile, sExcelFilePath, true);
            Microsoft.Office.Interop.Excel.Application app = new Microsoft.Office.Interop.Excel.Application();
            object missing = Type.Missing;
            Microsoft.Office.Interop.Excel.Workbook workbooks = null;



            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { UserNo = String.Empty, UserName = String.Empty, Dept = String.Empty, Sex = String.Empty };
                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sUserNo = Item.UserNo.Trim();
                sName = Item.UserName.Trim();
                sDept = Item.Dept.Trim();
                sSex = Item.Sex;
                if (sSex == "全部")
                {
                    sSex = "";
                }
            }
            else
            {

            }

            DataTable dt = BaseModuleBll.GetUserInfo(sUserNo, sName, sDept, sSex, sComp, "1");

            try
            {
                workbooks = app.Workbooks.Open(sExcelFilePath, missing, missing, missing, "", "", missing, missing, missing, missing, missing, missing, missing, missing, missing);
                //Microsoft.Office.Interop.Excel.Worksheet xlsSheet = (Microsoft.Office.Interop.Excel.Worksheet)workbooks.Worksheets[1];
                //Microsoft.Office.Interop.Excel.Range xlsColumns = (Microsoft.Office.Interop.Excel.Range)xlsSheet.Rows[5, missing];
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    app.Cells[2 + i, 1] = i + 1;//序号  // 5,1  :第5行，第 1 列    
                    app.Cells[2 + i, 2] = dt.Rows[i]["LoginName"].ToString();
                    app.Cells[2 + i, 3] = dt.Rows[i]["FullName"].ToString();
                    app.Cells[2 + i, 4] = dt.Rows[i]["Kind"].ToString();
                    app.Cells[2 + i, 5] = dt.Rows[i]["DeptName"].ToString();
                    app.Cells[2 + i, 6] = dt.Rows[i]["UserSex"].ToString();
                    app.Cells[2 + i, 7] = dt.Rows[i]["Phone"].ToString();
                    app.Cells[2 + i, 8] = dt.Rows[i]["OperatorTime"].ToString();
                }

                app.Visible = true;
                app.ActiveWorkbook.Save();
            }
            catch (Exception ex)
            {
            }
            finally
            {
                workbooks.Close(false, missing, missing);
                workbooks = null;
                app.Quit();
                app = null;

                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }


            Result = JsonConvert.SerializeObject(new { sFilePath = ".." + sReturnFile });
            return Result;
        }


        #region 显示用户注册信息列表
        public string GetUserRelationList(string Params, string sKFlag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sBDate = string.Empty;
            string sEDate = string.Empty;
            string sCon = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { BDate = String.Empty, EDate = String.Empty };

                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

                sBDate = Item.BDate;
                sEDate = Item.EDate;
            }

            if (string.IsNullOrEmpty(sBDate) || (sBDate == null) || (sBDate == "null"))
            {
                sBDate = DateTime.Now.AddDays(-7).ToString("yyyy-MM-dd");
            }
            if (string.IsNullOrEmpty(sEDate) || (sEDate == null) || (sEDate == "null"))
            {
                sEDate = DateTime.Now.ToString("yyyy-MM-dd");
            }

            sCon = " where a.CompanyNo = '" + sComp + "' and convert(char(10),a.OperatorTime,120)>= '" + sBDate + "' and  convert(char(10),a.OperatorTime,120)<= '" + sEDate + "' ";
            DataTable dt = BaseModuleBll.ShowUserRelationList(sCon, "", sComp, sKFlag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion



        #region 显示用户注册信息列表(测试使用)
        public string GetUserRnList(string Params)
        {
            string Result = string.Empty;
            string sCon = string.Empty;
            string Message = "Success";

            sCon = " where 1=1 ";
            DataTable dt = BaseModuleBll.ShowUserRelationList(sCon, "", "", "");
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion



        #region 获取系统类别及类别项次
        public string GetSysKind(string sKind, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sKFlag = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            DataTable dt = BaseModuleBll.GetSysKind(sKind, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 插入或更新多级数据：系统类别，菜单
        public string InsertUpdateSysKind(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            //string HttpUrl = System.Configuration.ConfigurationManager.AppSettings["HttpUrl"];
            string sFlag = string.Empty;
            string sOKFlag = string.Empty;
            string sKind = string.Empty;

            // Kind: Kind, PKind: PKind, MName: MName, KNo: KNo, Code: Code, URL: URL, SeqNo: SeqNo, InMan: InMan, Flag: Flag 
            var AnonymousUser = new
            {
                Kind = String.Empty,
                PKind = String.Empty,
                MName = String.Empty,
                KNo = String.Empty,
                Code = String.Empty,
                URL = String.Empty,
                SeqNo = String.Empty,
                InMan = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sKind = Item.Kind;


            if (Item.Flag == "23-5")  // 给公司 添加大模块
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist("模块名称", Item.PKind, "", Item.Code, "23-3");
                if (sOKFlag == "Y")  // 判断该模块是否已分配给该公司了
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (Item.Flag == "23-6")  // 给公司添加模块下的菜单
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.PKind, Item.MName, "", Item.Code, "23-3");
                if (sOKFlag == "Y")  // 判断该模块是否已分配给该公司了
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            //else if (Item.Flag == "23-1")  
            //{
            //    sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.PKind, Item.MName, "", sComp, Item.Flag);
            //}


            sOKFlag = BaseModuleBll.InsertOrUpdateSysKind(sKind, Item.KNo, Item.PKind, Item.MName, Item.Code, Item.URL, Item.SeqNo, sComp, sLogin, Item.Flag);

            if (sOKFlag.Length <= 2)
            {

                Message = "Success";

            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion




        #region 删除大类小类
        public string DelSysKind(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string HttpUrl = System.Configuration.ConfigurationManager.AppSettings["HttpUrl"];
            string sFlag = string.Empty;
            string sOKFlag = string.Empty;


            var AnonymousUser = new
            {
                Kind = String.Empty,
                KindList = String.Empty,
                InMan = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            if (sAMFlag == "1")  // 删除大类
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.Kind, Item.KindList, "", sComp, "3");
            }
            //else
            //{
            //    sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.Kind, Item.KindList, sComp, "4");
            //}

            if (sOKFlag == "Y")  // 表示新增的类别已存在了。
            {
                Message = "Y_EXIST";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }


            sOKFlag = BaseModuleBll.DeleteSysKind(Item.Kind, Item.KindList, sComp, Item.InMan, sAMFlag);

            if (sOKFlag.Length <= 2)
            {

                Message = "Success";

            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion



        #region 获取系统类别的下拉列表
        public string GetSysTypeChoogeList(string Kind, string sFlag)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sComp = string.Empty;
            string sTNo = string.Empty;
            string sTName = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            DataTable dt = BaseModuleBll.GetSysKind(Kind, sComp, sFlag);  //  39 获取类别的下拉选择
            if (dt.Rows.Count > 0)
            {

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    sTNo = dt.Rows[i]["ItemName"].ToString();
                    sTName = dt.Rows[i]["ItemName"].ToString();

                    Message += "<option value='" + sTNo + "'>" + sTName + "</option>";
                }
            }
            else
            {
                Message = "Error";
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;

        }
        #endregion



        #region 获取物料类型下来
        public string GetMaterTypeList(string Kind, string sFlag)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sComp = string.Empty;
            string sTNo = string.Empty;
            string sTName = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            DataTable dt = BaseModuleBll.GetMaterType(Kind, sComp, sFlag);
            if (dt.Rows.Count > 0)
            {

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    sTNo = dt.Rows[i]["TypeName"].ToString();
                    sTName = dt.Rows[i]["TypeName"].ToString();

                    Message += "<option value='" + sTNo + "'>" + sTName + "</option>";
                }
            }
            else
            {
                Message = "Error";
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;

        }
        #endregion



        #region 获取部门的下拉列表
        public string GetDeptInfoList(string Kind, string sFlag)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sComp = string.Empty;
            string sTNo = string.Empty;
            string sTName = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            DataTable dt = BaseModuleBll.GetSysKind(Kind, sComp, sFlag);  // 41 获取类别的下拉选择
            if (dt.Rows.Count > 0)
            {
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    sTNo = dt.Rows[i]["PNNo"].ToString();
                    sTName = dt.Rows[i]["PNName"].ToString();

                    Message += "<option value='" + sTNo + "'>" + sTName + "</option>";
                }
            }
            else
            {
                Message = "Error";
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;

        }
        #endregion







        #region 获取供应商信息  
        public void GetSupplierInfo(string Params, int rows, int page, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sReturn = string.Empty;
            string sComp = string.Empty;
            string sSNo = string.Empty;
            string sSEn = string.Empty;
            string sTJ = string.Empty;
            string sFS = string.Empty;
            int iSumCount = 0;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return;
            }


            var AnonymousUser = new
            {
                SupplierNo = String.Empty,
                SupplierEn = String.Empty,
                TJ = String.Empty,
                FS = String.Empty,
            };


            if (string.IsNullOrEmpty(Params) || (Params == null) || (Params == "null"))
            {
                sSNo = "";
                sSEn = "";
                sTJ = "";
                sFS = "";
            }
            else
            {
                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


                if (string.IsNullOrEmpty(Item.SupplierNo) || (Item.SupplierNo == null) || (Item.SupplierNo == "null"))
                {
                    sSNo = "";
                }
                else
                {
                    sSNo = Item.SupplierNo;
                }
                if (string.IsNullOrEmpty(Item.SupplierEn) || (Item.SupplierEn == null) || (Item.SupplierEn == "null"))
                {
                    sSEn = "";
                }
                else
                {
                    sSEn = Item.SupplierEn;
                }
                if (string.IsNullOrEmpty(Item.TJ) || (Item.TJ == null) || (Item.TJ == "null"))
                {
                    sTJ = "";
                }
                else
                {
                    sTJ = Item.TJ;
                }
                if (string.IsNullOrEmpty(Item.FS) || (Item.FS == null) || (Item.FS == "null"))
                {
                    sFS = "";
                }
                else
                {
                    sFS = Item.FS;
                }

            }

            //if (string.IsNullOrEmpty(sSNo))
            //{
            //   sSNo = sSupplierNo;
            //}


            DataTable dt = BaseModuleBll.GetSupplierInfo(sSNo, sSEn, sTJ, sFS, rows, page, sMan, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                iSumCount = int.Parse(dt.Rows[0]["NumCount"].ToString());

                string sJson = JsonConvert.SerializeObject(dt);
                sReturn = "{\"code\":0,\"msg\":\"\",\"count\":" + iSumCount + ",\"data\":" + sJson + "}";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;
            context.Response.Write(sReturn);
        }
        #endregion


        #region 获取客户信息
        public void GetCustInfo(string Params, int rows, int page, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sCustNo = string.Empty;
            string sName = string.Empty;
            string sTJ = string.Empty;
            string sFS = string.Empty;
            string sReturn = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            int iSumCount = 0;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
            }

            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { CustNo = String.Empty, CustName = String.Empty, TJ = String.Empty, FS = String.Empty };
                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sCustNo = Item.CustNo.Trim();
                sName = Item.CustName.Trim();
                sTJ = Item.TJ.Trim();
                sFS = Item.FS.Trim();
            }
            else
            {

            }

            DataTable dt = BaseModuleBll.GetCustInfo(sCustNo, sName, sTJ, sFS, "", rows, page, sMan, sComp, "40");
            // DataTable dt = BaseModuleBll.GetPageCustInfo("", "", sCustNo, sName, sBSC, "", rows, page, sMan); // 达能使用的  
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                iSumCount = int.Parse(dt.Rows[0]["NumCount"].ToString());

                string sJson = JsonConvert.SerializeObject(dt);
                sReturn = "{\"code\":0,\"msg\":\"\",\"count\":" + iSumCount + ",\"data\":" + sJson + "}";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;
            context.Response.Write(sReturn);

        }
        #endregion



        #region 获取菜单 -- 左边导航使用
        public string GetMenu(string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sModel = string.Empty;
            string sButtName = string.Empty;
            int FI = 0;
            int CI = 0;
            int iExp = 1;  // 是否展开
            string sURL = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            //获取大模块的菜单名字 ModuleName,ParentID
            DataTable dt = BaseModuleBll.GetMenu(sMan, "", 0, sComp, "22-1");
            DataTable dt2 = null;
            string sHtml = "[";
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                FI = int.Parse(dt.Rows[i]["ParentID"].ToString()); // 模块ID
                sModel = dt.Rows[i]["ModuleName"].ToString();  //模块名称
                iExp = int.Parse(dt.Rows[i]["AllowExpand"].ToString()); //是否展开

                //{ "F_ModuleId": "1", "F_ParentId": "0", "F_EnCode": "SysManage", "F_FullName": "提案管理", "F_Icon": "fa fa-desktop", "F_UrlAddress": "/default", "F_Target": "expand", "F_IsMenu": 0, "F_AllowExpand": 1, "F_IsPublic": 0, "F_AllowEdit": null, "F_AllowDelete": null, "F_SortCode": 1, "F_DeleteMark": 0, "F_EnabledMark": 1, "F_Description": null, "F_CreateDate": null, "F_CreateUserId": null, "F_CreateUserName": null, "F_ModifyDate": "2015-11-17 11:22:46", "F_ModifyUserId": "System", "F_ModifyUserName": "超级管理员" },
                sHtml = sHtml + " {\"F_ModuleId\": \"" + FI + "\", \"F_ParentId\": \"0\", \"F_EnCode\": \"SysManage\", \"F_FullName\": \"" + sModel + "\", \"F_Icon\": \"fa fa-desktop\", \"F_UrlAddress\": \"/default\", \"F_Target\": \"expand\", \"F_IsMenu\": 0, \"F_AllowExpand\":0, \"F_IsPublic\": 0, \"F_AllowEdit\": null, \"F_AllowDelete\": null, \"F_SortCode\": 1, \"F_DeleteMark\": 0, \"F_EnabledMark\": 1, \"F_Description\": null },";
                // { "F_ModuleId": "21", "F_ParentId": "2", "F_EnCode": "OrganizeManage", "F_FullName": "社情民意填写", "F_Icon": "fa fa-sitemap", "F_UrlAddress": "book02.html", "F_Target": "iframe", "F_IsMenu": 1, "F_AllowExpand": 1, "F_IsPublic": 0, "F_AllowEdit": null, "F_AllowDelete": null, "F_SortCode": 1, "F_DeleteMark": 0, "F_EnabledMark": 1, "F_Description": null, "F_CreateDate": null, "F_CreateUserId": null, "F_CreateUserName": null, "F_ModifyDate": "2016-04-29 11:55:28", "F_ModifyUserId": "System", "F_ModifyUserName": "超级管理员" },


                dt2 = BaseModuleBll.GetMenu(sMan, sModel, FI, sComp, "22-2");
                for (int j = 0; j < dt2.Rows.Count; j++)
                {
                    CI = int.Parse(dt2.Rows[j]["ModuleID"].ToString());  // 功能界面ID
                    sButtName = dt2.Rows[j]["ModuleName"].ToString(); // // 功能界面 
                    sURL = dt2.Rows[j]["ModuleURL"].ToString();

                    sHtml = sHtml + " { \"F_ModuleId\": \"" + CI + "\", \"F_ParentId\": \"" + FI + "\", \"F_EnCode\": \"OrganizeManage\", \"F_FullName\": \"" + sButtName + "\", \"F_Icon\": \"fa fa-sitemap\", \"F_UrlAddress\": \"" + sURL + "\", \"F_Target\": \"iframe\", \"F_IsMenu\": 1, \"F_AllowExpand\": 1, \"F_IsPublic\": 0, \"F_AllowEdit\": null,\"F_AllowDelete\": null, \"F_SortCode\": 1, \"F_DeleteMark\": 0, \"F_EnabledMark\": 1, \"F_Description\": null},";
                }
            }
            sHtml = sHtml.TrimEnd(',');
            sHtml = sHtml + "]";


            HttpContext context = HttpContext.Current;

            context.Response.Write(sHtml);

            return Result = sHtml;
            //return  Result = JsonConvert.SerializeObject(new { Msg = Message });
        }
        #endregion


        #region 获取模块及菜单 -- 模块管理使用
        public string GetModule(string Kind, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sModel = string.Empty;
            string sButtName = string.Empty;
            int FI = 0;
            int CI = 0;
            int iExp = 1;  // 是否展开
            string sURL = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            //获取大模块的菜单名字 ModuleName,ParentID
            DataTable dt = BaseModuleBll.GetMenu(sMan, Kind, 0, sComp, Flag);


            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 获取功能模块-- 角色管理使用  
        public string GetMenuForRole(string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sModel = string.Empty;
            string sButtName = string.Empty;
            string sFuncName = string.Empty;
            int FI = 0;
            int CI = 0;
            int FuncID = 0;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            //获取大模块的菜单名字 ModuleName,ParentID
            DataTable dt = BaseModuleBll.GetMenu(sMan, "", 0, sComp, "24-1");
            DataTable dt2 = null; // 模块下的功能界面
            DataTable dt3 = null; // 功能模块下的功能按钮
            string sHtml = "[";
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                FI = int.Parse(dt.Rows[i]["ParentID"].ToString()); // 模块ID
                sModel = dt.Rows[i]["ModuleName"].ToString();  //模块名称

                //{ "F_ModuleId": "1", "F_ParentId": "0", "F_EnCode": "SysManage", "F_FullName": "提案管理", "F_Icon": "fa fa-desktop", "F_UrlAddress": "/default", "F_Target": "expand", "F_IsMenu": 0, "F_AllowExpand": 1, "F_IsPublic": 0, "F_AllowEdit": null, "F_AllowDelete": null, "F_SortCode": 1, "F_DeleteMark": 0, "F_EnabledMark": 1, "F_Description": null, "F_CreateDate": null, "F_CreateUserId": null, "F_CreateUserName": null, "F_ModifyDate": "2015-11-17 11:22:46", "F_ModifyUserId": "System", "F_ModifyUserName": "超级管理员" },
                //{ id: 1, pId: 0, name: "随意勾选 1", font:{ 'font-weight': 'bold' }, open: true },
                //{ id: 11, pId: 1, name: "随意勾选 1-1" },
                //{ id: 12, pId: 1, name: "随意勾选 1-2", open: true },
                sHtml = sHtml + " {id: " + FI + ", pId: 0, name: \"" + sModel + "\", open:true},";


                dt2 = BaseModuleBll.GetMenu(sMan, sModel, FI, sComp, "24-2");
                for (int j = 0; j < dt2.Rows.Count; j++)
                {
                    CI = int.Parse(dt2.Rows[j]["ModuleID"].ToString());  // 功能界面ID
                    sButtName = dt2.Rows[j]["ModuleName"].ToString(); // // 功能界面 

                    sHtml = sHtml + " { id: " + CI + ", pId: " + FI + ",name: \"" + sButtName + "\" , open:true},";

                    // 查找该模块下是否设置了功能按钮的权限
                    dt3 = BaseModuleBll.GetMenu(sMan, sModel, CI, sComp, "24-3");
                    for (int m = 0; m < dt3.Rows.Count; m++)
                    {
                        FuncID = int.Parse(dt3.Rows[m]["FuncID"].ToString());  // 功能界面ID
                        sFuncName = dt3.Rows[m]["FuncName"].ToString(); // // 功能界面 

                        sHtml = sHtml + " { id: " + FuncID + ", pId: " + CI + ",name: \"" + sFuncName + "\" , open:true},";

                    }

                }
            }
            sHtml = sHtml.TrimEnd(',');
            sHtml = sHtml + "]";


            HttpContext context = HttpContext.Current;

            context.Response.Write(sHtml);

            return Result = sHtml;
            //return  Result = JsonConvert.SerializeObject(new { Msg = Message });
        }
        #endregion


        #region 获取用户或角色具有的菜单 
        public string GetRoleHaveMenu(string Flag, string sNo)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            int iMID = 0;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            //获取大模块的菜单名字 ModuleName,ParentID
            DataTable dt = BaseModuleBll.GetMenu(sMan, "", int.Parse(sNo), sComp, "24-6");
            string sHtml = "[";
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                iMID = int.Parse(dt.Rows[i]["ModuleID"].ToString()); // 模块ID

                sHtml = sHtml + "" + iMID + ",";
            }
            sHtml = sHtml.TrimEnd(',');
            sHtml = sHtml + "]";


            HttpContext context = HttpContext.Current;

            context.Response.Write(sHtml);

            return Result = sHtml;
            //return  Result = JsonConvert.SerializeObject(new { Msg = Message });
        }
        #endregion


        #region 删除模功能模块信息 
        public string DelModel(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            // string HttpUrl = System.Configuration.ConfigurationManager.AppSettings["HttpUrl"];
            string sFlag = string.Empty;
            string sOKFlag = string.Empty;

            //Kind, MName: MName, KNo: KNo, InMan: InMan, Flag
            var AnonymousUser = new
            {
                Kind = String.Empty,
                MName = String.Empty,
                KNo = String.Empty,
                InMan = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            if (Item.Flag == "23-2")  //删除模块
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.KNo, Item.MName, "", sComp, Item.Flag);
                if (sOKFlag == "Y")  // 判断下面是否有菜单
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }



            sOKFlag = BaseModuleBll.DeleteModelInfo(Item.KNo, Item.MName, Item.InMan, Item.Flag);

            if (sOKFlag.Length <= 2)
            {

                Message = "Success";

            }
            else
            {
                Message = "Error" + sOKFlag;
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 获取角色信息
        public string GetRoleInfo(string sUserNo, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sURL = string.Empty;



            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            //获取大模块的菜单名字 ModuleName,ParentID
            DataTable dt = BaseModuleBll.GetRoleInfo(sUserNo, sComp, Flag);


            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 新增或修改用户信息
        public string AddEditUser(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            //string HttpUrl = System.Configuration.ConfigurationManager.AppSettings["HttpUrl"];
            string sOKFlag = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            // Kind: sKind, No: sNo, Name: sName, Dept: sDept, Pwd: sPwd, ZW: sZW, Sex: sSex, Telph: sTelph, Phone: sPhone, Email: sEmail, QQ: sQQ, InMan: InMan, Flag: Flag 
            var AnonymousUser = new
            {
                Kind = String.Empty,
                No = String.Empty,
                Name = String.Empty,
                Dept = String.Empty,
                Pwd = String.Empty,
                ZW = String.Empty,
                Sex = String.Empty,
                Telph = String.Empty,
                Phone = String.Empty,
                Email = String.Empty,
                QQ = String.Empty,
                File = String.Empty,
                Path = String.Empty,
                Comp = String.Empty,
                RoleStr = String.Empty,
                InMan = String.Empty,
                Flag = String.Empty,
                ModuleName = String.Empty
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            if (Item.Flag == "24-3")
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.No, "", "", sComp, Item.Flag);
                if (sOKFlag == "Y")  // 判断新增的信息是否存在了
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }

            if (string.IsNullOrEmpty(Item.Comp) || (Item.Comp == null) || (Item.Comp == "null")) // 如果界面没有输入公司代码（界面输入公司代码，主要是我们给新的公司用户分配公司代码）
            {
                sComp = sComp;
            }
            else
            {
                sComp = Item.Comp;
            }



            sOKFlag = BaseModuleBll.AddEditUser(Item.Kind, Item.No, Item.Name, Item.Dept, Item.Pwd, Item.ZW, Item.Sex, Item.Telph, Item.Phone, Item.QQ, Item.Email, Item.File, Item.Path, sComp, Item.RoleStr, sLogin, Item.Flag, IP, Item.ModuleName);
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 删除用户信息
        public string DelUserInfo(string UserNo, string ModuleName)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sFlag = string.Empty;
            string sOKFlag = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            sOKFlag = BaseModuleBll.DelUserInfo(UserNo, sLogin, IP, sComp, ModuleName);

            if (sOKFlag.Length <= 2)
            {

                Message = "Success";

            }
            else
            {
                Message = "Error" + sOKFlag;
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 新增角色信息
        public string AddRole(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            //string HttpUrl = System.Configuration.ConfigurationManager.AppSettings["HttpUrl"];
            string sOKFlag = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            var AnonymousUser = new
            {
                Name = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            if (Item.Flag == "25-5")
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.Name, "", "", sComp, Item.Flag);
                if (sOKFlag == "Y")  // 判断新增的信息是否存在了
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }


            sOKFlag = BaseModuleBll.InsertRoleInfo(Item.Name, sLogin, sComp, Item.Flag);
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion



        #region 删除角色
        public string DelRoleInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;

            //Kind, MName: MName, KNo: KNo, InMan: InMan, Flag
            var AnonymousUser = new
            {
                No = String.Empty,
                InMan = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.No, "", "", sComp, "25-6");
            if (sOKFlag == "Y")  // 判断是否已被使用
            {
                Message = "Y_EXIST";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }


            sOKFlag = BaseModuleBll.DeleteRole(Item.No, Item.InMan, sAMFlag);

            if (sOKFlag.Length <= 2)
            {

                Message = "Success";

            }
            else
            {
                Message = "Error" + sOKFlag;
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion



        #region 插入角色对应的功能模块及操作按钮
        public string InsertRoleModule(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            var AnonymousUser = new
            {
                RoleID = String.Empty,
                RoleStr = String.Empty,
                FuncStr = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            sOKFlag = BaseModuleBll.InsertRoleModule(Item.RoleID, sComp, Item.RoleStr, Item.FuncStr, sLogin, "");
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion



        #region 获取物料分类信息
        public string GetMaterType(string sName, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sKFlag = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            DataTable dt = BaseModuleBll.GetMaterType(sName, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion



        #region 插入或更新物料分类
        public string AddEditMaterType(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sFlag = string.Empty;
            string sOKFlag = string.Empty;
            string sKind = string.Empty;

            // KNo: KNo, MName: MName,Location:sLoca,Remark:sRemark
            var AnonymousUser = new
            {
                MTID = String.Empty,
                KNo = String.Empty,
                FKID = String.Empty,
                MName = String.Empty,
                Location = String.Empty,
                FType = String.Empty,
                Remark = String.Empty,
                InMan = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            if ((Item.Flag == "1") || (Item.Flag == "3"))  // 新增大类
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist("大类", Item.MName, Item.Location, sComp, "50-3-1");
                if (sOKFlag == "Y")  // 判断新增的信息是否存在了
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((Item.Flag == "2") || (Item.Flag == "4"))   // 新增小类
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.FKID, Item.MName, Item.Location, sComp, "50-3-2");
                if (sOKFlag == "Y")  // 判断新增的信息是否存在了
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }

            sOKFlag = BaseModuleBll.AddEditMaterType(Item.MTID, Item.KNo, Item.MName, Item.FKID, Item.Location, Item.FType, Item.Remark, sComp, sLogin, Item.Flag);

            if (sOKFlag.Length <= 2)
            {

                Message = "Success";

            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 删除物料分类
        public string DelMaterType(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sFlag = string.Empty;
            string sOKFlag = string.Empty;


            var AnonymousUser = new
            {
                MTID = String.Empty,
                Name = String.Empty,
                No = String.Empty,
                Loca = String.Empty,
                InMan = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.MTID, Item.No, "", sComp, "50-4");
            if (sOKFlag == "Y")  // 判断新增的信息是否存在了
            {
                Message = "Y_EXIST";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }


            sOKFlag = BaseModuleBll.DelMaterType(Item.MTID, Item.Name, Item.No, Item.Loca, sComp, Item.InMan, sAMFlag);

            if (sOKFlag.Length <= 2)
            {

                Message = "Success";

            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 获取物料信息
        public void GetMaterInfo(string Params, int rows, int page, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sReturn = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;
            string sMName = string.Empty;
            string sSpec = string.Empty;
            string sBT = string.Empty;
            int iSumCount = 0;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return;
            }

            // No: sNo, Name: sName, Spec: sSpec,BT:sBT
            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { No = String.Empty, Name = String.Empty, Spec = String.Empty, BT = String.Empty, };

                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sNo = Item.No; sMName = Item.Name; sSpec = Item.Spec; sBT = Item.BT;
            }
            else // 查询最近一个月录入的记录
            {

            }


            DataTable dt = BaseModuleBll.GetMaterInfo(sNo, sMName, "", sSpec, sBT, rows, page, sComp, sMan, "51-2");
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                iSumCount = int.Parse(dt.Rows[0]["NumCount"].ToString());

                string sJson = JsonConvert.SerializeObject(dt);
                sReturn = "{\"code\":0,\"msg\":\"\",\"count\":" + iSumCount + ",\"data\":" + sJson + "}";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;
            context.Response.Write(sReturn);

        }
        #endregion


        #region 插入或更新物料信息
        public string AddEditMater(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                PN = String.Empty,
                Spec = String.Empty,
                wllb = String.Empty,
                Kd = String.Empty,
                Un = String.Empty,
                Bt = String.Empty,
                Ft = String.Empty,
                lx = String.Empty,
                cl = String.Empty,
                cc = String.Empty,
                xz = String.Empty,
                na = String.Empty,
                fov = String.Empty,
                cg = String.Empty,
                gq = String.Empty,
                jj = String.Empty,
                dm = String.Empty,
                pt = String.Empty,
                hd = String.Empty,
                zj = String.Empty,
                tx = String.Empty,
                jk = String.Empty,
                mtf = String.Empty,
                qx = String.Empty,
                gb = String.Empty,
                bl = String.Empty,
                bs = String.Empty,
                xn = String.Empty,
                gz = String.Empty,
                rs = String.Empty,
                sm = String.Empty,
                date = String.Empty,
                spl = String.Empty,
                prd = String.Empty,
                jz = String.Empty,
                mz = String.Empty,
                gl = String.Empty,
                pldy = String.Empty,
                tz = String.Empty,
                C1 = String.Empty,
                C2 = String.Empty,
                C3 = String.Empty,
                C4 = String.Empty,
                C5 = String.Empty,
                C6 = String.Empty,
                C7 = String.Empty,
                C8 = String.Empty,
                C9 = String.Empty,
                C10 = String.Empty,
                rmk = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            if (It.Flag == "1")  // 新增
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(It.No, "", "", sComp, "51");
                if (sOKFlag == "Y")  // 判断新增的信息是否存在了
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }

            sOKFlag = BaseModuleBll.InsertOrUpdateMater(It.No, It.Name, It.PN, It.Spec, It.wllb, It.Kd, It.Un, It.Bt, It.Ft, It.lx, It.cl, It.cc, It.xz, It.na, It.fov, It.cg, It.gq, It.jj, It.dm, It.pt, It.hd, It.zj, It.tx, It.jk, It.mtf, It.qx,
                It.gb, It.bl, It.bs, It.xn, It.gz, It.rs, It.sm, It.date, It.spl, It.prd, It.jz, It.mz, It.gl, It.pldy, It.tz, It.C1, It.C2, It.C3, It.C4, It.C5, It.C6, It.C7, It.C8, It.C9, It.C10, It.rmk, IP, sComp, sLogin, It.Flag);

            if (sOKFlag.Length <= 2)
            {

                Message = "Success";

            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion



        #region 获取客供物料  
        public string GetCustMaterInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sReturn = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;
            string sName = string.Empty;
            string sSpec = string.Empty;
            string sStock = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            // MaterNo: sNo, MaterName: sName, Spec: sSpec,Stock:sStock 
            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { MaterNo = String.Empty, MaterName = String.Empty, Spec = String.Empty, Stock = String.Empty };

                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sNo = Item.MaterNo; sName = Item.MaterName; sSpec = Item.Spec; sStock = Item.Stock;
            }
            else // 查询最近一个月录入的记录
            {

            }

            DataTable dt = BaseModuleBll.GetCustMaterInfo(sNo, sName, sSpec, sStock, sComp, "54-2");
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;

        }
        #endregion


        #region 插入或更新客供物料 
        public string AddEditCMater(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sFlag = string.Empty;
            string sOKFlag = string.Empty;
            string sKind = string.Empty;

            // Kind, KNo: KNo, MName: MName, Spec: Spec,Stock:Stock, Remark: sRemark, InMan: InMan, Flag: Flag
            var AnonymousUser = new
            {
                Kind = String.Empty,
                KNo = String.Empty,
                MName = String.Empty,
                Spec = String.Empty,
                Stock = String.Empty,
                Remark = String.Empty,
                InMan = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            if (Item.Flag == "1")  // 新增
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.KNo, "", "", sComp, "54-3");
                if (sOKFlag == "Y")  // 判断新增的信息是否存在了
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }

            sOKFlag = BaseModuleBll.AddEditCustMater(Item.KNo, Item.MName, Item.Spec, Item.Stock, Item.Remark, sComp, sLogin, Item.Flag);

            if (sOKFlag.Length <= 2)
            {

                Message = "Success";

            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 删除客供物料
        public string DelCustMater(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sFlag = string.Empty;
            string sOKFlag = string.Empty;


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                InMan = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            sOKFlag = BaseModuleBll.DelCustMater(Item.No, sComp, Item.InMan, sAMFlag);

            if (sOKFlag.Length <= 2)
            {

                Message = "Success";

            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 获取供应商编码
        public string GetSupplierNo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            var AnonymousUser = new { SGo = String.Empty };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            sNo = BaseModuleBll.GetSupplierNo(Item.SGo, sComp, "41-4");

            if (sNo == "OVER") // 表示流水号大于99 了
            {
                Message = "OVER";
            }
            else if (!string.IsNullOrEmpty(sNo))
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, SNO = sNo });

            return Result;

        }
        #endregion



        #region 插入或更新供应商信息  
        public string AddEditSupplier(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;


            var AnonymousUser = new
            {
                Kind = String.Empty,
                SGo = String.Empty,
                No = String.Empty,
                Name = String.Empty,
                NameEn = String.Empty,
                TJ = String.Empty,
                FS = String.Empty,
                SL = String.Empty,
                BZ = String.Empty,
                Code = String.Empty,
                CMan = String.Empty,
                Phone = String.Empty,
                Fax = String.Empty,
                Addr = String.Empty,
                Email = String.Empty,
                NameTwo = String.Empty,
                Remark = String.Empty,
                InMan = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            if (Item.Flag == "1")  // 新增
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.No, "", "", sComp, "41-1");
                if (sOKFlag == "Y")  // 判断新增的信息是否存在了
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }

            sOKFlag = BaseModuleBll.InsertOrUpdateSupplier(Item.No, Item.Name, Item.NameTwo, Item.NameEn, Item.Kind, Item.SGo, Item.Code, Item.TJ, Item.FS, Item.SL, Item.BZ, "", "", "", Item.CMan, Item.Phone, Item.Fax, Item.Addr, Item.Email, "", Item.Remark, sComp, sLogin, Item.Flag);

            if (sOKFlag.Length <= 2)
            {

                Message = "Success";

            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 根据供应商编号获取供应商信息
        public void GetSupplierInfoByNo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;
            string sFlag = string.Empty;

            HttpContext context = HttpContext.Current;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }


            // MaterNo: sNo, MaterName: sName, Spec: sSpec,Stock:sStock 
            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { No = String.Empty, Flag = String.Empty, };

                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sNo = Item.No; sFlag = Item.Flag;
            }
            else // 查询最近一个月录入的记录
            {

            }

            DataTable dt = BaseModuleBll.GetSupplierInfo(sNo, "", "", "", 0, 1, sMan, sComp, sFlag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }




            string json = JsonConvert.SerializeObject(dt);


            Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });

            context.Response.Write(Result);

            // return Result;

        }
        #endregion


        #region 获取BOM信息
        public void GetBOMInfo(string Params, int rows, int page, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sFNo = string.Empty;
            string sFName = string.Empty;
            string sCNo = string.Empty;
            string sCName = string.Empty;
            string sGX = string.Empty;
            string sA = string.Empty;
            string sB = string.Empty;
            string sC = string.Empty;
            string sD = string.Empty;
            string sFlag = string.Empty;
            int iSumCount = 0;

            HttpContext context = HttpContext.Current;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }


            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { FNo = String.Empty, FName = String.Empty, CNo = String.Empty, CName = String.Empty, GX = String.Empty, A = String.Empty, B = String.Empty, C = String.Empty, D = String.Empty, Flag = String.Empty, };

                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sFNo = Item.FNo; sFName = Item.FName; sCNo = Item.CNo; sCName = Item.CName; sGX = Item.GX; sA = Item.A; sB = Item.B; sC = Item.C; sD = Item.D; sFlag = Item.Flag;
            }
            else // 查询最近一个月录入的记录
            {

            }

            DataTable dt = BaseModuleBll.GetBOMInfo(sFNo, sFName, sCNo, sCName, sGX, "", "", sA, sB, sC, sD, rows, page, sComp, sMan, "1"); //  Flag=1  标识查询BOM，2 插入工单BOM
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                iSumCount = int.Parse(dt.Rows[0]["NumCount"].ToString());
            }
            else
            {
                Message = "Error";
            }


            string json = JsonConvert.SerializeObject(dt);
            Result = "{\"code\":0,\"msg\":\"\",\"count\":" + iSumCount + ",\"data\":" + json + "}";
            // Result = "{\"data\":" + json + "}";

            //Result = JsonConvert.SerializeObject(new {data = json });

            context.Response.Write(Result);

            // return Result;


        }
        #endregion


        #region 获取工作中心树形结构
        public void GetWorkCenterInfo(string Params, int rows, int page, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sFNo = string.Empty;
            string sFName = string.Empty;
            string sCNo = string.Empty;
            string sCName = string.Empty;
            string sGX = string.Empty;
            string sFlag = string.Empty;
            int iSumCount = 0;

            HttpContext context = HttpContext.Current;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }


            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { FNo = String.Empty, FName = String.Empty, CNo = String.Empty, CName = String.Empty, GX = String.Empty, Flag = String.Empty, };

                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sFNo = Item.FNo; sFName = Item.FName; sCNo = Item.CNo; sCName = Item.CName; sGX = Item.GX; sFlag = Item.Flag;
            }
            else // 查询最近一个月录入的记录
            {

            }

            DataTable dt = BaseModuleBll.GetWorkCenterInfo(sFNo, sFName, sCNo, sCName, sGX, rows, page, sComp); // FNo, FName, CNo, CName, GX, Row, num, Comp
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                iSumCount = int.Parse(dt.Rows[0]["NumCount"].ToString());
            }
            else
            {
                Message = "Error";
            }


            string json = JsonConvert.SerializeObject(dt);
            Result = "{\"code\":0,\"msg\":\"\",\"count\":" + iSumCount + ",\"data\":" + json + "}";
            // Result = "{\"data\":" + json + "}";

            //Result = JsonConvert.SerializeObject(new {data = json });

            context.Response.Write(Result);

            // return Result;


        }
        #endregion



        #region 根据物料编码返回物料信息
        public void GetMaterInfoByNo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;
            string sFlag = string.Empty;

            HttpContext context = HttpContext.Current;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }


            // No: sNo, Name: sName, Spec: sSpec,BT:sBT
            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { No = String.Empty, Flag = String.Empty, };

                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sNo = Item.No; sFlag = Item.Flag;
            }


            DataTable dt = BaseModuleBll.GetMaterInfo(sNo, "", "", "", "", 20, 1, sComp, sMan, sFlag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "请到“基础数据-序列号物料”建立该产品编码的发号规则！";
            }

            string json = JsonConvert.SerializeObject(dt);

            Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });

            context.Response.Write(Result);

            // return Result;

        }
        #endregion



        #region 插入或更新BOM信息
        public string AddEditBOM(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;


            // FNo: sFNo, FName: sFName, CNo: sCNo, CName: sCName, Num: sNum, GX: sGX, NewFNo: sNewFNo, NewFName: sNewFName, Remark: Remark, InMan: InMan, Flag: Flag
            var AnonymousUser = new
            {
                FNo = String.Empty,
                FName = String.Empty,
                CNo = String.Empty,
                CName = String.Empty,
                NameEn = String.Empty,
                Num = String.Empty,
                GX = String.Empty,
                Pack = String.Empty,
                NewFNo = String.Empty,
                NewFName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                Remark = String.Empty,
                InMan = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            if (Item.Flag == "1")  // 新增
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.FNo, Item.CNo, "", sComp, "44-1");
                if (sOKFlag == "Y")  // 判断新增的信息是否存在了
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                // 判断该公司的BOM层级是多少
                //sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.FNo, Item.FName, "", sComp, "44-1");
                //if (sOKFlag == "Y")  // 判断新增的信息是否存在了
                //{
                //    Message = "Y_EXIST";
                //    return JsonConvert.SerializeObject(new { Msg = Message });
                //}

                if (Item.E == "是")  // 前台勾选了典型产品
                {
                    sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.C, Item.FNo, "", sComp, "44-5");
                    if (sOKFlag == "Y")  // 如果是选择了典型产品，判断这个工艺代号，系统是否已有典型产品了，如是，不给在设置：一个工艺代号，只能一个典型产品
                    {
                        Message = "Y_EXISTDX";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }
            }
            else if (Item.Flag == "2")  // 修改
            {
                if (Item.E == "是")  // 前台勾选了典型产品
                {
                    sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.C, Item.FNo, "", sComp, "44-5");
                    if (sOKFlag == "Y")  // 如果是选择了典型产品，判断这个工艺代号，系统是否已有典型产品了，如是，不给在设置：一个工艺代号，只能一个典型产品
                    {
                        Message = "Y_EXISTDX";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }
            }
            else if (Item.Flag == "3")  // 删除
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.FNo, Item.CNo, "", sComp, "44-3");
                if (sOKFlag == "Y")  // 判断该父零件是否有下界物料，如果有，不给删除
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (Item.Flag == "4")  // 维护替代料
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.FNo, Item.CNo, "", sComp, "44-4");
                if (sOKFlag == "Y")  // 判断该主料下是否维护了替代料
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }





            // sFNo, sFName, sNewFNo, sNewFName, sCNo, sCName, sUseNum, sGX, sRemark, sComp, sInMan, sFlag
            sOKFlag = BaseModuleBll.InsertOrUpdateBOM(Item.FNo, Item.FName, Item.NewFNo, Item.NewFName, Item.CNo, Item.CName, Item.Num, Item.GX, Item.Pack, Item.A, Item.B, Item.C, Item.D, Item.E, Item.F, Item.Remark, sComp, sLogin, Item.Flag, IP);

            if (sOKFlag.Length <= 2)
            {

                Message = "Success";

            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 获取客户编码
        public string GetCustNo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;
            string sOKFlag = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            var AnonymousUser = new { No = String.Empty };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.No, "", "", sComp, "40-1");
            if (sOKFlag == "Y")  // 判断新增的信息是否存在了
            {
                Message = "Y_EXIST";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }


            Result = JsonConvert.SerializeObject(new { Msg = Message, SNO = Item.No });

            return Result;

        }
        #endregion


        #region 根据客户编码获取客户信息
        public string GetCustInfoByNo(string Params, string sCNo, string sFlag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;
            string sDEn = string.Empty;
            string sC = string.Empty;
            string sStr = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            var AnonymousUser = new { CNo = String.Empty, Flag = String.Empty, };

            if (string.IsNullOrEmpty(Params) || (Params == null) || (Params == "null"))
            {
                sNo = sCNo;
            }
            else
            {
                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sNo = Item.CNo;
            }


            DataTable dt = BaseModuleBll.GetCustInfo(sNo, "", "", "", "", 10, 1, sMan, sComp, "41-2");
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion





        #region 插入或更新客户信息
        public string AddEditCust(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;


            var AnonymousUser = new
            {
                Kind = String.Empty,
                No = String.Empty,
                Name = String.Empty,
                NameEn = String.Empty,
                TJ = String.Empty,
                FS = String.Empty,
                SL = String.Empty,
                BZ = String.Empty,
                Code = String.Empty,
                CMan = String.Empty,
                Phone = String.Empty,
                Fax = String.Empty,
                Addr = String.Empty,
                Email = String.Empty,
                Remark = String.Empty,
                InMan = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            if (Item.Flag == "1")  // 新增
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.No, "", "", sComp, "40-1");
                if (sOKFlag == "Y")  // 判断新增的信息是否存在了
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }

            // (sNo, sName, sEn, sCode, sPItem, sPType, sSL, sBZ, sInCH, sPhone, sFax, sAddr, sEmail, sRemark, sComp, sInMan, sFlag);
            sOKFlag = BaseModuleBll.InsertOrUpdateCust(Item.No, Item.Name, Item.NameEn, Item.Code, Item.TJ, Item.FS, Item.SL, Item.BZ, Item.CMan, Item.Phone, Item.Fax, Item.Addr, Item.Email, Item.Remark, sComp, sLogin, Item.Flag);

            if (sOKFlag.Length <= 2)
            {

                Message = "Success";

            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion




        #region 获取厂商识别码信息
        public string GetMCodetInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sReturn = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;
            string sCenter = string.Empty;
            string sBD = string.Empty;
            string sED = string.Empty;
            string sBED = string.Empty;
            string sEED = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            //  MCode: sNo, CodeCenter: Center, BDate: sBDate, EDate: sEDate, BEDate: sBEDate, EEDate
            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { MCode = String.Empty, CodeCenter = String.Empty, BDate = String.Empty, EDate = String.Empty, BEDate = String.Empty, EEDate = String.Empty };

                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sNo = Item.MCode; sCenter = Item.CodeCenter; sBD = Item.BDate; sED = Item.EDate; sBED = Item.BEDate; sEED = Item.EEDate;
            }
            else
            {
                if (string.IsNullOrEmpty(sNo))
                {
                    sNo = "";
                }
                if (string.IsNullOrEmpty(sCenter))
                {
                    sCenter = "";
                }
                if (string.IsNullOrEmpty(sBD))
                {
                    sBD = "";
                }
                if (string.IsNullOrEmpty(sED))
                {
                    sED = "";
                }
                if (string.IsNullOrEmpty(sBED))
                {
                    sBED = "";
                }
                if (string.IsNullOrEmpty(sEED))
                {
                    sEED = "";
                }
            }

            // MCode, Center, BD, ED, BED, EED, sComp, sFlag
            DataTable dt = BaseModuleBll.GetMCodetInfo(sNo, sCenter, sBD, sED, sBED, sEED, sComp, "20");
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;

        }
        #endregion


        #region 插入或更新 删除 厂商识别码
        public string OPMCodetInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sFlag = string.Empty;
            string sOKFlag = string.Empty;
            string sKind = string.Empty;


            var AnonymousUser = new
            {
                MCode = String.Empty,
                Center = String.Empty,
                MCodeType = String.Empty,
                NowNum = String.Empty,
                LNum = String.Empty,
                PDate = String.Empty,
                EDate = String.Empty,
                InCharge = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            if (Item.Flag == "1")  // 新增
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.MCode, "", "", sComp, "20-1");
                if (sOKFlag == "Y")  // 判断新增的信息是否存在了
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.MCode, "", "", sComp, "20-2");
                if (sOKFlag == "Y")  // 判断该公司是否已有一个在用的厂商识别码
                {
                    Message = "Y_NOWUSE";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }

            if (Item.Flag == "3")  // 删除 
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.MCode, "", "", sComp, "20-3");
                if (sOKFlag == "Y")  // 判断是否被使用了，使用了不能删除
                {
                    Message = "Y_DELETE";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }

            if (Item.Flag == "5")  // 启用：这个控制很有必要增加进去
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.MCode, "", "", sComp, "20-5");
                if (sOKFlag == "Y")  // 判断该公司是否已有一个在用的，如果是，则不能启用
                {
                    Message = "Y_QY";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }


            sOKFlag = BaseModuleBll.OPMCodetInfo(Item.MCode, Item.Center, Item.MCodeType, Item.NowNum, Item.LNum, Item.PDate, Item.EDate, Item.InCharge, Item.Remark, sComp, sLogin, Item.Flag);

            if (sOKFlag.Length <= 2)
            {

                Message = "Success";

            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 获取发号物料信息
        public void GetSerielMater(string Params, int rows, int page, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sReturn = string.Empty;
            string sComp = string.Empty;
            string sDNo = string.Empty;
            string sMNo = string.Empty;
            string sMName = string.Empty;
            string sModel = string.Empty;
            string sBDate = string.Empty;
            string sEDate = string.Empty;
            int iSumCount = 0;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return;
            }

            var AnonymousUser = new   //  { DNo:sNo, MNo: sMNo, MName: "", Model: sModel };
            {
                DNo = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                Model = String.Empty,
                BDate = String.Empty,
                EDate = String.Empty,
            };


            if (string.IsNullOrEmpty(Params) || (Params == null) || (Params == "null"))
            {
                sDNo = ""; sMNo = ""; sMName = ""; sModel = ""; sBDate = "2018-01-01"; sEDate = "2999-12-31";
            }
            else
            {
                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

                if (string.IsNullOrEmpty(Item.DNo) || (Item.DNo == null) || (Item.DNo == "null"))
                {
                    sDNo = "";
                }
                else
                {
                    sDNo = Item.DNo;
                }
                if (string.IsNullOrEmpty(Item.MNo) || (Item.MNo == null) || (Item.MNo == "null"))
                {
                    sMNo = "";
                }
                else
                {
                    sMNo = Item.MNo;
                }
                if (string.IsNullOrEmpty(Item.MName) || (Item.MName == null) || (Item.MName == "null"))
                {
                    sMName = "";
                }
                else
                {
                    sMName = Item.MName;
                }
                if (string.IsNullOrEmpty(Item.Model) || (Item.Model == null) || (Item.Model == "null"))
                {
                    sModel = "";
                }
                else
                {
                    sModel = Item.Model;
                }
                if (string.IsNullOrEmpty(Item.BDate) || (Item.BDate == null) || (Item.BDate == "null"))
                {
                    sBDate = "2018-01-01";
                }
                else
                {
                    sBDate = Item.BDate;
                }
                if (string.IsNullOrEmpty(Item.EDate) || (Item.EDate == null) || (Item.EDate == "null"))
                {
                    sEDate = "2999-12-31";
                }
                else
                {
                    sEDate = Item.EDate;
                }

            }

            DataTable dt = BaseModuleBll.GetSerielMater(sMNo, sMName, sModel, "", "", sDNo, "", "", "", "", "", rows, page, sMan, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                iSumCount = int.Parse(dt.Rows[0]["NumCount"].ToString());

                string sJson = JsonConvert.SerializeObject(dt);
                sReturn = "{\"code\":0,\"msg\":\"\",\"count\":" + iSumCount + ",\"data\":" + sJson + "}";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;
            context.Response.Write(sReturn);
        }
        #endregion


        #region 插入或更新 删除 序列号物料
        public string OPSerielMater(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sFlag = string.Empty;
            string sOKFlag = string.Empty;
            string sKind = string.Empty;


            var AnonymousUser = new
            {
                MNo = String.Empty,
                MName = String.Empty,
                Model = String.Empty,
                S1 = String.Empty,
                S2 = String.Empty,
                S3 = String.Empty,
                S4 = String.Empty,
                S5 = String.Empty,
                S6 = String.Empty,
                S7 = String.Empty,
                IValue = String.Empty,
                NValue = String.Empty,
                UseFlag = String.Empty,
                BNum = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            if (Item.Flag == "1")  // 新增序列号规则
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.MNo, "", "", sComp, "21-1");
                if (sOKFlag == "Y")  // //判断该序列号物料是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                string sGZ = Item.S1 + Item.S2 + Item.S3 + Item.S4 + Item.S5 + Item.S6 + Item.S7;
                if (sGZ.IndexOf("G1") < 0)  //  &G1  > 0, 表示选择工单的，则规则可以相同，因此选择了工单的，不需要判断
                {
                    sOKFlag = BaseModuleBll.JudgeSysKindExist(sGZ, "", "", sComp, "21-2");
                    if (sOKFlag == "Y")  // 判断规则是否存在了
                    {
                        Message = "Y_EXISTGZ";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }

            }
            else if (Item.Flag == "2")  // 修改序列号规则
            {
                string sGZ = Item.S1 + Item.S2 + Item.S3 + Item.S4 + Item.S5 + Item.S6 + Item.S7;
                if (sGZ.IndexOf("G1") < 0)  //  &G1  > 0, 表示选择工单的，则规则可以相同，因此选择了工单的，不需要判断
                {
                    sOKFlag = BaseModuleBll.JudgeSysKindExist(sGZ, Item.MNo, "", sComp, "21-2");
                    if (sOKFlag == "Y")  // 判断规则是否存在了
                    {
                        Message = "Y_EXISTGZ";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }

            }

            if (Item.Flag == "3888")  //    -- 没有使用
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.MNo, "", "", sComp, "21-3");
                if (sOKFlag == "Y")  //  
                {
                    Message = "Y_DELETE";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }


            sOKFlag = BaseModuleBll.OPSerielMater(Item.MNo, Item.MName, Item.Model, Item.S1, Item.S2, Item.S3, Item.S4, Item.S5, Item.S6, Item.S7, Item.IValue, Item.NValue, Item.UseFlag, Item.BNum, sComp, sLogin, Item.Remark, Item.Flag);
            if (sOKFlag.Length <= 2)
            {

                Message = "Success";

            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion



        #region 获取预览的序列号
        public string GetViewSerialNo(string Params, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            var AnonymousUser = new
            {
                MNo = String.Empty,
                MName = String.Empty
            };


            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            DataTable dt = BaseModuleBll.GetSerielMater(Item.MNo, "", "", "", "", "", "", "", "", "", "", 0, 0, sMan, sComp, Flag);


            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                sNo = dt.Rows[0]["BackInfo"].ToString();
            }
            else
            {
                Message = "Error";
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, SNO = sNo });

            return Result;

        }
        #endregion


        #region 获取发号信息,序列号，DI码
        public void GetSerielInfo(string Params, string sNo, int rows, int page, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sReturn = string.Empty;
            string sComp = string.Empty;
            string sMNo = string.Empty;
            string sMName = string.Empty;
            string sModel = string.Empty;
            string sSerial = string.Empty;
            string sOrderNo = string.Empty;
            string sBDate = string.Empty;
            string sEDate = string.Empty;
            int iSumCount = 0;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return;
            }

            // MNo: sNo, Model: sModel,Serial:sSerial,Order:sOrder,BDate:sBDate,EDate:sEDate
            var AnonymousUser = new
            {
                MNo = String.Empty,
                Model = String.Empty,
                Serial = String.Empty,
                Order = String.Empty,
                BDate = String.Empty,
                EDate = String.Empty,
            };


            if (string.IsNullOrEmpty(Params) || (Params == null) || (Params == "null"))
            {
                sMNo = "";
                sMName = "";
                sModel = "";
                sSerial = "";
                sOrderNo = "";
                sBDate = "2018-01-01";
                sEDate = "2999-12-31";
            }
            else
            {
                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


                if (string.IsNullOrEmpty(Item.MNo) || (Item.MNo == null) || (Item.MNo == "null"))
                {
                    sMNo = "";
                }
                else
                {
                    sMNo = Item.MNo;
                }
                if (string.IsNullOrEmpty(Item.Model) || (Item.Model == null) || (Item.Model == "null"))
                {
                    sModel = "";
                }
                else
                {
                    sModel = Item.Model;
                }
                if (string.IsNullOrEmpty(Item.Serial) || (Item.Serial == null) || (Item.Serial == "null"))
                {
                    sSerial = "";
                }
                else
                {
                    sSerial = Item.Serial;
                }
                if (string.IsNullOrEmpty(Item.Order) || (Item.Order == null) || (Item.Order == "null"))
                {
                    sOrderNo = "";
                }
                else
                {
                    sOrderNo = Item.Order;
                }
                if (string.IsNullOrEmpty(Item.BDate) || (Item.BDate == null) || (Item.BDate == "null"))
                {
                    sBDate = "2021-10-04";
                }
                else
                {
                    sBDate = Item.BDate;
                }
                if (string.IsNullOrEmpty(Item.EDate) || (Item.EDate == null) || (Item.EDate == "null"))
                {
                    sEDate = "2999-12-31";
                }
                else
                {
                    sEDate = Item.EDate;
                }
            }

            if (string.IsNullOrEmpty(sOrderNo))  // 传入工单号查询的情况
            {
                if (string.IsNullOrEmpty(sNo) || (sNo == null) || (sNo == "null"))
                {
                    sOrderNo = "";
                }
                else
                {
                    sOrderNo = sNo;
                }
            }



            DataTable dt = BaseModuleBll.GetSerielInfo(sMNo, sModel, sSerial, sOrderNo, sBDate, sEDate, rows, page, sMan, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                iSumCount = int.Parse(dt.Rows[0]["NumCount"].ToString());

                string sJson = JsonConvert.SerializeObject(dt);
                sReturn = "{\"code\":0,\"msg\":\"\",\"count\":" + iSumCount + ",\"data\":" + sJson + "}";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;
            context.Response.Write(sReturn);
        }
        #endregion


        #region 插入或更新 删除 序列号 
        public string OPSerielInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sFlag = string.Empty;
            string sOKFlag = string.Empty;
            string sOrderNo = string.Empty;
            string sOrderFlag = string.Empty;


            // OrderNo: sONo, MNo: sMNo, Num: sNum, Flag: sFlag
            var AnonymousUser = new
            {
                OrderNo = String.Empty,
                MNo = String.Empty,
                Num = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }



            if (Item.Flag == "2")  // 判断该工单是否已发放序列号
            {
                if (string.IsNullOrEmpty(Item.OrderNo))
                {
                    sOrderNo = CreateOrderNo();
                    sOrderFlag = "ZDY";  // 如果没有输入工单号，自己模拟一个工单号发放序列号
                }
                else
                {
                    sOrderNo = Item.OrderNo;
                    sOrderFlag = "LR";
                }

                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.OrderNo, Item.Num, "", sComp, "26-1");
                if (sOKFlag == "Y")  //  判断该工单是否已发放序列号
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                if (Item.A == "自制")//判断物料属性选择的自制则必须要有工艺路线，否则则不能发号
                {
                    sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.MNo, "", "", sComp, "30-1");
                    if (sOKFlag == "N")
                    {
                        Message = "N_TECHFLOW";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }

                if (Item.A == "外购")//判断物料属性选择的外购则不能有工艺路线，否则则不能发号
                {
                    sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.MNo, "", "", sComp, "30-1");
                    if (sOKFlag == "Y")
                    {
                        Message = "Y_TECHFLOW";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }

                sOKFlag = BaseModuleBll.OPSerielInfo(sOrderNo, "", Item.MNo, Item.Num, "", Item.A, "", "", sComp, sLogin, sOrderFlag, Item.Flag);

                Message = sOKFlag;
            }
            else if (Item.Flag == "3")  // 删除 
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.OrderNo, "", "", sComp, "26-2");
                if (sOKFlag == "Y")  //  判断该序列号是否已使用
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.OrderNo, "", "", sComp, "26-3");
                if (sOKFlag == "Y")  // 如果序列号已生产，不可以删除
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                sOKFlag = BaseModuleBll.OPSerielInfo(Item.OrderNo, "", "", "", "", "", "", "", sComp, sLogin, "", Item.Flag);
                if (sOKFlag.Length <= 6)
                {
                    Message = "Success";
                }
                else
                {
                    Message = "Error" + sOKFlag;

                    string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                    // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                    WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
                }
            }


            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion



        #region 获取产品编码对应的标贴，标贴模板
        public void GetProductLabel(string Params, string sNo, int rows, int page, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sReturn = string.Empty;
            string sComp = string.Empty;
            string sPMNo = string.Empty;
            string sPMName = string.Empty;
            string sModel = string.Empty;
            string sMNo = string.Empty;
            string sMName = string.Empty;
            string sA = string.Empty;
            string sB = string.Empty;
            string sC = string.Empty;
            string sD = string.Empty;
            string sBDate = string.Empty;
            string sEDate = string.Empty;
            int iSumCount = 0;
            string HttpUrl = System.Configuration.ConfigurationManager.AppSettings["HttpUrl"];


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return;
            }


            var AnonymousUser = new
            {
                PMNo = String.Empty,
                PMName = String.Empty,
                Model = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                BDate = String.Empty,
                EDate = String.Empty
            };


            if (string.IsNullOrEmpty(Params) || (Params == null) || (Params == "null"))
            {
                sPMNo = ""; sPMName = ""; sModel = ""; sMNo = ""; sMName = ""; sA = ""; sB = ""; sC = ""; sD = "";
                sBDate = "2021-10-04"; sEDate = "2999-12-31";
            }
            else
            {
                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


                if (string.IsNullOrEmpty(Item.PMNo) || (Item.PMNo == null) || (Item.PMNo == "null"))
                {
                    sPMNo = "";
                }
                else
                {
                    sPMNo = Item.PMNo;
                }
                if (string.IsNullOrEmpty(Item.PMName) || (Item.PMName == null) || (Item.PMName == "null"))
                {
                    sPMName = "";
                }
                else
                {
                    sPMName = Item.PMName;
                }
                if (string.IsNullOrEmpty(Item.Model) || (Item.Model == null) || (Item.Model == "null"))
                {
                    sModel = "";
                }
                else
                {
                    sModel = Item.Model;
                }
                if (string.IsNullOrEmpty(Item.MNo) || (Item.MNo == null) || (Item.MNo == "null"))
                {
                    sMNo = "";
                }
                else
                {
                    sMNo = Item.MNo;
                }
                if (string.IsNullOrEmpty(Item.MName) || (Item.MName == null) || (Item.MName == "null"))
                {
                    sMName = "";
                }
                else
                {
                    sMName = Item.MName;
                }

                if (string.IsNullOrEmpty(Item.A) || (Item.A == null) || (Item.A == "null"))
                {
                    sA = "";
                }
                else
                {
                    sA = Item.A;
                }
                if (string.IsNullOrEmpty(Item.B) || (Item.B == null) || (Item.B == "null"))
                {
                    sB = "";
                }
                else
                {
                    sB = Item.B;
                }
                if (string.IsNullOrEmpty(Item.C) || (Item.C == null) || (Item.C == "null"))
                {
                    sC = "";
                }
                else
                {
                    sC = Item.C;
                }
                if (string.IsNullOrEmpty(Item.D) || (Item.D == null) || (Item.D == "null"))
                {
                    sD = "";
                }
                else
                {
                    sD = Item.D;
                }

                if (string.IsNullOrEmpty(Item.BDate) || (Item.BDate == null) || (Item.BDate == "null"))
                {
                    sBDate = "2021-10-30";
                }
                else
                {
                    sBDate = Item.BDate;
                }
                if (string.IsNullOrEmpty(Item.EDate) || (Item.EDate == null) || (Item.EDate == "null"))
                {
                    sEDate = "2999-12-31";
                }
                else
                {
                    sEDate = Item.EDate;
                }
            }

            DataTable dt = BaseModuleBll.GetSerielMater(sPMNo, sPMName, sModel, sMNo, sMName, sA, sB, sC, sD, sBDate, sEDate, rows, page, sMan, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                // 增加一列，用来放路径的：系统发布路径，主要是标贴模板使用
                DataColumn HUrl = new DataColumn("HttpUrl", typeof(string));
                HUrl.DefaultValue = HttpUrl;
                dt.Columns.Add(HUrl);


                Message = "Success";
                iSumCount = int.Parse(dt.Rows[0]["NumCount"].ToString());

                string sJson = JsonConvert.SerializeObject(dt);
                sReturn = "{\"code\":0,\"msg\":\"\",\"count\":" + iSumCount + ",\"data\":" + sJson + "}";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;
            context.Response.Write(sReturn);
        }
        #endregion




        #region 插入或更新 删除 产对应的标贴
        public string OPProductLabel(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sFlag = string.Empty;
            string sOKFlag = string.Empty;
            string sKind = string.Empty;


            var AnonymousUser = new
            {
                FMNo = String.Empty,
                FMName = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                S1 = String.Empty,
                S2 = String.Empty,
                S3 = String.Empty,
                S4 = String.Empty,
                S5 = String.Empty,
                S6 = String.Empty,
                S7 = String.Empty,
                S8 = String.Empty,
                S9 = String.Empty,
                S10 = String.Empty,
                UseFlag = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            if (Item.Flag == "1")  // 新增 产品编码对应工艺
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.FMNo, Item.MNo, "", sComp, "28-1");
                if (sOKFlag == "Y")  // 判断新增的信息是否存在了
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                if (Item.UseFlag == "是")
                {
                    sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.FMNo, Item.MNo, Item.S7, sComp, "28-3");
                    if (sOKFlag == "Y")  // 一个工序只能有一个自动打印的标贴
                    {
                        Message = "Y_AutoPrint";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }

            }
            else if ((Item.Flag == "2") || (Item.Flag == "3")) //  产品编码对应工艺 修改 删除 
            {
                //sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.MNo, "", "", sComp, "28-2");
                //if (sOKFlag == "Y")  // 判断是否被使用了，使用了不能删除
                //{
                //    Message = "Y_DELETE";
                //    return JsonConvert.SerializeObject(new { Msg = Message });
                //}

                if (Item.UseFlag == "是")
                {
                    sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.FMNo, Item.MNo, "", sComp, "28-3");
                    if (sOKFlag == "Y")  // 一个工序只能有一个自动打印的标贴
                    {
                        Message = "Y_AutoPrint";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }
            }
            else if (Item.Flag == "7") //  新增标贴模板
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.FMNo, Item.MNo, "", sComp, "29-1");
                if (sOKFlag == "Y")  // 判断该标贴模板是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((Item.Flag == "8") || (Item.Flag == "10") || (Item.Flag == "11")) //  草稿状态才可以修改8，删除10，提交11
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.FMNo, Item.MNo, "", sComp, "29-2");
                if (sOKFlag == "Y")  // 判断该标贴模板是否存在 
                {
                    Message = "Y_Status";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (Item.Flag == "8-1") //  升版，填写版本，判断标贴模板是否存在
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.FMNo, Item.MNo, "", sComp, "29-1");
                if (sOKFlag == "Y")  // 判断该标贴模板是否存在 
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((Item.Flag == "12") || (Item.Flag == "13")) //  待审核状态才可审核12，驳回13
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.FMNo, Item.MNo, "", sComp, "29-3");
                if (sOKFlag == "Y")  // 判断该标贴模板是否存在 
                {
                    Message = "Y_Status";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((Item.Flag == "14") || (Item.Flag == "15")) //  启用状态才可以 禁用14，升版15
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.FMNo, Item.MNo, "", sComp, "29-4");
                if (sOKFlag == "Y")  // 判断该标贴模板是否存在 
                {
                    Message = "Y_Status";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (Item.Flag == "16") //  启用 
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.FMNo, Item.MNo, "", sComp, "29-5");
                if (sOKFlag == "Y")  // 判断该标贴模板是否存在 
                {
                    Message = "Y_Status";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }








            sOKFlag = BaseModuleBll.OPProductLabel(Item.FMNo, Item.FMName, Item.MNo, Item.MName, Item.S1, Item.S2, Item.S3, Item.S4, Item.S5, Item.S6, Item.S7, Item.S8, Item.S9, Item.S10, Item.UseFlag, sComp, sLogin, Item.Remark, Item.Flag);
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 根据产品编码获取其下面的标贴
        public string GetLabelByMNo(string Params, string sPMNo, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sKFlag = string.Empty;
            string HttpUrl = System.Configuration.ConfigurationManager.AppSettings["HttpUrl"];

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            var AnonymousUser = new
            {
                MNo = String.Empty,
                Model = String.Empty,
                Serial = String.Empty,
                Order = String.Empty,
                BDate = String.Empty,
                EDate = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);



            DataTable dt = BaseModuleBll.GetSerielMater(Item.MNo, "", Item.Model, Item.Serial, Item.Order, "", "", "", "", Item.BDate, Item.EDate, 2000, 1, sMan, sComp, Flag);  // 28-2
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }

            // 增加一列，用来放路径的：系统发布路径，主要是标贴模板使用
            DataColumn HUrl = new DataColumn("HttpUrl", typeof(string));
            HUrl.DefaultValue = HttpUrl;
            dt.Columns.Add(HUrl);

            DataColumn checkedValue = new DataColumn("checkedValue", typeof(string));
            dt.Columns.Add(checkedValue);


            foreach (DataRow dr in dt.Rows)
            {
                dr["checkedValue"] = "<input type=\"radio\" id=\"rdo" + dr["Rn"].ToString() + "\" name=\"rdoChecked\" checked=\"false\" />";
            }


            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 插入或更新 删除 公司基本信息
        public string OPCompanyInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sFlag = string.Empty;
            string sOKFlag = string.Empty;
            string sKind = string.Empty;


            // 
            var AnonymousUser = new
            {
                CompID = String.Empty,
                CName = String.Empty,
                CJC = String.Empty,
                CEN = String.Empty,
                LMan = String.Empty,
                Code = String.Empty,
                Category = String.Empty,
                BScope = String.Empty,
                RAmount = String.Empty,
                Number = String.Empty,
                RAddr = String.Empty,
                CAddr = String.Empty,
                PAddr = String.Empty,
                PCode = String.Empty,
                CMan = String.Empty,
                Phone = String.Empty,
                Fax = String.Empty,
                EMail = String.Empty,
                Website = String.Empty,
                APPID = String.Empty,
                APPSECRET = String.Empty,
                XKZ = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            if (It.Flag == "1")  // 新增
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(It.CompID, "", "", sComp, "31-1");
                if (sOKFlag == "Y")  // 判断新增的信息是否存在了
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }



            sOKFlag = BaseModuleBll.OPCompanyInfo(It.CompID, It.CName, It.CJC, It.CEN, It.LMan, It.Code, It.Category, It.BScope, It.RAmount, It.Number, It.RAddr, It.CAddr, It.PAddr, It.PCode, It.CMan, It.Phone, It.Fax, It.EMail, It.Website, It.APPID, It.APPSECRET, It.XKZ, sComp, sLogin, It.Remark, It.Flag);
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion



        #region 获取公司基本信息
        public void GetCompanyInfo(string Params, int rows, int page, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sReturn = string.Empty;
            string sComp = string.Empty;
            string sCName = string.Empty;
            string sLMan = string.Empty;
            string sCategory = string.Empty;
            string sBDate = string.Empty;
            string sEDate = string.Empty;
            int iSumCount = 0;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return;
            }

            var AnonymousUser = new
            {
                CName = String.Empty,
                LMan = String.Empty,
                Category = String.Empty,
                BDate = String.Empty,
                EDate = String.Empty,
            };


            if (string.IsNullOrEmpty(Params) || (Params == null) || (Params == "null"))
            {
                sCName = "";
                sLMan = "";
                sCategory = "";
                sBDate = "2018-10-04";
                sEDate = "2999-12-31";
            }
            else
            {
                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


                if (string.IsNullOrEmpty(Item.CName) || (Item.CName == null) || (Item.CName == "null"))
                {
                    sCName = "";
                }
                else
                {
                    sCName = Item.CName;
                }
                if (string.IsNullOrEmpty(Item.LMan) || (Item.LMan == null) || (Item.LMan == "null"))
                {
                    sLMan = "";
                }
                else
                {
                    sLMan = Item.LMan;
                }
                if (string.IsNullOrEmpty(Item.Category) || (Item.Category == null) || (Item.Category == "null"))
                {
                    sCategory = "";
                }
                else
                {
                    sCategory = Item.Category;
                }
                if (string.IsNullOrEmpty(Item.BDate) || (Item.BDate == null) || (Item.BDate == "null"))
                {
                    sBDate = "2018-01-04";
                }
                else
                {
                    sBDate = Item.BDate;
                }
                if (string.IsNullOrEmpty(Item.EDate) || (Item.EDate == null) || (Item.EDate == "null"))
                {
                    sEDate = "2999-12-31";
                }
                else
                {
                    sEDate = Item.EDate;
                }
            }

            DataTable dt = BaseModuleBll.GetCustInfo("", sCName, sLMan, sCategory, "", rows, page, sMan, sComp, "31");

            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                iSumCount = int.Parse(dt.Rows[0]["NumCount"].ToString());

                string sJson = JsonConvert.SerializeObject(dt);
                sReturn = "{\"code\":0,\"msg\":\"\",\"count\":" + iSumCount + ",\"data\":" + sJson + "}";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;
            context.Response.Write(sReturn);
        }
        #endregion



        #region 获取用户表公司下拉列表
        public string GetCompanyList(string Kind, string sFlag)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sComp = string.Empty;
            string sTNo = string.Empty;
            string sTName = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            DataTable dt = BaseModuleBll.GetCustInfo("", "", "", "", "", 50000, 1, "", sComp, "31-2");
            if (dt.Rows.Count > 0)
            {

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    sTNo = dt.Rows[i]["CompanyNo"].ToString();
                    sTName = dt.Rows[i]["CName"].ToString();

                    Message += "<option value='" + sTNo + "'>" + sTName + "</option>";
                }
            }
            else
            {
                Message = "Error";
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;

        }
        #endregion


        #region 获取模块下有哪些公司
        public string GetModuleForComp(string Kind, string Item, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sModel = string.Empty;
            string sButtName = string.Empty;


            //获取大模块的菜单名字 ModuleName,ParentID
            DataTable dt = BaseModuleBll.GetMenu(Item, Kind, 0, sComp, Flag);


            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion



        #region 获取标贴模板列表
        public string GetLabelTempleteList(string sPPNo, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sReturn = string.Empty;
            string sComp = string.Empty;
            int iSumCount = 0;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            DataTable dt = BaseModuleBll.GetSerielMater("", "", "", "", "", "", "", "", "", "", "", 50000, 1, sMan, sComp, "28-3");
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion



        // 导出标贴信息到EXCEL
        public string LabelTemplateToExcel(string Params)
        {
            string Message = string.Empty;
            string Result = string.Empty;
            string sUserNo = string.Empty;
            string sName = string.Empty;
            string sComp = string.Empty;
            string sPMNo = string.Empty;
            string sPMName = string.Empty;
            string sModel = string.Empty;
            string sMNo = string.Empty;
            string sMName = string.Empty;
            string sA = string.Empty;
            string sB = string.Empty;
            string sC = string.Empty;
            string sD = string.Empty;
            string sBDate = string.Empty;
            string sEDate = string.Empty;

            string sReturnFile = "\\ExcelFile\\W-JF01044-071-R1 标贴、标签模板维护记录表 " + DateTime.Now.ToString("yyyymmddss") + ".xls";
            string sFile = System.AppDomain.CurrentDomain.BaseDirectory + "\\Template\\W-JF01044-071-R1 标贴、标签模板维护记录表.xls";
            string sExcelFilePath = System.AppDomain.CurrentDomain.BaseDirectory + sReturnFile;
            System.IO.File.Copy(sFile, sExcelFilePath, true);
            Microsoft.Office.Interop.Excel.Application app = new Microsoft.Office.Interop.Excel.Application();
            object missing = Type.Missing;
            Microsoft.Office.Interop.Excel.Workbook workbooks = null;



            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sUserNo = HttpContext.Current.Session["LoginName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }


            var AnonymousUser = new
            {
                PMNo = String.Empty,
                PMName = String.Empty,
                Model = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                BDate = String.Empty,
                EDate = String.Empty
            };


            if (string.IsNullOrEmpty(Params) || (Params == null) || (Params == "null"))
            {
                sPMNo = ""; sPMName = ""; sModel = ""; sMNo = ""; sMName = ""; sA = ""; sB = ""; sC = ""; sD = "";
                sBDate = "2018-10-04"; sEDate = "2999-12-31";
            }
            else
            {
                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


                if (string.IsNullOrEmpty(Item.PMNo) || (Item.PMNo == null) || (Item.PMNo == "null"))
                {
                    sPMNo = "";
                }
                else
                {
                    sPMNo = Item.PMNo;
                }
                if (string.IsNullOrEmpty(Item.PMName) || (Item.PMName == null) || (Item.PMName == "null"))
                {
                    sPMName = "";
                }
                else
                {
                    sPMName = Item.PMName;
                }
                if (string.IsNullOrEmpty(Item.Model) || (Item.Model == null) || (Item.Model == "null"))
                {
                    sModel = "";
                }
                else
                {
                    sModel = Item.Model;
                }
                if (string.IsNullOrEmpty(Item.MNo) || (Item.MNo == null) || (Item.MNo == "null"))
                {
                    sMNo = "";
                }
                else
                {
                    sMNo = Item.MNo;
                }
                if (string.IsNullOrEmpty(Item.MName) || (Item.MName == null) || (Item.MName == "null"))
                {
                    sMName = "";
                }
                else
                {
                    sMName = Item.MName;
                }

                if (string.IsNullOrEmpty(Item.A) || (Item.A == null) || (Item.A == "null"))
                {
                    sA = "";
                }
                else
                {
                    sA = Item.A;
                }
                if (string.IsNullOrEmpty(Item.B) || (Item.B == null) || (Item.B == "null"))
                {
                    sB = "";
                }
                else
                {
                    sB = Item.B;
                }
                if (string.IsNullOrEmpty(Item.C) || (Item.C == null) || (Item.C == "null"))
                {
                    sC = "";
                }
                else
                {
                    sC = Item.C;
                }
                if (string.IsNullOrEmpty(Item.D) || (Item.D == null) || (Item.D == "null"))
                {
                    sD = "";
                }
                else
                {
                    sD = Item.D;
                }

                if (string.IsNullOrEmpty(Item.BDate) || (Item.BDate == null) || (Item.BDate == "null"))
                {
                    sBDate = "2018-10-30";
                }
                else
                {
                    sBDate = Item.BDate;
                }
                if (string.IsNullOrEmpty(Item.EDate) || (Item.EDate == null) || (Item.EDate == "null"))
                {
                    sEDate = "2999-12-31";
                }
                else
                {
                    sEDate = Item.EDate;
                }
            }

            DataTable dt = BaseModuleBll.GetSerielMater(sPMNo, sPMName, sModel, sMNo, sMName, sA, sB, sC, sD, sBDate, sEDate, 50000, 1, sUserNo, sComp, "29");// 和查询条件的一样

            try
            {
                workbooks = app.Workbooks.Open(sExcelFilePath, missing, missing, missing, "", "", missing, missing, missing, missing, missing, missing, missing, missing, missing);
                //Microsoft.Office.Interop.Excel.Worksheet xlsSheet = (Microsoft.Office.Interop.Excel.Worksheet)workbooks.Worksheets[1];
                //Microsoft.Office.Interop.Excel.Range xlsColumns = (Microsoft.Office.Interop.Excel.Range)xlsSheet.Rows[5, missing];
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    app.Cells[4 + i, 2] = i + 1;//序号  // 5,1  :第5行，第 1 列    
                    app.Cells[4 + i, 3] = dt.Rows[i]["EChange"].ToString();
                    app.Cells[4 + i, 4] = dt.Rows[i]["TPNo"].ToString();
                    app.Cells[4 + i, 5] = dt.Rows[i]["AddUp"].ToString();
                    app.Cells[4 + i, 6] = dt.Rows[i]["OldVer"].ToString();
                    app.Cells[4 + i, 7] = dt.Rows[i]["TPVer"].ToString();
                    app.Cells[4 + i, 8] = dt.Rows[i]["App"].ToString();
                    app.Cells[4 + i, 9] = dt.Rows[i]["Aud"].ToString();
                    app.Cells[4 + i, 10] = dt.Rows[i]["Remark"].ToString();
                }

                app.Visible = true;
                app.ActiveWorkbook.Save();
            }
            catch (Exception ex)
            {
            }
            finally
            {
                workbooks.Close(false, missing, missing);
                workbooks = null;
                app.Quit();
                app = null;

                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }


            Result = JsonConvert.SerializeObject(new { sFilePath = ".." + sReturnFile });
            return Result;
        }



        #region 获取厂商识别码下拉列表
        public string GetMCodeTypeList(string Kind, string sFlag)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sComp = string.Empty;
            string sTNo = string.Empty;
            string sTName = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            DataTable dt = BaseModuleBll.GetSysKind(Kind, sComp, sFlag);  //  39 获取类别的下拉选择
            if (dt.Rows.Count > 0)
            {

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    sTNo = dt.Rows[i]["ItemName"].ToString();
                    sTName = dt.Rows[i]["ItemName"].ToString();

                    Message += "<option value='" + sTNo + "'>" + sTName + "</option>";
                }
            }
            else
            {
                Message = "Error";
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;

        }
        #endregion



        #region 获取抽样方案下拉
        public string GetSamplingPlanList(string Kind, string sFlag)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sComp = string.Empty;
            string sTNo = string.Empty;
            string sTName = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            DataTable dt = BaseModuleBll.GetMaterType(Kind, sComp, "60-1");
            if (dt.Rows.Count > 0)
            {
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    sTNo = dt.Rows[i]["SPNo"].ToString();
                    sTName = dt.Rows[i]["SPName"].ToString();

                    Message += "<option value='" + sTNo + "'>" + sTName + "</option>";
                }
            }
            else
            {
                Message = "Error";
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;

        }
        #endregion



        #region 获取物料批序号
        public void GetBatchSerial(int limit, int page, string Flag, string DataParams)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;

            string sCMNO = string.Empty;
            string sMName = string.Empty;
            string sBNo = string.Empty;

            HttpContext context = HttpContext.Current;


            var AnonymousUser = new
            {
                CMNO = String.Empty,
                MName = String.Empty,
                BNo = String.Empty
            };

            if (string.IsNullOrEmpty(DataParams) || (DataParams == null) || (DataParams == "null"))
            {
                sCMNO = ""; sMName = ""; sBNo = "";
            }
            else
            {
                var Item2 = JsonConvert.DeserializeObject(DataParams);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sCMNO = Item.CMNO; sMName = Item.MName; sBNo = Item.BNo;
            }

            if (context.Session["LoginName"] != null)
            {
                sMan = context.Session["LoginName"].ToString();
                sManName = context.Session["FullName"].ToString();
                sComp = context.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
            }

            DataTable dt = BaseModuleBll.GetBatchSerial(limit, page, sMan, sCMNO, sMName, sBNo, Flag);

            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                int count = Convert.ToInt32(dt.Rows[0]["numCount"].ToString());
                string Json = JsonConvert.SerializeObject(dt);
                Result = "{\"code\":0,\"msg\":\"\",\"count\":" + count + ",\"data\":" + Json + "}";
            }
            else
            {
                Message = "Error";
            }

            context.Response.Write(Result);
        }
        #endregion

        #region 操作物料批序号
        public static string OPBatchSerial(string Flag, string Params)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;

            var AnonymousUser = new
            {
                MaterNo = String.Empty,
                MaterName = String.Empty,
                BatchNo = String.Empty,
                OPFlag = String.Empty,
                BatchNoOld = String.Empty,
            };
            var Itme1 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Itme1), AnonymousUser);

            HttpContext context = HttpContext.Current;

            if (context.Session["LoginName"] != null)
            {
                sMan = context.Session["LoginName"].ToString();
                sManName = context.Session["FullName"].ToString();
                sComp = context.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            if (Flag == "1")
            {
                sOKFlag = BaseModuleBll.OPBatchSerial(It.BatchNo, "", "", "", "", "", "", "3", "");

                if (Convert.ToInt32(sOKFlag) > 0)
                {
                    Message = "Existence";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                    return Result;
                }

            }

            sOKFlag = BaseModuleBll.OPBatchSerial(It.BatchNo, It.MaterNo, It.MaterName, "1", sComp, sMan, It.OPFlag, Flag, It.BatchNoOld);

            if (Convert.ToInt32(sOKFlag) > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }

            var json = new { code = "0", msg = Message };

            Result = JsonConvert.SerializeObject(json);

            return Result;
        }
        #endregion


        #region 物料批序号导入
        public static void BatchSerialToExcel(HttpContext context)
        {
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;

            if (context.Session["LoginName"] != null)
            {
                sMan = context.Session["LoginName"].ToString();
                sManName = context.Session["FullName"].ToString();
                sComp = context.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
            }

            HttpPostedFile file = context.Request.Files["file"];

            string extension = Path.GetExtension(file.FileName);

            if (extension.ToLower() != ".xlsx" && extension.ToLower() != ".xls")
            {
                Message = "FileTypeError";
            }
            else
            {
                try
                {
                    Random ran = new Random();

                    int num = ran.Next(100000, 999999);

                    string path = context.Server.MapPath("~/ExcelFile/") + num + "-" + file.FileName;

                    file.SaveAs(path);

                    // 创建Excel应用程序对象
                    Microsoft.Office.Interop.Excel.Application excelApp = new Microsoft.Office.Interop.Excel.Application();

                    // 打开Excel文件
                    Microsoft.Office.Interop.Excel.Workbook excelWorkbook = excelApp.Workbooks.Open(path);

                    // 选择第一个工作表
                    Microsoft.Office.Interop.Excel.Worksheet excelWorksheet = (Microsoft.Office.Interop.Excel.Worksheet)excelWorkbook.Sheets[1];

                    // 获取使用的行数和列数
                    int rowCount = excelWorksheet.UsedRange.Rows.Count;
                    int colCount = excelWorksheet.UsedRange.Columns.Count;

                    // 遍历每一行和列并读取数据
                    for (int row = 2; row <= rowCount; row++)
                    {
                        List<string> list = new List<string>();
                        for (int col = 1; col <= colCount; col++)
                        {
                            Microsoft.Office.Interop.Excel.Range excelCell = (Microsoft.Office.Interop.Excel.Range)excelWorksheet.Cells[row, col];

                            string cellValue = excelCell.Value.ToString();
                            list.Add(cellValue);
                        }

                        try
                        {
                            BaseModuleBll.OPBatchSerial(list[0], list[1], list[2], "1", sComp, sMan, "Add", "1", "");
                        }
                        catch (Exception)
                        {
                            Message = "RepeaData";
                            context.Response.Write(Message);
                            return;
                        }

                    }

                    // 关闭Excel应用程序对象
                    excelWorkbook.Close();
                    excelApp.Quit();

                    Message = "Success";
                }
                catch (Exception)
                {
                    Message = "Error";
                    context.Response.Write(Message);
                    return;
                }

            }

            context.Response.Write(Message);

        }
        #endregion



        #region 获取基础数据相关信息
        public void GetBaseInfoList(int limit, int page, string Flag, string DataParams)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            int count = 0;

            HttpContext context = HttpContext.Current;
            if (context.Session["LoginName"] != null)
            {
                sMan = context.Session["LoginName"].ToString();
                sManName = context.Session["FullName"].ToString();
                sComp = context.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
                return;
            }

            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                BDate = String.Empty,
                EDate = String.Empty,
            };

            var It = string.IsNullOrEmpty(DataParams) || (DataParams == "null") ? AnonymousUser : JsonConvert.DeserializeAnonymousType(DataParams, AnonymousUser);
            var sBDate = string.IsNullOrEmpty(It.BDate) || (It.BDate == "null") ? "2018-10-30" : It.BDate.Replace('T', ' ');
            var sEDate = string.IsNullOrEmpty(It.EDate) || (It.EDate == "null") ? "2999-10-30" : It.EDate.Replace('T', ' ');

            DataTable dt = BaseModuleBll.GetBaseInfoList(It.No, It.Name, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, sBDate, sEDate, limit, page, sMan, Flag);

            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                count = Convert.ToInt32(dt.Rows[0]["numCount"].ToString());

                if (Flag == "11")
                {
                    dt = CheckData(dt);
                }
            }
            else
            {
                Message = "Error";
            }

            Result = JsonConvert.SerializeObject(new
            {
                code = "0",
                msg = Message,
                count = count,
                data = dt,
            });

            context.Response.Write(Result);
        }


        private static DataTable CheckData(DataTable dt)
        {
            // 预定义敏感字段集合（HashSet提升查询性能）
            var sensitiveKeys = new HashSet<string> { "Pwd", "CompanyNo" };

            Regex KeyValueRegex = new Regex(@"(\w+):([^,]+)");

            // 克隆表结构（避免修改原始表）
            DataTable resultDt = dt.Clone();

            // 批量处理行数据（减少循环内操作）
            foreach (DataRow row in dt.Rows)
            {
                DataRow newRow = resultDt.NewRow();
                newRow.ItemArray = row.ItemArray;

                newRow["NewValue"] = MaskSensitiveValues(newRow["NewValue"].ToString(), sensitiveKeys, KeyValueRegex);
                newRow["OldValue"] = MaskSensitiveValues(newRow["OldValue"].ToString(), sensitiveKeys, KeyValueRegex);

                resultDt.Rows.Add(newRow);
            }
            return resultDt;
        }

        // 正则表达式脱敏感数据方法
        private static string MaskSensitiveValues(string input, HashSet<string> sensitiveKeys, Regex KeyValueRegex)
        {
            if (string.IsNullOrEmpty(input))
            {
                return input;
            }

            return KeyValueRegex.Replace(input, match =>
            {
                string key = match.Groups[1].Value;
                string value = match.Groups[2].Value;

                return sensitiveKeys.Contains(key) ? key + ":" + new string('*', Math.Max(value.Length, 4)) : match.Value;
            });
        }
        #endregion


        #region 对基础数据相关操作
        public void OPBaseInfoList(string DataParams)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;

            HttpContext context = HttpContext.Current;
            if (context.Session["LoginName"] != null)
            {
                sMan = context.Session["LoginName"].ToString();
                sManName = context.Session["FullName"].ToString();
                sComp = context.Session["CompanyNo"].ToString();
            }
            else
            {
                context.Response.Write(JsonConvert.SerializeObject(new { Msg = "LoginError", Login = sManName }));
                return;
            }

            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                Flag = String.Empty,
                Remark = String.Empty,
            };


            if (string.IsNullOrEmpty(DataParams) || (DataParams == "null"))
            {
                context.Response.Write(JsonConvert.SerializeObject(new { Msg = "ParamsError", Login = sManName }));
                return;
            }

            var It = JsonConvert.DeserializeAnonymousType(DataParams, AnonymousUser);

            Result = BaseModuleBll.OPBaseInfoList(It.No, It.Name, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, sMan, sComp, It.Flag, It.Remark, IP);

            context.Response.Write(JsonConvert.SerializeObject(new { Msg = Result }));
        }
        #endregion
























        #region  产生自定义工单号   QS210300001  MB2103230001
        public static string CreateOrderNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_SerielInfo where OrderType='ZDY' and convert(char(10),InDate,120)='" + sDate + "' ", "OrderNo");// 210323 0001
            if (sMaxNo == "")
            {
                sNo = CreateAllNo.CreateBillNo(2, 3) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(6, 4);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 4 - len;

                sNo = CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion























        public bool IsReusable
        {
            get
            {
                return false;
            }
        }


    }
}
