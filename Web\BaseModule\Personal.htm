﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" >
<head>
<title>用户信息</title>
    <link href="../css/all.css" rel="stylesheet" type="text/css"/>
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css"/>
    <script src="../js/jquery-1.9.1.min.js" type="text/javascript"></script>
    <script type="text/javascript" src="../js/jQuery-2.2.0.min.js"></script>
    <script type="text/javascript" src="../js/bstable/js/bootstrap.min.js"></script>
    <script  type="text/javascript" src="../js/bstable/js/bootstrap-table.js"></script>
    <script  type="text/javascript" src="../js/bstable/js/bootstrap-table-zh-CN.min.js"></script>
    <script  type="text/javascript" src="../js/date/js/laydate.js"></script>
    <script type="text/javascript"  src="../js/layer/layer.js"></script>
    <script type="text/javascript"  src="../js/BaseModule.js"></script>
    <link href="../css/XC.css" rel="stylesheet" />
    
    <script type="text/javascript">
        //获取当前所在的模块、菜单
        const currentTabId = sessionStorage.getItem("currentTabId");
        const ModuleName = currentTabId != null ? JSON.parse(currentTabId).ModuleName : null;

        $(function () {


            var sCorpID = "we";  // GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&sFlag=10&CorpID=" + sCorpID,
                success: function(data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=10&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        GetUserInfo(parsedJson.Man);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=25-1' });
                    }
                }
            })
        });



        function GetUserInfo(sNo) {  // 获取个人信息
            var Flag = "10-99";

            var Data = '';
            var Params = { No: sNo, Flag: Flag };
            var Data = JSON.stringify(Params);

            $.ajax({
                type: "POST",
                url: "../Service/BaseModuleAjax.ashx?OP=GetUserInfoByNo&CFlag=" + Flag,
                data: { Data: Data },
                success: function(data) {
                    var parsedJson = jQuery.parseJSON(data);

                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                        var User = eval('(' + parsedJson.json + ')');

                        $('#txtUserNo').val(User[0].LoginName);
                        $('#txtUserName').val(User[0].FullName);
                        $('#txtDept').val("(" + User[0].DeptNo + ")" + User[0].DeptName);
                        $('#txtPwd').val(User[0].Pwd);
                        $('#txtCPwd').val(User[0].Pwd);
                        $('#txtZW').val(User[0].Kind);
                        $('#txtSex').val(User[0].UserSex);
                        $('#txtTelph').val(User[0].WeiXinNo);
                        $('#txtPhone').val(User[0].Phone);
                        $('#txtEmail').val(User[0].SFCode);
                        $('#txtQQ').val(User[0].QYNo);
                    }
                    else {
                        $("#div_warning").html("请确认用户是否存在或被禁用！")
                        $("#div_warning").show();
                    }
                },
                error: function(data) {
                    $("#div_warning").html("系统出错，请重试2！")
                    $("#div_warning").show();
                }
            });

        }


        
        
        
        

   </script>
   
  
   
  

 <style type="text/css">
        #HDiv
        {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }
        .table > tbody > tr > td
        {
            border: 0px;
        }
        .black_overlay
        {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: black;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=88);
        }
        
      #Usertable thead > th {
           background-color: red;
       }
        
       #Usertable tbody > tr:hover {  /* 光标放上去的颜色 */
           background-color: #CCC;
       }
        
    </style>
    


</head>
<body>


     <div id="lightHead" style="height:30px;">
          <label id="txtK" style=" padding:10px;font-size: 14px; color:White; ">用户信息</label>
     </div>

            <table cellspacing="0" cellpadding="0" border='0' style="width: 99%;" class="table">
                <tr>
                    <td align="right">账号<span style="color: Red">*</span></td>
                    <td><input type="text" id="txtUserNo" disabled=disabled/></td>
                    <td align="right">用户名<span style="color: Red">*</span></td>
                    <td><input type="text" id="txtUserName" /></td>
                </tr>
                <tr>
                    <td align="right">部门</td>
                    <td>
                        <input type="text" id="txtDept"  disabled=disabled/>
                    </td>
                    <td align="right">密码</td>
                    <td><input type="password" id="txtPwd" class="long_text"  style="width:90px;"/> <input type="password" id="txtCPwd" class="long_text" style="width:90px;"/> </td>
                </tr>
                <tr>
                    <td align="right">职位</td>
                    <td>
                        <input type="text" id="txtZW"/>
                    </td>
                    <td align="right">性别</td>
                    <td>
                        <select  style="width:50%;" id="txtSex">
                          <option value=""></option>
                          <option value="男">男</option>
                          <option value="女">女</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td align="right">手机</td>
                    <td><input type="text" id="txtPhone"/></td>
                    <td align="right">电话</td>
                    <td><input type="text" id="txtTelph"/></td>
                </tr>
                <tr>
                    <td align="right">邮箱</td>
                    <td><input type="email" id="txtEmail" required /></td>
                    <td align="right">QQ</td>
                    <td><input type="text" id="txtQQ" /></td>
                </tr>
                <tr id="trComp" style=" display:none;">
                    <td align="right">所属公司</td>
                    <td><input type="text" id="txtCompany" /> 填写代码,如：C0010</td>
                    <td align="left"></td>
                    <td></td>
                </tr>
            </table>
            <div id="div_warning" role="alert" style="text-align: center; display: none; color: Red">
                <strong id="divsuccess" style="color: Red"></strong>
            </div>
            <br />
            <div align="center">
                <input type='button' id="PersonalSaveBtn" value='保存' style="width: 50px; height: 30px;" />
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </div>


</body>
</html>