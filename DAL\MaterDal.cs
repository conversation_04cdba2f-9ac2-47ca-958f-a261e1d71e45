﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using Common;
using System.Text;

namespace DAL
{
    public class MaterDal
    {


        public static string JudgeSysKindExist(string Kind, string KindList, string QNo, string QT, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string sStatus = string.Empty;

            if (sFlag == "10-1") // 判断是否已收货完成
            {

                sSQL = " select PurchNum from (" +
                       " SELECT sum(OverNum) as OverNum,PurchNum FROM T_PurchaseItemInfo where PurchNo='" + Kind + "' and PurchItem='" + KindList + "' group by PurchNum " +
                       " ) a where a.OverNum >= a.PurchNum "; 
                
            }
            else if (sFlag == "10-2")  // 判断是否有待IQC检验的，如是，不给新增，直接修改数量即可。
            {
                sSQL = " select InMan from T_MaterReceive where ConfirmStatus='待IQC检验' and PurchaseNo= '" + Kind + "' and PurchaseItem= '" + KindList + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "10-3")  // IQC是否已检验
            {
                sSQL = " select InMan from T_InspectInfo where ReceiveNo = '" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "12-1")  // 判断订单是否已完成
            {
                sSQL = " select InMan from T_PRPrice where PPNo='" + Kind + "' and Status='订单完成' ";
            }
            else if (sFlag == "12-2")  // 判断数量是否正确
            {
                sSQL = " select InMan from T_PRPrice where PPNo='" + Kind + "' and Qty >= OutQty+" + QT + " ";
            }
            else if (sFlag == "12-3")  // 判断出库单的数量是否大于PR单数量
            {
                sSQL = " select * from ( "+
                       " select PPNo,a.Qty,isnull(b.OutNum,0) as OutNum from T_PRPrice a " +
                       " left join (select PPNo,SUM(OutNum) as OutNum from T_ProductOutInfo where PPNo='" + Kind + "' and POutNo <>'" + QNo + "' group by PPNo) b on a.PPNo=b.PPNo " +
                       " where a.PPNo='" + Kind + "' " +
                       " ) aa where aa.Qty >= OutNum +" + QT + " ";
            }
            else if (sFlag == "12-4")  // 判断出库单的数量是否大于库存数量
            {
                sSQL = " select InMan from T_PRPrice where PPNo='" + Kind + "'  " +
                       " and " + QT + " <= "+
                       "        (select  SUM(Num)as Stock from ( "+
                       "           select SUM(InStockNum+RBNum+ZCNum) as Num from T_MaterStock where MaterNo='" + QNo + "' "+
                       "           union all  " +
                       "         select SUM(StockNum) as Num from T_ProductStockInfo where MaterNo='" + QNo + "' "+
                       "        ) aa) ";
            }
            else if (sFlag == "14-1")  //判断准备发料数量是否大库存数量：库存数量
            {
                sSQL = " select InMan from T_MaterStock where MaterBatch='" + Kind + "' and InStockNum >= " + QT + " ";
            }
            else if (sFlag == "14-2")  //判断准备发料数量是否大库存数量：暂存仓数量
            {
                sSQL = " select InMan from T_MaterStock where MaterBatch='" + Kind + "' and ZCNum >= " + QT + " ";
            }
            else if (sFlag == "14-3")  //判断准备发料数量是否大库存数量：让步接收受理
            {
                sSQL = " select InMan from T_MaterStock where MaterBatch='" + Kind + "' and RBNum >= " + QT + " ";
            }
            else if (sFlag == "15-1-1")  // 判断是否还有退货数量  -- IQC检验不合格的
            {
                sSQL = " select InMan from T_MaterStock where MaterBatch='" + Kind + "' and BadNum >= (" + QT + "-" + QNo + ")  ";
            }
            else if (sFlag == "15-1-2")  // 判断是否还有退货数量：退仓库 入库合格数量
            {
                //sSQL = " select * from ( " +
                //       " select a.InStockNum,isnull(b.ReturnNum,0) as ReturnNum from T_MaterStock a " +
                //       " left join (select PNo,SUM(RStock) as ReturnNum from T_ReturnMaterInfo where ReturnKind='仓库不合格' and  PNo='" + Kind + "' group by PNo) b on a.MaterBatch=b.PNo " +
                //       " where a.MaterBatch='" + Kind + "' " +
                //       " ) aa where aa.InStockNum >= aa.ReturnNum + (" + QT + "-" + QNo + ")  ";

                sSQL = " select InMan from T_MaterStock where MaterBatch='" + Kind + "' and InStockNum >= (" + QT + "-" + QNo + ")  ";
            }
            else if (sFlag == "15-1-3")  // 判断是否还有退货数量：退仓库 暂存仓数量
            {
                sSQL = " select InMan from T_MaterStock where MaterBatch='" + Kind + "' and ZCNum >= (" + QT + "-" + QNo + ")  ";
            }
            else if (sFlag == "15-1-4")  // 判断是否还有退货数量：退仓库让步接收数量
            {
                sSQL = " select InMan from T_MaterStock where MaterBatch='" + Kind + "' and RBNum >= (" + QT + "-" + QNo + ")  ";
            }
            else if (sFlag == "15-2")  // 判断是否已审核
            {
                sSQL = " select InMan from T_ReturnMaterInfo where ReturnNo = '" + Kind + "' and Status= '已审核' ";
            }
            else if (sFlag == "17-1")  // 判断产品编码是否存在
            {
                sSQL = " select InMan from T_MaterInfo where MaterNo= '" + Kind + "' and MaterKind in ('成品','半成品','组件') and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "20-1")  // 判断产品编码是否存在
            {
                sSQL = " select InMan from T_MaterStock where MaterBatch= '" + Kind + "' and (InStockNum+RBNum+ZCNum) > " + QT + " and CompanyNo= '" + sComp + "' ";
            }






            DataTable dt = DBHelper.GetDataTable(sSQL);
            if (dt.Rows.Count > 0)
            {
                sStatus = "Y";
            }
            else
            {
                sStatus = "N";
            }


            return sStatus;
        }


        /// <summary>
        /// 获取询价信息
        /// </summary>
        /// <param name="PPNo"></param>
        /// <param name="CustNo"></param>
        /// <param name="CustName"></param>
        /// <param name="BDate"></param>
        /// <param name="EDate"></param>
        /// <param name="Status"></param>
        /// <param name="BDate"></param>
        /// <param name="EDate"></param>
        /// <param name="sInMan"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetPurchProject(string PPNo, string Item, string CustNo, string CustName, string BDate, string EDate, string Status, string sCheck, int Row, int num, string sInMan, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string sChStr = string.Empty;
            string sNum = string.Empty;
            DataTable sdt = new DataTable();

            if (sCheck == "1")  // 表示需要查询历史单据
            {
                sChStr = "%";
            }
            else
            {
                sChStr = "1%";
            }

            SqlParameter[] parameters = {
					new SqlParameter("@lnNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnItem", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnStatus", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnTJOne", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnTJTwo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnTJThree", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
					new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,10),
					new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
            parameters[0].Value = PPNo;
            parameters[1].Value = Item;
            parameters[2].Value = sComp;
            parameters[3].Value = CustNo;
            parameters[4].Value = CustName;
            parameters[5].Value = Status;
            parameters[6].Value = BDate;
            parameters[7].Value = EDate;
            parameters[8].Value = sChStr;
            parameters[9].Value = Row;
            parameters[10].Value = num;
            parameters[11].Value = sInMan;
            parameters[12].Value = sFlag;
            parameters[13].Value = "";

            DataSet DS = DBHelper.RunProcedureForDS("P_GetPurchFlowInfoForPage", parameters);
            sdt = DS.Tables[0];

            return sdt;
        }

        // 查询物料控制相关数据
        public static DataTable GetMaterFlowInfo(string PONo, string MNo, string MName, string BDate, string EDate, string Status, string A, string B, string C, string D, int Row, int num, string sInMan, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string sChStr = string.Empty;
            string sNum = string.Empty;
            DataTable sdt = new DataTable();

            if (sFlag == "50-22")
            {
                sSQL = " select * from T_QuestionHead where PPNo = '" + PONo + "'  ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "50-3")
            {
                sSQL = " select COUNT(*) as C from T_PRInfo where QsNo='" + PONo + "' and MaterVer='" + A + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sNum = sdt.Rows[0]["C"].ToString();
                }
                else
                {
                    sNum = "0";
                }

                sSQL = " select  '" + sNum + "' as NumCount, *,convert(char(16),InDate,120) as InDate2 from T_PRInfo where QsNo='" + PONo + "' and MaterVer='" + PONo + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else
            {
                SqlParameter[] parameters = {
					new SqlParameter("@lnNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnStatus", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
					new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,10),
					new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
                parameters[0].Value = PONo;
                parameters[1].Value = MNo;
                parameters[2].Value = MName;
                parameters[3].Value = BDate;
                parameters[4].Value = EDate;
                parameters[5].Value = Status;
                parameters[6].Value = A;
                parameters[7].Value = B;
                parameters[8].Value = C;
                parameters[9].Value = D;
                parameters[10].Value = Row;
                parameters[11].Value = num;
                parameters[12].Value = sInMan;
                parameters[13].Value = sFlag;
                parameters[14].Value = "";

                DataSet DS = DBHelper.RunProcedureForDS("P_GetMaterFlowInfoForPage", parameters);
                sdt = DS.Tables[0];
            }


            return sdt;
        }



        // 插入出库信息
        public static string SaveProductOutInfo(string CKNo, string PPNo, string Num, string InMan, string sComp, string sFlag)
        {
            string sBStr = "执行无数据";
            string sBMgs = string.Empty;
            DataTable sdt = new DataTable();

            SqlParameter[] parameters = {
					new SqlParameter("@lnNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnPPNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnNum", SqlDbType.NVarChar,20),
					new SqlParameter("@lnMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnFlag", SqlDbType.NVarChar,10),
					new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
            parameters[0].Value = CKNo;
            parameters[1].Value = PPNo;
            parameters[2].Value = Num;
            parameters[3].Value = InMan;
            parameters[4].Value = sFlag;
            parameters[5].Value = "";

            DataSet DS = DBHelper.RunProcedureForDS("P_SaveProductOutInfo", parameters);
            sdt = DS.Tables[0];
            if (sdt.Rows.Count > 0)
            {
                sBMgs = sdt.Rows[0]["BackInfo"].ToString();
                if (sBMgs == "成功")
                {
                    sBStr = "1";
                }
                else
                {
                    sBStr = sBMgs;
                }
            }

            return sBStr;
        }



        // 保存物料相关的操作信息
        public static string AddEditSHInfo(string RNo, string DNo, string PONo, string POItem, string DNum, string OldNum, string Num, string ZCNum, string RBNum, string FXNum, string EffDate, string SHNo, string C1, string C2, string C3, string C4, string C5, string CQT, string sComp, string InMan, string InName, string Remark, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sStr = string.Empty;
            string sStrs = string.Empty;
            string sStrss = string.Empty;
            string sstr = string.Empty;
            string sNum = string.Empty;
            string pNum = string.Empty;
            string zNum = string.Empty;
            string sReceiveNo = string.Empty;
            double cNum = 0;
            double sSNum = 0;
            double InNum = 0;
            double IQCRNum = 0;
            double fZCNum = 0;

            string sSt = string.Empty;
            DataTable sdt = new DataTable();



            if (sFlag == "1")  // 新增  
            {
                sSQL = " select PurchNum,OverNum from T_PurchaseItemInfo  where PurchNo='" + PONo + "' and PurchItem='" + POItem + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sNum = sdt.Rows[0]["OverNum"].ToString();  // 已收货数量
                    pNum = sdt.Rows[0]["PurchNum"].ToString(); // 采购单数量

                    sSNum = double.Parse(sNum) + double.Parse(Num);
                    //20218112:cNum = sSNum - double.Parse(pNum);  // 已收货数量（含正在收货数量） -  采购数量，如果大于 0 ，说明超收了，超收部分转入暂存仓
                }
                else
                {
                    sSNum = double.Parse(Num);
                    //20218112:cNum = double.Parse(Num) - double.Parse(pNum);  // 正在收货数量 -  采购数量，如果大于 0 ，说明超收了，超收部分转入暂存仓
                }


                // 20210812:在收货环节，不记录是否超收，在IQC环节，检验OK后再判断。因此这里统一给超收的变量赋值 0
                cNum = 0;
                InNum = double.Parse(Num);
                //if (cNum >= 0){
                //    InNum = double.Parse(Num) - cNum;   // 如果大于 0 ，说明超收了，超收部分转入暂存仓
                //} 
                //else{
                //    cNum = 0;
                //    InNum = double.Parse(Num);
                //}

                sSQL = " insert into T_MaterReceive(ReceiveNo,DNo,PurchaseNo,PurchaseItem,MaterNo,MaterVer,MaterName,MaterSpec,ReceiveKind,PurchNum,DeliveryNum,GetNum,ZCNum,NumUnit,SupplierNo,SupplierEn,Price,DeliveryMan,DeliveryDate,ReceiveMan,ReceiveDate, " +
                       " EffDate,ConfirmStatus,ReceiveBillNo,Location,CHOne,CHTwo,CHThree,CHFour,CHFive,CHQT,CompanyNo,InMan,Remark) " +
                       " select '" + RNo + "', '" + DNo + "',a.PurchNo,a.PurchItem,a.MaterNo,a.MaterVer,a.MaterName,a.MaterSpec,'PO收货',PurchNum,'" + DNum + "','" + InNum + "','" + cNum + "',a.MaterUnit,b.SupplierNo,b.SupplierEn,a.PurchPrice, " +
                       " a.SupplierMan,a.ArrivalDate,'" + InName + "',convert(char(10),Getdate(),120),'" + EffDate + "','待IQC检验','" + SHNo + "','原材料仓','" + C1 + "','" + C2 + "','" + C3 + "','" + C4 + "','" + C5 + "','" + CQT + "','" + sComp + "','" + InMan + "','" + Remark + "' " +
                       " from T_PurchaseItemInfo a left join T_PurchaseInfo b on a.PurchNo=b.PurchNo where a.PurchNo='" + PONo + "' and a.PurchItem='" + POItem + "' ";

                sSQLs = " update T_PurchaseItemInfo set OverNum='" + sSNum + "' where PurchNo='" + PONo + "' and PurchItem='" + POItem + "' ";
            }
            else if (sFlag == "2")  // 修改  ？？
            {
                sSQL = " select Max(PurchNum) as PurchNum,sum(GetNum+ZCNum) as OverNum from T_MaterReceive where PurchaseNo='" + PONo + "' and PurchaseItem='" + POItem + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sNum = sdt.Rows[0]["OverNum"].ToString();  // 已收货数量
                    pNum = sdt.Rows[0]["PurchNum"].ToString(); // 采购单数量

                    sSNum = double.Parse(sNum) + (double.Parse(Num) - double.Parse(OldNum));
                    //20218112:cNum = sSNum - double.Parse(pNum);  // 已收货数量（含正在收货数量） -  采购数量，如果大于 0 ，说明超收了，超收部分转入暂存仓
                }
                // 20210812:在收货环节，不记录是否超收，在IQC环节，检验OK后再判断。因此这里统一给超收的变量赋值 0
                cNum = 0;
                InNum = double.Parse(Num);
                //if (cNum >= 0)
                //{
                //    InNum = double.Parse(Num) - cNum;   // 如果大于 0 ，说明超收了，超收部分转入暂存仓
                //}
                //else
                //{
                //    cNum = 0;
                //    InNum = double.Parse(Num);
                //}


                sSQL = " update T_MaterReceive set DNo='" + DNo + "',DeliveryNum='" + DNum + "',GetNum='" + InNum + "',ZCNum='" + cNum + "',EffDate='" + EffDate + "',ReceiveBillNo='" + SHNo + "', "+
                       " CHOne='" + C1 + "',CHTwo='" + C2 + "',CHThree='" + C3 + "',CHFour='" + C4 + "',CHFive='" + C5 + "',CHQT='" + CQT + "',Remark='" + Remark + "' " +
                       " where ReceiveNo='" + RNo + "' ";

                sSQLs = " update T_PurchaseItemInfo set OverNum='" + sSNum + "' where PurchNo='" + PONo + "' and PurchItem='" + POItem + "' ";
            }
            else if (sFlag == "3")  // 删除记录
            {
                sSQL = " SELECT GetNum+ZCNum as OverNum,PurchNum FROM T_MaterReceive where ReceiveNo='" + RNo + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0){
                    sNum = sdt.Rows[0]["OverNum"].ToString();  // 已收货数量
                }
                else{
                    sNum = "0";
                }

                sSQL = " delete T_MaterReceive  where ReceiveNo='" + RNo + "' ";
                sSQLs = " update T_PurchaseItemInfo set OverNum=OverNum-" + sNum + " where PurchNo='" + PONo + "' and PurchItem='" + POItem + "' ";
            }
            else if (sFlag == "4")  // 插入IQC检验信息 （前台传：DNum: 实收数量, OldNum: 不合格数量, Num: 合格数量, ZCNum: 暂存仓数据, EffDate: 让步）  T_InspectInfo表  IQCInspectNum:存放让步接收数 
            {
                // 获取采购数量
                sSQL = " SELECT PurchNum FROM T_MaterReceive where ReceiveNo='" + RNo + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0) {
                    pNum = sdt.Rows[0]["PurchNum"].ToString();  // 已收货数量
                }
                else {
                    pNum = "0";
                }

                // 插入库存信息 前台传：DNum: 实收数量；OldNum: 不合格数量；Num: 合格数量； ZCNum: 暂存仓数据；EffDate: 让步
                // 计算库存
                if (double.Parse(EffDate) > 0)  // 如果让步接收数量>0 ,说明没有暂存仓数量，因此，合格数量=库存数量
                {
                    InNum = double.Parse(Num);
                }
                else{
                    if (double.Parse(Num) > double.Parse(pNum)) // 如果合格数量大于采购数量，则采购数量=库存数量，多余部分为暂存仓数量，前台已计算，这里就不用记录了
                    {
                        InNum = double.Parse(pNum);
                    }
                    else{
                        InNum = double.Parse(Num);
                    }
                }


                IQCRNum = double.Parse(Num) + double.Parse(EffDate);  // IQC检验数量=合格数量+让步接收数量

                sSQL = " insert into T_InspectInfo(InIQCNo,ReceiveNo,IQCNo,KeyNo,MaterNo,MaterVer,MaterName,MaterUnit,SupplierNo,EffDate,IQCReceiveNum,SampleNum,IQCInspectNum,GoodNum,ZCNum,BadNum,BadDesc,DoWay,SamplingType,Status,CompanyNo,InMan,Remark) " +
                       " select '" + DNo + "',ReceiveNo,'" + DNo + "',DNo,MaterNo,MaterVer,MaterName,NumUnit,SupplierNo,EffDate,'" + DNum + "','" + IQCRNum + "','" + EffDate + "','" + Num + "','" + ZCNum + "','" + OldNum + "','" + SHNo + "','" + PONo + "'," +
                       " '" + POItem + "', 'IQC已检验','" + sComp + "','" + InMan + "','" + Remark + "' " +
                       " from T_MaterReceive  where ReceiveNo='" + RNo + "' ";


                sSQLs = " update T_MaterReceive set SendToIQCMan='" + InMan + "',SendToIQCDate=convert(char(16),Getdate(),120),ConfirmStatus='IQC已检验' where  ReceiveNo='" + RNo + "' ";


                sStr = " insert into T_MaterStock(MaterBatch,InIQCNo,ReceiveNo,MaterNo,MaterVer,MaterName,MaterUnit,SupplierNo,EffDate,IQCNum,InStockNum,ZCNum,RBNum,BadNum,ReceiveDate,Status,CompanyNo,InMan,Remark) " +
                       " select '" + DNo + "','" + DNo + "',ReceiveNo,MaterNo,MaterVer,MaterName,NumUnit,SupplierNo,EffDate,'" + IQCRNum + "'," + InNum + ",'" + ZCNum + "','" + EffDate + "','" + OldNum + "', " +
                       " convert(char(16),Getdate(),120),'已入库','" + sComp + "','" + InMan + "','" + Remark + "' " +
                       " from T_MaterReceive where ReceiveNo='" + RNo + "' ";

                // 针对IQC不良，QC检验不合格就更新，允许再次收货了。
                sStrs = " update T_PurchaseItemInfo set OverNum=case when OverNum>" + OldNum + " then OverNum-" + OldNum + " else 0 end,Remark='不合格数量：'+'" + OldNum + "' " +
                       " where LTRIM(RTRIM(PurchNo))+LTRIM(RTRIM(PurchItem)) in (SELECT LTRIM(RTRIM(PurchaseNo))+LTRIM(RTRIM(PurchaseItem)) FROM T_MaterReceive WHERE ReceiveNo='" + RNo + "') ";

            }
            else if (sFlag == "5")  // 修改IQC检验信息  前台传：DNum: 实收数量, OldNum: 不合格数量, Num: 合格数量, ZCNum: 暂存仓数据, EffDate: 让步
            {

                // 获取采购数量
                sSQL = " SELECT PurchNum FROM T_MaterReceive where ReceiveNo='" + RNo + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0) {
                    pNum = sdt.Rows[0]["PurchNum"].ToString();  // 已收货数量
                }
                else{
                    pNum = "0";
                }

                // 计算库存
                if (double.Parse(EffDate) > 0)  // 如果让步接收数量>0 ,说明没有暂存仓数量，因此，合格数量=库存数量
                {
                    InNum = double.Parse(Num);
                }
                else {
                    if (double.Parse(Num) > double.Parse(pNum)) // 如果合格数量大于采购数量，则采购数量=库存数量，多余部分为暂存仓数量，前台已计算，这里就不用记录了
                    {
                        InNum = double.Parse(pNum);
                    }
                    else {
                        InNum = double.Parse(Num);
                    }
                }

                sSQL = " SELECT BadNum FROM T_InspectInfo  where InIQCNo='" + DNo + "' and ReceiveNo='" + RNo + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0) {
                    sNum = sdt.Rows[0]["BadNum"].ToString();  //不良数量

                    cNum = double.Parse(sNum) - double.Parse(OldNum); 
                }
                else{
                    sNum = "0";
                }

                IQCRNum = double.Parse(Num) + double.Parse(EffDate);  // IQC检验数量=合格数量+让步接收数量

                sSQL = " update T_InspectInfo set IQCReceiveNum='" + DNum + "',SampleNum='" + IQCRNum + "',ZCNum='" + ZCNum + "',GoodNum='" + Num + "',BadNum='" + OldNum + "',IQCInspectNum ='" + EffDate + "', " +
                       " DoWay='" + PONo + "',BadDesc='" + SHNo + "',SamplingType='" + POItem + "',Remark='" + Remark + "' " +
                       " where InIQCNo='" + DNo + "' and ReceiveNo='" + RNo + "'  ";

                sStr = " update T_MaterStock  set IQCNum='" + IQCRNum + "', InStockNum=" + InNum + ",ZCNum='" + ZCNum + "',RBNum='" + EffDate + "',BadNum='" + OldNum + "' " +
                       " where MaterBatch='" + DNo + "' ";

                // 
                sStrs = " update T_PurchaseItemInfo set OverNum= OverNum+(" + cNum + "),Remark='IQC修改不合格数量：'+'" + cNum + "' " +
                       " where LTRIM(RTRIM(PurchNo))+LTRIM(RTRIM(PurchItem)) in (SELECT LTRIM(RTRIM(PurchaseNo))+LTRIM(RTRIM(PurchaseItem)) FROM T_MaterReceive WHERE ReceiveNo='" + RNo + "') ";

            }
            else if (sFlag == "6")  //  删除IQC检验信息  
            {
                sSQL = " SELECT BadNum FROM T_InspectInfo  where InIQCNo='" + DNo + "' and ReceiveNo='" + RNo + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0){
                    sNum = sdt.Rows[0]["BadNum"].ToString();  //不良数量
                }
                else {
                    sNum = "0";
                }


                sSQL = " delete T_InspectInfo  where InIQCNo='" + DNo + "' and ReceiveNo='" + RNo + "' ";

                sStr = " delete T_MaterStock  where MaterBatch='" + DNo + "' ";

                sSQLs = " update T_MaterReceive set  ConfirmStatus='待IQC检验' where  ReceiveNo='" + RNo + "' ";

                // 回退采购表的已收货数量
                sStrs = " update T_PurchaseItemInfo set OverNum=OverNum+" + sNum + ",Remark='IQC删除回退数量：'+'" + sNum + "' " +
                       " where LTRIM(RTRIM(PurchNo))+LTRIM(RTRIM(PurchItem)) in (SELECT LTRIM(RTRIM(PurchaseNo))+LTRIM(RTRIM(PurchaseItem)) FROM T_MaterReceive WHERE ReceiveNo='" + RNo + "') ";

            }
            else if (sFlag == "79999")  // 插入出库单信息 ，暂时不用，由上面的储存过程插入
            {
                sSQL = " select OutQty+" + DNum + " as OverNum,Qty from T_PRInfo where PRNo='" + PONo + "' and PRItem='" + POItem + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sNum = sdt.Rows[0]["OverNum"].ToString();  // 已出库数量
                    pNum = sdt.Rows[0]["Qty"].ToString(); // PR单数量

                    if (double.Parse(pNum) <= double.Parse(sNum)) {
                        sSQLs = " update T_PRInfo set OutQty=OutQty+" + DNum + ",Status='订单完成' where PRNo='" + PONo + "' and PRItem='" + POItem + "' ";
                    }
                    else {
                        sSQLs = " update T_PRInfo set OutQty=OutQty+" + DNum + ",Status='部分已出库' where PRNo='" + PONo + "' and PRItem='" + POItem + "' ";
                    }
                }
                // 20210608 :这里使用PRInfo的信息作为出库信息有问题，一个PR对应多个报价，而且报价物料编码不一样，所以应该按报价物料来出库
                sSQL = " insert into T_ProductOutInfo(POutNo,InNo,CustPN,PONO,ObjectNo,SerielNo,MaterNo,MaterVer,MaterName,Num,OutNum,OutDate,AcceptMan,SaleOrderNo,OrderItemNo,Status,CompanyNo,InMan,Remark) " +
                       " select '" + RNo + "',QsNo,CustPN,PONO,Quote,CType,'',MaterVer,'',Qty,'" + DNum + "',convert(char(16),Getdate(),120),'" + InName + "',PRNo,PRItem, " +
                       " '已出库','" + sComp + "','" + InMan + "','" + Remark + "' " +
                       " from T_PRInfo  where PRNo='" + PONo + "' and PRItem='" + POItem + "' ";



            }
            else if (sFlag == "7-2")  // 更新出库信息：上传出库检验文件  DNo: sDocName, PONo: sDocPath,  ,
            {
                sSQL = " update T_ProductOutInfo set DocName='" + DNo + "',DocPath='" + PONo + "' where POutNo='" + RNo + "' ";
            }
            else if (sFlag == "8")  // 修改出库单信息，20210608 :暂时不用，直接删除
            {
                sSQL = " select OutNum from T_ProductOutInfo where POutNo='" + RNo + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                var sendQty = sdt.Rows[0]["OutNum"].ToString();

                sSQL = " select OutQty,Qty from T_PRInfo where PRNo='" + PONo + "' and PRItem='" + POItem + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sNum = sdt.Rows[0]["OutQty"].ToString();  // 已出库数量
                    InNum = double.Parse(sNum) + (double.Parse(DNum) - double.Parse(sendQty)); //  准备出库数量
                    pNum = sdt.Rows[0]["Qty"].ToString(); // PR单数量

                    if (double.Parse(pNum) <= InNum)
                    {
                        sSQLs = " update T_PRInfo set OutQty= " + InNum + ",Status='订单完成' where PRNo='" + PONo + "' and PRItem='" + POItem + "' ";
                    }
                    else
                    {
                        sSQLs = " update T_PRInfo set OutQty= " + InNum + ",Status='部分已出库' where PRNo='" + PONo + "' and PRItem='" + POItem + "' ";
                    }
                }

                sSQL = " update T_ProductOutInfo set OutNum='" + DNum + "' where POutNo='" + RNo + "' ";
            }
            else if (sFlag == "9")  // 删除出库单信息
            {
                sdt = DBHelper.GetDataTable("select InMan from T_ProductOutInfo where PPNo='" + DNo + "' ");
                if (sdt.Rows.Count <= 1) // 说明这个PR单号+项次只有一笔出库记录，也就是当前删除这笔 
                {
                    sSQLs = " update T_PRPrice set OutQty=OutQty-" + DNum + ",Status='PR报价已审核' where PPNo='" + DNo + "' ";
                }
                else  // 说明这个PR单号还有其他的出库记录
                {
                    sSQLs = " update T_PRPrice set OutQty=OutQty-" + DNum + ",Status='部分已出库' where PPNo='" + DNo + "' ";
                }

                sStrs = " update T_MaterStock set InStockNum=b.StockOutNum from T_MaterStock a join T_MaterStockOutMX b on a.MaterBatch=b.MaterBatch "+
                        " where b.OutNo='" + RNo + "' ";  // 更新收货库存表，直接更新到入库库存数量

                sStrss = " update T_ProductStockInfo set StockNum=a.StockNum+b.StockOutNum,OutNum=a.OutNum-b.StockOutNum from T_ProductStockInfo a "+
                         " join T_MaterStockOutMX b on a.InNo=b.MaterBatch  where b.OutNo='" + RNo + "' ";  // 更新生产组装入库表，直接更新到入库库存数量

                sSQL = " delete T_MaterStockOutMX  where OutNo='" + RNo + "' ";  // 删除明细
                sStr = " delete T_ProductOutInfo  where POutNo='" + RNo + "' ";  // 删除出库表

            }
            else if (sFlag == "10")  // 保存退货信息 
            {
                sSQL = " insert into T_ReturnMaterInfo(ReturnNo,PNo,PItem,MaterNo,MaterVer,MaterName,ReturnKind,RStock,RZCNum,RRBNum,Num,ReturnNum,FXNum,SupplierNo,ReturnCause,ReturnDate,AudMan,AudDate,Status,InMan,Remark) " +
                        " select '" + RNo + "',MaterBatch,ReceiveNo,MaterNo,MaterVer,MaterName,'" + PONo + "','" + DNum + "','" + ZCNum + "','" + RBNum + "',BadNum,'" + Num + "','" + FXNum + "',SupplierNo,'" + EffDate + "', "+
                        " convert(char(16),getdate(),120),'','','待审核','" + InMan + "','" + Remark + "' " +
                        " from T_MaterStock where MaterBatch='" + DNo + "' ";

                // 扣减数量  PONo, string POItem, string DNum, string OldNum, string Num, string ZCNum, string RBNum, string FXNum,

                if (PONo == "IQC不合格")
                {
                    sSQLs = " update T_MaterStock set RBNum=RBNum+" + FXNum + " ,BadNum=BadNum-" + Num + " - " + FXNum + "  where MaterBatch='" + DNo + "' "; 
                }
                else // 退仓库的不合格数量
                {
                    sSQLs = " update T_MaterStock set InStockNum=InStockNum-" + DNum + ",ZCNum=ZCNum-" + ZCNum + ",RBNum=RBNum-" + RBNum + " where MaterBatch='" + DNo + "' "; 
                }


            }
            else if (sFlag == "11")  // 修改退货信息  //  20210821:暂时不需要修改，退错了，删除重新退货
            {
                sSQL = " update T_ReturnMaterInfo set ReturnKind='" + PONo + "',ReturnNum='" + Num + "',ReturnAmount='" + ZCNum + "',ReturnCause='" + EffDate + "',Remark='" + Remark + "' where ReturnNo='" + RNo + "' ";
            }
            else if (sFlag == "12")  // 退货审核
            {
                if (SHNo == "审核通过")
                {
                    sSQL = " update T_ReturnMaterInfo set Status='已审核',AudMan='" + InMan + "',AudDate=convert(char(16),getdate(),120),AudDesc='" + EffDate + "' where ReturnNo='" + RNo + "' "; 
                }
                else
                {
                    sSQL = " update T_ReturnMaterInfo set Status='审核不通过' where ReturnNo='" + RNo + "' ";
                }
            }
            else if (sFlag == "13")  // 删除退料记录
            {
                string sStock = string.Empty;
                string sZCNum = string.Empty;
                string sRBNum = string.Empty;

                // 回退数量
                sSQL = " select RStock,RZCNum,RRBNum,ReturnNum,FXNum,PItem,ReturnKind from T_ReturnMaterInfo where ReturnNo='" + RNo + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sStock = sdt.Rows[0]["RStock"].ToString();  // 退库存数量
                    sZCNum = sdt.Rows[0]["RZCNum"].ToString();  // 退暂存仓数量
                    sRBNum = sdt.Rows[0]["RRBNum"].ToString();  // 退让步接收数
                    pNum = sdt.Rows[0]["FXNum"].ToString(); // 再次放行数量：让步接收数量
                    zNum = sdt.Rows[0]["ReturnNum"].ToString(); // IQC不良数量
                    sReceiveNo = sdt.Rows[0]["PItem"].ToString(); //  PItem:收货编号
                }
                else
                {
                    sStock = "0";
                    sZCNum = "0";
                    sRBNum = "0";
                    pNum = "0";
                    zNum = "0";
                }

                if (sdt.Rows[0]["ReturnKind"].ToString() == "IQC不合格")
                {
                    sSQLs = " update T_MaterStock set BadNum=BadNum+" + zNum + " + " + pNum + " ,RBNum=RBNum-" + pNum + " where MaterBatch='" + DNo + "' "; 
                }
                else
                {
                    sSQLs = " update T_MaterStock set InStockNum=InStockNum+" + sStock + ",ZCNum=ZCNum+" + sZCNum + ",RBNum=RBNum+" + sRBNum + "  where MaterBatch='" + DNo + "' "; 
                }

                sSQL = " delete T_ReturnMaterInfo where ReturnNo='" + RNo + "' ";

            }
            else if (sFlag == "14") // 14 插入发料记录   string DNum,   string ZCNum, string RBNum, 
            {

                InNum = double.Parse(DNum) + double.Parse(ZCNum) + double.Parse(RBNum);

                sSQL = " insert into T_MaterFillInfo(FillNo,MaterBatch,ObjectNo,MaterNo,MaterVer,MaterName,FNum,StockNum,FillNum,RBNum,FRBNum,ZCNum,FZCNum,Location,Factory,MaterUnit,Status,InMan,Remark) " +
                       " select '" + RNo + "',MaterBatch,'" + PONo + "',MaterNo,MaterVer,MaterName,'" + InNum + "',InStockNum,'" + DNum + "',RBNum,'" + RBNum + "',ZCNum,'" + ZCNum + "',StorageLocation,Factory,MaterUnit,'','" + InMan + "','" + Remark + "' " +
                       " from T_MaterStock where MaterBatch='" + DNo + "' ";

                sSQLs = " update T_MaterStock set InStockNum=InStockNum-" + DNum + ",RBNum=RBNum-" + RBNum + ",ZCNum=ZCNum-" + ZCNum + "  where MaterBatch='" + DNo + "' ";


            }
            else if (sFlag == "15")  // 20210825：暂时不能修改，发料错误，直接删除重新发料即可 修改 ,如果数量改少，直接加到库存数即可。如果数量修改比原来多，从库存数量一直往下扣减。 
            {
                double nnum = 0;
                double DDNum = 0;

                if (double.Parse(DNum) - double.Parse(OldNum) > 0) // 说明修改数量比原来发料数量大
                {
                    DDNum = double.Parse(DNum) - double.Parse(OldNum);  //这个是新要扣减库存的数量

                    // 总库存由3部分组成，先扣库存数 InNum，再扣让步接收数 sSNum，最后扣暂存数 cNum
                    sSQL = " select InStockNum,RBNum,ZCNum from T_MaterStock where MaterBatch='" + DNo + "' ";
                    sdt = DBHelper.GetDataTable(sSQL);
                    if (sdt.Rows.Count > 0)
                    {
                        sNum = sdt.Rows[0]["InStockNum"].ToString(); // 库存数
                        pNum = sdt.Rows[0]["RBNum"].ToString(); // 让步接收数
                        zNum = sdt.Rows[0]["ZCNum"].ToString(); // 暂存数
                    }

                    if (double.Parse(sNum) > 0)  // 说明有 库存数，优先扣减
                    {
                        if (double.Parse(sNum) - DDNum >= 0) // 说明库存数够本次的发料数
                        {
                            InNum = DDNum;  // 扣减库存数量
                            sSNum = 0; // 扣减让步接收数
                            cNum = 0;  // 扣减  暂存数
                        }
                        else  // 说明库存数量不够扣减，则需要扣减让步接收数和暂存数
                        {
                            InNum = double.Parse(sNum); // 扣减库存数量
                            nnum = DDNum - double.Parse(sNum);  //  还有些发料数量，需要让步接收数量或暂存数量扣减了
                            if (double.Parse(pNum) - nnum >= 0)  // 说明让步接收数量够扣减剩下的发料数量
                            {
                                sSNum = nnum; // 扣减让步接收数
                                cNum = 0;  // 扣减  暂存数
                            }
                            else // 说明让步接收数还不够扣减，需要扣减到暂存数
                            {
                                sSNum = double.Parse(pNum); // 扣减让步接收数
                                cNum = nnum - double.Parse(pNum);  // 扣减  暂存数
                            }
                        }
                    }
                    else // 说明没有库存数量，则需要扣减让步接收数和暂存数
                    {
                        InNum = 0; // 扣减库存数量
                        if (double.Parse(pNum) - DDNum >= 0)  // 说明让步接收数量够扣减剩下的发料数量
                        {
                            sSNum = DDNum; // 扣减让步接收数
                            cNum = 0;  // 扣减  暂存数
                        }
                        else // 说明让步接收数还不够扣减，需要扣减到暂存数
                        {
                            sSNum = double.Parse(pNum); // 扣减让步接收数
                            cNum = DDNum - double.Parse(pNum);  // 扣减  暂存数
                        }

                    }
                }
                else  // 修改后数量比修改前小。把相差的库存加回库存表
                {
                    InNum = double.Parse(DNum) - double.Parse(OldNum); // 注意别减错了，这里需要出来负数
                    sSNum = 0;
                    cNum = 0;
                }


                if (InNum < 0) {
                    sSQLs = " update T_MaterStock set InStockNum=InStockNum-(" + InNum + "),RBNum=RBNum-" + sSNum + ",ZCNum=ZCNum-" + cNum + "  where MaterBatch='" + DNo + "' "; 
                    sSQL = " update T_MaterFillInfo set FNum='" + DNum + "',FillNum=FillNum+(" + InNum + "), ObjectNo='" + PONo + "',Remark='" + EffDate + "' where FillNo='" + RNo + "' "; 
                }
                else {
                    sSQLs = " update T_MaterStock set InStockNum=InStockNum-" + InNum + ",RBNum=RBNum-" + sSNum + ",ZCNum=ZCNum-" + cNum + "  where MaterBatch='" + DNo + "' ";
                    sSQL = " update T_MaterFillInfo set FNum='" + DNum + "',FillNum=FillNum+" + InNum + ",FRBNum=FRBNum+" + sSNum + ",FZCNum=FZCNum+" + cNum + " , ObjectNo='" + PONo + "',Remark='" + EffDate + "' where FillNo='" + RNo + "' "; 

                }
            }
            else if (sFlag == "16")  //删除 发料记录
            {
                // 总库存由3部分组成，先扣库存数 InNum，再扣让步接收数 sSNum，最后扣暂存数 cNum
                sSQL = " select FillNum,FRBNum,FZCNum from T_MaterFillInfo where  FillNo='" + RNo + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sNum = sdt.Rows[0]["FillNum"].ToString(); // 库存数
                    pNum = sdt.Rows[0]["FRBNum"].ToString(); // 让步接收数
                    zNum = sdt.Rows[0]["FZCNum"].ToString(); // 暂存数
                }

                sSQL = " delete T_MaterFillInfo  where FillNo='" + RNo + "' ";

                sSQLs = " update T_MaterStock set InStockNum=InStockNum+" + sNum + ",RBNum=RBNum+" + pNum + ",ZCNum=ZCNum+" + zNum + "  where MaterBatch='" + DNo + "' ";

            }
            else if (sFlag == "17")  // 保存产品入库信息
            {
                sSQL = " insert into T_ProductStockInfo(InNo,ObjectNo,SerielNo,DeptNo,MaterNo,MaterVer,MaterName,MaterSpec,InNum,StockNum,MaterUnit,Status,CompanyNo,InMan,Remark) " +
                        " select '" + RNo + "','" + DNo + "','','',MaterNo,MaterVer,MaterName,MaterSpec,'" + Num + "','" + Num + "',MaterUnit,'','" + sComp + "','" + InMan + "','" + Remark + "' " +
                        " from T_MaterInfo where MaterNo='" + PONo + "' ";

            }
            else if (sFlag == "18")  // 修改产品入库信息
            {
                sSQL = " update T_ProductStockInfo set ObjectNo='" + DNo + "',InNum='" + Num + "',StockNum='" + Num + "',Remark='" + Remark + "' where InNo='" + RNo + "' ";

            }
            else if (sFlag == "19")  // 删除产品入库信息
            {
                sSQL = " delete T_ProductStockInfo where InNo='" + RNo + "' ";

            }
            else if (sFlag == "20")  //保存零件报废记录
            {

                double nnum = 0;
                // 总库存由3部分组成，先报废存数 InNum，再报废让步接收数 sSNum，最后报废暂存数 cNum
                sSQL = " select InStockNum,RBNum,ZCNum from T_MaterStock where MaterBatch='" + DNo + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sNum = sdt.Rows[0]["InStockNum"].ToString(); // 库存数
                    pNum = sdt.Rows[0]["RBNum"].ToString(); // 让步接收数
                    zNum = sdt.Rows[0]["ZCNum"].ToString(); // 暂存数
                }

                if (double.Parse(sNum) > 0)  // 说明有 库存数，优先报废
                {
                    if (double.Parse(sNum) - double.Parse(Num) >= 0) // 说明库存数够本次的发料数
                    {
                        InNum = double.Parse(Num);  // 报废库存数量
                        sSNum = 0; // 报废让步接收数
                        cNum = 0;  // 报废  暂存数
                    }
                    else  // 说明库存数量不够扣减，则需要报废让步接收数和暂存数
                    {
                        InNum = double.Parse(sNum); // 报废库存数量
                        nnum = double.Parse(Num) - double.Parse(sNum);  //  还有些发料数量，需要让步接收数量或暂存数量扣减了
                        if (double.Parse(pNum) - nnum >= 0)  // 说明让步接收数量够报废剩下的发料数量
                        {
                            sSNum = nnum; // 报废让步接收数
                            cNum = 0;  // 报废  暂存数
                        }
                        else // 说明让步接收数还不够报废，需要报废到暂存数
                        {
                            sSNum = double.Parse(pNum); // 报废让步接收数
                            cNum = nnum - double.Parse(pNum);  // 扣减  暂存数
                        }
                    }
                }
                else // 说明没有库存数量，则需要报废让步接收数和暂存数
                {
                    InNum = 0; // 报废库存数量
                    if (double.Parse(pNum) - double.Parse(Num) >= 0)  // 说明让步接收数量够报废剩下的发料数量
                    {
                        sSNum = double.Parse(DNum); // 报废让步接收数
                        cNum = 0;  // 报废  暂存数
                    }
                    else // 说明让步接收数还不够报废，需要报废到暂存数
                    {
                        sSNum = double.Parse(pNum); // 报废让步接收数
                        cNum = double.Parse(Num) - double.Parse(pNum);  // 扣减  暂存数
                    }

                }

                sSQL = " insert into T_MaterFaulty(BFNo,MaterBatch,MaterNo,MaterName,MaterSpec,Num,StockNum,FStockNum,RBNum,FRBNum,ZCNum,FZCNum,MaterUnit,FMan,FDate,Cause,Status,CompanyNo,InMan,Remark) " +
                        " select '" + RNo + "','" + DNo + "',MaterNo,MaterName,'','" + Num + "',InStockNum,'" + InNum + "',RBNum,'" + sSNum + "',ZCNum,'" + cNum + "',MaterUnit,'" + InName + "', " +
                        " convert(char(16),Getdate(),120),'" + EffDate + "','','" + sComp + "','" + InMan + "','" + Remark + "' " +
                        " from T_MaterStock where MaterNo='" + PONo + "' and MaterBatch='" + DNo + "' ";

                sSQLs = " update T_MaterStock set InStockNum=InStockNum-" + InNum + ",RBNum=RBNum-" + sSNum + ",ZCNum=ZCNum-" + cNum + "  where MaterBatch='" + DNo + "' ";
            }
            else if (sFlag == "21")  // 修改零件报废信息
            {

                double nnum = 0;
                double DDNum = 0;

                if (double.Parse(Num) - double.Parse(OldNum) > 0) // 说明修改数量比原来发料数量大
                {
                    DDNum = double.Parse(Num) - double.Parse(OldNum);  //这个是新要报废库存的数量

                    // 总库存由3部分组成，先报废库存数 InNum，再报废让步接收数 sSNum，最后扣暂存数 cNum
                    sSQL = " select InStockNum,RBNum,ZCNum from T_MaterStock where MaterBatch='" + DNo + "' ";
                    sdt = DBHelper.GetDataTable(sSQL);
                    if (sdt.Rows.Count > 0)
                    {
                        sNum = sdt.Rows[0]["InStockNum"].ToString(); // 库存数
                        pNum = sdt.Rows[0]["RBNum"].ToString(); // 让步接收数
                        zNum = sdt.Rows[0]["ZCNum"].ToString(); // 暂存数
                    }

                    if (double.Parse(sNum) > 0)  // 说明有 库存数，优先报废
                    {
                        if (double.Parse(sNum) - DDNum >= 0) // 说明库存数够本次的发料数
                        {
                            InNum = DDNum;  // 报废库存数量
                            sSNum = 0; // 报废让步接收数
                            cNum = 0;  // 报废  暂存数
                        }
                        else  // 说明库存数量不够报废，则需要报废让步接收数和暂存数
                        {
                            InNum = double.Parse(sNum); // 报废库存数量
                            nnum = DDNum - double.Parse(sNum);  //  还有些发料数量，需要让步接收数量或暂存数量扣减了
                            if (double.Parse(pNum) - nnum >= 0)  // 说明让步接收数量够报废剩下的发料数量
                            {
                                sSNum = nnum; // 报废让步接收数
                                cNum = 0;  // 报废  暂存数
                            }
                            else // 说明让步接收数还不够扣减，需要扣减到暂存数
                            {
                                sSNum = double.Parse(pNum); // 报废让步接收数
                                cNum = nnum - double.Parse(pNum);  // 报废  暂存数
                            }
                        }
                    }
                    else // 说明没有库存数量，则需要报废让步接收数和暂存数
                    {
                        InNum = 0; // 报废库存数量
                        if (double.Parse(pNum) - DDNum >= 0)  // 说明让步接收数量够报废剩下的发料数量
                        {
                            sSNum = DDNum; // 报废让步接收数
                            cNum = 0;  // 报废  暂存数
                        }
                        else // 说明让步接收数还不够报废，需要报废到暂存数
                        {
                            sSNum = double.Parse(pNum); // 报废让步接收数
                            cNum = DDNum - double.Parse(pNum);  // 报废  暂存数
                        }

                    }
                }
                else  // 修改后数量比修改前小。把相差的库存加回库存表
                {
                    InNum = double.Parse(Num) - double.Parse(OldNum); // 注意别减错了，这里需要出来负数
                    sSNum = 0;
                    cNum = 0;
                }

                if (InNum < 0)
                {
                    sSQLs = " update T_MaterStock set InStockNum=InStockNum-(" + InNum + "),RBNum=RBNum-" + sSNum + ",ZCNum=ZCNum-" + cNum + "  where MaterBatch='" + DNo + "' ";
                    sSQL = " update T_MaterFaulty set Num='" + Num + "',FStockNum=FStockNum+(" + InNum + "),Cause='" + EffDate + "' where BFNo='" + RNo + "' ";
                }
                else
                {
                    sSQLs = " update T_MaterStock set InStockNum=InStockNum-" + InNum + ",RBNum=RBNum-" + sSNum + ",ZCNum=ZCNum-" + cNum + "  where MaterBatch='" + DNo + "' ";
                    sSQL = " update T_MaterFaulty set Num='" + Num + "',FStockNum=FStockNum+" + InNum + ",FRBNum=FRBNum+" + sSNum + ",FZCNum=FZCNum+" + cNum + " ,Cause='" + EffDate + "' where BFNo='" + RNo + "' ";
                }

                

            }
            else if (sFlag == "22")  // 删除零件报废信息
            {
                // 总库存由3部分组成，先报废库存数 InNum，再扣让步接收数 sSNum，最后报废暂存数 cNum
                sSQL = " select FStockNum,FRBNum,FZCNum from T_MaterFaulty where  BFNo='" + RNo + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sNum = sdt.Rows[0]["FStockNum"].ToString(); // 库存数
                    pNum = sdt.Rows[0]["FRBNum"].ToString(); // 让步接收数
                    zNum = sdt.Rows[0]["FZCNum"].ToString(); // 暂存数
                }

                sSQL = " delete T_MaterFaulty where BFNo='" + RNo + "' ";

                sSQLs = " update T_MaterStock set InStockNum=InStockNum+" + sNum + ",RBNum=RBNum+" + pNum + ",ZCNum=ZCNum+" + zNum + "  where MaterBatch='" + DNo + "' ";
            }



            List<string> links = new List<string>();
            links.Add(sStrs);   // 9:先更新库存
            links.Add(sStrss);
            links.Add(sSQL);
            links.Add(sSQLs);
            links.Add(sStr);


            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }



        /// <summary>
        /// 产生装箱单号，并返回装箱单明细
        /// </summary>
        /// <param name="sPStr"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable CreatePackList(string sPStr, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string sMaxNo = string.Empty;
            string iFlag = string.Empty;
            string sNo = string.Empty;
            string sstr = string.Empty;
            int iMax = 0; int i = 0;

            if (sFlag == "1")  // 在出库页签打印，同事生成装箱单
            {
                // 产生PO单据
                sMaxNo = DBHelper.GetMaxNo("T_ProductOutInfo where convert(char(10),InDate,120)=convert(char(10),getdate(),120) ", "BoxNo");//   BOX2104110001 
                if (sMaxNo == "")
                {
                    sNo = "BOX" + CreateAllNo.CreateBillNo(2, 3) + "1";
                }
                else
                {
                    string sTemp = sMaxNo.Substring(9, 4);
                    iMax = int.Parse(sTemp) + 1;
                    int len = iMax.ToString().Length;
                    i = 4 - len;

                    sNo = "BOX" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
                }


                sSQL = " update T_ProductOutInfo set BoxNo='" + sNo + "' where CompanyNo= '" + sComp + "' and POutNo in " + sPStr + " ";

                List<string> links = new List<string>();

                links.Add(sSQL);  //   

                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }


                sSQL = " select POutNo,InNo,ObjectNo,SerielNo,MaterNo,CustPN,PONO,OutNum,OutDate,SaleOrderNo,OrderItemNo from T_ProductOutInfo where CompanyNo= '" + sComp + "' and POutNo in " + sPStr + "  order by CustPN,PONO ";
            }
            else if (sFlag == "2") // 在已生成的装箱单打印查询
            {
                sSQL = " select POutNo,InNo,ObjectNo,SerielNo,MaterNo,CustPN,PONO,OutNum,OutDate,SaleOrderNo,OrderItemNo from T_ProductOutInfo where CompanyNo= '" + sComp + "' and BoxNo = '" + sPStr + "'  order by CustPN,PONO ";

            }

            DataTable sdt = DBHelper.GetDataTable(sSQL);

            return sdt;

        }


        /// <summary>
        /// 删除装箱单信息：更新出库信息装箱单编号为空
        /// </summary>
        /// <param name="sOutNo"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string OPPackList(string sOutNo, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string iFlag = string.Empty;
            string sstr = string.Empty;

            sSQL = " update T_ProductOutInfo set BoxNo='' where CompanyNo= '" + sComp + "' and POutNo =  '" + sOutNo + "' ";

            List<string> links = new List<string>();
            links.Add(sSQL);


            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;

        }




        /// <summary>
        /// 根据物料编码获取库存批次
        /// </summary>
        /// <param name="MNo"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetMaterBatchList(string MNo, string sComp, string sFlag)
        {
            string sSQL = string.Empty;

            sSQL = "select MaterBatch,MaterNo,MaterName from T_MaterStock  where MaterNo = '" + MNo + "' and CompanyNo = '" + sComp + "' order by MaterBatch  ";

            DataTable sdt = DBHelper.GetDataTable(sSQL);

            return sdt;
        }


        /// <summary>
        /// 获取出库明细
        /// </summary>
        /// <param name="POut"></param>
        /// <param name="Item"></param>
        /// <param name="RNo"></param>
        /// <param name="MNo"></param>
        /// <param name="sInMan"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetPOutMX(string POut, string Item, string RNo, string MNo, string sInMan, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string sChStr = string.Empty;
            string sNum = string.Empty;
            DataTable sdt = new DataTable();

            if (sFlag == "63-4")  //获取出库明细
            {
                sSQL = " select * from  T_MaterStockOutMX where OutNo='" + POut + "'  ";
            }


            sdt = DBHelper.GetDataTable(sSQL);
            return sdt;
        }



















    }
}
