﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>抽样标准</title>
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>

    <script src="../js/Tech.js" type="text/javascript"></script>

    <link href="../css/XC.css" rel="stylesheet" />

    <script type="text/javascript">
        layui.use("table", function () {
            var table = layui.table
            table.render({
                elem: "#SamplingStd",
                id: "SamplingStdID",
                url: "../Service/TechAjax.ashx?OP=GetSamplingStd&CFlag=120",
                height: "full-50",
                cellMinWidth: 80,
                count: 50,
                limit: 20,
                limits: [10, 20, 30, 40, 50],
                cols: [[
                    { type: "numbers", width: 60, title: "序号" },
                    { field: "SSNo", title: "编号", sort: true },
                    { field: "SampleCode", title: "样本量字码", sort: true },
                    { field: "Aql", title: "接收质量限", sort: true },
                    { field: "Stringency", title: "严格度" },
                    { field: "SampleSize", title: "样本量", sort: true },
                    { field: "RNum", title: "接收数", sort: true },
                    { field: "BNum", title: "拒收数", sort: true },
                    { field: 'op', title: '操作', toolbar: '#barDemo', fixed: 'right', width: 180 }
                ]],
                page: true
            })

            table.on("tool(SamplingStd)", function (obj) {
                var data = obj.data;
                var layEvent = obj.event;
                if (layEvent == "delete") {

                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")             

                    //设置删除的标题
                    $("#hint-title").html("您确定要删除该记录吗？ 编号：")
                    //设置删除的对象
                    $("#hint-value").html(data.SSNo)

                    $("#txtDeleteKind").val(data.SSNo)

                } else if (layEvent == "UP" || layEvent == "DOWN") {
                    var params = { SSNo: data.SSNo, PresentOrder: data.SeqNo, Flag: "2", OPFlag: layEvent }
                    var Data = JSON.stringify(params);
                    $.ajax({
                        url: "../Service/TechAjax.ashx?OP=OPSamplingStd&CFlag=2",
                        type: "POST",
                        data: {
                            Data: Data
                        },
                        success: function (data) {
                            var parsedJson = jQuery.parseJSON(data);

                            if (parsedJson != undefined && parsedJson != '' && parsedJson.msg == 'Success') {
                                layer.msg('移动成功！');

                                $("#SamplingStdSearch").click()

                            } else {
                                layer.msg('移动失败，请重试！')
                            }
                        },
                        error: function () {
                            layer.msg('移动失败，请重试！')
                        }
                    })
                }

            })


            $("#SamplingStdSearch").click(function () {
                table.reload("SamplingStdID", {
                    method: "post",
                    url: "../Service/TechAjax.ashx?OP=GetSamplingStd&CFlag=120",
                    page: {
                        curr: 1
                    }
                })
            })
        })
    </script>

    <script type="text/javascript">
        $(function () {
            $("#SamplingStdAdd").click(function () {
                $("#ShowOne").css("display", "block");
                $("#ShowOne-fade").css("display", "block");
                $("#head-title1").html("新增抽样标准")
                ResetForm()

            })


            $("#DeleteSampleStd").click(function () {

                var sSSNo = $("#txtDeleteKind").val()

                var params = { SSNo: sSSNo, PresentOrder: "", Flag: "3", OPFlag: "" }

                var Data = JSON.stringify(params);

                $.ajax({
                    url: "../Service/TechAjax.ashx?OP=OPSamplingStd&CFlag=3",
                    type: "POST",
                    data: {
                        Data: Data
                    },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.msg == 'Success') {
                            layer.msg('删除成功！');

                            $("#SamplingStdSearch").click()
                            closeDialog()

                        } else {
                            layer.msg('删除失败，请重试！')
                        }
                    },
                    error: function () {
                        layer.msg('删除失败，请重试！')
                    }
                })
            })

        })

        function ResetForm() {
            $("#sampleCode").val("");
            $("#stringency").val("");
            $("#aql").val("");
            $("#sampleSize").val("");
            $("#rNum").val("");
            $("#bNum").val("");
            $("#remark").val("");
        }

        function closeDialog() {
            $("#ShowOne").css("display", "none");
            $("#ShowOne-fade").css("display", "none");
            $("#ShowDel").css("display", "none");
            $("#ShowDel-fade").css("display", "none");
        }
    </script>

    <style>
        /*   .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: #bbbcc7;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=60);
        }*/
        .table > tbody > tr > td {
            border: 0px;
        }

        .layui-table th {
            font-size: 12px;
        }

        .layui-table td {
            font-size: 12px;
        }

        .XC-Form-block-Item span {
            width: 80px;
        }
    </style>

</head>
<body>
    <div style="margin:5px;text-align:right">
        <button type="button" id="SamplingStdAdd" class="XC-Btn-md XC-Btn-Green XC-Size-xs">添加</button>
        <button type="button" id="SamplingStdSearch" style="display:none" class="layui-btn layui-btn-sm">搜索</button>
    </div>
    <table class="layui-hide" id="SamplingStd" lay-filter="SamplingStd"></table>
    <script type="text/html" id="barDemo">
        <button type="button" id="edit" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="UP">上↑移</button>
        <button type="button" id="edit" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="DOWN">下↓移</button>
        <button type="button" id="del" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="delete">删除</button>
    </script>

    <!--弹出层-->
    <div class="XC-modal XC-modal-lg" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1"></span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body" style="padding: 20px; height: 380px">
            <form class="XC-Form-block">

                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">样本量字码<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <input class="XC-Input-block" id="sampleCode" name="sampleCode" placeholder="请输入样本量字码" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">严格度<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <input class="XC-Input-block" id="stringency" name="stringency" placeholder="请输入严格度" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">接收质量限<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <input class="XC-Input-block" id="aql" name="aql" placeholder="请输入接收质量限" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">样本量<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <input class="XC-Input-block" id="sampleSize" name="sampleSize" placeholder="请输入样本量" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">接收数<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <input class="XC-Input-block" id="rNum" name="rNum" placeholder="请输入接收数" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">拒收数<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <input class="XC-Input-block" id="bNum" name="bNum" placeholder="请输入拒收数" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Textarea-block">备注</span>
                    <textarea class="XC-Textarea-block" id="remark" name="remark" placeholder="请输入备注"></textarea>
                </div>
            </form>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="SamplingStdSubmit">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick='closeDialog()'>取消</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>


    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDeleteKind" name="txtDeleteKind" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="DeleteSampleStd">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>

    <!--消息提示-->
    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>
</html>