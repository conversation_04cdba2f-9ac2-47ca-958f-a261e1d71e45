
html,body{ overflow: auto; }
body{ font-family:"<PERSON>m<PERSON>un",宋体,华文细黑,<PERSON><PERSON><PERSON><PERSON>,MingLiu !important; font-size: 14px; background-color: #eee;}

/*top*/
.control_box .layui-layout-admin .layui-header{overflow: hidden; height: 90px;
    background-color:rgba(0,0,0,.2) ;
}
.top_box{ padding:15px .8%; overflow: hidden; }
.top_box .logo{
    padding-left: 4px;
    height: 64px;
    background: url(../images/logo01.png) no-repeat;
    background-size: 174px;
    float: left;
}
.top_box .logo>img{
    width: 168px;
    display: inline-block;
    vertical-align: bottom;
}
.top_box .logo span{
    height:40px; 
    width: 96px; 
    display: inline-block;
    vertical-align: bottom;
    background: url(../images/logotxt.png) no-repeat center left;
    background-size: 100% 100%;
    margin-left: 5px;
}
.top_box .logo b{display: inline-block; vertical-align: bottom; margin-left: 25px;}
.top_box .logo b img{ display: block;width: 180px; }
.top_box .top_icon{ float: right; margin-top: 14px; }
.top_box .top_icon a{ display: inline-block; vertical-align: top; margin:0 15px; transition: all .3s; opacity: 1;}
.top_box .top_icon a img{ display: block; width: 40px;}
.top_box .top_icon a:hover{
    margin-top: 2px; opacity: .6;
}
.top_box .top_user{ margin-top: 10px; margin-left: 90px; display: inline-block; }
.top_box .top_user span{display: inline-block; vertical-align: middle; width: 50px; height: 50px; 
    border-radius: 100%; overflow: hidden;
}
.top_box .top_user span img{ display: block; width: 100%; }
.top_box .top_user dl{display: inline-block; vertical-align: middle; color: #fff; font-size: 12px; line-height: 22px;}

@media screen and (max-width: 900px){
    .top_box .logo b{ display: none; }
}
@media screen and (max-width:1366px){
    .top_box .logo span{height:40px; width: 96px; background-size: 100% 100%; }
}

/*主体框*/
.control_box .layui-layout-admin .layui-body{ background-color: #fff; top: 90px; bottom:0; left: 220px; z-index: 10; }

/*侧边栏*/
.control_box .layui-layout-admin .layui-side{ width: 220px; top: 90px; z-index: 999;}
.control_box .layui-nav-tree{ width: 220px; background: none; }
.control_box .layui-side-scroll{ width: 240px; }
.control_box .layui-bg-black{ background-color: rgba(0,0,0,.7) !important; }
.control_box .layui-nav .layui-nav-item{ position: relative; z-index: 1; }
.layui-nav-item i{display: inline-block; margin-right:5px; color: #aaa;}
.layui-nav-item:hover i{color: #fff;}
.control_box .layui-nav .layui-nav-itemed i{ color: #fff; }
.control_box .layui-nav .layui-nav-item a{ color: #fff; background: none !important; line-height: 50px; height: 50px; }
.control_box .layui-nav .layui-nav-item .layui-this a{background:none;}
.control_box .layui-nav .layui-nav-item .layui-this{ background-color: transparent; }
.control_box .layui-nav .layui-nav-itemed>a{background-color: rgba(32,91,158,.3) !important;  }
.control_box .layui-nav-item .layui-nav-child{ background-color:transparent !important; 
    border-bottom:rgba(255,255,255,.2) solid 1px; padding: 10px 0;
}
.control_box .layui-nav-tree .layui-nav-child a{ color: rgba(255,255,255,.7); line-height: 40px; text-indent: 1.5em; height: 40px; }
.layui-side .layui-nav-tree .layui-nav-bar{ width: 5px ; z-index:1 ; background-color: #205b9e; }
.layui-nav .layui-nav-more{ border-width: 4px !important; }

/*展开收起按钮*/
.open_shrink{ position: absolute; top:50%; margin-top:-40px; left: 0; 
border-color:transparent transparent transparent #052040;border-style:solid none solid solid ;
border-width:20px 0 20px 20px; height:40px;width:0; cursor: pointer;
}
.open_shrink span{ position: absolute; top:50%; left:-13px; margin-top: -4px;
    border-color: transparent transparent transparent #fff; border-style:solid none solid solid ;
    border-width:4px 0 4px 6px; height:0;width:0; opacity: .7;
}
.open_shrink.open span{ transform: rotate(-180deg);}
.open_shrink:hover span{opacity: 1;}

/*iframe内容框*/
.layadmin-iframe{ display: block; height: 100%; width: 100%;}

/*其他子页样式往下写*/
/*body.wangid_box*/
.wangid_conbox{ width: calc(100% - 60px); min-height: calc(100% - 20px); margin:10px; padding: 0 20px;
    box-shadow: 0 0 7px #b7b7b7; background-color: #fff;
}

.bord_b{border-bottom: #ddd solid 1px;}
.zy_weizhi{ height: 54px; line-height: 54px; font-size:0; }
.zy_weizhi i{ color: #205b9e; font-size: 18px;vertical-align: middle; margin-right:5px;}
.zy_weizhi a{ color: #333; font-size: 12px; display: inline-block; vertical-align: middle; }
.zy_weizhi a:after{ content: '>';margin:0 2px; }
.zy_weizhi span{ color: #205b9e; font-size: 12px;display: inline-block; vertical-align: middle;}

/*翻页选中当前块颜色*/
.layui-laypage .layui-laypage-curr .layui-laypage-em{ background-color: #205b9e !important; }
/*更改复选框颜色*/
/*.layui-form-checked[lay-skin=primary] i{ background-color: #205b9e !important; border-color: #205b9e !important; }*/

/*表格分页固定底部*/
.layui-table-page{ background-color: #f5f5f5; box-shadow: 0 -2px 3px #ccc; border-top:none !important; }



.shuaix{
    display: flex;
    margin-top: 10px; 
}

.shuaix .left select{
    border: solid 1px #ccc;
    padding: 6px 4px;
    min-width: 130px;
    border-radius: 3px;
}

.shuaix .center{
    line-height: 30px;
    margin: 0 20px;
}

.shuaix .right input{
    border: solid 1px #ccc;
    border-radius: 3px;
    padding: 6px 4px; 
    margin-right: 10px;
    width: 200px;

}

.shuaix .right a{
    background: #205b9e;
    height: 30px;
    padding: 0 15px; 
    border-radius: 3px;
    display:  inline-block;
    float: right;
    line-height: 30px;
    color: #fff;
}

.tianjia_xx .if_tianjiatext{
    width: 96%;
    margin: 2%;
    border-collapse: separate; 
} 
.tianjia_xx .if_tianjiatext .td_1{
    background: #f2f2f2;
}
.tianjia_xx .if_tianjiatext td{
    /* border-left:none;
    border-bottom:none; */
}
.tianjia_xx .if_tianjiatext tr:first-child td:first-child{
    border-top-left-radius: 6px;
}
.tianjia_xx .if_tianjiatext tr:first-child  td:last-child{
    border-top-right-radius: 6px;
}
.tianjia_xx .if_tianjiatext tr:last-child td:first-child{
    border-bottom-left-radius: 6px;
}
.tianjia_xx .if_tianjiatext tr:last-child  td:last-child{
    border-bottom-right-radius: 6px;
}

.tianjia_xx .if_tianjiatext tr td:first-child{
    border-right: none;
    width: 20%;
    text-align: center;
    font-weight: 600;
}
.tianjia_xx .if_tianjiatext tr td{
    border-bottom: none;
    padding: 10px 30px;
}
.tianjia_xx .if_tianjiatext tr:last-child td{
    border-bottom:1px solid #e6e6e6;
}
 
.tianjia_xx .if_tianjiatext tr:last-child td:hover{
    background: #fff;
}
.tianjia_xx .if_tianjiatext tr td input{
    width: 100%;
}
.tianjie_button{
    text-align: center;
}
.tianjie_button button{
    padding: 15px 80px;
    background: #205b9e;
    border-radius: 4px;
    color: #fff;
    margin: 10px 0;
    cursor:pointer;
}

body .demo_class_color .layui-layer-title{
    background: #205b9e;
    color: #fff;
    font-size: 18px;
}
body .demo_class_color {
    border-radius: 5px;
}
body .demo_class_color .layui-layer-ico{
    background: url(../layui/css/modules/layer/default/icon1.png) no-repeat;
    background-position: 1px -40px;
}
.shuaix .layui-input-inline {
    margin-right:10px;

}
.shuaix .layui-input-inline input{
    height: 30px;
    border-color: #ccc;
}


/* 2019年7月15日 */

/* VIP回访记录开始 */
 
.VIP_huifang textarea{
    width:100%;
    padding: 5px;
    background: #f2f2f2;

}
.VIP_huifang input{
    min-width: 210px;
    width: initial!important;
    height: 38px;
    border:solid 1px #ccc;
    border-radius: 3px;
    background: #f5f5f5;
    padding: 0 3px;
}

.layui-btn { 
    background-color: #205b9e!important;
    height: 38px;
}
.vip_luru{
    width: 100%;
    overflow: auto;
}
.vip_luru .if_tianjiatext{
    margin: 2% 0;
    width: 100%;
}

.vip_luru .if_tianjiatext tr td:first-child{
    width: initial;
}

.vip_luru .td_border {
    border-left: none;
    border-right: none;
}

.vip_luru td:nth-child(2n){
    width: 20%;
}

.vip_luru .if_tianjiatext tr td { 
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

body .vip_luru .if_tianjiatext tr:hover{
    background: #fff;
}
body .tianjia_xx .if_tianjiatext tr:hover{
    background: #fff;
}

.vip_luru .layui-input-inline{
    width: 100%;
}

.vip_luru .layui-input-inline>select{
    width: 100%;
}

.vip_luru .if_tianjiatext .td_1 { 
    font-weight: 600;
}
.vip_luru .suoyour{
    overflow: hidden;
}
.vip_luru .suoyour>li{
width: 50%;
float: left;
}

.vip_luru .if_tianjiatext tr td{
    padding: 10px 10px;
}

.vip_luru .if_tianjiatext tr td .layui-input{
    background: url(../images/riq.png) no-repeat center right;
    border: none;
}

.vip_luru .if_tianjiatext tr td textarea{
    width: 100%;
}

.vip_luru  .tianjie_button button{
    border: solid 1px #205b9e;
}
.vip_luru  .tianjie_button button:last-child{
    background: #fff;
    color: #205b9e;
    border: solid 1px #205b9e;

    margin-left: 40px;
}   


/* 2019年7月17日 */

.wenxts_ke{
    background: #eeeeee;
    padding: 12px;
    margin: 10px 0;
}

.wenxts_ke i{
    color: #e10601;
    font-style: normal;
}
.kehubh_tj_k ul{
    padding: 20px 0;
}
.kehubh_tj_k ul li{
    overflow: hidden;
    padding: 10px 0;
}

.kehubh_tj_k ul li .left{
    float: left;
    text-align: right;
    width: 100px;
    /* display: flex;
    align-items: center;  
    justify-content: flex-end; */
    line-height: 38px;
}

.kehubh_tj_k ul li .right{ 
    float: left;
}
.kehubh_tj_k .layui-form-pane .layui-form-radio, .layui-form-pane .layui-form-switch{
     /* margin-top: 0px; */
     padding-right: 0;
}
.kehubh_tj_k ul li .right .layui-input{
    width: 640px;
}
.kehubh_tj_k ul li .right .text{
    line-height: 38px; 
}
.kehubh_tj_k ul li .right .layui-textarea{
    width: 640px;
}
.kehubh_tj_k ul li .right button.button_qr {
    padding: 15px 60px;
    background: #205b9e;
    border-radius: 4px;
    color: #fff;
    margin: 10px 0;
    cursor: pointer;
}



/*  */

.jianl_list_img{
    display: inline-block;
    width: 45px;

}

.jianl_list_img img{
width: 100%;
}

.yuamgong_xianq{
    border: solid 1px #eee;
    text-align: center;
    padding: 2%;
    margin: 2%;
}

 
.layui-form-pane .layui-form-checkbox {
    margin: 12px 0 4px 10px!important;
}
.layui-form-select dl dd.layui-this {
    background-color: #205b9e!important;
    color: #fff;
}


/* gongdan */

.gongdan_list_top{
 overflow: hidden;
}

.gongdan_list_top li{
    float: left;
    margin-right: 10px;
}
 
.gongdan_list_top li a{
    display: block;
    line-height: 30px;
    background: #205b9e;
    border-radius: 4px;
    padding: 0 10px;
    color: #fff;
}

.gongdan_list_top li a.color1 {
    background: #205b9e;
}
.gongdan_list_top li a.color2 {
    background: #ff0000;
}
.gongdan_list_top li a.color3 {
    background: #ffae00;
}
.gongdan_list_top li a.color4 {
    background: #00a8ff;
}
.gongdan_list_top li a.color5 {
    background: #ff5522;
}
.gongdan_list_top li a.color6 {
    background: #009682;
}

.back_color{
    background: #ff0000;

}
 


.gd_my_xq_top{
    overflow: hidden;
    border-bottom:solid 1px #ddd;
    padding: 13px 0;
}
.gd_my_xq_top p{
    float: left;
    margin-right: 8px;
}

.gd_my_buz{
    padding: 30px 0;
    border-bottom:solid 1px #ddd;

}

.gd_my_buz ul{
    overflow: hidden;
    
}
.gd_my_buz ul li{
    float: left;
    padding-right: 33px; 
    margin-right: 15px;
    background: url(../images/jiant3.png) no-repeat center right;
}
.gd_my_buz ul li p{
    background: #eeeeee;
    color: #999;
    border-radius: 3px;
    padding: 10px 30px;
}

.gd_my_buz ul li.no{ 
    background: url(../images/jiant.png) no-repeat center right;
}
.gd_my_buz ul li.no p{
    background: #e10601;
    color: #fff; 
}

.gd_my_buz ul li.ywc{ 
    background: url(../images/jiant1.png) no-repeat center right;
}
.gd_my_buz ul li.ywc p{
    background: #f39b99;
    color: #fff; 
}


.gd_my_buz ul li.zuiz{ 
    background: none;
}
.gd_my_buz ul li.zuiz p{
    background: #209e4c;
    color: #fff; 
}

.gd_diangq_zt{
    position: relative;
}
.gd_diangq_zt .left{
    width: 635px;
}
.gd_diangq_zt .left p{
    padding: 15px 0;
}
.gd_diangq_zt .left textarea{
    width: 623px;
    padding: 5px;
    border:solid #ccc 1px;
    background: #f5f5f5;
}
.gd_diangq_zt .left .anniu{
    padding: 30px 0;
}

.gd_diangq_zt .left .anniu a{
    padding: 10px 30px;
    background: #205b9e;
    color: #fff;display: inline-block;
    margin-right: 30px;
    border-radius: 4px;
}

.gd_diangq_zt .left .anniu a.no{ 
    background: #209e4c; 
}
.gd_diangq_zt .left .anniu a.no1{ 
    background: #eeeeee; 
    color: #666666;
}
.right_fd{
    position: absolute;
    left: 680px;
    top: 30px;
    width: 280px;
    height: 360px;
    overflow-y: auto;
    padding: 10px;
}
.right_fd li{
    border-left: solid 1px #dddddd;
    padding-left: 20px;
    padding-bottom: 15px;
}

.right_fd li p{
    position: relative;
}
.right_fd li p i{
    position: absolute;
    width: 11px;
    height: 11px;
    border-radius: 50%;
    background: #e10601;
    display: block;
    top: 1px; 
    left: -26px;
}
.right_fd li dl{
    position: relative;
}
.right_fd li dl dt{ 
    color: #999;
}
.right_fd li dl img{
    position: absolute;
    top: 5px;
    left: -23px;
}

 /*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/

 .right_fd::-webkit-scrollbar {
    width: 3px; /*对垂直流动条有效*/
    height: 3px; /*对水平流动条有效*/
}

/*定义滚动条的轨道颜色、内阴影及圆角*/
.right_fd::-webkit-scrollbar-track{
    /* -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3); */
    background-color: #dddddd;
    border-radius: 3px;
}


/*定义滑块颜色、内阴影及圆角*/
.right_fd::-webkit-scrollbar-thumb{
    border-radius: 7px;
    /* -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3); */
    background-color: #aaaaaa;
}

/*定义两端按钮的样式*/
.right_fd::-webkit-scrollbar-button {
    background-color:#dddddd;
}

/*定义右下角汇合处的样式*/
.right_fd::-webkit-scrollbar-corner {
    background:khaki;
} 



.my_yvsbeij{
    padding: 10px 0;

}
.my_yvsbeij .bt{
    background: url(../images/my_in.png) no-repeat center left;
    padding-left: 20px;
    font-weight: bold;
 
}

.my_yvsbeij ul{
    overflow: hidden;
    padding: 20px 0;
}

.my_yvsbeij ul li{
    width: 192px; 
    border-radius: 3px;
    /* border:solid 2px #3f69bb; */
    overflow: hidden;
    text-align: center;
    position: relative;
    float: left;
    margin: 0 10px 10px 0;
    cursor:pointer;
}
.my_yvsbeij ul li img{ 
    width: 192px;
    /* border:solid 2px #3f69bb; */
    height: 108px;
    border-radius: 3px;

}
.my_yvsbeij ul li p{ 
    line-height: 35px;
}
.my_yvsbeij ul li.no p{
    color: #3f69bb; 
}
.my_yvsbeij ul li.no{
    
}
.my_yvsbeij ul li.no img{
    width: 188px; 
    height: 104px;
    border:solid 2px #3f69bb;
}

.my_yvsbeij ul li .tu{
background: #dddddd;
height: 108px;
line-height: 108px;
color: #999999;

}

.my_yvsbeij ul li .tu input{
    display: none;
    
    }

.my_yvsbeij ul li i{
    background: #e10601;
    display: block;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 16px;
    color: #fff;
    font-style: normal;
    position: absolute;
    top: 0px;
    right: 0px;
}