﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>巡查计划</title>

    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <link href="../css/orderinfo.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <script src="../layui/layui.js" type="text/javascript"></script>
    <link href="../js/skin/layer.css" rel="stylesheet" />

    <script src="../js/layer/layer.js" type="text/javascript"></script>
    <script src="../js/PrdAssist.js" type="text/javascript"></script>

    <link href="../css/XC.css" rel="stylesheet" />

    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        //$('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=28-1' });
                    }
                }
            })
        });

    </script>

    <script type="text/javascript">

        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#InspectPlanHeadlist',
                id: 'InspectPlanHeadlistID',
                url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=21',
                height: 'full-80',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'IPNo', title: '巡查计划编号', width: 200, sort: true },
                    { field: 'Ver', title: '版本', width: 100, sort: true },
                    { field: 'Type', title: '巡查类型', width: 200, sort: true },
                    { field: 'Remark', title: '备注', width: 300 },
                    { field: 'InMan', title: '创建人', width: 90 },
                    { field: 'InDate', title: '创建时间', width: 200 },
                    { field: 'op', title: '操作', width: 180, toolbar: '#barDemo_InspectPlanHeadlist', fixed: 'right' }
                ]],
                page: true,


            });




            //监听是否选中操作
            table.on('checkbox(InspectPlanHeadlist)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);


            });


            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(InspectPlanHeadlist)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

            });

            //监听单元格编辑
            table.on('edit(InspectPlanHeadlist)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });


            //监听行工具事件
            table.on('tool(InspectPlanHeadlist)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值
                if (layEvent === 'del') {

                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("确定要删除该巡查计划吗？")

                    //设置删除的对象
                    $("#hint-value").html("编号：" + data.IPNo + "，版本：" + data.Ver)

                    $("#txtDelIPNo").val(data.IPNo)
                    $("#txtDelVer").val(data.Ver)
                    $("#txtDelOP").val("plan")

                }
                else if (layEvent === 'edit') {
                    $('#head-title1').html("修改巡查计划信息");
                    $('#txtAEFlag').val("37");

                    $("#txtIPNo").val(data.IPNo);
                    $("#txtIPVer").val(data.Ver);
                    $("#txtIPType").val(data.Type);
                    $("#txtIPRemark").val(data.Remark);


                    $("#txtIPNo").attr({ "disabled": "disabled" });
                    $("#txtIPVer").attr({ "disabled": "disabled" });

                    $("#div_warning").html("");
                    $("#div_warning").hide();

                    $("#ShowOne").css("display", "block")
                    $("#ShowOne-fade").css("display", "block")

                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }
                else if (layEvent === 'upgrade') {
                    $('#head-title1').html("升级巡查计划信息");
                    $('#txtAEFlag').val("36-2");

                    $("#txtIPNo").val(data.IPNo);
                    $("#txtIPVer").val(data.Ver);
                    $("#txtIPType").val(data.Type);
                    $("#txtIPRemark").val(data.Remark);
                    $("#txtIPVerOld").val(data.Ver);

                    $("#txtIPNo").attr({ "disabled": "disabled" });
                    $("#txtIPVer").removeAttr("disabled");

                    $("#div_warning").html("");
                    $("#div_warning").hide();

                    $("#ShowOne").css("display", "block")
                    $("#ShowOne-fade").css("display", "block")

                }
                else if (layEvent === 'detail')//显示明细页面
                {
                    $('#head-title2').html("巡查计划信息详情");
                    //$('#txtAEFlag').val("11");

                    $("#txtIPDNo").val(data.IPNo);
                    $("#txtIPDVer").val(data.Ver);
                    $("#txtIPDType").val(data.Type);
                    //$("#txtWKind").val(data.WKind);
                    $("#txtDRemark").val(data.Remark);


                    $("#txtIPNo").attr({ "disabled": "disabled" });
                    $("#txtIPDVer").attr({ "disabled": "disabled" });

                    $("#div_warningDetail").html("");
                    $("#div_warningDetail").hide();
                    //弹窗显示巡查计划明细页
                    ShowInspectPlanDetail(data.IPNo, data.Ver);


                    $("#ShowTow").css("display", "block")
                    $("#ShowTow-fade").css("display", "block")

                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }


            });


            //  查询表头 --
            $('#InspectPlanBut_open').click(function () {

                var sIPNo = $("#txtSIPNo").val();  //
                /* var sIPVer = encodeURI($("#txtSIPVer").val());  //*/
                var sIPVer = $("#txtSIPVer").val();  //
                var sIPType = $("#txtSIPType").val();


                var Data = '';
                var Params = { No: sIPNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: sIPVer, B: sIPType, C: "", D: "", E: "" };
                var Data = JSON.stringify(Params);

                table.reload('InspectPlanHeadlistID', {
                    method: 'post',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=21&Data=' + Data,
                    where: {
                        'No': sIPNo,
                        'A': sIPVer,
                        'B': sIPType,
                    }, page: {
                        curr: 1
                    }
                });
            });



        });




        function openDialog(n) {
            if (n == 1) { // 新增表头弹窗层显示
                $('#head-title1').html("新增巡查计划信息");
                $('#txtAEFlag').val("36");


                $("#txtIPNo").val("");
                $("#txtIPVer").val("1.0");//V1.0
                $("#txtIPDType").val("");
                $("#txtIPRemark").val("");

                $("#txtIPNo").attr({ "disabled": "disabled" });
                $("#txtIPVer").removeAttr("disabled");

                $("#div_warning").html("");
                $("#div_warning").hide();

                $("#ShowOne").css("display", "block")
                $("#ShowOne-fade").css("display", "block")
            }
            else if (n == 2)//新增明细弹窗层显示
            {
                $('#head-title3').html("新增巡查计划明细");
                $('#txtAEFlag').val("36-1");

                //$("#txtIPDNoADD").val(data.CPNo); // 序号
                //$("#txtIPDVerADD").val(data.CPVer);//描述
                //$("#txtDRemarkADD").val(data.Remark);//字段类型

                $("#txtIPDNoADD").val($("#txtIPDNo").val()); // 巡查计划编号
                $("#txtIPDVerADD").val($("#txtIPDVer").val());//巡查计划版本
                $("#txtIPDTypeADD").val($("#txtIPDType").val());//巡查计划版本
                $("#txtDRemarkADD").val($("#txtDRemark").val());//备注

                $("#txtItemNo").val(""); // 内容编号
                $("#txtItemName").val(""); // 工艺节点
                $("#txtCFPoint").val(""); // 工艺步骤
                $("#txtBuildFloor").val(""); // 来源
                $("#txtRange").val(""); // PFMEA关联

                $("#txtProof").val(""); // 工装及夹具
                $("#txtCompliant").val(""); // ID/NO
                $("#txtCause").val(""); // 产品

                $("#txtDRemarkAdd").val(""); // 备注


                $("#txtIncludeOne").removeProp("checked");
                $("#txtIncludeTwo").removeProp("checked");


                $("#div_warningDetailAdd").html("");
                $("#div_warningDetailAdd").hide();


                $("#ShowThree").css("display", "block")
            }
        }

        function closeDialog(s) {
            if (s == 1) {
                $("#ShowThree").css("display", "none")
            }
            else {
                $("#ShowOne").css("display", "none")
                $("#ShowOne-fade").css("display", "none")

                $("#ShowTow").css("display", "none")
                $("#ShowTow-fade").css("display", "none")
            }
        }


        /// 显示巡查计划明细项信息
        ///daiwanshan
        function ShowInspectPlanDetail(sIPNo, sVer) {

            //var sCPNo = $("#txtIPDNo").val();  //
            //var sCPVer = encodeURI($("#txtIPDVer").val());  //


            var Data = '';
            var Params = {
                No: sIPNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: sVer, B: "", C: "", D: "", E: ""
            };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#InspectPlanDetailList',
                    id: 'InspectPlanDetailID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=21-1&Data=' + Data,
                    height: '260',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers' },
                        { field: 'ItemNo', title: '内容编号', width: 100 },
                        { field: 'ItemName', title: '项目名称', width: 100 },
                        { field: 'CFPoint', title: '巡查确认点', width: 100 },
                        { field: 'BuildFloor', title: '楼层', width: 100 },
                        { field: 'Range', title: '区域/线体', width: 100 },
                        { field: 'Proof', title: '巡查证据', width: 100 },
                        { field: 'Compliant', title: '是否符合', width: 100 },
                        { field: 'Cause', title: '不符合原因', width: 100 },

                        { field: 'Remark', title: '备注', width: 100 },
                        { field: 'SNo', title: '序号', width: 40 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate', title: '录入时间', width: 150 },
                        { field: 'op', title: '操作', width: 230, toolbar: '#barDemo_Detail', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(InspectPlanDetailList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值


                    if (layEvent == 'detailEdit') {
                        $('#txtAEFlag').val("37-1");
                        $("#head-title3").html("修改巡查计划明细");

                        $("#txtIPDNoADD").val($("#txtIPDNo").val()); // 巡查计划编号
                        $("#txtIPDVerADD").val($("#txtIPDVer").val());//巡查计划版本
                        $("#txtIPDTypeADD").val($("#txtIPDType").val());//巡查计划版本
                        //$("#txtDRemarkADD").val($("#txtDRemark").val());//备注


                        $("#txtItemNo").val(data.ItemNo); // 内容编号
                        $("#txtItemName").val(data.ItemName); // 工艺节点
                        $("#txtCFPoint").val(data.CFPoint); // 工艺步骤
                        $("#txtBuildFloor").val(data.BuildFloor); // 来源
                        $("#txtRange").val(data.Range); // PFMEA关联

                        $("#txtProof").val(data.Proof); // 工装及夹具
                        $("#txtCompliant").val(data.Compliant); // ID/NO
                        $("#txtCause").val(data.Cause); // 产品
                        $("#txtDRemarkAdd").val(data.Remark); // 备注




                        $("#div_warningDetailAdd").html("");

                        //$("#txtWOFileNo").attr({ "disabled": "disabled" });
                        //$("#txtWOFileVer").attr({ "disabled": "disabled" });

                        $("#ShowThree").css("display", "block")

                    }
                    else if (layEvent == 'detailDel') {

                        $("#ShowDel").css("display", "block")
                        $("#ShowDel-fade").css("display", "block")

                        $("#XC-Icon").removeClass()
                        $("#hint-value").removeClass()
                        $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                        $("#hint-value").addClass("XC-Font-Red")

                        //设置删除的标题
                        $("#hint-title").html("确定要删除该巡查计划项吗？内容编号：")

                        //设置删除的对象
                        $("#hint-value").html(data.ItemNo)

                        $("#txtDelIPNo").val($("#txtIPDNo").val())
                        $("#txtDelVer").val($("#txtIPDVer").val())
                        $("#txtDelItemNo").val(data.ItemNo)
                        $("#txtDelOP").val("planItem")
                    }
                    else if (layEvent == 'detailUpm')// 上↑移
                    {
                        var sNo = $("#txtIPDNo").val();
                        var sPVer = $("#txtIPDVer").val();
                        var sItemNo = data.ItemNo;
                        var sSNO = data.SNo;

                        var sFlag = "37-2";
                        var Data = '';
                        var Params = { No: sNo, Name: sPVer, Item: sItemNo, SNO: sSNO, A: "", B: "", C: "", D: "", E: "", F: "up", G: "", H: "", I: "", J: "", K: "", L: "", Remark: "", Flag: sFlag };
                        var Data = JSON.stringify(Params);
                        $.ajax({
                            type: "POST",
                            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
                            data: { Data: Data },
                            success: function (data) {
                                var parsedJson = jQuery.parseJSON(data);

                                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                    layer.msg('移动成功！');
                                    ShowInspectPlanDetail(sNo, sPVer);  // 重新查询


                                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                                    layer.msg('该巡查计划项已禁用，不能操作！');
                                }
                                else {
                                    layer.msg('移动失败，请重试！')
                                }
                            },
                            error: function (data) {
                                layer.msg('移动失败2，请重试！')
                            }
                        });

                    }
                    else if (layEvent == 'detailDown')// 下↓移
                    {
                        var sNo = $("#txtIPDNo").val();
                        var sPVer = $("#txtIPDVer").val();
                        var sItemNo = data.ItemNo;
                        var sSNO = data.SNo;

                        var sFlag = "37-2";
                        var Data = '';
                        var Params = { No: sNo, Name: sPVer, Item: sItemNo, SNO: sSNO, A: "", B: "", C: "", D: "", E: "", F: "down", G: "", H: "", I: "", J: "", K: "", L: sSNO, Remark: "", Flag: sFlag };
                        var Data = JSON.stringify(Params);
                        $.ajax({
                            type: "POST",
                            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
                            data: { Data: Data },
                            success: function (data) {
                                var parsedJson = jQuery.parseJSON(data);

                                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                    layer.msg('移动成功！');

                                    ShowInspectPlanDetail(sNo, sPVer);  // 重新查询

                                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                                    layer.msg('该巡查计划项已禁用，不能操作！');
                                }
                                else {
                                    layer.msg('移动失败，请重试！')
                                }
                            },
                            error: function (data) {
                                layer.msg('移动失败2，请重试！')
                            }
                        });

                    }



                });



            });  // layui.use('table', function () {


        }

        function btn_InspectPlanDetailDel_Btn() {
            var delOP = $("#txtDelOP").val()
            if (delOP == "plan") {
                //向服务端发送禁用指令
                var sIPNo = $("#txtDelIPNo").val();
                var sIPVer = $("#txtDelVer").val();
                var sFlag = "38";

                var Data = '';
                var Params = {
                    No: sIPNo, Name: sIPVer, Item: "", A: "", B: "", C: "", D: "", E: "", F: "", I: "", J: "", K: "", L: "", Kind: "", Remark: "", Flag: sFlag
                };
                var Data = JSON.stringify(Params);
                $.ajax({
                    type: "POST",
                    url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('删除成功！');

                            closeDelDialog()

                            $('#InspectPlanBut_open').click();  // 重新查询

                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                            layer.msg('该IPQC巡查计划已被IPQC巡查记录引用，不能删除！');

                        }
                        else {
                            layer.msg('删除失败，请重试！');
                            $('#InspectPlanBut_open').click();  // 重新查询
                        }
                    },
                    error: function (data) {
                        layer.msg('删除失败2，请重试！');
                        $('#InspectPlanBut_open').click();  // 重新查询
                    }
                });

            } else if (delOP == "planItem") {

                //向服务端发送删除指令 A: sPVer, B: sPath, C: sFile, D: sFNo, E: sFVer, F: sFName,

                //var sWONo = data.CPNo;
                //var sNo = data.CPNo;
                //var sPVer = data.CPVer;
                var sNo = $("#txtDelIPNo").val();
                var sPVer = $("#txtDelVer").val();
                var sItemNo = $("#txtDelItemNo").val();
                //var sFVer = data.FileVer;
                var sFlag = "38-1";

                var Data = '';
                var Params = { No: sNo, Name: sPVer, Item: sItemNo, SNO: "", A: "", B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);

                $.ajax({
                    type: "POST",
                    url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('删除成功！');

                            closeDelDialog()

                            ShowInspectPlanDetail(sNo, sPVer);  // 重新查询
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                            layer.msg('该文件已使用，不能操作！');
                            // $('#Fin_SearchOpen').click();
                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                            layer.msg('该工序版本已禁用，不能操作！');
                            // $('#Fin_SearchOpen').click();
                        }
                        else {
                            layer.msg('删除失败，请重试！');
                            // $('#Fin_SearchOpen').click();
                        }
                    },
                    error: function (data) {
                        layer.msg('删除失败2，请重试！');
                        // $('#Fin_SearchOpen').click();
                    }
                });

            }
        }


        function closeDelDialog() {
            $('#ShowDel').css("display", "none")
            $('#ShowDel-fade').css("display", "none")
        }
    </script>


    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

        })
    </script>

    <style type="text/css">

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        /*    .black_overlay {
                    display: none;
                    position: absolute;
                    top: 0%;
                    left: 0%;
                    width: 100%;
                    height: 100%;
                    background-color: #bbbcc7;
                    z-index: 1001;
                    -moz-opacity: 0.8;
                    opacity: .80;
                    filter: alpha(opacity=60);
                }
        */
        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }

        #ShowOne .XC-Form-block-Item span, #ShowTow .XC-Form-block-Item span, #ShowThree .XC-Form-block-Item span {
            width: 96px;
            line-height:30px;
        }
    </style>

</head>
<body>
    <div class="div_find">
        <p>
            <label class="find_labela">巡查计划编号</label> <input type="text" id="txtSIPNo" class="find_input" />
            <label class="find_labela">巡查类型</label><input type="text" id="txtSIPType" class="find_input" />
            <label class="find_labela">版本</label><input type="text" id="txtSIPVer" class="find_input" />
            <!--<label class="find_labela">描述</label><input type="text" id="txtSCPTxt" class="find_input" />-->
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="InspectPlanBut_open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="openDialog(1)">添加</button>
        </p>
    </div>

    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="InspectPlanHeadlist" lay-filter="InspectPlanHeadlist"></table>

        <script type="text/html" id="barDemo_InspectPlanHeadlist">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detail">详情</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="upgrade">升版</button>
            <!--<button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="edit">修改</button>-->
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>
        </script>

    </div>


    <!--巡查计划表头新增和修改增弹层-->
    <div class="XC-modal XC-modal-md" id="ShowOne" style="height: 350px;">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1">修改巡查计划</span>
            <span class="head-close" onclick="closeDialog(2)">X</span>
        </div>
        <div class="XC-modal-body">
            <form class="XC-Form-block">
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">巡查计划编号</span>
                    <input type="text" class="XC-Input-block" id="txtIPNo" name="txtIPNo" readonly=readonly placeholder="系统自动产生" value="" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">版本<span class="XC-Font-Red">*</span></span>
                    <input type="text" class="XC-Input-block" id="txtIPVer" name="txtIPVer" value="" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">巡查类型<span class="XC-Font-Red">*</span></span>
                    <!--<input type="text" class="XC-Input-block" id="txtIPType" name="txtIPType" value="" />-->
                    <select class="XC-Select-block" id="txtIPType">
                        <option></option>
                        <option>生产前巡查</option>
                        <option>日常巡查</option>
                        <option>专项巡查</option>
                    </select>
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Textarea-block">备注</span>
                    <textarea type="text" class="XC-Textarea-block" id="txtIPRemark" name="txtIPRemark" value=""></textarea>
                </div>
                <div class="XC-Form-block-Item" style="display:none">
                    <span class="XC-Span-Input-block"></span>
                    <input type="text" class="XC-Input-block" id="txtIPVerOld" name="txtIPVerOld" value="" />
                </div>
            </form>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="InspectPlanHeadSave_Btn">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="InspectPlanHeadSaveClose" onclick='closeDialog(2)'>关闭</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>


    <!--巡查计划明细弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowTow">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title2">质量控制计划详情</span>
            <span class="head-close" onclick="closeDialog(2)">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <div style="border-bottom: solid 1px #ccc;padding-bottom:12px;margin-bottom:12px;">
                    <span style="font-weight: bold; ">巡查计划</span>
                </div>

                <form class="XC-Form-block">
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">巡查计划编号</span>
                        <input type="text" class="XC-Input-block" id="txtIPDNo" name="txtIPDNo" readonly=readonly placeholder="系统自动产生" value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">版本</span>
                        <input type="text" class="XC-Input-block" id="txtIPDVer" name="txtIPDVer" readonly="readonly" value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">巡查类型</span>
                        <input type="text" class="XC-Input-block" id="txtIPDType" name="txtIPDType" readonly="readonly" value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Textarea-block">备注</span>
                        <textarea class="XC-Textarea-block" id="txtDRemark" name="txtDRemark" readonly=readonly></textarea>
                    </div>
                </form>

                <div style="display:flex;justify-content:space-between;border-bottom: solid 1px #ccc;padding-bottom:3px">
                    <span style="font-weight: bold; ">巡查计划明细</span>
                    <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="InspectPlanDetailAdd_Btn" onclick='openDialog(2)'>添加</button>
                </div>

                <div class="wangid_conbox">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="InspectPlanDetailList" lay-filter="InspectPlanDetailList"></table>

                    <script type="text/html" id="barDemo_Detail">
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detailEdit">修改</button>
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detailUpm">上↑移</button>
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detailDown">下↓移</button>
                        <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="detailDel">删除</button>
                    </script>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowTow-fade" class="black_overlay">
    </div>

    <!--巡查计划明细项修改和新增弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowThree">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title3">巡查计划新增</span>
            <span class="head-close" onclick="closeDialog(1)">X</span>
        </div>

        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">巡查计划编号</span>
                    <input type="text" class="XC-Input-block" id="txtIPDNoADD" name="txtIPDNoADD" readonly=readonly placeholder="系统自动产生" value="" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">版本<span class="XC-Font-Red">*</span></span>
                    <input type="text" class="XC-Input-block" id="txtIPDVerADD" name="txtIPDVerADD" readonly="readonly" value="" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">巡查类型<span class="XC-Font-Red">*</span></span>
                    <input type="text" class="XC-Input-block" id="txtIPDTypeADD" name="txtIPDTypeADD" readonly="readonly" value="" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">巡查项目编号</span>
                    <input type="text" class="XC-Input-block" id="txtItemNo" name="txtItemNo" readonly="readonly" placeholder="系统自动产生" value="" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">巡查项目<span class="XC-Font-Red">*</span></span>
                    <!--<input type="text" class="XC-Input-block" id="txtItemName" name="txtItemName" value="" />-->
                    <select class="XC-Select-block" id="txtItemName">
                        <option></option>
                        <option>人员巡查</option>
                        <option>工装/设备巡查</option>
                        <option>物料巡查</option>
                        <option>工艺巡查</option>
                        <option>TCN/ECR巡查</option>
                        <option>环境巡查</option>
                        <option>现场管理巡查</option>
                        <option>基础设施巡查</option>
                        <option>文件管理/记录填写巡查</option>
                    </select>
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">巡查确认点</span>
                    <input type="text" class="XC-Input-block" id="txtCFPoint" name="txtCFPoint" value="" />
                </div>

                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">楼层</span>
                    <!--<input type="text" class="XC-Input-block" id="txtBuildFloor" name="txtBuildFloor" value="" />-->
                    <select class="XC-Select-block" id="txtBuildFloor">
                        <option></option>
                        <option>布尔型</option>
                        <option>文本型</option>
                        <option>数值型</option>
                        <option>日期型</option>
                    </select>
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">区域/线体</span>
                    <!--<input type="text" class="XC-Input-block" id="txtRange" name="txtRange" value="" />-->
                    <select class="XC-Select-block" id="txtRange">
                        <option></option>
                        <option>布尔型</option>
                        <option>文本型</option>
                        <option>数值型</option>
                        <option>日期型</option>
                    </select>
                </div>

                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">证据</span>
                    <!--<input type="text" class="XC-Input-block" id="txtProof" name="txtProof" value="" />-->
                    <select class="XC-Select-block" id="txtProof">
                        <option></option>
                        <option>布尔型</option>
                        <option>文本型</option>
                        <option>数值型</option>
                        <option>日期型</option>
                    </select>
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">是否符合</span>
                    <!--<input type="text" class="XC-Input-block" id="txtCompliant" name="txtCompliant" value="" />-->
                    <select class="XC-Select-block" id="txtCompliant">
                        <option></option>
                        <option>布尔型</option>
                        <option>文本型</option>
                        <option>数值型</option>
                        <option>日期型</option>
                    </select>
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">不符合原因</span>
                    <!--<input type="text" class="XC-Input-block" id="txtCause" name="txtCause" value="" />-->
                    <select class="XC-Select-block" id="txtCause">
                        <option></option>
                        <option>布尔型</option>
                        <option>文本型</option>
                        <option>数值型</option>
                        <option>日期型</option>
                    </select>
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">备注</span>
                    <input type="text" class="XC-Input-block" id="txtDRemarkAdd" name="txtDRemarkAdd" value="" />
                </div>
            </div>
        </div>
        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="btn_InspectPlanDetailSave_Btn">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="btn_InspectPlanHeadDetailSaveClose" onclick='closeDialog(1)'>关闭</button>
            </div>
        </div>
    </div>

    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel" style="z-index:1003">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDelDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelIPNo" name="txtDelIPNo" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelVer" name="txtDelVer" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelItemNo" name="txtDelItemNo" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelOP" name="txtDelOP" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" onclick="btn_InspectPlanDetailDel_Btn()">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDelDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay" style="z-index:1002">
    </div>

    <!--消息提示-->
    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>

</body>
</html>