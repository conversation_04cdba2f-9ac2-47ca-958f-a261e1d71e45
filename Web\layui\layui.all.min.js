!function(e){"use strict";var t=document,n={modules:{},status:{},timeout:10,event:{}},i=function(){this.v="2.4.5"},a=function(){var e=t.currentScript?t.currentScript.src:function(){for(var e,n=t.scripts,i=n.length-1,a=i;a>0;a--)if("interactive"===n[a].readyState){e=n[a].src;break}return e||n[i].src}();return e.substring(0,e.lastIndexOf("/")+1)}(),o=function(t){e.console&&console.error&&console.error("Layui hint: "+t)},r="undefined"!=typeof opera&&"[object Opera]"===opera.toString(),l={layer:"modules/layer",laydate:"modules/laydate",laypage:"modules/laypage",laytpl:"modules/laytpl",layim:"modules/layim",layedit:"modules/layedit",form:"modules/form",upload:"modules/upload",tree:"modules/tree",table:"modules/table",element:"modules/element",rate:"modules/rate",colorpicker:"modules/colorpicker",slider:"modules/slider",carousel:"modules/carousel",flow:"modules/flow",util:"modules/util",code:"modules/code",jquery:"modules/jquery",mobile:"modules/mobile","layui.all":"../layui.all"};i.prototype.cache=n,i.prototype.define=function(e,t){var i=this,a="function"==typeof e,o=function(){var e=function(e,t){layui[e]=t,n.status[e]=!0};return"function"==typeof t&&t(function(i,a){e(i,a),n.callback[i]=function(){t(e)}}),this};return a&&(t=e,e=[]),layui["layui.all"]||!layui["layui.all"]&&layui["layui.mobile"]?o.call(i):(i.use(e,o),i)},i.prototype.use=function(e,i,s){function c(e,t){var i="PLaySTATION 3"===navigator.platform?/^complete$/:/^(complete|loaded)$/;("load"===e.type||i.test((e.currentTarget||e.srcElement).readyState))&&(n.modules[h]=t,p.removeChild(m),function e(){return++y>1e3*n.timeout/4?o(h+" is not a valid module"):void(n.status[h]?u():setTimeout(e,4))}())}function u(){s.push(layui[h]),e.length>1?d.use(e.slice(1),i,s):"function"==typeof i&&i.apply(layui,s)}var d=this,f=n.dir=n.dir?n.dir:a,p=t.getElementsByTagName("head")[0];e="string"==typeof e?[e]:e,window.jQuery&&jQuery.fn.on&&(d.each(e,function(t,n){"jquery"===n&&e.splice(t,1)}),layui.jquery=layui.$=jQuery);var h=e[0],y=0;if(s=s||[],n.host=n.host||(f.match(/\/\/([\s\S]+?)\//)||["//"+location.host+"/"])[0],0===e.length||layui["layui.all"]&&l[h]||!layui["layui.all"]&&layui["layui.mobile"]&&l[h])return u(),d;if(n.modules[h])!function e(){return++y>1e3*n.timeout/4?o(h+" is not a valid module"):void("string"==typeof n.modules[h]&&n.status[h]?u():setTimeout(e,4))}();else{var m=t.createElement("script"),v=(l[h]?f+"lay/":/^\{\/\}/.test(d.modules[h])?"":n.base||"")+(d.modules[h]||h)+".js";v=v.replace(/^\{\/\}/,""),m.async=!0,m.charset="utf-8",m.src=v+function(){var e=!0===n.version?n.v||(new Date).getTime():n.version||"";return e?"?v="+e:""}(),p.appendChild(m),!m.attachEvent||m.attachEvent.toString&&m.attachEvent.toString().indexOf("[native code")<0||r?m.addEventListener("load",function(e){c(e,v)},!1):m.attachEvent("onreadystatechange",function(e){c(e,v)}),n.modules[h]=v}return d},i.prototype.getStyle=function(t,n){var i=t.currentStyle?t.currentStyle:e.getComputedStyle(t,null);return i[i.getPropertyValue?"getPropertyValue":"getAttribute"](n)},i.prototype.link=function(e,i,a){var r=this,l=t.createElement("link"),s=t.getElementsByTagName("head")[0];"string"==typeof i&&(a=i);var c=(a||e).replace(/\.|\//g,""),u=l.id="layuicss-"+c,d=0;return l.rel="stylesheet",l.href=e+(n.debug?"?v="+(new Date).getTime():""),l.media="all",t.getElementById(u)||s.appendChild(l),"function"!=typeof i?r:(function a(){return++d>1e3*n.timeout/100?o(e+" timeout"):void(1989===parseInt(r.getStyle(t.getElementById(u),"width"))?i():setTimeout(a,100))}(),r)},n.callback={},i.prototype.factory=function(e){if(layui[e])return"function"==typeof n.callback[e]?n.callback[e]:null},i.prototype.addcss=function(e,t,i){return layui.link(n.dir+"css/"+e,t,i)},i.prototype.img=function(e,t,n){var i=new Image;return i.src=e,i.complete?t(i):(i.onload=function(){i.onload=null,"function"==typeof t&&t(i)},void(i.onerror=function(e){i.onerror=null,"function"==typeof n&&n(e)}))},i.prototype.config=function(e){for(var t in e=e||{},e)n[t]=e[t];return this},i.prototype.modules=function(){var e={};for(var t in l)e[t]=l[t];return e}(),i.prototype.extend=function(e){var t=this;for(var n in e=e||{},e)t[n]||t.modules[n]?o("模块名 "+n+" 已被占用"):t.modules[n]=e[n];return t},i.prototype.router=function(e){var t=this,n=(e=e||location.hash,{path:[],search:{},hash:(e.match(/[^#](#.*$)/)||[])[1]||""});return/^#\//.test(e)?(e=e.replace(/^#\//,""),n.href="/"+e,e=e.replace(/([^#])(#.*$)/,"$1").split("/")||[],t.each(e,function(e,t){/^\w+=/.test(t)?(t=t.split("="),n.search[t[0]]=t[1]):n.path.push(t)}),n):n},i.prototype.data=function(t,n,i){if(t=t||"layui",i=i||localStorage,e.JSON&&e.JSON.parse){if(null===n)return delete i[t];n="object"==typeof n?n:{key:n};try{var a=JSON.parse(i[t])}catch(e){a={}}return"value"in n&&(a[n.key]=n.value),n.remove&&delete a[n.key],i[t]=JSON.stringify(a),n.key?a[n.key]:a}},i.prototype.sessionData=function(e,t){return this.data(e,t,sessionStorage)},i.prototype.device=function(t){var n=navigator.userAgent.toLowerCase(),i=function(e){var t=new RegExp(e+"/([^\\s\\_\\-]+)");return e=(n.match(t)||[])[1],e||!1},a={os:/windows/.test(n)?"windows":/linux/.test(n)?"linux":/iphone|ipod|ipad|ios/.test(n)?"ios":/mac/.test(n)?"mac":void 0,ie:!!(e.ActiveXObject||"ActiveXObject"in e)&&((n.match(/msie\s(\d+)/)||[])[1]||"11"),weixin:i("micromessenger")};return t&&!a[t]&&(a[t]=i(t)),a.android=/android/.test(n),a.ios="ios"===a.os,a},i.prototype.hint=function(){return{error:o}},i.prototype.each=function(e,t){var n,i=this;if("function"!=typeof t)return i;if(e=e||[],e.constructor===Object){for(n in e)if(t.call(e[n],n,e[n]))break}else for(n=0;n<e.length&&!t.call(e[n],n,e[n]);n++);return i},i.prototype.sort=function(e,t,n){var i=JSON.parse(JSON.stringify(e||[]));return t?(i.sort(function(e,n){var i=/^-?\d+$/,a=e[t],o=n[t];return i.test(a)&&(a=parseFloat(a)),i.test(o)&&(o=parseFloat(o)),a&&!o?1:!a&&o?-1:a>o?1:a<o?-1:0}),n&&i.reverse(),i):i},i.prototype.stope=function(t){t=t||e.event;try{t.stopPropagation()}catch(e){t.cancelBubble=!0}},i.prototype.onevent=function(e,t,n){return"string"!=typeof e||"function"!=typeof n?this:i.event(e,t,null,n)},i.prototype.event=i.event=function(e,t,i,a){var o=this,r=null,l=t.match(/\((.*)\)$/)||[],s=(e+"."+t).replace(l[0],""),c=l[1]||"",u=function(e,t){var n=t&&t.call(o,i);!1===n&&null===r&&(r=!1)};return a?(n.event[s]=n.event[s]||{},n.event[s][c]=[a],this):(layui.each(n.event[s],function(e,t){return"{*}"===c?void layui.each(t,u):(""===e&&layui.each(t,u),void(c&&e===c&&layui.each(t,u)))}),r)},e.layui=new i}(window),layui.define(function(e){var t=layui.cache;layui.config({dir:t.dir.replace(/lay\/dest\/$/,"")}),e("layui.all",layui.v)}),layui.define(function(e){"use strict";var t={open:"{{",close:"}}"},n={exp:function(e){return new RegExp(e,"g")},query:function(e,n,a){var o=["#([\\s\\S])+?","([^{#}])*?"][e||0];return i((n||"")+t.open+o+t.close+(a||""))},escape:function(e){return String(e||"").replace(/&(?!#?[a-zA-Z0-9]+;)/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/'/g,"&#39;").replace(/"/g,"&quot;")},error:function(e,t){var n="Laytpl Error：";return"object"==typeof console&&console.error(n+e+"\n"+(t||"")),n+e}},i=n.exp,a=function(e){this.tpl=e};a.pt=a.prototype,window.errors=0,a.pt.parse=function(e,a){var o=this,r=e,l=i("^"+t.open+"#",""),s=i(t.close+"$","");e=e.replace(/\s+|\r|\t|\n/g," ").replace(i(t.open+"#"),t.open+"# ").replace(i(t.close+"}"),"} "+t.close).replace(/\\/g,"\\\\").replace(i(t.open+"!(.+?)!"+t.close),function(e){return e.replace(i("^"+t.open+"!"),"").replace(i("!"+t.close),"").replace(i(t.open+"|"+t.close),function(e){return e.replace(/(.)/g,"\\$1")})}).replace(/(?="|')/g,"\\").replace(n.query(),function(e){return e=e.replace(l,"").replace(s,""),'";'+e.replace(/\\/g,"")+';view+="'}).replace(n.query(1),function(e){var n='"+(';return e.replace(/\s/g,"")===t.open+t.close?"":(e=e.replace(i(t.open+"|"+t.close),""),/^=/.test(e)&&(e=e.replace(/^=/,""),n='"+_escape_('),n+e.replace(/\\/g,"")+')+"')}),e='"use strict";var view = "'+e+'";return view;';try{return o.cache=e=new Function("d, _escape_",e),e(a,n.escape)}catch(e){return delete o.cache,n.error(e,r)}},a.pt.render=function(e,t){var i,a=this;return e?(i=a.cache?a.cache(e,n.escape):a.parse(a.tpl,e),t?void t(i):i):n.error("no data")};var o=function(e){return"string"!=typeof e?n.error("Template not found"):new a(e)};o.config=function(e){for(var n in e=e||{},e)t[n]=e[n]},o.v="1.2.0",e("laytpl",o)}),layui.define(function(e){"use strict";var t=document,n="getElementById",i="getElementsByTagName",a="laypage",o="layui-disabled",r=function(e){var t=this;t.config=e||{},t.config.index=++l.index,t.render(!0)};r.prototype.type=function(){var e=this.config;if("object"==typeof e.elem)return void 0===e.elem.length?2:3},r.prototype.view=function(){var e=this,t=e.config,n=t.groups="groups"in t?0|t.groups:5;t.layout="object"==typeof t.layout?t.layout:["prev","page","next"],t.count=0|t.count,t.curr=0|t.curr||1,t.limits="object"==typeof t.limits?t.limits:[10,20,30,40,50],t.limit=0|t.limit||10,t.pages=Math.ceil(t.count/t.limit)||1,t.curr>t.pages&&(t.curr=t.pages),n<0?n=1:n>t.pages&&(n=t.pages),t.prev="prev"in t?t.prev:"&#x4E0A;&#x4E00;&#x9875;",t.next="next"in t?t.next:"&#x4E0B;&#x4E00;&#x9875;";var i=t.pages>n?Math.ceil((t.curr+(n>1?1:0))/(n>0?n:1)):1,a={prev:t.prev?'<a href="javascript:;" class="layui-laypage-prev'+(1==t.curr?" "+o:"")+'" data-page="'+(t.curr-1)+'">'+t.prev+"</a>":"",page:function(){var e=[];if(t.count<1)return"";i>1&&!1!==t.first&&0!==n&&e.push('<a href="javascript:;" class="layui-laypage-first" data-page="1"  title="&#x9996;&#x9875;">'+(t.first||1)+"</a>");var a=Math.floor((n-1)/2),o=i>1?t.curr-a:1,r=i>1?function(){var e=t.curr+(n-a-1);return e>t.pages?t.pages:e}():n;for(r-o<n-1&&(o=r-n+1),!1!==t.first&&o>2&&e.push('<span class="layui-laypage-spr">&#x2026;</span>');o<=r;o++)o===t.curr?e.push('<span class="layui-laypage-curr"><em class="layui-laypage-em" '+(/^#/.test(t.theme)?'style="background-color:'+t.theme+';"':"")+"></em><em>"+o+"</em></span>"):e.push('<a href="javascript:;" data-page="'+o+'">'+o+"</a>");return t.pages>n&&t.pages>r&&!1!==t.last&&(r+1<t.pages&&e.push('<span class="layui-laypage-spr">&#x2026;</span>'),0!==n&&e.push('<a href="javascript:;" class="layui-laypage-last" title="&#x5C3E;&#x9875;"  data-page="'+t.pages+'">'+(t.last||t.pages)+"</a>")),e.join("")}(),next:t.next?'<a href="javascript:;" class="layui-laypage-next'+(t.curr==t.pages?" "+o:"")+'" data-page="'+(t.curr+1)+'">'+t.next+"</a>":"",count:'<span class="layui-laypage-count">共 '+t.count+" 条</span>",limit:function(){var e=['<span class="layui-laypage-limits"><select lay-ignore>'];return layui.each(t.limits,function(n,i){e.push('<option value="'+i+'"'+(i===t.limit?"selected":"")+">"+i+" 条/页</option>")}),e.join("")+"</select></span>"}(),refresh:['<a href="javascript:;" data-page="'+t.curr+'" class="layui-laypage-refresh">','<i class="layui-icon layui-icon-refresh"></i>',"</a>"].join(""),skip:['<span class="layui-laypage-skip">&#x5230;&#x7B2C;','<input type="text" min="1" value="'+t.curr+'" class="layui-input">','&#x9875;<button type="button" class="layui-laypage-btn">&#x786e;&#x5b9a;</button>',"</span>"].join("")};return['<div class="layui-box layui-laypage layui-laypage-'+(t.theme?/^#/.test(t.theme)?"molv":t.theme:"default")+'" id="layui-laypage-'+t.index+'">',function(){var e=[];return layui.each(t.layout,function(t,n){a[n]&&e.push(a[n])}),e.join("")}(),"</div>"].join("")},r.prototype.jump=function(e,t){if(e){var n=this,a=n.config,o=e.children,r=e[i]("button")[0],s=e[i]("input")[0],c=e[i]("select")[0],u=function(){var e=0|s.value.replace(/\s|\D/g,"");e&&(a.curr=e,n.render())};if(t)return u();for(var d=0,f=o.length;d<f;d++)"a"===o[d].nodeName.toLowerCase()&&l.on(o[d],"click",function(){var e=0|this.getAttribute("data-page");e<1||e>a.pages||(a.curr=e,n.render())});c&&l.on(c,"change",function(){var e=this.value;a.curr*e>a.count&&(a.curr=Math.ceil(a.count/e)),a.limit=e,n.render()}),r&&l.on(r,"click",function(){u()})}},r.prototype.skip=function(e){if(e){var t=this,n=e[i]("input")[0];n&&l.on(n,"keyup",function(n){var i=this.value,a=n.keyCode;/^(37|38|39|40)$/.test(a)||(/\D/.test(i)&&(this.value=i.replace(/\D/,"")),13===a&&t.jump(e,!0))})}},r.prototype.render=function(e){var i=this,a=i.config,o=i.type(),r=i.view();2===o?a.elem&&(a.elem.innerHTML=r):3===o?a.elem.html(r):t[n](a.elem)&&(t[n](a.elem).innerHTML=r),a.jump&&a.jump(a,e);var l=t[n]("layui-laypage-"+a.index);i.jump(l),a.hash&&!e&&(location.hash="!"+a.hash+"="+a.curr),i.skip(l)};var l={render:function(e){var t=new r(e);return t.index},index:layui.laypage?layui.laypage.index+1e4:0,on:function(e,t,n){return e.attachEvent?e.attachEvent("on"+t,function(t){t.target=t.srcElement,n.call(e,t)}):e.addEventListener(t,n,!1),this}};e(a,l)}),function(){"use strict";var e=window.layui&&layui.define,t={getPath:function(){var e=document.currentScript?document.currentScript.src:function(){for(var e,t=document.scripts,n=t.length-1,i=n;i>0;i--)if("interactive"===t[i].readyState){e=t[i].src;break}return e||t[n].src}();return e.substring(0,e.lastIndexOf("/")+1)}(),getStyle:function(e,t){var n=e.currentStyle?e.currentStyle:window.getComputedStyle(e,null);return n[n.getPropertyValue?"getPropertyValue":"getAttribute"](t)},link:function(e,i,a){if(n.path){var o=document.getElementsByTagName("head")[0],r=document.createElement("link");"string"==typeof i&&(a=i);var l=(a||e).replace(/\.|\//g,""),s="layuicss-"+l,c=0;r.rel="stylesheet",r.href=n.path+e,r.id=s,document.getElementById(s)||o.appendChild(r),"function"==typeof i&&function e(){return++c>80?window.console&&console.error("laydate.css: Invalid"):void(1989===parseInt(t.getStyle(document.getElementById(s),"width"))?i():setTimeout(e,100))}()}}},n={v:"5.0.9",config:{},index:window.laydate&&window.laydate.v?1e5:0,path:t.getPath,set:function(e){var t=this;return t.config=w.extend({},t.config,e),t},ready:function(i){var a="laydate",o="",r=(e?"modules/laydate/":"theme/")+"default/laydate.css?v="+n.v+o;return e?layui.addcss(r,i,a):t.link(r,i,a),this}},i=function(){var e=this;return{hint:function(t){e.hint.call(e,t)},config:e.config}},a="laydate",o=".layui-laydate",r="layui-this",l="laydate-disabled",s="开始日期超出了结束日期<br>建议重新选择",c=[100,2e5],u="layui-laydate-static",d="layui-laydate-list",f="laydate-selected",p="layui-laydate-hint",h="laydate-day-prev",y="laydate-day-next",m="layui-laydate-footer",v=".laydate-btns-confirm",g="laydate-time-text",b=".laydate-btns-time",x=function(e){var t=this;t.index=++n.index,t.config=w.extend({},t.config,n.config,e),n.ready(function(){t.init()})},w=function(e){return new k(e)},k=function(e){for(var t=0,n="object"==typeof e?[e]:(this.selector=e,document.querySelectorAll(e||null));t<n.length;t++)this.push(n[t])};k.prototype=[],k.prototype.constructor=k,w.extend=function(){var e=1,t=arguments,n=function(e,t){for(var i in e=e||(t.constructor===Array?[]:{}),t)e[i]=t[i]&&t[i].constructor===Object?n(e[i],t[i]):t[i];return e};for(t[0]="object"==typeof t[0]?t[0]:{};e<t.length;e++)"object"==typeof t[e]&&n(t[0],t[e]);return t[0]},w.ie=function(){var e=navigator.userAgent.toLowerCase();return!!(window.ActiveXObject||"ActiveXObject"in window)&&((e.match(/msie\s(\d+)/)||[])[1]||"11")}(),w.stope=function(e){e=e||window.event,e.stopPropagation?e.stopPropagation():e.cancelBubble=!0},w.each=function(e,t){var n,i=this;if("function"!=typeof t)return i;if(e=e||[],e.constructor===Object){for(n in e)if(t.call(e[n],n,e[n]))break}else for(n=0;n<e.length&&!t.call(e[n],n,e[n]);n++);return i},w.digit=function(e,t,n){var i="";e=String(e),t=t||2;for(var a=e.length;a<t;a++)i+="0";return e<Math.pow(10,t)?i+(0|e):e},w.elem=function(e,t){var n=document.createElement(e);return w.each(t||{},function(e,t){n.setAttribute(e,t)}),n},k.addStr=function(e,t){return e=e.replace(/\s+/," "),t=t.replace(/\s+/," ").split(" "),w.each(t,function(t,n){new RegExp("\\b"+n+"\\b").test(e)||(e=e+" "+n)}),e.replace(/^\s|\s$/,"")},k.removeStr=function(e,t){return e=e.replace(/\s+/," "),t=t.replace(/\s+/," ").split(" "),w.each(t,function(t,n){var i=new RegExp("\\b"+n+"\\b");i.test(e)&&(e=e.replace(i,""))}),e.replace(/\s+/," ").replace(/^\s|\s$/,"")},k.prototype.find=function(e){var t=this,n=0,i=[],a="object"==typeof e;return this.each(function(o,r){for(var l=a?[e]:r.querySelectorAll(e||null);n<l.length;n++)i.push(l[n]);t.shift()}),a||(t.selector=(t.selector?t.selector+" ":"")+e),w.each(i,function(e,n){t.push(n)}),t},k.prototype.each=function(e){return w.each.call(this,this,e)},k.prototype.addClass=function(e,t){return this.each(function(n,i){i.className=k[t?"removeStr":"addStr"](i.className,e)})},k.prototype.removeClass=function(e){return this.addClass(e,!0)},k.prototype.hasClass=function(e){var t=!1;return this.each(function(n,i){new RegExp("\\b"+e+"\\b").test(i.className)&&(t=!0)}),t},k.prototype.attr=function(e,t){var n=this;return void 0===t?function(){if(n.length>0)return n[0].getAttribute(e)}():n.each(function(n,i){i.setAttribute(e,t)})},k.prototype.removeAttr=function(e){return this.each(function(t,n){n.removeAttribute(e)})},k.prototype.html=function(e){return this.each(function(t,n){n.innerHTML=e})},k.prototype.val=function(e){return this.each(function(t,n){n.value=e})},k.prototype.append=function(e){return this.each(function(t,n){"object"==typeof e?n.appendChild(e):n.innerHTML=n.innerHTML+e})},k.prototype.remove=function(e){return this.each(function(t,n){e?n.removeChild(e):n.parentNode.removeChild(n)})},k.prototype.on=function(e,t){return this.each(function(n,i){i.attachEvent?i.attachEvent("on"+e,function(e){e.target=e.srcElement,t.call(i,e)}):i.addEventListener(e,t,!1)})},k.prototype.off=function(e,t){return this.each(function(n,i){i.detachEvent?i.detachEvent("on"+e,t):i.removeEventListener(e,t,!1)})},x.isLeapYear=function(e){return e%4==0&&e%100!=0||e%400==0},x.prototype.config={type:"date",range:!1,format:"yyyy-MM-dd",value:null,isInitValue:!0,min:"1900-1-1",max:"2099-12-31",trigger:"focus",show:!1,showBottom:!0,btns:["clear","now","confirm"],lang:"cn",theme:"default",position:null,calendar:!1,mark:{},zIndex:null,done:null,change:null},x.prototype.lang=function(){var e=this,t=e.config,n={cn:{weeks:["日","一","二","三","四","五","六"],time:["时","分","秒"],timeTips:"选择时间",startTime:"开始时间",endTime:"结束时间",dateTips:"返回日期",month:["一","二","三","四","五","六","七","八","九","十","十一","十二"],tools:{confirm:"确定",clear:"清空",now:"现在"}},en:{weeks:["Su","Mo","Tu","We","Th","Fr","Sa"],time:["Hours","Minutes","Seconds"],timeTips:"Select Time",startTime:"Start Time",endTime:"End Time",dateTips:"Select Date",month:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],tools:{confirm:"Confirm",clear:"Clear",now:"Now"}}};return n[t.lang]||n.cn},x.prototype.init=function(){var e=this,t=e.config,n="yyyy|y|MM|M|dd|d|HH|H|mm|m|ss|s",i="static"===t.position,a={year:"yyyy",month:"yyyy-MM",date:"yyyy-MM-dd",time:"HH:mm:ss",datetime:"yyyy-MM-dd HH:mm:ss"};t.elem=w(t.elem),t.eventElem=w(t.eventElem),t.elem[0]&&(!0===t.range&&(t.range="-"),t.format===a.date&&(t.format=a[t.type]),e.format=t.format.match(new RegExp(n+"|.","g"))||[],e.EXP_IF="",e.EXP_SPLIT="",w.each(e.format,function(t,i){var a=new RegExp(n).test(i)?"\\d{"+(new RegExp(n).test(e.format[0===t?t+1:t-1]||"")?/^yyyy|y$/.test(i)?4:i.length:/^yyyy$/.test(i)?"1,4":/^y$/.test(i)?"1,308":"1,2")+"}":"\\"+i;e.EXP_IF=e.EXP_IF+a,e.EXP_SPLIT=e.EXP_SPLIT+"("+a+")"}),e.EXP_IF=new RegExp("^"+(t.range?e.EXP_IF+"\\s\\"+t.range+"\\s"+e.EXP_IF:e.EXP_IF)+"$"),e.EXP_SPLIT=new RegExp("^"+e.EXP_SPLIT+"$",""),e.isInput(t.elem[0])||"focus"===t.trigger&&(t.trigger="click"),t.elem.attr("lay-key")||(t.elem.attr("lay-key",e.index),t.eventElem.attr("lay-key",e.index)),t.mark=w.extend({},t.calendar&&"cn"===t.lang?{"0-1-1":"元旦","0-2-14":"情人","0-3-8":"妇女","0-3-12":"植树","0-4-1":"愚人","0-5-1":"劳动","0-5-4":"青年","0-6-1":"儿童","0-9-10":"教师","0-9-18":"国耻","0-10-1":"国庆","0-12-25":"圣诞"}:{},t.mark),w.each(["min","max"],function(e,n){var i=[],a=[];if("number"==typeof t[n]){var o=t[n],r=(new Date).getTime(),l=864e5,s=new Date(o?o<l?r+o*l:o:r);i=[s.getFullYear(),s.getMonth()+1,s.getDate()],o<l||(a=[s.getHours(),s.getMinutes(),s.getSeconds()])}else i=(t[n].match(/\d+-\d+-\d+/)||[""])[0].split("-"),a=(t[n].match(/\d+:\d+:\d+/)||[""])[0].split(":");t[n]={year:0|i[0]||(new Date).getFullYear(),month:i[1]?(0|i[1])-1:(new Date).getMonth(),date:0|i[2]||(new Date).getDate(),hours:0|a[0],minutes:0|a[1],seconds:0|a[2]}}),e.elemID="layui-laydate"+t.elem.attr("lay-key"),(t.show||i)&&e.render(),i||e.events(),t.value&&t.isInitValue&&(t.value.constructor===Date?e.setValue(e.parse(0,e.systemDate(t.value))):e.setValue(t.value)))},x.prototype.render=function(){var e=this,t=e.config,n=e.lang(),i="static"===t.position,a=e.elem=w.elem("div",{id:e.elemID,class:["layui-laydate",t.range?" layui-laydate-range":"",i?" "+u:"",t.theme&&"default"!==t.theme&&!/^#/.test(t.theme)?" laydate-theme-"+t.theme:""].join("")}),o=e.elemMain=[],r=e.elemHeader=[],l=e.elemCont=[],s=e.table=[],c=e.footer=w.elem("div",{class:m});if(t.zIndex&&(a.style.zIndex=t.zIndex),w.each(new Array(2),function(e){if(!t.range&&e>0)return!0;var i=w.elem("div",{class:"layui-laydate-header"}),a=[function(){var e=w.elem("i",{class:"layui-icon laydate-icon laydate-prev-y"});return e.innerHTML="&#xe65a;",e}(),function(){var e=w.elem("i",{class:"layui-icon laydate-icon laydate-prev-m"});return e.innerHTML="&#xe603;",e}(),function(){var e=w.elem("div",{class:"laydate-set-ym"}),t=w.elem("span"),n=w.elem("span");return e.appendChild(t),e.appendChild(n),e}(),function(){var e=w.elem("i",{class:"layui-icon laydate-icon laydate-next-m"});return e.innerHTML="&#xe602;",e}(),function(){var e=w.elem("i",{class:"layui-icon laydate-icon laydate-next-y"});return e.innerHTML="&#xe65b;",e}()],c=w.elem("div",{class:"layui-laydate-content"}),u=w.elem("table"),d=w.elem("thead"),f=w.elem("tr");w.each(a,function(e,t){i.appendChild(t)}),d.appendChild(f),w.each(new Array(6),function(e){var t=u.insertRow(0);w.each(new Array(7),function(i){if(0===e){var a=w.elem("th");a.innerHTML=n.weeks[i],f.appendChild(a)}t.insertCell(i)})}),u.insertBefore(d,u.children[0]),c.appendChild(u),o[e]=w.elem("div",{class:"layui-laydate-main laydate-main-list-"+e}),o[e].appendChild(i),o[e].appendChild(c),r.push(a),l.push(c),s.push(u)}),w(c).html(function(){var e=[],a=[];return"datetime"===t.type&&e.push('<span lay-type="datetime" class="laydate-btns-time">'+n.timeTips+"</span>"),w.each(t.btns,function(e,o){var r=n.tools[o]||"btn";t.range&&"now"===o||(i&&"clear"===o&&(r="cn"===t.lang?"重置":"Reset"),a.push('<span lay-type="'+o+'" class="laydate-btns-'+o+'">'+r+"</span>"))}),e.push('<div class="laydate-footer-btns">'+a.join("")+"</div>"),e.join("")}()),w.each(o,function(e,t){a.appendChild(t)}),t.showBottom&&a.appendChild(c),/^#/.test(t.theme)){var d=w.elem("style"),f=["#{{id}} .layui-laydate-header{background-color:{{theme}};}","#{{id}} .layui-this{background-color:{{theme}} !important;}"].join("").replace(/{{id}}/g,e.elemID).replace(/{{theme}}/g,t.theme);"styleSheet"in d?(d.setAttribute("type","text/css"),d.styleSheet.cssText=f):d.innerHTML=f,w(a).addClass("laydate-theme-molv"),a.appendChild(d)}e.remove(x.thisElemDate),i?t.elem.append(a):(document.body.appendChild(a),e.position()),e.checkDate().calendar(),e.changeEvent(),x.thisElemDate=e.elemID,"function"==typeof t.ready&&t.ready(w.extend({},t.dateTime,{month:t.dateTime.month+1}))},x.prototype.remove=function(e){var t=this,n=(t.config,w("#"+(e||t.elemID)));return n.hasClass(u)||t.checkDate(function(){n.remove()}),t},x.prototype.position=function(){var e=this,t=e.config,n=e.bindElem||t.elem[0],i=n.getBoundingClientRect(),a=e.elem.offsetWidth,o=e.elem.offsetHeight,r=function(e){return e=e?"scrollLeft":"scrollTop",document.body[e]|document.documentElement[e]},l=function(e){return document.documentElement[e?"clientWidth":"clientHeight"]},s=5,c=i.left,u=i.bottom;c+a+s>l("width")&&(c=l("width")-a-s),u+o+s>l()&&(u=i.top>o?i.top-o:l()-o,u-=2*s),t.position&&(e.elem.style.position=t.position),e.elem.style.left=c+("fixed"===t.position?0:r(1))+"px",e.elem.style.top=u+("fixed"===t.position?0:r())+"px"},x.prototype.hint=function(e){var t=this,n=(t.config,w.elem("div",{class:p}));t.elem&&(n.innerHTML=e||"",w(t.elem).find("."+p).remove(),t.elem.appendChild(n),clearTimeout(t.hinTimer),t.hinTimer=setTimeout(function(){w(t.elem).find("."+p).remove()},3e3))},x.prototype.getAsYM=function(e,t,n){return n?t--:t++,t<0&&(t=11,e--),t>11&&(t=0,e++),[e,t]},x.prototype.systemDate=function(e){var t=e||new Date;return{year:t.getFullYear(),month:t.getMonth(),date:t.getDate(),hours:e?e.getHours():0,minutes:e?e.getMinutes():0,seconds:e?e.getSeconds():0}},x.prototype.checkDate=function(e){var t,i,a=this,o=(new Date,a.config),r=o.dateTime=o.dateTime||a.systemDate(),l=a.bindElem||o.elem[0],s=(a.isInput(l),a.isInput(l)?l.value:"static"===o.position?"":l.innerHTML),u=function(e){e.year>c[1]&&(e.year=c[1],i=!0),e.month>11&&(e.month=11,i=!0),e.hours>23&&(e.hours=0,i=!0),e.minutes>59&&(e.minutes=0,e.hours++,i=!0),e.seconds>59&&(e.seconds=0,e.minutes++,i=!0),t=n.getEndDate(e.month+1,e.year),e.date>t&&(e.date=t,i=!0)},d=function(e,t,n){var r=["startTime","endTime"];t=(t.match(a.EXP_SPLIT)||[]).slice(1),n=n||0,o.range&&(a[r[n]]=a[r[n]]||{}),w.each(a.format,function(l,s){var u=parseFloat(t[l]);t[l].length<s.length&&(i=!0),/yyyy|y/.test(s)?(u<c[0]&&(u=c[0],i=!0),e.year=u):/MM|M/.test(s)?(u<1&&(u=1,i=!0),e.month=u-1):/dd|d/.test(s)?(u<1&&(u=1,i=!0),e.date=u):/HH|H/.test(s)?(u<1&&(u=0,i=!0),e.hours=u,o.range&&(a[r[n]].hours=u)):/mm|m/.test(s)?(u<1&&(u=0,i=!0),e.minutes=u,o.range&&(a[r[n]].minutes=u)):/ss|s/.test(s)&&(u<1&&(u=0,i=!0),e.seconds=u,o.range&&(a[r[n]].seconds=u))}),u(e)};return"limit"===e?(u(r),a):(s=s||o.value,"string"==typeof s&&(s=s.replace(/\s+/g," ").replace(/^\s|\s$/g,"")),a.startState&&!a.endState&&(delete a.startState,a.endState=!0),"string"==typeof s&&s?a.EXP_IF.test(s)?o.range?(s=s.split(" "+o.range+" "),a.startDate=a.startDate||a.systemDate(),a.endDate=a.endDate||a.systemDate(),o.dateTime=w.extend({},a.startDate),w.each([a.startDate,a.endDate],function(e,t){d(t,s[e],e)})):d(r,s):(a.hint("日期格式不合法<br>必须遵循下述格式：<br>"+(o.range?o.format+" "+o.range+" "+o.format:o.format)+"<br>已为你重置"),i=!0):s&&s.constructor===Date?o.dateTime=a.systemDate(s):(o.dateTime=a.systemDate(),delete a.startState,delete a.endState,delete a.startDate,delete a.endDate,delete a.startTime,delete a.endTime),u(r),i&&s&&a.setValue(o.range?a.endDate?a.parse():"":a.parse()),e&&e(),a)},x.prototype.mark=function(e,t){var n,i=this,a=i.config;return w.each(a.mark,function(e,i){var a=e.split("-");a[0]!=t[0]&&0!=a[0]||a[1]!=t[1]&&0!=a[1]||a[2]!=t[2]||(n=i||t[2])}),n&&e.html('<span class="laydate-day-mark">'+n+"</span>"),i},x.prototype.limit=function(e,t,n,i){var a,o=this,r=o.config,s={},c=r[n>41?"endDate":"dateTime"],u=w.extend({},c,t||{});return w.each({now:u,min:r.min,max:r.max},function(e,t){s[e]=o.newDate(w.extend({year:t.year,month:t.month,date:t.date},function(){var e={};return w.each(i,function(n,i){e[i]=t[i]}),e}())).getTime()}),a=s.now<s.min||s.now>s.max,e&&e[a?"addClass":"removeClass"](l),a},x.prototype.calendar=function(e){var t,i,a,o=this,l=o.config,s=e||l.dateTime,u=new Date,d=o.lang(),f="date"!==l.type&&"datetime"!==l.type,p=e?1:0,h=w(o.table[p]).find("td"),y=w(o.elemHeader[p][2]).find("span");if(s.year<c[0]&&(s.year=c[0],o.hint("最低只能支持到公元"+c[0]+"年")),s.year>c[1]&&(s.year=c[1],o.hint("最高只能支持到公元"+c[1]+"年")),o.firstDate||(o.firstDate=w.extend({},s)),u.setFullYear(s.year,s.month,1),t=u.getDay(),i=n.getEndDate(s.month||12,s.year),a=n.getEndDate(s.month+1,s.year),w.each(h,function(e,n){var c=[s.year,s.month],u=0;n=w(n),n.removeAttr("class"),e<t?(u=i-t+e,n.addClass("laydate-day-prev"),c=o.getAsYM(s.year,s.month,"sub")):e>=t&&e<a+t?(u=e-t,l.range||u+1===s.date&&n.addClass(r)):(u=e-a-t,n.addClass("laydate-day-next"),c=o.getAsYM(s.year,s.month)),c[1]++,c[2]=u+1,n.attr("lay-ymd",c.join("-")).html(c[2]),o.mark(n,c).limit(n,{year:c[0],month:c[1]-1,date:c[2]},e)}),w(y[0]).attr("lay-ym",s.year+"-"+(s.month+1)),w(y[1]).attr("lay-ym",s.year+"-"+(s.month+1)),"cn"===l.lang?(w(y[0]).attr("lay-type","year").html(s.year+"年"),w(y[1]).attr("lay-type","month").html(s.month+1+"月")):(w(y[0]).attr("lay-type","month").html(d.month[s.month]),w(y[1]).attr("lay-type","year").html(s.year)),f&&(l.range&&(e?o.endDate=o.endDate||{year:s.year+("year"===l.type?1:0),month:s.month+("month"===l.type?0:-1)}:o.startDate=o.startDate||{year:s.year,month:s.month},e&&(o.listYM=[[o.startDate.year,o.startDate.month+1],[o.endDate.year,o.endDate.month+1]],o.list(l.type,0).list(l.type,1),"time"===l.type?o.setBtnStatus("时间",w.extend({},o.systemDate(),o.startTime),w.extend({},o.systemDate(),o.endTime)):o.setBtnStatus(!0))),l.range||(o.listYM=[[s.year,s.month+1]],o.list(l.type,0))),l.range&&!e){var m=o.getAsYM(s.year,s.month);o.calendar(w.extend({},s,{year:m[0],month:m[1]}))}return l.range||o.limit(w(o.footer).find(v),null,0,["hours","minutes","seconds"]),l.range&&e&&!f&&o.stampRange(),o},x.prototype.list=function(e,t){var n=this,i=n.config,a=i.dateTime,o=n.lang(),s=i.range&&"date"!==i.type&&"datetime"!==i.type,c=w.elem("ul",{class:d+" "+{year:"laydate-year-list",month:"laydate-month-list",time:"laydate-time-list"}[e]}),u=n.elemHeader[t],f=w(u[2]).find("span"),p=n.elemCont[t||0],h=w(p).find("."+d)[0],y="cn"===i.lang,m=y?"年":"",x=n.listYM[t]||{},k=["hours","minutes","seconds"],C=["startTime","endTime"][t];if(x[0]<1&&(x[0]=1),"year"===e){var T,E=T=x[0]-7;E<1&&(E=T=1),w.each(new Array(15),function(e){var a=w.elem("li",{"lay-ym":T}),o={year:T};T==x[0]&&w(a).addClass(r),a.innerHTML=T+m,c.appendChild(a),T<n.firstDate.year?(o.month=i.min.month,o.date=i.min.date):T>=n.firstDate.year&&(o.month=i.max.month,o.date=i.max.date),n.limit(w(a),o,t),T++}),w(f[y?0:1]).attr("lay-ym",T-8+"-"+x[1]).html(E+m+" - "+(T-1+m))}else if("month"===e)w.each(new Array(12),function(e){var a=w.elem("li",{"lay-ym":e}),l={year:x[0],month:e};e+1==x[1]&&w(a).addClass(r),a.innerHTML=o.month[e]+(y?"月":""),c.appendChild(a),x[0]<n.firstDate.year?l.date=i.min.date:x[0]>=n.firstDate.year&&(l.date=i.max.date),n.limit(w(a),l,t)}),w(f[y?0:1]).attr("lay-ym",x[0]+"-"+x[1]).html(x[0]+m);else if("time"===e){var D=function(){w(c).find("ol").each(function(e,i){w(i).find("li").each(function(i,a){n.limit(w(a),[{hours:i},{hours:n[C].hours,minutes:i},{hours:n[C].hours,minutes:n[C].minutes,seconds:i}][e],t,[["hours"],["hours","minutes"],["hours","minutes","seconds"]][e])})}),i.range||n.limit(w(n.footer).find(v),n[C],0,["hours","minutes","seconds"])};i.range?n[C]||(n[C]={hours:0,minutes:0,seconds:0}):n[C]=a,w.each([24,60,60],function(e,t){var i=w.elem("li"),a=["<p>"+o.time[e]+"</p><ol>"];w.each(new Array(t),function(t){a.push("<li"+(n[C][k[e]]===t?' class="'+r+'"':"")+">"+w.digit(t,2)+"</li>")}),i.innerHTML=a.join("")+"</ol>",c.appendChild(i)}),D()}if(h&&p.removeChild(h),p.appendChild(c),"year"===e||"month"===e)w(n.elemMain[t]).addClass("laydate-ym-show"),w(c).find("li").on("click",function(){var o=0|w(this).attr("lay-ym");if(!w(this).hasClass(l)){if(0===t)a[e]=o,s&&(n.startDate[e]=o),n.limit(w(n.footer).find(v),null,0);else if(s)n.endDate[e]=o;else{var u="year"===e?n.getAsYM(o,x[1]-1,"sub"):n.getAsYM(x[0],o,"sub");w.extend(a,{year:u[0],month:u[1]})}"year"===i.type||"month"===i.type?(w(c).find("."+r).removeClass(r),w(this).addClass(r),"month"===i.type&&"year"===e&&(n.listYM[t][0]=o,s&&(n[["startDate","endDate"][t]].year=o),n.list("month",t))):(n.checkDate("limit").calendar(),n.closeList()),n.setBtnStatus(),i.range||n.done(null,"change"),w(n.footer).find(b).removeClass(l)}});else{var S=w.elem("span",{class:g}),L=function(){w(c).find("ol").each(function(e){var t=this,i=w(t).find("li");t.scrollTop=30*(n[C][k[e]]-2),t.scrollTop<=0&&i.each(function(e,n){if(!w(this).hasClass(l))return t.scrollTop=30*(e-2),!0})})},A=w(u[2]).find("."+g);L(),S.innerHTML=i.range?[o.startTime,o.endTime][t]:o.timeTips,w(n.elemMain[t]).addClass("laydate-time-show"),A[0]&&A.remove(),u[2].appendChild(S),w(c).find("ol").each(function(e){var t=this
;w(t).find("li").on("click",function(){var o=0|this.innerHTML;w(this).hasClass(l)||(i.range?n[C][k[e]]=o:a[k[e]]=o,w(t).find("."+r).removeClass(r),w(this).addClass(r),D(),L(),(n.endDate||"time"===i.type)&&n.done(null,"change"),n.setBtnStatus())})})}return n},x.prototype.listYM=[],x.prototype.closeList=function(){var e=this;e.config,w.each(e.elemCont,function(t,n){w(this).find("."+d).remove(),w(e.elemMain[t]).removeClass("laydate-ym-show laydate-time-show")}),w(e.elem).find("."+g).remove()},x.prototype.setBtnStatus=function(e,t,n){var i,a=this,o=a.config,r=w(a.footer).find(v),c=o.range&&"date"!==o.type&&"time"!==o.type;c&&(t=t||a.startDate,n=n||a.endDate,i=a.newDate(t).getTime()>a.newDate(n).getTime(),a.limit(null,t)||a.limit(null,n)?r.addClass(l):r[i?"addClass":"removeClass"](l),e&&i&&a.hint("string"==typeof e?s.replace(/日期/g,e):s))},x.prototype.parse=function(e,t){var n=this,i=n.config,a=t||(e?w.extend({},n.endDate,n.endTime):i.range?w.extend({},n.startDate,n.startTime):i.dateTime),o=n.format.concat();return w.each(o,function(e,t){/yyyy|y/.test(t)?o[e]=w.digit(a.year,t.length):/MM|M/.test(t)?o[e]=w.digit(a.month+1,t.length):/dd|d/.test(t)?o[e]=w.digit(a.date,t.length):/HH|H/.test(t)?o[e]=w.digit(a.hours,t.length):/mm|m/.test(t)?o[e]=w.digit(a.minutes,t.length):/ss|s/.test(t)&&(o[e]=w.digit(a.seconds,t.length))}),i.range&&!e?o.join("")+" "+i.range+" "+n.parse(1):o.join("")},x.prototype.newDate=function(e){return e=e||{},new Date(e.year||1,e.month||0,e.date||1,e.hours||0,e.minutes||0,e.seconds||0)},x.prototype.setValue=function(e){var t=this,n=t.config,i=t.bindElem||n.elem[0],a=t.isInput(i)?"val":"html";return"static"===n.position||w(i)[a](e||""),this},x.prototype.stampRange=function(){var e,t,n=this,i=n.config,a=w(n.elem).find("td");if(i.range&&!n.endDate&&w(n.footer).find(v).addClass(l),n.endDate)return e=n.newDate({year:n.startDate.year,month:n.startDate.month,date:n.startDate.date}).getTime(),t=n.newDate({year:n.endDate.year,month:n.endDate.month,date:n.endDate.date}).getTime(),e>t?n.hint(s):void w.each(a,function(i,a){var o=w(a).attr("lay-ymd").split("-"),l=n.newDate({year:o[0],month:o[1]-1,date:o[2]}).getTime();w(a).removeClass(f+" "+r),l!==e&&l!==t||w(a).addClass(w(a).hasClass(h)||w(a).hasClass(y)?f:r),l>e&&l<t&&w(a).addClass(f)})},x.prototype.done=function(e,t){var n=this,i=n.config,a=w.extend({},n.startDate?w.extend(n.startDate,n.startTime):i.dateTime),o=w.extend({},w.extend(n.endDate,n.endTime));return w.each([a,o],function(e,t){"month"in t&&w.extend(t,{month:t.month+1})}),e=e||[n.parse(),a,o],"function"==typeof i[t||"done"]&&i[t||"done"].apply(i,e),n},x.prototype.choose=function(e){var t=this,n=t.config,i=n.dateTime,a=w(t.elem).find("td"),o=e.attr("lay-ymd").split("-"),s=function(e){new Date,e&&w.extend(i,o),n.range&&(t.startDate?w.extend(t.startDate,o):t.startDate=w.extend({},o,t.startTime),t.startYMD=o)};if(o={year:0|o[0],month:(0|o[1])-1,date:0|o[2]},!e.hasClass(l))if(n.range){if(w.each(["startTime","endTime"],function(e,n){t[n]=t[n]||{hours:0,minutes:0,seconds:0}}),t.endState)s(),delete t.endState,delete t.endDate,t.startState=!0,a.removeClass(r+" "+f),e.addClass(r);else if(t.startState){if(e.addClass(r),t.endDate?w.extend(t.endDate,o):t.endDate=w.extend({},o,t.endTime),t.newDate(o).getTime()<t.newDate(t.startYMD).getTime()){var c=w.extend({},t.endDate,{hours:t.startDate.hours,minutes:t.startDate.minutes,seconds:t.startDate.seconds});w.extend(t.endDate,t.startDate,{hours:t.endDate.hours,minutes:t.endDate.minutes,seconds:t.endDate.seconds}),t.startDate=c}n.showBottom||t.done(),t.stampRange(),t.endState=!0,t.done(null,"change")}else e.addClass(r),s(),t.startState=!0;w(t.footer).find(v)[t.endDate?"removeClass":"addClass"](l)}else"static"===n.position?(s(!0),t.calendar().done().done(null,"change")):"date"===n.type?(s(!0),t.setValue(t.parse()).remove().done()):"datetime"===n.type&&(s(!0),t.calendar().done(null,"change"))},x.prototype.tool=function(e,t){var n=this,i=n.config,a=i.dateTime,o="static"===i.position,r={datetime:function(){w(e).hasClass(l)||(n.list("time",0),i.range&&n.list("time",1),w(e).attr("lay-type","date").html(n.lang().dateTips))},date:function(){n.closeList(),w(e).attr("lay-type","datetime").html(n.lang().timeTips)},clear:function(){n.setValue("").remove(),o&&(w.extend(a,n.firstDate),n.calendar()),i.range&&(delete n.startState,delete n.endState,delete n.endDate,delete n.startTime,delete n.endTime),n.done(["",{},{}])},now:function(){var e=new Date;w.extend(a,n.systemDate(),{hours:e.getHours(),minutes:e.getMinutes(),seconds:e.getSeconds()}),n.setValue(n.parse()).remove(),o&&n.calendar(),n.done()},confirm:function(){if(i.range){if(!n.endDate)return n.hint("请先选择日期范围");if(w(e).hasClass(l))return n.hint("time"===i.type?s.replace(/日期/g,"时间"):s)}else if(w(e).hasClass(l))return n.hint("不在有效日期或时间范围内");n.done(),n.setValue(n.parse()).remove()}};r[t]&&r[t]()},x.prototype.change=function(e){var t=this,n=t.config,i=n.dateTime,a=n.range&&("year"===n.type||"month"===n.type),o=t.elemCont[e||0],r=t.listYM[e],l=function(l){var s=["startDate","endDate"][e],c=w(o).find(".laydate-year-list")[0],u=w(o).find(".laydate-month-list")[0];return c&&(r[0]=l?r[0]-15:r[0]+15,t.list("year",e)),u&&(l?r[0]--:r[0]++,t.list("month",e)),(c||u)&&(w.extend(i,{year:r[0]}),a&&(t[s].year=r[0]),n.range||t.done(null,"change"),t.setBtnStatus(),n.range||t.limit(w(t.footer).find(v),{year:r[0]})),c||u};return{prevYear:function(){l("sub")||(i.year--,t.checkDate("limit").calendar(),n.range||t.done(null,"change"))},prevMonth:function(){var e=t.getAsYM(i.year,i.month,"sub");w.extend(i,{year:e[0],month:e[1]}),t.checkDate("limit").calendar(),n.range||t.done(null,"change")},nextMonth:function(){var e=t.getAsYM(i.year,i.month);w.extend(i,{year:e[0],month:e[1]}),t.checkDate("limit").calendar(),n.range||t.done(null,"change")},nextYear:function(){l()||(i.year++,t.checkDate("limit").calendar(),n.range||t.done(null,"change"))}}},x.prototype.changeEvent=function(){var e=this;e.config,w(e.elem).on("click",function(e){w.stope(e)}),w.each(e.elemHeader,function(t,n){w(n[0]).on("click",function(n){e.change(t).prevYear()}),w(n[1]).on("click",function(n){e.change(t).prevMonth()}),w(n[2]).find("span").on("click",function(n){var i=w(this),a=i.attr("lay-ym"),o=i.attr("lay-type");a&&(a=a.split("-"),e.listYM[t]=[0|a[0],0|a[1]],e.list(o,t),w(e.footer).find(b).addClass(l))}),w(n[3]).on("click",function(n){e.change(t).nextMonth()}),w(n[4]).on("click",function(n){e.change(t).nextYear()})}),w.each(e.table,function(t,n){var i=w(n).find("td");i.on("click",function(){e.choose(w(this))})}),w(e.footer).find("span").on("click",function(){var t=w(this).attr("lay-type");e.tool(this,t)})},x.prototype.isInput=function(e){return/input|textarea/.test(e.tagName.toLocaleLowerCase())},x.prototype.events=function(){var e=this,t=e.config,n=function(n,i){n.on(t.trigger,function(){i&&(e.bindElem=this),e.render()})};t.elem[0]&&!t.elem[0].eventHandler&&(n(t.elem,"bind"),n(t.eventElem),w(document).on("click",function(n){n.target!==t.elem[0]&&n.target!==t.eventElem[0]&&n.target!==w(t.closeStop)[0]&&e.remove()}).on("keydown",function(t){13===t.keyCode&&w("#"+e.elemID)[0]&&e.elemID===x.thisElem&&(t.preventDefault(),w(e.footer).find(v)[0].click())}),w(window).on("resize",function(){return!(!e.elem||!w(o)[0])&&void e.position()}),t.elem[0].eventHandler=!0)},n.render=function(e){var t=new x(e);return i.call(t)},n.getEndDate=function(e,t){var n=new Date;return n.setFullYear(t||n.getFullYear(),e||n.getMonth()+1,1),new Date(n.getTime()-864e5).getDate()},window.lay=window.lay||w,e?(n.ready(),layui.define(function(e){n.path=layui.cache.dir,e(a,n)})):"function"==typeof define&&define.amd?define(function(){return n}):(n.ready(),window.laydate=n)}(),function(e,t){"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(e,t){function n(e){var t=!!e&&"length"in e&&e.length,n=pe.type(e);return"function"!==n&&!pe.isWindow(e)&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}function i(e,t,n){if(pe.isFunction(t))return pe.grep(e,function(e,i){return!!t.call(e,i,e)!==n});if(t.nodeType)return pe.grep(e,function(e){return e===t!==n});if("string"==typeof t){if(Ce.test(t))return pe.filter(t,e,n);t=pe.filter(t,e)}return pe.grep(e,function(e){return pe.inArray(e,t)>-1!==n})}function a(e,t){do{e=e[t]}while(e&&1!==e.nodeType);return e}function o(e){var t={};return pe.each(e.match(Ne)||[],function(e,n){t[n]=!0}),t}function r(){ie.addEventListener?(ie.removeEventListener("DOMContentLoaded",l),e.removeEventListener("load",l)):(ie.detachEvent("onreadystatechange",l),e.detachEvent("onload",l))}function l(){(ie.addEventListener||"load"===e.event.type||"complete"===ie.readyState)&&(r(),pe.ready())}function s(e,t,n){if(void 0===n&&1===e.nodeType){var i="data-"+t.replace(Fe,"-$1").toLowerCase();if(n=e.getAttribute(i),"string"==typeof n){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:He.test(n)?pe.parseJSON(n):n)}catch(e){}pe.data(e,t,n)}else n=void 0}return n}function c(e){var t;for(t in e)if(("data"!==t||!pe.isEmptyObject(e[t]))&&"toJSON"!==t)return!1;return!0}function u(e,t,n,i){if(Me(e)){var a,o,r=pe.expando,l=e.nodeType,s=l?pe.cache:e,c=l?e[r]:e[r]&&r;if(c&&s[c]&&(i||s[c].data)||void 0!==n||"string"!=typeof t)return c||(c=l?e[r]=ne.pop()||pe.guid++:r),s[c]||(s[c]=l?{}:{toJSON:pe.noop}),"object"!=typeof t&&"function"!=typeof t||(i?s[c]=pe.extend(s[c],t):s[c].data=pe.extend(s[c].data,t)),o=s[c],i||(o.data||(o.data={}),o=o.data),void 0!==n&&(o[pe.camelCase(t)]=n),"string"==typeof t?(a=o[t],null==a&&(a=o[pe.camelCase(t)])):a=o,a}}function d(e,t,n){if(Me(e)){var i,a,o=e.nodeType,r=o?pe.cache:e,l=o?e[pe.expando]:pe.expando;if(r[l]){if(t&&(i=n?r[l]:r[l].data)){pe.isArray(t)?t=t.concat(pe.map(t,pe.camelCase)):t in i?t=[t]:(t=pe.camelCase(t),t=t in i?[t]:t.split(" ")),a=t.length;for(;a--;)delete i[t[a]];if(n?!c(i):!pe.isEmptyObject(i))return}(n||(delete r[l].data,c(r[l])))&&(o?pe.cleanData([e],!0):de.deleteExpando||r!=r.window?delete r[l]:r[l]=void 0)}}}function f(e,t,n,i){var a,o=1,r=20,l=i?function(){return i.cur()}:function(){return pe.css(e,t,"")},s=l(),c=n&&n[3]||(pe.cssNumber[t]?"":"px"),u=(pe.cssNumber[t]||"px"!==c&&+s)&&qe.exec(pe.css(e,t));if(u&&u[3]!==c){c=c||u[3],n=n||[],u=+s||1;do{o=o||".5",u/=o,pe.style(e,t,u+c)}while(o!==(o=l()/s)&&1!==o&&--r)}return n&&(u=+u||+s||0,a=n[1]?u+(n[1]+1)*n[2]:+n[2],i&&(i.unit=c,i.start=u,i.end=a)),a}function p(e){var t=$e.split("|"),n=e.createDocumentFragment();if(n.createElement)for(;t.length;)n.createElement(t.pop());return n}function h(e,t){var n,i,a=0,o=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):void 0;if(!o)for(o=[],n=e.childNodes||e;null!=(i=n[a]);a++)!t||pe.nodeName(i,t)?o.push(i):pe.merge(o,h(i,t));return void 0===t||t&&pe.nodeName(e,t)?pe.merge([e],o):o}function y(e,t){for(var n,i=0;null!=(n=e[i]);i++)pe._data(n,"globalEval",!t||pe._data(t[i],"globalEval"))}function m(e){ze.test(e.type)&&(e.defaultChecked=e.checked)}function v(e,t,n,i,a){for(var o,r,l,s,c,u,d,f=e.length,v=p(t),g=[],b=0;b<f;b++)if(r=e[b],r||0===r)if("object"===pe.type(r))pe.merge(g,r.nodeType?[r]:r);else if(Ye.test(r)){for(s=s||v.appendChild(t.createElement("div")),c=(Oe.exec(r)||["",""])[1].toLowerCase(),d=Xe[c]||Xe._default,s.innerHTML=d[1]+pe.htmlPrefilter(r)+d[2],o=d[0];o--;)s=s.lastChild;if(!de.leadingWhitespace&&We.test(r)&&g.push(t.createTextNode(We.exec(r)[0])),!de.tbody)for(r="table"!==c||Ve.test(r)?"<table>"!==d[1]||Ve.test(r)?0:s:s.firstChild,o=r&&r.childNodes.length;o--;)pe.nodeName(u=r.childNodes[o],"tbody")&&!u.childNodes.length&&r.removeChild(u);for(pe.merge(g,s.childNodes),s.textContent="";s.firstChild;)s.removeChild(s.firstChild);s=v.lastChild}else g.push(t.createTextNode(r));for(s&&v.removeChild(s),de.appendChecked||pe.grep(h(g,"input"),m),b=0;r=g[b++];)if(i&&pe.inArray(r,i)>-1)a&&a.push(r);else if(l=pe.contains(r.ownerDocument,r),s=h(v.appendChild(r),"script"),l&&y(s),n)for(o=0;r=s[o++];)Re.test(r.type||"")&&n.push(r);return s=null,v}function g(){return!0}function b(){return!1}function x(){try{return ie.activeElement}catch(e){}}function w(e,t,n,i,a,o){var r,l;if("object"==typeof t){for(l in"string"!=typeof n&&(i=i||n,n=void 0),t)w(e,l,n,i,t[l],o);return e}if(null==i&&null==a?(a=n,i=n=void 0):null==a&&("string"==typeof n?(a=i,i=void 0):(a=i,i=n,n=void 0)),!1===a)a=b;else if(!a)return e;return 1===o&&(r=a,a=function(e){return pe().off(e),r.apply(this,arguments)},a.guid=r.guid||(r.guid=pe.guid++)),e.each(function(){pe.event.add(this,t,a,i,n)})}function k(e,t){return pe.nodeName(e,"table")&&pe.nodeName(11!==t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function C(e){return e.type=(null!==pe.find.attr(e,"type"))+"/"+e.type,e}function T(e){var t=at.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function E(e,t){if(1===t.nodeType&&pe.hasData(e)){var n,i,a,o=pe._data(e),r=pe._data(t,o),l=o.events;if(l)for(n in delete r.handle,r.events={},l)for(i=0,a=l[n].length;i<a;i++)pe.event.add(t,n,l[n][i]);r.data&&(r.data=pe.extend({},r.data))}}function D(e,t){var n,i,a;if(1===t.nodeType){if(n=t.nodeName.toLowerCase(),!de.noCloneEvent&&t[pe.expando]){for(i in a=pe._data(t),a.events)pe.removeEvent(t,i,a.handle);t.removeAttribute(pe.expando)}"script"===n&&t.text!==e.text?(C(t).text=e.text,T(t)):"object"===n?(t.parentNode&&(t.outerHTML=e.outerHTML),de.html5Clone&&e.innerHTML&&!pe.trim(t.innerHTML)&&(t.innerHTML=e.innerHTML)):"input"===n&&ze.test(e.type)?(t.defaultChecked=t.checked=e.checked,t.value!==e.value&&(t.value=e.value)):"option"===n?t.defaultSelected=t.selected=e.defaultSelected:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}}function S(e,t,n,i){t=oe.apply([],t);var a,o,r,l,s,c,u=0,d=e.length,f=d-1,p=t[0],y=pe.isFunction(p);if(y||d>1&&"string"==typeof p&&!de.checkClone&&it.test(p))return e.each(function(a){var o=e.eq(a);y&&(t[0]=p.call(this,a,o.html())),S(o,t,n,i)});if(d&&(c=v(t,e[0].ownerDocument,!1,e,i),a=c.firstChild,1===c.childNodes.length&&(c=a),a||i)){for(l=pe.map(h(c,"script"),C),r=l.length;u<d;u++)o=c,u!==f&&(o=pe.clone(o,!0,!0),r&&pe.merge(l,h(o,"script"))),n.call(e[u],o,u);if(r)for(s=l[l.length-1].ownerDocument,pe.map(l,T),u=0;u<r;u++)o=l[u],Re.test(o.type||"")&&!pe._data(o,"globalEval")&&pe.contains(s,o)&&(o.src?pe._evalUrl&&pe._evalUrl(o.src):pe.globalEval((o.text||o.textContent||o.innerHTML||"").replace(ot,"")));c=a=null}return e}function L(e,t,n){for(var i,a=t?pe.filter(t,e):e,o=0;null!=(i=a[o]);o++)n||1!==i.nodeType||pe.cleanData(h(i)),i.parentNode&&(n&&pe.contains(i.ownerDocument,i)&&y(h(i,"script")),i.parentNode.removeChild(i));return e}function A(e,t){var n=pe(t.createElement(e)).appendTo(t.body),i=pe.css(n[0],"display");return n.detach(),i}function j(e){var t=ie,n=ct[e];return n||(n=A(e,t),"none"!==n&&n||(st=(st||pe("<iframe frameborder='0' width='0' height='0'/>")).appendTo(t.documentElement),t=(st[0].contentWindow||st[0].contentDocument).document,t.write(),t.close(),n=A(e,t),st.detach()),ct[e]=n),n}function N(e,t){return{get:function(){return e()?void delete this.get:(this.get=t).apply(this,arguments)}}}function M(e){if(e in Tt)return e;for(var t=e.charAt(0).toUpperCase()+e.slice(1),n=Ct.length;n--;)if(e=Ct[n]+t,e in Tt)return e}function H(e,t){for(var n,i,a,o=[],r=0,l=e.length;r<l;r++)i=e[r],i.style&&(o[r]=pe._data(i,"olddisplay"),n=i.style.display,t?(o[r]||"none"!==n||(i.style.display=""),""===i.style.display&&_e(i)&&(o[r]=pe._data(i,"olddisplay",j(i.nodeName)))):(a=_e(i),(n&&"none"!==n||!a)&&pe._data(i,"olddisplay",a?n:pe.css(i,"display"))));for(r=0;r<l;r++)i=e[r],i.style&&(t&&"none"!==i.style.display&&""!==i.style.display||(i.style.display=t?o[r]||"":"none"));return e}function F(e,t,n){var i=xt.exec(t);return i?Math.max(0,i[1]-(n||0))+(i[2]||"px"):t}function I(e,t,n,i,a){for(var o=n===(i?"border":"content")?4:"width"===t?1:0,r=0;o<4;o+=2)"margin"===n&&(r+=pe.css(e,n+Pe[o],!0,a)),i?("content"===n&&(r-=pe.css(e,"padding"+Pe[o],!0,a)),"margin"!==n&&(r-=pe.css(e,"border"+Pe[o]+"Width",!0,a))):(r+=pe.css(e,"padding"+Pe[o],!0,a),"padding"!==n&&(r+=pe.css(e,"border"+Pe[o]+"Width",!0,a)));return r}function q(t,n,i){var a=!0,o="width"===n?t.offsetWidth:t.offsetHeight,r=ht(t),l=de.boxSizing&&"border-box"===pe.css(t,"boxSizing",!1,r);if(ie.msFullscreenElement&&e.top!==e&&t.getClientRects().length&&(o=Math.round(100*t.getBoundingClientRect()[n])),o<=0||null==o){if(o=yt(t,n,r),(o<0||null==o)&&(o=t.style[n]),dt.test(o))return o;a=l&&(de.boxSizingReliable()||o===t.style[n]),o=parseFloat(o)||0}return o+I(t,n,i||(l?"border":"content"),a,r)+"px"}function P(e,t,n,i,a){return new P.prototype.init(e,t,n,i,a)}function _(){return e.setTimeout(function(){Et=void 0}),Et=pe.now()}function B(e,t){var n,i={height:e},a=0;for(t=t?1:0;a<4;a+=2-t)n=Pe[a],i["margin"+n]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function z(e,t,n){for(var i,a=(W.tweeners[t]||[]).concat(W.tweeners["*"]),o=0,r=a.length;o<r;o++)if(i=a[o].call(n,t,e))return i}function O(e,t,n){var i,a,o,r,l,s,c,u,d=this,f={},p=e.style,h=e.nodeType&&_e(e),y=pe._data(e,"fxshow");for(i in n.queue||(l=pe._queueHooks(e,"fx"),null==l.unqueued&&(l.unqueued=0,s=l.empty.fire,l.empty.fire=function(){l.unqueued||s()}),l.unqueued++,d.always(function(){d.always(function(){l.unqueued--,pe.queue(e,"fx").length||l.empty.fire()})})),1===e.nodeType&&("height"in t||"width"in t)&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],c=pe.css(e,"display"),u="none"===c?pe._data(e,"olddisplay")||j(e.nodeName):c,"inline"===u&&"none"===pe.css(e,"float")&&(de.inlineBlockNeedsLayout&&"inline"!==j(e.nodeName)?p.zoom=1:p.display="inline-block")),n.overflow&&(p.overflow="hidden",de.shrinkWrapBlocks()||d.always(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]})),t)if(a=t[i],St.exec(a)){if(delete t[i],o=o||"toggle"===a,a===(h?"hide":"show")){if("show"!==a||!y||void 0===y[i])continue;h=!0}f[i]=y&&y[i]||pe.style(e,i)}else c=void 0;if(pe.isEmptyObject(f))"inline"===("none"===c?j(e.nodeName):c)&&(p.display=c);else for(i in y?"hidden"in y&&(h=y.hidden):y=pe._data(e,"fxshow",{}),o&&(y.hidden=!h),h?pe(e).show():d.done(function(){pe(e).hide()}),d.done(function(){var t;for(t in pe._removeData(e,"fxshow"),f)pe.style(e,t,f[t])}),f)r=z(h?y[i]:0,i,d),i in y||(y[i]=r.start,h&&(r.end=r.start,r.start="width"===i||"height"===i?1:0))}function R(e,t){var n,i,a,o,r;for(n in e)if(i=pe.camelCase(n),a=t[i],o=e[n],pe.isArray(o)&&(a=o[1],o=e[n]=o[0]),n!==i&&(e[i]=o,delete e[n]),r=pe.cssHooks[i],r&&"expand"in r)for(n in o=r.expand(o),delete e[i],o)n in e||(e[n]=o[n],t[n]=a);else t[i]=a}function W(e,t,n){var i,a,o=0,r=W.prefilters.length,l=pe.Deferred().always(function(){delete s.elem}),s=function(){if(a)return!1;for(var t=Et||_(),n=Math.max(0,c.startTime+c.duration-t),i=n/c.duration||0,o=1-i,r=0,s=c.tweens.length;r<s;r++)c.tweens[r].run(o);return l.notifyWith(e,[c,o,n]),o<1&&s?n:(l.resolveWith(e,[c]),!1)},c=l.promise({elem:e,props:pe.extend({},t),opts:pe.extend(!0,{specialEasing:{},easing:pe.easing._default},n),originalProperties:t,originalOptions:n,startTime:Et||_(),duration:n.duration,tweens:[],createTween:function(t,n){var i=pe.Tween(e,c.opts,t,n,c.opts.specialEasing[t]||c.opts.easing);return c.tweens.push(i),i},stop:function(t){var n=0,i=t?c.tweens.length:0;if(a)return this;for(a=!0;n<i;n++)c.tweens[n].run(1);return t?(l.notifyWith(e,[c,1,0]),l.resolveWith(e,[c,t])):l.rejectWith(e,[c,t]),this}}),u=c.props;for(R(u,c.opts.specialEasing);o<r;o++)if(i=W.prefilters[o].call(c,e,u,c.opts))return pe.isFunction(i.stop)&&(pe._queueHooks(c.elem,c.opts.queue).stop=pe.proxy(i.stop,i)),i;return pe.map(u,z,c),pe.isFunction(c.opts.start)&&c.opts.start.call(e,c),pe.fx.timer(pe.extend(s,{elem:e,anim:c,queue:c.opts.queue})),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always)}function $(e){return pe.attr(e,"class")||""}function X(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var i,a=0,o=t.toLowerCase().match(Ne)||[];if(pe.isFunction(n))for(;i=o[a++];)"+"===i.charAt(0)?(i=i.slice(1)||"*",(e[i]=e[i]||[]).unshift(n)):(e[i]=e[i]||[]).push(n)}}function Y(e,t,n,i){function a(l){var s;return o[l]=!0,pe.each(e[l]||[],function(e,l){var c=l(t,n,i);return"string"!=typeof c||r||o[c]?r?!(s=c):void 0:(t.dataTypes.unshift(c),a(c),!1)}),s}var o={},r=e===Qt;return a(t.dataTypes[0])||!o["*"]&&a("*")}function V(e,t){var n,i,a=pe.ajaxSettings.flatOptions||{};for(i in t)void 0!==t[i]&&((a[i]?e:n||(n={}))[i]=t[i]);return n&&pe.extend(!0,e,n),e}function K(e,t,n){for(var i,a,o,r,l=e.contents,s=e.dataTypes;"*"===s[0];)s.shift(),void 0===a&&(a=e.mimeType||t.getResponseHeader("Content-Type"));if(a)for(r in l)if(l[r]&&l[r].test(a)){s.unshift(r);break}if(s[0]in n)o=s[0];else{for(r in n){if(!s[0]||e.converters[r+" "+s[0]]){o=r;break}i||(i=r)}o=o||i}if(o)return o!==s[0]&&s.unshift(o),n[o]}function G(e,t,n,i){var a,o,r,l,s,c={},u=e.dataTypes.slice();if(u[1])for(r in e.converters)c[r.toLowerCase()]=e.converters[r];for(o=u.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!s&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),s=o,o=u.shift())if("*"===o)o=s;else if("*"!==s&&s!==o){if(r=c[s+" "+o]||c["* "+o],!r)for(a in c)if(l=a.split(" "),l[1]===o&&(r=c[s+" "+l[0]]||c["* "+l[0]])){!0===r?r=c[a]:!0!==c[a]&&(o=l[0],u.unshift(l[1]));break}if(!0!==r)if(r&&e.throws)t=r(t);else try{t=r(t)}catch(e){return{state:"parsererror",error:r?e:"No conversion from "+s+" to "+o}}}return{state:"success",data:t}}function J(e){return e.style&&e.style.display||pe.css(e,"display")}function U(e){for(;e&&1===e.nodeType;){if("none"===J(e)||"hidden"===e.type)return!0;e=e.parentNode}return!1}function Q(e,t,n,i){var a;if(pe.isArray(t))pe.each(t,function(t,a){n||an.test(e)?i(e,a):Q(e+"["+("object"==typeof a&&null!=a?t:"")+"]",a,n,i)});else if(n||"object"!==pe.type(t))i(e,t);else for(a in t)Q(e+"["+a+"]",t[a],n,i)}function Z(){try{return new e.XMLHttpRequest}catch(e){}}function ee(){try{return new e.ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}function te(e){return pe.isWindow(e)?e:9===e.nodeType&&(e.defaultView||e.parentWindow)}var ne=[],ie=e.document,ae=ne.slice,oe=ne.concat,re=ne.push,le=ne.indexOf,se={},ce=se.toString,ue=se.hasOwnProperty,de={},fe="1.12.3",pe=function(e,t){return new pe.fn.init(e,t)},he=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,ye=/^-ms-/,me=/-([\da-z])/gi,ve=function(e,t){return t.toUpperCase()};pe.fn=pe.prototype={jquery:fe,constructor:pe,selector:"",length:0,toArray:function(){return ae.call(this)},get:function(e){return null!=e?e<0?this[e+this.length]:this[e]:ae.call(this)},pushStack:function(e){var t=pe.merge(this.constructor(),e);return t.prevObject=this,t.context=this.context,t},each:function(e){return pe.each(this,e)},map:function(e){return this.pushStack(pe.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(ae.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:re,sort:ne.sort,splice:ne.splice},pe.extend=pe.fn.extend=function(){var e,t,n,i,a,o,r=arguments[0]||{},l=1,s=arguments.length,c=!1;for("boolean"==typeof r&&(c=r,r=arguments[l]||{},l++),"object"==typeof r||pe.isFunction(r)||(r={}),l===s&&(r=this,l--);l<s;l++)if(null!=(a=arguments[l]))for(i in a)e=r[i],n=a[i],r!==n&&(c&&n&&(pe.isPlainObject(n)||(t=pe.isArray(n)))?(t?(t=!1,o=e&&pe.isArray(e)?e:[]):o=e&&pe.isPlainObject(e)?e:{},r[i]=pe.extend(c,o,n)):void 0!==n&&(r[i]=n));return r},pe.extend({expando:"jQuery"+(fe+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===pe.type(e)},isArray:Array.isArray||function(e){return"array"===pe.type(e)},isWindow:function(e){return null!=e&&e==e.window},isNumeric:function(e){var t=e&&e.toString();return!pe.isArray(e)&&t-parseFloat(t)+1>=0},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},isPlainObject:function(e){var t;if(!e||"object"!==pe.type(e)||e.nodeType||pe.isWindow(e))return!1;try{if(e.constructor&&!ue.call(e,"constructor")&&!ue.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(e){return!1}if(!de.ownFirst)for(t in e)return ue.call(e,t);for(t in e);return void 0===t||ue.call(e,t)},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?se[ce.call(e)]||"object":typeof e},globalEval:function(t){t&&pe.trim(t)&&(e.execScript||function(t){e.eval.call(e,t)})(t)},camelCase:function(e){return e.replace(ye,"ms-").replace(me,ve)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t){var i,a=0;if(n(e))for(i=e.length;a<i&&!1!==t.call(e[a],a,e[a]);a++);else for(a in e)if(!1===t.call(e[a],a,e[a]))break;return e},trim:function(e){return null==e?"":(e+"").replace(he,"")},makeArray:function(e,t){var i=t||[];return null!=e&&(n(Object(e))?pe.merge(i,"string"==typeof e?[e]:e):re.call(i,e)),i},inArray:function(e,t,n){var i;if(t){if(le)return le.call(t,e,n);for(i=t.length,n=n?n<0?Math.max(0,i+n):n:0;n<i;n++)if(n in t&&t[n]===e)return n}return-1},merge:function(e,t){for(var n=+t.length,i=0,a=e.length;i<n;)e[a++]=t[i++];if(n!=n)for(;void 0!==t[i];)e[a++]=t[i++];return e.length=a,e},grep:function(e,t,n){for(var i,a=[],o=0,r=e.length,l=!n;o<r;o++)i=!t(e[o],o),i!==l&&a.push(e[o]);return a},map:function(e,t,i){var a,o,r=0,l=[];if(n(e))for(a=e.length;r<a;r++)o=t(e[r],r,i),null!=o&&l.push(o);else for(r in e)o=t(e[r],r,i),null!=o&&l.push(o);return oe.apply([],l)},guid:1,proxy:function(e,t){var n,i,a;if("string"==typeof t&&(a=e[t],t=e,e=a),pe.isFunction(e))return n=ae.call(arguments,2),i=function(){return e.apply(t||this,n.concat(ae.call(arguments)))},i.guid=e.guid=e.guid||pe.guid++,i},now:function(){return+new Date},support:de}),"function"==typeof Symbol&&(pe.fn[Symbol.iterator]=ne[Symbol.iterator]),pe.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){se["[object "+t+"]"]=t.toLowerCase()});var ge=function(e){function t(e,t,n,i){var a,o,r,l,s,c,d,p,h=t&&t.ownerDocument,y=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==y&&9!==y&&11!==y)return n;if(!i&&((t?t.ownerDocument||t:z)!==M&&N(t),t=t||M,F)){if(11!==y&&(c=ve.exec(e)))if(a=c[1]){if(9===y){if(!(r=t.getElementById(a)))return n;if(r.id===a)return n.push(r),n}else if(h&&(r=h.getElementById(a))&&_(t,r)&&r.id===a)return n.push(r),n}else{if(c[2])return Q.apply(n,t.getElementsByTagName(e)),n;if((a=c[3])&&w.getElementsByClassName&&t.getElementsByClassName)return Q.apply(n,t.getElementsByClassName(a)),n}if(w.qsa&&!X[e+" "]&&(!I||!I.test(e))){if(1!==y)h=t,p=e;else if("object"!==t.nodeName.toLowerCase()){for((l=t.getAttribute("id"))?l=l.replace(be,"\\$&"):t.setAttribute("id",l=B),d=E(e),o=d.length,s=fe.test(l)?"#"+l:"[id='"+l+"']";o--;)d[o]=s+" "+f(d[o]);p=d.join(","),h=ge.test(e)&&u(t.parentNode)||t}if(p)try{return Q.apply(n,h.querySelectorAll(p)),n}catch(e){}finally{l===B&&t.removeAttribute("id")}}}return S(e.replace(le,"$1"),t,n,i)}function n(){function e(n,i){return t.push(n+" ")>k.cacheLength&&delete e[t.shift()],e[n+" "]=i}var t=[];return e}function i(e){return e[B]=!0,e}function a(e){var t=M.createElement("div");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function o(e,t){for(var n=e.split("|"),i=n.length;i--;)k.attrHandle[n[i]]=t}function r(e,t){var n=t&&e,i=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||V)-(~e.sourceIndex||V);if(i)return i;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function l(e){return function(t){var n=t.nodeName.toLowerCase();return"input"===n&&t.type===e}}function s(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function c(e){return i(function(t){return t=+t,i(function(n,i){for(var a,o=e([],n.length,t),r=o.length;r--;)n[a=o[r]]&&(n[a]=!(i[a]=n[a]))})})}function u(e){return e&&void 0!==e.getElementsByTagName&&e}function d(){}function f(e){for(var t=0,n=e.length,i="";t<n;t++)i+=e[t].value;return i}function p(e,t,n){var i=t.dir,a=n&&"parentNode"===i,o=R++;return t.first?function(t,n,o){for(;t=t[i];)if(1===t.nodeType||a)return e(t,n,o)}:function(t,n,r){var l,s,c,u=[O,o];if(r){for(;t=t[i];)if((1===t.nodeType||a)&&e(t,n,r))return!0}else for(;t=t[i];)if(1===t.nodeType||a){if(c=t[B]||(t[B]={}),s=c[t.uniqueID]||(c[t.uniqueID]={}),(l=s[i])&&l[0]===O&&l[1]===o)return u[2]=l[2];if(s[i]=u,u[2]=e(t,n,r))return!0}}}function h(e){return e.length>1?function(t,n,i){for(var a=e.length;a--;)if(!e[a](t,n,i))return!1;return!0}:e[0]}function y(e,n,i){for(var a=0,o=n.length;a<o;a++)t(e,n[a],i);return i}function m(e,t,n,i,a){for(var o,r=[],l=0,s=e.length,c=null!=t;l<s;l++)(o=e[l])&&(n&&!n(o,i,a)||(r.push(o),c&&t.push(l)));return r}function v(e,t,n,a,o,r){return a&&!a[B]&&(a=v(a)),o&&!o[B]&&(o=v(o,r)),i(function(i,r,l,s){var c,u,d,f=[],p=[],h=r.length,v=i||y(t||"*",l.nodeType?[l]:l,[]),g=!e||!i&&t?v:m(v,f,e,l,s),b=n?o||(i?e:h||a)?[]:r:g;if(n&&n(g,b,l,s),a)for(c=m(b,p),a(c,[],l,s),u=c.length;u--;)(d=c[u])&&(b[p[u]]=!(g[p[u]]=d));if(i){if(o||e){if(o){for(c=[],u=b.length;u--;)(d=b[u])&&c.push(g[u]=d);o(null,b=[],c,s)}for(u=b.length;u--;)(d=b[u])&&(c=o?ee(i,d):f[u])>-1&&(i[c]=!(r[c]=d))}}else b=m(b===r?b.splice(h,b.length):b),o?o(null,r,b,s):Q.apply(r,b)})}function g(e){for(var t,n,i,a=e.length,o=k.relative[e[0].type],r=o||k.relative[" "],l=o?1:0,s=p(function(e){return e===t},r,!0),c=p(function(e){return ee(t,e)>-1},r,!0),u=[function(e,n,i){var a=!o&&(i||n!==L)||((t=n).nodeType?s(e,n,i):c(e,n,i));return t=null,a}];l<a;l++)if(n=k.relative[e[l].type])u=[p(h(u),n)];else{if(n=k.filter[e[l].type].apply(null,e[l].matches),n[B]){for(i=++l;i<a&&!k.relative[e[i].type];i++);return v(l>1&&h(u),l>1&&f(e.slice(0,l-1).concat({value:" "===e[l-2].type?"*":""})).replace(le,"$1"),n,l<i&&g(e.slice(l,i)),i<a&&g(e=e.slice(i)),i<a&&f(e))}u.push(n)}return h(u)}function b(e,n){var a=n.length>0,o=e.length>0,r=function(i,r,l,s,c){var u,d,f,p=0,h="0",y=i&&[],v=[],g=L,b=i||o&&k.find.TAG("*",c),x=O+=null==g?1:Math.random()||.1,w=b.length;for(c&&(L=r===M||r||c);h!==w&&null!=(u=b[h]);h++){if(o&&u){for(d=0,r||u.ownerDocument===M||(N(u),l=!F);f=e[d++];)if(f(u,r||M,l)){s.push(u);break}c&&(O=x)}a&&((u=!f&&u)&&p--,i&&y.push(u))}if(p+=h,a&&h!==p){for(d=0;f=n[d++];)f(y,v,r,l);if(i){if(p>0)for(;h--;)y[h]||v[h]||(v[h]=J.call(s));v=m(v)}Q.apply(s,v),c&&!i&&v.length>0&&p+n.length>1&&t.uniqueSort(s)}return c&&(O=x,L=g),y};return a?i(r):r}var x,w,k,C,T,E,D,S,L,A,j,N,M,H,F,I,q,P,_,B="sizzle"+1*new Date,z=e.document,O=0,R=0,W=n(),$=n(),X=n(),Y=function(e,t){return e===t&&(j=!0),0},V=1<<31,K={}.hasOwnProperty,G=[],J=G.pop,U=G.push,Q=G.push,Z=G.slice,ee=function(e,t){for(var n=0,i=e.length;n<i;n++)if(e[n]===t)return n;return-1},te="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",ne="[\\x20\\t\\r\\n\\f]",ie="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",ae="\\["+ne+"*("+ie+")(?:"+ne+"*([*^$|!~]?=)"+ne+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+ie+"))|)"+ne+"*\\]",oe=":("+ie+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+ae+")*)|.*)\\)|)",re=new RegExp(ne+"+","g"),le=new RegExp("^"+ne+"+|((?:^|[^\\\\])(?:\\\\.)*)"+ne+"+$","g"),se=new RegExp("^"+ne+"*,"+ne+"*"),ce=new RegExp("^"+ne+"*([>+~]|"+ne+")"+ne+"*"),ue=new RegExp("="+ne+"*([^\\]'\"]*?)"+ne+"*\\]","g"),de=new RegExp(oe),fe=new RegExp("^"+ie+"$"),pe={ID:new RegExp("^#("+ie+")"),CLASS:new RegExp("^\\.("+ie+")"),TAG:new RegExp("^("+ie+"|[*])"),ATTR:new RegExp("^"+ae),PSEUDO:new RegExp("^"+oe),
CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+ne+"*(even|odd|(([+-]|)(\\d*)n|)"+ne+"*(?:([+-]|)"+ne+"*(\\d+)|))"+ne+"*\\)|)","i"),bool:new RegExp("^(?:"+te+")$","i"),needsContext:new RegExp("^"+ne+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+ne+"*((?:-\\d)?\\d*)"+ne+"*\\)|)(?=[^-]|$)","i")},he=/^(?:input|select|textarea|button)$/i,ye=/^h\d$/i,me=/^[^{]+\{\s*\[native \w/,ve=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ge=/[+~]/,be=/'|\\/g,xe=new RegExp("\\\\([\\da-f]{1,6}"+ne+"?|("+ne+")|.)","ig"),we=function(e,t,n){var i="0x"+t-65536;return i!=i||n?t:i<0?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,1023&i|56320)},ke=function(){N()};try{Q.apply(G=Z.call(z.childNodes),z.childNodes),G[z.childNodes.length].nodeType}catch(e){Q={apply:G.length?function(e,t){U.apply(e,Z.call(t))}:function(e,t){for(var n=e.length,i=0;e[n++]=t[i++];);e.length=n-1}}}for(x in w=t.support={},T=t.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},N=t.setDocument=function(e){var t,n,i=e?e.ownerDocument||e:z;return i!==M&&9===i.nodeType&&i.documentElement?(M=i,H=M.documentElement,F=!T(M),(n=M.defaultView)&&n.top!==n&&(n.addEventListener?n.addEventListener("unload",ke,!1):n.attachEvent&&n.attachEvent("onunload",ke)),w.attributes=a(function(e){return e.className="i",!e.getAttribute("className")}),w.getElementsByTagName=a(function(e){return e.appendChild(M.createComment("")),!e.getElementsByTagName("*").length}),w.getElementsByClassName=me.test(M.getElementsByClassName),w.getById=a(function(e){return H.appendChild(e).id=B,!M.getElementsByName||!M.getElementsByName(B).length}),w.getById?(k.find.ID=function(e,t){if(void 0!==t.getElementById&&F){var n=t.getElementById(e);return n?[n]:[]}},k.filter.ID=function(e){var t=e.replace(xe,we);return function(e){return e.getAttribute("id")===t}}):(delete k.find.ID,k.filter.ID=function(e){var t=e.replace(xe,we);return function(e){var n=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}}),k.find.TAG=w.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):w.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,i=[],a=0,o=t.getElementsByTagName(e);if("*"===e){for(;n=o[a++];)1===n.nodeType&&i.push(n);return i}return o},k.find.CLASS=w.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&F)return t.getElementsByClassName(e)},q=[],I=[],(w.qsa=me.test(M.querySelectorAll))&&(a(function(e){H.appendChild(e).innerHTML="<a id='"+B+"'></a><select id='"+B+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&I.push("[*^$]="+ne+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||I.push("\\["+ne+"*(?:value|"+te+")"),e.querySelectorAll("[id~="+B+"-]").length||I.push("~="),e.querySelectorAll(":checked").length||I.push(":checked"),e.querySelectorAll("a#"+B+"+*").length||I.push(".#.+[+~]")}),a(function(e){var t=M.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&I.push("name"+ne+"*[*^$|!~]?="),e.querySelectorAll(":enabled").length||I.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),I.push(",.*:")})),(w.matchesSelector=me.test(P=H.matches||H.webkitMatchesSelector||H.mozMatchesSelector||H.oMatchesSelector||H.msMatchesSelector))&&a(function(e){w.disconnectedMatch=P.call(e,"div"),P.call(e,"[s!='']:x"),q.push("!=",oe)}),I=I.length&&new RegExp(I.join("|")),q=q.length&&new RegExp(q.join("|")),t=me.test(H.compareDocumentPosition),_=t||me.test(H.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,i=t&&t.parentNode;return e===i||!(!i||1!==i.nodeType||!(n.contains?n.contains(i):e.compareDocumentPosition&&16&e.compareDocumentPosition(i)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},Y=t?function(e,t){if(e===t)return j=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1,1&n||!w.sortDetached&&t.compareDocumentPosition(e)===n?e===M||e.ownerDocument===z&&_(z,e)?-1:t===M||t.ownerDocument===z&&_(z,t)?1:A?ee(A,e)-ee(A,t):0:4&n?-1:1)}:function(e,t){if(e===t)return j=!0,0;var n,i=0,a=e.parentNode,o=t.parentNode,l=[e],s=[t];if(!a||!o)return e===M?-1:t===M?1:a?-1:o?1:A?ee(A,e)-ee(A,t):0;if(a===o)return r(e,t);for(n=e;n=n.parentNode;)l.unshift(n);for(n=t;n=n.parentNode;)s.unshift(n);for(;l[i]===s[i];)i++;return i?r(l[i],s[i]):l[i]===z?-1:s[i]===z?1:0},M):M},t.matches=function(e,n){return t(e,null,null,n)},t.matchesSelector=function(e,n){if((e.ownerDocument||e)!==M&&N(e),n=n.replace(ue,"='$1']"),w.matchesSelector&&F&&!X[n+" "]&&(!q||!q.test(n))&&(!I||!I.test(n)))try{var i=P.call(e,n);if(i||w.disconnectedMatch||e.document&&11!==e.document.nodeType)return i}catch(e){}return t(n,M,null,[e]).length>0},t.contains=function(e,t){return(e.ownerDocument||e)!==M&&N(e),_(e,t)},t.attr=function(e,t){(e.ownerDocument||e)!==M&&N(e);var n=k.attrHandle[t.toLowerCase()],i=n&&K.call(k.attrHandle,t.toLowerCase())?n(e,t,!F):void 0;return void 0!==i?i:w.attributes||!F?e.getAttribute(t):(i=e.getAttributeNode(t))&&i.specified?i.value:null},t.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},t.uniqueSort=function(e){var t,n=[],i=0,a=0;if(j=!w.detectDuplicates,A=!w.sortStable&&e.slice(0),e.sort(Y),j){for(;t=e[a++];)t===e[a]&&(i=n.push(a));for(;i--;)e.splice(n[i],1)}return A=null,e},C=t.getText=function(e){var t,n="",i=0,a=e.nodeType;if(a){if(1===a||9===a||11===a){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=C(e)}else if(3===a||4===a)return e.nodeValue}else for(;t=e[i++];)n+=C(t);return n},k=t.selectors={cacheLength:50,createPseudo:i,match:pe,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(xe,we),e[3]=(e[3]||e[4]||e[5]||"").replace(xe,we),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||t.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&t.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return pe.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&de.test(n)&&(t=E(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(xe,we).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=W[e+" "];return t||(t=new RegExp("(^|"+ne+")"+e+"("+ne+"|$)"))&&W(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(e,n,i){return function(a){var o=t.attr(a,e);return null==o?"!="===n:!n||(o+="","="===n?o===i:"!="===n?o!==i:"^="===n?i&&0===o.indexOf(i):"*="===n?i&&o.indexOf(i)>-1:"$="===n?i&&o.slice(-i.length)===i:"~="===n?(" "+o.replace(re," ")+" ").indexOf(i)>-1:"|="===n&&(o===i||o.slice(0,i.length+1)===i+"-"))}},CHILD:function(e,t,n,i,a){var o="nth"!==e.slice(0,3),r="last"!==e.slice(-4),l="of-type"===t;return 1===i&&0===a?function(e){return!!e.parentNode}:function(t,n,s){var c,u,d,f,p,h,y=o!==r?"nextSibling":"previousSibling",m=t.parentNode,v=l&&t.nodeName.toLowerCase(),g=!s&&!l,b=!1;if(m){if(o){for(;y;){for(f=t;f=f[y];)if(l?f.nodeName.toLowerCase()===v:1===f.nodeType)return!1;h=y="only"===e&&!h&&"nextSibling"}return!0}if(h=[r?m.firstChild:m.lastChild],r&&g){for(f=m,d=f[B]||(f[B]={}),u=d[f.uniqueID]||(d[f.uniqueID]={}),c=u[e]||[],p=c[0]===O&&c[1],b=p&&c[2],f=p&&m.childNodes[p];f=++p&&f&&f[y]||(b=p=0)||h.pop();)if(1===f.nodeType&&++b&&f===t){u[e]=[O,p,b];break}}else if(g&&(f=t,d=f[B]||(f[B]={}),u=d[f.uniqueID]||(d[f.uniqueID]={}),c=u[e]||[],p=c[0]===O&&c[1],b=p),!1===b)for(;(f=++p&&f&&f[y]||(b=p=0)||h.pop())&&((l?f.nodeName.toLowerCase()!==v:1!==f.nodeType)||!++b||(g&&(d=f[B]||(f[B]={}),u=d[f.uniqueID]||(d[f.uniqueID]={}),u[e]=[O,b]),f!==t)););return b-=a,b===i||b%i==0&&b/i>=0}}},PSEUDO:function(e,n){var a,o=k.pseudos[e]||k.setFilters[e.toLowerCase()]||t.error("unsupported pseudo: "+e);return o[B]?o(n):o.length>1?(a=[e,e,"",n],k.setFilters.hasOwnProperty(e.toLowerCase())?i(function(e,t){for(var i,a=o(e,n),r=a.length;r--;)i=ee(e,a[r]),e[i]=!(t[i]=a[r])}):function(e){return o(e,0,a)}):o}},pseudos:{not:i(function(e){var t=[],n=[],a=D(e.replace(le,"$1"));return a[B]?i(function(e,t,n,i){for(var o,r=a(e,null,i,[]),l=e.length;l--;)(o=r[l])&&(e[l]=!(t[l]=o))}):function(e,i,o){return t[0]=e,a(t,null,o,n),t[0]=null,!n.pop()}}),has:i(function(e){return function(n){return t(e,n).length>0}}),contains:i(function(e){return e=e.replace(xe,we),function(t){return(t.textContent||t.innerText||C(t)).indexOf(e)>-1}}),lang:i(function(e){return fe.test(e||"")||t.error("unsupported lang: "+e),e=e.replace(xe,we).toLowerCase(),function(t){var n;do{if(n=F?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return n=n.toLowerCase(),n===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===H},focus:function(e){return e===M.activeElement&&(!M.hasFocus||M.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!k.pseudos.empty(e)},header:function(e){return ye.test(e.nodeName)},input:function(e){return he.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:c(function(){return[0]}),last:c(function(e,t){return[t-1]}),eq:c(function(e,t,n){return[n<0?n+t:n]}),even:c(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:c(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:c(function(e,t,n){for(var i=n<0?n+t:n;--i>=0;)e.push(i);return e}),gt:c(function(e,t,n){for(var i=n<0?n+t:n;++i<t;)e.push(i);return e})}},k.pseudos.nth=k.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})k.pseudos[x]=l(x);for(x in{submit:!0,reset:!0})k.pseudos[x]=s(x);return d.prototype=k.filters=k.pseudos,k.setFilters=new d,E=t.tokenize=function(e,n){var i,a,o,r,l,s,c,u=$[e+" "];if(u)return n?0:u.slice(0);for(l=e,s=[],c=k.preFilter;l;){for(r in i&&!(a=se.exec(l))||(a&&(l=l.slice(a[0].length)||l),s.push(o=[])),i=!1,(a=ce.exec(l))&&(i=a.shift(),o.push({value:i,type:a[0].replace(le," ")}),l=l.slice(i.length)),k.filter)!(a=pe[r].exec(l))||c[r]&&!(a=c[r](a))||(i=a.shift(),o.push({value:i,type:r,matches:a}),l=l.slice(i.length));if(!i)break}return n?l.length:l?t.error(e):$(e,s).slice(0)},D=t.compile=function(e,t){var n,i=[],a=[],o=X[e+" "];if(!o){for(t||(t=E(e)),n=t.length;n--;)o=g(t[n]),o[B]?i.push(o):a.push(o);o=X(e,b(a,i)),o.selector=e}return o},S=t.select=function(e,t,n,i){var a,o,r,l,s,c="function"==typeof e&&e,d=!i&&E(e=c.selector||e);if(n=n||[],1===d.length){if(o=d[0]=d[0].slice(0),o.length>2&&"ID"===(r=o[0]).type&&w.getById&&9===t.nodeType&&F&&k.relative[o[1].type]){if(t=(k.find.ID(r.matches[0].replace(xe,we),t)||[])[0],!t)return n;c&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(a=pe.needsContext.test(e)?0:o.length;a--&&(r=o[a],!k.relative[l=r.type]);)if((s=k.find[l])&&(i=s(r.matches[0].replace(xe,we),ge.test(o[0].type)&&u(t.parentNode)||t))){if(o.splice(a,1),e=i.length&&f(o),!e)return Q.apply(n,i),n;break}}return(c||D(e,d))(i,t,!F,n,!t||ge.test(e)&&u(t.parentNode)||t),n},w.sortStable=B.split("").sort(Y).join("")===B,w.detectDuplicates=!!j,N(),w.sortDetached=a(function(e){return 1&e.compareDocumentPosition(M.createElement("div"))}),a(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||o("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),w.attributes&&a(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||o("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),a(function(e){return null==e.getAttribute("disabled")})||o(te,function(e,t,n){var i;if(!n)return!0===e[t]?t.toLowerCase():(i=e.getAttributeNode(t))&&i.specified?i.value:null}),t}(e);pe.find=ge,pe.expr=ge.selectors,pe.expr[":"]=pe.expr.pseudos,pe.uniqueSort=pe.unique=ge.uniqueSort,pe.text=ge.getText,pe.isXMLDoc=ge.isXML,pe.contains=ge.contains;var be=function(e,t,n){for(var i=[],a=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(a&&pe(e).is(n))break;i.push(e)}return i},xe=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},we=pe.expr.match.needsContext,ke=/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/,Ce=/^.[^:#\[\.,]*$/;pe.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?pe.find.matchesSelector(i,e)?[i]:[]:pe.find.matches(e,pe.grep(t,function(e){return 1===e.nodeType}))},pe.fn.extend({find:function(e){var t,n=[],i=this,a=i.length;if("string"!=typeof e)return this.pushStack(pe(e).filter(function(){for(t=0;t<a;t++)if(pe.contains(i[t],this))return!0}));for(t=0;t<a;t++)pe.find(e,i[t],n);return n=this.pushStack(a>1?pe.unique(n):n),n.selector=this.selector?this.selector+" "+e:e,n},filter:function(e){return this.pushStack(i(this,e||[],!1))},not:function(e){return this.pushStack(i(this,e||[],!0))},is:function(e){return!!i(this,"string"==typeof e&&we.test(e)?pe(e):e||[],!1).length}});var Te,Ee=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,De=pe.fn.init=function(e,t,n){var i,a;if(!e)return this;if(n=n||Te,"string"==typeof e){if(i="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:Ee.exec(e),!i||!i[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(i[1]){if(t=t instanceof pe?t[0]:t,pe.merge(this,pe.parseHTML(i[1],t&&t.nodeType?t.ownerDocument||t:ie,!0)),ke.test(i[1])&&pe.isPlainObject(t))for(i in t)pe.isFunction(this[i])?this[i](t[i]):this.attr(i,t[i]);return this}if(a=ie.getElementById(i[2]),a&&a.parentNode){if(a.id!==i[2])return Te.find(e);this.length=1,this[0]=a}return this.context=ie,this.selector=e,this}return e.nodeType?(this.context=this[0]=e,this.length=1,this):pe.isFunction(e)?void 0!==n.ready?n.ready(e):e(pe):(void 0!==e.selector&&(this.selector=e.selector,this.context=e.context),pe.makeArray(e,this))};De.prototype=pe.fn,Te=pe(ie);var Se=/^(?:parents|prev(?:Until|All))/,Le={children:!0,contents:!0,next:!0,prev:!0};pe.fn.extend({has:function(e){var t,n=pe(e,this),i=n.length;return this.filter(function(){for(t=0;t<i;t++)if(pe.contains(this,n[t]))return!0})},closest:function(e,t){for(var n,i=0,a=this.length,o=[],r=we.test(e)||"string"!=typeof e?pe(e,t||this.context):0;i<a;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(r?r.index(n)>-1:1===n.nodeType&&pe.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?pe.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?pe.inArray(this[0],pe(e)):pe.inArray(e.jquery?e[0]:e,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(pe.uniqueSort(pe.merge(this.get(),pe(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),pe.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return be(e,"parentNode")},parentsUntil:function(e,t,n){return be(e,"parentNode",n)},next:function(e){return a(e,"nextSibling")},prev:function(e){return a(e,"previousSibling")},nextAll:function(e){return be(e,"nextSibling")},prevAll:function(e){return be(e,"previousSibling")},nextUntil:function(e,t,n){return be(e,"nextSibling",n)},prevUntil:function(e,t,n){return be(e,"previousSibling",n)},siblings:function(e){return xe((e.parentNode||{}).firstChild,e)},children:function(e){return xe(e.firstChild)},contents:function(e){return pe.nodeName(e,"iframe")?e.contentDocument||e.contentWindow.document:pe.merge([],e.childNodes)}},function(e,t){pe.fn[e]=function(n,i){var a=pe.map(this,t,n);return"Until"!==e.slice(-5)&&(i=n),i&&"string"==typeof i&&(a=pe.filter(i,a)),this.length>1&&(Le[e]||(a=pe.uniqueSort(a)),Se.test(e)&&(a=a.reverse())),this.pushStack(a)}});var Ae,je,Ne=/\S+/g;for(je in pe.Callbacks=function(e){e="string"==typeof e?o(e):pe.extend({},e);var t,n,i,a,r=[],l=[],s=-1,c=function(){for(a=e.once,i=t=!0;l.length;s=-1)for(n=l.shift();++s<r.length;)!1===r[s].apply(n[0],n[1])&&e.stopOnFalse&&(s=r.length,n=!1);e.memory||(n=!1),t=!1,a&&(r=n?[]:"")},u={add:function(){return r&&(n&&!t&&(s=r.length-1,l.push(n)),function t(n){pe.each(n,function(n,i){pe.isFunction(i)?e.unique&&u.has(i)||r.push(i):i&&i.length&&"string"!==pe.type(i)&&t(i)})}(arguments),n&&!t&&c()),this},remove:function(){return pe.each(arguments,function(e,t){for(var n;(n=pe.inArray(t,r,n))>-1;)r.splice(n,1),n<=s&&s--}),this},has:function(e){return e?pe.inArray(e,r)>-1:r.length>0},empty:function(){return r&&(r=[]),this},disable:function(){return a=l=[],r=n="",this},disabled:function(){return!r},lock:function(){return a=!0,n||u.disable(),this},locked:function(){return!!a},fireWith:function(e,n){return a||(n=n||[],n=[e,n.slice?n.slice():n],l.push(n),t||c()),this},fire:function(){return u.fireWith(this,arguments),this},fired:function(){return!!i}};return u},pe.extend({Deferred:function(e){var t=[["resolve","done",pe.Callbacks("once memory"),"resolved"],["reject","fail",pe.Callbacks("once memory"),"rejected"],["notify","progress",pe.Callbacks("memory")]],n="pending",i={state:function(){return n},always:function(){return a.done(arguments).fail(arguments),this},then:function(){var e=arguments;return pe.Deferred(function(n){pe.each(t,function(t,o){var r=pe.isFunction(e[t])&&e[t];a[o[1]](function(){var e=r&&r.apply(this,arguments);e&&pe.isFunction(e.promise)?e.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[o[0]+"With"](this===i?n.promise():this,r?[e]:arguments)})}),e=null}).promise()},promise:function(e){return null!=e?pe.extend(e,i):i}},a={};return i.pipe=i.then,pe.each(t,function(e,o){var r=o[2],l=o[3];i[o[1]]=r.add,l&&r.add(function(){n=l},t[1^e][2].disable,t[2][2].lock),a[o[0]]=function(){return a[o[0]+"With"](this===a?i:this,arguments),this},a[o[0]+"With"]=r.fireWith}),i.promise(a),e&&e.call(a,a),a},when:function(e){var t,n,i,a=0,o=ae.call(arguments),r=o.length,l=1!==r||e&&pe.isFunction(e.promise)?r:0,s=1===l?e:pe.Deferred(),c=function(e,n,i){return function(a){n[e]=this,i[e]=arguments.length>1?ae.call(arguments):a,i===t?s.notifyWith(n,i):--l||s.resolveWith(n,i)}};if(r>1)for(t=new Array(r),n=new Array(r),i=new Array(r);a<r;a++)o[a]&&pe.isFunction(o[a].promise)?o[a].promise().progress(c(a,n,t)).done(c(a,i,o)).fail(s.reject):--l;return l||s.resolveWith(i,o),s.promise()}}),pe.fn.ready=function(e){return pe.ready.promise().done(e),this},pe.extend({isReady:!1,readyWait:1,holdReady:function(e){e?pe.readyWait++:pe.ready(!0)},ready:function(e){(!0===e?--pe.readyWait:pe.isReady)||(pe.isReady=!0,!0!==e&&--pe.readyWait>0||(Ae.resolveWith(ie,[pe]),pe.fn.triggerHandler&&(pe(ie).triggerHandler("ready"),pe(ie).off("ready"))))}}),pe.ready.promise=function(t){if(!Ae)if(Ae=pe.Deferred(),"complete"===ie.readyState||"loading"!==ie.readyState&&!ie.documentElement.doScroll)e.setTimeout(pe.ready);else if(ie.addEventListener)ie.addEventListener("DOMContentLoaded",l),e.addEventListener("load",l);else{ie.attachEvent("onreadystatechange",l),e.attachEvent("onload",l);var n=!1;try{n=null==e.frameElement&&ie.documentElement}catch(e){}n&&n.doScroll&&function t(){if(!pe.isReady){try{n.doScroll("left")}catch(n){return e.setTimeout(t,50)}r(),pe.ready()}}()}return Ae.promise(t)},pe.ready.promise(),pe(de))break;de.ownFirst="0"===je,de.inlineBlockNeedsLayout=!1,pe(function(){var e,t,n,i;n=ie.getElementsByTagName("body")[0],n&&n.style&&(t=ie.createElement("div"),i=ie.createElement("div"),i.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(i).appendChild(t),void 0!==t.style.zoom&&(t.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",de.inlineBlockNeedsLayout=e=3===t.offsetWidth,e&&(n.style.zoom=1)),n.removeChild(i))}),function(){var e=ie.createElement("div");de.deleteExpando=!0;try{delete e.test}catch(e){de.deleteExpando=!1}e=null}();var Me=function(e){var t=pe.noData[(e.nodeName+" ").toLowerCase()],n=+e.nodeType||1;return(1===n||9===n)&&(!t||!0!==t&&e.getAttribute("classid")===t)},He=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Fe=/([A-Z])/g;pe.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"},hasData:function(e){return e=e.nodeType?pe.cache[e[pe.expando]]:e[pe.expando],!!e&&!c(e)},data:function(e,t,n){return u(e,t,n)},removeData:function(e,t){return d(e,t)},_data:function(e,t,n){return u(e,t,n,!0)},_removeData:function(e,t){return d(e,t,!0)}}),pe.fn.extend({data:function(e,t){var n,i,a,o=this[0],r=o&&o.attributes;if(void 0===e){if(this.length&&(a=pe.data(o),1===o.nodeType&&!pe._data(o,"parsedAttrs"))){for(n=r.length;n--;)r[n]&&(i=r[n].name,0===i.indexOf("data-")&&(i=pe.camelCase(i.slice(5)),s(o,i,a[i])));pe._data(o,"parsedAttrs",!0)}return a}return"object"==typeof e?this.each(function(){pe.data(this,e)}):arguments.length>1?this.each(function(){pe.data(this,e,t)}):o?s(o,e,pe.data(o,e)):void 0},removeData:function(e){return this.each(function(){pe.removeData(this,e)})}}),pe.extend({queue:function(e,t,n){var i;if(e)return t=(t||"fx")+"queue",i=pe._data(e,t),n&&(!i||pe.isArray(n)?i=pe._data(e,t,pe.makeArray(n)):i.push(n)),i||[]},dequeue:function(e,t){t=t||"fx";var n=pe.queue(e,t),i=n.length,a=n.shift(),o=pe._queueHooks(e,t),r=function(){pe.dequeue(e,t)};"inprogress"===a&&(a=n.shift(),i--),a&&("fx"===t&&n.unshift("inprogress"),delete o.stop,a.call(e,r,o)),!i&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return pe._data(e,n)||pe._data(e,n,{empty:pe.Callbacks("once memory").add(function(){pe._removeData(e,t+"queue"),pe._removeData(e,n)})})}}),pe.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?pe.queue(this[0],e):void 0===t?this:this.each(function(){var n=pe.queue(this,e,t);pe._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&pe.dequeue(this,e)})},dequeue:function(e){return this.each(function(){pe.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,a=pe.Deferred(),o=this,r=this.length,l=function(){--i||a.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";r--;)n=pe._data(o[r],e+"queueHooks"),n&&n.empty&&(i++,n.empty.add(l));return l(),a.promise(t)}}),function(){var e;de.shrinkWrapBlocks=function(){return null!=e?e:(e=!1,n=ie.getElementsByTagName("body")[0],n&&n.style?(t=ie.createElement("div"),i=ie.createElement("div"),i.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(i).appendChild(t),void 0!==t.style.zoom&&(t.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:1px;width:1px;zoom:1",t.appendChild(ie.createElement("div")).style.width="5px",e=3!==t.offsetWidth),n.removeChild(i),e):void 0);var t,n,i}}();var Ie=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,qe=new RegExp("^(?:([+-])=|)("+Ie+")([a-z%]*)$","i"),Pe=["Top","Right","Bottom","Left"],_e=function(e,t){return e=t||e,"none"===pe.css(e,"display")||!pe.contains(e.ownerDocument,e)},Be=function(e,t,n,i,a,o,r){var l=0,s=e.length,c=null==n;if("object"===pe.type(n))for(l in a=!0,n)Be(e,t,l,n[l],!0,o,r);else if(void 0!==i&&(a=!0,pe.isFunction(i)||(r=!0),c&&(r?(t.call(e,i),t=null):(c=t,t=function(e,t,n){return c.call(pe(e),n)})),t))for(;l<s;l++)t(e[l],n,r?i:i.call(e[l],l,t(e[l],n)));return a?e:c?t.call(e):s?t(e[0],n):o},ze=/^(?:checkbox|radio)$/i,Oe=/<([\w:-]+)/,Re=/^$|\/(?:java|ecma)script/i,We=/^\s+/,$e="abbr|article|aside|audio|bdi|canvas|data|datalist|details|dialog|figcaption|figure|footer|header|hgroup|main|mark|meter|nav|output|picture|progress|section|summary|template|time|video";!function(){var e=ie.createElement("div"),t=ie.createDocumentFragment(),n=ie.createElement("input");e.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",de.leadingWhitespace=3===e.firstChild.nodeType,de.tbody=!e.getElementsByTagName("tbody").length,de.htmlSerialize=!!e.getElementsByTagName("link").length,de.html5Clone="<:nav></:nav>"!==ie.createElement("nav").cloneNode(!0).outerHTML,n.type="checkbox",n.checked=!0,t.appendChild(n),de.appendChecked=n.checked,e.innerHTML="<textarea>x</textarea>",de.noCloneChecked=!!e.cloneNode(!0).lastChild.defaultValue,t.appendChild(e),n=ie.createElement("input"),n.setAttribute("type","radio"),n.setAttribute("checked","checked"),n.setAttribute("name","t"),e.appendChild(n),de.checkClone=e.cloneNode(!0).cloneNode(!0).lastChild.checked,de.noCloneEvent=!!e.addEventListener,e[pe.expando]=1,de.attributes=!e.getAttribute(pe.expando)}();var Xe={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:de.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]};Xe.optgroup=Xe.option,Xe.tbody=Xe.tfoot=Xe.colgroup=Xe.caption=Xe.thead,Xe.th=Xe.td;var Ye=/<|&#?\w+;/,Ve=/<tbody/i;!function(){var t,n,i=ie.createElement("div");for(t in{submit:!0,change:!0,focusin:!0})n="on"+t,(de[t]=n in e)||(i.setAttribute(n,"t"),de[t]=!1===i.attributes[n].expando);i=null}();var Ke=/^(?:input|select|textarea)$/i,Ge=/^key/,Je=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Ue=/^(?:focusinfocus|focusoutblur)$/,Qe=/^([^.]*)(?:\.(.+)|)/;pe.event={global:{},add:function(e,t,n,i,a){var o,r,l,s,c,u,d,f,p,h,y,m=pe._data(e);if(m){for(n.handler&&(s=n,n=s.handler,a=s.selector),n.guid||(n.guid=pe.guid++),(r=m.events)||(r=m.events={}),(u=m.handle)||(u=m.handle=function(e){return void 0===pe||e&&pe.event.triggered===e.type?void 0:pe.event.dispatch.apply(u.elem,arguments)},u.elem=e),t=(t||"").match(Ne)||[""],l=t.length;l--;)o=Qe.exec(t[l])||[],p=y=o[1],h=(o[2]||"").split(".").sort(),p&&(c=pe.event.special[p]||{},p=(a?c.delegateType:c.bindType)||p,c=pe.event.special[p]||{},d=pe.extend({type:p,origType:y,data:i,handler:n,guid:n.guid,selector:a,needsContext:a&&pe.expr.match.needsContext.test(a),namespace:h.join(".")},s),(f=r[p])||(f=r[p]=[],f.delegateCount=0,c.setup&&!1!==c.setup.call(e,i,h,u)||(e.addEventListener?e.addEventListener(p,u,!1):e.attachEvent&&e.attachEvent("on"+p,u))),c.add&&(c.add.call(e,d),d.handler.guid||(d.handler.guid=n.guid)),a?f.splice(f.delegateCount++,0,d):f.push(d),pe.event.global[p]=!0);e=null}},remove:function(e,t,n,i,a){var o,r,l,s,c,u,d,f,p,h,y,m=pe.hasData(e)&&pe._data(e);if(m&&(u=m.events)){for(t=(t||"").match(Ne)||[""],c=t.length;c--;)if(l=Qe.exec(t[c])||[],p=y=l[1],h=(l[2]||"").split(".").sort(),p){for(d=pe.event.special[p]||{},p=(i?d.delegateType:d.bindType)||p,f=u[p]||[],l=l[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=f.length;o--;)r=f[o],!a&&y!==r.origType||n&&n.guid!==r.guid||l&&!l.test(r.namespace)||i&&i!==r.selector&&("**"!==i||!r.selector)||(f.splice(o,1),r.selector&&f.delegateCount--,d.remove&&d.remove.call(e,r));s&&!f.length&&(d.teardown&&!1!==d.teardown.call(e,h,m.handle)||pe.removeEvent(e,p,m.handle),delete u[p])}else for(p in u)pe.event.remove(e,p+t[c],n,i,!0);pe.isEmptyObject(u)&&(delete m.handle,pe._removeData(e,"events"))}},trigger:function(t,n,i,a){var o,r,l,s,c,u,d,f=[i||ie],p=ue.call(t,"type")?t.type:t,h=ue.call(t,"namespace")?t.namespace.split("."):[];if(l=u=i=i||ie,3!==i.nodeType&&8!==i.nodeType&&!Ue.test(p+pe.event.triggered)&&(p.indexOf(".")>-1&&(h=p.split("."),p=h.shift(),h.sort()),r=p.indexOf(":")<0&&"on"+p,t=t[pe.expando]?t:new pe.Event(p,"object"==typeof t&&t),t.isTrigger=a?2:3,t.namespace=h.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=i),n=null==n?[t]:pe.makeArray(n,[t]),c=pe.event.special[p]||{},a||!c.trigger||!1!==c.trigger.apply(i,n))){if(!a&&!c.noBubble&&!pe.isWindow(i)){for(s=c.delegateType||p,Ue.test(s+p)||(l=l.parentNode);l;l=l.parentNode)f.push(l),u=l;u===(i.ownerDocument||ie)&&f.push(u.defaultView||u.parentWindow||e)}for(d=0;(l=f[d++])&&!t.isPropagationStopped();)t.type=d>1?s:c.bindType||p,o=(pe._data(l,"events")||{})[t.type]&&pe._data(l,"handle"),o&&o.apply(l,n),o=r&&l[r],o&&o.apply&&Me(l)&&(t.result=o.apply(l,n),!1===t.result&&t.preventDefault());if(t.type=p,!a&&!t.isDefaultPrevented()&&(!c._default||!1===c._default.apply(f.pop(),n))&&Me(i)&&r&&i[p]&&!pe.isWindow(i)){u=i[r],u&&(i[r]=null),pe.event.triggered=p;try{i[p]()}catch(e){}pe.event.triggered=void 0,u&&(i[r]=u)}return t.result}},dispatch:function(e){e=pe.event.fix(e);var t,n,i,a,o,r=[],l=ae.call(arguments),s=(pe._data(this,"events")||{})[e.type]||[],c=pe.event.special[e.type]||{};if(l[0]=e,e.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,e)){for(r=pe.event.handlers.call(this,e,s),t=0;(a=r[t++])&&!e.isPropagationStopped();)for(e.currentTarget=a.elem,n=0;(o=a.handlers[n++])&&!e.isImmediatePropagationStopped();)e.rnamespace&&!e.rnamespace.test(o.namespace)||(e.handleObj=o,e.data=o.data,i=((pe.event.special[o.origType]||{}).handle||o.handler).apply(a.elem,l),void 0!==i&&!1===(e.result=i)&&(e.preventDefault(),e.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,i,a,o,r=[],l=t.delegateCount,s=e.target;if(l&&s.nodeType&&("click"!==e.type||isNaN(e.button)||e.button<1))for(;s!=this;s=s.parentNode||this)if(1===s.nodeType&&(!0!==s.disabled||"click"!==e.type)){for(i=[],n=0;n<l;n++)o=t[n],a=o.selector+" ",void 0===i[a]&&(i[a]=o.needsContext?pe(a,this).index(s)>-1:pe.find(a,this,null,[s]).length),i[a]&&i.push(o);i.length&&r.push({elem:s,handlers:i})}return l<t.length&&r.push({elem:this,handlers:t.slice(l)}),r},fix:function(e){if(e[pe.expando])return e;var t,n,i,a=e.type,o=e,r=this.fixHooks[a];for(r||(this.fixHooks[a]=r=Je.test(a)?this.mouseHooks:Ge.test(a)?this.keyHooks:{}),i=r.props?this.props.concat(r.props):this.props,e=new pe.Event(o),t=i.length;t--;)n=i[t],e[n]=o[n];return e.target||(e.target=o.srcElement||ie),3===e.target.nodeType&&(e.target=e.target.parentNode),e.metaKey=!!e.metaKey,r.filter?r.filter(e,o):e},props:"altKey bubbles cancelable ctrlKey currentTarget detail eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,i,a,o=t.button,r=t.fromElement;return null==e.pageX&&null!=t.clientX&&(i=e.target.ownerDocument||ie,a=i.documentElement,n=i.body,e.pageX=t.clientX+(a&&a.scrollLeft||n&&n.scrollLeft||0)-(a&&a.clientLeft||n&&n.clientLeft||0),e.pageY=t.clientY+(a&&a.scrollTop||n&&n.scrollTop||0)-(a&&a.clientTop||n&&n.clientTop||0)),!e.relatedTarget&&r&&(e.relatedTarget=r===e.target?t.toElement:r),e.which||void 0===o||(e.which=1&o?1:2&o?3:4&o?2:0),e}},special:{load:{noBubble:!0
},focus:{trigger:function(){if(this!==x()&&this.focus)try{return this.focus(),!1}catch(e){}},delegateType:"focusin"},blur:{trigger:function(){if(this===x()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if(pe.nodeName(this,"input")&&"checkbox"===this.type&&this.click)return this.click(),!1},_default:function(e){return pe.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,n){var i=pe.extend(new pe.Event,n,{type:e,isSimulated:!0});pe.event.trigger(i,null,t),i.isDefaultPrevented()&&n.preventDefault()}},pe.removeEvent=ie.removeEventListener?function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)}:function(e,t,n){var i="on"+t;e.detachEvent&&(void 0===e[i]&&(e[i]=null),e.detachEvent(i,n))},pe.Event=function(e,t){return this instanceof pe.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?g:b):this.type=e,t&&pe.extend(this,t),this.timeStamp=e&&e.timeStamp||pe.now(),void(this[pe.expando]=!0)):new pe.Event(e,t)},pe.Event.prototype={constructor:pe.Event,isDefaultPrevented:b,isPropagationStopped:b,isImmediatePropagationStopped:b,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=g,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=g,e&&!this.isSimulated&&(e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0)},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=g,e&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),this.stopPropagation()}},pe.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){pe.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,i=this,a=e.relatedTarget,o=e.handleObj;return a&&(a===i||pe.contains(i,a))||(e.type=o.origType,n=o.handler.apply(this,arguments),e.type=t),n}}}),de.submit||(pe.event.special.submit={setup:function(){return!pe.nodeName(this,"form")&&void pe.event.add(this,"click._submit keypress._submit",function(e){var t=e.target,n=pe.nodeName(t,"input")||pe.nodeName(t,"button")?pe.prop(t,"form"):void 0;n&&!pe._data(n,"submit")&&(pe.event.add(n,"submit._submit",function(e){e._submitBubble=!0}),pe._data(n,"submit",!0))})},postDispatch:function(e){e._submitBubble&&(delete e._submitBubble,this.parentNode&&!e.isTrigger&&pe.event.simulate("submit",this.parentNode,e))},teardown:function(){return!pe.nodeName(this,"form")&&void pe.event.remove(this,"._submit")}}),de.change||(pe.event.special.change={setup:function(){return Ke.test(this.nodeName)?("checkbox"!==this.type&&"radio"!==this.type||(pe.event.add(this,"propertychange._change",function(e){"checked"===e.originalEvent.propertyName&&(this._justChanged=!0)}),pe.event.add(this,"click._change",function(e){this._justChanged&&!e.isTrigger&&(this._justChanged=!1),pe.event.simulate("change",this,e)})),!1):void pe.event.add(this,"beforeactivate._change",function(e){var t=e.target;Ke.test(t.nodeName)&&!pe._data(t,"change")&&(pe.event.add(t,"change._change",function(e){!this.parentNode||e.isSimulated||e.isTrigger||pe.event.simulate("change",this.parentNode,e)}),pe._data(t,"change",!0))})},handle:function(e){var t=e.target;if(this!==t||e.isSimulated||e.isTrigger||"radio"!==t.type&&"checkbox"!==t.type)return e.handleObj.handler.apply(this,arguments)},teardown:function(){return pe.event.remove(this,"._change"),!Ke.test(this.nodeName)}}),de.focusin||pe.each({focus:"focusin",blur:"focusout"},function(e,t){var n=function(e){pe.event.simulate(t,e.target,pe.event.fix(e))};pe.event.special[t]={setup:function(){var i=this.ownerDocument||this,a=pe._data(i,t);a||i.addEventListener(e,n,!0),pe._data(i,t,(a||0)+1)},teardown:function(){var i=this.ownerDocument||this,a=pe._data(i,t)-1;a?pe._data(i,t,a):(i.removeEventListener(e,n,!0),pe._removeData(i,t))}}}),pe.fn.extend({on:function(e,t,n,i){return w(this,e,t,n,i)},one:function(e,t,n,i){return w(this,e,t,n,i,1)},off:function(e,t,n){var i,a;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,pe(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof e){for(a in e)this.off(a,t,e[a]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=b),this.each(function(){pe.event.remove(this,e,n,t)})},trigger:function(e,t){return this.each(function(){pe.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return pe.event.trigger(e,t,n,!0)}});var Ze=/ jQuery\d+="(?:null|\d+)"/g,et=new RegExp("<(?:"+$e+")[\\s/>]","i"),tt=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:-]+)[^>]*)\/>/gi,nt=/<script|<style|<link/i,it=/checked\s*(?:[^=]|=\s*.checked.)/i,at=/^true\/(.*)/,ot=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,rt=p(ie),lt=rt.appendChild(ie.createElement("div"));pe.extend({htmlPrefilter:function(e){return e.replace(tt,"<$1></$2>")},clone:function(e,t,n){var i,a,o,r,l,s=pe.contains(e.ownerDocument,e);if(de.html5Clone||pe.isXMLDoc(e)||!et.test("<"+e.nodeName+">")?o=e.cloneNode(!0):(lt.innerHTML=e.outerHTML,lt.removeChild(o=lt.firstChild)),!(de.noCloneEvent&&de.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||pe.isXMLDoc(e)))for(i=h(o),l=h(e),r=0;null!=(a=l[r]);++r)i[r]&&D(a,i[r]);if(t)if(n)for(l=l||h(e),i=i||h(o),r=0;null!=(a=l[r]);r++)E(a,i[r]);else E(e,o);return i=h(o,"script"),i.length>0&&y(i,!s&&h(e,"script")),i=l=a=null,o},cleanData:function(e,t){for(var n,i,a,o,r=0,l=pe.expando,s=pe.cache,c=de.attributes,u=pe.event.special;null!=(n=e[r]);r++)if((t||Me(n))&&(a=n[l],o=a&&s[a])){if(o.events)for(i in o.events)u[i]?pe.event.remove(n,i):pe.removeEvent(n,i,o.handle);s[a]&&(delete s[a],c||void 0===n.removeAttribute?n[l]=void 0:n.removeAttribute(l),ne.push(a))}}}),pe.fn.extend({domManip:S,detach:function(e){return L(this,e,!0)},remove:function(e){return L(this,e)},text:function(e){return Be(this,function(e){return void 0===e?pe.text(this):this.empty().append((this[0]&&this[0].ownerDocument||ie).createTextNode(e))},null,e,arguments.length)},append:function(){return S(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=k(this,e);t.appendChild(e)}})},prepend:function(){return S(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=k(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return S(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return S(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++){for(1===e.nodeType&&pe.cleanData(h(e,!1));e.firstChild;)e.removeChild(e.firstChild);e.options&&pe.nodeName(e,"select")&&(e.options.length=0)}return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return pe.clone(this,e,t)})},html:function(e){return Be(this,function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e)return 1===t.nodeType?t.innerHTML.replace(Ze,""):void 0;if("string"==typeof e&&!nt.test(e)&&(de.htmlSerialize||!et.test(e))&&(de.leadingWhitespace||!We.test(e))&&!Xe[(Oe.exec(e)||["",""])[1].toLowerCase()]){e=pe.htmlPrefilter(e);try{for(;n<i;n++)t=this[n]||{},1===t.nodeType&&(pe.cleanData(h(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=[];return S(this,arguments,function(t){var n=this.parentNode;pe.inArray(this,e)<0&&(pe.cleanData(h(this)),n&&n.replaceChild(t,this))},e)}}),pe.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){pe.fn[e]=function(e){for(var n,i=0,a=[],o=pe(e),r=o.length-1;i<=r;i++)n=i===r?this:this.clone(!0),pe(o[i])[t](n),re.apply(a,n.get());return this.pushStack(a)}});var st,ct={HTML:"block",BODY:"block"},ut=/^margin/,dt=new RegExp("^("+Ie+")(?!px)[a-z%]+$","i"),ft=function(e,t,n,i){var a,o,r={};for(o in t)r[o]=e.style[o],e.style[o]=t[o];for(o in a=n.apply(e,i||[]),t)e.style[o]=r[o];return a},pt=ie.documentElement;!function(){function t(){var t,u,d=ie.documentElement;d.appendChild(s),c.style.cssText="-webkit-box-sizing:border-box;box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%",n=a=l=!1,i=r=!0,e.getComputedStyle&&(u=e.getComputedStyle(c),n="1%"!==(u||{}).top,l="2px"===(u||{}).marginLeft,a="4px"===(u||{width:"4px"}).width,c.style.marginRight="50%",i="4px"===(u||{marginRight:"4px"}).marginRight,t=c.appendChild(ie.createElement("div")),t.style.cssText=c.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",t.style.marginRight=t.style.width="0",c.style.width="1px",r=!parseFloat((e.getComputedStyle(t)||{}).marginRight),c.removeChild(t)),c.style.display="none",o=0===c.getClientRects().length,o&&(c.style.display="",c.innerHTML="<table><tr><td></td><td>t</td></tr></table>",t=c.getElementsByTagName("td"),t[0].style.cssText="margin:0;border:0;padding:0;display:none",o=0===t[0].offsetHeight,o&&(t[0].style.display="",t[1].style.display="none",o=0===t[0].offsetHeight)),d.removeChild(s)}var n,i,a,o,r,l,s=ie.createElement("div"),c=ie.createElement("div");c.style&&(c.style.cssText="float:left;opacity:.5",de.opacity="0.5"===c.style.opacity,de.cssFloat=!!c.style.cssFloat,c.style.backgroundClip="content-box",c.cloneNode(!0).style.backgroundClip="",de.clearCloneStyle="content-box"===c.style.backgroundClip,s=ie.createElement("div"),s.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",c.innerHTML="",s.appendChild(c),de.boxSizing=""===c.style.boxSizing||""===c.style.MozBoxSizing||""===c.style.WebkitBoxSizing,pe.extend(de,{reliableHiddenOffsets:function(){return null==n&&t(),o},boxSizingReliable:function(){return null==n&&t(),a},pixelMarginRight:function(){return null==n&&t(),i},pixelPosition:function(){return null==n&&t(),n},reliableMarginRight:function(){return null==n&&t(),r},reliableMarginLeft:function(){return null==n&&t(),l}}))}();var ht,yt,mt=/^(top|right|bottom|left)$/;e.getComputedStyle?(ht=function(t){var n=t.ownerDocument.defaultView;return n&&n.opener||(n=e),n.getComputedStyle(t)},yt=function(e,t,n){var i,a,o,r,l=e.style;return n=n||ht(e),r=n?n.getPropertyValue(t)||n[t]:void 0,""!==r&&void 0!==r||pe.contains(e.ownerDocument,e)||(r=pe.style(e,t)),n&&!de.pixelMarginRight()&&dt.test(r)&&ut.test(t)&&(i=l.width,a=l.minWidth,o=l.maxWidth,l.minWidth=l.maxWidth=l.width=r,r=n.width,l.width=i,l.minWidth=a,l.maxWidth=o),void 0===r?r:r+""}):pt.currentStyle&&(ht=function(e){return e.currentStyle},yt=function(e,t,n){var i,a,o,r,l=e.style;return n=n||ht(e),r=n?n[t]:void 0,null==r&&l&&l[t]&&(r=l[t]),dt.test(r)&&!mt.test(t)&&(i=l.left,a=e.runtimeStyle,o=a&&a.left,o&&(a.left=e.currentStyle.left),l.left="fontSize"===t?"1em":r,r=l.pixelLeft+"px",l.left=i,o&&(a.left=o)),void 0===r?r:r+""||"auto"});var vt=/alpha\([^)]*\)/i,gt=/opacity\s*=\s*([^)]*)/i,bt=/^(none|table(?!-c[ea]).+)/,xt=new RegExp("^("+Ie+")(.*)$","i"),wt={position:"absolute",visibility:"hidden",display:"block"},kt={letterSpacing:"0",fontWeight:"400"},Ct=["Webkit","O","Moz","ms"],Tt=ie.createElement("div").style;pe.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=yt(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:de.cssFloat?"cssFloat":"styleFloat"},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var a,o,r,l=pe.camelCase(t),s=e.style;if(t=pe.cssProps[l]||(pe.cssProps[l]=M(l)||l),r=pe.cssHooks[t]||pe.cssHooks[l],void 0===n)return r&&"get"in r&&void 0!==(a=r.get(e,!1,i))?a:s[t];if(o=typeof n,"string"===o&&(a=qe.exec(n))&&a[1]&&(n=f(e,t,a),o="number"),null!=n&&n==n&&("number"===o&&(n+=a&&a[3]||(pe.cssNumber[l]?"":"px")),de.clearCloneStyle||""!==n||0!==t.indexOf("background")||(s[t]="inherit"),!(r&&"set"in r&&void 0===(n=r.set(e,n,i)))))try{s[t]=n}catch(e){}}},css:function(e,t,n,i){var a,o,r,l=pe.camelCase(t);return t=pe.cssProps[l]||(pe.cssProps[l]=M(l)||l),r=pe.cssHooks[t]||pe.cssHooks[l],r&&"get"in r&&(o=r.get(e,!0,n)),void 0===o&&(o=yt(e,t,i)),"normal"===o&&t in kt&&(o=kt[t]),""===n||n?(a=parseFloat(o),!0===n||isFinite(a)?a||0:o):o}}),pe.each(["height","width"],function(e,t){pe.cssHooks[t]={get:function(e,n,i){if(n)return bt.test(pe.css(e,"display"))&&0===e.offsetWidth?ft(e,wt,function(){return q(e,t,i)}):q(e,t,i)},set:function(e,n,i){var a=i&&ht(e);return F(e,n,i?I(e,t,i,de.boxSizing&&"border-box"===pe.css(e,"boxSizing",!1,a),a):0)}}}),de.opacity||(pe.cssHooks.opacity={get:function(e,t){return gt.test((t&&e.currentStyle?e.currentStyle.filter:e.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":t?"1":""},set:function(e,t){var n=e.style,i=e.currentStyle,a=pe.isNumeric(t)?"alpha(opacity="+100*t+")":"",o=i&&i.filter||n.filter||"";n.zoom=1,(t>=1||""===t)&&""===pe.trim(o.replace(vt,""))&&n.removeAttribute&&(n.removeAttribute("filter"),""===t||i&&!i.filter)||(n.filter=vt.test(o)?o.replace(vt,a):o+" "+a)}}),pe.cssHooks.marginRight=N(de.reliableMarginRight,function(e,t){if(t)return ft(e,{display:"inline-block"},yt,[e,"marginRight"])}),pe.cssHooks.marginLeft=N(de.reliableMarginLeft,function(e,t){if(t)return(parseFloat(yt(e,"marginLeft"))||(pe.contains(e.ownerDocument,e)?e.getBoundingClientRect().left-ft(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}):0))+"px"}),pe.each({margin:"",padding:"",border:"Width"},function(e,t){pe.cssHooks[e+t]={expand:function(n){for(var i=0,a={},o="string"==typeof n?n.split(" "):[n];i<4;i++)a[e+Pe[i]+t]=o[i]||o[i-2]||o[0];return a}},ut.test(e)||(pe.cssHooks[e+t].set=F)}),pe.fn.extend({css:function(e,t){return Be(this,function(e,t,n){var i,a,o={},r=0;if(pe.isArray(t)){for(i=ht(e),a=t.length;r<a;r++)o[t[r]]=pe.css(e,t[r],!1,i);return o}return void 0!==n?pe.style(e,t,n):pe.css(e,t)},e,t,arguments.length>1)},show:function(){return H(this,!0)},hide:function(){return H(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){_e(this)?pe(this).show():pe(this).hide()})}}),pe.Tween=P,P.prototype={constructor:P,init:function(e,t,n,i,a,o){this.elem=e,this.prop=n,this.easing=a||pe.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=o||(pe.cssNumber[n]?"":"px")},cur:function(){var e=P.propHooks[this.prop];return e&&e.get?e.get(this):P.propHooks._default.get(this)},run:function(e){var t,n=P.propHooks[this.prop];return this.options.duration?this.pos=t=pe.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):P.propHooks._default.set(this),this}},P.prototype.init.prototype=P.prototype,P.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=pe.css(e.elem,e.prop,""),t&&"auto"!==t?t:0)},set:function(e){pe.fx.step[e.prop]?pe.fx.step[e.prop](e):1!==e.elem.nodeType||null==e.elem.style[pe.cssProps[e.prop]]&&!pe.cssHooks[e.prop]?e.elem[e.prop]=e.now:pe.style(e.elem,e.prop,e.now+e.unit)}}},P.propHooks.scrollTop=P.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},pe.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},pe.fx=P.prototype.init,pe.fx.step={};var Et,Dt,St=/^(?:toggle|show|hide)$/,Lt=/queueHooks$/;pe.Animation=pe.extend(W,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return f(n.elem,e,qe.exec(t),n),n}]},tweener:function(e,t){pe.isFunction(e)?(t=e,e=["*"]):e=e.match(Ne);for(var n,i=0,a=e.length;i<a;i++)n=e[i],W.tweeners[n]=W.tweeners[n]||[],W.tweeners[n].unshift(t)},prefilters:[O],prefilter:function(e,t){t?W.prefilters.unshift(e):W.prefilters.push(e)}}),pe.speed=function(e,t,n){var i=e&&"object"==typeof e?pe.extend({},e):{complete:n||!n&&t||pe.isFunction(e)&&e,duration:e,easing:n&&t||t&&!pe.isFunction(t)&&t};return i.duration=pe.fx.off?0:"number"==typeof i.duration?i.duration:i.duration in pe.fx.speeds?pe.fx.speeds[i.duration]:pe.fx.speeds._default,null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){pe.isFunction(i.old)&&i.old.call(this),i.queue&&pe.dequeue(this,i.queue)},i},pe.fn.extend({fadeTo:function(e,t,n,i){return this.filter(_e).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(e,t,n,i){var a=pe.isEmptyObject(e),o=pe.speed(t,n,i),r=function(){var t=W(this,pe.extend({},e),o);(a||pe._data(this,"finish"))&&t.stop(!0)};return r.finish=r,a||!1===o.queue?this.each(r):this.queue(o.queue,r)},stop:function(e,t,n){var i=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&!1!==e&&this.queue(e||"fx",[]),this.each(function(){var t=!0,a=null!=e&&e+"queueHooks",o=pe.timers,r=pe._data(this);if(a)r[a]&&r[a].stop&&i(r[a]);else for(a in r)r[a]&&r[a].stop&&Lt.test(a)&&i(r[a]);for(a=o.length;a--;)o[a].elem!==this||null!=e&&o[a].queue!==e||(o[a].anim.stop(n),t=!1,o.splice(a,1));!t&&n||pe.dequeue(this,e)})},finish:function(e){return!1!==e&&(e=e||"fx"),this.each(function(){var t,n=pe._data(this),i=n[e+"queue"],a=n[e+"queueHooks"],o=pe.timers,r=i?i.length:0;for(n.finish=!0,pe.queue(this,e,[]),a&&a.stop&&a.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<r;t++)i[t]&&i[t].finish&&i[t].finish.call(this);delete n.finish})}}),pe.each(["toggle","show","hide"],function(e,t){var n=pe.fn[t];pe.fn[t]=function(e,i,a){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(B(t,!0),e,i,a)}}),pe.each({slideDown:B("show"),slideUp:B("hide"),slideToggle:B("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){pe.fn[e]=function(e,n,i){return this.animate(t,e,n,i)}}),pe.timers=[],pe.fx.tick=function(){var e,t=pe.timers,n=0;for(Et=pe.now();n<t.length;n++)e=t[n],e()||t[n]!==e||t.splice(n--,1);t.length||pe.fx.stop(),Et=void 0},pe.fx.timer=function(e){pe.timers.push(e),e()?pe.fx.start():pe.timers.pop()},pe.fx.interval=13,pe.fx.start=function(){Dt||(Dt=e.setInterval(pe.fx.tick,pe.fx.interval))},pe.fx.stop=function(){e.clearInterval(Dt),Dt=null},pe.fx.speeds={slow:600,fast:200,_default:400},pe.fn.delay=function(t,n){return t=pe.fx&&pe.fx.speeds[t]||t,n=n||"fx",this.queue(n,function(n,i){var a=e.setTimeout(n,t);i.stop=function(){e.clearTimeout(a)}})},function(){var e,t=ie.createElement("input"),n=ie.createElement("div"),i=ie.createElement("select"),a=i.appendChild(ie.createElement("option"));n=ie.createElement("div"),n.setAttribute("className","t"),n.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",e=n.getElementsByTagName("a")[0],t.setAttribute("type","checkbox"),n.appendChild(t),e=n.getElementsByTagName("a")[0],e.style.cssText="top:1px",de.getSetAttribute="t"!==n.className,de.style=/top/.test(e.getAttribute("style")),de.hrefNormalized="/a"===e.getAttribute("href"),de.checkOn=!!t.value,de.optSelected=a.selected,de.enctype=!!ie.createElement("form").enctype,i.disabled=!0,de.optDisabled=!a.disabled,t=ie.createElement("input"),t.setAttribute("value",""),de.input=""===t.getAttribute("value"),t.value="t",t.setAttribute("type","radio"),de.radioValue="t"===t.value}();var At=/\r/g,jt=/[\x20\t\r\n\f]+/g;pe.fn.extend({val:function(e){var t,n,i,a=this[0];return arguments.length?(i=pe.isFunction(e),this.each(function(n){var a;1===this.nodeType&&(a=i?e.call(this,n,pe(this).val()):e,null==a?a="":"number"==typeof a?a+="":pe.isArray(a)&&(a=pe.map(a,function(e){return null==e?"":e+""})),t=pe.valHooks[this.type]||pe.valHooks[this.nodeName.toLowerCase()],t&&"set"in t&&void 0!==t.set(this,a,"value")||(this.value=a))})):a?(t=pe.valHooks[a.type]||pe.valHooks[a.nodeName.toLowerCase()],t&&"get"in t&&void 0!==(n=t.get(a,"value"))?n:(n=a.value,"string"==typeof n?n.replace(At,""):null==n?"":n)):void 0}}),pe.extend({valHooks:{option:{get:function(e){var t=pe.find.attr(e,"value");return null!=t?t:pe.trim(pe.text(e)).replace(jt," ")}},select:{get:function(e){for(var t,n,i=e.options,a=e.selectedIndex,o="select-one"===e.type||a<0,r=o?null:[],l=o?a+1:i.length,s=a<0?l:o?a:0;s<l;s++)if(n=i[s],(n.selected||s===a)&&(de.optDisabled?!n.disabled:null===n.getAttribute("disabled"))&&(!n.parentNode.disabled||!pe.nodeName(n.parentNode,"optgroup"))){if(t=pe(n).val(),o)return t;r.push(t)}return r},set:function(e,t){for(var n,i,a=e.options,o=pe.makeArray(t),r=a.length;r--;)if(i=a[r],pe.inArray(pe.valHooks.option.get(i),o)>-1)try{i.selected=n=!0}catch(e){i.scrollHeight}else i.selected=!1;return n||(e.selectedIndex=-1),a}}}}),pe.each(["radio","checkbox"],function(){pe.valHooks[this]={set:function(e,t){if(pe.isArray(t))return e.checked=pe.inArray(pe(e).val(),t)>-1}},de.checkOn||(pe.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var Nt,Mt,Ht=pe.expr.attrHandle,Ft=/^(?:checked|selected)$/i,It=de.getSetAttribute,qt=de.input;pe.fn.extend({attr:function(e,t){return Be(this,pe.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){pe.removeAttr(this,e)})}}),pe.extend({attr:function(e,t,n){var i,a,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?pe.prop(e,t,n):(1===o&&pe.isXMLDoc(e)||(t=t.toLowerCase(),a=pe.attrHooks[t]||(pe.expr.match.bool.test(t)?Mt:Nt)),void 0!==n?null===n?void pe.removeAttr(e,t):a&&"set"in a&&void 0!==(i=a.set(e,n,t))?i:(e.setAttribute(t,n+""),n):a&&"get"in a&&null!==(i=a.get(e,t))?i:(i=pe.find.attr(e,t),null==i?void 0:i))},attrHooks:{type:{set:function(e,t){if(!de.radioValue&&"radio"===t&&pe.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,i,a=0,o=t&&t.match(Ne);if(o&&1===e.nodeType)for(;n=o[a++];)i=pe.propFix[n]||n,pe.expr.match.bool.test(n)?qt&&It||!Ft.test(n)?e[i]=!1:e[pe.camelCase("default-"+n)]=e[i]=!1:pe.attr(e,n,""),e.removeAttribute(It?n:i)}}),Mt={set:function(e,t,n){return!1===t?pe.removeAttr(e,n):qt&&It||!Ft.test(n)?e.setAttribute(!It&&pe.propFix[n]||n,n):e[pe.camelCase("default-"+n)]=e[n]=!0,n}},pe.each(pe.expr.match.bool.source.match(/\w+/g),function(e,t){var n=Ht[t]||pe.find.attr;qt&&It||!Ft.test(t)?Ht[t]=function(e,t,i){var a,o;return i||(o=Ht[t],Ht[t]=a,a=null!=n(e,t,i)?t.toLowerCase():null,Ht[t]=o),a}:Ht[t]=function(e,t,n){if(!n)return e[pe.camelCase("default-"+t)]?t.toLowerCase():null}}),qt&&It||(pe.attrHooks.value={set:function(e,t,n){return pe.nodeName(e,"input")?void(e.defaultValue=t):Nt&&Nt.set(e,t,n)}}),It||(Nt={set:function(e,t,n){var i=e.getAttributeNode(n);if(i||e.setAttributeNode(i=e.ownerDocument.createAttribute(n)),i.value=t+="","value"===n||t===e.getAttribute(n))return t}},Ht.id=Ht.name=Ht.coords=function(e,t,n){var i;if(!n)return(i=e.getAttributeNode(t))&&""!==i.value?i.value:null},pe.valHooks.button={get:function(e,t){var n=e.getAttributeNode(t);if(n&&n.specified)return n.value},set:Nt.set},pe.attrHooks.contenteditable={set:function(e,t,n){Nt.set(e,""!==t&&t,n)}},pe.each(["width","height"],function(e,t){pe.attrHooks[t]={set:function(e,n){if(""===n)return e.setAttribute(t,"auto"),n}}})),de.style||(pe.attrHooks.style={get:function(e){return e.style.cssText||void 0},set:function(e,t){return e.style.cssText=t+""}});var Pt=/^(?:input|select|textarea|button|object)$/i,_t=/^(?:a|area)$/i;pe.fn.extend({prop:function(e,t){return Be(this,pe.prop,e,t,arguments.length>1)},removeProp:function(e){return e=pe.propFix[e]||e,this.each(function(){try{this[e]=void 0,delete this[e]}catch(e){}})}}),pe.extend({prop:function(e,t,n){var i,a,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&pe.isXMLDoc(e)||(t=pe.propFix[t]||t,a=pe.propHooks[t]),void 0!==n?a&&"set"in a&&void 0!==(i=a.set(e,n,t))?i:e[t]=n:a&&"get"in a&&null!==(i=a.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=pe.find.attr(e,"tabindex");return t?parseInt(t,10):Pt.test(e.nodeName)||_t.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),de.hrefNormalized||pe.each(["href","src"],function(e,t){pe.propHooks[t]={get:function(e){return e.getAttribute(t,4)}}}),de.optSelected||(pe.propHooks.selected={get:function(e){var t=e.parentNode;return t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex),null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),pe.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){pe.propFix[this.toLowerCase()]=this}),de.enctype||(pe.propFix.enctype="encoding");var Bt=/[\t\r\n\f]/g;pe.fn.extend({addClass:function(e){var t,n,i,a,o,r,l,s=0;if(pe.isFunction(e))return this.each(function(t){pe(this).addClass(e.call(this,t,$(this)))});if("string"==typeof e&&e)for(t=e.match(Ne)||[];n=this[s++];)if(a=$(n),i=1===n.nodeType&&(" "+a+" ").replace(Bt," ")){for(r=0;o=t[r++];)i.indexOf(" "+o+" ")<0&&(i+=o+" ");l=pe.trim(i),a!==l&&pe.attr(n,"class",l)}return this},removeClass:function(e){var t,n,i,a,o,r,l,s=0;if(pe.isFunction(e))return this.each(function(t){pe(this).removeClass(e.call(this,t,$(this)))});if(!arguments.length)return this.attr("class","");if("string"==typeof e&&e)for(t=e.match(Ne)||[];n=this[s++];)if(a=$(n),i=1===n.nodeType&&(" "+a+" ").replace(Bt," ")){for(r=0;o=t[r++];)for(;i.indexOf(" "+o+" ")>-1;)i=i.replace(" "+o+" "," ");l=pe.trim(i),a!==l&&pe.attr(n,"class",l)}return this},toggleClass:function(e,t){var n=typeof e;return"boolean"==typeof t&&"string"===n?t?this.addClass(e):this.removeClass(e):pe.isFunction(e)?this.each(function(n){pe(this).toggleClass(e.call(this,n,$(this),t),t)}):this.each(function(){var t,i,a,o;if("string"===n)for(i=0,a=pe(this),o=e.match(Ne)||[];t=o[i++];)a.hasClass(t)?a.removeClass(t):a.addClass(t);else void 0!==e&&"boolean"!==n||(t=$(this),t&&pe._data(this,"__className__",t),pe.attr(this,"class",t||!1===e?"":pe._data(this,"__className__")||""))})},hasClass:function(e){var t,n,i=0;for(t=" "+e+" ";n=this[i++];)if(1===n.nodeType&&(" "+$(n)+" ").replace(Bt," ").indexOf(t)>-1)return!0;return!1}}),pe.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,t){pe.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}),pe.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}});var zt=e.location,Ot=pe.now(),Rt=/\?/,Wt=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;pe.parseJSON=function(t){if(e.JSON&&e.JSON.parse)return e.JSON.parse(t+"");var n,i=null,a=pe.trim(t+"");return a&&!pe.trim(a.replace(Wt,function(e,t,a,o){return n&&t&&(i=0),0===i?e:(n=a||t,i+=!o-!a,"")}))?Function("return "+a)():pe.error("Invalid JSON: "+t)},pe.parseXML=function(t){var n,i;if(!t||"string"!=typeof t)return null;try{e.DOMParser?(i=new e.DOMParser,n=i.parseFromString(t,"text/xml")):(n=new e.ActiveXObject("Microsoft.XMLDOM"),n.async="false",n.loadXML(t))}catch(e){n=void 0}return n&&n.documentElement&&!n.getElementsByTagName("parsererror").length||pe.error("Invalid XML: "+t),n};var $t=/#.*$/,Xt=/([?&])_=[^&]*/,Yt=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,Vt=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Kt=/^(?:GET|HEAD)$/,Gt=/^\/\//,Jt=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Ut={},Qt={},Zt="*/".concat("*"),en=zt.href,tn=Jt.exec(en.toLowerCase())||[];pe.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:en,type:"GET",isLocal:Vt.test(tn[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Zt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":pe.parseJSON,"text xml":pe.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?V(V(e,pe.ajaxSettings),t):V(pe.ajaxSettings,e)},ajaxPrefilter:X(Ut),ajaxTransport:X(Qt),ajax:function(t,n){function i(t,n,i,a){var o,d,g,b,w,C=n;2!==x&&(x=2,s&&e.clearTimeout(s),u=void 0,l=a||"",k.readyState=t>0?4:0,o=t>=200&&t<300||304===t,i&&(b=K(f,k,i)),b=G(f,b,k,o),o?(f.ifModified&&(w=k.getResponseHeader("Last-Modified"),w&&(pe.lastModified[r]=w),w=k.getResponseHeader("etag"),w&&(pe.etag[r]=w)),204===t||"HEAD"===f.type?C="nocontent":304===t?C="notmodified":(C=b.state,d=b.data,g=b.error,o=!g)):(g=C,!t&&C||(C="error",t<0&&(t=0))),k.status=t,k.statusText=(n||C)+"",o?y.resolveWith(p,[d,C,k]):y.rejectWith(p,[k,C,g]),k.statusCode(v),v=void 0,c&&h.trigger(o?"ajaxSuccess":"ajaxError",[k,f,o?d:g]),m.fireWith(p,[k,C]),c&&(h.trigger("ajaxComplete",[k,f]),--pe.active||pe.event.trigger("ajaxStop")))}"object"==typeof t&&(n=t,t=void 0),n=n||{};var a,o,r,l,s,c,u,d,f=pe.ajaxSetup({},n),p=f.context||f,h=f.context&&(p.nodeType||p.jquery)?pe(p):pe.event,y=pe.Deferred(),m=pe.Callbacks("once memory"),v=f.statusCode||{},g={},b={},x=0,w="canceled",k={readyState:0,getResponseHeader:function(e){var t;if(2===x){if(!d)for(d={};t=Yt.exec(l);)d[t[1].toLowerCase()]=t[2];t=d[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===x?l:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return x||(e=b[n]=b[n]||e,g[e]=t),this},overrideMimeType:function(e){return x||(f.mimeType=e),this},statusCode:function(e){var t;if(e)if(x<2)for(t in e)v[t]=[v[t],e[t]];else k.always(e[k.status]);return this},abort:function(e){var t=e||w;return u&&u.abort(t),i(0,t),this}};if(y.promise(k).complete=m.add,k.success=k.done,k.error=k.fail,f.url=((t||f.url||en)+"").replace($t,"").replace(Gt,tn[1]+"//"),f.type=n.method||n.type||f.method||f.type,f.dataTypes=pe.trim(f.dataType||"*").toLowerCase().match(Ne)||[""],null==f.crossDomain&&(a=Jt.exec(f.url.toLowerCase()),f.crossDomain=!(!a||a[1]===tn[1]&&a[2]===tn[2]&&(a[3]||("http:"===a[1]?"80":"443"))===(tn[3]||("http:"===tn[1]?"80":"443")))),f.data&&f.processData&&"string"!=typeof f.data&&(f.data=pe.param(f.data,f.traditional)),Y(Ut,f,n,k),2===x)return k;for(o in c=pe.event&&f.global,c&&0==pe.active++&&pe.event.trigger("ajaxStart"),f.type=f.type.toUpperCase(),f.hasContent=!Kt.test(f.type),r=f.url,f.hasContent||(f.data&&(r=f.url+=(Rt.test(r)?"&":"?")+f.data,delete f.data),!1===f.cache&&(f.url=Xt.test(r)?r.replace(Xt,"$1_="+Ot++):r+(Rt.test(r)?"&":"?")+"_="+Ot++)),f.ifModified&&(pe.lastModified[r]&&k.setRequestHeader("If-Modified-Since",pe.lastModified[r]),pe.etag[r]&&k.setRequestHeader("If-None-Match",pe.etag[r])),(f.data&&f.hasContent&&!1!==f.contentType||n.contentType)&&k.setRequestHeader("Content-Type",f.contentType),k.setRequestHeader("Accept",f.dataTypes[0]&&f.accepts[f.dataTypes[0]]?f.accepts[f.dataTypes[0]]+("*"!==f.dataTypes[0]?", "+Zt+"; q=0.01":""):f.accepts["*"]),f.headers)k.setRequestHeader(o,f.headers[o]);if(f.beforeSend&&(!1===f.beforeSend.call(p,k,f)||2===x))return k.abort();for(o in w="abort",{success:1,error:1,complete:1})k[o](f[o]);if(u=Y(Qt,f,n,k)){if(k.readyState=1,c&&h.trigger("ajaxSend",[k,f]),2===x)return k;f.async&&f.timeout>0&&(s=e.setTimeout(function(){k.abort("timeout")},f.timeout));try{x=1,u.send(g,i)}catch(e){if(!(x<2))throw e;i(-1,e)}}else i(-1,"No Transport");return k},getJSON:function(e,t,n){return pe.get(e,t,n,"json")},getScript:function(e,t){return pe.get(e,void 0,t,"script")}}),
pe.each(["get","post"],function(e,t){pe[t]=function(e,n,i,a){return pe.isFunction(n)&&(a=a||i,i=n,n=void 0),pe.ajax(pe.extend({url:e,type:t,dataType:a,data:n,success:i},pe.isPlainObject(e)&&e))}}),pe._evalUrl=function(e){return pe.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,throws:!0})},pe.fn.extend({wrapAll:function(e){if(pe.isFunction(e))return this.each(function(t){pe(this).wrapAll(e.call(this,t))});if(this[0]){var t=pe(e,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstChild&&1===e.firstChild.nodeType;)e=e.firstChild;return e}).append(this)}return this},wrapInner:function(e){return pe.isFunction(e)?this.each(function(t){pe(this).wrapInner(e.call(this,t))}):this.each(function(){var t=pe(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=pe.isFunction(e);return this.each(function(n){pe(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(){return this.parent().each(function(){pe.nodeName(this,"body")||pe(this).replaceWith(this.childNodes)}).end()}}),pe.expr.filters.hidden=function(e){return de.reliableHiddenOffsets()?e.offsetWidth<=0&&e.offsetHeight<=0&&!e.getClientRects().length:U(e)},pe.expr.filters.visible=function(e){return!pe.expr.filters.hidden(e)};var nn=/%20/g,an=/\[\]$/,on=/\r?\n/g,rn=/^(?:submit|button|image|reset|file)$/i,ln=/^(?:input|select|textarea|keygen)/i;pe.param=function(e,t){var n,i=[],a=function(e,t){t=pe.isFunction(t)?t():null==t?"":t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(void 0===t&&(t=pe.ajaxSettings&&pe.ajaxSettings.traditional),pe.isArray(e)||e.jquery&&!pe.isPlainObject(e))pe.each(e,function(){a(this.name,this.value)});else for(n in e)Q(n,e[n],t,a);return i.join("&").replace(nn,"+")},pe.fn.extend({serialize:function(){return pe.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=pe.prop(this,"elements");return e?pe.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!pe(this).is(":disabled")&&ln.test(this.nodeName)&&!rn.test(e)&&(this.checked||!ze.test(e))}).map(function(e,t){var n=pe(this).val();return null==n?null:pe.isArray(n)?pe.map(n,function(e){return{name:t.name,value:e.replace(on,"\r\n")}}):{name:t.name,value:n.replace(on,"\r\n")}}).get()}}),pe.ajaxSettings.xhr=void 0!==e.ActiveXObject?function(){return this.isLocal?ee():ie.documentMode>8?Z():/^(get|post|head|put|delete|options)$/i.test(this.type)&&Z()||ee()}:Z;var sn=0,cn={},un=pe.ajaxSettings.xhr();e.attachEvent&&e.attachEvent("onunload",function(){for(var e in cn)cn[e](void 0,!0)}),de.cors=!!un&&"withCredentials"in un,un=de.ajax=!!un,un&&pe.ajaxTransport(function(t){var n;if(!t.crossDomain||de.cors)return{send:function(i,a){var o,r=t.xhr(),l=++sn;if(r.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(o in t.xhrFields)r[o]=t.xhrFields[o];for(o in t.mimeType&&r.overrideMimeType&&r.overrideMimeType(t.mimeType),t.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest"),i)void 0!==i[o]&&r.setRequestHeader(o,i[o]+"");r.send(t.hasContent&&t.data||null),n=function(e,i){var o,s,c;if(n&&(i||4===r.readyState))if(delete cn[l],n=void 0,r.onreadystatechange=pe.noop,i)4!==r.readyState&&r.abort();else{c={},o=r.status,"string"==typeof r.responseText&&(c.text=r.responseText);try{s=r.statusText}catch(e){s=""}o||!t.isLocal||t.crossDomain?1223===o&&(o=204):o=c.text?200:404}c&&a(o,s,c,r.getAllResponseHeaders())},t.async?4===r.readyState?e.setTimeout(n):r.onreadystatechange=cn[l]=n:n()},abort:function(){n&&n(void 0,!0)}}}),pe.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return pe.globalEval(e),e}}}),pe.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET",e.global=!1)}),pe.ajaxTransport("script",function(e){if(e.crossDomain){var t,n=ie.head||pe("head")[0]||ie.documentElement;return{send:function(i,a){t=ie.createElement("script"),t.async=!0,e.scriptCharset&&(t.charset=e.scriptCharset),t.src=e.url,t.onload=t.onreadystatechange=function(e,n){(n||!t.readyState||/loaded|complete/.test(t.readyState))&&(t.onload=t.onreadystatechange=null,t.parentNode&&t.parentNode.removeChild(t),t=null,n||a(200,"success"))},n.insertBefore(t,n.firstChild)},abort:function(){t&&t.onload(void 0,!0)}}}});var dn=[],fn=/(=)\?(?=&|$)|\?\?/;pe.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=dn.pop()||pe.expando+"_"+Ot++;return this[e]=!0,e}}),pe.ajaxPrefilter("json jsonp",function(t,n,i){var a,o,r,l=!1!==t.jsonp&&(fn.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&fn.test(t.data)&&"data");if(l||"jsonp"===t.dataTypes[0])return a=t.jsonpCallback=pe.isFunction(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,l?t[l]=t[l].replace(fn,"$1"+a):!1!==t.jsonp&&(t.url+=(Rt.test(t.url)?"&":"?")+t.jsonp+"="+a),t.converters["script json"]=function(){return r||pe.error(a+" was not called"),r[0]},t.dataTypes[0]="json",o=e[a],e[a]=function(){r=arguments},i.always(function(){void 0===o?pe(e).removeProp(a):e[a]=o,t[a]&&(t.jsonpCallback=n.jsonpCallback,dn.push(a)),r&&pe.isFunction(o)&&o(r[0]),r=o=void 0}),"script"}),pe.parseHTML=function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||ie;var i=ke.exec(e),a=!n&&[];return i?[t.createElement(i[1])]:(i=v([e],t,a),a&&a.length&&pe(a).remove(),pe.merge([],i.childNodes))};var pn=pe.fn.load;return pe.fn.load=function(e,t,n){if("string"!=typeof e&&pn)return pn.apply(this,arguments);var i,a,o,r=this,l=e.indexOf(" ");return l>-1&&(i=pe.trim(e.slice(l,e.length)),e=e.slice(0,l)),pe.isFunction(t)?(n=t,t=void 0):t&&"object"==typeof t&&(a="POST"),r.length>0&&pe.ajax({url:e,type:a||"GET",dataType:"html",data:t}).done(function(e){o=arguments,r.html(i?pe("<div>").append(pe.parseHTML(e)).find(i):e)}).always(n&&function(e,t){r.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},pe.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){pe.fn[t]=function(e){return this.on(t,e)}}),pe.expr.filters.animated=function(e){return pe.grep(pe.timers,function(t){return e===t.elem}).length},pe.offset={setOffset:function(e,t,n){var i,a,o,r,l,s,c,u=pe.css(e,"position"),d=pe(e),f={};"static"===u&&(e.style.position="relative"),l=d.offset(),o=pe.css(e,"top"),s=pe.css(e,"left"),c=("absolute"===u||"fixed"===u)&&pe.inArray("auto",[o,s])>-1,c?(i=d.position(),r=i.top,a=i.left):(r=parseFloat(o)||0,a=parseFloat(s)||0),pe.isFunction(t)&&(t=t.call(e,n,pe.extend({},l))),null!=t.top&&(f.top=t.top-l.top+r),null!=t.left&&(f.left=t.left-l.left+a),"using"in t?t.using.call(e,f):d.css(f)}},pe.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){pe.offset.setOffset(this,e,t)});var t,n,i={top:0,left:0},a=this[0],o=a&&a.ownerDocument;return o?(t=o.documentElement,pe.contains(t,a)?(void 0!==a.getBoundingClientRect&&(i=a.getBoundingClientRect()),n=te(o),{top:i.top+(n.pageYOffset||t.scrollTop)-(t.clientTop||0),left:i.left+(n.pageXOffset||t.scrollLeft)-(t.clientLeft||0)}):i):void 0},position:function(){if(this[0]){var e,t,n={top:0,left:0},i=this[0];return"fixed"===pe.css(i,"position")?t=i.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),pe.nodeName(e[0],"html")||(n=e.offset()),n.top+=pe.css(e[0],"borderTopWidth",!0),n.left+=pe.css(e[0],"borderLeftWidth",!0)),{top:t.top-n.top-pe.css(i,"marginTop",!0),left:t.left-n.left-pe.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&!pe.nodeName(e,"html")&&"static"===pe.css(e,"position");)e=e.offsetParent;return e||pt})}}),pe.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n=/Y/.test(t);pe.fn[e]=function(i){return Be(this,function(e,i,a){var o=te(e);return void 0===a?o?t in o?o[t]:o.document.documentElement[i]:e[i]:void(o?o.scrollTo(n?pe(o).scrollLeft():a,n?a:pe(o).scrollTop()):e[i]=a)},e,i,arguments.length,null)}}),pe.each(["top","left"],function(e,t){pe.cssHooks[t]=N(de.pixelPosition,function(e,n){if(n)return n=yt(e,t),dt.test(n)?pe(e).position()[t]+"px":n})}),pe.each({Height:"height",Width:"width"},function(e,t){pe.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,i){pe.fn[i]=function(i,a){var o=arguments.length&&(n||"boolean"!=typeof i),r=n||(!0===i||!0===a?"margin":"border");return Be(this,function(t,n,i){var a;return pe.isWindow(t)?t.document.documentElement["client"+e]:9===t.nodeType?(a=t.documentElement,Math.max(t.body["scroll"+e],a["scroll"+e],t.body["offset"+e],a["offset"+e],a["client"+e])):void 0===i?pe.css(t,n,r):pe.style(t,n,i,r)},t,o?i:void 0,o,null)}})}),pe.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}}),pe.fn.size=function(){return this.length},pe.fn.andSelf=pe.fn.addBack,layui.define(function(e){layui.$=pe,e("jquery",pe)}),pe}),function(e,t){"use strict";var n,i,a=e.layui&&layui.define,o={getPath:function(){var e=document.currentScript?document.currentScript.src:function(){for(var e,t=document.scripts,n=t.length-1,i=n;i>0;i--)if("interactive"===t[i].readyState){e=t[i].src;break}return e||t[n].src}();return e.substring(0,e.lastIndexOf("/")+1)}(),config:{},end:{},minIndex:0,minLeft:[],btn:["&#x786E;&#x5B9A;","&#x53D6;&#x6D88;"],type:["dialog","page","iframe","loading","tips"],getStyle:function(t,n){var i=t.currentStyle?t.currentStyle:e.getComputedStyle(t,null);return i[i.getPropertyValue?"getPropertyValue":"getAttribute"](n)},link:function(t,n,i){if(r.path){var a=document.getElementsByTagName("head")[0],l=document.createElement("link");"string"==typeof n&&(i=n);var s=(i||t).replace(/\.|\//g,""),c="layuicss-"+s,u=0;l.rel="stylesheet",l.href=r.path+t,l.id=c,document.getElementById(c)||a.appendChild(l),"function"==typeof n&&function t(){return++u>80?e.console&&console.error("layer.css: Invalid"):void(1989===parseInt(o.getStyle(document.getElementById(c),"width"))?n():setTimeout(t,100))}()}}},r={v:"3.1.1",ie:function(){var t=navigator.userAgent.toLowerCase();return!!(e.ActiveXObject||"ActiveXObject"in e)&&((t.match(/msie\s(\d+)/)||[])[1]||"11")}(),index:e.layer&&e.layer.v?1e5:0,path:o.getPath,config:function(e,t){return e=e||{},r.cache=o.config=n.extend({},o.config,e),r.path=o.config.path||r.path,"string"==typeof e.extend&&(e.extend=[e.extend]),o.config.path&&r.ready(),e.extend?(a?layui.addcss("modules/layer/"+e.extend):o.link("theme/"+e.extend),this):this},ready:function(e){var t="layer",n="",i=(a?"modules/layer/":"theme/")+"default/layer.css?v="+r.v+n;return a?layui.addcss(i,e,t):o.link(i,e,t),this},alert:function(e,t,i){var a="function"==typeof t;return a&&(i=t),r.open(n.extend({content:e,yes:i},a?{}:t))},confirm:function(e,t,i,a){var l="function"==typeof t;return l&&(a=i,i=t),r.open(n.extend({content:e,btn:o.btn,yes:i,btn2:a},l?{}:t))},msg:function(e,i,a){var l="function"==typeof i,c=o.config.skin,u=(c?c+" "+c+"-msg":"")||"layui-layer-msg",d=s.anim.length-1;return l&&(a=i),r.open(n.extend({content:e,time:3e3,shade:!1,skin:u,title:!1,closeBtn:!1,btn:!1,resize:!1,end:a},l&&!o.config.skin?{skin:u+" layui-layer-hui",anim:d}:(i=i||{},(-1===i.icon||i.icon===t&&!o.config.skin)&&(i.skin=u+" "+(i.skin||"layui-layer-hui")),i)))},load:function(e,t){return r.open(n.extend({type:3,icon:e||0,resize:!1,shade:.01},t))},tips:function(e,t,i){return r.open(n.extend({type:4,content:[e,t],closeBtn:!1,time:3e3,shade:!1,resize:!1,fixed:!1,maxWidth:210},i))}},l=function(e){var t=this;t.index=++r.index,t.config=n.extend({},t.config,o.config,e),document.body?t.creat():setTimeout(function(){t.creat()},30)};l.pt=l.prototype;var s=["layui-layer",".layui-layer-title",".layui-layer-main",".layui-layer-dialog","layui-layer-iframe","layui-layer-content","layui-layer-btn","layui-layer-close"];s.anim=["layer-anim-00","layer-anim-01","layer-anim-02","layer-anim-03","layer-anim-04","layer-anim-05","layer-anim-06"],l.pt.config={type:0,shade:.3,fixed:!0,move:s[1],title:"&#x4FE1;&#x606F;",offset:"auto",area:"auto",closeBtn:1,time:0,zIndex:19891014,maxWidth:360,anim:0,isOutAnim:!0,icon:-1,moveType:1,resize:!0,scrollbar:!0,tips:2},l.pt.vessel=function(e,t){var i=this,a=i.index,r=i.config,l=r.zIndex+a,c="object"==typeof r.title,u=r.maxmin&&(1===r.type||2===r.type),d=r.title?'<div class="layui-layer-title" style="'+(c?r.title[1]:"")+'">'+(c?r.title[0]:r.title)+"</div>":"";return r.zIndex=l,t([r.shade?'<div class="layui-layer-shade" id="layui-layer-shade'+a+'" times="'+a+'" style="z-index:'+(l-1)+'; "></div>':"",'<div class="'+s[0]+" layui-layer-"+o.type[r.type]+(0!=r.type&&2!=r.type||r.shade?"":" layui-layer-border")+" "+(r.skin||"")+'" id="'+s[0]+a+'" type="'+o.type[r.type]+'" times="'+a+'" showtime="'+r.time+'" conType="'+(e?"object":"string")+'" style="z-index: '+l+"; width:"+r.area[0]+";height:"+r.area[1]+(r.fixed?"":";position:absolute;")+'">'+(e&&2!=r.type?"":d)+'<div id="'+(r.id||"")+'" class="layui-layer-content'+(0==r.type&&-1!==r.icon?" layui-layer-padding":"")+(3==r.type?" layui-layer-loading"+r.icon:"")+'">'+(0==r.type&&-1!==r.icon?'<i class="layui-layer-ico layui-layer-ico'+r.icon+'"></i>':"")+(1==r.type&&e?"":r.content||"")+'</div><span class="layui-layer-setwin">'+function(){var e=u?'<a class="layui-layer-min" href="javascript:;"><cite></cite></a><a class="layui-layer-ico layui-layer-max" href="javascript:;"></a>':"";return r.closeBtn&&(e+='<a class="layui-layer-ico '+s[7]+" "+s[7]+(r.title?r.closeBtn:4==r.type?"1":"2")+'" href="javascript:;"></a>'),e}()+"</span>"+(r.btn?function(){var e="";"string"==typeof r.btn&&(r.btn=[r.btn]);for(var t=0,n=r.btn.length;t<n;t++)e+='<a class="'+s[6]+t+'">'+r.btn[t]+"</a>";return'<div class="'+s[6]+" layui-layer-btn-"+(r.btnAlign||"")+'">'+e+"</div>"}():"")+(r.resize?'<span class="layui-layer-resize"></span>':"")+"</div>"],d,n('<div class="layui-layer-move"></div>')),i},l.pt.creat=function(){var e=this,t=e.config,a=e.index,l=t.content,c="object"==typeof l,u=n("body");if(!t.id||!n("#"+t.id)[0]){switch("string"==typeof t.area&&(t.area="auto"===t.area?["",""]:[t.area,""]),t.shift&&(t.anim=t.shift),6==r.ie&&(t.fixed=!1),t.type){case 0:t.btn="btn"in t?t.btn:o.btn[0],r.closeAll("dialog");break;case 2:l=t.content=c?t.content:[t.content||"","auto"];t.content='<iframe scrolling="'+(t.content[1]||"auto")+'" allowtransparency="true" id="'+s[4]+a+'" name="'+s[4]+a+'" onload="this.className=\'\';" class="layui-layer-load" frameborder="0" src="'+t.content[0]+'"></iframe>';break;case 3:delete t.title,delete t.closeBtn,-1===t.icon&&t.icon,r.closeAll("loading");break;case 4:c||(t.content=[t.content,"body"]),t.follow=t.content[1],t.content=t.content[0]+'<i class="layui-layer-TipsG"></i>',delete t.title,t.tips="object"==typeof t.tips?t.tips:[t.tips,!0],t.tipsMore||r.closeAll("tips")}if(e.vessel(c,function(i,r,d){u.append(i[0]),c?2==t.type||4==t.type?n("body").append(i[1]):l.parents("."+s[0])[0]||(l.data("display",l.css("display")).show().addClass("layui-layer-wrap").wrap(i[1]),n("#"+s[0]+a).find("."+s[5]).before(r)):u.append(i[1]),n(".layui-layer-move")[0]||u.append(o.moveElem=d),e.layero=n("#"+s[0]+a),t.scrollbar||s.html.css("overflow","hidden").attr("layer-full",a)}).auto(a),n("#layui-layer-shade"+e.index).css({"background-color":t.shade[1]||"#000",opacity:t.shade[0]||t.shade}),2==t.type&&6==r.ie&&e.layero.find("iframe").attr("src",l[0]),4==t.type?e.tips():e.offset(),t.fixed&&i.on("resize",function(){e.offset(),(/^\d+%$/.test(t.area[0])||/^\d+%$/.test(t.area[1]))&&e.auto(a),4==t.type&&e.tips()}),t.time<=0||setTimeout(function(){r.close(e.index)},t.time),e.move().callback(),s.anim[t.anim]){var d="layer-anim "+s.anim[t.anim];e.layero.addClass(d).one("webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend",function(){n(this).removeClass(d)})}t.isOutAnim&&e.layero.data("isOutAnim",!0)}},l.pt.auto=function(e){var t=this,a=t.config,o=n("#"+s[0]+e);""===a.area[0]&&a.maxWidth>0&&(r.ie&&r.ie<8&&a.btn&&o.width(o.innerWidth()),o.outerWidth()>a.maxWidth&&o.width(a.maxWidth));var l=[o.innerWidth(),o.innerHeight()],c=o.find(s[1]).outerHeight()||0,u=o.find("."+s[6]).outerHeight()||0,d=function(e){e=o.find(e),e.height(l[1]-c-u-2*(0|parseFloat(e.css("padding-top"))))};switch(a.type){case 2:d("iframe");break;default:""===a.area[1]?a.maxHeight>0&&o.outerHeight()>a.maxHeight?(l[1]=a.maxHeight,d("."+s[5])):a.fixed&&l[1]>=i.height()&&(l[1]=i.height(),d("."+s[5])):d("."+s[5])}return t},l.pt.offset=function(){var e=this,t=e.config,n=e.layero,a=[n.outerWidth(),n.outerHeight()],o="object"==typeof t.offset;e.offsetTop=(i.height()-a[1])/2,e.offsetLeft=(i.width()-a[0])/2,o?(e.offsetTop=t.offset[0],e.offsetLeft=t.offset[1]||e.offsetLeft):"auto"!==t.offset&&("t"===t.offset?e.offsetTop=0:"r"===t.offset?e.offsetLeft=i.width()-a[0]:"b"===t.offset?e.offsetTop=i.height()-a[1]:"l"===t.offset?e.offsetLeft=0:"lt"===t.offset?(e.offsetTop=0,e.offsetLeft=0):"lb"===t.offset?(e.offsetTop=i.height()-a[1],e.offsetLeft=0):"rt"===t.offset?(e.offsetTop=0,e.offsetLeft=i.width()-a[0]):"rb"===t.offset?(e.offsetTop=i.height()-a[1],e.offsetLeft=i.width()-a[0]):e.offsetTop=t.offset),t.fixed||(e.offsetTop=/%$/.test(e.offsetTop)?i.height()*parseFloat(e.offsetTop)/100:parseFloat(e.offsetTop),e.offsetLeft=/%$/.test(e.offsetLeft)?i.width()*parseFloat(e.offsetLeft)/100:parseFloat(e.offsetLeft),e.offsetTop+=i.scrollTop(),e.offsetLeft+=i.scrollLeft()),n.attr("minLeft")&&(e.offsetTop=i.height()-(n.find(s[1]).outerHeight()||0),e.offsetLeft=n.css("left")),n.css({top:e.offsetTop,left:e.offsetLeft})},l.pt.tips=function(){var e=this,t=e.config,a=e.layero,o=[a.outerWidth(),a.outerHeight()],r=n(t.follow);r[0]||(r=n("body"));var l={width:r.outerWidth(),height:r.outerHeight(),top:r.offset().top,left:r.offset().left},c=a.find(".layui-layer-TipsG"),u=t.tips[0];t.tips[1]||c.remove(),l.autoLeft=function(){l.left+o[0]-i.width()>0?(l.tipLeft=l.left+l.width-o[0],c.css({right:12,left:"auto"})):l.tipLeft=l.left},l.where=[function(){l.autoLeft(),l.tipTop=l.top-o[1]-10,c.removeClass("layui-layer-TipsB").addClass("layui-layer-TipsT").css("border-right-color",t.tips[1])},function(){l.tipLeft=l.left+l.width+10,l.tipTop=l.top,c.removeClass("layui-layer-TipsL").addClass("layui-layer-TipsR").css("border-bottom-color",t.tips[1])},function(){l.autoLeft(),l.tipTop=l.top+l.height+10,c.removeClass("layui-layer-TipsT").addClass("layui-layer-TipsB").css("border-right-color",t.tips[1])},function(){l.tipLeft=l.left-o[0]-10,l.tipTop=l.top,c.removeClass("layui-layer-TipsR").addClass("layui-layer-TipsL").css("border-bottom-color",t.tips[1])}],l.where[u-1](),1===u?l.top-(i.scrollTop()+o[1]+16)<0&&l.where[2]():2===u?i.width()-(l.left+l.width+o[0]+16)>0||l.where[3]():3===u?l.top-i.scrollTop()+l.height+o[1]+16-i.height()>0&&l.where[0]():4===u&&o[0]+16-l.left>0&&l.where[1](),a.find("."+s[5]).css({"background-color":t.tips[1],"padding-right":t.closeBtn?"30px":""}),a.css({left:l.tipLeft-(t.fixed?i.scrollLeft():0),top:l.tipTop-(t.fixed?i.scrollTop():0)})},l.pt.move=function(){var e=this,t=e.config,a=n(document),l=e.layero,s=l.find(t.move),c=l.find(".layui-layer-resize"),u={};return t.move&&s.css("cursor","move"),s.on("mousedown",function(e){e.preventDefault(),t.move&&(u.moveStart=!0,u.offset=[e.clientX-parseFloat(l.css("left")),e.clientY-parseFloat(l.css("top"))],o.moveElem.css("cursor","move").show())}),c.on("mousedown",function(e){e.preventDefault(),u.resizeStart=!0,u.offset=[e.clientX,e.clientY],u.area=[l.outerWidth(),l.outerHeight()],o.moveElem.css("cursor","se-resize").show()}),a.on("mousemove",function(n){if(u.moveStart){var a=n.clientX-u.offset[0],o=n.clientY-u.offset[1],s="fixed"===l.css("position");if(n.preventDefault(),u.stX=s?0:i.scrollLeft(),u.stY=s?0:i.scrollTop(),!t.moveOut){var c=i.width()-l.outerWidth()+u.stX,d=i.height()-l.outerHeight()+u.stY;a<u.stX&&(a=u.stX),a>c&&(a=c),o<u.stY&&(o=u.stY),o>d&&(o=d)}l.css({left:a,top:o})}if(t.resize&&u.resizeStart){a=n.clientX-u.offset[0],o=n.clientY-u.offset[1];n.preventDefault(),r.style(e.index,{width:u.area[0]+a,height:u.area[1]+o}),u.isResize=!0,t.resizing&&t.resizing(l)}}).on("mouseup",function(e){u.moveStart&&(delete u.moveStart,o.moveElem.hide(),t.moveEnd&&t.moveEnd(l)),u.resizeStart&&(delete u.resizeStart,o.moveElem.hide())}),e},l.pt.callback=function(){function e(){var e=a.cancel&&a.cancel(t.index,i);!1===e||r.close(t.index)}var t=this,i=t.layero,a=t.config;t.openLayer(),a.success&&(2==a.type?i.find("iframe").on("load",function(){a.success(i,t.index)}):a.success(i,t.index)),6==r.ie&&t.IE6(i),i.find("."+s[6]).children("a").on("click",function(){var e=n(this).index();if(0===e)a.yes?a.yes(t.index,i):a.btn1?a.btn1(t.index,i):r.close(t.index);else{var o=a["btn"+(e+1)]&&a["btn"+(e+1)](t.index,i);!1===o||r.close(t.index)}}),i.find("."+s[7]).on("click",e),a.shadeClose&&n("#layui-layer-shade"+t.index).on("click",function(){r.close(t.index)}),i.find(".layui-layer-min").on("click",function(){var e=a.min&&a.min(i);!1===e||r.min(t.index,a)}),i.find(".layui-layer-max").on("click",function(){n(this).hasClass("layui-layer-maxmin")?(r.restore(t.index),a.restore&&a.restore(i)):(r.full(t.index,a),setTimeout(function(){a.full&&a.full(i)},100))}),a.end&&(o.end[t.index]=a.end)},o.reselect=function(){n.each(n("select"),function(e,t){var i=n(this);i.parents("."+s[0])[0]||1==i.attr("layer")&&n("."+s[0]).length<1&&i.removeAttr("layer").show(),i=null})},l.pt.IE6=function(e){n("select").each(function(e,t){var i=n(this);i.parents("."+s[0])[0]||"none"===i.css("display")||i.attr({layer:"1"}).hide(),i=null})},l.pt.openLayer=function(){var e=this;r.zIndex=e.config.zIndex,r.setTop=function(e){var t=function(){r.zIndex++,e.css("z-index",r.zIndex+1)};return r.zIndex=parseInt(e[0].style.zIndex),e.on("mousedown",t),r.zIndex}},o.record=function(e){var t=[e.width(),e.height(),e.position().top,e.position().left+parseFloat(e.css("margin-left"))];e.find(".layui-layer-max").addClass("layui-layer-maxmin"),e.attr({area:t})},o.rescollbar=function(e){s.html.attr("layer-full")==e&&(s.html[0].style.removeProperty?s.html[0].style.removeProperty("overflow"):s.html[0].style.removeAttribute("overflow"),s.html.removeAttr("layer-full"))},e.layer=r,r.getChildFrame=function(e,t){return t=t||n("."+s[4]).attr("times"),n("#"+s[0]+t).find("iframe").contents().find(e)},r.getFrameIndex=function(e){return n("#"+e).parents("."+s[4]).attr("times")},r.iframeAuto=function(e){if(e){var t=r.getChildFrame("html",e).outerHeight(),i=n("#"+s[0]+e),a=i.find(s[1]).outerHeight()||0,o=i.find("."+s[6]).outerHeight()||0;i.css({height:t+a+o}),i.find("iframe").css({height:t})}},r.iframeSrc=function(e,t){n("#"+s[0]+e).find("iframe").attr("src",t)},r.style=function(e,t,i){var a=n("#"+s[0]+e),r=a.find(".layui-layer-content"),l=a.attr("type"),c=a.find(s[1]).outerHeight()||0,u=a.find("."+s[6]).outerHeight()||0;a.attr("minLeft"),l!==o.type[3]&&l!==o.type[4]&&(i||(parseFloat(t.width)<=260&&(t.width=260),parseFloat(t.height)-c-u<=64&&(t.height=64+c+u)),a.css(t),u=a.find("."+s[6]).outerHeight(),l===o.type[2]?a.find("iframe").css({height:parseFloat(t.height)-c-u}):r.css({height:parseFloat(t.height)-c-u-parseFloat(r.css("padding-top"))-parseFloat(r.css("padding-bottom"))}))},r.min=function(e,t){var a=n("#"+s[0]+e),l=a.find(s[1]).outerHeight()||0,c=a.attr("minLeft")||181*o.minIndex+"px",u=a.css("position");o.record(a),o.minLeft[0]&&(c=o.minLeft[0],o.minLeft.shift()),a.attr("position",u),r.style(e,{width:180,height:l,left:c,top:i.height()-l,position:"fixed",overflow:"hidden"},!0),a.find(".layui-layer-min").hide(),"page"===a.attr("type")&&a.find(s[4]).hide(),o.rescollbar(e),a.attr("minLeft")||o.minIndex++,a.attr("minLeft",c)},r.restore=function(e){var t=n("#"+s[0]+e),i=t.attr("area").split(",");t.attr("type"),r.style(e,{width:parseFloat(i[0]),height:parseFloat(i[1]),top:parseFloat(i[2]),left:parseFloat(i[3]),position:t.attr("position"),overflow:"visible"},!0),t.find(".layui-layer-max").removeClass("layui-layer-maxmin"),t.find(".layui-layer-min").show(),"page"===t.attr("type")&&t.find(s[4]).show(),o.rescollbar(e)},r.full=function(e){var t,a=n("#"+s[0]+e);o.record(a),s.html.attr("layer-full")||s.html.css("overflow","hidden").attr("layer-full",e),clearTimeout(t),t=setTimeout(function(){var t="fixed"===a.css("position");r.style(e,{top:t?0:i.scrollTop(),left:t?0:i.scrollLeft(),width:i.width(),height:i.height()},!0),a.find(".layui-layer-min").hide()},100)},r.title=function(e,t){var i=n("#"+s[0]+(t||r.index)).find(s[1]);i.html(e)},r.close=function(e){var t=n("#"+s[0]+e),i=t.attr("type"),a="layer-anim-close";if(t[0]){var l="layui-layer-wrap",c=function(){if(i===o.type[1]&&"object"===t.attr("conType")){t.children(":not(."+s[5]+")").remove();for(var a=t.find("."+l),r=0;r<2;r++)a.unwrap();a.css("display",a.data("display")).removeClass(l)}else{if(i===o.type[2])try{var c=n("#"+s[4]+e)[0];c.contentWindow.document.write(""),c.contentWindow.close(),t.find("."+s[5])[0].removeChild(c)}catch(e){}t[0].innerHTML="",t.remove()}"function"==typeof o.end[e]&&o.end[e](),delete o.end[e]};t.data("isOutAnim")&&t.addClass("layer-anim "+a),n("#layui-layer-moves, #layui-layer-shade"+e).remove(),6==r.ie&&o.reselect(),o.rescollbar(e),t.attr("minLeft")&&(o.minIndex--,o.minLeft.push(t.attr("minLeft"))),r.ie&&r.ie<10||!t.data("isOutAnim")?c():setTimeout(function(){c()},200)}},r.closeAll=function(e){n.each(n("."+s[0]),function(){var t=n(this),i=e?t.attr("type")===e:1;i&&r.close(t.attr("times")),i=null})};var c=r.cache||{},u=function(e){return c.skin?" "+c.skin+" "+c.skin+"-"+e:""};r.prompt=function(e,t){var a="";if(e=e||{},"function"==typeof e&&(t=e),e.area){var o=e.area;a='style="width: '+o[0]+"; height: "+o[1]+';"',delete e.area}var l,s=2==e.formType?'<textarea class="layui-layer-input"'+a+"></textarea>":'<input type="'+(1==e.formType?"password":"text")+'" class="layui-layer-input">',c=e.success;return delete e.success,r.open(n.extend({type:1,btn:["&#x786E;&#x5B9A;","&#x53D6;&#x6D88;"],content:s,skin:"layui-layer-prompt"+u("prompt"),maxWidth:i.width(),success:function(t){l=t.find(".layui-layer-input"),l.val(e.value||"").focus(),"function"==typeof c&&c(t)},resize:!1,yes:function(n){var i=l.val();""===i?l.focus():i.length>(e.maxlength||500)?r.tips("&#x6700;&#x591A;&#x8F93;&#x5165;"+(e.maxlength||500)+"&#x4E2A;&#x5B57;&#x6570;",l,{tips:1}):t&&t(i,n,l)}},e))},r.tab=function(e){e=e||{};var t=e.tab||{},i="layui-this",a=e.success;return delete e.success,r.open(n.extend({type:1,skin:"layui-layer-tab"+u("tab"),resize:!1,title:function(){var e=t.length,n=1,a="";if(e>0)for(a='<span class="'+i+'">'+t[0].title+"</span>";n<e;n++)a+="<span>"+t[n].title+"</span>";return a}(),content:'<ul class="layui-layer-tabmain">'+function(){var e=t.length,n=1,a="";if(e>0)for(a='<li class="layui-layer-tabli '+i+'">'+(t[0].content||"no content")+"</li>";n<e;n++)a+='<li class="layui-layer-tabli">'+(t[n].content||"no  content")+"</li>";return a}()+"</ul>",success:function(t){var o=t.find(".layui-layer-title").children(),r=t.find(".layui-layer-tabmain").children();o.on("mousedown",function(t){t.stopPropagation?t.stopPropagation():t.cancelBubble=!0;var a=n(this),o=a.index();a.addClass(i).siblings().removeClass(i),r.eq(o).show().siblings().hide(),"function"==typeof e.change&&e.change(o)}),"function"==typeof a&&a(t)}},e))},r.photos=function(t,i,a){function o(e,t,n){var i=new Image;return i.src=e,i.complete?t(i):(i.onload=function(){i.onload=null,t(i)},void(i.onerror=function(e){i.onerror=null,n(e)}))}var l={};if(t=t||{},t.photos){var s=t.photos.constructor===Object,c=s?t.photos:{},d=c.data||[],f=c.start||0;l.imgIndex=1+(0|f),t.img=t.img||"img";var p=t.success;if(delete t.success,s){if(0===d.length)return r.msg("&#x6CA1;&#x6709;&#x56FE;&#x7247;")}else{var h=n(t.photos),y=function(){d=[],h.find(t.img).each(function(e){var t=n(this);t.attr("layer-index",e),d.push({alt:t.attr("alt"),pid:t.attr("layer-pid"),src:t.attr("layer-src")||t.attr("src"),thumb:t.attr("src")})})};if(y(),0===d.length)return;if(i||h.on("click",t.img,function(){var e=n(this),i=e.attr("layer-index");r.photos(n.extend(t,{photos:{start:i,data:d,tab:t.tab},full:t.full}),!0),y()}),!i)return}l.imgprev=function(e){l.imgIndex--,l.imgIndex<1&&(l.imgIndex=d.length),l.tabimg(e)},l.imgnext=function(e,t){l.imgIndex++,l.imgIndex>d.length&&(l.imgIndex=1,t)||l.tabimg(e)},l.keyup=function(e){if(!l.end){var t=e.keyCode;e.preventDefault(),37===t?l.imgprev(!0):39===t?l.imgnext(!0):27===t&&r.close(l.index)}},l.tabimg=function(e){if(!(d.length<=1))return c.start=l.imgIndex-1,r.close(l.index),r.photos(t,!0,e)},l.event=function(){l.bigimg.hover(function(){l.imgsee.show()},function(){l.imgsee.hide()}),l.bigimg.find(".layui-layer-imgprev").on("click",function(e){e.preventDefault(),l.imgprev()}),l.bigimg.find(".layui-layer-imgnext").on("click",function(e){e.preventDefault(),l.imgnext()}),n(document).on("keyup",l.keyup)},l.loadi=r.load(1,{shade:!("shade"in t)&&.9,scrollbar:!1}),o(d[f].src,function(i){r.close(l.loadi),l.index=r.open(n.extend({type:1,id:"layui-layer-photos",area:function(){var a=[i.width,i.height],o=[n(e).width()-100,n(e).height()-100];if(!t.full&&(a[0]>o[0]||a[1]>o[1])){var r=[a[0]/o[0],a[1]/o[1]];r[0]>r[1]?(a[0]=a[0]/r[0],a[1]=a[1]/r[0]):r[0]<r[1]&&(a[0]=a[0]/r[1],a[1]=a[1]/r[1])}return[a[0]+"px",a[1]+"px"]}(),title:!1,shade:.9,shadeClose:!0,closeBtn:!1,move:".layui-layer-phimg img",moveType:1,scrollbar:!1,moveOut:!0,isOutAnim:!1,skin:"layui-layer-photos"+u("photos"),content:'<div class="layui-layer-phimg"><img src="'+d[f].src+'" alt="'+(d[f].alt||"")+'" layer-pid="'+d[f].pid+'"><div class="layui-layer-imgsee">'+(d.length>1?'<span class="layui-layer-imguide"><a href="javascript:;" class="layui-layer-iconext layui-layer-imgprev"></a><a href="javascript:;" class="layui-layer-iconext layui-layer-imgnext"></a></span>':"")+'<div class="layui-layer-imgbar" style="display:'+(a?"block":"")+'"><span class="layui-layer-imgtit"><a href="javascript:;">'+(d[f].alt||"")+"</a><em>"+l.imgIndex+"/"+d.length+"</em></span></div></div></div>",success:function(e,n){l.bigimg=e.find(".layui-layer-phimg"),l.imgsee=e.find(".layui-layer-imguide,.layui-layer-imgbar"),l.event(e),t.tab&&t.tab(d[f],e),"function"==typeof p&&p(e)},end:function(){l.end=!0,n(document).off("keyup",l.keyup)}},t))},function(){r.close(l.loadi),r.msg("&#x5F53;&#x524D;&#x56FE;&#x7247;&#x5730;&#x5740;&#x5F02;&#x5E38;<br>&#x662F;&#x5426;&#x7EE7;&#x7EED;&#x67E5;&#x770B;&#x4E0B;&#x4E00;&#x5F20;&#xFF1F;",{time:3e4,btn:["&#x4E0B;&#x4E00;&#x5F20;","&#x4E0D;&#x770B;&#x4E86;"],yes:function(){d.length>1&&l.imgnext(!0,!0)}})})}},o.run=function(t){n=t,i=n(e),s.html=n("html"),r.open=function(e){var t=new l(e);return t.index}},e.layui&&layui.define?(r.ready(),layui.define("jquery",function(t){r.path=layui.cache.dir,o.run(layui.$),e.layer=r,t("layer",r)})):"function"==typeof define&&define.amd?define(["jquery"],function(){return o.run(e.jQuery),r}):(o.run(e.jQuery),r.ready())}(window),layui.define("jquery",function(e){"use strict";var t=layui.$,n=(layui.hint(),layui.device()),i="element",a="layui-this",o="layui-show",r=function(){this.config={}};r.prototype.set=function(e){var n=this;return t.extend(!0,n.config,e),n},r.prototype.on=function(e,t){return layui.onevent.call(this,i,e,t)},r.prototype.tabAdd=function(e,n){var i=".layui-tab-title",a=t(".layui-tab[lay-filter="+e+"]"),o=a.children(i),r=o.children(".layui-tab-bar"),l=a.children(".layui-tab-content"),s='<li lay-id="'+(n.id||"")+'"'+(n.attr?' lay-attr="'+n.attr+'"':"")+">"+(n.title||"unnaming")+"</li>";return r[0]?r.before(s):o.append(s),l.append('<div class="layui-tab-item">'+(n.content||"")+"</div>"),h.hideTabMore(!0),h.tabAuto(),this},r.prototype.tabDelete=function(e,n){var i=".layui-tab-title",a=t(".layui-tab[lay-filter="+e+"]"),o=a.children(i),r=o.find('>li[lay-id="'+n+'"]');return h.tabDelete(null,r),this},
r.prototype.tabChange=function(e,n){var i=".layui-tab-title",a=t(".layui-tab[lay-filter="+e+"]"),o=a.children(i),r=o.find('>li[lay-id="'+n+'"]');return h.tabClick.call(r[0],null,null,r),this},r.prototype.tab=function(e){e=e||{},m.on("click",e.headerElem,function(n){var i=t(this).index();h.tabClick.call(this,n,i,null,e)})},r.prototype.progress=function(e,n){var i="layui-progress",a=t("."+i+"[lay-filter="+e+"]"),o=a.find("."+i+"-bar"),r=o.find("."+i+"-text");return o.css("width",n),r.text(n),this};var l=".layui-nav",s="layui-nav-item",c="layui-nav-bar",u="layui-nav-tree",d="layui-nav-child",f="layui-nav-more",p="layui-anim layui-anim-upbit",h={tabClick:function(e,n,r,l){l=l||{};var s=r||t(this),c=(n=n||s.parent().children("li").index(s),l.headerElem?s.parent():s.parents(".layui-tab").eq(0)),u=l.bodyElem?t(l.bodyElem):c.children(".layui-tab-content").children(".layui-tab-item"),d=s.find("a"),f=c.attr("lay-filter");"javascript:;"!==d.attr("href")&&"_blank"===d.attr("target")||(s.addClass(a).siblings().removeClass(a),u.eq(n).addClass(o).siblings().removeClass(o)),layui.event.call(this,i,"tab("+f+")",{elem:c,index:n})},tabDelete:function(e,n){var o=n||t(this).parent(),r=o.index(),l=o.parents(".layui-tab").eq(0),s=l.children(".layui-tab-content").children(".layui-tab-item"),c=l.attr("lay-filter");o.hasClass(a)&&(o.next()[0]?h.tabClick.call(o.next()[0],null,r+1):o.prev()[0]&&h.tabClick.call(o.prev()[0],null,r-1)),o.remove(),s.eq(r).remove(),setTimeout(function(){h.tabAuto()},50),layui.event.call(this,i,"tabDelete("+c+")",{elem:l,index:r})},tabAuto:function(){var e="layui-tab-more",i="layui-tab-bar",a="layui-tab-close",o=this;t(".layui-tab").each(function(){var r=t(this),l=r.children(".layui-tab-title"),s=(r.children(".layui-tab-content").children(".layui-tab-item"),'lay-stope="tabmore"'),c=t('<span class="layui-unselect layui-tab-bar" '+s+"><i "+s+' class="layui-icon">&#xe61a;</i></span>');if(o===window&&8!=n.ie&&h.hideTabMore(!0),r.attr("lay-allowClose")&&l.find("li").each(function(){var e=t(this);if(!e.find("."+a)[0]){var n=t('<i class="layui-icon layui-unselect '+a+'">&#x1006;</i>');n.on("click",h.tabDelete),e.append(n)}}),"string"!=typeof r.attr("lay-unauto"))if(l.prop("scrollWidth")>l.outerWidth()+1){if(l.find("."+i)[0])return;l.append(c),r.attr("overflow",""),c.on("click",function(t){l[this.title?"removeClass":"addClass"](e),this.title=this.title?"":"收缩"})}else l.find("."+i).remove(),r.removeAttr("overflow")})},hideTabMore:function(e){var n=t(".layui-tab-title");!0!==e&&"tabmore"===t(e.target).attr("lay-stope")||(n.removeClass("layui-tab-more"),n.find(".layui-tab-bar").attr("title",""))},clickThis:function(){var e=t(this),n=e.parents(l),o=n.attr("lay-filter"),r=e.parent(),c=e.siblings("."+d),f="string"==typeof r.attr("lay-unselect");"javascript:;"!==e.attr("href")&&"_blank"===e.attr("target")||f||c[0]||(n.find("."+a).removeClass(a),r.addClass(a)),n.hasClass(u)&&(c.removeClass(p),c[0]&&(r["none"===c.css("display")?"addClass":"removeClass"](s+"ed"),"all"===n.attr("lay-shrink")&&r.siblings().removeClass(s+"ed"))),layui.event.call(this,i,"nav("+o+")",e)},collapse:function(){var e=t(this),n=e.find(".layui-colla-icon"),a=e.siblings(".layui-colla-content"),r=e.parents(".layui-collapse").eq(0),l=r.attr("lay-filter"),s="none"===a.css("display");if("string"==typeof r.attr("lay-accordion")){var c=r.children(".layui-colla-item").children("."+o);c.siblings(".layui-colla-title").children(".layui-colla-icon").html("&#xe602;"),c.removeClass(o)}a[s?"addClass":"removeClass"](o),n.html(s?"&#xe61a;":"&#xe602;"),layui.event.call(this,i,"collapse("+l+")",{title:e,content:a,show:s})}};r.prototype.init=function(e,i){var a=i?'[lay-filter="'+i+'"]':"",r={tab:function(){h.tabAuto.call({})},nav:function(){var e=200,i={},r={},y={},m=function(a,l,s){var c=t(this),h=c.find("."+d);l.hasClass(u)?a.css({top:c.position().top,height:c.children("a").outerHeight(),opacity:1}):(h.addClass(p),a.css({left:c.position().left+parseFloat(c.css("marginLeft")),top:c.position().top+c.height()-a.height()}),i[s]=setTimeout(function(){a.css({width:c.width(),opacity:1})},n.ie&&n.ie<10?0:e),clearTimeout(y[s]),"block"===h.css("display")&&clearTimeout(r[s]),r[s]=setTimeout(function(){h.addClass(o),c.find("."+f).addClass(f+"d")},300))};t(l+a).each(function(n){var a=t(this),l=t('<span class="'+c+'"></span>'),p=a.find("."+s);a.find("."+c)[0]||(a.append(l),p.on("mouseenter",function(){m.call(this,l,a,n)}).on("mouseleave",function(){a.hasClass(u)||(clearTimeout(r[n]),r[n]=setTimeout(function(){a.find("."+d).removeClass(o),a.find("."+f).removeClass(f+"d")},300))}),a.on("mouseleave",function(){clearTimeout(i[n]),y[n]=setTimeout(function(){a.hasClass(u)?l.css({height:0,top:l.position().top+l.height()/2,opacity:0}):l.css({width:0,left:l.position().left+l.width()/2,opacity:0})},e)})),p.find("a").each(function(){var e=t(this),n=(e.parent(),e.siblings("."+d));n[0]&&!e.children("."+f)[0]&&e.append('<span class="'+f+'"></span>'),e.off("click",h.clickThis).on("click",h.clickThis)})})},breadcrumb:function(){var e=".layui-breadcrumb";t(e+a).each(function(){var e=t(this),n="lay-separator",i=e.attr(n)||"/",a=e.find("a");a.next("span["+n+"]")[0]||(a.each(function(e){e!==a.length-1&&t(this).after("<span "+n+">"+i+"</span>")}),e.css("visibility","visible"))})},progress:function(){var e="layui-progress";t("."+e+a).each(function(){var n=t(this),i=n.find(".layui-progress-bar"),a=i.attr("lay-percent");i.css("width",/^.+\/.+$/.test(a)?100*new Function("return "+a)()+"%":a),n.attr("lay-showPercent")&&setTimeout(function(){i.html('<span class="'+e+'-text">'+a+"</span>")},350)})},collapse:function(){var e="layui-collapse";t("."+e+a).each(function(){var e=t(this).find(".layui-colla-item");e.each(function(){var e=t(this),n=e.find(".layui-colla-title"),i=e.find(".layui-colla-content"),a="none"===i.css("display");n.find(".layui-colla-icon").remove(),n.append('<i class="layui-icon layui-colla-icon">'+(a?"&#xe602;":"&#xe61a;")+"</i>"),n.off("click",h.collapse).on("click",h.collapse)})})}};return r[e]?r[e]():layui.each(r,function(e,t){t()})},r.prototype.render=r.prototype.init;var y=new r,m=t(document);y.render();var v=".layui-tab-title li";m.on("click",v,h.tabClick),m.on("click",h.hideTabMore),t(window).on("resize",h.tabAuto),e(i,y)}),layui.define("layer",function(e){"use strict";var t=layui.$,n=layui.layer,i=layui.hint(),a=layui.device(),o={config:{},set:function(e){var n=this;return n.config=t.extend({},n.config,e),n},on:function(e,t){return layui.onevent.call(this,l,e,t)}},r=function(){var e=this;return{upload:function(t){e.upload.call(e,t)},config:e.config}},l="upload",s="layui-upload-file",c="layui-upload-form",u="layui-upload-iframe",d="layui-upload-choose",f=function(e){var n=this;n.config=t.extend({},n.config,o.config,e),n.render()};f.prototype.config={accept:"images",exts:"",auto:!0,bindAction:"",url:"",field:"file",method:"post",data:{},drag:!0,size:0,number:0,multiple:!1},f.prototype.render=function(e){var n=this;e=n.config;e.elem=t(e.elem),e.bindAction=t(e.bindAction),n.file(),n.events()},f.prototype.file=function(){var e=this,n=e.config,i=e.elemFile=t(['<input class="'+s+'" type="file" accept="'+n.acceptMime+'" name="'+n.field+'"',n.multiple?" multiple":"",">"].join("")),o=n.elem.next();(o.hasClass(s)||o.hasClass(c))&&o.remove(),a.ie&&a.ie<10&&n.elem.wrap('<div class="layui-upload-wrap"></div>'),e.isFile()?(e.elemFile=n.elem,n.field=n.elem[0].name):n.elem.after(i),a.ie&&a.ie<10&&e.initIE()},f.prototype.initIE=function(){var e=this,n=e.config,i=t('<iframe id="'+u+'" class="'+u+'" name="'+u+'" frameborder="0"></iframe>'),a=t(['<form target="'+u+'" class="'+c+'" method="post" key="set-mine" enctype="multipart/form-data" action="'+n.url+'">',"</form>"].join(""));t("#"+u)[0]||t("body").append(i),n.elem.next().hasClass(c)||(e.elemFile.wrap(a),n.elem.next("."+c).append(function(){var e=[];return layui.each(n.data,function(t,n){n="function"==typeof n?n():n,e.push('<input type="hidden" name="'+t+'" value="'+n+'">')}),e.join("")}()))},f.prototype.msg=function(e){return n.msg(e,{icon:2,shift:6})},f.prototype.isFile=function(){var e=this.config.elem[0];if(e)return"input"===e.tagName.toLocaleLowerCase()&&"file"===e.type},f.prototype.preview=function(e){var t=this;window.FileReader&&layui.each(t.chooseFiles,function(t,n){var i=new FileReader;i.readAsDataURL(n),i.onload=function(){e&&e(t,n,this.result)}})},f.prototype.upload=function(e,n){var i,o=this,r=o.config,l=o.elemFile[0],s=function(){var n=0,i=0,a=e||o.files||o.chooseFiles||l.files,s=function(){r.multiple&&n+i===o.fileLength&&"function"==typeof r.allDone&&r.allDone({total:o.fileLength,successful:n,aborted:i})};layui.each(a,function(e,a){var l=new FormData;l.append(r.field,a),layui.each(r.data,function(e,t){t="function"==typeof t?t():t,l.append(e,t)}),t.ajax({url:r.url,type:"post",data:l,contentType:!1,processData:!1,dataType:"json",headers:r.headers||{},success:function(t){n++,p(e,t),s()},error:function(){i++,o.msg("请求上传接口出现异常"),h(e),s()}})})},c=function(){var e=t("#"+u);o.elemFile.parent().submit(),clearInterval(f.timer),f.timer=setInterval(function(){var t,n=e.contents().find("body");try{t=n.text()}catch(e){o.msg("获取上传后的响应信息出现异常"),clearInterval(f.timer),h()}t&&(clearInterval(f.timer),n.html(""),p(0,t))},30)},p=function(e,t){if(o.elemFile.next("."+d).remove(),l.value="","object"!=typeof t)try{t=JSON.parse(t)}catch(e){return t={},o.msg("请对上传接口返回有效JSON")}"function"==typeof r.done&&r.done(t,e||0,function(e){o.upload(e)})},h=function(e){r.auto&&(l.value=""),"function"==typeof r.error&&r.error(e||0,function(e){o.upload(e)})},y=r.exts,m=function(){var t=[];return layui.each(e||o.chooseFiles,function(e,n){t.push(n.name)}),t}(),v={preview:function(e){o.preview(e)},upload:function(e,t){var n={};n[e]=t,o.upload(n)},pushFile:function(){return o.files=o.files||{},layui.each(o.chooseFiles,function(e,t){o.files[e]=t}),o.files},resetFile:function(e,t,n){var i=new File([t],n);o.files=o.files||{},o.files[e]=i}},g=function(){if("choose"!==n&&!r.auto||(r.choose&&r.choose(v),"choose"!==n))return r.before&&r.before(v),a.ie?a.ie>9?s():c():void s()};if(m=0===m.length?l.value.match(/[^\/\\]+\..+/g)||[]||"":m,0!==m.length){switch(r.accept){case"file":if(y&&!RegExp("\\w\\.("+y+")$","i").test(escape(m)))return o.msg("选择的文件中包含不支持的格式"),l.value="";break;case"video":if(!RegExp("\\w\\.("+(y||"avi|mp4|wma|rmvb|rm|flash|3gp|flv")+")$","i").test(escape(m)))return o.msg("选择的视频中包含不支持的格式"),l.value="";break;case"audio":if(!RegExp("\\w\\.("+(y||"mp3|wav|mid")+")$","i").test(escape(m)))return o.msg("选择的音频中包含不支持的格式"),l.value="";break;default:if(layui.each(m,function(e,t){RegExp("\\w\\.("+(y||"jpg|png|gif|bmp|jpeg$")+")","i").test(escape(t))||(i=!0)}),i)return o.msg("选择的图片中包含不支持的格式"),l.value=""}if(o.fileLength=function(){var t=0,n=e||o.files||o.chooseFiles||l.files;return layui.each(n,function(){t++}),t}(),r.number&&o.fileLength>r.number)return o.msg("同时最多只能上传的数量为："+r.number);var b;if(r.size>0&&!(a.ie&&a.ie<10))if(layui.each(o.chooseFiles,function(e,t){if(t.size>1024*r.size){var n=r.size/1024;n=n>=1?n.toFixed(2)+"MB":r.size+"KB",l.value="",b=n}}),b)return o.msg("文件不能超过"+b);g()}},f.prototype.events=function(){var e=this,n=e.config,o=function(t){e.chooseFiles={},layui.each(t,function(t,n){var i=(new Date).getTime();e.chooseFiles[i+"-"+t]=n})},r=function(t,i){var a=e.elemFile,o=t.length>1?t.length+"个文件":(t[0]||{}).name||a[0].value.match(/[^\/\\]+\..+/g)||[]||"";a.next().hasClass(d)&&a.next().remove(),e.upload(null,"choose"),e.isFile()||n.choose||a.after('<span class="layui-inline '+d+'">'+o+"</span>")};n.elem.off("upload.start").on("upload.start",function(){var a=t(this),o=a.attr("lay-data");if(o)try{o=new Function("return "+o)(),e.config=t.extend({},n,o)}catch(e){i.error("Upload element property lay-data configuration item has a syntax error: "+o)}e.config.item=a,e.elemFile[0].click()}),a.ie&&a.ie<10||n.elem.off("upload.over").on("upload.over",function(){var e=t(this);e.attr("lay-over","")}).off("upload.leave").on("upload.leave",function(){var e=t(this);e.removeAttr("lay-over")}).off("upload.drop").on("upload.drop",function(i,a){var l=t(this),s=a.originalEvent.dataTransfer.files||[];l.removeAttr("lay-over"),o(s),n.auto?e.upload(s):r(s)}),e.elemFile.off("upload.change").on("upload.change",function(){var t=this.files||[];o(t),n.auto?e.upload():r(t)}),n.bindAction.off("upload.action").on("upload.action",function(){e.upload()}),n.elem.data("haveEvents")||(e.elemFile.on("change",function(){t(this).trigger("upload.change")}),n.elem.on("click",function(){e.isFile()||t(this).trigger("upload.start")}),n.drag&&n.elem.on("dragover",function(e){e.preventDefault(),t(this).trigger("upload.over")}).on("dragleave",function(e){t(this).trigger("upload.leave")}).on("drop",function(e){e.preventDefault(),t(this).trigger("upload.drop",e)}),n.bindAction.on("click",function(){t(this).trigger("upload.action")}),n.elem.data("haveEvents",!0))},o.render=function(e){var t=new f(e);return r.call(t)},e(l,o)}),layui.define("jquery",function(e){"use strict";var t=layui.jquery,n={config:{},index:layui.slider?layui.slider.index+1e4:0,set:function(e){var n=this;return n.config=t.extend({},n.config,e),n},on:function(e,t){return layui.onevent.call(this,a,e,t)}},i=function(){var e=this,t=e.config;return{setValue:function(t,n){return e.slide("set",t,n||0)},config:t}},a="slider",o="layui-disabled",r="layui-slider",l="layui-slider-bar",s="layui-slider-wrap",c="layui-slider-wrap-btn",u="layui-slider-tips",d="layui-slider-input",f="layui-slider-input-txt",p="layui-slider-input-btn",h="layui-slider-hover",y=function(e){var i=this;i.index=++n.index,i.config=t.extend({},i.config,n.config,e),i.render()};y.prototype.config={type:"default",min:0,max:100,value:0,step:1,showstep:!1,tips:!0,input:!1,range:!1,height:200,disabled:!1,theme:"#009688"},y.prototype.render=function(){var e=this,n=e.config;if(n.step<1&&(n.step=1),n.max<n.min&&(n.max=n.min+n.step),n.range){n.value="object"==typeof n.value?n.value:[n.min,n.value];var i=Math.min(n.value[0],n.value[1]),a=Math.max(n.value[0],n.value[1]);n.value[0]=i>n.min?i:n.min,n.value[1]=a>n.min?a:n.min,n.value[0]=n.value[0]>n.max?n.max:n.value[0],n.value[1]=n.value[1]>n.max?n.max:n.value[1];var l=Math.floor((n.value[0]-n.min)/(n.max-n.min)*100),d=Math.floor((n.value[1]-n.min)/(n.max-n.min)*100),p=d-l+"%";l+="%",d+="%"}else{"object"==typeof n.value&&(n.value=Math.min.apply(null,n.value)),n.value<n.min&&(n.value=n.min),n.value>n.max&&(n.value=n.max);p=Math.floor((n.value-n.min)/(n.max-n.min)*100)+"%"}var h=n.disabled?"#c2c2c2":n.theme,y='<div class="layui-slider '+("vertical"===n.type?"layui-slider-vertical":"")+'">'+(n.tips?'<div class="layui-slider-tips"></div>':"")+'<div class="layui-slider-bar" style="background:'+h+"; "+("vertical"===n.type?"height":"width")+":"+p+";"+("vertical"===n.type?"bottom":"left")+":"+(l||0)+';"></div><div class="layui-slider-wrap" style="'+("vertical"===n.type?"bottom":"left")+":"+(l||p)+';"><div class="layui-slider-wrap-btn" style="border: 2px solid '+h+';"></div></div>'+(n.range?'<div class="layui-slider-wrap" style="'+("vertical"===n.type?"bottom":"left")+":"+d+';"><div class="layui-slider-wrap-btn" style="border: 2px solid '+h+';"></div></div>':"")+"</div>",m=t(n.elem),v=m.next("."+r);if(v[0]&&v.remove(),e.elemTemp=t(y),n.range?(e.elemTemp.find("."+s).eq(0).data("value",n.value[0]),e.elemTemp.find("."+s).eq(1).data("value",n.value[1])):e.elemTemp.find("."+s).data("value",n.value),m.html(e.elemTemp),"vertical"===n.type&&e.elemTemp.height(n.height+"px"),n.showstep){for(var g=(n.max-n.min)/n.step,b="",x=1;x<g+1;x++){var w=100*x/g;w<100&&(b+='<div class="layui-slider-step" style="'+("vertical"===n.type?"bottom":"left")+":"+w+'%"></div>')}e.elemTemp.append(b)}if(n.input&&!n.range){var k=t('<div class="layui-slider-input layui-input"><div class="layui-slider-input-txt"><input type="text" class="layui-input"></div><div class="layui-slider-input-btn"><i class="layui-icon layui-icon-up"></i><i class="layui-icon layui-icon-down"></i></div></div>');m.css("position","relative"),m.append(k),m.find("."+f).children("input").val(n.value),"vertical"===n.type?k.css({left:0,top:-48}):e.elemTemp.css("margin-right",k.outerWidth()+15)}n.disabled?(e.elemTemp.addClass(o),e.elemTemp.find("."+c).addClass(o)):e.slide(),e.elemTemp.find("."+c).on("mouseover",function(){var i="vertical"===n.type?n.height:e.elemTemp[0].offsetWidth,a=e.elemTemp.find("."+s),o="vertical"===n.type?i-t(this).parent()[0].offsetTop-a.height():t(this).parent()[0].offsetLeft,r=o/i*100,l=t(this).parent().data("value"),c=n.setTips?n.setTips(l):l;e.elemTemp.find("."+u).html(c),"vertical"===n.type?e.elemTemp.find("."+u).css({bottom:r+"%","margin-bottom":"20px",display:"inline-block"}):e.elemTemp.find("."+u).css({left:r+"%",display:"inline-block"})}).on("mouseout",function(){e.elemTemp.find("."+u).css("display","none")})},y.prototype.slide=function(e,n,i){var a=this,o=a.config,r=a.elemTemp,y=function(){return"vertical"===o.type?o.height:r[0].offsetWidth},m=r.find("."+s),v=r.next("."+d),g=v.children("."+f).children("input").val(),b=100/((o.max-o.min)/Math.ceil(o.step)),x=function(e,t){e=Math.ceil(e)*b>100?Math.ceil(e)*b:Math.round(e)*b,e=e>100?100:e,m.eq(t).css("vertical"===o.type?"bottom":"left",e+"%");var n=w(m[0].offsetLeft),i=o.range?w(m[1].offsetLeft):0;"vertical"===o.type?(r.find("."+u).css({bottom:e+"%","margin-bottom":"20px"}),n=w(y()-m[0].offsetTop-m.height()),i=o.range?w(y()-m[1].offsetTop-m.height()):0):r.find("."+u).css("left",e+"%"),n=n>100?100:n,i=i>100?100:i;var a=Math.min(n,i),s=Math.abs(n-i);"vertical"===o.type?r.find("."+l).css({height:s+"%",bottom:a+"%"}):r.find("."+l).css({width:s+"%",left:a+"%"});var c=o.min+Math.round((o.max-o.min)*e/100);if(g=c,v.children("."+f).children("input").val(g),m.eq(t).data("value",c),c=o.setTips?o.setTips(c):c,r.find("."+u).html(c),o.range){var d=[m.eq(0).data("value"),m.eq(1).data("value")];d[0]>d[1]&&d.reverse()}o.change&&o.change(o.range?d:c)},w=function(e){var t=e/y()*100/b,n=Math.round(t)*b;return e==y()&&(n=Math.ceil(t)*b),n},k=t(['<div class="layui-auxiliar-moving" id="LAY-slider-moving"></div'].join("")),C=function(e,n){var i=function(){n&&n(),k.remove()};t("#LAY-slider-moving")[0]||t("body").append(k),k.on("mousemove",e),k.on("mouseup",i).on("mouseleave",i)};if("set"===e)return x(n,i);r.find("."+c).each(function(e){var n=t(this);n.on("mousedown",function(t){t=t||window.event;var i=n.parent()[0].offsetLeft,a=t.clientX;"vertical"===o.type&&(i=y()-n.parent()[0].offsetTop-m.height(),a=t.clientY);var l=function(t){t=t||window.event;var l=i+("vertical"===o.type?a-t.clientY:t.clientX-a);l<0&&(l=0),l>y()&&(l=y());var s=l/y()*100/b;x(s,e),n.addClass(h),r.find("."+u).show(),t.preventDefault()},s=function(){n.removeClass(h),r.find("."+u).hide()};C(l,s)})}),r.on("click",function(e){var n=t("."+c);if(!n.is(event.target)&&0===n.has(event.target).length&&n.length){var i,a="vertical"===o.type?y()-e.clientY+t(this).offset().top:e.clientX-t(this).offset().left;a<0&&(a=0),a>y()&&(a=y());var r=a/y()*100/b;i=o.range?"vertical"===o.type?Math.abs(a-parseInt(t(m[0]).css("bottom")))>Math.abs(a-parseInt(t(m[1]).css("bottom")))?1:0:Math.abs(a-m[0].offsetLeft)>Math.abs(a-m[1].offsetLeft)?1:0:0,x(r,i),e.preventDefault()}}),v.hover(function(){var e=t(this);e.children("."+p).fadeIn("fast")},function(){var e=t(this);e.children("."+p).fadeOut("fast")}),v.children("."+p).children("i").each(function(e){t(this).on("click",function(){g=1==e?g-o.step<o.min?o.min:Number(g)-o.step:Number(g)+o.step>o.max?o.max:Number(g)+o.step;var t=(g-o.min)/(o.max-o.min)*100/b;x(t,0)})});var T=function(){var e=this.value;e=isNaN(e)?0:e,e=e<o.min?o.min:e,e=e>o.max?o.max:e,this.value=e;var t=(e-o.min)/(o.max-o.min)*100/b;x(t,0)};v.children("."+f).children("input").on("keydown",function(e){13===e.keyCode&&(e.preventDefault(),T.call(this))}).on("change",T)},y.prototype.events=function(){var e=this;e.config},n.render=function(e){var t=new y(e);return i.call(t)},e(a,n)}),layui.define("jquery",function(e){"use strict";var t=layui.jquery,n={config:{},index:layui.colorpicker?layui.colorpicker.index+1e4:0,set:function(e){var n=this;return n.config=t.extend({},n.config,e),n},on:function(e,t){return layui.onevent.call(this,"colorpicker",e,t)}},i=function(){var e=this,t=e.config;return{config:t}},a="colorpicker",o="layui-show",r="layui-colorpicker",l=".layui-colorpicker-main",s="layui-icon-down",c="layui-icon-close",u="layui-colorpicker-trigger-span",d="layui-colorpicker-trigger-i",f="layui-colorpicker-side",p="layui-colorpicker-side-slider",h="layui-colorpicker-basis",y="layui-colorpicker-alpha-bgcolor",m="layui-colorpicker-alpha-slider",v="layui-colorpicker-basis-cursor",g="layui-colorpicker-main-input",b=function(e){var t={h:0,s:0,b:0},n=Math.min(e.r,e.g,e.b),i=Math.max(e.r,e.g,e.b),a=i-n;return t.b=i,t.s=0!=i?255*a/i:0,0!=t.s?e.r==i?t.h=(e.g-e.b)/a:e.g==i?t.h=2+(e.b-e.r)/a:t.h=4+(e.r-e.g)/a:t.h=-1,i==n&&(t.h=0),t.h*=60,t.h<0&&(t.h+=360),t.s*=100/255,t.b*=100/255,t},x=function(e){e=e.indexOf("#")>-1?e.substring(1):e;if(3==e.length){var t=e.split("");e=t[0]+t[0]+t[1]+t[1]+t[2]+t[2]}e=parseInt(e,16);var n={r:e>>16,g:(65280&e)>>8,b:255&e};return b(n)},w=function(e){var t={},n=e.h,i=255*e.s/100,a=255*e.b/100;if(0==i)t.r=t.g=t.b=a;else{var o=a,r=(255-i)*a/255,l=n%60*(o-r)/60;360==n&&(n=0),n<60?(t.r=o,t.b=r,t.g=r+l):n<120?(t.g=o,t.b=r,t.r=o-l):n<180?(t.g=o,t.r=r,t.b=r+l):n<240?(t.b=o,t.r=r,t.g=o-l):n<300?(t.b=o,t.g=r,t.r=r+l):n<360?(t.r=o,t.g=r,t.b=o-l):(t.r=0,t.g=0,t.b=0)}return{r:Math.round(t.r),g:Math.round(t.g),b:Math.round(t.b)}},k=function(e){var n=w(e),i=[n.r.toString(16),n.g.toString(16),n.b.toString(16)];return t.each(i,function(e,t){1==t.length&&(i[e]="0"+t)}),i.join("")},C=function(e){var t=/[0-9]{1,3}/g,n=e.match(t)||[];return{r:n[0],g:n[1],b:n[2]}},T=t(window),E=t(document),D=function(e){var i=this;i.index=++n.index,i.config=t.extend({},i.config,n.config,e),i.render()};D.prototype.config={color:"",size:null,alpha:!1,format:"hex",predefine:!1,colors:["#009688","#5FB878","#1E9FFF","#FF5722","#FFB800","#01AAED","#999","#c00","#ff8c00","#ffd700","#90ee90","#00ced1","#1e90ff","#c71585","rgb(0, 186, 189)","rgb(255, 120, 0)","rgb(250, 212, 0)","#393D49","rgba(0,0,0,.5)","rgba(255, 69, 0, 0.68)","rgba(144, 240, 144, 0.5)","rgba(31, 147, 255, 0.73)"]},D.prototype.render=function(){var e=this,n=e.config,i=t(['<div class="layui-unselect layui-colorpicker">',"<span "+("rgb"==n.format&&n.alpha?'class="layui-colorpicker-trigger-bgcolor"':"")+">",'<span class="layui-colorpicker-trigger-span" ','lay-type="'+("rgb"==n.format?n.alpha?"rgba":"torgb":"")+'" ','style="'+function(){var e="";return n.color?(e=n.color,(n.color.match(/[0-9]{1,3}/g)||[]).length>3&&(n.alpha&&"rgb"==n.format||(e="#"+k(b(C(n.color))))),"background: "+e):e}()+'">','<i class="layui-icon layui-colorpicker-trigger-i '+(n.color?s:c)+'"></i>',"</span>","</span>","</div>"].join("")),a=t(n.elem);n.size&&i.addClass("layui-colorpicker-"+n.size),a.addClass("layui-inline").html(e.elemColorBox=i),e.color=e.elemColorBox.find("."+u)[0].style.background,e.events()},D.prototype.renderPicker=function(){var e=this,n=e.config,i=e.elemColorBox[0],a=e.elemPicker=t(['<div id="layui-colorpicker'+e.index+'" data-index="'+e.index+'" class="layui-anim layui-anim-upbit layui-colorpicker-main">','<div class="layui-colorpicker-main-wrapper">','<div class="layui-colorpicker-basis">','<div class="layui-colorpicker-basis-white"></div>','<div class="layui-colorpicker-basis-black"></div>','<div class="layui-colorpicker-basis-cursor"></div>',"</div>",'<div class="layui-colorpicker-side">','<div class="layui-colorpicker-side-slider"></div>',"</div>","</div>",'<div class="layui-colorpicker-main-alpha '+(n.alpha?o:"")+'">','<div class="layui-colorpicker-alpha-bgcolor">','<div class="layui-colorpicker-alpha-slider"></div>',"</div>","</div>",function(){if(n.predefine){var e=['<div class="layui-colorpicker-main-pre">'];return layui.each(n.colors,function(t,n){e.push(['<div class="layui-colorpicker-pre'+((n.match(/[0-9]{1,3}/g)||[]).length>3?" layui-colorpicker-pre-isalpha":"")+'">','<div style="background:'+n+'"></div>',"</div>"].join(""))}),e.push("</div>"),e.join("")}return""}(),'<div class="layui-colorpicker-main-input">','<div class="layui-inline">','<input type="text" class="layui-input">',"</div>",'<div class="layui-btn-container">','<button class="layui-btn layui-btn-primary layui-btn-sm" colorpicker-events="clear">清空</button>','<button class="layui-btn layui-btn-sm" colorpicker-events="confirm">确定</button>',"</div","</div>","</div>"].join(""));e.elemColorBox.find("."+u)[0],t(l)[0]&&t(l).data("index")==e.index?e.removePicker(D.thisElemInd):(e.removePicker(D.thisElemInd),t("body").append(a)),D.thisElemInd=e.index,D.thisColor=i.style.background,e.position(),e.pickerEvents()},D.prototype.removePicker=function(e){var n=this;return n.config,t("#layui-colorpicker"+(e||n.index)).remove(),n},D.prototype.position=function(){var e=this,t=e.config,n=e.bindElem||e.elemColorBox[0],i=e.elemPicker[0],a=n.getBoundingClientRect(),o=i.offsetWidth,r=i.offsetHeight,l=function(e){return e=e?"scrollLeft":"scrollTop",document.body[e]|document.documentElement[e]},s=function(e){return document.documentElement[e?"clientWidth":"clientHeight"]},c=5,u=a.left,d=a.bottom;u-=(o-n.offsetWidth)/2,d+=c,u+o+c>s("width")?u=s("width")-o-c:u<c&&(u=c),d+r+c>s()&&(d=a.top>r?a.top-r:s()-r,d-=2*c),t.position&&(i.style.position=t.position),i.style.left=u+("fixed"===t.position?0:l(1))+"px",i.style.top=d+("fixed"===t.position?0:l())+"px"},D.prototype.val=function(){var e=this,t=(e.config,e.elemColorBox.find("."+u)),n=e.elemPicker.find("."+g),i=t[0],a=i.style.backgroundColor;if(a){var o=b(C(a)),r=t.attr("lay-type");if(e.select(o.h,o.s,o.b),"torgb"===r&&n.find("input").val(a),"rgba"===r){var l=C(a);if(3==(a.match(/[0-9]{1,3}/g)||[]).length)n.find("input").val("rgba("+l.r+", "+l.g+", "+l.b+", 1)"),e.elemPicker.find("."+m).css("left",280);else{n.find("input").val(a);var s=280*a.slice(a.lastIndexOf(",")+1,a.length-1);e.elemPicker.find("."+m).css("left",s)}e.elemPicker.find("."+y)[0].style.background="linear-gradient(to right, rgba("+l.r+", "+l.g+", "+l.b+", 0), rgb("+l.r+", "+l.g+", "+l.b+"))"}}else e.select(0,100,100),n.find("input").val(""),e.elemPicker.find("."+y)[0].style.background="",e.elemPicker.find("."+m).css("left",280)},D.prototype.side=function(){var e=this,n=e.config,i=e.elemColorBox.find("."+u),a=i.attr("lay-type"),o=e.elemPicker.find("."+f),r=e.elemPicker.find("."+p),l=e.elemPicker.find("."+h),x=e.elemPicker.find("."+v),k=e.elemPicker.find("."+y),E=e.elemPicker.find("."+m),D=r[0].offsetTop/180*360,S=100-(x[0].offsetTop+3)/180*100,L=(x[0].offsetLeft+3)/260*100,A=Math.round(E[0].offsetLeft/280*100)/100,j=e.elemColorBox.find("."+d),N=e.elemPicker.find(".layui-colorpicker-pre").children("div"),M=function(t,o,r,l){e.select(t,o,r);var u=w({h:t,s:o,b:r});if(j.addClass(s).removeClass(c),i[0].style.background="rgb("+u.r+", "+u.g+", "+u.b+")","torgb"===a&&e.elemPicker.find("."+g).find("input").val("rgb("+u.r+", "+u.g+", "+u.b+")"),"rgba"===a){var d=0;d=280*l,E.css("left",d),e.elemPicker.find("."+g).find("input").val("rgba("+u.r+", "+u.g+", "+u.b+", "+l+")"),i[0].style.background="rgba("+u.r+", "+u.g+", "+u.b+", "+l+")",k[0].style.background="linear-gradient(to right, rgba("+u.r+", "+u.g+", "+u.b+", 0), rgb("+u.r+", "+u.g+", "+u.b+"))"}n.change&&n.change(e.elemPicker.find("."+g).find("input").val())},H=t(['<div class="layui-auxiliar-moving" id="LAY-colorpicker-moving"></div'].join("")),F=function(e){t("#LAY-colorpicker-moving")[0]||t("body").append(H),H.on("mousemove",e),H.on("mouseup",function(){H.remove()}).on("mouseleave",function(){H.remove()})};r.on("mousedown",function(e){var t=this.offsetTop,n=e.clientY,i=function(e){var i=t+(e.clientY-n),a=o[0].offsetHeight;i<0&&(i=0),i>a&&(i=a);var r=i/180*360;D=r,M(r,L,S,A),e.preventDefault()};F(i),e.preventDefault()}),o.on("click",function(e){var n=e.clientY-t(this).offset().top;n<0&&(n=0),n>this.offsetHeight&&(n=this.offsetHeight);var i=n/180*360;D=i,M(i,L,S,A),e.preventDefault()}),x.on("mousedown",function(e){var t=this.offsetTop,n=this.offsetLeft,i=e.clientY,a=e.clientX,o=function(e){var o=t+(e.clientY-i),r=n+(e.clientX-a),s=l[0].offsetHeight-3,c=l[0].offsetWidth-3;o<-3&&(o=-3),o>s&&(o=s),r<-3&&(r=-3),r>c&&(r=c);var u=(r+3)/260*100,d=100-(o+3)/180*100;S=d,L=u,M(D,u,d,A),e.preventDefault()};layui.stope(e),F(o),e.preventDefault()}),l.on("mousedown",function(e){var n=e.clientY-t(this).offset().top-3+T.scrollTop(),i=e.clientX-t(this).offset().left-3+T.scrollLeft();n<-3&&(n=-3),n>this.offsetHeight-3&&(n=this.offsetHeight-3),i<-3&&(i=-3),i>this.offsetWidth-3&&(i=this.offsetWidth-3);var a=(i+3)/260*100,o=100-(n+3)/180*100;S=o,L=a,M(D,a,o,A),e.preventDefault(),x.trigger(e,"mousedown")}),E.on("mousedown",function(e){var t=this.offsetLeft,n=e.clientX,i=function(e){var i=t+(e.clientX-n),a=k[0].offsetWidth;i<0&&(i=0),i>a&&(i=a);var o=Math.round(i/280*100)/100;A=o,M(D,L,S,o),e.preventDefault()};F(i),e.preventDefault()}),k.on("click",function(e){var n=e.clientX-t(this).offset().left;n<0&&(n=0),n>this.offsetWidth&&(n=this.offsetWidth);var i=Math.round(n/280*100)/100;A=i,M(D,L,S,i),e.preventDefault()}),N.each(function(){t(this).on("click",function(){t(this).parent(".layui-colorpicker-pre").addClass("selected").siblings().removeClass("selected");var e=this.style.backgroundColor,n=b(C(e)),i=e.slice(e.lastIndexOf(",")+1,e.length-1);D=n.h,L=n.s,S=n.b,3==(e.match(/[0-9]{1,3}/g)||[]).length&&(i=1),A=i,M(n.h,n.s,n.b,i)})})},D.prototype.select=function(e,t,n,i){var a=this,o=(a.config,k({h:e,s:100,b:100})),r=k({h:e,s:t,b:n}),l=e/360*180,s=180-n/100*180-3,c=t/100*260-3;a.elemPicker.find("."+p).css("top",l),a.elemPicker.find("."+h)[0].style.background="#"+o,a.elemPicker.find("."+v).css({top:s,left:c}),"change"!==i&&a.elemPicker.find("."+g).find("input").val("#"+r)},D.prototype.pickerEvents=function(){var e=this,n=e.config,i=e.elemColorBox.find("."+u),a=e.elemPicker.find("."+g+" input"),o={clear:function(t){i[0].style.background="",e.elemColorBox.find("."+d).removeClass(s).addClass(c),e.color="",n.done&&n.done(""),e.removePicker()},confirm:function(t,o){var r=a.val(),l=r,u={};if(r.indexOf(",")>-1){if(u=b(C(r)),e.select(u.h,u.s,u.b),i[0].style.background=l="#"+k(u),(r.match(/[0-9]{1,3}/g)||[]).length>3&&"rgba"===i.attr("lay-type")){var f=280*r.slice(r.lastIndexOf(",")+1,r.length-1);e.elemPicker.find("."+m).css("left",f),i[0].style.background=r,l=r}}else u=x(r),i[0].style.background=l="#"+k(u),e.elemColorBox.find("."+d).removeClass(c).addClass(s);return"change"===o?(e.select(u.h,u.s,u.b,o),void(n.change&&n.change(l))):(e.color=r,n.done&&n.done(r),void e.removePicker())}};e.elemPicker.on("click","*[colorpicker-events]",function(){var e=t(this),n=e.attr("colorpicker-events");o[n]&&o[n].call(this,e)}),a.on("keyup",function(e){var n=t(this);o.confirm.call(this,n,13===e.keyCode?null:"change")})},D.prototype.events=function(){var e=this,n=e.config,i=e.elemColorBox.find("."+u);e.elemColorBox.on("click",function(){e.renderPicker(),t(l)[0]&&(e.val(),e.side())}),n.elem[0]&&!e.elemColorBox[0].eventHandler&&(E.on("click",function(n){if(!t(n.target).hasClass(r)&&!t(n.target).parents("."+r)[0]&&!t(n.target).hasClass(l.replace(/\./g,""))&&!t(n.target).parents(l)[0]&&e.elemPicker){if(e.color){var a=b(C(e.color));e.select(a.h,a.s,a.b)}else e.elemColorBox.find("."+d).removeClass(s).addClass(c);i[0].style.background=e.color||"",e.removePicker()}}),T.on("resize",function(){return!(!e.elemPicker||!t(l)[0])&&void e.position()}),e.elemColorBox[0].eventHandler=!0)},n.render=function(e){var t=new D(e);return i.call(t)},e(a,n)}),layui.define("layer",function(e){"use strict";var t=layui.$,n=layui.layer,i=layui.hint(),a=layui.device(),o="form",r=".layui-form",l="layui-this",s="layui-hide",c="layui-disabled",u=function(){this.config={verify:{required:[/[\S]+/,"必填项不能为空"],phone:[/^1\d{10}$/,"请输入正确的手机号"],email:[/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/,"邮箱格式不正确"],
url:[/(^#)|(^http(s*):\/\/[^\s]+\.[^\s]+)/,"链接格式不正确"],number:function(e){if(!e||isNaN(e))return"只能填写数字"},date:[/^(\d{4})[-\/](\d{1}|0\d{1}|1[0-2])([-\/](\d{1}|0\d{1}|[1-2][0-9]|3[0-1]))*$/,"日期格式不正确"],identity:[/(^\d{15}$)|(^\d{17}(x|X|\d)$)/,"请输入正确的身份证号"]}}};u.prototype.set=function(e){var n=this;return t.extend(!0,n.config,e),n},u.prototype.verify=function(e){var n=this;return t.extend(!0,n.config.verify,e),n},u.prototype.on=function(e,t){return layui.onevent.call(this,o,e,t)},u.prototype.val=function(e,n){var i=t(r+'[lay-filter="'+e+'"]');i.each(function(e,i){var a=t(this);layui.each(n,function(e,t){var n,i=a.find('[name="'+e+'"]');i[0]&&(n=i[0].type,"checkbox"===n?i[0].checked=t:"radio"===n?i.each(function(){this.value===t&&(this.checked=!0)}):i.val(t))})}),f.render(null,e)},u.prototype.render=function(e,n){var a=this,u=t(r+(n?'[lay-filter="'+n+'"]':"")),d={select:function(){var e,n="请选择",i="layui-form-select",a="layui-select-title",r="layui-select-none",d="",f=u.find("select"),p=function(n,o){t(n.target).parent().hasClass(a)&&!o||(t("."+i).removeClass(i+"ed "+i+"up"),e&&d&&e.val(d)),e=null},y=function(n,u,f){var y,m=t(this),v=n.find("."+a),g=v.find("input"),b=n.find("dl"),x=b.children("dd"),w=this.selectedIndex;if(!u){var k=function(){var e=n.offset().top+n.outerHeight()+5-h.scrollTop(),t=b.outerHeight();w=m[0].selectedIndex,n.addClass(i+"ed"),x.removeClass(s),y=null,x.eq(w).addClass(l).siblings().removeClass(l),e+t>h.height()&&e>=t&&n.addClass(i+"up"),T()},C=function(e){n.removeClass(i+"ed "+i+"up"),g.blur(),y=null,e||E(g.val(),function(e){var n=m[0].selectedIndex;e&&(d=t(m[0].options[n]).html(),0===n&&d===g.attr("placeholder")&&(d=""),g.val(d||""))})},T=function(){var e=b.children("dd."+l);if(e[0]){var t=e.position().top,n=b.height(),i=e.height();t>n&&b.scrollTop(t+b.scrollTop()-n+i-5),t<0&&b.scrollTop(t+b.scrollTop()-5)}};v.on("click",function(e){n.hasClass(i+"ed")?C():(p(e,!0),k()),b.find("."+r).remove()}),v.find(".layui-edge").on("click",function(){g.focus()}),g.on("keyup",function(e){var t=e.keyCode;9===t&&k()}).on("keydown",function(e){var t=e.keyCode;9===t&&C();var n=function(t,i){var a,o;e.preventDefault();var r=function(){var e=b.children("dd."+l);if(b.children("dd."+s)[0]&&"next"===t){var n=b.children("dd:not(."+s+",."+c+")"),a=n.eq(0).index();if(a>=0&&a<e.index()&&!n.hasClass(l))return n.eq(0).prev()[0]?n.eq(0).prev():b.children(":last")}return i&&i[0]?i:y&&y[0]?y:e}();return o=r[t](),a=r[t]("dd:not(."+s+")"),o[0]?(y=r[t](),a[0]&&!a.hasClass(c)||!y[0]?(a.addClass(l).siblings().removeClass(l),void T()):n(t,y)):y=null};38===t&&n("prev"),40===t&&n("next"),13===t&&(e.preventDefault(),b.children("dd."+l).trigger("click"))});var E=function(e,n,i){var a=0;layui.each(x,function(){var n=t(this),o=n.text(),r=-1===o.indexOf(e);(""===e||"blur"===i?e!==o:r)&&a++,"keyup"===i&&n[r?"addClass":"removeClass"](s)});var o=a===x.length;return n(o),o},D=function(e){var t=this.value,n=e.keyCode;return 9!==n&&13!==n&&37!==n&&38!==n&&39!==n&&40!==n&&(E(t,function(e){e?b.find("."+r)[0]||b.append('<p class="'+r+'">无匹配项</p>'):b.find("."+r).remove()},"keyup"),""===t&&b.find("."+r).remove(),void T())};f&&g.on("keyup",D).on("blur",function(n){var i=m[0].selectedIndex;e=g,d=t(m[0].options[i]).html(),0===i&&d===g.attr("placeholder")&&(d=""),setTimeout(function(){E(g.val(),function(e){d||g.val("")},"blur")},200)}),x.on("click",function(){var e=t(this),i=e.attr("lay-value"),a=m.attr("lay-filter");return!e.hasClass(c)&&(e.hasClass("layui-select-tips")?g.val(""):(g.val(e.text()),e.addClass(l)),e.siblings().removeClass(l),m.val(i).removeClass("layui-form-danger"),layui.event.call(this,o,"select("+a+")",{elem:m[0],value:i,othis:n}),C(!0),!1)}),n.find("dl>dt").on("click",function(e){return!1}),t(document).off("click",p).on("click",p)}};f.each(function(e,o){var r=t(this),s=r.next("."+i),u=this.disabled,d=o.value,f=t(o.options[o.selectedIndex]),p=o.options[0];if("string"==typeof r.attr("lay-ignore"))return r.show();var h="string"==typeof r.attr("lay-search"),m=p?p.value?n:p.innerHTML||n:n,v=t(['<div class="'+(h?"":"layui-unselect ")+i,(u?" layui-select-disabled":"")+'">','<div class="'+a+'">','<input type="text" placeholder="'+m+'" value="'+(d?f.html():"")+'"'+(h?"":" readonly")+' class="layui-input'+(h?"":" layui-unselect")+(u?" "+c:"")+'">','<i class="layui-edge"></i></div>','<dl class="layui-anim layui-anim-upbit'+(r.find("optgroup")[0]?" layui-select-group":"")+'">',function(e){var t=[];return layui.each(e,function(e,i){0!==e||i.value?"optgroup"===i.tagName.toLowerCase()?t.push("<dt>"+i.label+"</dt>"):t.push('<dd lay-value="'+i.value+'" class="'+(d===i.value?l:"")+(i.disabled?" "+c:"")+'">'+i.innerHTML+"</dd>"):t.push('<dd lay-value="" class="layui-select-tips">'+(i.innerHTML||n)+"</dd>")}),0===t.length&&t.push('<dd lay-value="" class="'+c+'">没有选项</dd>'),t.join("")}(r.find("*"))+"</dl>","</div>"].join(""));s[0]&&s.remove(),r.after(v),y.call(this,v,u,h)})},checkbox:function(){var e={checkbox:["layui-form-checkbox","layui-form-checked","checkbox"],_switch:["layui-form-switch","layui-form-onswitch","switch"]},n=u.find("input[type=checkbox]"),i=function(e,n){var i=t(this);e.on("click",function(){var t=i.attr("lay-filter"),a=(i.attr("lay-text")||"").split("|");i[0].disabled||(i[0].checked?(i[0].checked=!1,e.removeClass(n[1]).find("em").text(a[1])):(i[0].checked=!0,e.addClass(n[1]).find("em").text(a[0])),layui.event.call(i[0],o,n[2]+"("+t+")",{elem:i[0],value:i[0].value,othis:e}))})};n.each(function(n,a){var o=t(this),r=o.attr("lay-skin"),l=(o.attr("lay-text")||"").split("|"),s=this.disabled;"switch"===r&&(r="_"+r);var u=e[r]||e.checkbox;if("string"==typeof o.attr("lay-ignore"))return o.show();var d=o.next("."+u[0]),f=t(['<div class="layui-unselect '+u[0],a.checked?" "+u[1]:"",s?" layui-checkbox-disbaled "+c:"",'"',r?' lay-skin="'+r+'"':"",">",function(){var e=a.title.replace(/\s/g,""),t={checkbox:[e?"<span>"+a.title+"</span>":"",'<i class="layui-icon layui-icon-ok"></i>'].join(""),_switch:"<em>"+((a.checked?l[0]:l[1])||"")+"</em><i></i>"};return t[r]||t.checkbox}(),"</div>"].join(""));d[0]&&d.remove(),o.after(f),i.call(this,f,u)})},radio:function(){var e="layui-form-radio",n=["&#xe643;","&#xe63f;"],i=u.find("input[type=radio]"),a=function(i){var a=t(this),l="layui-anim-scaleSpring";i.on("click",function(){var s=a[0].name,c=a.parents(r),u=a.attr("lay-filter"),d=c.find("input[name="+s.replace(/(\.|#|\[|\])/g,"\\$1")+"]");a[0].disabled||(layui.each(d,function(){var i=t(this).next("."+e);this.checked=!1,i.removeClass(e+"ed"),i.find(".layui-icon").removeClass(l).html(n[1])}),a[0].checked=!0,i.addClass(e+"ed"),i.find(".layui-icon").addClass(l).html(n[0]),layui.event.call(a[0],o,"radio("+u+")",{elem:a[0],value:a[0].value,othis:i}))})};i.each(function(i,o){var r=t(this),l=r.next("."+e),s=this.disabled;if("string"==typeof r.attr("lay-ignore"))return r.show();l[0]&&l.remove();var u=t(['<div class="layui-unselect '+e,o.checked?" "+e+"ed":"",(s?" layui-radio-disbaled "+c:"")+'">','<i class="layui-anim layui-icon">'+n[o.checked?0:1]+"</i>","<div>"+function(){var e=o.title||"";return"string"==typeof r.next().attr("lay-radio")&&(e=r.next().html(),r.next().remove()),e}()+"</div>","</div>"].join(""));r.after(u),a.call(this,u)})}};return e?d[e]?d[e]():i.error("不支持的"+e+"表单渲染"):layui.each(d,function(e,t){t()}),a};var d=function(){var e=t(this),i=f.config.verify,l=null,s="layui-form-danger",c={},u=e.parents(r),d=u.find("*[lay-verify]"),p=e.parents("form")[0],h=u.find("input,select,textarea"),y=e.attr("lay-filter");if(layui.each(d,function(e,o){var r=t(this),c=r.attr("lay-verify").split("|"),u=r.attr("lay-verType"),d=r.val();if(r.removeClass(s),layui.each(c,function(e,t){var c="",f="function"==typeof i[t];if(i[t]){var p=f?c=i[t](d,o):!i[t][0].test(d);if(c=c||i[t][1],p)return"tips"===u?n.tips(c,"string"==typeof r.attr("lay-ignore")||"select"!==o.tagName.toLowerCase()&&!/^checkbox|radio$/.test(o.type)?r:r.next(),{tips:1}):"alert"===u?n.alert(c,{title:"提示",shadeClose:!0}):n.msg(c,{icon:5,shift:6}),a.android||a.ios||o.focus(),r.addClass(s),l=!0}}),l)return l}),l)return!1;var m={};return layui.each(h,function(e,t){if(t.name=(t.name||"").replace(/^\s*|\s*&/,""),t.name){if(/^.*\[\]$/.test(t.name)){var n=t.name.match(/^(.*)\[\]$/g)[0];m[n]=0|m[n],t.name=t.name.replace(/^(.*)\[\]$/,"$1["+m[n]+++"]")}/^checkbox|radio$/.test(t.type)&&!t.checked||(c[t.name]=t.value)}}),layui.event.call(this,o,"submit("+y+")",{elem:this,form:p,field:c})},f=new u,p=t(document),h=t(window);f.render(),p.on("reset",r,function(){var e=t(this).attr("lay-filter");setTimeout(function(){f.render(null,e)},50)}),p.on("submit",r,d).on("click","*[lay-submit]",d),e(o,f)}),layui.define("jquery",function(e){"use strict";var t=layui.$,n=layui.hint(),i="layui-tree-enter",a=function(e){this.options=e},o={arrow:["&#xe623;","&#xe625;"],checkbox:["&#xe626;","&#xe627;"],radio:["&#xe62b;","&#xe62a;"],branch:["&#xe622;","&#xe624;"],leaf:"&#xe621;"};a.prototype.init=function(e){var t=this;e.addClass("layui-box layui-tree"),t.options.skin&&e.addClass("layui-tree-skin-"+t.options.skin),t.tree(e),t.on(e)},a.prototype.tree=function(e,n){var i=this,a=i.options,r=n||a.nodes;layui.each(r,function(n,r){var l=r.children&&r.children.length>0,s=t('<ul class="'+(r.spread?"layui-show":"")+'"></ul>'),c=t(["<li "+(r.spread?'data-spread="'+r.spread+'"':"")+">",l?'<i class="layui-icon layui-tree-spread">'+(r.spread?o.arrow[1]:o.arrow[0])+"</i>":"",a.check?'<i class="layui-icon layui-tree-check">'+("checkbox"===a.check?o.checkbox[0]:"radio"===a.check?o.radio[0]:"")+"</i>":"",'<a href="'+(r.href||"javascript:;")+'" '+(a.target&&r.href?'target="'+a.target+'"':"")+'><i class="layui-icon layui-tree-'+(l?"branch":"leaf")+'">'+(l?r.spread?o.branch[1]:o.branch[0]:o.leaf)+"</i><cite>"+(r.name||"未命名")+"</cite></a>","</li>"].join(""));l&&(c.append(s),i.tree(s,r.children)),e.append(c),"function"==typeof a.click&&i.click(c,r),i.spread(c,r),a.drag&&i.drag(c,r)})},a.prototype.click=function(e,t){var n=this,i=n.options;e.children("a").on("click",function(e){layui.stope(e),i.click(t)})},a.prototype.spread=function(e,t){var n=this,i=(n.options,e.children(".layui-tree-spread")),a=e.children("ul"),r=e.children("a"),l=function(){e.data("spread")?(e.data("spread",null),a.removeClass("layui-show"),i.html(o.arrow[0]),r.find(".layui-icon").html(o.branch[0])):(e.data("spread",!0),a.addClass("layui-show"),i.html(o.arrow[1]),r.find(".layui-icon").html(o.branch[1]))};a[0]&&(i.on("click",l),r.on("dblclick",l))},a.prototype.on=function(e){var n=this,a=n.options,o="layui-tree-drag";e.find("i").on("selectstart",function(e){return!1}),a.drag&&t(document).on("mousemove",function(e){var i=n.move;if(i.from){var a=(i.to,t('<div class="layui-box '+o+'"></div>'));e.preventDefault(),t("."+o)[0]||t("body").append(a);var r=t("."+o)[0]?t("."+o):a;r.addClass("layui-show").html(i.from.elem.children("a").html()),r.css({left:e.pageX+10,top:e.pageY+10})}}).on("mouseup",function(){var e=n.move;e.from&&(e.from.elem.children("a").removeClass(i),e.to&&e.to.elem.children("a").removeClass(i),n.move={},t("."+o).remove())})},a.prototype.move={},a.prototype.drag=function(e,n){var a=this,o=(a.options,e.children("a")),r=function(){var o=t(this),r=a.move;r.from&&(r.to={item:n,elem:e},o.addClass(i))};o.on("mousedown",function(){var t=a.move;t.from={item:n,elem:e}}),o.on("mouseenter",r).on("mousemove",r).on("mouseleave",function(){var e=t(this),n=a.move;n.from&&(delete n.to,e.removeClass(i))})},e("tree",function(e){var i=new a(e=e||{}),o=t(e.elem);return o[0]?void i.init(o):n.error("layui.tree 没有找到"+e.elem+"元素")})}),layui.define(["laytpl","laypage","layer","form","util"],function(e){"use strict";var t=layui.$,n=layui.laytpl,i=layui.laypage,a=layui.layer,o=layui.form,r=(layui.util,layui.hint()),l=layui.device(),s={config:{checkName:"LAY_CHECKED",indexName:"LAY_TABLE_INDEX"},cache:{},index:layui.table?layui.table.index+1e4:0,set:function(e){var n=this;return n.config=t.extend({},n.config,e),n},on:function(e,t){return layui.onevent.call(this,d,e,t)}},c=function(){var e=this,t=e.config,n=t.id||t.index;return n&&(c.that[n]=e,c.config[n]=t),{reload:function(t){e.reload.call(e,t)},setColsWidth:function(){e.setColsWidth.call(e)},resize:function(){e.resize.call(e)},config:t}},u=function(e){var t=c.config[e];return t||r.error("The ID option was not found in the table instance"),t||null},d="table",f=".layui-table",p="layui-hide",h="layui-none",y="layui-table-view",m=".layui-table-tool",v=".layui-table-box",g=".layui-table-init",b=".layui-table-header",x=".layui-table-body",w=".layui-table-main",k=".layui-table-fixed",C=".layui-table-fixed-l",T=".layui-table-fixed-r",E=".layui-table-total",D=".layui-table-page",S=".layui-table-sort",L="layui-table-edit",A="layui-table-hover",j=function(e){var t='{{#if(item2.colspan){}} colspan="{{item2.colspan}}"{{#} if(item2.rowspan){}} rowspan="{{item2.rowspan}}"{{#}}}';return e=e||{},['<table cellspacing="0" cellpadding="0" border="0" class="layui-table" ','{{# if(d.data.skin){ }}lay-skin="{{d.data.skin}}"{{# } }} {{# if(d.data.size){ }}lay-size="{{d.data.size}}"{{# } }} {{# if(d.data.even){ }}lay-even{{# } }}>',"<thead>","{{# layui.each(d.data.cols, function(i1, item1){ }}","<tr>","{{# layui.each(item1, function(i2, item2){ }}",'{{# if(item2.fixed && item2.fixed !== "right"){ left = true; } }}','{{# if(item2.fixed === "right"){ right = true; } }}',e.fixed&&"right"!==e.fixed?'{{# if(item2.fixed && item2.fixed !== "right"){ }}':"right"===e.fixed?'{{# if(item2.fixed === "right"){ }}':"","{{# var isSort = !(item2.colGroup) && item2.sort; }}",'<th data-field="{{ item2.field||i2 }}" data-key="{{d.index}}-{{i1}}-{{i2}}" {{# if( item2.parentKey){ }}data-parentkey="{{ item2.parentKey }}"{{# } }} {{# if(item2.minWidth){ }}data-minwidth="{{item2.minWidth}}"{{# } }} '+t+' {{# if(item2.unresize || item2.colGroup){ }}data-unresize="true"{{# } }} class="{{# if(item2.hide){ }}layui-hide{{# } }}{{# if(isSort){ }} layui-unselect{{# } }}{{# if(!item2.field){ }} layui-table-col-special{{# } }}">','<div class="layui-table-cell laytable-cell-',"{{# if(item2.colGroup){ }}","group","{{# } else { }}","{{d.index}}-{{i1}}-{{i2}}",'{{# if(item2.type !== "normal"){ }}'," laytable-cell-{{ item2.type }}","{{# } }}","{{# } }}",'" {{#if(item2.align){}}align="{{item2.align}}"{{#}}}>','{{# if(item2.type === "checkbox"){ }}','<input type="checkbox" name="layTableCheckbox" lay-skin="primary" lay-filter="layTableAllChoose" {{# if(item2[d.data.checkName]){ }}checked{{# }; }}>',"{{# } else { }}",'<span>{{item2.title||""}}</span>',"{{# if(isSort){ }}",'<span class="layui-table-sort layui-inline"><i class="layui-edge layui-table-sort-asc" title="升序"></i><i class="layui-edge layui-table-sort-desc" title="降序"></i></span>',"{{# } }}","{{# } }}","</div>","</th>",e.fixed?"{{# }; }}":"","{{# }); }}","</tr>","{{# }); }}","</thead>","</table>"].join("")},N=['<table cellspacing="0" cellpadding="0" border="0" class="layui-table" ','{{# if(d.data.skin){ }}lay-skin="{{d.data.skin}}"{{# } }} {{# if(d.data.size){ }}lay-size="{{d.data.size}}"{{# } }} {{# if(d.data.even){ }}lay-even{{# } }}>',"<tbody></tbody>","</table>"].join(""),M=['<div class="layui-form layui-border-box {{d.VIEW_CLASS}}" lay-filter="LAY-table-{{d.index}}" lay-id="{{ d.data.id }}" style="{{# if(d.data.width){ }}width:{{d.data.width}}px;{{# } }} {{# if(d.data.height){ }}height:{{d.data.height}}px;{{# } }}">',"{{# if(d.data.toolbar){ }}",'<div class="layui-table-tool">','<div class="layui-table-tool-temp"></div>','<div class="layui-table-tool-self"></div>',"</div>","{{# } }}",'<div class="layui-table-box">',"{{# if(d.data.loading){ }}",'<div class="layui-table-init" style="background-color: #fff;">','<i class="layui-icon layui-icon-loading layui-icon"></i>',"</div>","{{# } }}","{{# var left, right; }}",'<div class="layui-table-header">',j(),"</div>",'<div class="layui-table-body layui-table-main">',N,"</div>","{{# if(left){ }}",'<div class="layui-table-fixed layui-table-fixed-l">','<div class="layui-table-header">',j({fixed:!0}),"</div>",'<div class="layui-table-body">',N,"</div>","</div>","{{# }; }}","{{# if(right){ }}",'<div class="layui-table-fixed layui-table-fixed-r">','<div class="layui-table-header">',j({fixed:"right"}),'<div class="layui-table-mend"></div>',"</div>",'<div class="layui-table-body">',N,"</div>","</div>","{{# }; }}","</div>","{{# if(d.data.totalRow){ }}",'<div class="layui-table-total">','<table cellspacing="0" cellpadding="0" border="0" class="layui-table" ','{{# if(d.data.skin){ }}lay-skin="{{d.data.skin}}"{{# } }} {{# if(d.data.size){ }}lay-size="{{d.data.size}}"{{# } }} {{# if(d.data.even){ }}lay-even{{# } }}>','<tbody><tr><td><div class="layui-table-cell" style="visibility: hidden;">Total</div></td></tr></tbody>',"</table>","</div>","{{# } }}","{{# if(d.data.page){ }}",'<div class="layui-table-page">','<div id="layui-table-page{{d.index}}"></div>',"</div>","{{# } }}","<style>","{{# layui.each(d.data.cols, function(i1, item1){","layui.each(item1, function(i2, item2){ }}",".laytable-cell-{{d.index}}-{{i1}}-{{i2}}{ ","{{# if(item2.width){ }}","width: {{item2.width}}px;","{{# } }}"," }","{{# });","}); }}","</style>","</div>"].join(""),H=t(window),F=t(document),I=function(e){var n=this;n.index=++s.index,n.config=t.extend({},n.config,s.config,e),n.render()};I.prototype.config={limit:10,loading:!0,cellMinWidth:60,defaultToolbar:["filter","exports","print"],autoSort:!0,text:{none:"无数据"}},I.prototype.render=function(){var e=this,i=e.config;if(i.elem=t(i.elem),i.where=i.where||{},i.id=i.id||i.elem.attr("id")||e.index,i.request=t.extend({pageName:"page",limitName:"limit"},i.request),i.response=t.extend({statusName:"code",statusCode:0,msgName:"msg",dataName:"data",countName:"count"},i.response),"object"==typeof i.page&&(i.limit=i.page.limit||i.limit,i.limits=i.page.limits||i.limits,e.page=i.page.curr=i.page.curr||1,delete i.page.elem,delete i.page.jump),!i.elem[0])return e;i.height&&/^full-\d+$/.test(i.height)&&(e.fullHeightGap=i.height.split("-")[1],i.height=H.height()-e.fullHeightGap),e.setInit();var a=i.elem,o=a.next("."+y),r=e.elem=t(n(M).render({VIEW_CLASS:y,data:i,index:e.index}));if(i.index=e.index,o[0]&&o.remove(),a.after(r),e.layTool=r.find(m),e.layBox=r.find(v),e.layHeader=r.find(b),e.layMain=r.find(w),e.layBody=r.find(x),e.layFixed=r.find(k),e.layFixLeft=r.find(C),e.layFixRight=r.find(T),e.layTotal=r.find(E),e.layPage=r.find(D),e.renderToolbar(),e.fullSize(),i.cols.length>1){var l=e.layFixed.find(b).find("th");l.height(e.layHeader.height()-1-parseFloat(l.css("padding-top"))-parseFloat(l.css("padding-bottom")))}e.pullData(e.page),e.events()},I.prototype.initOpts=function(e){var t=this,n=(t.config,{checkbox:48,radio:48,space:15,numbers:40});e.checkbox&&(e.type="checkbox"),e.space&&(e.type="space"),e.type||(e.type="normal"),"normal"!==e.type&&(e.unresize=!0,e.width=e.width||n[e.type])},I.prototype.setInit=function(e){var t=this,n=t.config;return n.clientWidth=n.width||function(){var e=function(t){var i,a;t=t||n.elem.parent(),i=t.width();try{a="none"===t.css("display")}catch(e){}return!t[0]||i&&!a?i:e(t.parent())};return e()}(),"width"===e?n.clientWidth:void layui.each(n.cols,function(e,i){layui.each(i,function(a,o){if(o){if(o.key=e+"-"+a,o.hide=o.hide||!1,o.colGroup||o.colspan>1){var r=0;layui.each(n.cols[e+1],function(t,n){n.HAS_PARENT||r>1&&r==o.colspan||(n.HAS_PARENT=!0,n.parentKey=e+"-"+a,r+=parseInt(n.colspan>1?n.colspan:1))}),o.colGroup=!0}t.initOpts(o)}else i.splice(a,1)})})},I.prototype.renderToolbar=function(){var e=this,i=e.config,a=['<div class="layui-inline" lay-event="add"><i class="layui-icon layui-icon-add-1"></i></div>','<div class="layui-inline" lay-event="update"><i class="layui-icon layui-icon-edit"></i></div>','<div class="layui-inline" lay-event="delete"><i class="layui-icon layui-icon-delete"></i></div>'].join(""),o=e.layTool.find(".layui-table-tool-temp");if("default"===i.toolbar)o.html(a);else if("string"==typeof i.toolbar){var r=t(i.toolbar).html()||"";r&&o.html(n(r).render(i))}var l={filter:{title:"筛选列",layEvent:"LAYTABLE_COLS",icon:"layui-icon-cols"},exports:{title:"导出",layEvent:"LAYTABLE_EXPORT",icon:"layui-icon-export"},print:{title:"打印",layEvent:"LAYTABLE_PRINT",icon:"layui-icon-print"}},s=[];"object"==typeof i.defaultToolbar&&layui.each(i.defaultToolbar,function(e,t){var n=l[t];n&&s.push('<div class="layui-inline" title="'+n.title+'" lay-event="'+n.layEvent+'"><i class="layui-icon '+n.icon+'"></i></div>')}),e.layTool.find(".layui-table-tool-self").html(s.join(""))},I.prototype.setParentCol=function(e,t){var n=this,i=n.config,a=n.layHeader.find('th[data-key="'+i.index+"-"+t+'"]'),o=parseInt(a.attr("colspan"))||0;if(a[0]){var r=t.split("-"),l=i.cols[r[0]][r[1]];e?o--:o++,a.attr("colspan",o),a[o<1?"addClass":"removeClass"](p),l.colspan=o,l.hide=o<1;var s=a.data("parentkey");s&&n.setParentCol(e,s)}},I.prototype.setColsPatch=function(){var e=this,t=e.config;layui.each(t.cols,function(t,n){layui.each(n,function(t,n){n.hide&&e.setParentCol(n.hide,n.parentKey)})})},I.prototype.setColsWidth=function(){var e=this,t=e.config,n=0,i=0,a=0,o=0,r=e.setInit("width");e.eachCols(function(e,t){t.hide||n++}),r=r-("line"===t.skin||"nob"===t.skin?2:n+1)-e.getScrollWidth(e.layMain[0])-1;var l=function(e){layui.each(t.cols,function(n,l){layui.each(l,function(n,s){var c=0,u=s.minWidth||t.cellMinWidth;return s?void(s.colGroup||s.hide||(e?a&&a<u&&(i--,c=u):(c=s.width||0,/\d+%$/.test(c)?(c=Math.floor(parseFloat(c)/100*r),c<u&&(c=u)):c||(s.width=c=0,i++)),s.hide&&(c=0),o+=c)):void l.splice(n,1)})}),r>o&&i&&(a=(r-o)/i)};l(),l(!0),e.autoColNums=i,e.eachCols(function(n,i){var o=i.minWidth||t.cellMinWidth;i.colGroup||i.hide||(0===i.width?e.getCssRule(t.index+"-"+i.key,function(e){e.style.width=Math.floor(a>=o?a:o)+"px"}):/\d+%$/.test(i.width)&&e.getCssRule(t.index+"-"+i.key,function(e){e.style.width=Math.floor(parseFloat(i.width)/100*r)+"px"}))});var s=e.layMain.width()-e.getScrollWidth(e.layMain[0])-e.layMain.children("table").outerWidth();if(e.autoColNums&&s>=-n&&s<=n){var c=function(t){var n;return t=t||e.layHeader.eq(0).find("thead th:last-child"),n=t.data("field"),!n&&t.prev()[0]?c(t.prev()):t},u=c(),d=u.data("key");e.getCssRule(d,function(t){var n=t.style.width||u.outerWidth();t.style.width=parseFloat(n)+s+"px",e.layMain.height()-e.layMain.prop("clientHeight")>0&&(t.style.width=parseFloat(t.style.width)-1+"px")})}e.loading(!0)},I.prototype.resize=function(){var e=this;e.fullSize(),e.setColsWidth(),e.scrollPatch()},I.prototype.reload=function(e){var n=this;n.config.data&&n.config.data.constructor===Array&&delete n.config.data,n.config=t.extend({},n.config,e),n.render()},I.prototype.page=1,I.prototype.pullData=function(e){var n=this,i=n.config,a=i.request,o=i.response,r=function(){"object"==typeof i.initSort&&n.sort(i.initSort.field,i.initSort.type)};if(n.startTime=(new Date).getTime(),i.url){var l={};l[a.pageName]=e,l[a.limitName]=i.limit;var s=t.extend(l,i.where);i.contentType&&0==i.contentType.indexOf("application/json")&&(s=JSON.stringify(s)),t.ajax({type:i.method||"get",url:i.url,contentType:i.contentType,data:s,dataType:"json",headers:i.headers||{},success:function(t){"function"==typeof i.parseData&&(t=i.parseData(t)||t),t[o.statusName]!=o.statusCode?(n.renderForm(),n.layMain.html('<div class="'+h+'">'+(t[o.msgName]||"返回的数据不符合规范，正确的成功状态码 ("+o.statusName+") 应为："+o.statusCode)+"</div>")):(n.renderData(t,e,t[o.countName]),r(),i.time=(new Date).getTime()-n.startTime+" ms"),n.setColsWidth(),"function"==typeof i.done&&i.done(t,e,t[o.countName])},error:function(e,t){n.layMain.html('<div class="'+h+'">数据接口请求异常：'+t+"</div>"),n.renderForm(),n.setColsWidth()}})}else if(i.data&&i.data.constructor===Array){var c={},u=e*i.limit-i.limit;c[o.dataName]=i.data.concat().splice(u,i.limit),c[o.countName]=i.data.length,n.renderData(c,e,i.data.length),r(),n.setColsWidth(),"function"==typeof i.done&&i.done(c,e,c[o.countName])}},I.prototype.eachCols=function(e){var t=this;return s.eachCols(null,e,t.config.cols),t},I.prototype.renderData=function(e,o,r,l){var c=this,u=c.config,d=e[u.response.dataName]||[],f=[],y=[],m=[],v=function(){var e;return!l&&c.sortKey?c.sort(c.sortKey.field,c.sortKey.sort,!0):(layui.each(d,function(i,a){var r=[],d=[],h=[],v=i+u.limit*(o-1)+1;0!==a.length&&(l||(a[s.config.indexName]=i),c.eachCols(function(o,l){var c=l.field||o,f=u.index+"-"+l.key,y=a[c];if(null!=y||(y=""),!l.colGroup){var m=['<td data-field="'+c+'" data-key="'+f+'" '+function(){var e=[];return l.edit&&e.push('data-edit="'+l.edit+'"'),l.align&&e.push('align="'+l.align+'"'),l.templet&&e.push('data-content="'+y+'"'),l.toolbar&&e.push('data-off="true"'),l.event&&e.push('lay-event="'+l.event+'"'),l.style&&e.push('style="'+l.style+'"'),l.minWidth&&e.push('data-minwidth="'+l.minWidth+'"'),e.join(" ")}()+' class="'+function(){var e=[];return l.hide&&e.push(p),l.field||e.push("layui-table-col-special"),e.join(" ")}()+'">','<div class="layui-table-cell laytable-cell-'+("normal"===l.type?f:f+" laytable-cell-"+l.type)+'">'+function(){var o=t.extend(!0,{LAY_INDEX:v},a),r=s.config.checkName;switch(l.type){case"checkbox":return'<input type="checkbox" name="layTableCheckbox" lay-skin="primary" '+(l[r]?(a[r]=l[r],l[r]?"checked":""):o[r]?"checked":"")+">";case"radio":return o[r]&&(e=i),'<input type="radio" name="layTableRadio_'+u.index+'" '+(o[r]?"checked":"")+' lay-type="layTableRadio">';case"numbers":return v}return l.toolbar?n(t(l.toolbar).html()||"").render(o):l.templet?"function"==typeof l.templet?l.templet(o):n(t(l.templet).html()||String(y)).render(o):y}(),"</div></td>"].join("");r.push(m),l.fixed&&"right"!==l.fixed&&d.push(m),"right"===l.fixed&&h.push(m)}}),f.push('<tr data-index="'+i+'">'+r.join("")+"</tr>"),y.push('<tr data-index="'+i+'">'+d.join("")+"</tr>"),m.push('<tr data-index="'+i+'">'+h.join("")+"</tr>"))}),c.layBody.scrollTop(0),c.layMain.find("."+h).remove(),c.layMain.find("tbody").html(f.join("")),c.layFixLeft.find("tbody").html(y.join("")),c.layFixRight.find("tbody").html(m.join("")),c.renderForm(),"number"==typeof e&&c.setThisRowChecked(e),c.syncCheckAll(),c.haveInit?c.scrollPatch():setTimeout(function(){c.scrollPatch()},50),c.haveInit=!0,a.close(c.tipsIndex),u.HAS_SET_COLS_PATCH||c.setColsPatch(),void(u.HAS_SET_COLS_PATCH=!0))};return c.key=u.id||u.index,s.cache[c.key]=d,c.layPage[0==r||0===d.length&&1==o?"addClass":"removeClass"](p),l?v():0===d.length?(c.renderForm(),c.layFixed.remove(),c.layMain.find("tbody").html(""),c.layMain.find("."+h).remove(),c.layMain.append('<div class="'+h+'">'+u.text.none+"</div>")):(v(),c.renderTotal(d),void(u.page&&(u.page=t.extend({elem:"layui-table-page"+u.index,count:r,limit:u.limit,limits:u.limits||[10,20,30,40,50,60,70,80,90],groups:3,layout:["prev","page","next","skip","count","limit"],prev:'<i class="layui-icon">&#xe603;</i>',next:'<i class="layui-icon">&#xe602;</i>',jump:function(e,t){t||(c.page=e.curr,u.limit=e.limit,c.loading(),c.pullData(e.curr))}},u.page),u.page.count=r,i.render(u.page))))},I.prototype.renderTotal=function(e){var t=this,n=t.config,i={};if(n.totalRow){layui.each(e,function(e,n){0!==n.length&&t.eachCols(function(e,t){var a=t.field||e,o=n[a];t.totalRow&&(i[a]=(i[a]||0)+(parseFloat(o)||0))})});var a=[];t.eachCols(function(e,t){var o=t.field||e,r=['<td data-field="'+o+'" data-key="'+n.index+"-"+t.key+'" '+function(){var e=[];return t.align&&e.push('align="'+t.align+'"'),t.style&&e.push('style="'+t.style+'"'),t.minWidth&&e.push('data-minwidth="'+t.minWidth+'"'),e.join(" ")}()+' class="'+function(){var e=[];return t.hide&&e.push(p),t.field||e.push("layui-table-col-special"),e.join(" ")}()+'">','<div class="layui-table-cell laytable-cell-'+function(){var e=n.index+"-"+t.key;return"normal"===t.type?e:e+" laytable-cell-"+t.type}()+'">'+function(){var e=t.totalRowText||"";return t.totalRow&&parseFloat(i[o]).toFixed(2)||e}(),"</div></td>"].join("");a.push(r)}),t.layTotal.find("tbody").html("<tr>"+a.join("")+"</tr>")}},I.prototype.getColElem=function(e,t){var n=this,i=n.config;return e.eq(0).find(".laytable-cell-"+i.index+"-"+t+":eq(0)")},I.prototype.renderForm=function(e){o.render(e,"LAY-table-"+this.index)},I.prototype.setThisRowChecked=function(e){var t=this,n=(t.config,"layui-table-click"),i=t.layBody.find('tr[data-index="'+e+'"]');i.addClass(n).siblings("tr").removeClass(n)},I.prototype.sort=function(e,n,i,a){var o,l=this,c={},u=l.config,f=u.elem.attr("lay-filter"),p=s.cache[l.key];"string"==typeof e&&l.layHeader.find("th").each(function(n,i){var a=t(this),o=a.data("field");if(o===e)return e=a,h=o,!1});try{var h=h||e.data("field"),y=e.data("key");if(l.sortKey&&!i&&h===l.sortKey.field&&n===l.sortKey.sort)return;var m=l.layHeader.find("th .laytable-cell-"+y).find(S);l.layHeader.find("th").find(S).removeAttr("lay-sort"),m.attr("lay-sort",n||null),l.layFixed.find("th")}catch(e){return r.error("Table modules: Did not match to field")}l.sortKey={field:h,sort:n},u.autoSort&&("asc"===n?o=layui.sort(p,h):"desc"===n?o=layui.sort(p,h,!0):(o=layui.sort(p,s.config.indexName),delete l.sortKey)),c[u.response.dataName]=o||p,l.renderData(c,l.page,l.count,!0),a&&layui.event.call(e,d,"sort("+f+")",{field:h,type:n})},I.prototype.loading=function(e){var n=this,i=n.config;i.loading&&(e?(n.layInit&&n.layInit.remove(),delete n.layInit,n.layBox.find(g).remove()):(n.layInit=t(['<div class="layui-table-init">','<i class="layui-icon layui-icon-loading layui-icon"></i>',"</div>"].join("")),n.layBox.append(n.layInit)))},I.prototype.setCheckData=function(e,t){var n=this,i=n.config,a=s.cache[n.key];a[e]&&a[e].constructor!==Array&&(a[e][i.checkName]=t)},I.prototype.syncCheckAll=function(){var e=this,t=e.config,n=e.layHeader.find('input[name="layTableCheckbox"]'),i=function(n){return e.eachCols(function(e,i){"checkbox"===i.type&&(i[t.checkName]=n)}),n};n[0]&&(s.checkStatus(e.key).isAll?(n[0].checked||(n.prop("checked",!0),e.renderForm("checkbox")),i(!0)):(n[0].checked&&(n.prop("checked",!1),e.renderForm("checkbox")),i(!1)))},I.prototype.getCssRule=function(e,t){var n=this,i=n.elem.find("style")[0],a=i.sheet||i.styleSheet||{},o=a.cssRules||a.rules;layui.each(o,function(n,i){if(i.selectorText===".laytable-cell-"+e)return t(i),!0})},I.prototype.fullSize=function(){var e,t=this,n=t.config,i=n.height;t.fullHeightGap&&(i=H.height()-t.fullHeightGap,i<135&&(i=135),t.elem.css("height",i)),i&&(e=parseFloat(i)-(t.layHeader.outerHeight()||38),n.toolbar&&(e-=t.layTool.outerHeight()||50),n.totalRow&&(e-=t.layTotal.outerHeight()||40),n.page&&(e=e-(t.layPage.outerHeight()||41)-2),t.layMain.css("height",e))},I.prototype.getScrollWidth=function(e){var t=0;return e?t=e.offsetWidth-e.clientWidth:(e=document.createElement("div"),e.style.width="100px",e.style.height="100px",e.style.overflowY="scroll",document.body.appendChild(e),t=e.offsetWidth-e.clientWidth,document.body.removeChild(e)),t},I.prototype.scrollPatch=function(){var e=this,n=e.layMain.children("table"),i=e.layMain.width()-e.layMain.prop("clientWidth"),a=e.layMain.height()-e.layMain.prop("clientHeight"),o=(e.getScrollWidth(e.layMain[0]),n.outerWidth()-e.layMain.width()),r=function(e){if(i&&a){if(e=e.eq(0),!e.find(".layui-table-patch")[0]){var n=t('<th class="layui-table-patch"><div class="layui-table-cell"></div></th>');n.find("div").css({width:i}),e.find("tr").append(n)}}else e.find(".layui-table-patch").remove()};r(e.layHeader),r(e.layTotal);var l=e.layMain.height(),s=l-a;e.layFixed.find(x).css("height",n.height()>=s?s:"auto"),e.layFixRight[o>0?"removeClass":"addClass"](p),e.layFixRight.css("right",i-1)},I.prototype.events=function(){var e,i=this,r=i.config,c=t("body"),u={},f=i.layHeader.find("th"),h=".layui-table-cell",y=r.elem.attr("lay-filter");i.layTool.on("click","*[lay-event]",function(e){var n=t(this),c=n.attr("lay-event"),u=function(e){var a=t(e.list),o=t('<ul class="layui-table-tool-panel"></ul>');o.html(a),r.height&&o.css("max-height",r.height-(i.layTool.outerHeight()||50)),n.find(".layui-table-tool-panel")[0]||n.append(o),i.renderForm(),o.on("click",function(e){layui.stope(e)}),e.done&&e.done(o,a)};switch(layui.stope(e),
F.trigger("table.tool.panel.remove"),a.close(i.tipsIndex),c){case"LAYTABLE_COLS":u({list:function(){var e=[];return i.eachCols(function(t,n){n.field&&"normal"==n.type&&e.push('<li><input type="checkbox" name="'+n.field+'" data-key="'+n.key+'" data-parentkey="'+(n.parentKey||"")+'" lay-skin="primary" '+(n.hide?"":"checked")+' title="'+(n.title||n.field)+'" lay-filter="LAY_TABLE_TOOL_COLS"></li>')}),e.join("")}(),done:function(){o.on("checkbox(LAY_TABLE_TOOL_COLS)",function(e){var n=t(e.elem),a=this.checked,o=n.data("key"),l=n.data("parentkey");layui.each(r.cols,function(e,t){layui.each(t,function(t,n){if(e+"-"+t===o){var s=n.hide;n.hide=!a,i.elem.find('*[data-key="'+r.index+"-"+o+'"]')[a?"removeClass":"addClass"](p),s!=n.hide&&i.setParentCol(!a,l),i.resize()}})})})}});break;case"LAYTABLE_EXPORT":l.ie?a.tips("导出功能不支持 IE，请用 Chrome 等高级浏览器导出",this,{tips:3}):u({list:['<li data-type="csv">导出到 Csv 文件</li>','<li data-type="xls">导出到 Excel 文件</li>'].join(""),done:function(e,n){n.on("click",function(){var e=t(this).data("type");s.exportFile(r.id,null,e)})}});break;case"LAYTABLE_PRINT":var f=window.open("打印窗口","_blank"),h=["<style>","body{font-size: 12px; color: #666;}","table{width: 100%; border-collapse: collapse; border-spacing: 0;}","th,td{line-height: 20px; padding: 9px 15px; border: 1px solid #ccc; text-align: left; font-size: 12px; color: #666;}","a{color: #666; text-decoration:none;}","*.layui-hide{display: none}","</style>"].join(""),m=t(i.layHeader.html());m.append(i.layMain.find("table").html()),m.find("th.layui-table-patch").remove(),m.find(".layui-table-col-special").remove(),f.document.write(h+m.prop("outerHTML")),f.document.close(),f.print(),f.close()}layui.event.call(this,d,"toolbar("+y+")",t.extend({event:c,config:r},{}))}),f.on("mousemove",function(e){var n=t(this),i=n.offset().left,a=e.clientX-i;n.data("unresize")||u.resizeStart||(u.allowResize=n.width()-a<=10,c.css("cursor",u.allowResize?"col-resize":""))}).on("mouseleave",function(){t(this),u.resizeStart||c.css("cursor","")}).on("mousedown",function(e){var n=t(this);if(u.allowResize){var a=n.data("key");e.preventDefault(),u.resizeStart=!0,u.offset=[e.clientX,e.clientY],i.getCssRule(a,function(e){var t=e.style.width||n.outerWidth();u.rule=e,u.ruleWidth=parseFloat(t),u.minWidth=n.data("minwidth")||r.cellMinWidth})}}),F.on("mousemove",function(t){if(u.resizeStart){if(t.preventDefault(),u.rule){var n=u.ruleWidth+t.clientX-u.offset[0];n<u.minWidth&&(n=u.minWidth),u.rule.style.width=n+"px",a.close(i.tipsIndex)}e=1}}).on("mouseup",function(t){u.resizeStart&&(u={},c.css("cursor",""),i.scrollPatch()),2===e&&(e=null)}),f.on("click",function(n){var a,o=t(this),r=o.find(S),l=r.attr("lay-sort");return r[0]&&1!==e?(a="asc"===l?"desc":"desc"===l?null:"asc",void i.sort(o,a,null,!0)):e=2}).find(S+" .layui-edge ").on("click",function(e){var n=t(this),a=n.index(),o=n.parents("th").eq(0).data("field");layui.stope(e),0===a?i.sort(o,"asc",null,!0):i.sort(o,"desc",null,!0)});var m=function(e){var a=t(this),o=a.parents("tr").eq(0).data("index"),r=i.layBody.find('tr[data-index="'+o+'"]'),l=s.cache[i.key][o];return t.extend({tr:r,data:s.clearCacheKey(l),del:function(){s.cache[i.key][o]=[],r.remove(),i.scrollPatch()},update:function(e){e=e||{},layui.each(e,function(e,a){if(e in l){var o,s=r.children('td[data-field="'+e+'"]');l[e]=a,i.eachCols(function(t,n){n.field==e&&n.templet&&(o=n.templet)}),s.children(h).html(o?"function"==typeof o?o(l):n(t(o).html()||a).render(l):a),s.data("content",a)}})}},e)};i.elem.on("click",'input[name="layTableCheckbox"]+',function(){var e=t(this).prev(),n=i.layBody.find('input[name="layTableCheckbox"]'),a=e.parents("tr").eq(0).data("index"),o=e[0].checked,r="layTableAllChoose"===e.attr("lay-filter");r?(n.each(function(e,t){t.checked=o,i.setCheckData(e,o)}),i.syncCheckAll(),i.renderForm("checkbox")):(i.setCheckData(a,o),i.syncCheckAll()),layui.event.call(e[0],d,"checkbox("+y+")",m.call(e[0],{checked:o,type:r?"all":"one"}))}),i.elem.on("click",'input[lay-type="layTableRadio"]+',function(){var e=t(this).prev(),n=e[0].checked,a=s.cache[i.key],o=e.parents("tr").eq(0).data("index");layui.each(a,function(e,t){o===e?t.LAY_CHECKED=!0:delete t.LAY_CHECKED}),i.setThisRowChecked(o),layui.event.call(this,d,"radio("+y+")",m.call(this,{checked:n}))}),i.layBody.on("mouseenter","tr",function(){var e=t(this),n=e.index();i.layBody.find("tr:eq("+n+")").addClass(A)}).on("mouseleave","tr",function(){var e=t(this),n=e.index();i.layBody.find("tr:eq("+n+")").removeClass(A)}).on("click","tr",function(){v.call(this,"row")}).on("dblclick","tr",function(){v.call(this,"rowDouble")});var v=function(e){var n=t(this);layui.event.call(this,d,e+"("+y+")",m.call(n.children("td")[0]))};i.layBody.on("change","."+L,function(){var e=t(this),n=this.value,a=e.parent().data("field"),o=e.parents("tr").eq(0).data("index"),r=s.cache[i.key][o];r[a]=n,layui.event.call(this,d,"edit("+y+")",m.call(this,{value:n,field:a}))}).on("blur","."+L,function(){var e,a=t(this),o=a.parent().data("field"),r=a.parents("tr").eq(0).data("index"),l=s.cache[i.key][r];i.eachCols(function(t,n){n.field==o&&n.templet&&(e=n.templet)}),a.siblings(h).html(function(i){return e?function(){return"function"==typeof e?e(l):n(t(e).html()||this.value).render(l)}():i}(this.value)),a.parent().data("content",this.value),a.remove()}),i.layBody.on("click","td",function(e){var n=t(this),i=(n.data("field"),n.data("edit")),a=n.children(h);if(!n.data("off")&&i){var o=t('<input class="layui-input '+L+'">');return o[0].value=n.data("content")||a.text(),n.find("."+L)[0]||n.append(o),o.focus(),void layui.stope(e)}}).on("mouseenter","td",function(){b.call(this)}).on("mouseleave","td",function(){b.call(this,"hide")});var g="layui-table-grid-down",b=function(e){var n=t(this),i=n.children(h);if(e)n.find(".layui-table-grid-down").remove();else if(i.prop("scrollWidth")>i.outerWidth()){if(i.find("."+g)[0])return;n.append('<div class="'+g+'"><i class="layui-icon layui-icon-down"></i></div>')}};i.layBody.on("click","."+g,function(e){var n=t(this),o=n.parent(),s=o.children(h);i.tipsIndex=a.tips(['<div class="layui-table-tips-main" style="margin-top: -'+(s.height()+16)+"px;"+("sm"===r.size?"padding: 4px 15px; font-size: 12px;":"lg"===r.size?"padding: 14px 15px;":"")+'">',s.html(),"</div>",'<i class="layui-icon layui-table-tips-c layui-icon-close"></i>'].join(""),s[0],{tips:[3,""],time:-1,anim:-1,maxWidth:l.ios||l.android?300:i.elem.width()/2,isOutAnim:!1,skin:"layui-table-tips",success:function(e,t){e.find(".layui-table-tips-c").on("click",function(){a.close(t)})}}),layui.stope(e)}),i.layBody.on("click","*[lay-event]",function(){var e=t(this),n=e.parents("tr").eq(0).data("index");layui.event.call(this,d,"tool("+y+")",m.call(this,{event:e.attr("lay-event")})),i.setThisRowChecked(n)}),i.layMain.on("scroll",function(){var e=t(this),n=e.scrollLeft(),o=e.scrollTop();i.layHeader.scrollLeft(n),i.layTotal.scrollLeft(n),i.layFixed.find(x).scrollTop(o),a.close(i.tipsIndex)}),F.on("click",function(){F.trigger("table.remove.tool.panel")}),F.on("table.remove.tool.panel",function(){t(".layui-table-tool-panel").remove()}),H.on("resize",function(){i.resize()})},s.init=function(e,n){n=n||{};var i=this,a=t(e?'table[lay-filter="'+e+'"]':f+"[lay-data]"),o="Table element property lay-data configuration item has a syntax error: ";return a.each(function(){var i=t(this),a=i.attr("lay-data");try{a=new Function("return "+a)()}catch(e){r.error(o+a)}var l=[],c=t.extend({elem:this,cols:[],data:[],skin:i.attr("lay-skin"),size:i.attr("lay-size"),even:"string"==typeof i.attr("lay-even")},s.config,n,a);e&&i.hide(),i.find("thead>tr").each(function(e){c.cols[e]=[],t(this).children().each(function(n){var i=t(this),a=i.attr("lay-data");try{a=new Function("return "+a)()}catch(e){return r.error(o+a)}var s=t.extend({title:i.text(),colspan:i.attr("colspan")||0,rowspan:i.attr("rowspan")||0},a);s.colspan<2&&l.push(s),c.cols[e].push(s)})}),i.find("tbody>tr").each(function(e){var n=t(this),i={};n.children("td").each(function(e,n){var a=t(this),o=a.data("field");if(o)return i[o]=a.html()}),layui.each(l,function(e,t){var a=n.children("td").eq(e);i[t.field]=a.html()}),c.data[e]=i}),s.render(c)}),i},c.that={},c.config={},s.eachCols=function(e,n,i){var a=c.config[e]||{},o=[],r=0;i=t.extend(!0,[],i||a.cols),layui.each(i,function(e,t){layui.each(t,function(t,n){if(n.colGroup){var a=0;r++,n.CHILD_COLS=[],layui.each(i[e+1],function(e,t){t.PARENT_COL_INDEX||a>1&&a==n.colspan||(t.PARENT_COL_INDEX=r,n.CHILD_COLS.push(t),a+=parseInt(t.colspan>1?t.colspan:1))})}n.PARENT_COL_INDEX||o.push(n)})});var l=function(e){layui.each(e||o,function(e,t){return t.CHILD_COLS?l(t.CHILD_COLS):void("function"==typeof n&&n(e,t))})};l()},s.checkStatus=function(e){var t=0,n=0,i=[],a=s.cache[e]||[];return layui.each(a,function(e,a){return a.constructor===Array?void n++:void(a[s.config.checkName]&&(t++,i.push(s.clearCacheKey(a))))}),{data:i,isAll:!!a.length&&t===a.length-n}},s.exportFile=function(e,t,n){t=t||s.clearCacheKey(s.cache[e]),n=n||"csv";var i=c.config[e]||{},a={csv:"text/csv",xls:"application/vnd.ms-excel"}[n],o=document.createElement("a");return l.ie?r.error("IE_NOT_SUPPORT_EXPORTS"):(o.href="data:"+a+";charset=utf-8,\ufeff"+encodeURIComponent(function(){var n=[],i=[];return layui.each(t,function(t,a){var o=[];"object"==typeof e?(layui.each(e,function(e,i){0==t&&n.push(i||"")}),layui.each(s.clearCacheKey(a),function(e,t){o.push(t)})):s.eachCols(e,function(e,i){i.field&&"normal"==i.type&&!i.hide&&(0==t&&n.push(i.title||""),o.push(a[i.field]))}),i.push(o.join(","))}),n.join(",")+"\r\n"+i.join("\r\n")}()),o.download=(i.title||"table_"+(i.index||""))+"."+n,document.body.appendChild(o),o.click(),void document.body.removeChild(o))},s.resize=function(e){if(e){var t=u(e);if(!t)return;c.that[e].resize()}else layui.each(c.that,function(){this.resize()})},s.reload=function(e,n){n=n||{};var i=u(e);if(i)return n.data&&n.data.constructor===Array&&delete i.data,s.render(t.extend(!0,{},i,n))},s.render=function(e){var t=new I(e);return c.call(t)},s.clearCacheKey=function(e){return e=t.extend({},e),delete e[s.config.checkName],delete e[s.config.indexName],e},s.init(),e(d,s)}),layui.define("jquery",function(e){"use strict";var t=layui.$,n=(layui.hint(),layui.device(),{config:{},set:function(e){var n=this;return n.config=t.extend({},n.config,e),n},on:function(e,t){return layui.onevent.call(this,i,e,t)}}),i="carousel",a="layui-this",o=">*[carousel-item]>*",r="layui-carousel-left",l="layui-carousel-right",s="layui-carousel-prev",c="layui-carousel-next",u="layui-carousel-arrow",d="layui-carousel-ind",f=function(e){var i=this;i.config=t.extend({},i.config,n.config,e),i.render()};f.prototype.config={width:"600px",height:"280px",full:!1,arrow:"hover",indicator:"inside",autoplay:!0,interval:3e3,anim:"",trigger:"click",index:0},f.prototype.render=function(){var e=this,n=e.config;n.elem=t(n.elem),n.elem[0]&&(e.elemItem=n.elem.find(o),n.index<0&&(n.index=0),n.index>=e.elemItem.length&&(n.index=e.elemItem.length-1),n.interval<800&&(n.interval=800),n.full?n.elem.css({position:"fixed",width:"100%",height:"100%",zIndex:9999}):n.elem.css({width:n.width,height:n.height}),n.elem.attr("lay-anim",n.anim),e.elemItem.eq(n.index).addClass(a),e.elemItem.length<=1||(e.indicator(),e.arrow(),e.autoplay(),e.events()))},f.prototype.reload=function(e){var n=this;clearInterval(n.timer),n.config=t.extend({},n.config,e),n.render()},f.prototype.prevIndex=function(){var e=this,t=e.config,n=t.index-1;return n<0&&(n=e.elemItem.length-1),n},f.prototype.nextIndex=function(){var e=this,t=e.config,n=t.index+1;return n>=e.elemItem.length&&(n=0),n},f.prototype.addIndex=function(e){var t=this,n=t.config;e=e||1,n.index=n.index+e,n.index>=t.elemItem.length&&(n.index=0)},f.prototype.subIndex=function(e){var t=this,n=t.config;e=e||1,n.index=n.index-e,n.index<0&&(n.index=t.elemItem.length-1)},f.prototype.autoplay=function(){var e=this,t=e.config;t.autoplay&&(e.timer=setInterval(function(){e.slide()},t.interval))},f.prototype.arrow=function(){var e=this,n=e.config,i=t(['<button class="layui-icon '+u+'" lay-type="sub">'+("updown"===n.anim?"&#xe619;":"&#xe603;")+"</button>",'<button class="layui-icon '+u+'" lay-type="add">'+("updown"===n.anim?"&#xe61a;":"&#xe602;")+"</button>"].join(""));n.elem.attr("lay-arrow",n.arrow),n.elem.find("."+u)[0]&&n.elem.find("."+u).remove(),n.elem.append(i),i.on("click",function(){var n=t(this),i=n.attr("lay-type");e.slide(i)})},f.prototype.indicator=function(){var e=this,n=e.config,i=e.elemInd=t(['<div class="'+d+'"><ul>',function(){var t=[];return layui.each(e.elemItem,function(e){t.push("<li"+(n.index===e?' class="layui-this"':"")+"></li>")}),t.join("")}(),"</ul></div>"].join(""));n.elem.attr("lay-indicator",n.indicator),n.elem.find("."+d)[0]&&n.elem.find("."+d).remove(),n.elem.append(i),"updown"===n.anim&&i.css("margin-top",-i.height()/2),i.find("li").on("hover"===n.trigger?"mouseover":n.trigger,function(){var i=t(this),a=i.index();a>n.index?e.slide("add",a-n.index):a<n.index&&e.slide("sub",n.index-a)})},f.prototype.slide=function(e,t){var n=this,o=n.elemItem,u=n.config,d=u.index,f=u.elem.attr("lay-filter");n.haveSlide||("sub"===e?(n.subIndex(t),o.eq(u.index).addClass(s),setTimeout(function(){o.eq(d).addClass(l),o.eq(u.index).addClass(l)},50)):(n.addIndex(t),o.eq(u.index).addClass(c),setTimeout(function(){o.eq(d).addClass(r),o.eq(u.index).addClass(r)},50)),setTimeout(function(){o.removeClass(a+" "+s+" "+c+" "+r+" "+l),o.eq(u.index).addClass(a),n.haveSlide=!1},300),n.elemInd.find("li").eq(u.index).addClass(a).siblings().removeClass(a),n.haveSlide=!0,layui.event.call(this,i,"change("+f+")",{index:u.index,prevIndex:d,item:o.eq(u.index)}))},f.prototype.events=function(){var e=this,t=e.config;t.elem.data("haveEvents")||(t.elem.on("mouseenter",function(){clearInterval(e.timer)}).on("mouseleave",function(){e.autoplay()}),t.elem.data("haveEvents",!0))},n.render=function(e){var t=new f(e);return t},e(i,n)}),layui.define("jquery",function(e){"use strict";var t=layui.jquery,n={config:{},index:layui.rate?layui.rate.index+1e4:0,set:function(e){var n=this;return n.config=t.extend({},n.config,e),n},on:function(e,t){return layui.onevent.call(this,a,e,t)}},i=function(){var e=this,t=e.config;return{setvalue:function(t){e.setvalue.call(e,t)},config:t}},a="rate",o="layui-rate",r="layui-icon-rate",l="layui-icon-rate-solid",s="layui-icon-rate-half",c="layui-icon-rate-solid layui-icon-rate-half",u="layui-icon-rate-solid layui-icon-rate",d="layui-icon-rate layui-icon-rate-half",f=function(e){var i=this;i.index=++n.index,i.config=t.extend({},i.config,n.config,e),i.render()};f.prototype.config={length:5,text:!1,readonly:!1,half:!1,value:0,theme:""},f.prototype.render=function(){var e=this,n=e.config,i=n.theme?'style="color: '+n.theme+';"':"";n.elem=t(n.elem),parseInt(n.value)!==n.value&&(n.half||(n.value=Math.ceil(n.value)-n.value<.5?Math.ceil(n.value):Math.floor(n.value)));for(var a='<ul class="layui-rate" '+(n.readonly?"readonly":"")+">",s=1;s<=n.length;s++){var c='<li class="layui-inline"><i class="layui-icon '+(s>Math.floor(n.value)?r:l)+'" '+i+"></i></li>";n.half&&parseInt(n.value)!==n.value&&s==Math.ceil(n.value)?a=a+'<li><i class="layui-icon layui-icon-rate-half" '+i+"></i></li>":a+=c}a+="</ul>"+(n.text?'<span class="layui-inline">'+n.value+"星":"")+"</span>";var u=n.elem,d=u.next("."+o);d[0]&&d.remove(),e.elemTemp=t(a),n.span=e.elemTemp.next("span"),n.setText&&n.setText(n.value),u.html(e.elemTemp),u.addClass("layui-inline"),n.readonly||e.action()},f.prototype.setvalue=function(e){var t=this,n=t.config;n.value=e,t.render()},f.prototype.action=function(){var e=this,n=e.config,i=e.elemTemp,a=i.find("i").width();i.children("li").each(function(e){var o=e+1,f=t(this);f.on("click",function(e){if(n.value=o,n.half){var r=e.pageX-t(this).offset().left;r<=a/2&&(n.value=n.value-.5)}n.text&&i.next("span").text(n.value+"星"),n.choose&&n.choose(n.value),n.setText&&n.setText(n.value)}),f.on("mousemove",function(e){if(i.find("i").each(function(){t(this).addClass(r).removeClass(c)}),i.find("i:lt("+o+")").each(function(){t(this).addClass(l).removeClass(d)}),n.half){var u=e.pageX-t(this).offset().left;u<=a/2&&f.children("i").addClass(s).removeClass(l)}}),f.on("mouseleave",function(){i.find("i").each(function(){t(this).addClass(r).removeClass(c)}),i.find("i:lt("+Math.floor(n.value)+")").each(function(){t(this).addClass(l).removeClass(d)}),n.half&&parseInt(n.value)!==n.value&&i.children("li:eq("+Math.floor(n.value)+")").children("i").addClass(s).removeClass(u)})})},f.prototype.events=function(){var e=this;e.config},n.render=function(e){var t=new f(e);return i.call(t)},e(a,n)}),layui.define("jquery",function(e){"use strict";var t=layui.$,n={fixbar:function(e){var n,i,a="layui-fixbar",o="layui-fixbar-top",r=t(document),l=t("body");e=t.extend({showHeight:200},e),e.bar1=!0===e.bar1?"&#xe606;":e.bar1,e.bar2=!0===e.bar2?"&#xe607;":e.bar2,e.bgcolor=e.bgcolor?"background-color:"+e.bgcolor:"";var s=[e.bar1,e.bar2,"&#xe604;"],c=t(['<ul class="'+a+'">',e.bar1?'<li class="layui-icon" lay-type="bar1" style="'+e.bgcolor+'">'+s[0]+"</li>":"",e.bar2?'<li class="layui-icon" lay-type="bar2" style="'+e.bgcolor+'">'+s[1]+"</li>":"",'<li class="layui-icon '+o+'" lay-type="top" style="'+e.bgcolor+'">'+s[2]+"</li>","</ul>"].join("")),u=c.find("."+o),d=function(){var t=r.scrollTop();t>=e.showHeight?n||(u.show(),n=1):n&&(u.hide(),n=0)};t("."+a)[0]||("object"==typeof e.css&&c.css(e.css),l.append(c),d(),c.find("li").on("click",function(){var n=t(this),i=n.attr("lay-type");"top"===i&&t("html,body").animate({scrollTop:0},200),e.click&&e.click.call(this,i)}),r.on("scroll",function(){clearTimeout(i),i=setTimeout(function(){d()},100)}))},countdown:function(e,t,n){var i=this,a="function"==typeof t,o=new Date(e).getTime(),r=new Date(!t||a?(new Date).getTime():t).getTime(),l=o-r,s=[Math.floor(l/864e5),Math.floor(l/36e5)%24,Math.floor(l/6e4)%60,Math.floor(l/1e3)%60];a&&(n=t);var c=setTimeout(function(){i.countdown(e,r+1e3,n)},1e3);return n&&n(l>0?s:[0,0,0,0],t,c),l<=0&&clearTimeout(c),c},timeAgo:function(e,t){var n=this,i=[[],[]],a=(new Date).getTime()-new Date(e).getTime();return a>6912e5?(a=new Date(e),i[0][0]=n.digit(a.getFullYear(),4),i[0][1]=n.digit(a.getMonth()+1),i[0][2]=n.digit(a.getDate()),t||(i[1][0]=n.digit(a.getHours()),i[1][1]=n.digit(a.getMinutes()),i[1][2]=n.digit(a.getSeconds())),i[0].join("-")+" "+i[1].join(":")):a>=864e5?(a/1e3/60/60/24|0)+"天前":a>=36e5?(a/1e3/60/60|0)+"小时前":a>=12e4?(a/1e3/60|0)+"分钟前":a<0?"未来":"刚刚"},digit:function(e,t){var n="";e=String(e),t=t||2;for(var i=e.length;i<t;i++)n+="0";return e<Math.pow(10,t)?n+(0|e):e},toDateString:function(e,t){var n=this,i=new Date(e||new Date),a=[n.digit(i.getFullYear(),4),n.digit(i.getMonth()+1),n.digit(i.getDate())],o=[n.digit(i.getHours()),n.digit(i.getMinutes()),n.digit(i.getSeconds())];return t=t||"yyyy-MM-dd HH:mm:ss",t.replace(/yyyy/g,a[0]).replace(/MM/g,a[1]).replace(/dd/g,a[2]).replace(/HH/g,o[0]).replace(/mm/g,o[1]).replace(/ss/g,o[2])},escape:function(e){return String(e||"").replace(/&(?!#?[a-zA-Z0-9]+;)/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/'/g,"&#39;").replace(/"/g,"&quot;")}};!function(e,t,n){function i(){a=t[l](function(){o.each(function(){var t=e(this),n=t.width(),i=t.height(),a=e.data(this,c);(n!==a.w||i!==a.h)&&t.trigger(s,[a.w=n,a.h=i])}),i()},r[u])}var a,o=e([]),r=e.resize=e.extend(e.resize,{}),l="setTimeout",s="resize",c=s+"-special-event",u="delay",d="throttleWindow";r[u]=250,r[d]=!0,e.event.special[s]={setup:function(){if(!r[d]&&this[l])return!1;var t=e(this);o=o.add(t),e.data(this,c,{w:t.width(),h:t.height()}),1===o.length&&i()},teardown:function(){if(!r[d]&&this[l])return!1;var t=e(this);o=o.not(t),t.removeData(c),o.length||clearTimeout(a)},add:function(t){function i(t,i,o){var r=e(this),l=e.data(this,c)||{};l.w=i!==n?i:r.width(),l.h=o!==n?o:r.height(),a.apply(this,arguments)}return!(!r[d]&&this[l])&&(e.isFunction(t)?(a=t,i):(a=t.handler,void(t.handler=i)));var a}}}(t,window),e("util",n)}),layui.define("jquery",function(e){"use strict";var t=layui.$,n=function(e){},i='<i class="layui-anim layui-anim-rotate layui-anim-loop layui-icon ">&#xe63e;</i>';n.prototype.load=function(e){var n,a,o,r=this,l=0;e=e||{};var s=t(e.elem);if(s[0]){var c=t(e.scrollElem||document),u=e.mb||50,d=!("isAuto"in e)||e.isAuto,f=e.end||"没有更多了",p=e.scrollElem&&e.scrollElem!==document,h="<cite>加载更多</cite>",y=t('<div class="layui-flow-more"><a href="javascript:;">'+h+"</a></div>");s.find(".layui-flow-more")[0]||s.append(y);var m=function(e,i){e=t(e),y.before(e),i=0==i||null,i?y.html(f):y.find("a").html(h),a=i,n=null,g&&g()},v=function(){n=!0,y.find("a").html(i),"function"==typeof e.done&&e.done(++l,m)};if(v(),y.find("a").on("click",function(){t(this),a||n||v()}),e.isLazyimg)var g=r.lazyimg({elem:e.elem+" img",scrollElem:e.scrollElem});return d?(c.on("scroll",function(){var e=t(this),i=e.scrollTop();o&&clearTimeout(o),a||(o=setTimeout(function(){var a=p?e.height():t(window).height(),o=p?e.prop("scrollHeight"):document.documentElement.scrollHeight;o-i-a<=u&&(n||v())},100))}),r):r}},n.prototype.lazyimg=function(e){var n,i=this,a=0;e=e||{};var o,r=t(e.scrollElem||document),l=e.elem||"img",s=e.scrollElem&&e.scrollElem!==document,c=function(e,t){var n=r.scrollTop(),o=n+t,l=s?e.offset().top-r.offset().top+n:e.offset().top;if(l>=n&&l<=o&&!e.attr("src")){var c=e.attr("lay-src");layui.img(c,function(){var t=i.lazyimg.elem.eq(a);e.attr("src",c).removeAttr("lay-src"),t[0]&&u(t),a++})}},u=function(e,n){var o=s?(n||r).height():t(window).height(),u=r.scrollTop(),d=u+o;if(i.lazyimg.elem=t(l),e)c(e,o);else for(var f=0;f<i.lazyimg.elem.length;f++){var p=i.lazyimg.elem.eq(f),h=s?p.offset().top-r.offset().top+u:p.offset().top;if(c(p,o),a=f,h>d)break}};(u(),n)||(r.on("scroll",function(){var e=t(this);o&&clearTimeout(o),o=setTimeout(function(){u(null,e)},50)}),n=!0);return u},e("flow",new n)}),layui.define(["layer","form"],function(e){"use strict";var t=layui.$,n=layui.layer,i=layui.form,a=(layui.hint(),layui.device()),o="layedit",r="layui-show",l="layui-disabled",s=function(){var e=this;e.index=0,e.config={tool:["strong","italic","underline","del","|","left","center","right","|","link","unlink","face","image"],hideTool:[],height:280}};s.prototype.set=function(e){var n=this;return t.extend(!0,n.config,e),n},s.prototype.on=function(e,t){return layui.onevent(o,e,t)},s.prototype.build=function(e,n){n=n||{};var i=this,o=i.config,l="layui-layedit",s=t("string"==typeof e?"#"+e:e),u="LAY_layedit_"+ ++i.index,d=s.next("."+l),f=t.extend({},o,n),p=function(){var e=[],t={};return layui.each(f.hideTool,function(e,n){t[n]=!0}),layui.each(f.tool,function(n,i){k[i]&&!t[i]&&e.push(k[i])}),e.join("")}(),h=t(['<div class="'+l+'">','<div class="layui-unselect layui-layedit-tool">'+p+"</div>",'<div class="layui-layedit-iframe">','<iframe id="'+u+'" name="'+u+'" textarea="'+e+'" frameborder="0"></iframe>',"</div>","</div>"].join(""));return a.ie&&a.ie<8?s.removeClass("layui-hide").addClass(r):(d[0]&&d.remove(),c.call(i,h,s[0],f),s.addClass("layui-hide").after(h),i.index)},s.prototype.getContent=function(e){var t=u(e);if(t[0])return d(t[0].document.body.innerHTML)},s.prototype.getText=function(e){var n=u(e);if(n[0])return t(n[0].document.body).text()},s.prototype.setContent=function(e,n,i){var a=u(e);a[0]&&(i?t(a[0].document.body).append(n):t(a[0].document.body).html(n),layedit.sync(e))},s.prototype.sync=function(e){var n=u(e);if(n[0]){var i=t("#"+n[1].attr("textarea"));i.val(d(n[0].document.body.innerHTML))}},s.prototype.getSelection=function(e){var t=u(e);if(t[0]){var n=h(t[0].document);return document.selection?n.text:n.toString()}};var c=function(e,n,i){var a=this,o=e.find("iframe");o.css({height:i.height}).on("load",function(){var r=o.contents(),l=o.prop("contentWindow"),s=r.find("head"),c=t(["<style>","*{margin: 0; padding: 0;}","body{padding: 10px; line-height: 20px; overflow-x: hidden; word-wrap: break-word; font: 14px Helvetica Neue,Helvetica,PingFang SC,Microsoft YaHei,Tahoma,Arial,sans-serif; -webkit-box-sizing: border-box !important; -moz-box-sizing: border-box !important; box-sizing: border-box !important;}","a{color:#01AAED; text-decoration:none;}a:hover{color:#c00}","p{margin-bottom: 10px;}","img{display: inline-block; border: none; vertical-align: middle;}","pre{margin: 10px 0; padding: 10px; line-height: 20px; border: 1px solid #ddd; border-left-width: 6px; background-color: #F2F2F2; color: #333; font-family: Courier New; font-size: 12px;}","</style>"].join("")),u=r.find("body");s.append(c),u.attr("contenteditable","true").css({"min-height":i.height}).html(n.value||""),f.apply(a,[l,o,n,i]),g.call(a,l,e,i)})},u=function(e){var n=t("#LAY_layedit_"+e),i=n.prop("contentWindow");return[i,n]},d=function(e){return 8==a.ie&&(e=e.replace(/<.+>/g,function(e){return e.toLowerCase()})),e},f=function(e,i,o,r){var l=e.document,s=t(l.body);s.on("keydown",function(e){var t=e.keyCode;if(13===t){var i=h(l),a=y(i),o=a.parentNode;if("pre"===o.tagName.toLowerCase()){if(e.shiftKey)return;return n.msg("请暂时用shift+enter"),!1}l.execCommand("formatBlock",!1,"<p>")}}),t(o).parents("form").on("submit",function(){var e=s.html();8==a.ie&&(e=e.replace(/<.+>/g,function(e){return e.toLowerCase()})),o.value=e}),s.on("paste",function(t){l.execCommand("formatBlock",!1,"<p>"),setTimeout(function(){p.call(e,s),o.value=s.html()},100)})},p=function(e){var n=this;n.document,e.find("*[style]").each(function(){var e=this.style.textAlign;this.removeAttribute("style"),t(this).css({"text-align":e||""})}),e.find("table").addClass("layui-table"),e.find("script,link").remove()},h=function(e){return e.selection?e.selection.createRange():e.getSelection().getRangeAt(0)},y=function(e){return e.endContainer||e.parentElement().childNodes[0]},m=function(e,n,i){var a=this.document,o=document.createElement(e);for(var r in n)o.setAttribute(r,n[r]);if(o.removeAttribute("text"),a.selection){var l=i.text||n.text;if("a"===e&&!l)return;l&&(o.innerHTML=l),i.pasteHTML(t(o).prop("outerHTML")),i.select()}else{l=i.toString()||n.text;if("a"===e&&!l)return;l&&(o.innerHTML=l),i.deleteContents(),i.insertNode(o)}},v=function(e,n){var i=this.document,a="layedit-tool-active",o=y(h(i)),r=function(t){return e.find(".layedit-tool-"+t)};n&&n[n.hasClass(a)?"removeClass":"addClass"](a),e.find(">i").removeClass(a),r("unlink").addClass(l),t(o).parents().each(function(){var e=this.tagName.toLowerCase(),t=this.style.textAlign;"b"!==e&&"strong"!==e||r("b").addClass(a),"i"!==e&&"em"!==e||r("i").addClass(a),"u"===e&&r("u").addClass(a),"strike"===e&&r("d").addClass(a),"p"===e&&("center"===t?r("center").addClass(a):"right"===t?r("right").addClass(a):r("left").addClass(a)),"a"===e&&(r("link").addClass(a),r("unlink").removeClass(l))})},g=function(e,i,a){var o=e.document,r=t(o.body),s={link:function(n){var i=y(n),a=t(i).parent();b.call(r,{href:a.attr("href"),target:a.attr("target")},function(t){var i=a[0];"A"===i.tagName?i.href=t.url:m.call(e,"a",{target:t.target,href:t.url,text:t.url},n)})},unlink:function(e){o.execCommand("unlink")},face:function(t){x.call(this,function(n){m.call(e,"img",{src:n.src,alt:n.alt},t)})},image:function(i){var o=this;layui.use("upload",function(r){var l=a.uploadImage||{};r.render({url:l.url,method:l.type,elem:t(o).find("input")[0],done:function(t){0==t.code?(t.data=t.data||{},m.call(e,"img",{src:t.data.src,alt:t.data.title},i)):n.msg(t.msg||"上传失败")}})})},code:function(t){w.call(r,function(n){m.call(e,"pre",{text:n.code,"lay-lang":n.lang},t)})},help:function(){n.open({type:2,title:"帮助",area:["600px","380px"],shadeClose:!0,shade:.1,skin:"layui-layer-msg",content:["http://www.layui.com/about/layedit/help.html","no"]})}},c=i.find(".layui-layedit-tool"),u=function(){var n=t(this),i=n.attr("layedit-event"),a=n.attr("lay-command");if(!n.hasClass(l)){r.focus();var u=h(o);u.commonAncestorContainer,a?(o.execCommand(a),/justifyLeft|justifyCenter|justifyRight/.test(a)&&o.execCommand("formatBlock",!1,"<p>"),setTimeout(function(){r.focus()},10)):s[i]&&s[i].call(this,u),v.call(e,c,n)}},d=/image/;c.find(">i").on("mousedown",function(){var e=t(this),n=e.attr("layedit-event");d.test(n)||u.call(this)}).on("click",function(){var e=t(this),n=e.attr("layedit-event");d.test(n)&&u.call(this)}),r.on("click",function(){v.call(e,c),n.close(x.index)})},b=function(e,t){var a=this,o=n.open({type:1,id:"LAY_layedit_link",area:"350px",shade:.05,shadeClose:!0,moveType:1,title:"超链接",skin:"layui-layer-msg",content:['<ul class="layui-form" style="margin: 15px;">','<li class="layui-form-item">','<label class="layui-form-label" style="width: 60px;">URL</label>','<div class="layui-input-block" style="margin-left: 90px">','<input name="url" lay-verify="url" value="'+(e.href||"")+'" autofocus="true" autocomplete="off" class="layui-input">',"</div>","</li>",'<li class="layui-form-item">','<label class="layui-form-label" style="width: 60px;">打开方式</label>','<div class="layui-input-block" style="margin-left: 90px">','<input type="radio" name="target" value="_self" class="layui-input" title="当前窗口"'+("_self"!==e.target&&e.target?"":"checked")+">",'<input type="radio" name="target" value="_blank" class="layui-input" title="新窗口" '+("_blank"===e.target?"checked":"")+">","</div>","</li>",'<li class="layui-form-item" style="text-align: center;">','<button type="button" lay-submit lay-filter="layedit-link-yes" class="layui-btn"> 确定 </button>','<button style="margin-left: 20px;" type="button" class="layui-btn layui-btn-primary"> 取消 </button>',"</li>","</ul>"].join(""),success:function(e,o){var r="submit(layedit-link-yes)";i.render("radio"),e.find(".layui-btn-primary").on("click",function(){n.close(o),a.focus()}),i.on(r,function(e){n.close(b.index),t&&t(e.field)})}});b.index=o},x=function(e){var i=function(){var e=["[微笑]","[嘻嘻]","[哈哈]","[可爱]","[可怜]","[挖鼻]","[吃惊]","[害羞]","[挤眼]","[闭嘴]","[鄙视]","[爱你]","[泪]","[偷笑]","[亲亲]","[生病]","[太开心]","[白眼]","[右哼哼]","[左哼哼]","[嘘]","[衰]","[委屈]","[吐]","[哈欠]","[抱抱]","[怒]","[疑问]","[馋嘴]","[拜拜]","[思考]","[汗]","[困]","[睡]","[钱]","[失望]","[酷]","[色]","[哼]","[鼓掌]","[晕]","[悲伤]","[抓狂]","[黑线]","[阴险]","[怒骂]","[互粉]","[心]","[伤心]","[猪头]","[熊猫]","[兔子]","[ok]","[耶]","[good]","[NO]","[赞]","[来]","[弱]","[草泥马]","[神马]","[囧]","[浮云]","[给力]","[围观]","[威武]","[奥特曼]","[礼物]","[钟]","[话筒]","[蜡烛]","[蛋糕]"],t={};return layui.each(e,function(e,n){t[n]=layui.cache.dir+"images/face/"+e+".gif"}),t}();return x.hide=x.hide||function(e){"face"!==t(e.target).attr("layedit-event")&&n.close(x.index)},x.index=n.tips(function(){var e=[];return layui.each(i,function(t,n){e.push('<li title="'+t+'"><img src="'+n+'" alt="'+t+'"></li>')}),'<ul class="layui-clear">'+e.join("")+"</ul>"}(),this,{tips:1,time:0,skin:"layui-box layui-util-face",maxWidth:500,success:function(a,o){a.css({marginTop:-4,marginLeft:-10}).find(".layui-clear>li").on("click",function(){e&&e({src:i[this.title],alt:this.title}),n.close(o)}),t(document).off("click",x.hide).on("click",x.hide)}})},w=function(e){var t=this,a=n.open({type:1,id:"LAY_layedit_code",area:"550px",shade:.05,shadeClose:!0,moveType:1,title:"插入代码",skin:"layui-layer-msg",
content:['<ul class="layui-form layui-form-pane" style="margin: 15px;">','<li class="layui-form-item">','<label class="layui-form-label">请选择语言</label>','<div class="layui-input-block">','<select name="lang">','<option value="JavaScript">JavaScript</option>','<option value="HTML">HTML</option>','<option value="CSS">CSS</option>','<option value="Java">Java</option>','<option value="PHP">PHP</option>','<option value="C#">C#</option>','<option value="Python">Python</option>','<option value="Ruby">Ruby</option>','<option value="Go">Go</option>',"</select>","</div>","</li>",'<li class="layui-form-item layui-form-text">','<label class="layui-form-label">代码</label>','<div class="layui-input-block">','<textarea name="code" lay-verify="required" autofocus="true" class="layui-textarea" style="height: 200px;"></textarea>',"</div>","</li>",'<li class="layui-form-item" style="text-align: center;">','<button type="button" lay-submit lay-filter="layedit-code-yes" class="layui-btn"> 确定 </button>','<button style="margin-left: 20px;" type="button" class="layui-btn layui-btn-primary"> 取消 </button>',"</li>","</ul>"].join(""),success:function(a,o){var r="submit(layedit-code-yes)";i.render("select"),a.find(".layui-btn-primary").on("click",function(){n.close(o),t.focus()}),i.on(r,function(t){n.close(w.index),e&&e(t.field)})}});w.index=a},k={html:'<i class="layui-icon layedit-tool-html" title="HTML源代码" lay-command="html" layedit-event="html"">&#xe64b;</i><span class="layedit-tool-mid"></span>',strong:'<i class="layui-icon layedit-tool-b" title="加粗" lay-command="Bold" layedit-event="b"">&#xe62b;</i>',italic:'<i class="layui-icon layedit-tool-i" title="斜体" lay-command="italic" layedit-event="i"">&#xe644;</i>',underline:'<i class="layui-icon layedit-tool-u" title="下划线" lay-command="underline" layedit-event="u"">&#xe646;</i>',del:'<i class="layui-icon layedit-tool-d" title="删除线" lay-command="strikeThrough" layedit-event="d"">&#xe64f;</i>',"|":'<span class="layedit-tool-mid"></span>',left:'<i class="layui-icon layedit-tool-left" title="左对齐" lay-command="justifyLeft" layedit-event="left"">&#xe649;</i>',center:'<i class="layui-icon layedit-tool-center" title="居中对齐" lay-command="justifyCenter" layedit-event="center"">&#xe647;</i>',right:'<i class="layui-icon layedit-tool-right" title="右对齐" lay-command="justifyRight" layedit-event="right"">&#xe648;</i>',link:'<i class="layui-icon layedit-tool-link" title="插入链接" layedit-event="link"">&#xe64c;</i>',unlink:'<i class="layui-icon layedit-tool-unlink layui-disabled" title="清除链接" lay-command="unlink" layedit-event="unlink"">&#xe64d;</i>',face:'<i class="layui-icon layedit-tool-face" title="表情" layedit-event="face"">&#xe650;</i>',image:'<i class="layui-icon layedit-tool-image" title="图片" layedit-event="image">&#xe64a;<input type="file" name="file"></i>',code:'<i class="layui-icon layedit-tool-code" title="插入代码" layedit-event="code">&#xe64e;</i>',help:'<i class="layui-icon layedit-tool-help" title="帮助" layedit-event="help">&#xe607;</i>'},C=new s;e(o,C)}),layui.define("jquery",function(e){"use strict";var t=layui.$,n="http://www.layui.com/doc/modules/code.html";e("code",function(e){var i=[];e=e||{},e.elem=t(e.elem||".layui-code"),e.about=!("about"in e)||e.about,e.elem.each(function(){i.push(this)}),layui.each(i.reverse(),function(i,a){var o=t(a),r=o.html();(o.attr("lay-encode")||e.encode)&&(r=r.replace(/&(?!#?[a-zA-Z0-9]+;)/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/'/g,"&#39;").replace(/"/g,"&quot;")),o.html('<ol class="layui-code-ol"><li>'+r.replace(/[\r\t\n]+/g,"</li><li>")+"</li></ol>"),o.find(">.layui-code-h3")[0]||o.prepend('<h3 class="layui-code-h3">'+(o.attr("lay-title")||e.title||"code")+(e.about?'<a href="'+n+'" target="_blank">layui.code</a>':"")+"</h3>");var l=o.find(">.layui-code-ol");o.addClass("layui-box layui-code-view"),(o.attr("lay-skin")||e.skin)&&o.addClass("layui-code-"+(o.attr("lay-skin")||e.skin)),(l.find("li").length/100|0)>0&&l.css("margin-left",(l.find("li").length/100|0)+"px"),(o.attr("lay-height")||e.height)&&l.css("max-height",o.attr("lay-height")||e.height)})})}).addcss("modules/code.css","skincodecss");