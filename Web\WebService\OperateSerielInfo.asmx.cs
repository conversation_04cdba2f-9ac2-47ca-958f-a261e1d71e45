﻿using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml.Linq;
using BLL;
using Newtonsoft.Json;
using System.Web.SessionState;
using Common;
using System.Collections.Generic;
using WXCommon;


namespace Web.WebService
{
    /// <summary>
    /// OperateSerielInfo 的摘要说明
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    [ToolboxItem(false)]
    // 若要允许使用 ASP.NET AJAX 从脚本中调用此 Web 服务，请取消对下行的注释。
    // [System.Web.Script.Services.ScriptService]
    public class OperateSerielInfo : System.Web.Services.WebService
    {

        [WebMethod]

        // 烧录后的序列号，回写状态
        public string UpdateSerielInfoStatus(string sSeriel,string sComp)
        {
            string sJson = string.Empty;
            string sReturn = string.Empty;
            string Message = string.Empty;

            sReturn = BaseModuleBll.OPSerielInfo("", sSeriel, "", "", "", "", "", "", sComp, "", "", "4");
            if (sReturn.Length < 5)  // 更新成功
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }
            sJson = JsonConvert.SerializeObject(new { Msg = Message });

            return sJson;
        }

        [WebMethod]

        // 发送需要打印的序列号 
        public string SentPrintSerielInfo(string sSeriel, string sProc, string sComp)
        {
            string sJson = string.Empty;
            string sReturn = string.Empty;
            string Message = string.Empty;

            sReturn = BaseModuleBll.OPSerielInfo("", sSeriel, sProc, "", "", "", "", "", sComp, "", "", "5");
            if (sReturn == "2")  // 更新成功
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }
            sJson = JsonConvert.SerializeObject(new { Msg = Message });

            return sJson;
        }













    }
}
