﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件版本</title>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/XC.css" rel="stylesheet" />
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/BaseModule.js" type="text/javascript"></script>

    <script>
        //获取当前所在的模块、菜单
        const currentTabId = sessionStorage.getItem("currentTabId");
        const ModuleName = currentTabId != null ? JSON.parse(currentTabId).ModuleName : null;

        layui.use('table', function () {
            var table = layui.table;

            table.render({
                elem: '#SoftwareVersionInfoList',
                id: 'SoftwareVersionInfoListID',
                url: '../Service/BaseModuleAjax.ashx?OP=GetSoftwareVersionInfoList&CFlag=12',
                height: 'full-50',
                cols: [[ //标题栏
                    { type: 'numbers', title: '序号', width: 45, align: "center" },
                    { field: 'Version', title: '版本号', width: 80, align: "center" },
                    { field: 'VersionType', title: '版本类型', width: 80, align: "center" },
                    {
                        field: 'VersionDesc', title: '版本描述', width: 700, align: 'center', templet: function (d) {
                            return '<textarea class="XC-Textarea-block" readonly style="max-height: 160px;height:160px; width: 99%;resize:none;">' + d.VersionDesc +'</textarea>'
                        }
                    },
                    { field: 'InDate', title: '操作时间', width: 100, align: "center" },
                    { field: 'InMan', title: '操作人', width: 100, align: "center" },
                    { field: 'Remark', title: '备注', width: 150 },
                    { field: '', title: '操作', width: 190, toolbar: '#barDemo', align: "center" }
                ]],
                skin: 'line', // 表格风格
                page: true, // 是否显示分页
                count: 50, //数据总数 服务端获得
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
            });

            table.on('tool(SoftwareVersionInfoList)', function (obj) {
                var data = obj.data, layEvent = obj.event;
                if (layEvent == 'edit') {
                    $('#head-title1').html("修改版本信息");
                    $("#ShowOne").css("display", "block")
                    $("#ShowOne-fade").css("display", "block")
                    $('#txtAEFlag').val("2");
                    $("#txtVerNo").val(data.Version)
                    $("#txtVerKind").val(data.VersionType)
                    $("#txtVerDesc").val(data.VersionDesc)
                    $("#txtRemark").val(data.Remark)
                    $("#txtVNo").val(data.VNo)
                }
                else if (layEvent == 'delete') {
                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("确定要删除该版本信息吗？版本号：")

                    //设置删除的对象
                    $("#hint-value").html(data.Version)

                    $("#txtDelVNo").val(data.VNo)
                }

            })

            $("#SoftwareVerInfoList_open").click(function () {
                var sVersionNo = $("#txtVersionNo").val()
                var sVersionDesc = $("#txtVersionDesc").val()
                var Params = { No: sVersionNo, Name: sVersionDesc, A: "", B: "", C: "", D: "", E: "", F: "", G: "", H: "", BDate: "", EDate: "", };
                var Data = JSON.stringify(Params);
                table.reload('SoftwareVersionInfoListID', {
                    url: '../Service/BaseModuleAjax.ashx?OP=GetSoftwareVersionInfoList&CFlag=12&Data=' + encodeURIComponent(Data),
                    page: {
                        curr: 1
                    }
                });

            })
        });


        function openDialog() {  // 新增
            $('#head-title1').html("新增版本信息");
            $('#txtAEFlag').val("1");//新增的类型标记

            $('#ShowOne').css("display", "block")
            $('#ShowOne-fade').css("display", "block")
        }

        function closeDialog() {
            $('#ShowOne').css("display", "none")
            $('#ShowOne-fade').css("display", "none")
            $('#ShowDel').css("display", "none")
            $('#ShowDel-fade').css("display", "none")
            $("#txtVerNo").val("")
            $("#txtVerDesc").val("")
            $("#txtRemark").val("")
            $("#txtVNo").val("")
            $("#txtDelVNo").val("")
        }


    </script>
    <style>
        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }

        .wangid_conbox {
            margin-right: 5px;
        }

        .div_find {
            width: 99%;
        }

        .find_input {
            width: 10%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }

        .table-td {
            width: 70px;
            text-align: right;
            font-size: 12px
        }

        .table-row {
            height: 65px
        }

        .layui-badge {
            width: 95%;
            height: 100%;
            min-height: 10px;
            text-align: left;
            margin-left: 15px;
            padding: 10px
        }

        .diff {
            color: red;
        }
    </style>
</head>
<body>
    <div class="div_find">
        <p>
            <label class="find_labela">版本号</label><input type="text" id="txtVersionNo" class="find_input" style="padding-left:5px;width:12%" />
            <label class="find_labela">版本描述</label><input type="text" id="txtVersionDesc" class="find_input" placeholder="支持模糊查询" style="padding-left:5px;width:12%" />
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="SoftwareVerInfoList_open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="openDialog()">添加</button>
        </p>
    </div>
    <div class="wangid_conbox">
        <table class="layui-hide" id="SoftwareVersionInfoList" lay-filter="SoftwareVersionInfoList"></table>
        <script type="text/html" id="barDemo">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="edit">修改</button>
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="delete">删除</button>
        </script>
    </div>

    <!--弹出层-->
    <div class="XC-modal XC-modal-xl" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1"></span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <form class="XC-Form-block">
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">版本号</span>
                        <input type="text" class="XC-Input-block" id="txtVerNo" name="txtVerNo" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Select-block">版本类型</span>
                        <select class="XC-Select-block" id="txtVerKind" name="txtVerKind">
                            <option value=""></option>
                            <option value="WEB">WEB</option>
                            <option value="APP">APP</option>
                        </select>
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Textarea-block">版本描述</span>
                        <textarea class="XC-Textarea-block" id="txtVerDesc" name="txtVerDesc" style="max-height: 150px;height:150px; width: 99%;"> </textarea>
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Textarea-block">备注</span>
                        <textarea class="XC-Textarea-block" id="txtRemark" name="txtRemark" style="height: 70px; width: 99%;"> </textarea>
                    </div>
                </form>

                <div class="input-group" style="display:none; ">
                    <span class="input-group-addon">.</span>
                    <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
                    <input type="text" class="form-control" id="txtVNo" name="txtVNo" />
                </div>
            </div>
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="btn_SoftwareVerSave">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="btn_SoftwareVerClose" onclick='closeDialog()'>关闭</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>

    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelVNo" name="txtDelVNo" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="SoftwareVerDel_Btn">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>


    <!--消息提示-->
    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>
</html>