﻿using System;
using System.Collections;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml.Linq;
using BLL;
using Newtonsoft.Json;
using System.Web.SessionState;
using Common;
using System.Collections.Generic;
using WXCommon;
using System.Text;
using Newtonsoft.Json.Linq;
using System.Net;
using System.IO;
using NPOI.SS.Formula.Functions;
using System.Web.UI.WebControls;
using grsvr6Lib;
using Web.appcode;
using System.Configuration;
using static ICSharpCode.SharpZipLib.Zip.ExtendedUnixData;
using System.Web.Services.Description;

namespace Web.Service
{
    /// <summary>
    /// $codebehindclassname$ 的摘要说明
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class TechAjax : IHttpHandler, IRequiresSessionState
    {

        string sAMFlag = string.Empty;
        string IP = string.Empty;
        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "text/plain";
            string Result = string.Empty;
            string Operate = context.Request.Params["OP"];
            string DataParams = context.Request.Params["Data"];
            string sItem = context.Request.Params["Item"];
            sAMFlag = context.Request.Params["CFlag"];
            string sKind = context.Request.Params["CKind"];  // 接收所有类别
            string sStatus = context.Request.Params["CStatus"];  // 状态
            string sCNo = context.Request.Params["CNO"];  // 接收所有编号  
            string sMNo = context.Request.Params["CMNO"];  // 物料编码 
            string sPNo = context.Request.Params["CPNO"];  //  
            string sSPNo = context.Request.Params["CSPNO"];  //  
            string sCName = context.Request.Params["CNAME"];  // 接收所有名称
            string sCustNo = context.Request.Params["CCustNo"];
            string sAddNum = context.Request.Params["AddNum"];  // 数据加载的次数
            IP = context.Request.ServerVariables.Get("Remote_Addr").ToString().Trim();  //获取IP 地址
            int slimit = 0;
            int spage = 0;


            switch (Operate)
            {



                case "GetTechInfo":  // 获取工艺相关信息
                    slimit = int.Parse(context.Request.Params["limit"]);
                    spage = int.Parse(context.Request.Params["page"]);
                    GetTechInfo(DataParams, slimit, spage, sStatus, sCNo, sItem,sMNo, sAMFlag);
                    break;

                case "OPTechInfo": //    对工艺相关信息进行操作
                    Result = OPTechInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetProcedureList": //获取工序下拉列表    
                    Result = GetProcedureList(sKind, sMNo, sItem, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetProcedureActionList": //获取工序行为下拉列表    
                    Result = GetProcedureActionList(sKind, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "GetWorkUnitConList": //在流程增加工序，选择作业单元时：加载作业单号下的工厂、车间、线体
                    Result = GetWorkUnitConList(sKind, sAMFlag, sCNo);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetWorkUnit": //获取作业单元，给产品流程选择
                    Result = GetWorkUnitInfo(DataParams, sSPNo, sCNo, sItem, sMNo, sPNo, sCustNo, sKind, sAMFlag);
                    break;


                case "GetLableTemplateList": //获取产品编码+工序对应模板 下拉列表    
                    Result = GetLableTemplateList(sMNo, sItem, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetActListTips": //下来选择工序行为对应的提示信息 
                    Result = GetActListTips(sKind, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;



                case "GetSampleSizeCodeInfo"://获取样本量字码
                    slimit = Convert.ToInt32(context.Request.Params["limit"]);
                    spage = Convert.ToInt32(context.Request.Params["page"]);
                    GetSampleSizeCode(slimit, spage, sAMFlag);
                    break;
                case "OPSampleSizeCodeInfo"://操作样本量字码
                    Result = OPSampleSizeCodeInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;
                case "GetSamplingStd"://获取抽样标准
                    slimit = Convert.ToInt32(context.Request.Params["limit"]);
                    spage = Convert.ToInt32(context.Request.Params["page"]);
                    GetSamplingStd(slimit, spage, sAMFlag);
                    break;
                case "OPSamplingStd"://操作抽样标准
                    Result = OPSamplingStd(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;
                case "GetSamplingPlan"://获取抽样方案
                    slimit = Convert.ToInt32(context.Request.Params["limit"]);
                    spage = Convert.ToInt32(context.Request.Params["page"]);
                    GetSamplingPlan(slimit, spage, DataParams, sAMFlag);
                    break;
                case "OPSamplingPlan"://获取抽样方案
                    Result = OPSamplingPlan(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "GetExportConditions":  // 产品流程：打印DHR报表，获取序列号对应的工序
                    Result = GetExportConditions(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetExportData":     // 产品流程：打印DHR报表，循环每个序列号下的工序，打印DHR报表
                    GetExportData(DataParams, context);
                    break;

                case "COPYFILE"://拷贝文件，临时使用
                    Result = COPYFILE(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;
                case "InitializeMater":  // 产品流程：批量更新bom\工艺路线
                    InitializeMater(DataParams);
                    break;

                case "GetProductFlowBaseInfo":  //根据产品编码，工序，工序版本获取信息 
                    Result = GetProductFlowBaseInfo(sCNo, sItem, sMNo, sKind, sAMFlag);
                    break;














            }
        }









        #region 获取工艺相关的信息
        public void GetTechInfo(string Params, int rows, int page, string sStatus, string sCQNo, string sItmNo, string sMaterNo, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sReturn = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;
            string sItem = string.Empty;
            string sName = string.Empty;
            string sMNo = string.Empty;
            string sMName = string.Empty;
            string sSt = string.Empty;
            string sBDate = string.Empty;
            string sEDate = string.Empty;
            string sA = string.Empty;
            string sB = string.Empty;
            string sC = string.Empty;
            string sD = string.Empty;
            int iSumCount = 0;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return;
            }

            var AnonymousUser = new
            {
                No = String.Empty, Item = String.Empty,  Name = String.Empty,MNo = String.Empty,  MName = String.Empty, Status = String.Empty, BDate = String.Empty, EDate = String.Empty,
                A = String.Empty,B = String.Empty, C = String.Empty, D = String.Empty,
            };



            if (string.IsNullOrEmpty(Params) || (Params == null) || (Params == "null"))
            {
                sNo = ""; sItem = "";sName = "";sMNo = "";sMName = ""; sSt = ""; sBDate = ""; sEDate = "";
                sA = "";sB = ""; sC = ""; sD = "";
            }
            else
            {
                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

                sNo = Item.No; sItem = Item.Item; sName = Item.Name;sMNo = Item.MNo;sMName = Item.MName;sSt = Item.Status;
                sBDate = Item.BDate;sEDate = Item.EDate; sA = Item.A; sB = Item.B; sC = Item.C; sD = Item.D;
            }
            if (sBDate == "")
            {
                sBDate = "2021-01-01";
            }
            if (sEDate == "")
            {
                sEDate = "2999-12-30";
            }

            if (string.IsNullOrEmpty(sMNo)){  // 主要给传入产品编码查询信息的，如查询产品流程
                if (string.IsNullOrEmpty(sMaterNo) || (sMaterNo == null) || (sMaterNo == "null")){
                    sMNo = "";
                }
                else {
                    sMNo = sMaterNo;
                }
            }
            if (string.IsNullOrEmpty(sNo)){   
                if (string.IsNullOrEmpty(sCQNo) || (sCQNo == null) || (sCQNo == "null")){
                    sNo = "";
                }
                else{
                    sNo = sCQNo;
                }
            }
            if (string.IsNullOrEmpty(sItem)){   
                if (string.IsNullOrEmpty(sItmNo) || (sItmNo == null) || (sItmNo == "null")){
                    sItem = "";
                }
                else{
                    sItem = sItmNo;
                }
            }
            if (string.IsNullOrEmpty(sSt))
            {
                if (string.IsNullOrEmpty(sStatus) || (sStatus == null) || (sStatus == "null")){
                    sSt = "";
                }
                else{
                    sSt = sStatus;
                }
            }



            DataTable dt = TechBll.GetTechInfo(sNo, sItem, sName, sMNo, sMName, sSt, sBDate, sEDate, sA, sB, sC, sD, rows, page, sMan, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                iSumCount = int.Parse(dt.Rows[0]["NumCount"].ToString());

                string sJson = JsonConvert.SerializeObject(dt);
                sReturn = "{\"code\":0,\"msg\":\"\",\"count\":" + iSumCount + ",\"data\":" + sJson + "}";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;
            context.Response.Write(sReturn);
        }
        #endregion



        #region 对工艺信息进行操作
        public string OPTechInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            string sDNo = string.Empty;
            string sIQCNo = string.Empty;
            string SeqNo = string.Empty;
            string sSNum = string.Empty;
            string sSpecNo = string.Empty;


            var AnonymousUser = new
            {
                No = String.Empty,Name = String.Empty, Item = String.Empty, MNo = String.Empty, MName = String.Empty,A = String.Empty, B = String.Empty, C = String.Empty,
                D = String.Empty, E = String.Empty,F = String.Empty,G = String.Empty,H = String.Empty,I = String.Empty,J = String.Empty,K = String.Empty,
                L = String.Empty, M = String.Empty,N = String.Empty,O = String.Empty,P = String.Empty,
                Remark = String.Empty, Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null) {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else{
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sSpecNo = It.J; // 初始化编号，测试项编号

            if (It.Flag == "1") // 新增工序
            {
                sOKFlag = TechBll.JudgeObjectExist(It.No, It.Name, "", sComp, "1");
                if (sOKFlag == "Y")  // 判断新增的信息是否存在了
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "3") //删除工序
            {
                sOKFlag = TechBll.JudgeObjectExist(It.No, "", "", sComp, "3");
                if (sOKFlag == "Y")  // 判断是否用于产品流程
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sOKFlag = TechBll.JudgeObjectExist(It.No, "", "", sComp, "3-1");
                if (sOKFlag == "Y")  // 判断是否用于产品流程
                {
                    Message = "Y_EXIST1";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sOKFlag = TechBll.JudgeObjectExist(It.No, "", "", sComp, "3-2");
                if (sOKFlag == "Y")  // 判断是否用于产品流程
                {
                    Message = "Y_EXIST2";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "4") //添加产品流程对应的工序，判断工序是否已存在
            {
                sOKFlag = TechBll.JudgeObjectExist(It.No, It.MNo, "", sComp, "4");
                if (sOKFlag == "Y")  // 判断是否用于产品流程
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                if (It.I == "是")  // 前台勾选了典型产品
                {
                    sOKFlag = TechBll.JudgeObjectExist(It.H, It.MNo, "", sComp, "4-1");
                    if (sOKFlag == "Y")  // 如果该工艺代号已设置标准流程，则不给再次设置，区分整机，主机
                    {
                        Message = "Y_EXISTDX";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }


                // 产品流程 判断当前工序的工序行为有没有选择 送检抽检合并 
                sOKFlag = TechBll.JudgeObjectExist(It.B, "", "", sComp, "30-2");
                if (sOKFlag == "Y")
                {
                    // 选择了送检抽检合并工序行为，检查抽样方式和抽样方案是否为空
                    if (It.N == "" || It.M == "")//It.M 抽样方式  It.N 抽样方案
                    {
                        Message = "Y_FAFSNULL";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }

                    if (It.M == "工序测试项抽样")//选择的是工序测试项抽样就需要判断是否有设置抽样的测试项
                    {
                        sOKFlag = TechBll.JudgeObjectExist(It.MNo, It.No, It.Item, sComp, "30-1");
                        if (sOKFlag == "N")
                        {
                            Message = "Y_NOTEST";
                            return JsonConvert.SerializeObject(new { Msg = Message });
                        }
                    }
                    else//选择的是工序抽样就需要判断是否有设置抽样的测试项,如果有就不能保存
                    {
                        sOKFlag = TechBll.JudgeObjectExist(It.MNo, It.No, It.Item, sComp, "30-3");
                        if (sOKFlag == "Y")
                        {
                            Message = "Y_ISTEST";
                            return JsonConvert.SerializeObject(new { Msg = Message });
                        }
                    }
                }
                else
                {
                    // 没有选择送检抽检合并工序行为，检查抽样方式和抽样方案是否不为空
                    if (It.N != "" || It.M != "")//It.M 抽样方式  It.N 抽样方案
                    {
                        Message = "Y_FAFSNOTNULL";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }

                    sOKFlag = TechBll.JudgeObjectExist(It.MNo, It.No, It.Item, sComp, "30-3");
                    if (sOKFlag == "Y")
                    {
                        Message = "Y_ISTEST";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }
            }
            else if (It.Flag == "5") //修改工艺流程
            {
                if (It.I == "是")  // 前台勾选了典型产品
                {
                    sOKFlag = TechBll.JudgeObjectExist(It.H, It.MNo, "", sComp, "4-1");
                    if (sOKFlag == "Y")  // 如果该工艺代号已设置标准流程，则不给再次设置，区分整机，主机
                    {
                        Message = "Y_EXISTDX";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }

                // 产品流程 判断当前工序的工序行为有没有选择 送检抽检合并 
                sOKFlag = TechBll.JudgeObjectExist(It.B, "", "", sComp, "30-2");
                if (sOKFlag == "Y")
                {
                    // 选择了送检抽检合并工序行为，检查抽样方式和抽样方案是否为空
                    if (It.N == "" || It.M == "")//It.M 抽样方式  It.N 抽样方案
                    {
                        Message = "Y_FAFSNULL";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }

                    if (It.M == "工序测试项抽样")//选择的是工序测试项抽样就需要判断是否有设置抽样的测试项
                    {
                        sOKFlag = TechBll.JudgeObjectExist(It.MNo, It.No, It.Item, sComp, "30-1");
                        if (sOKFlag == "N")
                        {
                            Message = "Y_NOTEST";
                            return JsonConvert.SerializeObject(new { Msg = Message });
                        }
                    }
                    else//选择的是工序抽样就需要判断是否有设置抽样的测试项,如果有就不能保存
                    {
                        sOKFlag = TechBll.JudgeObjectExist(It.MNo, It.No, It.Item, sComp, "30-3");
                        if (sOKFlag == "Y")
                        {
                            Message = "Y_ISTEST";
                            return JsonConvert.SerializeObject(new { Msg = Message });
                        }
                    }
                }
                else
                {
                    // 没有选择送检抽检合并工序行为，检查抽样方式和抽样方案是否不为空
                    if (It.N != "" || It.M != "")//It.M 抽样方式  It.N 抽样方案
                    {
                        Message = "Y_FAFSNOTNULL";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }

                    sOKFlag = TechBll.JudgeObjectExist(It.MNo, It.No, It.Item, sComp, "30-3");
                    if (sOKFlag == "Y")
                    {
                        Message = "Y_ISTEST";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }

            }
            else if (It.Flag == "9")  //删除产品流程：用于生产不能删除 20220825：产品流程独立于工单，序列号流程，因为工单，序列流程直接继承了产品流程
            {
                //sOKFlag = TechBll.JudgeObjectExist(It.MNo, "", "", sComp, "9");
                //if (sOKFlag == "Y")  //  
                //{
                //    Message = "Y_EXIST";
                //    return JsonConvert.SerializeObject(new { Msg = Message });
                //}
            }
            else if (It.Flag == "9-0-3") //产品工艺流程-拷贝工序版本信息到新的工序版本
            {
                string sPNVer = It.No + It.Item;  // 工序+版本
                sOKFlag = TechBll.JudgeObjectExist(It.MNo, sPNVer, "", sComp, "9-1");
                if (sOKFlag == "N"){  // 判断新的工序版本是否存在，如果不存在，则不给拷贝  =N 说明不存在，或者禁用了
                    Message = "Y_EXSITstu";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "9-1-1") || (It.Flag == "9-1-2")) //产品工艺流程-修改，删除设备，判断是否禁用
            {
                string sPNVer = It.No + It.Item;  // 工序+版本
                sOKFlag = TechBll.JudgeObjectExist(It.MNo, sPNVer, "", sComp, "9-0");
                if (sOKFlag == "Y"){  // =Y 已禁用
                    Message = "Y_Status";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "9-2-1")|| (It.Flag == "9-2-4")) //产品工艺流程-新增工艺文件 判断工艺文件是否存在 9-2-1:新增  9-2-4：选择
            {
                string sPNVer = It.No + It.Item;  // 工序+版本
                string sFNVer = It.C + It.D;  // 工艺文件+版本

                sOKFlag = TechBll.JudgeObjectExist(It.MNo, sPNVer, sFNVer, sComp, "9-2");
                if (sOKFlag == "Y")  //  已存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                sOKFlag = TechBll.JudgeObjectExist(It.MNo, sPNVer, "", sComp, "9-0");
                if (sOKFlag == "Y"){  // =Y 已禁用
                    Message = "Y_Status";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "9-2-2")|| (It.Flag == "9-2-3")) //产品工艺流程-修改，删除工艺文件，判断是否禁用
            {
                string sPNVer = It.No + It.Item;  // 工序+版本
                sOKFlag = TechBll.JudgeObjectExist(It.MNo, sPNVer, "", sComp, "9-0");
                if (sOKFlag == "Y"){  // =Y 已禁用
                    Message = "Y_Status";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "9-3-1")|| (It.Flag == "9-3-5")) //产品工艺流程-新增测试项 9-3-1:新增，增加在后面；9-3-5：插入新测试项
            {
                sSpecNo = CreateSpecNo();
                string sPNVer = It.No + It.Item;  // 工序+版本
                string sHB = It.A + It.B;  // 检验项目+要求不重复
                sOKFlag = TechBll.JudgeObjectExist(It.MNo, sPNVer, sHB, sComp, "9-3");
                if (sOKFlag == "Y")  //  已存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sOKFlag = TechBll.JudgeObjectExist(It.MNo, sPNVer, "", sComp, "9-0");
                if (sOKFlag == "Y"){  // =Y 已禁用
                    Message = "Y_Status";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "9-3-2") || (It.Flag == "9-3-3") || (It.Flag == "9-3-4") || (It.Flag == "9-3-8")) //产品工艺流程-新增测试项 判断这个工序版本是否已禁用
            {
                string sPNVer = It.No + It.Item;  // 工序+版本

                sOKFlag = TechBll.JudgeObjectExist(It.MNo, sPNVer, "", sComp, "9-0");
                if (sOKFlag == "Y"){  // =Y 已禁用
                    Message = "Y_Status";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "9-3-6") //产品工艺流程-选择测试项 判断这个工序版本是否已禁用
            {
                string sPNVer = It.No + It.Item;  // 工序+版本

                sOKFlag = TechBll.JudgeObjectExist(It.MNo, sPNVer, "", sComp, "9-0");
                if (sOKFlag == "Y"){  // =Y 已禁用
                    Message = "Y_Status";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sOKFlag = TechBll.JudgeObjectExist(It.MNo, sPNVer, It.A, sComp, "9-3");
                if (sOKFlag == "Y")  //  已存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            //else if (It.Flag == "9-3-9") //产品工艺流程-判断当前测试是否设置工序测试项抽样
            //{
            //    sOKFlag = TechBll.JudgeObjectExist(It.MNo, It.No, It.Item, sComp, "30-4");
            //    if (sOKFlag == "N")  //  未设置
            //    {
            //        Message = "Y_OKTEST";
            //        return JsonConvert.SerializeObject(new { Msg = Message });
            //    }
            //}
            else if (It.Flag == "10")  //新增工作中心
            {
                sOKFlag = TechBll.JudgeObjectExist(It.No, It.MNo, "", sComp, "10");
                if (sOKFlag == "Y")  //  
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "12")  //删除工作中心
            {
                sOKFlag = TechBll.JudgeObjectExist(It.MNo, "", "", sComp, "12");
                if (sOKFlag == "Y")  //  判断这个删除编码下面有没有其他子信息
                {
                    Message = "Y_EXISTCW";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "16")  //新增工序行是否存在
            {
                sOKFlag = TechBll.JudgeObjectExist(It.Name, "", "", sComp, "16");
                if (sOKFlag == "Y")  //  
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "18")  //删除工序行为是，判断是否被引用了
            {
                sOKFlag = TechBll.JudgeObjectExist(It.No, "", "", sComp, "18");
                if (sOKFlag == "Y")  //  
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "22")  //删除员工对应的作业单号
            {
                //sOKFlag = TechBll.JudgeObjectExist(It.No, "", "", sComp, "22");
                //if (sOKFlag == "Y")  //  已有生产记录，不能删除
                //{
                //    Message = "Y_EXISTPRD";
                //    return JsonConvert.SerializeObject(new { Msg = Message });
                //}
            }
            else if (It.Flag == "25")  //判断新增的异常代码是否存在
            {
                sOKFlag = TechBll.JudgeObjectExist(It.No, "", "", sComp, "25");
                if (sOKFlag == "Y")  //  已存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "28-1") || (It.Flag == "28-2")) //测试项库-新增、修改测试项 
            {
                if (It.Flag == "28-1"){
                    sSpecNo = CreateSpecNo();
                }

                string sHB = It.A + It.B;
                sOKFlag = TechBll.JudgeObjectExist(It.J, It.No+It.Item, sHB, sComp, "28-1");
                if (sOKFlag == "Y")  //  已存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }





            sOKFlag = TechBll.OPTechInfo(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, sSpecNo, It.K, It.L, It.M, It.N, It.O, It.P, sComp, sLogin, It.Remark, It.Flag, IP);
            //if (sOKFlag.IndexOf("SQ")>0)  //  sstr = "SQ_"+iMax.ToString();
            if (sOKFlag.Length <= 10)
            {
                Message = "Success";
                int m = sOKFlag.IndexOf("SQ");
                SeqNo = sOKFlag.Substring(m + 3, sOKFlag.Length - m - 3);
            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo,SeqNo= SeqNo });

            return Result;
        }
        #endregion



        #region 获取工序的下拉列表
        public string GetProcedureList(string Kind, string MNo,string Item, string sFlag)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sComp = string.Empty;
            string sTNo = string.Empty;
            string sTName = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            DataTable dt = TechBll.GetTechInfo("", Item, "", MNo, "", "", "", "", "", "", "", "", 50000, 1, "", sComp, sFlag);
            if (dt.Rows.Count > 0)
            {

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    sTNo = dt.Rows[i]["PNName"].ToString();
                    sTName = dt.Rows[i]["PNName"].ToString();

                    Message += "<option value='" + sTNo + "'>" + sTName + "</option>";
                }
            }
            else
            {
                Message = "Error";
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;

        }
        #endregion


        #region 获取工序行为的下拉列表
        public string GetProcedureActionList(string Kind, string sFlag)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sComp = string.Empty;
            string sTNo = string.Empty;
            string sTName = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            DataTable dt = TechBll.GetTechInfo("", "", "", "", "", "", "", "", "", "", "", "", 50000, 1, "", sComp, "111-2");
            if (dt.Rows.Count > 0)
            {

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    sTNo = dt.Rows[i]["PNName"].ToString();
                    sTName = dt.Rows[i]["PNName"].ToString();

                    Message += "<option value='" + sTNo + "'>" + sTName + "</option>";
                }
            }
            else
            {
                Message = "Error";
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;

        }
        #endregion



        #region 在流程增加工序，选择作业单元时：加载作业单号下的工厂
        public string GetWorkUnitConList(string Kind, string sFlag,string sCNo)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sComp = string.Empty;
            string sTNo = string.Empty;
            string sTName = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null){
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else{
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            DataTable dt = TechBll.GetTechInfo(sCNo, Kind, "", "", "", "", "", "", "", "", "", "", 50000, 1, "", sComp, "111-3-1");
            if (dt.Rows.Count > 0){

                for (int i = 0; i < dt.Rows.Count; i++){
                    sTNo = dt.Rows[i]["WNo"].ToString();
                    sTName = dt.Rows[i]["PNName"].ToString();

                    Message += "<option value='" + sTNo + "'>" + sTName + "</option>";
                }
            }
            else{
                Message = "Error";
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;

        }
        #endregion


        #region 获取作业单元
        public string GetWorkUnitInfo(string Params,string sSPNo, string sCNo, string Item, string sMNo, string SPNo, string TNo,string Kind, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sComp = string.Empty;
            string sLogin = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null){
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else{
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            if (SPNo!=null && SPNo != "")
            {
                sLogin = SPNo;
            }

            DataTable dt = TechBll.GetTechInfo(sCNo, Item, "", sMNo, "", "", "", "", sSPNo, TNo, "", Kind, 50000, 1, sLogin, sComp, Flag);  //  
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;
            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;

        }
        #endregion



        #region 获取产品编码+工序对应模板 下拉列表
        public string GetLableTemplateList(string PNo, string Proc, string sFlag)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sComp = string.Empty;
            string sTNo = string.Empty;
            string sTName = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null) {
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            DataTable dt = TechBll.GetTechInfo("", Proc, "", PNo, "", "", "", "", "", "", "", "", 50000, 1, "", sComp, "28-10");
            if (dt.Rows.Count > 0){
                for (int i = 0; i < dt.Rows.Count; i++){
                    sTNo = dt.Rows[i]["TPPath"].ToString();  // 标贴模板路径  
                    sTName = dt.Rows[i]["TemplateName"].ToString() +"&*"+ dt.Rows[i]["Label"].ToString(); // 标贴模板名称 + 标贴编码


                    Message += "<option value='" + sTNo + "'>" + sTName + "</option>";
                }
            }
            else{
                Message = "Error";
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;

        }
        #endregion




        #region 根据下拉的名称，获取描述信息TIPS
        public string GetActListTips(string Kind, string sFlag)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sComp = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null){
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            DataTable dt = TechBll.GetTechInfo("", Kind, "", "", "", "", "", "", "", "", "", "", 5000, 1, "", sComp, sFlag);
            if (dt.Rows.Count > 0){
                Message = dt.Rows[0]["ActDesc"].ToString();
            }
            else{
                Message = "";
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;

        }
        #endregion


        #region 获取样本量字码
        public void GetSampleSizeCode(int limit, int page, string Flag)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            int count = 0;

            HttpContext context = HttpContext.Current;

            if (context.Session["LoginName"] != null)
            {
                sMan = context.Session["LoginName"].ToString();
                sManName = context.Session["FullName"].ToString();
                sComp = context.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return;
            }

            DataTable dt = TechBll.GetSampleSizeCode(limit, page, Flag, sMan);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                count = Convert.ToInt32(dt.Rows[0]["numCount"].ToString());
                string Json = JsonConvert.SerializeObject(dt);
                Result = "{\"code\":0,\"msg\":\"\",\"count\":" + count + ",\"data\":" + Json + "}";
            }
            else
            {
                Message = "Error";
            }

            context.Response.Write(Result);
        }
        #endregion


        #region 操作样本量字码
        public string OPSampleSizeCodeInfo(string DataParams)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;

            string sSCNo = string.Empty;

            string sStrictness = string.Empty;
            string sBatch = string.Empty;
            string sBatch_start = string.Empty;
            string sBatch_end = string.Empty;
            string sInspection_level = string.Empty;
            string sSample_code = string.Empty;
            string sRemark = string.Empty;


            string sPresentOrder = string.Empty;
            string sOPFlag = string.Empty;
            string sFlag = string.Empty;

            var AnonymousUser = new
            {
                SCNo = String.Empty,
                Strictness = String.Empty,
                Batch = String.Empty,
                Batch_start = String.Empty,
                Batch_end = String.Empty,
                Inspection_level = String.Empty,
                Sample_code = String.Empty,
                Remark = String.Empty,
                PresentOrder = String.Empty,
                OPFlag = String.Empty,
                Flag = String.Empty

            };


            var Item1 = JsonConvert.DeserializeObject(DataParams);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item1), AnonymousUser);
            sFlag = It.Flag; sSCNo = It.SCNo; sPresentOrder = It.PresentOrder; sOPFlag = It.OPFlag;
            sStrictness = It.Strictness; sBatch = It.Batch; sBatch_start = It.Batch_start; sRemark = It.Remark;
            sBatch_end = It.Batch_end; sInspection_level = It.Inspection_level; sSample_code = It.Sample_code;


            HttpContext context = HttpContext.Current;
            if (context.Session["LoginName"] != null)
            {
                sMan = context.Session["LoginName"].ToString();
                sManName = context.Session["FullName"].ToString();
                sComp = context.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                return JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
            }

            if (sFlag == "1")
            {
                sSCNo = CreateSCNo();
            }



            sOKFlag = TechBll.OPSampleSizeCodeInfo(sSCNo, sStrictness, sBatch, sBatch_start, sBatch_end, sInspection_level, sSample_code, sComp, sMan, sRemark, sPresentOrder, sOPFlag, sFlag);

            if (Convert.ToInt32(sOKFlag) > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }

            var json = new { code = "0", msg = Message };

            Result = JsonConvert.SerializeObject(json);

            return Result;
        }
        #endregion


        #region 获取抽样标准
        public void GetSamplingStd(int limit, int page, string Flag)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            int count = 0;

            HttpContext context = HttpContext.Current;

            if (context.Session["LoginName"] != null)
            {
                sMan = context.Session["LoginName"].ToString();
                sManName = context.Session["FullName"].ToString();
                sComp = context.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return;
            }

            DataTable dt = TechBll.GetSamplingStd(limit, page, Flag, sMan);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                count = Convert.ToInt32(dt.Rows[0]["numCount"].ToString());
                string Json = JsonConvert.SerializeObject(dt);
                Result = "{\"code\":0,\"msg\":\"\",\"count\":" + count + ",\"data\":" + Json + "}";
            }
            else
            {
                Message = "Error";
            }

            context.Response.Write(Result);
        }


        #endregion


        #region 操作抽样标准
        public string OPSamplingStd(string DataParams)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;

            string sSSNo = string.Empty;

            string sSampleCode = string.Empty;
            string sAql = string.Empty;
            string sStringency = string.Empty;
            string sSampleSize = string.Empty;
            string sRNum = string.Empty;
            string sBNum = string.Empty;
            string sRemark = string.Empty;


            string sPresentOrder = string.Empty;
            string sOPFlag = string.Empty;
            string sFlag = string.Empty;

            var AnonymousUser = new
            {
                SSNo = String.Empty,
                SampleCode = String.Empty,
                Aql = String.Empty,
                Stringency = String.Empty,
                SampleSize = String.Empty,
                RNum = String.Empty,
                BNum = String.Empty,
                Remark = String.Empty,
                PresentOrder = String.Empty,
                OPFlag = String.Empty,
                Flag = String.Empty

            };


            var Item1 = JsonConvert.DeserializeObject(DataParams);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item1), AnonymousUser);
            sFlag = It.Flag; sSSNo = It.SSNo; sPresentOrder = It.PresentOrder; sOPFlag = It.OPFlag;
            sSampleCode = It.SampleCode; sAql = It.Aql; sStringency = It.Stringency; sRemark = It.Remark;
            sSampleSize = It.SampleSize; sRNum = It.RNum; sBNum = It.BNum;


            HttpContext context = HttpContext.Current;
            if (context.Session["LoginName"] != null)
            {
                sMan = context.Session["LoginName"].ToString();
                sManName = context.Session["FullName"].ToString();
                sComp = context.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                return JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
            }

            if (sFlag == "1")
            {
                sSSNo = CreateSSNo();
            }



            sOKFlag = TechBll.OPSamplingStd(sSSNo, sSampleCode, sAql, sStringency, sSampleSize, sRNum, sBNum, sComp, sMan, sRemark, sPresentOrder, sOPFlag, sFlag);

            if (Convert.ToInt32(sOKFlag) > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }

            var json = new { code = "0", msg = Message };

            Result = JsonConvert.SerializeObject(json);

            return Result;
        }
        #endregion


        #region 获取抽样方案
        public static void GetSamplingPlan(int limit, int page, string DataParams, string Flag)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sComp = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sMNo = string.Empty;
            string sMName = string.Empty;

            HttpContext context = HttpContext.Current;

            var AnonymousUser = new
            {
                MNo = String.Empty,
                MName = String.Empty,
            };

            if (string.IsNullOrEmpty(DataParams) || (DataParams == null) || (DataParams == "null"))
            {
                sMNo = ""; sMName = "";
            }
            else
            {
                var Item1 = JsonConvert.DeserializeObject(DataParams);
                var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item1), AnonymousUser);
                sMNo = It.MNo; sMName = It.MName;
            }

            if (context.Session["LoginName"] != null)
            {
                sComp = context.Session["CompanyNo"].ToString();
                sMan = context.Session["LoginName"].ToString();
                sManName = context.Session["FullName"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
            }

            DataTable dt = TechBll.GetSamplingPlan(limit, page, sMan, sMNo, sMName, Flag);

            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                int count = Convert.ToInt32(dt.Rows[0]["numCount"].ToString());
                string Json = JsonConvert.SerializeObject(dt);
                Result = "{\"code\":0,\"msg\":\"\",\"count\":" + count + ",\"data\":" + Json + "}";
            }
            else
            {
                Message = "Error";
            }

            context.Response.Write(Result);
        }

        #endregion

        #region 操作抽样方案
        public static string OPSamplingPlan(string DataParams)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;

            string sSPNo = string.Empty;
            string sSPName = string.Empty;
            string sType = string.Empty;
            string sInspectLevel = string.Empty;
            string sStringency = string.Empty;
            string sAql = string.Empty;

            string sOPFlag = string.Empty;
            string sFlag = string.Empty;

            var AnonymousUser = new
            {
                SPNo = String.Empty,
                SPName = String.Empty,
                Type = String.Empty,
                InspectLevel = String.Empty,
                Stringency = String.Empty,
                Aql = String.Empty,
                OPFlag = String.Empty,
                Flag = String.Empty,
            };


            var Item1 = JsonConvert.DeserializeObject(DataParams);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item1), AnonymousUser);
            sSPNo = It.SPNo;
            sSPName = It.SPName;
            sType = It.Type;
            sInspectLevel = It.InspectLevel;
            sStringency = It.Stringency;
            sAql = It.Aql;
            sOPFlag = It.OPFlag;
            sFlag = It.Flag;


            HttpContext context = HttpContext.Current;

            if (context.Session["LoginName"] != null)
            {
                sMan = context.Session["LoginName"].ToString();
                sManName = context.Session["FullName"].ToString();
                sComp = context.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                return JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
            }

            if (sOPFlag == "Add")
            {
                sSPNo = CreateSPNo();
            }


            sOKFlag = TechBll.OPSamplingPlan(sSPNo, sSPName, sType, sInspectLevel, sStringency, sAql, sComp, sMan, sFlag, sOPFlag);

            if (Convert.ToInt32(sOKFlag) > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }

            var json = new { code = "0", msg = Message };

            Result = JsonConvert.SerializeObject(json);

            return Result;
        }
        #endregion



        // 产品流程：按工序打印DHR报表，
        public static string GetExportConditions(string DataParams)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;


            var AnonymousUser = new
            {
                No = String.Empty,Name = String.Empty,Item = String.Empty,MNo = String.Empty,MName = String.Empty,A = String.Empty,B = String.Empty,
                C = String.Empty,D = String.Empty,E = String.Empty,F = String.Empty,Remark = String.Empty,Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(DataParams);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            // GetExportConditions(sNo, A, B, C, sInMan, Comp, Flag);
           // var dt = OrderBll.GetExportConditions(It.No, It.A, sMan, sComp, It.Flag);
            var dt = TechBll.GetExportConditions(It.No, It.A, "", "", sMan, sComp, It.Flag);

            Result = JsonConvert.SerializeObject(dt);


            return Result;
        }



        // 产品流程：按工序打印DHR报表，

        public static void GetExportData(string DataParams, HttpContext context)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sIsPass = string.Empty;
            string sComp = string.Empty;
            var finalConclusion = "通过";  // 默认DHR报表通过
            var sData1 = new DataTable();
            var sData2 = new DataTable();
            var sData3 = new DataTable();
            var sData4 = new DataTable();
            var sData5 = new DataTable();
            var sData6 = new DataTable();
            var sData7 = new DataTable();


            var AnonymousUser = new
            {
                No = String.Empty,Name = String.Empty,Item = String.Empty,MNo = String.Empty,MName = String.Empty,A = String.Empty,B = String.Empty,
                C = String.Empty,D = String.Empty,E = String.Empty, F = String.Empty,Remark = String.Empty,Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(DataParams);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }

            sData1 = TechBll.GetTechInfo(It.No, "1", "", "", "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//设备及工装
            sData2 = TechBll.GetTechInfo(It.No, "2", "", "", "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//试剂/质控品信息
            sData3 = TechBll.GetTechInfo(It.No, "3", "", "", "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//追溯物料信息
            sData4 = TechBll.GetTechInfo(It.No, "4", "", "", "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//测试信息
            sData5 = TechBll.GetTechInfo(It.No, "5", "", "", "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//基本信息   表头
            sData6 = TechBll.GetTechInfo(It.No, "6", "", "", "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//检验方式    表头
            sData7 = TechBll.GetTechInfo(It.No, "7", "", "", "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//检验方式

            // 格式化，符合锐浪报表的数据格式
            Result = JsonConvert.SerializeObject(new
            {
                Table1 = sData1,
                Table2 = sData2,
                Table3 = sData3,
                Table4 = sData4,
                Table5 = sData5,
                Table6 = sData6,
                Table7 = sData7,

                //结论
                FinalConclusion = finalConclusion
            });

            GeneratePDF(context, Result, "DHR", It.No, "", It.A, It.B, sComp);
        }

        public static void GeneratePDF(HttpContext context, string data, string OPFlag, string PN, string SNo, string ProcName, string sProcNo, string Comp) 
        {
            GridppReportServer Report = new GridppReportServer();

            //首先载入报表模板文件
            string ReportPathFile = string.Empty;

            //文件名称
            string fileName = string.Empty;

            //如果是等于DHR就是DHR报表，否则就是汇总报表
            if (OPFlag == "DHR")
            {
                //使用服务器的相对路径
                ReportPathFile = context.Server.MapPath("/Template/" + Comp + "/LabelPrint/DHR.grf");//根据WEB服务器根目录寻址

                //DHR文件名称 工序编号 - 工序名称
                fileName = sProcNo + "-" + ProcName + ".pdf";
            }
            else
            {
                //使用服务器的相对路径
                ReportPathFile = context.Server.MapPath("/Template/" + Comp + "/LabelPrint/DHR(HZ).grf");//根据WEB服务器根目录寻址

                //汇总页文件名称 序列号
                fileName = SNo + ".pdf";
            }


            bool Success = Report.LoadFromFile(ReportPathFile);


            if (!Success)
            {
                ServerUtility.ResponseException(context, "载入报表模板失败。");
                return;
            }

            //载入报表数据，为约定格式的 XML 或 JSON 文本数据
            string reportDataText = data;

            Report.LoadDataFromXML(reportDataText);

            //生成PDF文档数据，出发路径：DHRFile
            IGRBinaryObject PDFDataObject = Report.ExportDirectToBinaryObject(GRExportType.gretPDF);

            //将生成的数据响应给客户端
            if (PDFDataObject.DataSize > 0)
                ServerUtility.ResponseBinary(context, PDFDataObject, fileName, PN, SNo, "", "", "", "", "", "", "", "","", "", "application/pdf", "attachment", "PN"); //attachment ： inline
            else
                ServerUtility.ResponseException(context, "报表生成失败");
        }


        #region 拷贝文件，临时使用，后面删除
        public static string COPYFILE(string DataParams)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sOKFlag = string.Empty;
            string sOPFlag = string.Empty;
            string sFlag = string.Empty;


            // { SPNo: sSN, SPName: "", Path: sPath, A: sIP, B: sZH, C: sPWD, OPFlag: "", Flag: "1" }
            var AnonymousUser = new
            {
                SPNo = String.Empty,
                SPName = String.Empty,
                Path = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                OPFlag = String.Empty,
                Flag = String.Empty,
            };

            
            var Item1 = JsonConvert.DeserializeObject(DataParams);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item1), AnonymousUser);

            if (It.Flag == "1") {  // 没有域名那种的
                string sReturnFile = "\\ExcelFile\\" + It.SPNo + DateTime.Now.ToString("yyyymmddss") + ".xls";
                //string sFile = System.AppDomain.CurrentDomain.BaseDirectory + "\\ExcelFile\\测试记录.xls";
                string sFile = It.Path + "\\" + It.SPNo + "_PASS.xls";  // var sPath = sSerial + "_PASS.xls";  // YG-3B000811_PASS.xls
                string sExcelFilePath = System.AppDomain.CurrentDomain.BaseDirectory + sReturnFile;
                System.IO.File.Copy(sFile, sExcelFilePath, true);
            }
            else // 需要输入域账号才能访问那种
            {
                using (IdentityScope c = new IdentityScope(It.B, It.A, It.C))
                {
                    string sReturnFile = "\\ExcelTestFile\\" + It.SPNo + DateTime.Now.ToString("yyyymmddss") + ".xls";
                    //string sFile = System.AppDomain.CurrentDomain.BaseDirectory + "\\ExcelFile\\测试记录.xls";
                    string sFile = It.Path + "\\" + It.SPNo + "_PASS.xls";  // var sPath = sSerial + "_PASS.xls";  // YG-3B000811_PASS.xls
                    string sExcelFilePath = System.AppDomain.CurrentDomain.BaseDirectory + sReturnFile;
                    System.IO.File.Copy(sFile, sExcelFilePath, true);
                }
            }

            var json = new { code = "0", msg = Message };

            Result = JsonConvert.SerializeObject(json);

            return Result;
        }
        #endregion



        public  void InitializeMater(string DataParams)
        {
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string Message= string.Empty;
            HttpContext context = HttpContext.Current;


            var AnonymousUser = new
            {
                No = String.Empty,
                SNo = String.Empty,
                MNo = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(DataParams);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                context.Response.Write(JsonConvert.SerializeObject(new { Msg = "LoginError", Login = sManName }));
            }

            if (It.Flag== "PF")
            {
                sOKFlag = TechBll.JudgeObjectExist(It.A, "", "", sComp, "29");
                if (sOKFlag == "Y")  //判断更新的产品代号典型流程有没有被禁用
                {
                    Message = "Y_FlowDisable";
                    context.Response.Write(JsonConvert.SerializeObject(new { Msg = Message }));
                    return;
                }
                sOKFlag = TechBll.JudgeObjectExist(It.A, "", "", sComp, "29-1");
                if (sOKFlag == "Y")// 产品流程 判断典型流程当前使用版本是否被禁用
                {
                    Message = "Y_VerDisable";
                    context.Response.Write(JsonConvert.SerializeObject(new { Msg = Message }));
                    return;
                }
            }


            Message = TechBll.InitializeMater(It.No, It.SNo, It.MNo, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.Remark, sMan, It.Flag,sComp,IP);

            context.Response.Write(JsonConvert.SerializeObject(new { Msg = Message }));
        }


        #region 根据产品编码，工序，工序版本获取信息
        public string GetProductFlowBaseInfo(string sSSNo, string sItem, string sMNo, string Kind, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sKFlag = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            DataTable dt = TechBll.GetTechInfo(sSSNo, sItem, "", sMNo, "", "", "", "", "", "", "", "", 5000, 1, sMan, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion
















        #region  生成测试项属性编号 S23090900001
        public static string CreateSpecNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_TestItemBaseInfo where convert(char(10),InDate,120)='" + sDate + "' ", "SpecNo");// S230909 00001
            if (sMaxNo == "")
            {
                sNo = "S" + CreateAllNo.CreateBillNo(2, 4) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(7, 5);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 5 - len;

                sNo = "S" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion


        #region 生成样本量字码编码  SC23100001
        public static string CreateSCNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_SampleSizeCode where convert(char(7),InDate,120)='" + sDate + "' ", "SCNo");// SC 23 10 0001
            if (sMaxNo == "")
            {
                sNo = "SC" + CreateAllNo.CreateBillNo(4, 3) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(6, 4);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 4 - len;

                sNo = "SC" + CreateAllNo.CreateBillNo(4, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion


        #region 生成采样标准编码  SS23100001
        public static string CreateSSNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_SamplingStd where convert(char(7),InDate,120)='" + sDate + "' ", "SSNo");// SS 23 10 0001
            if (sMaxNo == "")
            {
                sNo = "SS" + CreateAllNo.CreateBillNo(4, 3) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(6, 4);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 4 - len;

                sNo = "SS" + CreateAllNo.CreateBillNo(4, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion


        #region 生成抽样方案编码  CYFA001
        public static string CreateSPNo()
        {
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_SamplingPlan", "SPNo");// CYFA 001
            if (sMaxNo == "")
            {
                sNo = "CYFA001";
            }
            else
            {
                string sTemp = sMaxNo.Substring(4);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 3 - len;
                sNo = "CYFA" + CreateAllNo.GetZoreNumString(i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion





        #region  生成发料编号   F2104230001  CreateInStockNo
        public static string CreateFillNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_MaterFillInfo where convert(char(10),InDate,120)='" + sDate + "' ", "FillNo");//  F210423 0001
            if (sMaxNo == "")
            {
                sNo = "F" + CreateAllNo.CreateBillNo(2, 3) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(7, 4);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 4 - len;

                sNo = "F" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion















        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}
