@charset "utf-8";
/* CSS Document */

*{
	margin:0;
	padding:0;
	font-family:"微软雅黑";}
::-webkit-scrollbar-track{border-radius: 10px;-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0);}/*滚动条的滑轨背景颜色*/
::-webkit-scrollbar-thumb{background-color: rgba(0,0,0,0.05);border-radius: 10px;-webkit-box-shadow: inset 1px 1px 0 rgba(0,0,0,.1);}/*滑块颜色*/
::-webkit-scrollbar-thumb{background-color: rgba(0,0,0,0.2);border-radius: 10px;-webkit-box-shadow: inset 1px 1px 0 rgba(0,0,0,.1);}
::-webkit-scrollbar{width: 16px;height: 16px;}/* 滑块整体设置*/
::-webkit-scrollbar-track,
::-webkit-scrollbar-thumb{border-radius: 999px;border: 5px solid transparent;}
::-webkit-scrollbar-track{box-shadow: 1px 1px 5px rgba(0,0,0,.2) inset;}
::-webkit-scrollbar-thumb{min-height: 20px;background-clip: content-box;box-shadow: 0 0 0 5px rgba(0,0,0,.2) inset;}
::-webkit-scrollbar-corner{background: transparent;}/* 横向滚动条和纵向滚动条相交处尖角的颜色 */
.clear{
	clear: both;
}
.map_find{
padding-top: 5px;
	padding-bottom: 5px;
	border-bottom: 1px solid #CCCCCC;
	font-size: 13px;
	padding-left: 10px;
}
.map_find1{
	padding-top: 10px;

	border-bottom: 1px solid #CCCCCC;
	font-size: 13px;
	padding-left: 10px;
}
.map_find label{
	margin-right: 5px;
}
.map_input{
	width: 220px;
	height: 24px;
	border-radius: 3px;
	border:1px solid #CCCCCC;
	margin-right: 10px;
}
.find_but {
	padding-left: 8px;
	padding-right: 8px;
	padding-top: 3px;
	padding-bottom: 4px;
	background-color: #56dcaf;
	color: #ffffff;
	border: none;
	border-radius: 3px;
}
.find_but:active{
	background-color: #1c6a9e;
}
.map_con{
	width: 100%;
	height: 100%;
	background-image: url("../img/map01.png");
	position: absolute;
}
.map_tail{
	width: 600px;
	height: 350px;
	position:relative;
	margin: 0 auto;
	margin-top: 50px;
	background-color: #ffffff;
	box-shadow: 5px 5px 3px #333;
	border-radius: 5px;


}
.map_h{
	padding-top: 10px;
	padding-bottom: 5px;
	text-align: center;
	border-bottom: 1px solid #CCCCCC;
	width: 96%;
	margin-left: 2%;
}
.map_table{
	width: 96%;
	margin-left: 2%;
	border:none;
	font-size: 13px;
	color: #262626;
}
.map_mos{
	text-decoration: none;
	padding-top: 3px;
	padding-left: 10px;
	padding-right: 10px;
	padding-bottom: 4px;
	color: #ffffff;
	background-color: #0D8BBD;
	border:none;
	border-radius: 3px;
}
.map_mos:active{
	background-color: #00a0e9;
		 }
.map_video{
	text-decoration: none;
	padding-top: 3px;
	padding-left: 10px;
	padding-right: 10px;
	padding-bottom: 4px;
	color: #ffffff;
	background-color: #00aa00;
	border:none;
	border-radius: 3px;
}
.map_video:active{
	background-color: #00e765;
}
.map_p01{
	padding-top: 15px;
	padding-bottom: 5px;
	border-bottom: 1px solid #CCCCCC;
	width: 96%;
	margin-left: 2%;
	font-size: 12px;
	color: #666666;
}
.map_p02{
	width: 96%;
	margin-left: 2%;
	height: 170px;
	margin-top: 5px;

}
.map_p02 img{
	width: 100%;
	height: 100%;
}
.map_close{
	width: 20px;
	height: 20px;
	position: absolute;
	right: -5px;
	top:-5px;
}
.p_but{
	padding-top: 0px;
	padding-left: 5px;
	font-size: 12px;
}
.p_but a{
margin-right: 10px;}
#table{
	font-size: 13px;
}
#table a{
	margin-right: 10px;
}
#table th{
	border-bottom: 1px solid #0D8BBD;
	background-color: #EEEEEE;

}
.add2_i{
	width: 16px;
	height: 16px;
	position: relative;
	display: inline-block;
	background-image: url("../img/opne.png");
	top:3px;
	margin-right: 2px;
}
.add_i{
	width: 16px;
	height: 16px;
	position: relative;
	display: inline-block;
	background-image: url("../img/table_add.png");
	top:3px;
	margin-right: 2px;
}
.del_i{
	width: 16px;
	height: 16px;
	position: relative;
	display: inline-block;
	background-image: url("../img/table_del.png");
	top:3px;
	margin-right: 1px;
}
.down_i{
	width: 16px;
	height: 16px;
	position: relative;
	display: inline-block;
	background-image: url("../img/table_down.png");
	top:3px;
	margin-right: 1px;
}
.print_i{
	width: 16px;
	height: 16px;
	position: relative;
	display: inline-block;
	background-image: url("../img/table_print.png");
	top:3px;
	margin-right: 1px;
}
.div_find {
	/*padding-top: 10px;*/
	/*padding-bottom: 10px;*/
	width: 100%;
	/*background-color:#c5e0fb ;*/

	padding-bottom: 0px;
	/*padding: 5px 0 5px 5px;*/
	padding: 0px 0 8px 5px;
	top: 5px;
	font-size: 12px;
}
.find_labela {
	/*width: 5%; */
	text-align: right;
	font-size: 12px;
	display: inline-block;
	color: #4b545d;
}
.find_input{
	width: 13%;
	height: 25px;
	border:solid 1px #ccc;
	border-radius: 3px;
	margin-right: 1%;

}
.find_but {
	padding-left: 9px;
	padding-bottom: 4px;
	padding-top: 4px;
	padding-right: 8px;
	background-color: #56dcaf;
	color: #ffffff;
	border-radius: 5px;
	border: none;
	margin-right: 10px;
}
.find_but:active{
	background-color: #00a0e9;
	border: none;}
.find_but1{
	padding-left: 9px;
	padding-bottom: 4px;
	padding-top: 4px;
	padding-right: 8px;
	background-color:#2e8ded;
	color: #ffffff;
	border-radius: 5px;
	border: none;
}
.find_but1:active{
	background-color: #044588;
}
.find_span{
	float: right;
	margin-right: 15px;
	margin-top: 5px;
	cursor:pointer;
	font-size: 13px;
}
.find_span a{
	text-decoration: none;
	
	color: #333;
}
.find_span1{
	float: right;
	margin-right: 15px;
	margin-top: 5px;
	cursor:pointer;
	font-size: 13px;
	display: none;
}
.find_span1 a{
	text-decoration: none;
	color: #333;
}
.i_open{
	width: 14px;
	height: 14px;
	position: relative;
	display: inline-block;
	background-image: url("../img/opne.png");
	top:2px;
}
.i_close{
	width: 14px;
	height: 14px;
	position: relative;
	display: inline-block;
	background-image: url("../img/colse.png");
	top:2px;
}
.user_table{
	width: 96%;
	margin-left: 2%;
	border:none;
	font-size: 13px;
}
.user_table tr{
	height: 30px;

}
.user_td{
	text-align: right;
}
.user_td1{
	width: 100px;
}
.user_rdio{
	width: 15px;
	height: 15px;
	position: relative;
	top:3px;
	margin-right: 5px;
}
.rideo_label{
	margin-right: 10px;
	font-size: 13px;
}
.user_save{
	background-color: #00aa00;
	color: #ffffff;
	padding-bottom: 4px;
	padding-top: 3px;
	padding-left: 8px;
	padding-right: 8px;
	border:none;
	border-radius: 3px;
	margin-right: 10px;
}
.user_save:active{
	background-color: #00ca6d;
}
.user_esc{
	background-color: #d58512;
	color: #ffffff;
	padding-bottom: 4px;
	padding-top: 3px;
	padding-left: 8px;
	padding-right: 8px;
	border:none;
	border-radius: 3px;
}
.user_esc:active{
	background-color: #985f0d;
}
.rose_text{
	width: 350px;
	height: 100px;
	resize: none;
}
.part_span{
	float: right;
	margin-right: 5px;
}
.part_table{
	float: left;
	width: 79%;
	height: 600px;
	border:1px solid #0D8BBD;
	margin-left: 2px;
}
.part_tree{
	float: right;
	width: 20%;
	height: 600px;
	border:1px solid #0D8BBD;
	margin-right: 5px;
}
.part_tree ul{
	list-style: none;
	text-indent: 10px;
}
.tree_h{
	height: 40px;
	background-color: #EEEEEE;
	margin-top: 0px;
	line-height: 40px;
	text-indent: 5px;
}
.static_all{
width: 100%;
height: 100%;


}
.static01{
	width: 98%;
	margin-left: 1%;
	height: 300px;

	margin-top: 5px;
}
.static02{
	width: 98%;
	margin-left: 1%;
	height: 300px;

	margin-top: 5px;
}
.static03{
	width: 98%;
	margin-left: 1%;
height: 300px;

	margin-top: 5px;
}
.alam_static{
	width: 98%;
	height: 350px;


	margin-left: 1%;
	margin-top: 5px;

}
.alam_static1{

	width: 100%;

	background-color: #ffffff;

}
.alstic_ledt{
	padding-top: 10px;
	width: 59%;
	height: 350px;
	float: left;
}
.alstic_right{
	padding-top: 10px;
	width: 39%;
	height: 350px;
	float: right;
	background-color: #ffffff;


}
.alstic_right p{

	padding-bottom: 5px;
}
.spqn_red{
	color: #f11010;
	font-size: 20px;
}
.sque_red{
	width: 16px;
	height: 16px;
	background-color: #c23531;
	display: inline-block;
	margin-right: 10px;
	position: relative;
	top:3px;
}
.span_margin{
	margin-left: 10px;
}
.sque_02{
	width: 16px;
	height: 16px;
	background-color: #d48265;
	display: inline-block;
	margin-right: 10px;
	position: relative;
	top:3px;
}
.cloum_alam{
	width: 100%;
	height: 350px;
}
.book_con01{
	width: 80%;
	height: auto;
	margin-left: 10%;
	margin-top: 2%;
	margin-bottom: 3%;
	background-color: #ffffff;
	padding-top: 2%;
	padding-bottom: 2%;
	font-size: 14px;
	box-shadow:-5px 0 5px #666, /*左边阴影*/
	5px 0 5px #666, /*右边阴影*/
	0 -5px 5px #666, /*顶部阴影*/
	0 5px 5px #666; /*底边阴影*/


}
.book_p{
	width: 94%;
	margin-left: 3%;

	margin-bottom: 15px;
	padding-bottom: 10px;
}
.book_titSpan{
	float: right;
}
.book_p label{
	color: #3665a9;
	margin-right: 5px;
}
.book_input01{
	width: 100px;
	border-top:none;
	border-left: none;
	border-right: none;
	border-bottom: 1px solid #a9c9f7;
	margin-right: 5px;
	height:30px;
}
.book_input02{
	width: 60px;
	border-top:none;
	border-left: none;
	border-right: none;
	border-bottom: 1px solid #a9c9f7;
	margin-right: 5px;
	height:30px;
}
.book_input03{
	width: 97%;
	margin-left: 1%;
	height:30px;
	border:none;

}
.book_h01{
	width: 94%;
	margin-left: 3%;
	padding-top: 15px;
	margin-bottom: 10px;
	text-align: center;
	letter-spacing: 1px;
	font-size: 16px;

}
.label_red{
	margin-left: 5px;
	margin-right: 5px;
	color: #f50f35;
}
.book_table{
	width: 94%;
	margin-left: 3%;
	border-collapse:collapse;


}
.book_table tr{

	border:1px solid #a9c9f7;




}
.book_table td{
	padding-top: 8px;
	padding-bottom: 8px;
	padding-left: 5px;

}
.input_radio{
	position: relative;
	top:1px;
	margin-right: 5px;
	margin-left: 8px;
}
.textArae{
	width: 96%;
	margin-left: 1%;
	height: 350px;
	resize: none;


}
.textArae01{
	width: 96%;
	margin-left: 1%;
	height: 350px;
	resize: none;
	border:none;


}
.label_right{
	float: right;
	margin-right: 5px;
}
.td_label{
	color: #3665a9;
	margin-right: 5px;
	padding-top: 5px;
	padding-bottom: 10px;

}
.book_input04{
	width: 20%;
	height: 30px;
	margin-left: 3%;

}
.book_input05{
	width: 45%;
	height: 30px;
	margin-left: 3%;

}
.my_static{
	width: 100%;

	height: 350px;
	margin-top: 15px;
}
.book_foot{
	width: 100%;
	text-align: center;
	margin-top: 25px;
	margin-bottom: 25px;
}
.book_foot input{
	margin-right: 20px;
	padding-left: 5px;
	padding-right: 5px;
}
.reead{

	background-color: #EEEEEE;
}
.tr_color{
	background-color: #ECF4FB;
	text-align: center;
	font-weight: bold;
}
.P_chart{
	width: 98%;
	margin-left: 1%;
	height: 450px;

}
.p_static{
	padding-top: 15px;

	text-align: center;
	width: 100%;
	font-weight: bold;
	font-size: 18px;
}
.static_span01{
	margin-right: 15px;
	margin-left: 5px;
	font-size: 12px;
}
.static_span02{
	margin-right: 15px;
	float: right;
	font-size: 12px;
}
.static_tabel{
	width: 100%;
	border-collapse: collapse;
	font-size: 12px;
}
.static_tabel td{
	padding-top: 5px;
	padding-bottom: 5px;
	border:1px solid #CCCCCC;
	text-align: center;
}
.td_red{
	color: red;
}
.td_blue{
	color: #1F547E;
}
.index_top{
	color: #ffffff;
	line-height: 60px;
}
.i_start {
	width: 8px;
	height: 8px;
	position: relative;
	display: inline-block;
	background-image: url("../img/start.png");

	margin-right: 5px;
	top:-2px;
}
.online_left{
	width: 55%;
	height: 100%;
	float: left;
	background-color: #e6ecf5;
}
.online_right{
	width: 45%;
	height: 100%;
	float: left;
	background-color: #eff2f7;

}
.online_leftcon01{
	width: 100%;
	border-bottom: 1px solid #0D8BBD;
	/*padding-top: 10px;*/
	/*padding-bottom: 10px;*/
}

.online_table{
	width: 96%;
	margin-left: 2%;
	font-size: 12px;
}
.online_table td{
	padding-top: 15px;
	padding-bottom: 15px;

}
.online_table tr{
	border:1px solid #EEEEEE;
}
.table_time{
	color: #999999;
}
.online_input01{
	width: 97%;
	margin-left: 1%;
	height:150px;
	resize: none;
	border:1px solid #EEEEEE;
	border-radius: 5px;


}
.online_ringcon01{
	padding-top: 15px;
	padding-bottom: 15px;
}
.online_input02{
	width: 97%;
	margin-left: 1%;
	height:30px;
	resize: none;
	border:1px solid #EEEEEE;
	border-radius: 5px;
	color: #666666;
	text-indent: 2em;


}
.onlineUl{
	list-style: none;
	width: 96%;
	margin-left: 2%;
	font-size: 12px;

}
.onlineUl li{
	padding-top: 10px;
	padding-bottom: 10px;
	border-bottom: 1px solid #CCCCCC;
}
.onlineUl_span01{
	float: right;
	color: #999999;
}
.onlineUl_span02{
	float: left;
	color: #999999;
	margin-right: 10px;
}
.user_p01{
	width: 30px;
	height: 30px;
	background-image: url("../img/user2-160x160.jpg");
	border-radius: 50%;
	background-size: 100% 100%;
}
.static_label{
	font-size: 12px;
	margin-right: 10px;
}
#light {
	margin: 0px;
	padding: 0px;
	height: auto;
	border: solid 2px #26d0a1;
}
	#light table {
		margin: 5px;
		padding:5px;
	}

#light #lightHead {
	background-color: #26d0a1;
}
	#light #button {
		margin-top: 5px;
		width: 100%;
		float: left;
	}
	#light #lightBody {
		height: auto;
		min-height: 330px;
		width: 100%;
		float: left;
	}
#light2 {
	margin: 0px;
	padding: 0px;
	height: auto;
	border: solid 2px #26d0a1;
}

	#light2 table {
		margin: 5px;
		padding: 5px;
	}

	#light2 #lightHead {
		background-color: #26d0a1;
	}

	#light2 #button {
		margin: 5px;
		width: 100%;
		float: left;
	}

#button input {
	background-color: #56dcaf;
	border: 0px;
	border-radius:5px;
	color:white;
}

#light2 #lightBody {
	height: auto;
	min-height: 330px;
	width: 100%;
	float: left;
}

#yh_header2 tr td {
	border-top-left-radius: 4px;
	border-top-right-radius: 4px;
}