﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using Common;
using System.Text;


namespace DAL
{
    public class PurchDal
    {


        public static string JudgeSysKindExist(string Kind, string KindList, string QT, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string sStatus = string.Empty;

            if (sFlag == "50")
            {
                sSQL = " select InMan from T_PurchProject where PPNo = '" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "50-1")  // 判断客户是否存在
            {
                sSQL = " select InMan from T_CustInfo where CustNo = '" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "50-2")  // 判断是否已转图纸
            {
                sSQL = " select InMan from T_PurchProject where PPNo = '" + Kind + "' and Status='已转图纸' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "57-1")//判断询价是否已PR交期确认 ,
            {
                sSQL = " select InMan from T_PRInfo where PRNo='" + Kind + "'and PRItem='" + KindList + "' and Status in ('交期已确认','部分已出库','订单完成') and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "58")//判断该PR单是否已报价，目前是：一个PR+ITEM只能对应一个报价物料 
            {
                sSQL = " select InMan from T_PRPrice where PRNo='" + Kind + "'and PRItem='" + KindList + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "58-1")//判断该物料是否已进行了PR报价  
            {
                sSQL = " select InMan from T_PRPrice where PRNo='" + Kind + "'and PRItem='" + KindList + "' and Status<>'PR报价不通过' and MaterNo='" + QT + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "58-2")//判断该物料是否已进行了PR报价  
            {
                sSQL = " select InMan from T_PRPrice where PPNo='" + Kind + "' and Status='PR报价已审核' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "58-4")//判断PR报价是否已添加供应商
            {
                sSQL = " select InMan from T_PRSupplier where PPNo='" + Kind + "' and SupplierNo='" + KindList + "' ";
            }
            else if (sFlag == "58-5")//审核不通过的PR报价单，也不允许添加供应商，审核供应商 
            {
                sSQL = " select InMan from T_PRPrice where PPNo='" + Kind + "' and Status='PR报价不通过' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "58-6")// 20210611：一个报价的物料，可添加多个比价的供应商，但审核通过的只能有一个
            {
                sSQL = " select InMan from T_PRSupplier where PPNo='" + Kind + "' and Status='审核通过' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "59-1")//判断这个PR是否已转单  
            {
                sSQL = " select InMan from T_PurchaseInfo where PPNo='" + Kind + "' and SupplierNo='" + KindList + "'  and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "59-2")//判断这个PO单是否已审核
            {
                sSQL = " select InMan from T_PurchaseItemInfo where PurchNo='" + Kind + "' AND PurchItem='" + KindList + "' and Status not in ('PO待审核','PO审核不通过')  ";
            }
            else if (sFlag == "59-3")// 取消：PO待审核 状态才可以取消；恢复：取消采购订单  状态才可回复
            {
                sSQL = " select InMan from T_PurchaseItemInfo where PurchNo='" + Kind + "' AND PurchItem='" + KindList + "' and Status='" + QT + "'  ";
            }
            else if (sFlag == "59-4")// 勾选PR单，判断该PR单是否已生成PO单
            {
                sSQL = " select InMan from T_PurchaseItemInfo where PPNo='" + Kind + "' and SupplierNo='" + QT + "' ";
            }
            else if (sFlag == "59-5")// 批量生成PO单时判断这些PR单是否有些已转PO
            {
                sSQL = " select InMan from T_PurchaseItemInfo where ltrim(rtrim(PPNo))+ltrim(rtrim(SupplierNo)) in (select ltrim(rtrim(SNo))+ltrim(rtrim(CNo)) from T_TempInfo where InMan='" + Kind + "' and Kind='PR单') ";
            }
            else if (sFlag == "63-1")//判断发票金额是否大于对账单金额
            {
                sSQL = " select Amount from ( " +
                       "   select Amount,isnull(b.FPAmount,0) as FPAmount from T_AccountInfo a left join (select ACNo,sum(FPAmount) as FPAmount from T_AccountFP group by ACNo) b on a.ACNo=b.ACNo " +
                       "   where a.ACNo='" + Kind + "' " +
                       " ) aa where FPAmount + " + QT + " > Amount ";
            }





            DataTable dt = DBHelper.GetDataTable(sSQL);
            if (dt.Rows.Count > 0)
            {
                sStatus = "Y";
            }
            else
            {
                sStatus = "N";
            }


            return sStatus;
        }


        /// <summary>
        /// 获取询价信息
        /// </summary>
        /// <param name="PPNo"></param>
        /// <param name="CustNo"></param>
        /// <param name="CustName"></param>
        /// <param name="BDate"></param>
        /// <param name="EDate"></param>
        /// <param name="Status"></param>
        /// <param name="BDate"></param>
        /// <param name="EDate"></param>
        /// <param name="sInMan"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetPurchProject(string No, string Item, string Name, string MNo, string MName, string Status, string BDate, string EDate, string A, string B, string C, int Row, int num, string sInMan, string sComp, string sFlag)
        {                                  // { No:sPNo,Item:sCNo,Name:sEn,MNo:"",MName:"",Status:sSt,BDate:sBDate, EDate:sEDate,A:sCheck,B:"",C:""};
            string sSQL = string.Empty;
            string sChStr = string.Empty;
            string sNum = string.Empty;
            DataTable sdt = new DataTable();

            if (sFlag == "50-22")
            {
                sSQL = " select * from T_QuestionHead where PPNo = '" + No + "'  ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "50-2")
            {
                sSQL = " select * from T_QuestionPrice where QsNo = '" + No + "' order by MaterVer ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "50-3")
            {
                sSQL = " select COUNT(*) as C from T_PRInfo where QsNo='" + No + "' and MaterVer='" + Item + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sNum = sdt.Rows[0]["C"].ToString();
                }
                else
                {
                    sNum = "0";
                }

                sSQL = " select  '" + sNum + "' as NumCount, *,convert(char(16),InDate,120) as InDate2 from T_PRInfo where QsNo='" + No + "' and MaterVer='" + Item + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "50-4")  // 获取PR信息，打印PR合同
            {
                sSQL = " select * from T_PRInfo where PRNo='" + No + "' and PRItem='" + Item + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "56-3")  //相同询价单号的，不需要重新输入。
            {
                sSQL = " select top 1 PRNo,PRItem,QsNo,MaterVer,PRDate,PONO,Quote,ShipTo,AddDesc,ReqDesc,ConfirmBy from T_PRInfo where QsNo = '" + No + "' order by InDate desc ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "56-4") // 获取PO信息，打印PO合同报表
            {
                sSQL = " select a.PurchNo,a.PurchItem,a.SaleOrderNo,a.MaterNo,a.MaterName,a.ProductName,b.FullName,c.SupplierName,c.InchangeMan,c.Phone,c.Fax,c.PaymentTerms,c.Addr,c.PostalCode,a.PurchNum,a.Tax,a.PurchPrice,a.Amount, " +
                       " a.PurchNum*a.PurchPrice*(100+a.Tax)/100 as HSAmount,d.SumAmount,d.SumHSAmount,a.PurchDate,a.ArrivalDate,a.CHOne,a.CHTwo,a.CHThree,a.CHFour,a.CHFive,a.NowRemark " +
                       " from T_PurchaseItemInfo a left join T_SPUser b on a.InMan=b.LoginName left join T_SupplierInfo c on a.SupplierNo=c.SupplierNo "+
                       " left join (select PurchNo,SUM(Amount) as SumAmount,SUM(Amount)*(100+Tax)/100 as SumHSAmount from T_PurchaseItemInfo where PurchNo= '" + No + "' group by PurchNo,Tax) d on a.PurchNo=d.PurchNo " +
                       " where a.PurchNo = '" + No + "'  ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "58-4")  // 查询PR报价对应的供应商
            {
                sSQL = " select COUNT(*) as C from T_PRSupplier where PPNo='" + No + "'  ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sNum = sdt.Rows[0]["C"].ToString();
                }
                else
                {
                    sNum = "0";
                }

                sSQL = " select  '" + sNum + "' as NumCount, *,convert(char(16),InDate,120) as InDate2 from T_PRSupplier where PPNo='" + No + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "61-3")
            {
                sSQL = " select COUNT(*) as C from T_ProgressInfo where ltrim(rtrim(OneNo))+ltrim(rtrim(TwoNo))='" + No + "' and ltrim(rtrim(ThreeNo))+ltrim(rtrim(FourNo))='" + Item + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sNum = sdt.Rows[0]["C"].ToString();
                }
                else
                {
                    sNum = "0";
                }

                sSQL = " select  '" + sNum + "' as NumCount, *,convert(char(16),InDate,120) as InDate2 from T_ProgressInfo  where ltrim(rtrim(OneNo))+ltrim(rtrim(TwoNo))='" + No + "' and ltrim(rtrim(ThreeNo))+ltrim(rtrim(FourNo))='" + Item + "' order by InDate desc ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "62-3")
            {
                sSQL = " select COUNT(*) as C from T_ProgressInfo where OneNo='" + No + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sNum = sdt.Rows[0]["C"].ToString();
                }
                else
                {
                    sNum = "0";
                }

                sSQL = " select  '" + sNum + "' as NumCount, *,convert(char(16),InDate,120) as InDate2 from T_ProgressInfo  where OneNo='" + No + "' order by InDate desc ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else
            {
                SqlParameter[] parameters = {
					new SqlParameter("@lnNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnItem", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnStatus", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
					new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,10),
					new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
                parameters[0].Value = No;
                parameters[1].Value = Item;
                parameters[2].Value = Name;  
                parameters[3].Value = MNo;
                parameters[4].Value = MName;
                parameters[5].Value = Status;
                parameters[6].Value = BDate;
                parameters[7].Value = EDate;
                parameters[8].Value = A; // A
                parameters[9].Value = B;
                parameters[10].Value = C;
                parameters[11].Value = Row;
                parameters[12].Value = num;
                parameters[13].Value = sInMan;
                parameters[14].Value = sFlag;
                parameters[15].Value = "";

                DataSet DS = DBHelper.RunProcedureForDS("P_GetPurchFlowInfoForPage", parameters);
                sdt = DS.Tables[0];
            }

            return sdt;
        }

        /// <summary>
        /// 维护询价信息-需求询价
        /// </summary>
        /// <param name="PPNo"></param>
        /// <param name="PPVer"></param>
        /// <param name="CustNo"></param>
        /// <param name="CustEn"></param>
        /// <param name="PPMan"></param>
        /// <param name="PPDate"></param>
        /// <param name="PPDesc"></param>
        /// <param name="DocName"></param>
        /// <param name="DocPath"></param>
        /// <param name="sComp"></param>
        /// <param name="InMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string AddEditPurchProject(string PPNo, string PPVer, string CustNo, string CustEn, string PPMan, string PPDate, string PPDesc, string DocName, string DocPath, string sComp, string InMan,string Remark, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sstr = string.Empty;
            string sKind = string.Empty;
            string sPNo = PPNo.Substring(0, PPNo.Length - 1);
            string sSt = string.Empty;
            
 

            if (sFlag == "1")  // 新增   CNo,INo,DNo,DKind,Num,AmountS,ZJWay,ZJYear,ZJLv,ZJMonth,SDate,Amount,IStatus,SumZJ,Flag
            {
                // 更新这个询价单号其他版本为历史版本
                sSQLs = " update T_PurchProject set NowShow='0' where CustNo='" + CustNo + "' and SUBSTRING(PPNo,1,LEN(PPNo)-1)= '" + sPNo + "' ";

                sSQL = " insert into T_PurchProject(PPNo,PPVer,CustNo,CustEn,PPMan,PPDate,PPDesc,DocName,DocPath,Status,CompanyNo,InMan,Remark) " +
                       " values('" + PPNo + "','" + PPVer + "','" + CustNo + "','" + CustEn + "','" + PPMan + "','" + PPDate + "','" + PPDesc + "','" + DocName + "','" + DocPath + "','待转图纸','" + sComp + "','" + InMan + "','" + Remark + "') ";

            }
            else if (sFlag == "50")  // 删除文件更新信息--原文件
            {
                sSQL = " update T_PurchProject set DocName='" + DocName + "',DocPath='" + DocPath + "' where PPNo='" + PPNo + "'  ";
            }
            else if (sFlag == "50-1")  // 删除记录
            {
                sSQL = " delete T_PurchProject  where PPNo='" + PPNo + "'  ";
            }
            else if (sFlag == "50-2")  //上传：更新解读后的文件  DocNameJD,DocPathJD,JDMan,JDDate
            {
                sSQL = " update T_PurchProject set DocNameJD='" + DocName + "',DocPathJD='" + DocPath + "',JDMan='" + InMan + "',JDDate=convert(char(16),getdate(),120),Status='已转图纸' where PPNo='" + PPNo + "'  ";
            }
            else if (sFlag == "50-3")  //删除：更新解读后的文件  DocNameJD,DocPathJD,JDMan,JDDate
            {
                if (DocName.Length > 3)  // 表示 还有已上传的解读文件
                {
                    sSQL = " update T_PurchProject set DocNameJD='" + DocName + "',DocPathJD='" + DocPath + "',JDMan='" + InMan + "',JDDate=convert(char(16),getdate(),120),Status='已转图纸' where PPNo='" + PPNo + "'  ";
                }
                else
                {
                    sSQL = " update T_PurchProject set DocNameJD='" + DocName + "',DocPathJD='" + DocPath + "',JDMan='" + InMan + "',JDDate=convert(char(16),getdate(),120),Status='待转图纸' where PPNo='" + PPNo + "'  ";
                }
            }
            else // 更新采购询价信息
            {
                sSQL = " update T_PurchProject set PPDesc='" + PPDesc + "',PPMan='" + PPMan + "',PPDate='" + PPDate + "',DocName='" + DocName + "',DocPath='" + DocPath + "',Remark='" + Remark + "' where PPNo='" + PPNo + "'  ";
            }


            List<string> links = new List<string>();
            links.Add(sSQLs);  // 先更新历史版本，在插入
            links.Add(sSQL);


            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }

        // 新增，修改，删除，审核 询价单信息
        public static string OPQsPrice(string QNo, string QD, string CPN, string Desc, string RFQNo, string PNo, string MNo, string Ver, string Num, string FP, string P, string UP, string Tax, string Rate, string L, string W, string H,
            string Ds, string WH, string TWH, string Wkg, string WZS, string FT, string AP, string LT, string MS, string SPNO, string SOPN,string DF, string InMan, string sComp, string Remark, string sFlag)
        { 
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sstr = string.Empty;
            string sMaxNo = string.Empty;
            string sNo = string.Empty;
            string sSt = string.Empty;
            int iMax = 0;
            DataTable sdt;


            if (sFlag == "1")  // 新增  表头信息
            {
                sSQL = " insert into T_QuestionHead(QsNo,QsDate,CustPN,CustNo,Description,RFQNo,PPNo,CompanyNo,InMan) " +
                       " values('" + QNo + "','" + QD + "','" + CPN + "','" + MNo + "','" + Desc + "','" + RFQNo + "','" + PNo + "','" + sComp + "','" + InMan + "') ";

            }
            else if (sFlag == "2")  // 修改表头信息
            {
                sSQL = " update T_QuestionHead set QsDate='" + QD + "',RFQNo='" + RFQNo + "' where PPNo='" + PNo + "'  ";
            }
            else if (sFlag == "3")  // 删除记录明细，如果明细没有了，则表头也一起删除
            {

                sdt = DBHelper.GetDataTable("select InMan from T_QuestionPrice where QsNo='" + QNo + "' ");
                if (sdt.Rows.Count <=1) // 说明这个询价单的明细只有一个，后没有了，那么把表头也删除
                {
                    sSQLs = " delete T_QuestionHead  where QsNo='" + QNo + "' ";
                }


                sSQL = " delete T_QuestionPrice  where QsNo='" + QNo + "' and MaterNo='" + MNo + "'and MaterVer='" + Ver + "' ";
            }
            else if (sFlag == "4")  // 插入明细信息
            {
                sMaxNo = DBHelper.GetMaxNo("T_QuestionPrice where QsNo='" + QNo + "' ", "MaterVer");//
                if (sMaxNo == "")
                {
                    sNo = "001";
                }
                else
                {
                    iMax = int.Parse(sMaxNo) + 1;
                    int len = iMax.ToString().Length;
                    if (len < 2)
                    {
                        sNo = "00" + iMax.ToString();
                    }
                    else if ((len >= 2) && (len < 3))
                    {
                        sNo = "0" + iMax.ToString();
                    }
                    else
                    {
                        sNo = iMax.ToString();
                    }
                }

                sSQL = " insert into T_QuestionPrice(QsNo,MaterNo,MaterVer,SOPN,CustPN,Description,Num,FinalP,Price,UnitPrice,Tax,Rate,L,W,H,Density,Weights,TolWeight,WeightKg,WeightZS,Freight,AvgPrice,LTime,MaterSpec,SupplierNo,Difficulty,Status,CompanyNo,InMan) " +
                      " values('" + QNo + "','" + sNo + "','" + sNo + "','" + SOPN + "','" + CPN + "','" + Desc + "','" + Num + "','" + FP + "','" + P + "','" + UP + "','" + Tax + "','" + Rate + "','" + L + "','" + W + "','" + H + "','" + Ds + "','" + WH + "', " +
                      " '" + TWH + "','" + Wkg + "','" + WZS + "','" + FT + "','" + AP + "','" + LT + "','" + MS + "','" + SPNO + "','" + DF + "','已报价待确认','" + sComp + "','" + InMan + "') ";
            }
            else if (sFlag == "5")  // 更新明细信息
            {
                sSQL = " update T_QuestionPrice set Num='" + Num + "',SOPN='" + SOPN + "',CustPN='" + CPN + "',Description='" + Desc + "',FinalP='" + FP + "',Price='" + P + "',UnitPrice='" + UP + "',Tax='" + Tax + "',Rate='" + Rate + "', " +
                       " L='" + L + "',W='" + W + "',H='" + H + "',Density='" + Ds + "',Weights='" + WH + "',TolWeight='" + TWH + "',WeightKg='" + Wkg + "',WeightZS='" + WZS + "',Freight='" + FT + "',AvgPrice='" + AP + "',LTime='" + LT + "',MaterSpec='" + MS + "', " +
                       " SupplierNo='" + SPNO + "',Difficulty='" + DF + "',Status='已报价待确认' " +
                       " where QsNo='" + QNo + "' and MaterNo='" + MNo + "'and MaterVer='" + Ver + "' ";

                sSQLs = " update T_QuestionHead set QsDate='" + QD + "',RFQNo='" + RFQNo + "' where QsNo='" + QNo + "'  ";
            }
            else if (sFlag == "10")  // 审核询价单  -- 单个记录审核
            {
                if (DF == "不通过")
                {
                    sSt = "报价确认不通过";
                }
                else
                {
                    sSt = "已报价已确认";
                }

                sSQL = " update T_QuestionPrice set Status='" + sSt + "',UnitPriceTwo='" + UP + "',AudMan='" + InMan + "',AudDate=Convert(char(16),getdate(),120), PDesc='" + MS + "' where QsNo='" + QNo + "'and MaterVer='" + Ver + "' ";
            }
            else if (sFlag == "11")  // 审核询价单  -- 批量记录审核
            {
                if (DF == "不通过")
                {
                    sSt = "报价确认不通过";
                }
                else
                {
                    sSt = "已报价已确认";
                }

                sSQL = " update T_QuestionPrice set Status='" + sSt + "',AudMan='" + InMan + "',AudDate=Convert(char(16),getdate(),120), PDesc='" + MS + "' where QsNo='" + QNo + "' ";
            }

            List<string> links = new List<string>();
            links.Add(sSQLs);   
            links.Add(sSQL);


            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }

        // 维护PR信息
        public static string AddEditPRInfo(string PPNo, string PRNo, string PRItem, string Item, string QsNo, string Ver, string Date, string CustNo, string PONO, string Quote, string ShipTo, string AddDesc, string ReqDesc, string CBy, string CType, string MNo, string KG, string IDesc, string Qty, string Tax, string SPCDate, string DDate, string sComp, string InMan, string Remark, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sstr = string.Empty;
            string sKind = string.Empty;
            string sSt = string.Empty;
            string sMaxNo = string.Empty;
            int iMax = 0;
            string sNo = string.Empty;


            if (sFlag == "1")  // 新增   
            {
                sMaxNo = DBHelper.GetMaxNo(" T_PRInfo where PRNo='" + PRNo + "' ", "PRItem");//  001  002
                if (sMaxNo == "")
                {
                    sNo = "001";
                }
                else
                {
                    iMax = int.Parse(sMaxNo) + 1;
                    int len = iMax.ToString().Length;
                    if (len == 1){
                        sNo = "00" + iMax.ToString();
                    }
                    else if (len == 2) {
                        sNo = "0" + iMax.ToString();
                    }
                    else{
                        sNo = iMax.ToString();
                    }
                }

                sSQL = " insert into T_PRInfo(PRNo,PRItem,QsNo,MaterVer,Tax,PRDate,CustPN,CustNo,PONO,Quote,ShipTo,AddDesc,ReqDesc,ConfirmBy,ConfirmDate,CType,IDesc,Qty,SPCDate,DDate,Status,CompanyNo,InMan,Remark) " +
                       " values('" + PRNo + "','" + sNo + "','" + QsNo + "','" + Ver + "','" + Tax + "','" + Date + "','" + Item + "','" + CustNo + "','" + PONO + "','" + Quote + "','" + ShipTo + "','" + AddDesc + "','" + ReqDesc + "','" + CBy + "','','" + CType + "', " +
                       " '" + IDesc + "','" + Qty + "','" + SPCDate + "','" + DDate + "','待PR确认','" + sComp + "','" + InMan + "','" + Remark + "') ";

            }
            else if (sFlag == "2")  // 修改PR信息
            {
                sSQL = " update T_PRInfo set PRDate='" + Date + "',CustNo='" + CustNo + "',PONO='" + PONO + "',Quote='" + Quote + "',ShipTo='" + ShipTo + "',AddDesc='" + AddDesc + "',ReqDesc='" + ReqDesc + "',ConfirmBy='" + CBy + "',CType='" + CType + "', " +
                       " IDesc='" + IDesc + "',Qty='" + Qty + "',SPCDate='" + SPCDate + "',DDate='" + DDate + "',Status='待PR确认' where PRNo='" + PRNo + "' and PRItem='" + PRItem + "' ";
            }
            else if (sFlag == "3")  // 删除
            {
                sSQL = " delete T_PRInfo where PRNo='" + PRNo + "' and PRItem='" + PRItem + "' ";
            }
            else if (sFlag == "4")  // 修改交期保存
            {
                sSQL = " update T_PRInfo set DDate='" + DDate + "' where PRNo='" + PRNo + "' and PRItem='" + PRItem + "' ";
            }
            else if (sFlag == "5")  // 审核
            {
                sSQL = " update T_PRInfo set AudDate=convert(char(16),getdate(),120),AudMan='" + PONO + "',IsPass='PR交期确认通过',AudDesc='PR交期确认通过',Status='交期已确认' where PRNo='" + PRNo + "' and PRItem='" + PRItem + "' ";
            }
            else if (sFlag == "6")  // 驳回
            {
                sSQL = " update T_PRInfo set AudDate=convert(char(16),getdate(),120),AudMan='" + PONO + "',IsPass='确认不通过',AudDesc='" + AddDesc + "',Status='确认不通过' where PRNo='" + PRNo + "' and PRItem='" + PRItem + "' ";
            }
            else if (sFlag == "10")  // PR报价录入-新增：根据PR单录入
            {
                sSQL = " insert into T_PRPrice(PPNo,PRNo,PRItem,PRDate,CustPN,PONO,Quote,ShipTo,AddDesc,ReqDesc,ConfirmBy,CType,IDesc,Qty,Tax,SPCDate,DDate,Kind,MaterNo,IsKG,Status,CompanyNo,InMan,Remark) " +
                       " select '" + PPNo + "',PRNo,PRItem,PRDate,CustPN,PONO,Quote,ShipTo,AddDesc,ReqDesc,ConfirmBy,CType,IDesc,Qty,Tax,SPCDate,DDate,'PR','" + MNo + "','" + KG + "','PR报价待审核','" + sComp + "','" + InMan + "','" + Remark + "' " +
                       " from T_PRInfo where PRNo='" + PRNo + "' and PRItem='" + PRItem + "' ";
            }
            else if (sFlag == "11")  // PR报价录入-新增：手工录入的  
            {
                sMaxNo = DBHelper.GetMaxNo(" T_PRInfo where PRNo='" + PRNo + "' ", "PRItem");//  001  002
                if (sMaxNo == "")
                {
                    sNo = "001";
                }
                else
                {
                    iMax = int.Parse(sMaxNo) + 1;
                    int len = iMax.ToString().Length;
                    if (len == 1)
                    {
                        sNo = "00" + iMax.ToString();
                    }
                    else if (len == 2)
                    {
                        sNo = "0" + iMax.ToString();
                    }
                    else
                    {
                        sNo = iMax.ToString();
                    }
                }


                sSQL = " insert into T_PRPrice(PPNo,PRNo,PRItem,PRDate,CustPN,PONO,Quote,ShipTo,AddDesc,ReqDesc,ConfirmBy,CType,IDesc,Qty,Tax,SPCDate,DDate,Kind,MaterNo,IsKG,Status,CompanyNo,InMan,Remark) " +
                       " values('" + PPNo + "','" + PRNo + "','" + sNo + "','" + Date + "','" + Item + "','" + PONO + "','" + Quote + "','" + ShipTo + "','" + AddDesc + "','" + ReqDesc + "','" + CBy + "','" + CType + "', " +
                       " '" + IDesc + "','" + Qty + "','" + Tax + "','" + SPCDate + "','" + DDate + "','手动','" + MNo + "','" + KG + "','PR报价待审核','" + sComp + "','" + InMan + "','" + Remark + "') ";
            }
            else if (sFlag == "12")  // PR报价录入-修改：根据PR单录入
            {
                sSQL = " update T_PRPrice set MaterNo='" + MNo + "',IsKG='" + KG + "',Status='PR报价待审核' where PPNo= '" + PPNo + "' ";
            }
            else if (sFlag == "13")  // PR报价录入-修改：手工录入的 
            {
                sSQL = " update T_PRPrice set PRDate='" + Date + "',CustNo='" + CustNo + "',PONO='" + PONO + "',Quote='" + Quote + "',ShipTo='" + ShipTo + "',AddDesc='" + AddDesc + "',ReqDesc='" + ReqDesc + "',ConfirmBy='" + CBy + "',CType='" + CType + "', " +
                       " IDesc='" + IDesc + "',Qty='" + Qty + "',Tax='" + Tax + "',SPCDate='" + SPCDate + "',DDate='" + DDate + "',MaterNo='" + MNo + "',IsKG='" + KG + "',Status='PR报价待审核' where PPNo= '" + PPNo + "' ";
            }
            else if (sFlag == "14")  // 删除PR报价
            {
                sSQLs = " delete T_PRSupplier where PPNo= '" + PPNo + "' ";
                sSQL = " delete T_PRPrice where PPNo= '" + PPNo + "' ";
            }
            else if (sFlag == "15")  //新增PR报价对应的供应商  
            {
                sSQL = " insert into T_PRSupplier(PPNo,SupplierNo,SupplierEn,Price,Qty,Amount,Tax,DDate,LTime,CMan,Phone,Status,CompanyNo,InMan,Remark) " +
                       " values('" + PPNo + "','" + PRNo + "','" + PONO + "','" + ShipTo + "','" + Qty + "','" + AddDesc + "','" + Tax + "','" + Date + "','" + ReqDesc + "','" + Item + "','" + Quote + "','待审核','" + sComp + "','" + InMan + "','" + CBy + "') ";
            }
            else if (sFlag == "16")  //修改PR报价对应的供应商  
            {
                sSQL = " update T_PRSupplier set Price='" + ShipTo + "',Amount='" + AddDesc + "',DDate='" + Date + "',LTime='" + ReqDesc + "',Remark='" + CBy + "',Status='待审核' where PPNo='" + PPNo + "' and SupplierNo='" + PRNo + "' ";

                // 除了现在修改的这个供应商，如果这个PR报价单没有审核不通过的供应商，则更新PR报价单为  PR报价待审核
                DataTable sdt = DBHelper.GetDataTable("select InMan from T_PRSupplier where PPNo= '" + PPNo + "' and Status='PR报价审核不通过' and SupplierNo<>'" + PRNo + "' ");
                if (sdt.Rows.Count == 0) //  = 0 说明没有审核不通过的供应商了
                {
                    sSQLs = " update T_PRPrice set Status='PR报价待审核' where PPNo= '" + PPNo + "' ";
                }

            }
            else if (sFlag == "17")  //删除PR报价对应的供应商  
            {
                sSQL = " delete T_PRSupplier where PPNo='" + PPNo + "' and SupplierNo='" + PRNo + "'  ";
            }
            else if (sFlag == "18")  // 审核 -- 20210410：暂时不需要审核PR报表信息，审核报价的供应商即可，审核报价的供应商，同事更新PR报价信息状态。
            {
                sSQL = " update T_PRPrice set AudDate=convert(char(16),getdate(),120),AudMan='" + PONO + "',IsPass='PR报价审核通过',AudDesc='PR报价审核通过',Status='PR报价已审核' where PPNo= '" + PPNo + "' ";
            }
            else if (sFlag == "19")  // 驳回 
            {
                sSQL = " update T_PRPrice set AudDate=convert(char(16),getdate(),120),AudMan='" + PONO + "',IsPass='PR报价审核不通过',AudDesc='" + AddDesc + "',Status='PR报价不通过' where PPNo= '" + PPNo + "' ";
            }
            else if (sFlag == "20")  //审核报价的供应商  20210611：一个报价单对应的物料，有多个比价的供应商，但只能有一个供应商能通过审核
            {
                if (KG == "审核通过")
                {
                    sSQLs = " update T_PRPrice set AudDate=convert(char(16),getdate(),120),AudMan='" + PONO + "',IsPass='PR报价已审核',AudDesc='" + AddDesc + "',Status='PR报价已审核' where PPNo= '" + PPNo + "' ";
                }
                else
                {
                    sSQLs = " update T_PRPrice set AudDate=convert(char(16),getdate(),120),AudMan='" + PONO + "',IsPass='PR报价审核不通过',AudDesc='" + AddDesc + "',Status='PR报价不通过' where PPNo= '" + PPNo + "' ";
                }

                sSQL = " update T_PRSupplier set AudDate=convert(char(16),getdate(),120),DeliveryDate='" + DDate + "',AudMan='" + PONO + "',AudDesc='" + AddDesc + "',Status='" + KG + "' where PPNo= '" + PPNo + "' and SupplierNo='" + PRNo + "' ";
            }
            else if (sFlag == "21")  //插入订单的跟进信息
            {
                sSQL = " insert into T_ProgressInfo(GJNo,OneNo,TwoNo,ThreeNo,FourNo,Kind,Man,PDate,PText,InMan,Remark) " +
                       " values('" + Ver + "','" + PRNo + "','" + PRItem + "','" + QsNo + "','" + CustNo + "','订单跟进','" + PONO + "','" + Date + "','" + AddDesc + "','" + InMan + "','') ";
            }
            else if ((sFlag == "22") || (sFlag == "25"))  //修改订单/项目的跟进信息
            {
                sSQL = " update T_ProgressInfo set Man='" + PONO + "',PDate='" + Date + "',PText='" + AddDesc + "' where GJNo= '" + Ver + "' ";
            }
            else if ((sFlag == "23") || (sFlag == "26"))   //删除订单的跟进信息
            {
                sSQL = " delete T_ProgressInfo  where GJNo= '" + Ver + "' ";
            }
            else if (sFlag == "24")  //插入项目的跟进信息
            {
                sSQL = " insert into T_ProgressInfo(GJNo,OneNo,TwoNo,ThreeNo,FourNo,Kind,Man,PDate,PText,InMan,Remark) " +
                       " values('" + Ver + "','" + QsNo + "','" + Item + "','" + Quote + "','','项目跟进','" + PONO + "','" + Date + "','" + AddDesc + "','" + InMan + "','') ";
            }
            else if (sFlag == "27")  //暂停项目跟进信息
            {
                sSQL = " insert into T_ProgressInfo(GJNo,OneNo,TwoNo,ThreeNo,FourNo,Kind,Man,PDate,PText,InMan,Remark) " +
                       " values('" + Ver + "','" + QsNo + "','" + Item + "','" + Quote + "','','项目跟进','" + PONO + "','" + Date + "','项目暂停','" + InMan + "','') ";

                sSQLs = " update T_QuestionHead set Status='暂停' where QsNo= '" + QsNo + "' ";
            }
            else if (sFlag == "28")  //关闭项目的跟进信息
            {
                sSQL = " insert into T_ProgressInfo(GJNo,OneNo,TwoNo,ThreeNo,FourNo,Kind,Man,PDate,PText,InMan,Remark) " +
                       " values('" + Ver + "','" + QsNo + "','" + Item + "','" + Quote + "','','项目跟进','" + PONO + "','" + Date + "','项目关闭','" + InMan + "','') ";

                sSQLs = " update T_QuestionHead set Status='关闭' where QsNo= '" + QsNo + "' ";
            }




            List<string> links = new List<string>();
            links.Add(sSQLs);   
            links.Add(sSQL);


            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }


        // 维护PO信息
        public static string AddEditPOInfo(string PO, string POItem, string PPNo, string PRNo, string PRItem, string SupplierNo, string MNo, string PName, string JQDate, string Pay, string C1, string C2, string C3, string C4, string C5, string QT, string HTNo, string InMan, string InName, string Remark, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sStr = string.Empty;
            string sstr = string.Empty;
            string sKind = string.Empty;
            string sSt = string.Empty;
            string sMaxNo = string.Empty;
            string sNo = string.Empty;
            string sHTNo = string.Empty;
            string sPSNo = string.Empty;
            string sSL = string.Empty;
            int iMax = 0; int i = 0;

            List<string> links = new List<string>();


            if (sFlag == "1")  // 新增PO   单个PR单转PO单，20210606：暂时不用单个的。都是先勾选，在生成。
            {
                sSQL = " insert into T_PurchaseInfo(PurchNo,CompanyNo,PPNo,PurchMan,PurchDate,SupplierNo,SupplierEn,SupplierMan,TransportWay,TransportAddr,ArrivalDate,PayWay,InvioceKind,Amount,PayMoney,Currency,Phone,KeyInKind,HTNo,Status,PurchKind,InMan,Remark) " +
                       " select  '" + PO + "',CompanyNo,'" + PPNo + "','" + InName + "',convert(char(10),getdate(),120),SupplierNo,SupplierEn,CMan,'','', '" + JQDate + "', '" + Pay + "','',Amount,Amount,'',Phone,'PR', '" + HTNo + "','PO待审核','PR转PO', '" + InMan + "', '" + Remark + "' " +
                       " from T_PRSupplier where PPNo='" + PPNo + "' and SupplierNo= '" + SupplierNo + "' ";


                sSQLs = " insert into T_PurchaseItemInfo(PurchNo,PurchItem,CompanyNo,PPNo,SupplierNo,QsNo,PRItem,MaterNo,MaterVer,MaterName,PurchNum,OverNum,PurchPrice,Amount,PurchDate,MaterUnit,NetValue,MaterSpec,MaterXH,ArrivalDate,TransportWay,SupplierMan,Phone, " +
                       " ProductName,PayWay,CHOne,CHTwo,CHThree,CHFour,CHFive,SaleOrderNo,OrderItemNo,NowRemark,CDAudDesc,Status,InMan,Remark) " +
                       " select '" + PO + "','001',a.CompanyNo,'" + PRNo + "','" + PPNo + "','" + SupplierNo + "','" + PRItem + "',c.MaterNo,c.MaterVer,c.MaterName,a.Qty,0,b.Price,b.Amount,convert(char(10),getdate(),120),c.MaterUnit,b.Amount,c.MaterSpec,c.MaterXH,'" + JQDate + "','',b.CMan,b.Phone, " +
                       " '" + PName + "','" + Pay + "','" + C1 + "','" + C2 + "','" + C3 + "','" + C4 + "','" + C5 + "','" + HTNo + "','','" + QT + "','','PO待审核','" + InMan + "', '" + Remark + "' " +
                       " from T_PRPrice a join T_PRSupplier b on a.PPNo=b.PPNo join T_MaterInfo c on a.MaterNo=c.MaterNo "+
                       " where a.PPNo='" + PPNo + "' and b.SupplierNo= '" + SupplierNo + "' and a.MaterNo= '" + MNo + "'  ";

                // 如果勾选了，会插入临时表，需要删除它。
                sStr = " delete T_TempInfo where SNo='" + PPNo + "' and ANo='" + PRNo + "' and CNo='" + SupplierNo + "' and Kind='PR单' ";


                links.Add(sSQLs);  //   
                links.Add(sSQL);
                links.Add(sStr);

                try{
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex){
                    sstr = ex.Message.ToString();
                    throw;
                }

            }
            else if (sFlag == "7")  // 进入该界面，把之前勾选放在临时表的PR单全部删除
            {
                sSQLs = " delete T_TempInfo where InMan='" + InMan + "' and Kind='PR单' ";

                links.Add(sSQLs);  //   

                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }
            }
            else if (sFlag == "8")  // 勾选PR单据，临时存放，用来批量生成PO单据 20210606:暂时不使用勾选记录存储数据方式，在记录后面增加一个按钮实现该功能
            {
                DataTable sdt = DBHelper.GetDataTable("select InMan from T_TempInfo where SNo='" + PPNo + "' and ANo='" + PRNo + "' and CNo='" + SupplierNo + "' and Kind='PR单' ");
                if (sdt.Rows.Count > 0)  //  
                {
                    sSQLs = " delete T_TempInfo  where SNo='" + PPNo + "' and ANo='" + PRNo + "' and CNo='" + SupplierNo + "' and Kind='PR单' ";
                }

                sSQL = " insert into T_TempInfo(SNo,ANo,BNo,CNo,One,Two,Three,Four,Five,Six,Kind,InMan)  " +
                       " values('" + PPNo + "','" + PRNo + "','" + PRItem + "','" + SupplierNo + "','" + PName + "','" + JQDate + "','" + Pay + "','" + C1 + "','','','PR单','" + InMan + "') ";

                links.Add(sSQLs);  //   
                links.Add(sSQL);

                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }
            }
            else if (sFlag == "9")  //  ，删除临时存放的PR单据  
            { 
                sSQLs = " delete T_TempInfo where SNo='" + PPNo + "' and ANo='" + PRNo + "' and CNo='" + SupplierNo + "' and InMan='" + InMan + "' and Kind='PR单' ";  // 取消时，带上人，这样别人勾选的，不要删除；插入不需要


                links.Add(sSQLs);  //   

                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }
            }
            else if (sFlag == "10")  // 批量生成PO单据
            {
                sSQL = "select distinct CNo,Four from T_TempInfo where InMan='" + InMan + "' and Kind='PR单' "; // 供应商 + 税率相同，则产生一个PO单据
                DataTable sdt = DBHelper.GetDataTable(sSQL);
                for (var j = 0; j < sdt.Rows.Count; j++)  //  
                {
                    sPSNo = sdt.Rows[j]["CNo"].ToString();  // 供应商编码
                    sSL = sdt.Rows[j]["Four"].ToString();  //税率
                    

                    // 产生PO单据
                    sMaxNo = DBHelper.GetMaxNo("T_PurchaseInfo where convert(char(10),InDate,120)=convert(char(10),getdate(),120) ", "PurchNo");//   PO2104110001
                    if (sMaxNo == ""){
                        sNo = "PO" + CreateAllNo.CreateBillNo(2, 3) + "1";
                    }
                    else{
                        string sTemp = sMaxNo.Substring(8, 4);
                        iMax = int.Parse(sTemp) + 1;
                        int len = iMax.ToString().Length;
                        i = 4 - len;

                        sNo = "PO" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
                    }

                    string sDate = DateTime.Now.ToString("yyMMdd");
                    sMaxNo = DBHelper.GetMaxNo("T_PurchaseInfo where convert(char(10),InDate,120)=convert(char(10),getdate(),120) and SupplierNo='" + sPSNo + "' ", "HTNo");//  A24-210108-01  PO2103 00001
                    if (sMaxNo == ""){
                        sHTNo = sPSNo + "-" + sDate + "-01";
                    }
                    else {
                        string sTemp = sMaxNo.Substring(11, 2);
                        iMax = int.Parse(sTemp) + 1;
                        int len = iMax.ToString().Length;
                        if (len == 1)
                        {
                            sHTNo = sPSNo + "-" + sDate + "-" + "0" + iMax.ToString();
                        }
                        else
                        {
                            sHTNo = sPSNo + "-" + sDate + "-" + iMax.ToString();
                        }
                    }


                    sSQL = " insert into T_PurchaseInfo(PurchNo,CompanyNo,PPNo,PurchMan,PurchDate,SupplierNo,SupplierEn,SupplierMan,TransportWay,TransportAddr,ArrivalDate,PayWay,InvioceKind,Amount,PayMoney,Currency,Phone,KeyInKind,HTNo,Status,PurchKind,InMan,Remark) " +
                           " select top 1  '" + sNo + "',CompanyNo,'','" + InName + "',convert(char(10),getdate(),120),SupplierNo,SupplierEn,InchangeMan,'','', '', '','',0,0,'',Phone,'PR', '" + sHTNo + "','PO待审核','PR转PO', '" + InMan + "', '" + Remark + "' " +
                           " from T_SupplierInfo where  SupplierNo= '" + sPSNo + "' ";


                    sSQLs = " insert into T_PurchaseItemInfo(PurchNo,PurchItem,CompanyNo,PPNo,SupplierNo,QsNo,PRItem,MaterNo,MaterVer,MaterName,PurchNum,OverNum,PurchPrice,Tax,Amount,PurchDate,MaterUnit,NetValue,MaterSpec,MaterXH,ArrivalDate,TransportWay,SupplierMan,Phone, " +
                           " ProductName,PayWay,CHOne,CHTwo,CHThree,CHFour,CHFive,SaleOrderNo,OrderItemNo,NowRemark,CDAudDesc,Status,InMan,Remark) " +
                           " select '" + sNo + "',a.PPNo+b.SupplierNo,a.CompanyNo,a.PPNo,b.SupplierNo,a.PRNo,a.PRItem,c.MaterNo,c.MaterVer,c.MaterName,a.Qty,0,b.Price,'" + sSL + "',b.Amount,convert(char(10),getdate(),120),c.MaterUnit,b.Amount,c.MaterSpec,c.MaterXH,d.Two,'',b.CMan,b.Phone, " +
                           " d.One,d.Three,'" + C1 + "','" + C2 + "','" + C3 + "','" + C4 + "','" + C5 + "','" + sHTNo + "','','" + QT + "','','PO待审核','" + InMan + "', '" + Remark + "' " +
                           " from T_PRPrice a join T_PRSupplier b on a.PPNo=b.PPNo "+ 
                           " join T_TempInfo d on b.PPNo=d.SNo and b.SupplierNo=d.CNo left join T_MaterInfo c on a.MaterNo=c.MaterNo  " +
                           " where d.CNo= '" + sPSNo + "' and d.Four='" + sSL + "'  ";


                    // 如果勾选了，会插入临时表，需要删除它。
                    sStr = " delete T_TempInfo where  CNo='" + sPSNo + "' and Four='" + sSL + "' and Kind='PR单' ";



                    links.Add(sSQLs);  //   
                    links.Add(sSQL);
                    links.Add(sStr);

                    try {
                        iFlag = DBHelper.ExecuteSqlTranStr(links);
                        links.Clear();
                    }
                    catch (Exception ex) {
                        sstr = ex.Message.ToString();
                        throw;
                    }

                }  // for


            }
            else if (sFlag == "2")  // 修改PO信息
            {
                sSQL = " update T_PurchaseInfo set ArrivalDate='" + JQDate + "',PayWay='" + Pay + "',Remark='" + Remark + "',Status='PO待审核'  where PurchNo='" + PO + "' ";
                sSQLs = " update T_PurchaseItemInfo set ProductName='" + PName + "',ArrivalDate='" + JQDate + "',PayWay='" + Pay + "',CHOne='" + C1 + "',CHTwo='" + C2 + "',CHThree='" + C3 + "',CHFour='" + C4 + "',CHFive='" + C5 + "',Remark='" + Remark + "',NowRemark='" + QT + "',Status='PO待审核' " +
                        " where PurchNo='" + PO + "' and PurchItem='" + POItem + "' ";


                links.Add(sSQLs);  //   
                links.Add(sSQL);

                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }
            }
            else if (sFlag == "3")  // 删除PO
            {
                sSQL = " delete T_PurchaseItemInfo where PurchNo='" + PO + "' and PurchItem='" + POItem + "' ";


                // 判断这个采购订单下面是否有项次，没有，则采购订单表头也删除
                DataTable sdt = DBHelper.GetDataTable("select InMan from T_PurchaseItemInfo where  PurchNo='" + PO + "' ");
                if (sdt.Rows.Count == 1)  // 说明这个采购订单只有一个项次，也是现在删除的这个
                {
                    sSQLs = " delete T_PurchaseInfo where PurchNo='" + PO + "' ";
                }


                links.Add(sSQLs);  //   
                links.Add(sSQL);

                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }
            }
            else if (sFlag == "4")  // 审核
            {
                sSQL = " update T_PurchaseItemInfo set AudDate=convert(char(16),getdate(),120),AudMan='" + InName + "',AudDesc='" + PName + "',Status='" + QT + "' where PurchNo='" + PO + "' and PurchItem='" + POItem + "' ";
                sSQLs = " update T_PurchaseInfo set  Status='" + QT + "' where PurchNo='" + PO + "' ";


                links.Add(sSQLs);  //   
                links.Add(sSQL);

                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }
            }
            else if (sFlag == "5")  //取消
            {
                sSQL = " update T_PurchaseItemInfo set AudDate=convert(char(16),getdate(),120),AudMan='" + InName + "',AudDesc='" + PName + "',Status='取消采购订单',CDAudDesc='采购订单取消' " +
                       " where PurchNo='" + PO + "' and PurchItem='" + POItem + "' ";
                sSQLs = " update T_PurchaseInfo set  Status='取消采购订单' where PurchNo='" + PO + "' ";


                links.Add(sSQLs);  //   
                links.Add(sSQL);

                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }
            }
            else if (sFlag == "6")  //恢复取消
            {
                sSQL = " update T_PurchaseItemInfo set AudDate=convert(char(16),getdate(),120),AudMan='" + InName + "',AudDesc='" + PName + "',Status='PO待审核',CDAudDesc='采购订单恢复' " +
                       " where PurchNo='" + PO + "' and PurchItem='" + POItem + "' ";
                sSQLs = " update T_PurchaseInfo set  Status='PO待审核' where PurchNo='" + PO + "' ";


                links.Add(sSQLs);  //   
                links.Add(sSQL);

                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }
            }




            return iFlag + sstr;
        }


        // 维护账务相关信息
        public static string AddEditFinInfo(string SNo, string ANo, string BNo, string CNo, string RNo, string MNo, string C1, string C2, string C3, string C4, string InMan, string InName, string sComp, string Remark, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sStr = string.Empty;
            string sStrs = string.Empty;
            string sstr = string.Empty;
            string sSL = string.Empty;
            string sPSNo = string.Empty;
            string sMaxNo = string.Empty;
            int iMax = 0;
            int i = 0;
            string sNo = string.Empty;

            List<string> links = new List<string>();

            if (sFlag == "1")  // 保存临时选中的信息 
            {
                DataTable sdt = DBHelper.GetDataTable("select InMan from T_TempInfo where SNo='" + SNo + "' and Kind='对账单' ");
                if (sdt.Rows.Count > 0)  //  
                {
                    sSQLs = " delete T_TempInfo  where SNo='" + SNo + "' and Kind='对账单' ";
                }

                sSQL = " insert into T_TempInfo(SNo,ANo,BNo,CNo,One,Two,Three,Four,Kind,InMan)  "+
                       " values('" + SNo + "','" + ANo + "','" + BNo + "','" + CNo + "','" + C1 + "','" + C2 + "','" + C3 + "','" + C4 + "','对账单','" + InMan + "') ";

                links.Add(sSQLs);  // 先删除  
                links.Add(sSQL);
                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }
            }
            else if (sFlag == "2")  // 修改选中记录的信息
            {
                sSQL = " update T_TempInfo set One='" + C1 + "' where SNo='" + SNo + "' and ANo='" + ANo + "' and Kind='对账单' ";
                links.Add(sSQL);
                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }
            }
            else if (sFlag == "3")  // 取消勾选，删除信息
            {
                sSQL = " delete T_TempInfo where SNo='" + SNo + "' and ANo='" + ANo + "' and Kind='对账单' ";

                links.Add(sSQL);
                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }
            }
            else if (sFlag == "4")  // 提交PO收货记录，生成对账单 相同
            {
                sSQL = "select distinct One,BNo from T_TempInfo where InMan='" + InMan + "' and Kind='对账单' ";
                DataTable sdt = DBHelper.GetDataTable(sSQL);
                for (var j=0;j<sdt.Rows.Count;j++)  //  
                {
                    sSL = sdt.Rows[j]["One"].ToString();  //税率
                    sPSNo = sdt.Rows[j]["BNo"].ToString();  // 供应商编码

                    // 产生对账单号
                    sMaxNo = DBHelper.GetMaxNo("T_AccountInfo where convert(char(10),InDate,120)=convert(char(10),getdate(),120) ", "ACNo");// ******** 0001
                    if (sMaxNo == "")
                    {
                        sNo = CreateAllNo.CreateBillNo(1, 3) + "1";
                    }
                    else
                    {
                        string sTemp = sMaxNo.Substring(8, 4);
                        iMax = int.Parse(sTemp) + 1;
                        int len = iMax.ToString().Length;
                        i = 4 - len;

                        sNo = CreateAllNo.CreateBillNo(1, i) + iMax.ToString();
                    }

                    sSQLs = " insert into T_AccountInfo(ACNo,SupplierNo,SupplierEn,TaxRate,Num,Price,Amount,Currency,PayDate,Status,InName,InMan,CompanyNo,Remark) " +
                            " select '" + sNo + "','" + sPSNo + "',SupplierEn," + sSL + ",SUM(Num),0,SUM(Amount),Currency,convert(char(16),GETDATE(),120),'待录入发票','" + InName + "','" + InMan + "','" + sComp + "','' from ( " +
                            "     select b.SupplierEn,convert(numeric(12,2),a.Three) as Num,a.Two,convert(numeric(12,2),(convert(numeric(12,2),a.Three)*a.Two*(1+" + sSL + "/100))) as Amount,b.Currency " +
                            "     from T_TempInfo a join T_SupplierInfo b on a.BNo=b.SupplierNo " +
                            "     where a.One='" + sSL + "' and a.BNo='" + sPSNo + "' and a.InMan='" + InMan + "' " +
                            "  ) aa group by SupplierEn,Currency ";

                    sSQL = " insert into T_AccountPO(ACNo,PurchNo,PurchItem,ReceiveNo,InIQCNo,MaterNo,MaterName,SupplierNo,TaxRate,Num,Price,Amount,Status,InMan,Remark) " +
                           " select '" + sNo + "',b.PurchNo,b.PurchItem,a.ANo,a.CNo,b.MaterNo,b.MaterName,'" + sPSNo + "'," + sSL + ",a.Three,a.Two, "+
                           " convert(numeric(12,2),(convert(numeric(12,2),a.Three)*a.Two*(1+" + sSL + "/100))),'','" + InMan + "','' " +
                           " from T_TempInfo a join T_PurchaseItemInfo b on a.SNo=ltrim(rtrim(b.PurchNo))+ltrim(rtrim(b.PurchItem)) " +
                           " where a.One='" + sSL + "' and a.BNo='" + sPSNo + "' and a.InMan='" + InMan + "' ";


                    // 更新PO收货记录（IQC已检验记录）为已对账
                    sStrs = " update T_InspectInfo set Status='已对账' from T_InspectInfo a join T_TempInfo b on a.InIQCNo=b.CNo where b.One='" + sSL + "' and b.BNo='" + sPSNo + "' and b.InMan='" + InMan + "' ";

                    //  删除临时记录
                    sStr = " delete T_TempInfo where One='" + sSL + "' and BNo='" + sPSNo + "' and InMan='" + InMan + "' and Kind='对账单' ";


                    links.Add(sSQLs);   
                    links.Add(sSQL);
                    links.Add(sStrs);  // 先更新。后删除
                    links.Add(sStr);
                    

                    try
                    {
                        iFlag = DBHelper.ExecuteSqlTranStr(links);
                        links.Clear();
                    }
                    catch (Exception ex)
                    {
                        sstr = ex.Message.ToString();
                        throw;
                    }


                }
                
            }
            else if (sFlag == "5")  // 删除已生成的对账单记录
            {

                //回退IQC的状态
                sStrs = " update T_InspectInfo set Status='IQC已检验' from T_InspectInfo a join T_AccountPO b on a.InIQCNo=b.InIQCNo where b.ACNo='" + ANo + "' and a.Status='已对账' ";
                sSQLs = " delete T_AccountPO where ACNo='" + ANo + "' ";
                sSQL = " delete T_AccountInfo where ACNo='" + ANo + "' ";

                links.Add(sStrs);
                links.Add(sSQLs);
                links.Add(sSQL);

                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }
            }
            else if (sFlag == "6")  // 录入发票  --  供应商给公司的
            {
                string sAmount = string.Empty;
                string sFPAmount = string.Empty;
                sSQL = "select Amount,isnull(b.FPAmount,0) as FPAmount from T_AccountInfo a left join (select ACNo,sum(FPAmount) as FPAmount from T_AccountFP group by ACNo) b on a.ACNo=b.ACNo where a.ACNo='" + ANo + "' ";
                DataTable sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)  // 
                {
                    sAmount = sdt.Rows[0]["Amount"].ToString();
                    sFPAmount = sdt.Rows[0]["FPAmount"].ToString();
                }
                else
                {
                    sAmount = "0";
                }

               // if (double.Parse(sFPAmount) + double.Parse(C3) >= double.Parse(sAmount)) // 说明这个对账单已录入完成发票金额
               // {
                    sSQLs = " update T_AccountInfo set Status='已录入发票' where ACNo='" + ANo + "' ";
               // }

                sSQL = " insert into T_AccountFP(ACNo,FPCode,FPNo,FPDate,Currency,TaxRate,NotTaxAmount,TaxAmount,FPAmount,SupplierNo,SupplierEn,InName,Kind,Status,InMan,CompanyNo,Remark)  " +
                       " select ACNo,'" + BNo + "','" + CNo + "','" + RNo + "',Currency,TaxRate,'" + C1 + "','" + C2 + "','" + C3 + "',SupplierNo,SupplierEn,'" + InName + "','供应商发票','','" + InMan + "',CompanyNo,'" + Remark + "'  " +
                       " from T_AccountInfo where ACNo='" + ANo + "' ";

                links.Add(sSQLs);
                links.Add(sSQL);
                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }
            }
            else if (sFlag == "7")  //修改发票信息
            {
                sSQL = " update T_AccountFP set FPCode='" + BNo + "',FPNo='" + CNo + "',FPDate='" + RNo + "',NotTaxAmount='" + C1 + "',TaxAmount='" + C2 + "',FPAmount='" + C3 + "',Remark='" + Remark + "' " +
                       " where SeqNo='" + C4 + "' ";

                links.Add(sSQL);
                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }
            }
            else if (sFlag == "8")  //删除发票信息
            {

                sSQL = "select InMan from T_AccountFP where ACNo='" + ANo + "' ";
                DataTable sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 1)  // 说明这个对账单，删除了本次记录，还有其他的发票记录
                {
                    sSQLs = " update T_AccountInfo set Status='已录入发票' where ACNo='" + ANo + "' ";
                }
                else
                {
                    sSQLs = " update T_AccountInfo set Status='待录入发票' where ACNo='" + ANo + "' ";
                }


                sSQL = " delete T_AccountFP where SeqNo='" + C4 + "' ";

                links.Add(sSQL);
                links.Add(sSQLs);
                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }
            }
            else if (sFlag == "9")  // 录入发票  -- 给客户的。 
            {
                var sCustNo = SNo.Substring(0, SNo.IndexOf("_"));
                var sCustEN = SNo.Substring(SNo.IndexOf("_")+1, SNo.Length - SNo.IndexOf("_")-1);
                // 产生对账单号给客户发票
                sMaxNo = DBHelper.GetMaxNo("T_AccountFP where convert(char(10),InDate,120)=convert(char(10),getdate(),120) and Kind='给客户发票' ", "ACNo");// C******** 0001
                if (sMaxNo == "")
                {
                    sNo = "C"+ CreateAllNo.CreateBillNo(1, 3) + "1";
                }
                else
                {
                    string sTemp = sMaxNo.Substring(9, 4);
                    iMax = int.Parse(sTemp) + 1;
                    int len = iMax.ToString().Length;
                    i = 4 - len;

                    sNo = "C" + CreateAllNo.CreateBillNo(1, i) + iMax.ToString();
                }

                sSQL = " insert into T_AccountFP(ACNo,FPCode,FPNo,FPDate,Currency,TaxRate,NotTaxAmount,TaxAmount,FPAmount,SupplierNo,SupplierEn,InName,Kind,Status,InMan,CompanyNo,Remark)  " +
                       " values('" + sNo + "','" + BNo + "','" + CNo + "','" + RNo + "','" + MNo + "','" + C4 + "','" + C1 + "','" + C2 + "','" + C3 + "','" + sCustNo + "','" + sCustEN + "', " +
                       " '" + InName + "','给客户发票','','" + InMan + "','" + sComp + "','" + Remark + "')  ";

                links.Add(sSQL);
                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }
            }
            else if (sFlag == "10")  //修改发票信息  --给客户的
            {
                var sCustNo = SNo.Substring(0, SNo.IndexOf("_"));
                var sCustEN = SNo.Substring(SNo.IndexOf("_") + 1, SNo.Length - SNo.IndexOf("_") - 1);

                sSQL = " update T_AccountFP set Currency='" + MNo + "',TaxRate='" + C4 + "',FPCode='" + BNo + "',FPNo='" + CNo + "',SupplierNo='" + sCustNo + "',SupplierEn='" + sCustEN + "', " +
                       " FPDate='" + RNo + "',NotTaxAmount='" + C1 + "',TaxAmount='" + C2 + "',FPAmount='" + C3 + "',Remark='" + Remark + "' " +
                       " where ACNo='" + ANo + "' ";

                links.Add(sSQL);
                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }
            }
            else if (sFlag == "11")  //删除发票信息  --给客户的
            {
                sSQL = " delete T_AccountFP where ACNo='" + ANo + "' ";

                links.Add(sSQL);
                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    sstr = ex.Message.ToString();
                    throw;
                }
            }




            return iFlag + sstr;
        }

        /// <summary>
        /// 获取PO勾选记录
        /// </summary>
        /// <param name="PO"></param>
        /// <param name="Item"></param>
        /// <param name="RNo"></param>
        /// <param name="MNo"></param>
        /// <param name="sInMan"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetChoosePO(string PO, string Item, string RNo, string MNo, string sInMan, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string sChStr = string.Empty;
            string sNum = string.Empty;
            DataTable sdt = new DataTable();

            if (sFlag == "63-1")  // 获取已勾选的采购收货记录，准备生成对账单
            {
                sSQL = " select a.SNo,a.ANo,b.MaterNo,b.MaterName,a.Two,b.SupplierNo,a.One,a.Three,'<label onclick=''DelChooge(1)'' class=''LabelDelBtn''>删除</label>' as DEL, " +
                       " convert(numeric(12,2),convert(numeric(12,2),a.Two)*convert(numeric(12,2),a.Three)*convert(numeric(12,2),a.One) /100) as AllAmount "+
                       " from T_TempInfo a join T_MaterReceive b on a.SNo=ltrim(rtrim(b.PurchaseNo))+ltrim(rtrim(b.PurchaseItem)) and a.ANo=b.ReceiveNo " +
                       " where a.InMan = '" + sInMan + "' and a.Kind='对账单' order by b.SupplierNo,a.One ";
            }
            if (sFlag == "63-2")  // 获取已勾选的采购收货记录，准备生成对账单
            {
                sSQL = " select b.PurchNo+b.PurchItem as SNo,a.ACNo as ANo,b.MaterNo,b.MaterName,b.Price as Two,a.SupplierNo,b.TaxRate as One,b.Num as Three,b.Amount as AllAmount " +
                       " from T_AccountInfo a join T_AccountPO b on a.ACNo=b.ACNo " +
                       " where a.ACNo = '" + PO + "' order by b.PurchNo ";
            }
            if (sFlag == "63-3")  // 获取已勾选的PR单，准备生成PO单
            {
                sSQL = " select a.SNo,a.ANo,a.BNo,a.CNo,a.One,a.Two,a.Three,a.Four,'<label onclick=''DelChooge(1)'' class=''LabelDelBtn''>删除</label>' as DEL " +
                       " from T_TempInfo a " +
                       " where  a.InMan = '" + sInMan + "' and a.Kind='PR单' order by a.CNo,a.Four ";
            }

            sdt = DBHelper.GetDataTable(sSQL);
            return sdt;
        }


        // 查询供应商质量相关数据
        public static DataTable GetSupplierQA(string SPNo, string SPName, string BDate, string EDate, string A, string B, string C, int Row, int num, string sInMan, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string sNum = string.Empty;
            DataTable sdt = new DataTable();

            if (sFlag == "5") // 显示采购物料的单据历史情况
            {
                if (BDate == ""){
                    BDate = DateTime.Now.ToString("yyyy-MM") + "-01";
                }
                if (EDate == "")
                {
                    EDate = DateTime.Now.ToString("yyyy-MM") + "-31";
                }

                sSQL = " select COUNT(*) as C from T_PurchaseItemInfo a join T_PurchaseInfo b on a.PurchNo=b.PurchNo " +
                       " where a.CompanyNo = '" + sComp + "' and a.MaterNo = '" + A + "' and CONVERT(char(10),a.InDate,120)>='" + BDate + "' and CONVERT(char(10),a.InDate,120)<='" + EDate + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sNum = sdt.Rows[0]["C"].ToString();
                }
                else
                {
                    sNum = "0";
                }

                sSQL = " select  '" + sNum + "' as NumCount,a.PurchNo,a.PurchItem,a.PurchNum,a.PurchPrice,b.SupplierNo,b.SupplierEn,a.MaterName,a.PurchDate " +
                       " from T_PurchaseItemInfo a join T_PurchaseInfo b on a.PurchNo=b.PurchNo "+
                       " where a.CompanyNo = '" + sComp + "' and a.MaterNo = '" + A + "' and CONVERT(char(10),a.InDate,120)>='" + BDate + "' and CONVERT(char(10),a.InDate,120)<='" + EDate + "' "+
                       " order by b.SupplierNo,a.PurchDate desc ";

                sdt = DBHelper.GetDataTable(sSQL);
            }
            else
            {

                SqlParameter[] parameters = {
					new SqlParameter("@lnNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
					new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,10),
					new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
                parameters[0].Value = SPNo;
                parameters[1].Value = SPName;
                parameters[2].Value = BDate;
                parameters[3].Value = EDate;
                parameters[4].Value = A;
                parameters[5].Value = B;
                parameters[6].Value = C;
                parameters[7].Value = Row;
                parameters[8].Value = num;
                parameters[9].Value = sInMan;
                parameters[10].Value = sFlag;
                parameters[11].Value = "";

                DataSet DS = DBHelper.RunProcedureForDS("P_GetSupplierPayRateForPage", parameters);
                sdt = DS.Tables[0];
            }

            return sdt;
        }





















    }
}
