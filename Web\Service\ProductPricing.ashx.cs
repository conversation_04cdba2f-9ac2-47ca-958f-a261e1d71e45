﻿using System;
using System.Collections;
using System.Data;
using System.Web;
using System.Web.SessionState;
using BLL;
using Common;
using Newtonsoft.Json;
using System.Collections.Generic; // Added for List<Dictionary<string, object>>

namespace Web.Service
{
    /// <summary>
    /// ProductPricing 的摘要说明 - 产品定价套餐管理
    /// </summary>
    public class ProductPricing : IHttpHandler, IRequiresSessionState
    {
        private string _loginUser;
        private string _loginName;
        private string _companyNo;

        #region 处理请求
        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "text/plain";
            string Result = string.Empty;
            string Operate = context.Request.Params["OP"];//获取传入的操作过程
            string DataParams = context.Request.Params["Data"];//获取传入的Data值

            // 验证登录状态
            if (!CheckLogin())
            {
                context.Response.Write(JsonConvert.SerializeObject(new { Msg = "LoginError", Login = _loginName }));
                return;
            }

            switch (Operate)
            {
                case "GetSetMenuInfo": // 获取套餐信息
                    int slimit = int.Parse(context.Request.Params["limit"] ?? "10");
                    int spage = int.Parse(context.Request.Params["page"] ?? "1");
                    GetSetMenuInfo(DataParams, slimit, spage);
                    break;

                case "AddSetMenuInfo": // 添加套餐信息
                case "UpdateSetMenuInfo": // 修改套餐信息
                case "UpdateSetMenuStatus": // 更新套餐状态（禁用/启用）
                case "DeleteSetMenuInfo": // 删除套餐信息
                    if (string.IsNullOrEmpty(DataParams))
                    {
                        context.Response.Write(JsonConvert.SerializeObject(new { Msg = "Error: 无效的参数" }));
                        return;
                    }
                    Result = ProcessOperation(Operate, DataParams);
                    context.Response.Write(Result);
                    break;

                default:
                    context.Response.Write(JsonConvert.SerializeObject(new { Msg = "Error: 无效的操作类型" }));
                    break;
            }
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 检查登录状态并获取用户信息
        /// </summary>
        private bool CheckLogin()
        {
            if (HttpContext.Current.Session["LoginName"] != null)
            {
                _loginUser = HttpContext.Current.Session["LoginName"].ToString();
                _loginName = HttpContext.Current.Session["FullName"].ToString();
                _companyNo = HttpContext.Current.Session["CompanyNo"].ToString();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 处理参数并执行相应操作
        /// </summary>
        private string ProcessOperation(string operation, string jsonParams)
        {
            try
            {
                var paramObj = DeserializeParams(jsonParams);
                if (paramObj == null)
                {
                    return JsonConvert.SerializeObject(new { Msg = "Error: 无效的参数格式" });
                }

                string result = "";
                switch (operation)
                {
                    case "AddSetMenuInfo":
                        result = HandleAddOperation(paramObj);
                        break;
                    case "UpdateSetMenuInfo":
                        result = HandleUpdateOperation(paramObj);
                        break;
                    case "UpdateSetMenuStatus":
                        result = HandleStatusOperation(paramObj);
                        break;
                    case "DeleteSetMenuInfo":
                        result = HandleDeleteOperation(paramObj);
                        break;
                }

                return JsonConvert.SerializeObject(new { Msg = string.IsNullOrEmpty(result) ? "Success" : result });
            }
            catch (Exception ex)
            {
                return JsonConvert.SerializeObject(new { Msg = "Error: " + ex.Message });
            }
        }

        /// <summary>
        /// 反序列化参数
        /// </summary>
        private Hashtable DeserializeParams(string jsonParams)
        {
            try
            {
                return JsonConvert.DeserializeObject<Hashtable>(jsonParams);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 获取参数值
        /// </summary>
        private T GetParamValue<T>(Hashtable paramObj, string key, T defaultValue = default)
        {
            if (paramObj == null || !paramObj.ContainsKey(key) || paramObj[key] == null)
                return defaultValue;

            try
            {
                // 如果是字符串类型，去除前后空格
                if (typeof(T) == typeof(string))
                {
                    string value = paramObj[key].ToString().Trim();
                    return value.Length == 0 ? defaultValue : (T)(object)value;
                }
                if (typeof(T) == typeof(bool))
                    return (T)(object)Convert.ToBoolean(paramObj[key]);
                if (typeof(T) == typeof(int))
                    return (T)(object)Convert.ToInt32(paramObj[key]);
                if (typeof(T) == typeof(decimal))
                    return (T)(object)Convert.ToDecimal(paramObj[key]);
                return (T)Convert.ChangeType(paramObj[key], typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }
        #endregion

        #region 业务操作处理
        /// <summary>
        /// 处理添加套餐信息的操作
        /// </summary>
        /// <param name="paramObj">包含套餐信息的参数集合</param>
        /// <returns>
        /// - Success: 操作成功
        /// - MenuNameExists: 套餐名称已存在
        /// - Error: xxx: 具体错误信息
        /// </returns>
        private string HandleAddOperation(Hashtable paramObj)
        {
            // 验证套餐名称
            string menuName = GetParamValue<string>(paramObj, "MenuName");
            if (string.IsNullOrEmpty(menuName))
            {
                return "Error: 套餐名称不能为空";
            }

            // 检查套餐名称是否已存在
            if (ProductPricingBll.CheckMenuNameExists(menuName, _companyNo))
            {
                return "MenuNameExists";
            }

            // 获取并验证工单超量价格JSON
            string awoPrice = GetParamValue<string>(paramObj, "AWOPrice");
            if (string.IsNullOrEmpty(awoPrice))
            {
                return "Error: 工单超量价格不能为空";
            }

            try
            {
                // 验证JSON格式是否正确
                var priceData = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(awoPrice);
                if (priceData == null || priceData.Count == 0)
                {
                    return "Error: 无效的工单超量价格格式";
                }

                // 验证价格梯度数据的完整性和合法性
                for (int i = 0; i < priceData.Count; i++)
                {
                    if (!priceData[i].ContainsKey("start") || !priceData[i].ContainsKey("price"))
                    {
                        return "Error: 工单超量价格数据格式不完整";
                    }
                    if (i < priceData.Count - 1 && !priceData[i].ContainsKey("end"))
                    {
                        return "Error: 除最后一个梯度外，其他梯度必须设置结束数量";
                    }
                }
            }
            catch
            {
                return "Error: 工单超量价格数据格式错误";
            }

            // 生成新的套餐编号
            string menuNo = CreateMenuNo();

            // 调用BLL层添加套餐信息
            return ProductPricingBll.AddSetMenuInfo(
                menuNo,                                          // 套餐编号
                menuName,                                        // 套餐名称
                GetParamValue<int>(paramObj, "MenuLevel"),      // 套餐等级
                GetParamValue<decimal>(paramObj, "BasePrice"),  // 基础价格
                GetParamValue<int>(paramObj, "BUP"),           // 包含活跃用户数
                GetParamValue<int>(paramObj, "TVA"),           // 包含工单数
                GetParamValue<decimal>(paramObj, "AUserPrice"), // 额外用户单价
                awoPrice,                                       // 工单超量价格(JSON格式的梯度价格)
                GetParamValue<string>(paramObj, "DFunction"),   // 核心功能差异
                GetParamValue<bool>(paramObj, "SLA"),          // 标准在线支持
                GetParamValue<bool>(paramObj, "DepthTrain"),   // 深度培训
                GetParamValue<bool>(paramObj, "IMServices"),   // 实施服务
                GetParamValue<bool>(paramObj, "CustomDev"),    // 高级定制开发
                GetParamValue<bool>(paramObj, "InterfaceDev"), // 特定接口开发
                GetParamValue<bool>(paramObj, "OnsiteSV"),     // 专人驻场服务
                "未禁用",                                       // 初始状态
                GetParamValue<string>(paramObj, "Remark"),     // 备注
                _companyNo,                                     // 公司编号
                _loginUser                                      // 操作人
            );
        }

        /// <summary>
        /// 处理更新套餐信息的操作
        /// </summary>
        /// <param name="paramObj">包含套餐信息的参数集合</param>
        /// <returns>
        /// - Success: 操作成功
        /// - MenuNameExists: 套餐名称已存在
        /// - Error: xxx: 具体错误信息
        /// </returns>
        private string HandleUpdateOperation(Hashtable paramObj)
        {
            // 验证必填参数
            string menuNo = GetParamValue<string>(paramObj, "MenuNo");
            string menuName = GetParamValue<string>(paramObj, "MenuName");

            if (string.IsNullOrEmpty(menuNo))
                return "Error: 套餐编号不能为空";
            if (string.IsNullOrEmpty(menuName))
                return "Error: 套餐名称不能为空";

            // 检查套餐名称是否已存在（排除自身）
            if (ProductPricingBll.CheckMenuNameExists(menuName, _companyNo, menuNo))
                return "MenuNameExists";

            // 获取并验证工单超量价格JSON
            string awoPrice = GetParamValue<string>(paramObj, "AWOPrice");
            if (string.IsNullOrEmpty(awoPrice))
            {
                return "Error: 工单超量价格不能为空";
            }

            try
            {
                // 验证JSON格式是否正确
                var priceData = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(awoPrice);
                if (priceData == null || priceData.Count == 0)
                {
                    return "Error: 无效的工单超量价格格式";
                }

                // 验证价格梯度数据的完整性和合法性
                for (int i = 0; i < priceData.Count; i++)
                {
                    if (!priceData[i].ContainsKey("start") || !priceData[i].ContainsKey("price"))
                    {
                        return "Error: 工单超量价格数据格式不完整";
                    }
                    if (i < priceData.Count - 1 && !priceData[i].ContainsKey("end"))
                    {
                        return "Error: 除最后一个梯度外，其他梯度必须设置结束数量";
                    }
                }
            }
            catch
            {
                return "Error: 工单超量价格数据格式错误";
            }

            // 调用BLL层更新套餐信息
            return ProductPricingBll.UpdateSetMenuInfo(
                GetParamValue<string>(paramObj, "Id"),          // 记录ID
                menuNo,                                          // 套餐编号
                menuName,                                        // 套餐名称
                GetParamValue<int>(paramObj, "MenuLevel"),      // 套餐等级
                GetParamValue<decimal>(paramObj, "BasePrice"),  // 基础价格
                GetParamValue<int>(paramObj, "BUP"),           // 包含活跃用户数
                GetParamValue<int>(paramObj, "TVA"),           // 包含工单数
                GetParamValue<decimal>(paramObj, "AUserPrice"), // 额外用户单价
                awoPrice,                                       // 工单超量价格(JSON格式的梯度价格)
                GetParamValue<string>(paramObj, "DFunction"),   // 核心功能差异
                GetParamValue<bool>(paramObj, "SLA"),          // 标准在线支持
                GetParamValue<bool>(paramObj, "DepthTrain"),   // 深度培训
                GetParamValue<bool>(paramObj, "IMServices"),   // 实施服务
                GetParamValue<bool>(paramObj, "CustomDev"),    // 高级定制开发
                GetParamValue<bool>(paramObj, "InterfaceDev"), // 特定接口开发
                GetParamValue<bool>(paramObj, "OnsiteSV"),     // 专人驻场服务
                GetParamValue<string>(paramObj, "Status", "未禁用"), // 状态
                GetParamValue<string>(paramObj, "Remark"),     // 备注
                _companyNo,                                     // 公司编号
                _loginUser                                      // 操作人
            );
        }

        private string HandleStatusOperation(Hashtable paramObj)
        {
            string menuNo = GetParamValue<string>(paramObj, "MenuNo");
            string status = GetParamValue<string>(paramObj, "Status");

            if (string.IsNullOrEmpty(menuNo))
                return "Error: 套餐编号不能为空";
            if (string.IsNullOrEmpty(status))
                return "Error: 状态不能为空";

            return ProductPricingBll.UpdateSetMenuInfo(
                "", menuNo, "", 0, 0, 0, 0, 0, "", "", false, false, false, false, false, false,
                status, "", _companyNo, _loginUser
            );
        }

        private string HandleDeleteOperation(Hashtable paramObj)
        {
            string menuNo = GetParamValue<string>(paramObj, "MenuNo");
            if (string.IsNullOrEmpty(menuNo))
                return "Error: 套餐编号不能为空";

            if (!ProductPricingBll.CheckMenuCanDelete(menuNo, _companyNo))
                return "CannotDelete";

            return ProductPricingBll.DeleteSetMenuInfo(menuNo, _companyNo, _loginUser);
        }
        #endregion

        #region 获取套餐信息
        private void GetSetMenuInfo(string Params, int rows, int page)
        {
            string sReturn = string.Empty;

            try
            {
                string sMenuNo = "";
                string sMenuName = "";
                string sStatus = "";

                // 解析查询参数
                if (!string.IsNullOrEmpty(Params))
                {
                    var queryParams = DeserializeParams(Params);
                    if (queryParams != null)
                    {
                        sMenuNo = GetParamValue<string>(queryParams, "MenuNo");
                        sMenuName = GetParamValue<string>(queryParams, "MenuName");
                        sStatus = GetParamValue<string>(queryParams, "Status");
                    }
                }

                // 调用BLL层获取数据
                DataTable dt = ProductPricingBll.GetSetMenuInfo(sMenuNo, sMenuName, sStatus, rows, page, _loginUser, _companyNo);
                
                if (dt != null && dt.Rows.Count > 0)
                {
                    int iSumCount = int.Parse(dt.Rows[0]["NumCount"].ToString());
                    string sJson = JsonConvert.SerializeObject(dt);
                    sReturn = "{\"code\":0,\"msg\":\"\",\"count\":" + iSumCount + ",\"data\":" + sJson + "}";
                }
                else
                {
                    sReturn = "{\"code\":0,\"msg\":\"无数据\",\"count\":0,\"data\":[]}";
                }
            }
            catch (Exception ex)
            {
                sReturn = "{\"code\":1,\"msg\":\"" + ex.Message + "\",\"count\":0,\"data\":[]}";
            }

            HttpContext.Current.Response.Write(sReturn);
        }
        #endregion

        #region 生成套餐编号
        private string CreateMenuNo()
        {
            string sDate = DateTime.Now.ToString("yyMMdd");
            string sNo = string.Empty;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_SetMenuInfo where SUBSTRING(MenuNo, 2, 6) = '" + sDate + "'", "MenuNo");
            if (string.IsNullOrEmpty(sMaxNo))
            {
                sNo = "M" + sDate + "0001";
            }
            else
            {
                string sTemp = sMaxNo.Substring(7, 4);
                iMax = int.Parse(sTemp) + 1;
                sNo = "M" + sDate + iMax.ToString().PadLeft(4, '0');
            }
            return sNo;
        }
        #endregion

        public bool IsReusable
        {
            get { return false; }
        }
    }
}