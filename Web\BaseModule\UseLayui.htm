﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" >
<title>如何使用Layui</title>
     <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css"/>
    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all">
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript" ></script>
    <script src="../js/BaseModule.js" type="text/javascript"></script>
    
    
    
    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function() {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
           // $('#dgDataList1').bootstrapTable('resetView', { height: h });
           // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
           // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=23&CorpID=" + sCorpID,
                success: function(data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=23&RA" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        //$('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                       // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=25-1' });
                    }
                }
            })
        });


        function Sheet_onclick(num) {

            if (num == 1) {
                document.getElementById("DivSheet1").style.display = "block";
                document.getElementById("DivSheet2").style.display = "none";

                document.getElementById("Sheet1").setAttribute("style", "width:80px; background-color:Gray; text-align:center; font-size:12px; font-weight:bold;");
                document.getElementById("Sheet2").setAttribute("style", "width:80px; background-color:white; text-align:center; font-size:12px; font-weight:bold;");
            }
            else if (num == 2) {  
                document.getElementById("DivSheet1").style.display = "none";
                document.getElementById("DivSheet2").style.display = "block";

                document.getElementById("Sheet1").setAttribute("style", "width:80px; background-color:white; text-align:center; font-size:12px; font-weight:bold;");
                document.getElementById("Sheet2").setAttribute("style", "width:80px; background-color:Gray; text-align:center; font-size:12px; font-weight:bold;");


            }   


        }
    

        // 获取TXT文件的内容
        function GetTxtInfo() {

            var sPath = $("#txtPath").val();  //文件路径

            var sFlag = "17-2";

            var Data = '';
            var Params = { No: "", Item: "", Path: sPath, A: "", B: "", C: "", D: "",Flag: sFlag };
            var Data = JSON.stringify(Params);

            $.ajax({
                type: "POST",
                url: "../Service/FlowConfigAjax.ashx?OP=GetTxtInfo&SCFlag=17-2",
                data: { Data: Data },
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    //$("#txtPath").val(parsedJson.Msg);
                    alert(parsedJson.Msg);
                },
                error: function (data) {
                    $("#LB_Flow").html("获取数据出错");
                }
            });
        }


   </script>
   
    <script type="text/javascript">

    
	//静态表格
        layui.use('table', function() {
            var table = layui.table;
            //转换静态表格
            table.init('mylist', {
                height: 'full-240' //高度最大化减去差值,也可以自己设置高度值：如 height:300
		  , count: 50 //数据总数 服务端获得
		  , limit: 20 //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
		  , page: true //开启分页
                //,toolbar: 'default'//工具栏
                // ,defaultToolbar:['filter', 'exports']
		   , limits: [10, 20, 30, 40, 50]//分页显示每页条目下拉选择
		  , cellMinWidth: 60//定义全局最小单元格宽度，其余自动分配宽度

            });
            //监听行工具事件
            table.on('tool(mylist)', function(obj) { //注：tool 是工具条事件名，mylist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data //获得当前行数据
			, layEvent = obj.event; //获得 lay-event 对应的值
                if (layEvent === 'del') {
                    layer.confirm('真的删除行么', function(index) {
                        obj.del(); //删除对应行（tr）的DOM结构
                        layer.close(index);
                        //向服务端发送删除指令
                    });
                } else if (layEvent === 'edit') {
                    layer.alert('编辑行：<br>' + JSON.stringify(data))
                }
            });
            //监听单元格编辑
            table.on('edit(mylist)', function(obj) {
                var value = obj.value //得到修改后的值
    , data = obj.data //得到所在行所有键值
    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });

            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(mylist)', function(obj) {

                document.getElementById("SheetN").style.display = "block";
                document.getElementById("DivSheet1").style.display = "block";
                document.getElementById("DivSheet2").style.display = "none";

                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID
                $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="CustEn"]').data('edit', true); // 指定修改具体的列

                var ss = obj.checked

                $(".layui-table-body tbody tr[data-index='5']").data('edit', true)

                var data = obj.data;

                layer.alert(JSON.stringify(data), {
                    title: '当前行数据：'
                });

                //标注选中样式
                obj.tr.addClass('layui-table-click').siblings().removeClass('layui-table-click');
                $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
            });


            table.on('checkbox(mylist)', function(obj) {
                var checked = obj.checked;
                var id = obj.data.CustNo;
                var type = obj.type; // all/one
                if (type == "all") {
                    alert("全部选中");
                } else {
                    if (checked) {
                        orgIdArr.push(id);
                    } else {
                        orgIdArr.remove(id);
                    }
                }
                orgIds = orgIdArr.join(",");
                console.log(orgIds);
            });


        }); 



    </script>




</head>
<body>
<div class="div_find">

    <p>
      <label class="find_labela">用户账号：</label> <input type="text" id="LoginNo" class="find_input"/>
      <label class="find_labela">用户名称：</label><input type="text" id="LoginName" class="find_input"/>
      <label class="find_labela">部门：</label><input type="text" id="DeptName" class="find_input"/>
      <input type="button" value="搜索一下" class="find_but" id="UserBut_open"><input type="button" value="重置" class="find_but" id="but_close02">
      <span class="find_span" ><i class="i_open01"></i>展开</span>
      <span class="find_span1" ><i class="i_close01"></i>收起</span> 
    </p>
    <p id="open" style="display:none" >
        <label class="find_labela">性别：</label>
        <select class="find_input" id="UserSex">
              <option>全部</option>
              <option>男</option>
              <option>女</option>
        </select>
        <input type="button" value="搜索一下" class="find_but" id="UserBut_close">
        <input type="button" value="重置" class="find_but" id="but_close01">
      </p>
</div>
<div style="text-align:right">
   <i class="down_i" ></i><a href="JavaScript:void(0)" onclick="openDialog(1)" class="add_a">导出</a>
   <i class="add2_i" ></i><a href="JavaScript:void(0)" onclick="openDialog(1)" style="color: Blue">添加</a>
</div>

<div  class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-table" lay-filter="mylist" lay-data="{ url:'../Service/BaseModuleAjax.ashx?OP=GetCust&sFlag=40', id:'mylist'}">
            <thead>
                <tr>
                    <th lay-data="{type: 'checkbox', fixed: 'left'}">
                    </th>
                    <th lay-data="{field:'CustNo', align:'center',width:120,edit: 'text',sort: true}">
                        客户编码
                    </th>
                    <th lay-data="{field:'CustEn',align:'center', minWidth:170}">
                        客户名称
                    </th>
                    <th lay-data="{field:'CZone',align:'center',minWidth:130}">
                        大区
                    </th>
                    <th lay-data="{field:'CProvince',align:'center',minWidth:260}">
                        办事处
                    </th>
                    <th lay-data="{field:'City',align:'center',width:130}">
                        城市
                    </th>
                    <th lay-data="{field:'InchangeMan',align:'center',width:130}">
                        客负责人
                    </th>
                    <th lay-data="{field:'Phone',align:'center',minWidth:260,style:'background-color: #009688; color: #fff;'}">
                        电话
                    </th>
                    <th lay-data="{field:'Addr',align:'center',width:130}">
                        地址
                    </th>
                    <th lay-data="{field:'CustKind',align:'center',width:130}">
                        类别
                    </th>
                    <th lay-data="{field:'SalesSuper',align:'center',width:130}">
                        跟进人
                    </th>
                    <th lay-data="{field:'option',align:'center',width:130,toolbar:'#barDemo',fixed: 'right'}">
                        操作
                    </th>
                </tr>
            </thead>
        </table>

        <script type="text/html" id="barDemo">
		<a class="layui-btn layui-btn-xs" lay-event="edit">修改</a>
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
        </script>

</div>
<div id="SheetN" style=" height:25px;display:none;">
   <table id="yh_header2" cellspacing="0" cellpadding="0" border='0' style="width:100%;background:white; height:25px;">
     <tr>
          <td id="Sheet1" onclick ="Sheet_onclick(1)"  style="width:80px;background:Gray; text-align:center; font-size:12px; font-weight:bold;">
             第一页 
          </td>
          <td style="width:20px;background:white; text-align:center;">
          </td>
          <td  id="Sheet2" onclick ="Sheet_onclick(2)"  style="width:80px;background:white; text-align:center; font-size:12px; font-weight:bold;">
              第二页
          </td>
          <td style="width:80%;background:white;">
          
          </td>
      </tr>
   </table>
</div>
<div id="DivSheet1" style=" height:120px; background-color:cornflowerblue;display:none;">
    <input type="text" class="form-control" id="txtPath" name="txtPath" placeholder="请输入文件详细路径,如: D:\temp\A.txt" />  
    <input type='button' id="GetTxtInfo_Btn" value='获取TXT信息' style="width: 90px; height: 30px;" onclick="GetTxtInfo()"/>
</div>
<div id="DivSheet2" style=" height:120px;background-color:Blue; display:none;">

</div>



</body>
</html>