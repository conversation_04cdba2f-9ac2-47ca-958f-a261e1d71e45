﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net;
using System.IO;
using Newtonsoft.Json;

namespace WXCommon
{
   public class WXSendMessage
    {

        /// <summary>
        /// 发信息给微信
        /// </summary>
        /// <param name="sentMan">发送人，如果发给部门全部人，用;@all</param>
        /// <param name="sentTxt">发送内容</param>
        /// <param name="toparty">发送对应的部门，如果是给个人发，不需要这个参数</param>
        /// <param name="agentid">发送给对应的应用程序</param>
        /// <param name="GeFlag">是给个人发，还是给整个部门的人发</param>
       public static void SentMessageToWeiXin(string sentMan, string sentTxt, string CorpID, string Secret, string toparty, string agentid, string GeFlag, string Company)
        {
            /*Send Message*/

            TokenHelper itokenStr = new TokenHelper();
            string tokens = itokenStr.GetTokenString("", CorpID, Secret, toparty, Company);

            string iToken = GetJsonValue(tokens, "access_token");

            WXSendMessage iSend = new WXSendMessage();
            CorpSendBase iCorpSendBase = new CorpSendBase();
            iCorpSendBase.touser = sentMan;// "@all";
            if (GeFlag != "G")  // G :标识标识给个人发信息，不需要这个参数
            {
                iCorpSendBase.toparty = toparty;
            }
            iCorpSendBase.totag = "0";
            iCorpSendBase.msgtype = "text";//文本类型  "news":新闻，可以有链接的，还可以有图片  "text": 文本类型 
            iCorpSendBase.text = new text(sentTxt);
            iCorpSendBase.agentid = agentid;
            iCorpSendBase.safe = "0";

            string Data = JsonConvert.SerializeObject(iCorpSendBase);
            //JsonConvert.SerializeObject(new { touser = ToUser, toparty = "1", totag = "0", msgtype = "text", agentid = "0", text = new { content = "发送消息:" + iMessage + "。 当前日间：" + Convert.ToString(DateTime.Now) }, safe = "0" });
            iSend.DoSendText(iToken, Data);
        }


        /// <summary>
        /// 发送新闻信息给相关人员
        /// </summary>
        /// <param name="sentMan">发送人，如果发给部门全部人，用;@all</param>
        /// <param name="sentTxt">发送内容</param>
        /// <param name="toparty">发送对应的部门，如果是给个人发，不需要这个参数</param>
        /// <param name="agentid">发送给对应的应用程序</param>
        /// <param name="sGFlag">是发个个人还是整个部门的人</param>
       public static void SentNewsToWeiXin(string sentMan, string title, string sentTxt, string url, string CorpID, string Secret, string toparty, string agentid, string sGFlag, string Company)
       {
            /*Send Message*/

            TokenHelper itokenStr = new TokenHelper();
            string tokens = itokenStr.GetTokenString("", CorpID, Secret, toparty, Company);

            string iToken = GetJsonValue(tokens, "access_token");

            WXSendMessage iSend = new WXSendMessage();

            CorpSendNews iCorpSendNews = new CorpSendNews();
            iCorpSendNews.touser = sentMan;// "@all";
            if (sGFlag != "G")  // G :标识标识给个人发信息，不需要这个参数
            {
                iCorpSendNews.toparty = toparty;
            }
            iCorpSendNews.totag = "0";
            iCorpSendNews.msgtype = "news";//新闻信息

            // 定义数组 可定义多个数组 “,” 隔开
            articles[] iarticles = new articles[]{
               //new articles(title,"","","PIC_URL"),
               new articles(title,sentTxt,url,"")
            };

            iCorpSendNews.news = new news(iarticles);
            iCorpSendNews.agentid = agentid;

            string Data = JsonConvert.SerializeObject(iCorpSendNews);
            //JsonConvert.SerializeObject(new { touser = ToUser, toparty = "1", totag = "0", msgtype = "text", agentid = "0", text = new { content = "发送消息:" + iMessage + "。 当前日间：" + Convert.ToString(DateTime.Now) }, safe = "0" });
            iSend.DoSendText(iToken, Data);
        }


        #region 获取Json字符串某节点的值
        /// <summary>
        /// 获取Json字符串某节点的值
        /// </summary>
        public static string GetJsonValue(string jsonStr, string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(jsonStr))
            {
                key = "\"" + key.Trim('"') + "\"";
                int index = jsonStr.IndexOf(key) + key.Length + 1;
                if (index > key.Length + 1)
                {
                    //先截逗号，若是最后一个，截“｝”号，取最小值
                    int end = jsonStr.IndexOf(',', index);
                    if (end == -1)
                    {
                        end = jsonStr.IndexOf('}', index);
                    }

                    result = jsonStr.Substring(index, end - index);
                    result = result.Trim(new char[] { '"', ' ', '\'' }); //过滤引号或空格
                }
            }
            return result;
        }
        #endregion


        public void DoSendText(string Access_Token, string Content)
        {
            string URL = string.Format("https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={0}", Access_Token);
            PostWebRequest(URL, Content, Encoding.UTF8);
        }



        /// <summary>
        /// Post数据接口
        /// </summary>
        /// <param name="postUrl">接口地址</param>
        /// <param name="paramData">提交json数据</param>
        /// <param name="dataEncode">编码方式Encoding.UTF8</param>
        /// <returns></returns>
        public string PostWebRequest(string postUrl, string paramData, Encoding dataEncode)
        {
            string ret = string.Empty;
            try
            {
                byte[] byteArray = dataEncode.GetBytes(paramData); //转化
                HttpWebRequest webReq = (HttpWebRequest)WebRequest.Create(new Uri(postUrl));
                webReq.ContentType = "text/html";
                webReq.Headers.Add("charset", "utf-8");
                webReq.Method = "POST";
                //webReq.ContentType = "application/x-www-form-urlencoded";
                webReq.ContentLength = byteArray.Length;
                Stream newStream = webReq.GetRequestStream();
                newStream.Write(byteArray, 0, byteArray.Length);//写入参数
                newStream.Close();
                HttpWebResponse response = (HttpWebResponse)webReq.GetResponse();
                StreamReader sr = new StreamReader(response.GetResponseStream(), Encoding.Default);
                ret = sr.ReadToEnd();
                sr.Close();
                response.Close();
                newStream.Close();
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
            return ret;
        }


    }
}
