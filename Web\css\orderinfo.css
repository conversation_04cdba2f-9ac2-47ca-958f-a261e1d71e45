﻿body {
}
.layui-table-view .layui-table {
width:100%;
}
.divOrderDetailBody {
    height:auto;
}
.lightHead {
    background-color: #26d0a1;
    color:white;
}
.black_overlay {
    display: none;
    position: absolute;
    top: 0%;
    left: 0%;
    width: 100%;
    min-height:100%;
    height: auto;
    background-color: #bbbcc7;
    z-index: 1001;
    -moz-opacity: 0.8;
    opacity: .80;
    filter: alpha(opacity=60);
}
#divOrderDetail_SN {
    position: absolute;
    top: 1%;
    left: 1%;
    right: 1%;
    width: 98%;
    height: auto;
    background-color: white;
    /*overflow: auto;
    overflow-y: hidden;*/
    margin: 0px;
    /*padding: 8px 5px 5px 5px;*/
    display: none;
    z-index: 1002;
    border: solid 2px #26d0a1;
}
#divOrderDetail {
    position: absolute;
    top: 1%;
    left: 1%;
    right: 1%;
    width: 98%;
    height: auto;
    background-color: white;
    /*overflow: auto;
    overflow-y: hidden;*/
    margin: 0px;
    /*padding: 8px 5px 5px 5px;*/
    display: none;
    z-index: 1002;
    border: solid 2px #26d0a1;
}

#divOrderDetail .layui-table td, .layui-table th {
    position: relative;
}
#divOrderDetail_SN .layui-table td, .layui-table th {
    position: relative;
}


.divOrderBody {
    /*height: 93%;
    width: auto;*/
    
        overflow: auto;
        height: 93%;
        width: 100%;
}
.divOrderDetailHead {
    padding: 5px;
}
.divOrderDetailHead table {
    /*width: 100%;*/
    line-height: 30px;
}
.divOrderDetailHead table tr td {
    width: 25%;
}
.tdOrderButton input {
    width: 80px;
    background-color: #56dcaf;
    color: white;
    border: 0px;
    border-radius: 5px;
}

.divOrderDetailBody_Detail {
    padding:5px;
    height:100%;
}
    .divOrderDetailBody_Detail div {
         /*width: 100%;
       border: solid 1px #ccc;
        float: left*/
    }

.divOrderDetailBody_OrderHead {
    padding:5px;
    height: 35px;
    background-color: #f1f4f4;
    border: solid 1px #ccc;
    line-height: 25px;
    margin-top:5px;
}
.trOrderHead_Detail td {
    height: 35px;
    background-color: #f1f4f4;
    line-height: 25px;
    width: 25%;
}



.trOrderHead_Detail1 td {
    height: 35px;
    background-color: #f1f4f4;
    line-height: 25px;
    border-bottom-width:0px;
}
.divOrderDetailBody_Detail div {
    /*border:0px;
padding:5px;*/
}
.divOrderDetailBody_Detail div table {
    width: 100%;
}
.divOrderDetailBody_Detail div table tr td {
    border: solid 1px #ccc;
    line-height:25px;
    height:35px;
}
.divallDetail {
    border: solid 1px #ccc;
    border-top-width:0px;
    padding:5px;
}

.divOrderDetailBody_OrderHeadDown {
    padding: 5px;
    height: 35px;
    line-height: 25px;
    margin-top: 5px;
    border:0px;
    border-bottom:solid 1px #ccc;
}
.divOrderDetailBody_OrderHeadRight {
float:right;
}
.divOrderDetailBody_OrderHeadRight input {
    width: 65px;
    background-color: #56dcaf;
    color: white;
    border: 0px;
    border-radius: 5px;
}
    .layui-table-box {
        padding: 0px;
    }