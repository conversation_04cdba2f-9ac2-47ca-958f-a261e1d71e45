﻿using Newtonsoft.Json;
using System;
using System.Collections;
using System.Web;

namespace GridReport.HTML5.Check
{
    /// <summary>
    /// $codebehindclassname$ 的摘要说明
    /// </summary>
    public class Check : IHttpHandler
    {

        public void ProcessRequest(HttpContext context)
        {

            var Table = new
            {
                BankName = "中国银行33",
                PayTo = "锐浪软件技术有限公司33",
                Year = "2021",
                Month = "11",
                Day = "08",
                Amount = "********",
                Usage = "Grid++Report专业版",
                Remark = "Grid++Report 个人用户版",
                Date = "2012-11-11",
                Other1 = "01",
                Other2 = "02",
                Other3 = "03",
                Other4 = "04",
                Other5 = "05",
                DI = "*************",
            };
            var list = new ArrayList();
            list.Add(Table);
            //这里查询出来的数据将传递给报表中与字段同名的参数或部件框
            context.Response.Write(JsonConvert.SerializeObject(new
            {
                Table = list
            }));
            context.Response.End();
        }

        public bool IsReusable
        {
            get
            {
                return true; //false;
            }
        }
    }
}
