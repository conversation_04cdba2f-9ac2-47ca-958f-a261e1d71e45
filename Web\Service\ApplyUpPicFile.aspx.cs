﻿using System;
using System.Collections;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml.Linq;
using Newtonsoft.Json;
using System.Web.SessionState;
using System.Web.Script.Serialization;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Text;
using BLL;
using Common;
using System.Runtime.Remoting.Contexts;

namespace Web.Service
{
    public partial class ApplyUpPicFile : System.Web.UI.Page, IHttpHandler
    {
        string sCFlag = string.Empty;
        protected void Page_Load(object sender, EventArgs e)
        {
            string sMsg = string.Empty;
            string NesPath = string.Empty;
            string sXuPath = string.Empty;
            string Path1 = string.Empty;
            string Path = string.Empty;
            string sNNo = HttpContext.Current.Request.Params["NNo"];
            string sCFlag = HttpContext.Current.Request.Params["sFlag"]; // 1 店招申请上传图片；2：制作商上传图片
            string sMTPic = HttpContext.Current.Request.Params["MTPic"]; // 有MD5值的门头照路径，判定是否上传重复MD5
            string sCLPic = HttpContext.Current.Request.Params["CLPic"]; // 有MD5值的陈列照路径，判定是否上传重复MD5
            string sCompanyNo = string.Empty;
            string sDate = DateTime.Now.ToString("yyyyMMddHHmmss");
            string sDateTime = DateTime.Now.ToString("yyyyMMdd");
            string sNewName = string.Empty;
            //float MMcha = float.Parse(System.Configuration.ConfigurationManager.AppSettings["PicDateMM"]); // 控制上传图片不能超过多少分钟
            string sMD5 = string.Empty;
            string sMD5T = string.Empty;
            string sBMD5 = string.Empty;  // 后台查询返回的。
            string sKZM = string.Empty;  // 文件扩展名
            string IP = HttpContext.Current.Request.ServerVariables.Get("Remote_Addr").ToString().Trim();  //获取IP 地址
            string sInMan = string.Empty;
            string sModuleName=string.Empty;


            //接收请求的文件
            HttpPostedFile file1;

            if (sCFlag == "1") // 店招申请的图片
            {
                file1 = Request.Files["UpImg1"];

                sCompanyNo = HttpContext.Current.Session["CompanyNo"].ToString();
                NesPath = sDate + file1.FileName;//新图片名称
                sXuPath = "/UpPicFile/" + sCompanyNo + "/ShopSignsApply/" + sDateTime + "/";
                Path = sXuPath + NesPath;
                Path1 = sXuPath + sDate + System.IO.Path.GetExtension(file1.FileName);
            }
            else if (sCFlag == "15")  //询价（需求询价）--上传图纸  ,,用下面这种方式，解决中文乱码问题。
            {
                file1 = Request.Files["UpFile"];

                
                sCompanyNo = HttpContext.Current.Session["CompanyNo"].ToString();
                string sfilePath = HttpContext.Current.Request.Params["sUpFile"];
                string[] str = sfilePath.Split('\\');
                NesPath = sNNo + " " + str[str.Length - 1]; //file1.FileName;//新图片名称
                sXuPath = "/UpPicFile/" + sCompanyNo + "/PurchProject/" + sDateTime + "/";
                Path = sXuPath + NesPath;
                //sKZM = System.IO.Path.GetExtension(file1.FileName); // 文件扩展名
                // Path1 = sXuPath + sDate + sKZM; //原路径 

                string path2 = Request.MapPath(Path);//服务器路径
                if (!Directory.Exists(Request.MapPath(sXuPath)))
                {
                    Directory.CreateDirectory(Request.MapPath(sXuPath));
                }
                file1.SaveAs(path2);


                Response.Clear();
                Response.Write(JsonConvert.SerializeObject(new { Path = Path, FileName = NesPath }));
                Response.End();   // 如果是上传图片，则走到下面，压缩一下
            }
            else if (sCFlag == "16")  //图纸转换--上传解读后的图纸  
            {
                sMsg = "Success";
                file1 = Request.Files["UpFile"];

                sCompanyNo = HttpContext.Current.Session["CompanyNo"].ToString();
                string sfilePath = HttpContext.Current.Request.Params["sUpFile"];
                string[] str = sfilePath.Split('\\');
                NesPath = "R-"+sNNo+" " + str[str.Length - 1]; //file1.FileName;//新图片名称
                sXuPath = "/UpPicFile/" + sCompanyNo + "/PurchProject/" + sDateTime + "/";
                Path = sXuPath + NesPath;
                //sKZM = System.IO.Path.GetExtension(file1.FileName); // 文件扩展名
                // Path1 = sXuPath + sDate + sKZM; //原路径 

                string path2 = Request.MapPath(Path);//服务器路径
                if (!Directory.Exists(Request.MapPath(sXuPath)))
                {
                    Directory.CreateDirectory(Request.MapPath(sXuPath));
                }

                try
                {
                    file1.SaveAs(path2);
                }
                catch
                {
                    sMsg = "Error";
                }
                


                Response.Clear();
                Response.Write(JsonConvert.SerializeObject(new { Path = Path, FileName = NesPath, Msg = sMsg }));
                Response.End();   // 如果是上传图片，则走到下面，压缩一下
            }
            else if (sCFlag == "17")  //出库记录：上传初级检验报告
            {
                sMsg = "Success";
                file1 = Request.Files["UpFile"];

                sCompanyNo = HttpContext.Current.Session["CompanyNo"].ToString();
                string sfilePath = HttpContext.Current.Request.Params["sUpFile"];
                string[] str = sfilePath.Split('\\');
                NesPath = sNNo + " " + str[str.Length - 1]; //file1.FileName;//新图片名称
                sXuPath = "/UpPicFile/" + sCompanyNo + "/POutDoc/" + sDateTime + "/";
                Path = sXuPath + NesPath;
                //sKZM = System.IO.Path.GetExtension(file1.FileName); // 文件扩展名
                // Path1 = sXuPath + sDate + sKZM; //原路径 

                string path2 = Request.MapPath(Path);//服务器路径
                if (!Directory.Exists(Request.MapPath(sXuPath)))
                {
                    Directory.CreateDirectory(Request.MapPath(sXuPath));
                }

                try
                {
                    file1.SaveAs(path2);
                }
                catch
                {
                    sMsg = "Error";
                }



                Response.Clear();
                Response.Write(JsonConvert.SerializeObject(new { Path = Path, FileName = NesPath, Msg = sMsg }));
                Response.End();   // 如果是上传图片，则走到下面，压缩一下
            }
            else if (sCFlag == "18")  //上传标贴模板
            {
                sMsg = "Success";
                file1 = Request.Files["UpFile"];

                sCompanyNo = HttpContext.Current.Session["CompanyNo"].ToString();
                string sfilePath = HttpContext.Current.Request.Params["sUpFile"];
                string[] str = sfilePath.Split('\\');
                NesPath = sNNo + str[str.Length - 1]; //file1.FileName;//新图片名称
                sXuPath = "/Template/" + sCompanyNo + "/LabelPrint/";
                Path = sXuPath + NesPath;
                //sKZM = System.IO.Path.GetExtension(file1.FileName); // 文件扩展名
                // Path1 = sXuPath + sDate + sKZM; //原路径 

                string path2 = Request.MapPath(Path);//服务器路径
                if (!Directory.Exists(Request.MapPath(sXuPath)))
                {
                    Directory.CreateDirectory(Request.MapPath(sXuPath));
                }

                try
                {
                    file1.SaveAs(path2);
                }
                catch
                {
                    sMsg = "Error";
                }



                Response.Clear();
                Response.Write(JsonConvert.SerializeObject(new { Path = Path, FileName = NesPath, Msg = sMsg }));
                Response.End();   // 如果是上传图片，则走到下面，压缩一下
            }
            else if (sCFlag == "19")  //用户管理-上传签名图片
            {
                sMsg = "Success";
                file1 = Request.Files["UpFile"];

                sCompanyNo = HttpContext.Current.Session["CompanyNo"].ToString();
                string sfilePath = HttpContext.Current.Request.Params["sUpFile"];
                string[] str = sfilePath.Split('\\');
                NesPath = sNNo + " " + str[str.Length - 1]; //file1.FileName;//新图片名称
                sXuPath = "/UpPicFile/" + sCompanyNo + "/UserSign/" + sDateTime + "/";
                Path = sXuPath + NesPath;
                //sKZM = System.IO.Path.GetExtension(file1.FileName); // 文件扩展名
                // Path1 = sXuPath + sDate + sKZM; //原路径 

                string path2 = Request.MapPath(Path);//服务器路径
                if (!Directory.Exists(Request.MapPath(sXuPath)))
                {
                    Directory.CreateDirectory(Request.MapPath(sXuPath));
                }

                try
                {
                    file1.SaveAs(path2);
                }
                catch
                {
                    sMsg = "Error";
                }

                Response.Clear();
                Response.Write(JsonConvert.SerializeObject(new { Path = Path, FileName = NesPath, Msg = sMsg }));
                Response.End();    
            }
            else if (sCFlag == "20")  //产品工艺流程，上传工艺文件
            {
                sMsg = "Success";
                file1 = Request.Files["UpFile"];

                sInMan = HttpContext.Current.Session["LoginName"].ToString();
                sCompanyNo = HttpContext.Current.Session["CompanyNo"].ToString();
                sModuleName = HttpContext.Current.Request.Params["ModuleName"];
                string sfilePath = HttpContext.Current.Request.Params["sUpFile"];
                string[] str = sfilePath.Split('\\');
                NesPath = sNNo + str[str.Length - 1]; //file1.FileName;//新图片名称
                sXuPath = "/Template/" + sCompanyNo + "/ProductFile/";
                Path = sXuPath + NesPath;
                //sKZM = System.IO.Path.GetExtension(file1.FileName); // 文件扩展名
                // Path1 = sXuPath + sDate + sKZM; //原路径 

                string path2 = Request.MapPath(Path);//服务器路径
                if (!Directory.Exists(Request.MapPath(sXuPath))){
                    Directory.CreateDirectory(Request.MapPath(sXuPath));
                }

                try{
                    file1.SaveAs(path2);
                    LogHelper.LogInfo("导入", "产品工艺路线上传工艺文件，文件名称："+ NesPath, IP, sCompanyNo, sInMan, sModuleName);
                }
                catch(Exception ex) {
                    LogHelper.LogError("导入", "产品工艺路线上传工艺文件，文件名称：" + NesPath + "法生错误，错误原因：" + ex.Message.ToString(), IP, sCompanyNo, sInMan, sModuleName);
                    sMsg = "Error";
                }

                Response.Clear();
                Response.Write(JsonConvert.SerializeObject(new { Path = Path, FileName = NesPath, Msg = sMsg }));
                Response.End();  
            }
            else if (sCFlag == "20-1")  //工艺文件库，上传工艺文件
            {
                sMsg = "Success";
                file1 = Request.Files["UpFile"];

                sCompanyNo = HttpContext.Current.Session["CompanyNo"].ToString();
                string sfilePath = HttpContext.Current.Request.Params["sUpFile"];
                string[] str = sfilePath.Split('\\');
                NesPath = sNNo + str[str.Length - 1]; //file1.FileName;//新图片名称
                sXuPath = "/Template/" + sCompanyNo + "/ProcessFile/";
                Path = sXuPath + NesPath;
                //sKZM = System.IO.Path.GetExtension(file1.FileName); // 文件扩展名
                // Path1 = sXuPath + sDate + sKZM; //原路径 

                string path2 = Request.MapPath(Path);//服务器路径
                if (!Directory.Exists(Request.MapPath(sXuPath)))
                {
                    Directory.CreateDirectory(Request.MapPath(sXuPath));
                }

                try
                {
                    file1.SaveAs(path2);
                }
                catch
                {
                    sMsg = "Error";
                }

                Response.Clear();
                Response.Write(JsonConvert.SerializeObject(new { Path = Path, FileName = NesPath, Msg = sMsg }));
                Response.End();
            }
            else if (sCFlag == "21")  //工单工艺流程，上传工艺文件
            {
                sMsg = "Success";
                file1 = Request.Files["UpFile"];

                sCompanyNo = HttpContext.Current.Session["CompanyNo"].ToString();
                string sfilePath = HttpContext.Current.Request.Params["sUpFile"];
                string[] str = sfilePath.Split('\\');
                NesPath = sNNo + str[str.Length - 1]; //file1.FileName;//新图片名称
                sXuPath = "/Template/" + sCompanyNo + "/OrderFile/";
                Path = sXuPath + NesPath;
                //sKZM = System.IO.Path.GetExtension(file1.FileName); // 文件扩展名
                // Path1 = sXuPath + sDate + sKZM; //原路径 

                string path2 = Request.MapPath(Path);//服务器路径
                if (!Directory.Exists(Request.MapPath(sXuPath)))
                {
                    Directory.CreateDirectory(Request.MapPath(sXuPath));
                }

                try
                {
                    file1.SaveAs(path2);
                }
                catch
                {
                    sMsg = "Error";
                }

                Response.Clear();
                Response.Write(JsonConvert.SerializeObject(new { Path = Path, FileName = NesPath, Msg = sMsg }));
                Response.End();
            }
            else if (sCFlag == "22")
            {
                sMsg = "Success";
                file1 = Request.Files["file"];

                string fileName = file1.FileName;

                sXuPath = "/Echart/image/";

                string serverPath = Request.MapPath(sXuPath);  //服务器路径

                if (!Directory.Exists(serverPath))
                {
                    Directory.CreateDirectory(serverPath);
                }

                string path = serverPath + fileName;

                try
                {
                    file1.SaveAs(path);
                }
                catch
                {
                    sMsg = "Error";
                }

                Response.Clear();
                Response.Write(JsonConvert.SerializeObject(new { Path = sXuPath, FileName = fileName, Msg = sMsg }));
                Response.End();
            }
            else if (sCFlag == "300")  //流程申请上传视频
            {
                file1 = Request.Files["fileToUpload"];

                sCompanyNo = HttpContext.Current.Session["CompanyNo"].ToString();
                NesPath = file1.FileName;//新图片名称
                sXuPath = "/UpPicFile/" + sCompanyNo + "/Video/FlowApply/" + sDateTime + "/";
                Path = sXuPath + NesPath;
                // Path1 = sXuPath + sDate + System.IO.Path.GetExtension(file1.FileName); //原路径
                Path1 = sXuPath + file1.FileName; //原路径

                string path2 = Request.MapPath(Path1);//服务器路径
                if (!Directory.Exists(Request.MapPath(sXuPath)))
                {
                    Directory.CreateDirectory(Request.MapPath(sXuPath));
                }
                file1.SaveAs(path2);

                Response.Clear();
                Response.Write(JsonConvert.SerializeObject(new { Path = Path }));
                Response.End();
            }
            else if (sCFlag == "400")  //删除文件
            {
                sMsg = "Success";
                file1 = Request.Files["fileToUpload"];

                string sDelPath = HttpContext.Current.Request.Params["DelPath"];
                string path2 = Request.MapPath(sDelPath);//服务器路径
                try
                {
                    File.Delete(path2);
                    sMsg = "Success";
                }
                catch
                {
                    sMsg = "Error";
                }

                Response.Clear();
                Response.Write(JsonConvert.SerializeObject(new { FileName = sDelPath, Msg = sMsg }));
                Response.End();   // 如果是上传图片，则走到下面，压缩一下
            }
            else
            {
                file1 = Request.Files["UpImg5"];
            }



            if (Path.Length > 22)
            {
                string path2 = Request.MapPath(Path1);//服务器路径
                if (!Directory.Exists(Request.MapPath(sXuPath)))
                {
                    Directory.CreateDirectory(Request.MapPath(sXuPath));
                }
                file1.SaveAs(path2);  //保存图片
                SaveSmallPhoto(path2, 3, Request.MapPath(Path));  // 第二个参数是压缩比例 20160312 修改为下面的方法：压缩图片在上传

                try
                {
                    FileStream file = new FileStream(path2, FileMode.Open);
                    System.Security.Cryptography.MD5 md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
                    byte[] retVal = md5.ComputeHash(file);
                    file.Close();

                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < retVal.Length; i++)
                    {
                        sb.Append(retVal[i].ToString("x2"));
                    }

                    sMD5 = sb.ToString();
                }
                catch (Exception ex)
                {
                    throw new Exception("GetMD5HashFromFile() fail,error:" + ex.Message);
                }

                File.Delete(path2);  // 删除原图  


                Response.Clear();
                Response.Write(JsonConvert.SerializeObject(new { Path = Path, MD5 = sMD5, MD5T = sMD5T, Mgs = sMsg }));
                Response.End();

            }
        }

        /// <summary>
        /// 将图片按比例缩小保存
        /// </summary>
        /// <param name="fromPath">原图片路径名</param>
        /// <param name="scale">缩小比例</param>
        /// <param name="toPath">缩小后保存的路径名</param>
        public void SaveSmallPhoto(string fromPath, int scale, string toPath)
        {
            int width, height;
            using (Image img = Image.FromFile(fromPath))
            {
                width = img.Width / scale;
                height = img.Height / scale;

                using (Image imgNew = new Bitmap(width, height))
                {
                    using (Graphics g = Graphics.FromImage(imgNew))
                    {
                        g.DrawImage(img, new Rectangle(0, 0, width, height),
                            new Rectangle(0, 0, img.Width, img.Height), System.Drawing.GraphicsUnit.Pixel);
                    }

                    imgNew.Save(toPath, ImageFormat.Jpeg);
                }
            }
        }


    }
}
