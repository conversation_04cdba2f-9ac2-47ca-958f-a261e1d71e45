﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>不良现象</title>
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <script src="../js/ajaxfileupload.js" type="text/javascript"></script>
    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all">
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/PrdAssist.js" type="text/javascript"></script>

    <link href="../css/XC.css" rel="stylesheet" />



    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        //$('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=28-1' });
                    }
                }
            })
        });



    </script>


    <script type="text/javascript">

        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#table_DeviceInfoList',
                id: 'DeviceInfoListID',
                url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=15',
                height: 'full-80',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'DeviceNo', title: '设备编号', width: 120, sort: true },
                    { field: 'DeviceName', title: '设备名称', width: 200, sort: true },
                    { field: 'DeviceDesc', title: '使用场景', width: 100 },
                    { field: 'DeviceSpec', title: '设备规格', width: 100 },
                    { field: 'DeviceKind', title: '设备类型', width: 100 },
                    { field: 'DeptNo', title: '管理部门编号', width: 100 },
                    { field: 'DeptName', title: '管理部门', width: 100 },
                    { field: 'Status', title: '设备状态', width: 80 },
                    { field: 'MaterNo', title: '设备分类编号', width: 100, sort: true },
                    { field: 'InventoryCycle', title: '有效期（年）', width: 80 },
                    { field: 'UseDate', title: '校准有效期', width: 80 },
                    { field: 'CheckReq', title: '点检要求', width: 100 },
                    { field: 'MaintainReq', title: '保养要求', width: 100 },
                    { field: 'Remark', title: '备注', width: 200 },
                    { field: 'InMan', title: '创建人', width: 90 },
                    { field: 'InDate2', title: '创建时间', width: 140, sort: true },
                    { field: 'op', title: '操作', width: 130, toolbar: '#barDemo_DeviceInfo', fixed: 'right' }
                ]],
                page: true,

            });

            //监听是否选中操作
            table.on('checkbox(table_DeviceInfoList)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);


            });


            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(table_DeviceInfoList)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

            });

            //监听单元格编辑
            table.on('edit(table_DeviceInfoList)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });


            //监听行工具事件
            table.on('tool(table_DeviceInfoList)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值
                if (layEvent === 'del') {

                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("您确定要删除该设备信息吗？ 设备编号：")

                    //设置删除的对象
                    $("#hint-value").html(data.DeviceNo)

                    $("#txtDelDeviceNo").val(data.DeviceNo)

                } else if (layEvent === 'edit') {
                    $('#head-title1').val("修改设备信息");
                    $('#txtAEFlag').val("20");


                    $("#txtDeviceNo").val(data.DeviceNo);
                    $("#txtDeviceName").val(data.DeviceName);
                    $("#txtDeviceDesc").val(data.DeviceDesc);
                    $("#txtDeviceSpec").val(data.DeviceSpec);
                    $("#txtDeviceKind").val(data.DeviceKind);

                    $("#txtDeptNo").val(data.DeptNo);
                    $("#txtDeptName").val(data.DeptName);
                    $("#txtStatus").val(data.Status);
                    $("#txtMaterNo").val(data.MaterNo);
                    $("#txtInventoryCycle").val(data.InventoryCycle);

                    $("#txtDCUseDate").val(data.UseDate == "" ? "2999-12-30" : data.UseDate);


                    $("#txtCheckReq").val(data.CheckReq);
                    $("#txtMaintainReq").val(data.MaintainReq);
                    $("#txtRemark").val(data.Remark);

                    $("#txtDeviceNo").attr({ "disabled": "disabled" });

                    $("#div_warning").html("");
                    $("#div_warning").hide();

                    $('#ShowOne').css("display", "block")
                    $('#ShowOne-fade').css("display", "block")
                    $("#div_DeviceUsePDFList").show()
                    DeviceUseInfo()
                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }

            });


            //  查询 --
            $('#btn_DeviceNo_Open').click(function () {

                var sDeviceNo = $("#txtsDeviceNo").val();  //
                var sName = encodeURI($("#txtSDeviceName").val());  //
                var sMaterNo = encodeURI($("#txtSMMaterNo").val());  //
                var sStartTime = $("#txtStartTime").val()
                var sEndTime = $("#txtEndTime").val()

                var Data = '';
                var Params = { No: sDeviceNo, Name: sName, Item: "", Status: "", BDate: sStartTime, EDate: sEndTime, A: sMaterNo, B: "", C: "", D: "", E: "" };
                var Data = JSON.stringify(Params);

                table.reload('DeviceInfoListID', {
                    method: 'post',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=15&Data=' + Data,
                    where: {
                        'No': sDeviceNo,
                        'name': sName
                    }, page: {
                        curr: 1
                    }
                });
            });

            $("#btn_DeviceInfoDel").click(function () {

                //向服务端发送禁用指令

                var sDeviceNo = $("#txtDelDeviceNo").val();

                var sFlag = "21";

                var Data = '';
                var Params = { No: sDeviceNo, Name: "", Item: "", A: "", B: "", C: "", D: "", E: "", F: "", G: "", H: "", I: "", J: "", K: "", L: "", Kind: "", Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);

                $.ajax({
                    type: "POST",
                    url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('删除成功！');

                            $('#btn_DeviceNo_Open').click();  // 重新查询

                            $('#ShowDel').css("display", "none")
                            $('#ShowDel-fade').css("display", "none")

                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                            layer.msg('工序已用于工艺流程，不能删除！');

                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST1') {
                            layer.msg('工序已用于BOM设计，不能删除！');

                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST2') {
                            layer.msg('工序已用于产品对应标贴，不能删除！');

                        }
                        else {
                            layer.msg('删除失败，请重试！');
                            $('#btn_DeviceNo_Open').click();  // 重新查询
                        }
                    },
                    error: function (data) {
                        layer.msg('删除失败2，请重试！');
                        $('#btn_DeviceNo_Open').click();  // 重新查询
                    }
                });
            })



        });

        function openDialog(n) {  // 新增弹窗
            if (n == 1)//打开新增弹窗
            {
                $('#head-title1').val("新增设备信息");
                $('#txtAEFlag').val("19");


                $("#txtDeviceNo").val("");
                $("#txtDeviceName").val("");
                $("#txtDeviceDesc").val("");
                $("#txtDeviceSpec").val("");
                $("#txtDeviceKind").val("");

                $("#txtDeptNo").val("");
                $("#txtDeptName").val("");
                $("#txtStatus").val("");
                $("#txtMaterNo").val("");
                $("#txtInventoryCycle").val("");

                $("#txtDCUseDate").val("2999-12-30");
                $("#txtCheckReq").val("");
                $("#txtMaintainReq").val("");
                $("#txtRemark").val("");

                $("#div_warning").html("");
                $("#div_warning").hide();
                $("#txtDeviceNo").removeAttr("disabled");

                $('#ShowOne').css("display", "block")
                $('#ShowOne-fade').css("display", "block")
                $("#div_DeviceUsePDFList").hide()
            }
            else if (n == 2)//打开部门选择弹窗
            {
                layui.use('table', function () {
                    var table = layui.table,
                        form = layui.form;
                    table.render({
                        elem: '#table_SelectDeptInfo',
                        id: 'SelectDeptInfoID',
                        url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=12',//初始化界面查询对应的存过程标记信息，获取部门信息
                        height: 'full-200',
                        cellMinWidth: 80,
                        //toolbar: 'default',//工具栏
                        //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                        count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                        limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                        limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                        cols: [[
                            { type: 'numbers' },
                            { field: 'DeptNo', title: '部门编号', width: 120 },
                            { field: 'DeptName', title: '部门名称', width: 200 },
                            { field: 'DeptAddr', title: '部门地址', width: 200 },
                            { field: 'Remark', title: '备注' },
                            { field: 'op', title: '操作', width: 120, toolbar: '#barDemo_SelectDeptInfo', fixed: 'right' }
                        ]],
                        page: true,


                    });

                    //监听行工具事件
                    table.on('tool(table_SelectDeptInfo)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                        var data = obj.data, //获得当前行数据
                            layEvent = obj.event; //获得 lay-event 对应的值
                        if (layEvent === 'slc') {

                            $("#txtDeptNo").val(data.DeptNo);
                            $("#txtDeptName").val(data.DeptName);

                            /* $("#txtWNo").attr({ "disabled": "disabled" });*/

                            $("#div_warning").html("");
                            $("#div_warning").hide();

                            closeDialog(2);
                        }

                    });


                    //  查询 --
                    $('#btn_DeptInfo_open').click(function () {

                        var sWNo = $("#txtSWNo").val();  //
                        var sName = encodeURI($("#txtSWDesc").val());  //

                        var Data = '';
                        var Params = { No: sWNo, Name: sName, Item: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "", E: "" };
                        var Data = JSON.stringify(Params);

                        table.reload('SelectDeptInfoID', {
                            method: 'post',
                            url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=12&Data=' + Data,
                            where: {
                                'No': sWNo,
                                'name': sName
                            }, page: {
                                curr: 1
                            }
                        });
                    });


                });   // table 方法体

                //监听工具条
                $('#ShowTow').css("display", "block")
            }
            else//弹窗显示设备分类信息
            {

                layui.use('table', function () {
                    var table = layui.table,
                        form = layui.form;
                    table.render({
                        elem: '#DeviceClassHeadlist',
                        id: 'DeviceClassHeadlistID',
                        url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=16',
                        height: 'full-200',
                        cellMinWidth: 80,
                        //toolbar: 'default',//工具栏
                        //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                        count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                        limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                        limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                        cols: [[
                            { type: 'numbers' },
                            { field: 'MaterNo', title: '设备分类编号', width: 200 },
                            { field: 'MaterName', title: '设备分类名称', width: 100 },
                            { field: 'DeviceKind', title: '设备类型', width: 100 },
                            { field: 'CheckReq', title: '点检要求', width: 100 },
                            { field: 'MaintainReq', title: '维护要求', width: 100 },
                            { field: 'DeptNo', title: '管理部门编号', width: 200 },
                            { field: 'DeptName', title: '管理部门名称', width: 200 },
                            { field: 'Remark', title: '备注', width: 300 },
                            { field: 'InMan', title: '创建人', width: 90 },
                            { field: 'InDate', title: '创建时间', width: 200 },
                            { field: 'op', title: '操作', width: 240, toolbar: '#barDemo_SelectDeviceClass', fixed: 'right' }
                        ]],
                        page: true,

                    });

                    //监听行工具事件
                    table.on('tool(DeviceClassHeadlist)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                        var data = obj.data, //获得当前行数据
                            layEvent = obj.event; //获得 lay-event 对应的值
                        if (layEvent === 'detailC')//显示明细页面
                        {
                            $('#head-title4').html("设备分类详情（点检）");
                            //$('#txtAEFlag').val("11");
                            $("#txtAddKind").val("Check");
                            var sKind = $("#txtAddKind").val();//获取点检清单

                            //var sKind = "Maintain";//获取保养清单
                            $("#txtDMaterNo").val(data.MaterNo);
                            $("#txtDMaterName").val(data.MaterName);
                            $("#txtDDeviceKind").val(data.DeviceKind);
                            $("#txtDCheckReq").val(data.CheckReq);
                            $("#txtDMaintainReq").val(data.MaintainReq);

                            $("#txtDDeptNo").val(data.DeptNo);
                            $("#txtDDeptName").val(data.DeptName);
                            $("#txtDRemark").val(data.Remark);


                            $("#txtDMaterNo").attr({ "disabled": "disabled" });

                            $("#div_warningDetail").html("");
                            $("#div_warningDetail").hide();
                            //弹窗显示设备分类明细页
                            ShowDeviceClassDetail(data.MaterNo, sKind);

                            $('#ShowFour').css("display", "block")


                            // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                        }
                        else if (layEvent === 'detailM')//显示明细页面
                        {
                            $('#head-title4').html("设备分类详情（维护）");
                            //$('#txtAEFlag').val("11");
                            $("#txtAddKind").val("Maintain");
                            var sKind = $("#txtAddKind").val();//获取点检清单

                            //var sKind = "Maintain";//获取保养清单
                            $("#txtDMaterNo").val(data.MaterNo);
                            $("#txtDMaterName").val(data.MaterName);
                            $("#txtDDeviceKind").val(data.DeviceKind);
                            $("#txtDCheckReq").val(data.CheckReq);
                            $("#txtDMaintainReq").val(data.MaintainReq);

                            $("#txtDDeptNo").val(data.DeptNo);
                            $("#txtDDeptName").val(data.DeptName);
                            $("#txtDRemark").val(data.Remark);


                            $("#txtDMaterNo").attr({ "disabled": "disabled" });

                            $("#div_warningDetail").html("");
                            $("#div_warningDetail").hide();
                            //弹窗显示设备分类明细明细页
                            ShowDeviceClassDetail(data.MaterNo, sKind);

                            $('#ShowFour').css("display", "block")

                            // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                        }
                        else if (layEvent === 'selectDC')//显示明细页面
                        {
                            $("#txtMaterNo").val(data.MaterNo);
                            $("#txtDeviceKind").val(data.DeviceKind);
                            $("#txtCheckReq").val(data.CheckReq);
                            $("#txtMaintainReq").val(data.MaintainReq);

                            closeDialog(3);

                        }

                    });

                    //  查询表头 --
                    $('#DeviceClassBut_open').click(function () {

                        var sMaterNo = $("#txtSMaterNo").val();  //
                        var sCPVer = encodeURI($("#txtSMaterName").val());  //

                        var Data = '';
                        var Params = { No: sMaterNo, CPVer: sCPVer, Item: "", Status: "", BDate: "", EDate: "", A: sCPVer, B: "", C: "", D: "", E: "" };
                        var Data = JSON.stringify(Params);

                        table.reload('DeviceClassHeadlistID', {
                            method: 'post',
                            url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=16&Data=' + Data,
                            where: {
                                'No': sMaterNo,
                                'name': sCPVer
                            }, page: {
                                curr: 1
                            }
                        });
                    });

                });

                $('#ShowThree').css("display", "block")

            }
        }


        /// 显示设备分类明细项信息
        ///daiwanshan
        function ShowDeviceClassDetail(sMaterNo, sKind) {

            var Data = '';
            var Params = { No: sMaterNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: sKind, B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#DeviceClassDetailList',
                    id: 'DeviceClassDetailID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=16-1&Data=' + Data,
                    height: '300',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers' },
                        { field: 'SNO', title: '序号', width: 40 },
                        { field: 'DCItem', title: '编号', width: 100 },
                        { field: 'CheckTxt', title: '内容', width: 230 },
                        { field: 'Type', title: '数据类型', width: 100 },
                        { field: 'Status', title: '状态', width: 100 },
                        { field: 'Kind', title: '分类', minWidth: 90 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate', title: '录入时间', width: 200 }
                        //{ field: 'op', title: '操作', width: 230, toolbar: '#barDemo_Detail', fixed: 'right' }
                    ]],
                    page: true
                });

            });  // layui.use('table', function () {


        }

        function closeDialog(n) {
            if (n == 1)//关闭详细信息弹窗
            {
                $('#ShowOne').css("display", "none")
                $('#ShowOne-fade').css("display", "none")
            }
            else if (n == 2)//关闭部门弹窗
            {
                $('#ShowTow').css("display", "none")
            }
            else if (n == 3)//关闭设备分类规则弹窗
            {
                $('#ShowThree').css("display", "none")
            }
            else//关闭设备分类出纳许明细
            {
                $('#ShowFour').css("display", "none")
            }
        }

        //关闭删除提示框
        function closeDelDialog() {
            $('#ShowDel').css("display", "none")
            $('#ShowDel-fade').css("display", "none")
        }

        //设备使用记录导出
        function exportDeviceUse() {
            var sDeviceNo = $("#txtDeviceNo").val()
            var sDeviceName = $("#txtDeviceName").val()
            var Params = { No: sDeviceNo, Name: sDeviceName, Item: "", Status: "", A: "DeviceUse", B: "", C: "", D: "", Remark: "", Flag: "100" };
            var Data = JSON.stringify(Params);

            $("#exportDeviceUse").attr("disabled", true)

            $.ajax({
                url: "../Service/PrdAssistAjax.ashx?OP=GenerateRecordFile",
                type: "POST",
                data: {
                    Data: Data
                },
                success: function (res) {
                    var parsedJson = jQuery.parseJSON(res);
                    if (parsedJson.Msg == "LoginError") {
                        ErrorMessage("登录超时，请重新登录！", 2000);
                        $("#exportDeviceUse").removeAttr("disabled")
                    } else if (parsedJson.Msg == "ParamsError") {
                        ErrorMessage("请求参数出错，请重试！", 2000);
                        $("#exportDeviceUse").removeAttr("disabled")
                    } else if (parsedJson.Msg == "Success") {  //如过是Success没有生成过就直接生成
                        DeviceUseCreaatePDF()
                    } else if (parsedJson.Msg == "Y_EXIST") {  //如过是Y_EXIST就说明已经生成过了一次则提示一下是否再次生成PDF
                        layer.confirm('该编号已经生成过，确定再次生成吗？', {
                            btn: ['确定', '取消'], // 按钮
                            cancel: function () {
                                $("#exportDeviceUse").removeAttr("disabled")
                            }
                        }, function () {
                            DeviceUseCreaatePDF();
                        }, function () {
                            $("#exportDeviceUse").removeAttr("disabled")
                        });
                    }
                },
                error: function () {
                    ErrorMessage("系统出错，请重试！", 2000);
                    $("#exportDeviceUse").removeAttr("disabled")
                    return;
                }
            })
        }

        //设备使用记录生成PDF
        function DeviceUseCreaatePDF() {
            var sDeviceNo = $("#txtDeviceNo").val()
            var sDeviceName = $("#txtDeviceName").val()
            var Params = { No: sDeviceNo, Name: sDeviceName, Item: "", Status: "", A: "", B: "", C: "", D: "", Remark: "", Flag: "DeviceUse" };
            var Data = JSON.stringify(Params);
            if (sDeviceNo == "") {
                ErrorMessage("获取不到设备编号", 2000);
                return;
            }
            if (sDeviceName == "") {
                ErrorMessage("获取不到设备名称", 2000);
                return;
            }

            $("#exportDeviceUse").attr("disabled", true)

            $.ajax({
                url: "../Service/PrdAssistAjax.ashx?OP=GenerateRecordFile",
                type: "POST",
                data: {
                    Data: Data
                },
                success: function (res) {
                    var parsedJson = jQuery.parseJSON(res);
                    if (parsedJson.Msg == "ImportError") {
                        ErrorMessage("模板导入失败，请重试！", 2000);
                    } else if (parsedJson.Msg == "CreateError") {
                        ErrorMessage("生成失败！", 2000);
                    } else if (parsedJson.Msg == "Error") {
                        ErrorMessage("生成失败！" + parsedJson.ExceptionMessage, 2000);
                    } else if (parsedJson.Msg == "Success") {
                        layer.msg("生成成功")
                        DeviceUseInfo();
                    }
                    $("#exportDeviceUse").removeAttr("disabled")
                },
                error: function () {
                    ErrorMessage("系统出错，请重试！", 2000);
                    $("#exportDeviceUse").removeAttr("disabled")
                    return;
                }
            })
        }

        function DeviceUseInfo() {
            var sDeviceNo = $("#txtDeviceNo").val()
            var Data = '';
            var Params = { No: sDeviceNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: "DeviceUse", B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);
            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#DeviceUsePDFList',
                    id: 'DeviceUsePDFList',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=28&Data=' + Data,
                    height: '280',
                    cellMinWidth: 80,
                    count: 50,
                    limit: 20,
                    limits: [10, 20, 30, 40, 50],
                    cols: [[
                        { type: 'numbers' },
                        { field: 'No', title: '编号', width: 200 },
                        { field: 'Name', title: '名称' },
                        { field: 'InMan', title: '录入人', width: 100 },
                        { field: 'InDate2', title: '录入时间', width: 170 },
                        { field: 'op', title: '操作', width: 100, toolbar: '#bar_DeviceUsePDFList', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(DeviceUsePDFList)', function (obj) {
                    var data = obj.data,
                        layEvent = obj.event;
                    if (layEvent == 'view') {
                        window.open(data.FilePath)
                    }
                })
            })
        }

        //设备信息导出
        function DeviceNo_export() {
            $("#btn_DeviceNo_export").attr("disabled", true)
            var sDeviceNo = $("#txtsDeviceNo").val();  //
            var sName = encodeURI($("#txtSDeviceName").val());  //
            var sMaterNo = encodeURI($("#txtSMMaterNo").val());  //
            var sStartTime = $("#txtStartTime").val()
            var sEndTime = $("#txtEndTime").val()

            var Data = '';
            var Params = { No: sDeviceNo, Name: sName, Item: "", Status: "", BDate: sStartTime, EDate: sEndTime, A: sMaterNo, B: "", C: "", D: "", E: "", Flag: "15" };
            var Data = JSON.stringify(Params);

            $.ajax({
                type: "POST",
                url: "../Service/PrdAssistAjax.ashx?OP=DeviceInfoExcel",   
                data: { Data: Data },
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    $("#btn_DeviceNo_export").removeAttr("disabled")
                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                        layer.msg('导出成功！');
                        window.open("../" + parsedJson.FilePath);
                    }
                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Error') {
                        layer.msg('导出失败！');
                        console.log(parsedJson.Message)
                    }
                    else {
                        layer.msg('系统出错，请重试1！');
                    }
                },
                error: function (data) {
                    $("#btn_DeviceNo_export").removeAttr("disabled")
                    layer.msg('系统出错，请重试2！');
                }
            });
        }
    </script>


    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

        })


    </script>

    <style type="text/css">

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        /* .black_overlay {
                    display: none;
                    position: absolute;
                    top: 0%;
                    left: 0%;
                    width: 100%;
                    height: 100%;
                    background-color: #bbbcc7;
                    z-index: 1001;
                    -moz-opacity: 0.8;
                    opacity: .80;
                    filter: alpha(opacity=60);
                }
        */
        .LabelDelBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #da542e;
        }

        .LabelAddBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #579fd8;
        }

        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }

        .find_input {
            width: 12.5%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }

        #ShowOne .XC-Form-block-Item span {
            width: 100px;
            line-height: 30px;
        }

        #ShowFour .XC-Form-block-Item span {
            width: 100px;
            line-height: 30px;
        }
    </style>


</head>
<body>
    <div class="div_find">
        <p>
            <label class="find_labela">设备编号</label> <input type="text" id="txtsDeviceNo" class="find_input" />
            <label class="find_labela">设备名称</label><input type="text" id="txtSDeviceName" class="find_input" />
            <label class="find_labela">设备分类编号</label><input type="text" id="txtSMMaterNo" class="find_input" />
            <label class="find_labela">校准有效期</label><input type="date" id="txtStartTime" class="find_input" style="margin-right:0.5%" />-<input type="date" id="txtEndTime" class="find_input" />
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="btn_DeviceNo_Open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="btn_DeviceNo_export" onclick="DeviceNo_export()">导出</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="javascript:openDialog(1)">添加</button>
        </p>
    </div>

    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="table_DeviceInfoList" lay-filter="table_DeviceInfoList"></table>

        <script type="text/html" id="barDemo_DeviceInfo">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="edit">修改</button>
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>
        </script>

    </div>


    <!--设备信息修改和新增弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1">设备信息</span>
            <span class="head-close" onclick="closeDialog(1)">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <div style="border-bottom: solid 1px #ccc;padding-bottom:12px">
                    <span style="font-weight: bold; ">设备分类</span>
                </div>

                <div style="display:flex;margin-top:15px">
                    <div style="width:100%">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">设备编号<span class="XC-Font-Red">*</span></span>
                                <input type="text" class="XC-Input-block" id="txtDeviceNo" name="txtDeviceNo" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">使用场景</span>
                                <select class="XC-Select-block" id="txtDeviceDesc">
                                    <option></option>
                                    <option>工位固定使用</option>
                                    <option>工位间移动使用</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div style="width:100%">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">设备名称<span class="XC-Font-Red">*</span></span>
                                <input type="text" class="XC-Input-block" id="txtDeviceName" name="txtDeviceName" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">设备规格</span>
                                <input type="text" class="XC-Input-block" id="txtDeviceSpec" name="txtDeviceSpec" />
                            </div>
                        </form>
                    </div>
                </div>

                <div style="border-bottom: solid 1px #ccc;padding-bottom:12px">
                    <span style="font-weight: bold; ">管理信息</span>
                </div>

                <div style="display:flex;margin-top:15px">
                    <div style="width:100%">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">管理部门编号</span>
                                <input type="text" class="XC-Input-block" id="txtDeptNo" name="txtDeptNo" readonly="readonly" />
                                <input type="button" value="选择" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="btn_Dept_open" onclick="openDialog(2)" style="margin-left:5px" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">设备状态</span>
                                <select class="XC-Select-block" id="txtStatus">
                                    <option></option>
                                    <option>启用</option>
                                    <option>封存</option>
                                    <option>停用</option>
                                    <option>验收中</option>
                                </select>
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">校准有效期</span>
                                <input type="date" class="XC-Input-block" id="txtDCUseDate" name="txtDCUseDate" />
                            </div>

                        </form>
                    </div>
                    <div style="width:100%">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">管理部门</span>
                                <input type="text" class="XC-Input-block" id="txtDeptName" name="txtDeptName" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">有效期(年)<span class="XC-Font-Red">*</span></span>
                                <input type="text" class="XC-Input-block" id="txtInventoryCycle" name="txtInventoryCycle" />
                            </div>
                            <!--<div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">维护要求</span>
                    <input type="text" class="XC-Input-block" id="txtDMaintainReq" name="txtDMaintainReq" readonly=readonly />
                </div>-->
                        </form>
                    </div>
                </div>

                <div style="border-bottom: solid 1px #ccc;padding-bottom:12px">
                    <span style="font-weight: bold; ">维护信息</span>
                </div>

                <div style="display:flex;margin-top:15px">
                    <div style="width:100%">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">设备分类编号</span>
                                <input type="text" class="XC-Input-block" id="txtMaterNo" name="txtMaterNo" readonly="readonly" style="height:30px;" />
                                <input type="button" value="选择" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="btn_DeviceClass_open" onclick="openDialog(3)" style="margin-left:5px" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Select-block">点检要求</span>
                                <select class="XC-Select-block" id="txtCheckReq">
                                    <option></option>
                                    <option>工作日点检</option>
                                    <option>使用前点检</option>
                                    <option>周点检</option>
                                </select>
                            </div>
                            <!--<div class="XC-Form-block-Item">
                    <span class="XC-Span-Textarea-block">备注</span>
                    <textarea class="XC-Textarea-block" id="txtRemark"></textarea>
                </div>-->
                        </form>
                    </div>
                    <div style="width:100%">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Select-block">设备类型</span>
                                <select class="XC-Select-block" id="txtDeviceKind">
                                    <option></option>
                                    <option>设备</option>
                                    <option>工装</option>
                                    <option>仪器</option>

                                </select>
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Select-block">保养要求</span>
                                <select class="XC-Select-block" id="txtMaintainReq">
                                    <option></option>
                                    <option>年度</option>
                                    <option>半年</option>
                                    <option>季度</option>
                                    <option>月度</option>
                                    <option>周度</option>
                                </select>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="wangid_conbox" id="div_DeviceUsePDFList">
                    <div style="display:flex;justify-content:space-between;border-bottom: solid 1px #ccc;padding-bottom:3px;margin-top:15px">
                        <span style="font-weight: bold; ">使用记录</span>
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="exportDeviceUse" onclick="exportDeviceUse()" style="width:70px">生成PDF</button>
                    </div>
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="DeviceUsePDFList" lay-filter="DeviceUsePDFList"></table>

                    <script type="text/html" id="bar_DeviceUsePDFList">
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="view">查看</button>
                    </script>

                </div>
            </div>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="btn_DeviceInfoSave">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="btn_DeviceInfoSaveClose" onclick='closeDialog(1)'>关闭</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>

    <!--弹窗选择部门-->
    <div class="XC-modal XC-modal-xl" id="ShowTow">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title2">选择部门信息</span>
            <span class="head-close" onclick="closeDialog(2)">X</span>
        </div>
        <div class="XC-modal-body">
            <div>
                <form class="XC-Form-inline-block" style="margin:0px;">
                    <div class="XC-Form-Item" style="margin-bottom:2px">
                        <span class="XC-Span-Input-block">部门编号</span>
                        <input type="text" class="XC-Input-block" id="txtSWNo" name="txtSWNo" value="" />
                    </div>
                    <div class="XC-Form-Item">
                        <span class="XC-Span-Input-block">部门名称</span>
                        <input type="text" class="XC-Input-block" id="txtSWDesc" name="txtSWDesc" value="" />
                    </div>
                    <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="btn_DeptInfo_open">搜索</button>
                </form>
            </div>
            <div class="XC-modal-xl-center">
                <div style=" font-weight: bold;padding-bottom:10px;">
                    基本信息
                </div>
                <div class="wangid_conbox">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="table_SelectDeptInfo" lay-filter="table_SelectDeptInfo"></table>

                    <script type="text/html" id="barDemo_SelectDeptInfo">
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="slc">选择</button>
                    </script>
                </div>
            </div>
        </div>
    </div>


    <!--弹窗选择设备分类信息-->
    <div class="XC-modal XC-modal-xl" id="ShowThree">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title3">选择设备分类信息</span>
            <span class="head-close" onclick="closeDialog(3)">X</span>
        </div>
        <div class="XC-modal-body">
            <div>
                <form class="XC-Form-inline-block" style="margin:0px;">
                    <div class="XC-Form-Item" style="margin-bottom:2px">
                        <span class="XC-Span-Input-block">设备分类编号</span>
                        <input type="text" class="XC-Input-block" id="txtSMaterNo" name="txtSMaterNo" value="" />
                    </div>
                    <div class="XC-Form-Item">
                        <span class="XC-Span-Input-block">设备分类名称</span>
                        <input type="text" class="XC-Input-block" id="txtSMaterName" name="txtSMaterName" value="" />
                    </div>
                    <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="DeviceClassBut_open">搜索</button>
                </form>
            </div>
            <div class="XC-modal-xl-center">
                <div style=" font-weight: bold;padding-bottom:10px;">
                    基本信息
                </div>
                <div class="wangid_conbox">
                    <!--下面写内容-->
                    <table class="layui-hide" id="DeviceClassHeadlist" lay-filter="DeviceClassHeadlist"></table>

                    <script type="text/html" id="barDemo_SelectDeviceClass">
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detailC" style="width:70px">点检详情</button>
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detailM" style="width:70px">维护详情</button>
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="selectDC">选择</button>
                    </script>
                </div>
            </div>
        </div>
    </div>

    <!--显示设备分类规则明细弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowFour">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title4">设备分类详情</span>
            <span class="head-close" onclick="closeDialog(4)">X</span>
        </div>
        <div class="XC-modal-body" style="padding:10px">
            <div class="XC-modal-xl-center">

                <div style="border-bottom: solid 1px #ccc;padding-bottom:12px">
                    <span style="font-weight: bold; ">设备分类</span>
                </div>

                <div style="display:flex;margin-top:10px">
                    <div style="width:100%">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">设备分类编号</span>
                                <input type="text" class="XC-Input-block" id="txtDMaterNo" name="txtDMaterNo" readonly=readonly placeholder="系统自动产生" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">管理部门编号</span>
                                <input type="text" class="XC-Input-block" id="txtDDeptNo" name="txtDDeptNo" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">点检要求</span>
                                <input type="text" class="XC-Input-block" id="txtDCheckReq" name="txtDCheckReq" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">设备类型</span>
                                <input type="text" class="XC-Input-block" id="txtDDeviceKind" name="txtDDeviceKind" readonly=readonly />
                            </div>
                        </form>
                    </div>
                    <div style="width:100%">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">设备分类名称</span>
                                <input type="text" class="XC-Input-block" id="txtDMaterName" name="txtDMaterName" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">管理部门名称</span>
                                <input type="text" class="XC-Input-block" id="txtDDeptName" name="txtDDeptName" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">维护要求</span>
                                <input type="text" class="XC-Input-block" id="txtDMaintainReq" name="txtDMaintainReq" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Textarea-block">备注</span>
                                <textarea class="XC-Textarea-block" id="txtDRemark" name="txtDRemark" readonly=readonly style="max-height:45px"> </textarea>
                            </div>
                        </form>
                    </div>
                </div>

                <div style="border-bottom: solid 1px #ccc;padding-bottom:12px">
                    <span style="font-weight: bold; ">点检/维护明细</span>
                </div>

                <div class="wangid_conbox">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="DeviceClassDetailList" lay-filter="DeviceClassDetailList"></table>
                </div>
            </div>
        </div>
    </div>

    <!--操作提示框 删除设备信息-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDelDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelDeviceNo" name="txtDelDeviceNo" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="btn_DeviceInfoDel">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDelDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>


    <!--消息提示-->
    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>
</html>