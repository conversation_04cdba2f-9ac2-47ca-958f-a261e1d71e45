﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Model
{
    public class K3HttpResultEntity
    {
        public Results Result { get; set; }

        public class SuccessEntity
        {
            public string Id { get; set; }
            public string Number { get; set; }
            public int DIndex { get; set; }
        }

        public class ResponseStatus
        {
            public bool Issuccess { get; set; }
            public Error[] Errors { get; set; }
            public SuccessEntity[] SuccessEntitys { get; set; }
            public string[] SuccessMessages { get; set; }
            public string Msgcode { get; set; }
        }

        public class Results
        {
            public ResponseStatus Responsestatus { get; set; }
            public string Id { get; set; }
            public string Number { get; set; }
            public object[] NeedReturnData { get; set; }
        }

        public class Error
        {

        }
    }
}
