﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using Common;

namespace DAL
{
    public static class CustInfoDal
    {

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public static DataTable GetCustInfo(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * from T_CustInfo ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DBHelper.GetDataTable(strSql.ToString());

        }
    }
}
