﻿
var BaseModule = function() {

    this.PurchNo = '';
    this.Supplier = '';
    this.Locate = '';
    this.Lng = '';
    this.Lat = '';
    this.OperateName = '';
    this.Operate = '';
    this.InMan = '';
    this.Status = '';
    this.CompanyNo = '';
    this.SeqNo = '';
    this.Remark = '';
    this.NEFlag = '';

    var that = this;

    this.setData = function(data) {
        that.PurchNo = this.PurchNo;
        that.Supplier = this.Supplier;
        that.Locate = this.Locate;
        that.Lng = this.Lng;
        that.Lat = this.Lat;
        that.Lat = this.OperateName;
        that.Operate = this.Operate;
        that.InMan = this.InMan;
        that.Status = this.Status;
        that.CompanyNo = this.CompanyNo;
        that.SeqNo = this.SeqNo;
        that.Remark = this.Remark;
        that.NEFlag = this.NEFlag;
    };

    this.getDataFromControl = function() {
        /*获取控件值*/
        that.PurchNo = $.trim($('#txtPurchNo').val());
        that.Supplier = $.trim($('#txtSupplier').val());
        that.Locate = $.trim($('#txtLocate').val());
        that.Lng = $.trim($('#txtLng').val());
        that.Lat = $.trim($('#txtLat').val());
        that.OperateName = $.trim($('#txtOperateName').val());
        that.Operate = $.trim($('#txtOperate').val());
        that.SeqNo = $.trim($('#txtSeqNo').val());
        that.Remark = $.trim($('#txtRemark').val());
        that.NEFlag = $.trim($('#txtNEFlag').val());
        that.Status = '';
        that.CompanyNo = '';
    };

    this.setDataFromControl = function(data) {
        /*设置控件值*/
        $('#txtPurchNo').val(data.ApplyNo);
        $('#txtSupplier').val(data.SupplierEn);
        $('#txtOperateName').val(data.OperateName);
        $('#txtOperate').val(data.Operate);
        $('#Status').val(data.Status);
        $('#CompanyNo').val(data.CompanyNo);
        $('#txtSeqNo').val(data.SeqNo);
        $('#txtRemark').val(data.Remark);
        $('#txtNEFlag').val(data.NEFlag);
        $('#txtInFlag').val(data.InFlag); // 记录是自己录入，还是办事处导入的标识
    };

    this.clareDataFromControl = function(data) {
        $('#txtPurchNo').val('');
        $('#txtSupplier').val('');
        $('#txtLocate').val('');
        $('#txtLng').val('');
        $('#txtLat').val('');
        $('#txtOperateName').val('');
        $('#txtOperate').val('');
        $('#Status').val('');
        $('#CompanyNo').val('');
        $('#txtSeqNo').val('');
        $('#txtRemark').val('');
        $('#txtNEFlag').val('');
        $('#txtInFlag').val('');
        $('#txtIsEXE').val('');
    }

};


$(function() {

    function EntityMsg(item, Mesage) {
        $("#div_warning").show();
        item.focus();
        $("#sMessage").html(Mesage);
    }
    // 注册用户查询 重新登录
    $('#BtnRelationLogin').click(function() {
        var Num = parseInt(Math.random() * 1000);
        location.href = "../Login.htm?CFlag=30&RA" + Num;

    });


    // 数据查询 -- 用户注册查询
    $('#btnRelationSumbit').click(function() {

        var sBDate = $("#txtRegBDate").val();
        var sEDate = $("#txtPurchEDate").val();

        var Data = '';
        var Params = { BDate: sBDate, EDate: sEDate };
        var Data = JSON.stringify(Params);
        $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=RelationList&Data=' + Data + "&CFlag=30" });
    });


    //  保存:保存系统类别
    $('#TypeSaveBtn').click(function () {
        $("#TypeSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();

        var Kind = $("#head-title1").html();  // 添加模块  
        var PKind = $("#txtAddKind").val();  // 选中的大类，主要是新增小类时用来判断，大类是否有选择
        var MName = $("#txtKind").val();  // 大类或小类名称
        var KNo = $("#txtNo").val();  // 唯一标识，用来修改的
        var URL = "";
        var Code = "";
        var SeqNo = $("#txtSeq").val();
        var InMan = $("#txtInMan").val();
        var Flag = "";


        if (Kind == "添加大类") {
            Flag = "1";
        }
        else {
            Flag = "2";
        }

        if (MName == "") {
            ErrorMessage("请输入类别名称！", 2000)
            $("#TypeSaveBtn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { Kind: Kind, PKind: PKind, MName: MName, KNo: KNo, Code: Code, URL: URL, SeqNo: SeqNo, InMan: InMan, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=AddSysKind&CFlag=1",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#TypeSaveBtn").removeAttr("disabled");
                    $("#divsuccess").show();

                    $("#ShowOne").css("display", "none")
                    $("#ShowOne-fade").css("display", "none")

                    $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetSysKind&CFlag=40' }); // 刷新数据
                    $('#dgDataList1').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetSysKindList&CFlag=40-1&CKind=' + encodeURI(PKind) });
                    $("#txtK").val('');
                    $("#txtKind").val('');
                    $("#txtAddKind").val('');
                    $("#txtNo").val('');
                    $("#txtSeq").val('');

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#TypeSaveBtn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#TypeSaveBtn").removeAttr("disabled");
                    ErrorMessage("类别已存在，请确认", 2000)
                }
                else {
                    $("#TypeSaveBtn").removeAttr("disabled");
                    ErrorMessage("系统出错，请重试1！", 2000)
                }
            },
            error: function (data) {
                $("#TypeSaveBtn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });




    //  保存:保存父级，下级关系的数据：系统类别，菜单（模块-功能）
    $('#MenuSaveBtn').click(function () {
        $("#MenuSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();

        var Kind = $("#head-title1").html();  // 添加模块  添加菜单
        var PKind = $("#txtAddKind").val();  // 选中的大类，主要是新增小类时用来判断，大类是否有选择
        var MName = $("#txtName").val();
        var KNo = $("#txtNo").html();   // 唯一标识，用来修改的
        var URL = $("#txtURL").val();
        var Code = $("#txtCode").val();
        var SeqNo = $("#txtSeq").val();
        var InMan = $("#txtInMan").val();
        var Flag = "";

        if (Kind == "添加模块" || Kind == "修改模块") {
            Flag = "23-1";
        }
        else if (Kind == "添加菜单" || Kind == "修改菜单") {
            Flag = "23-2";
        }
        else {
            Flag = "2";
        }

        if (MName == "") {
            ErrorMessage("请输入名称！", 2000)
            $("#MenuSaveBtn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { Kind: Kind, PKind: PKind, MName: MName, KNo: KNo, Code: Code, URL: URL, SeqNo: SeqNo, InMan: InMan, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=AddSysKind&CFlag=1",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#MenuSaveBtn").removeAttr("disabled");
                    $("#divsuccess").show();

                    $("#ShowOne").css("display", "none")
                    $("#ShowOne-fade").css("display", "none")

                    $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetModule&CFlag=23-1' });

                    $('#dgDataList1').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetModule&CFlag=23-2&CKind=' + encodeURI(PKind) });

                    $("#txtK").val('');
                    //$("#txtAddKind").val('');
                    $("#txtName").val('');
                    $("#txtNo").val('');
                    $("#txtSeq").val('');

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#MenuSaveBtn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#MenuSaveBtn").removeAttr("disabled");
                    ErrorMessage("功能模块已存在，请确认", 2000)
                }
                else {
                    $("#TypeSaveBtn").removeAttr("disabled");
                    ErrorMessage("系统出错，请重试1！", 2000)
                }
            },
            error: function (data) {
                $("#MenuSaveBtn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });


    //  删除模块或菜单
    $('#Model_Del_Btn').click(function () {
        $("#Model_Del_Btn").attr({ "disabled": "disabled" });
        $("#div_warning2").hide();
        $("#divsuccess2").hide();
        $("#loginError").hide();

        var PKind = $("#txtAddKind").val(); //父模块
        var Kind = $("#Label_Type").html();  //  模块   菜单
        var MName = $("#txtDelName").val();
        var KNo = $("#LMID").html();   // 唯一标识，用来修改的
        var InMan = $("#txtInMan").val();
        var Flag = "";

        if (Kind == "模块") {
            Flag = "23-2";
        }
        else {
            Flag = "23-4";
        }

        if (MName == "") {
            ErrorMessage("请选择要删除的记录！", 2000)
            $("#Model_Del_Btn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { Kind: Kind, MName: MName, KNo: KNo, InMan: InMan, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=DelModel&CFlag=1",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#Model_Del_Btn").removeAttr("disabled");
                    $("#divsuccess").show();
                    $('#dgDataList1').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetModule&CFlag=23-2&CKind=' + encodeURI(PKind) });
                    $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetModule&CFlag=23-1' });
                    $("#txtDelName").val('');
                    $("#LMID").val('');

                    $("#ShowDel").css("display", "none")
                    $("#ShowDel-fade").css("display", "none")

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#Model_Del_Btn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'SentError') {
                    $("#Model_Del_Btn").removeAttr("disabled");
                    ErrorMessage("发送微信出错，请线下通知！", 2000)
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#Model_Del_Btn").removeAttr("disabled");
                    ErrorMessage("该模块下有功能菜单，请先删除菜单!", 2000)
                }
                else {
                    $("#Model_Del_Btn").removeAttr("disabled");
                    ErrorMessage("系统出错，请重试1！", 2000)
                }
            },
            error: function (data) {
                $("#Model_Del_Btn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });




    //  给公司添加能操作的功能模块
    $('#Btn_AddModelForComp').click(function () {
        $("#Btn_AddModelForComp").attr({ "disabled": "disabled" });
        $("#div_warning3").hide();
        $("#divsuccess3").hide();

        var Kind = $("#Label_Type").html();  //  模块   菜单
        var Model = $("#txtModel").val(); //父模块
        var ButtName = $("#txtButtName").val();
        var CompNo = $("#txtCompNo").val();
        var SeqNo = $("#txtSeqNo").val();
        var Flag = "";

        if (Kind == "模块") {
            Flag = "23-5";
        }
        else {
            Flag = "23-6";

            if (ButtName == "") {
                ErrorMessage("请选择要添加的菜单！", 2000)
                $("#Btn_AddModelForComp").removeAttr("disabled");
                return;
            }
        }

        if (Model == "") {
            ErrorMessage("请先选择模块！", 2000)
            $("#Btn_AddModelForComp").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { Kind: Kind, PKind: Model, MName: ButtName, KNo: "", Code: CompNo, URL: "", SeqNo: SeqNo, InMan: "", Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=AddSysKind&CFlag=1",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#Btn_AddModelForComp").removeAttr("disabled");
                    $("#div_warning3").show();

                    $("#ShowTow").css("display", "none")
                    $("#ShowTow-fade").css("display", "none")

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#Btn_AddModelForComp").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#Btn_AddModelForComp").removeAttr("disabled");
                    ErrorMessage("该模块已添加到该公司了！", 2000)
                } else {
                    $("#Btn_AddModelForComp").removeAttr("disabled");
                    ErrorMessage("系统出错，请重试1！", 2000)
                }
            },
            error: function (data) {
                $("#Btn_AddModelForComp").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });






    //  删除大类
    $('#DelDL').click(function() {
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();

        var Kind = $("#txtAddKind").val();  // 选中的大类，主要是新增小类时用来判断，大类是否有选择
        var KindList = "";
        var InMan = $("#txtInMan").val();


        if (Kind == "") {
            $("#warningCH").html("请选择要删除的大类！")
            $("#warningCH").show();
            $("#DelDL").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { Kind: Kind, KindList: KindList, InMan: InMan };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=DelSysKind&CFlag=1",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#warningCH").html("删除成功!")
                    $("#warningCH").show();
                    $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetSysKind&CFlag=40' }); // 刷新数据

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#warningCH").html("您未登陆系统，请先登录！")
                    $("#warningCH").show();
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'SentError') {
                    $("#warningCH").html("发送微信出错，请线下通知！")
                    $("#warningCH").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#warningCH").html("该大类下有小类，请先删除小类!")
                    $("#warningCH").show();
                }
                else {
                    $("#warningCH").html("系统出错，请重试1！")
                    $("#warningCH").show();
                }
            },
            error: function(data) {
                $("#warningCH").html("系统出错，请重试2！")
                $("#warningCH").show();
            }
        });
    });

    //  删除小类
    $('#DelXL').click(function() {
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();

        var Kind = $("#txtAddKind").val();  // 选中的大类，主要是新增小类时用来判断，大类是否有选择
        var KindList = $("#txtDKL").val()
        var InMan = $("#txtInMan").val();


        if (Kind == "") {
            $("#warningCH").html("请选择要删除的大类！")
            $("#warningCH").show();
            $("#DelDL").removeAttr("disabled");
            return;
        }

        if (KindList == "") {
            $("#warningCH").html("请选择要删除的小类！")
            $("#warningCH").show();
            $("#DelDL").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { Kind: Kind, KindList: KindList, InMan: InMan };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=DelSysKind&CFlag=2",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#warningCH").html("删除成功!")
                    $("#warningCH").show();
                    $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetSysKind&CFlag=40' }); // 刷新数据

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#warningCH").html("您未登陆系统，请先登录！")
                    $("#warningCH").show();
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'SentError') {
                    $("#warningCH").html("发送微信出错，请线下通知！")
                    $("#warningCH").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#warningCH").html("该大类下有小类，请先删除小类!")
                    $("#warningCH").show();
                }
                else {
                    $("#warningCH").html("系统出错，请重试1！")
                    $("#warningCH").show();
                }
            },
            error: function(data) {
                $("#warningCH").html("系统出错，请重试2！")
                $("#warningCH").show();
            }
        });
    });


    //  字典类别查询
    $('#Btn_RSearh').click(function() {
        var sName = encodeURI($("#txtKName").val());  // 名称  // 解决中文乱码的问题。

        $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetSysKind&CFlag=40&CKind=' + sName });
        $('#dgDataList1').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetSysKindList&CFlag=40-1&CKind=' + sName });
    });


    //  删除系统类别
    $('#SysKind_Del_Btn').click(function () {

        $("#SysKind_Del_Btn").removeAttr("disabled");
        $("#div_warning2").hide();
        $("#divsuccess2").hide();
        $("#loginError").hide();

        var Kind = $("#txtAddKind").val();  // 选中的大类，主要是新增小类时用来判断，大类是否有选择
        var KindList = $("#txtDelName").val()
        var InMan = $("#txtInMan").val();
        var Flag = $("#txtDFlag").val();   // 1 删除大类，2 删除小类


        if (KindList == "") {
            ErrorMessage("请选择要删除的类别！", 2000)
            $("#SysKind_Del_Btn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { Kind: Kind, KindList: KindList, InMan: InMan };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=DelSysKind&CFlag=" + Flag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#SysKind_Del_Btn").removeAttr("disabled");

                    $("#ShowDel").css("display", "none")
                    $("#ShowDel-fade").css("display", "none")

                    $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetSysKind&CFlag=40' }); // 刷新数据
                    $('#dgDataList1').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetSysKindList&CFlag=40-1&CKind=' + encodeURI(Kind) });

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'SentError') {
                    $("#SysKind_Del_Btn").removeAttr("disabled");
                    ErrorMessage("发送微信出错，请线下通知！", 2000)
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#SysKind_Del_Btn").removeAttr("disabled");
                    ErrorMessage("该大类下有小类，请先删除小类!", 2000)
                }
                else {
                    $("#SysKind_Del_Btn").removeAttr("disabled");
                    ErrorMessage("系统出错，请重试1！", 2000)
                }
            },
            error: function (data) {
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });

    });


    //  用户查询
    $('#UserBut_open').click(function() {
        var sNo = $("#LoginNo").val();  //账号
        var sName = $("#LoginName").val()  // 用户名称
        var sDName = $("#DeptName").val();  // 部门
        var sSex = $("#UserSex").val();

        var Data = '';
        var Params = { UserNo: sNo, UserName: sName, Dept: sDName, Sex: sSex };
        var Data = JSON.stringify(Params);

        $('#Usertable').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetUserInfo&sFlag=10&Data=' + Data });

    });

    // 把用户信息导出EXCEL
    $("#UserBut_excel").click(function() {


        var sNo = $("#LoginNo").val();  //账号
        var sName = $("#LoginName").val()  // 用户名称
        var sDName = $("#DeptName").val();  // 部门
        var sSex = $("#UserSex").val();

        var Data = '';
        var Params = { UserNo: sNo, UserName: sName, Dept: sDName, Sex: sSex };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetUserExcel",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);
                window.open(parsedJson.sFilePath);
            }
        });
    });


    // 保存用户信息
    $('#UserSaveBtn').click(function () {
        $("#UserSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();
        var sRoleStr = "";  // 角色字符串
        var sKind = $("#txtK").html();  // 新增 修改 标识                     
        var sNo = $("#txtUserNo").val().trim();
        var sName = $("#txtUserName").val().trim();
        // var sDept = $("#txtDept").val();
        var sDept = $('#txtDept option:selected').text().trim(); //选中的文本 获取下拉值  var sDept = $("#txtLDept").val(); // 部门 得到 编号
        var sPwd = $("#txtPwd").val().trim();
        var sCPwd = $("#txtCPwd").val().trim();
        var sZW = $("#txtZW").val().trim();
        var sSex = $("#txtSex").val();
        var sTelph = $("#txtTelph").val().trim();
        var sPhone = $("#txtPhone").val().trim();
        var sEmail = $("#txtEmail").val().trim();
        var sQQ = $("#txtQQ").val().trim();
        var sComp = $("#txtCompany").val();
        var InMan = $("#txtInMan").val();
        var sFile = $("#txtFile").val(); // 签名名称
        var sPath = $("#txtPath").val(); // 签名图片路径
        var Flag = $("#txtAEFlag").val();   //24-3 新增；24-4 修改


        $.each($('input:checkbox:checked'), function () {
            sRoleStr = sRoleStr + "," + $(this).val();
            // window.alert("你选了：" +
            //    $('input[type=checkbox]:checked').length + "个，其中有：" + $(this).val());
        });

        sRoleStr = sRoleStr.substr(1);
        sRoleStr = "(" + sRoleStr + ")";

        if (sNo == "") {
            ErrorMessage("请输入账号！", 2000)
            $("#UserSaveBtn").removeAttr("disabled");
            return;
        }
        if (sName == "") {
            ErrorMessage("请输入名称！", 2000)
            $("#UserSaveBtn").removeAttr("disabled");
            return;
        }
        if (sPwd == "") {
            ErrorMessage("请输入密码！", 2000)
            $("#UserSaveBtn").removeAttr("disabled");
            return;
        }

        // 定义匹配的正则表达式
        const pattern = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]+$/;

        if (sPwd.length < 6 || sPwd.length > 12) {
            ErrorMessage("密码长度必须在6到12个字符之间！", 2000)
            $("#UserSaveBtn").removeAttr("disabled");
            return;
        } else if (pattern.test(sPwd) == false) {
            ErrorMessage("密码必须包含至少一个字母和一个数字，且只能包含字母和数字！", 3500)
            $("#UserSaveBtn").removeAttr("disabled");
            return;
        }

        if (sPwd != sCPwd) {
            ErrorMessage("两次密码输入不一致！", 2000)
            $("#UserSaveBtn").removeAttr("disabled");
            return;
        }

        if ((sRoleStr == "") || (sRoleStr == "(on)")) {
            ErrorMessage("请选择角色", 2000)
            $("#UserSaveBtn").removeAttr("disabled");
            return;
        }

        if ($("#CH_SignFlag").is(':checked')) {
            if (sFile == "") {
                ErrorMessage("请选择签名图片！", 2000)
                $("#UserSaveBtn").removeAttr("disabled");
                return;
            }
        }


        var Data = '';
        var Params = { Kind: sKind, No: sNo, Name: sName, Dept: sDept, Pwd: sPwd, ZW: sZW, Sex: sSex, Telph: sTelph, Phone: sPhone, Email: sEmail, QQ: sQQ, File: sFile, Path: sPath, ModuleName: ModuleName, Comp: sComp, RoleStr: sRoleStr, InMan: InMan, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=AddEditUser&CFlag=" + Flag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#UserSaveBtn").removeAttr("disabled");
                    $("#divsuccess").show();

                    $('#txtUserNo').val("");
                    $('#txtUserName').val("");
                    $('#txtDept').val("");
                    $('#txtPwd').val("");
                    $('#txtCPwd').val("");
                    $('#txtZW').val("");
                    $('#txtSex').val("");
                    $('#txtTelph').val("");
                    $('#txtPhone').val("");
                    $('#txtEmail').val("");
                    $('#txtQQ').val("");


                    $('#ShowOne').css("display", "none");
                    $('#ShowOne-fade').css("display", "none");

                    $('#Usertable').bootstrapTable('refresh', { url: "../Service/BaseModuleAjax.ashx?OP=GetUserInfo&sFlag=30" });

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#UserSaveBtn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#UserSaveBtn").removeAttr("disabled");
                    ErrorMessage("该账号已存在，如在本组织找不到，请联系管理员！", 2000)
                }
                else {
                    $("#UserSaveBtn").removeAttr("disabled");
                    ErrorMessage("系统出错，请重试1！", 2000)
                }
            },
            error: function (data) {
                $("#UserSaveBtn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });


    // 保存个人信息
    $('#PersonalSaveBtn').click(function() {
        $("#PersonalSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();

        var sKind = $("#txtK").html();  // 新增 修改 标识                     
        var sNo = $("#txtUserNo").val();
        var sName = $("#txtUserName").val().trim();
        var sDept = $("#txtDept").val();
        var sPwd = $("#txtPwd").val().trim();
        var sCPwd = $("#txtCPwd").val().trim();
        var sZW = $("#txtZW").val().trim();
        var sSex = $("#txtSex").val();
        var sTelph = $("#txtTelph").val().trim();
        var sPhone = $("#txtPhone").val().trim();
        var sEmail = $("#txtEmail").val().trim();
        var sQQ = $("#txtQQ").val().trim();
        var sComp = $("#txtCompany").val();
        var InMan = $("#txtInMan").val();
        var Flag = "";
        Flag = "24-5";  // 修改个人信息

        if (sNo == "") {
            $("#div_warning").html("请输入账号！")
            $("#div_warning").show();
            $("#PersonalSaveBtn").removeAttr("disabled");
            return;
        }
        if (sName == "") {
            $("#div_warning").html("请输入名称！")
            $("#div_warning").show();
            $("#PersonalSaveBtn").removeAttr("disabled");
            return;
        }
        if (sPwd == "") {
            $("#div_warning").html("请输入密码！")
            $("#div_warning").show();
            $("#PersonalSaveBtn").removeAttr("disabled");
            return;
        }


        // 定义匹配的正则表达式
        const pattern = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]+$/;

        if (sPwd.length < 6 || sPwd.length > 12) {
            $("#div_warning").html("密码长度必须在6到12个字符之间！")
            $("#div_warning").show();
            $("#PersonalSaveBtn").removeAttr("disabled");
            return;
        } else if (pattern.test(sPwd) == false) {
            $("#div_warning").html("密码必须包含至少一个字母和一个数字，且只能包含字母和数字！")
            $("#div_warning").show();
            $("#PersonalSaveBtn").removeAttr("disabled");
            return;
        }


        if (sPwd != sCPwd) {
            $("#div_warning").html("两次密码输入不一致！")
            $("#div_warning").show();
            $("#PersonalSaveBtn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { Kind: sKind, No: sNo, Name: sName, Dept: sDept, Pwd: sPwd, ZW: sZW, Sex: sSex, Telph: sTelph, Phone: sPhone, Email: sEmail, QQ: sQQ, ModuleName: ModuleName, Comp: sComp, RoleStr: "()", InMan: InMan, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=AddEditUser&CFlag=" + Flag,
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#PersonalSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("信息修改成功！");
                    $("#div_warning").show();

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#PersonalSaveBtn").removeAttr("disabled");
                    $("#loginError").html("您未登陆系统，请先登录！")
                    $("#loginError").show();
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#PersonalSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("该用户已存在，请确认！")
                    $("#div_warning").show();
                }
                else {
                    $("#PersonalSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#PersonalSaveBtn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });



    //  删除用户信息
    $('#User_Del_Btn').click(function () {
        $("#User_Del_Btn").removeAttr("disabled");
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();

        var sNo = $("#txtDelNo").val();

        if (sNo == "") {
            ErrorMessage("请选择要删除的用户！", 2000)
            $("#User_Del_Btn").removeAttr("disabled");
            return;
        }

        var Data = '';
        // var Params = { DelNo: sNo};
        //var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=DelUserInfo&CNO=" + sNo + "&sMName=" + encodeURIComponent(ModuleName),
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $('#ShowDel').css("display", "none");
                    $('#ShowDel-fade').css("display", "none");

                    $('#Usertable').bootstrapTable('refresh', { url: "../Service/BaseModuleAjax.ashx?OP=GetUserInfo&sFlag=30" }); // 刷新数据

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                    //location.href = "Login.htm";
                } else {
                    $("#User_Del_Btn").removeAttr("disabled");
                    ErrorMessage("系统出错，请重试1！", 2000)
                }
            },
            error: function (data) {
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });

    });



    // 保存角色
    $('#RoleSaveBtn').click(function () {
        $("#RoleSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();
        var sName = $("#txtRole").val();
        //var InMan = $("#txtInMan").val();
        var Flag = "25-5";

        if (sName == "") {
            $("#AdmsgS").html("请输入角色名称！")
            $("#RoleSaveBtn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { Name: sName, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=AddRole&CFlag=25-5",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#RoleSaveBtn").removeAttr("disabled");
                    $("#AdmsgS").html("添加成功！")
                    $("#txtRole").val("");
                    $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=25-1' });

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#RoleSaveBtn").removeAttr("disabled");
                    $("#loginError").html("您未登陆系统，请先登录！")
                    $("#loginError").show();
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#RoleSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("该角色已存在，请确认！")
                    $("#div_warning").show();
                }
                else {
                    $("#RoleSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function (data) {
                $("#RoleSaveBtn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });


    //  删除角色
    $('#Role_Del_Btn').click(function () {
        $("#Role_Del_Btn").attr({ "disabled": "disabled" });
        $("#div_warning2").hide();
        $("#div_warning2").html("")
        $("#divsuccess2").hide();
        $("#loginError").hide();

        var sName = $("#txtDelName").val();  //  
        var sNo = $("#RoleID").val();
        var InMan = $("#txtInMan").val();


        if (sName == "") {
            ErrorMessage("请选择要删除的角色！", 2000)
            $("#Role_Del_Btn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { No: sNo, InMan: InMan };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=DelRoleInfo&CFlag=25-6",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#Role_Del_Btn").removeAttr("disabled");

                    $("#ShowDel").css("display", "none")
                    $("#ShowDel-fade").css("display", "none")

                    $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=25-1' });

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    ErrorMessage("该角色已被用户管理使用，不能删除！", 2000)
                    //location.href = "Login.htm";
                } else {
                    ErrorMessage("系统出错，请重试1！", 2000)
                }
            },
            error: function (data) {
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });


    //  物料分类查询
    $('#Btn_MTSearh').click(function() {
        var sName = encodeURI($("#txtKName").val());  // 名称  // 解决中文乱码的问题。

        $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetMaterType&CFlag=50&CKind=' + sName });
        $('#dgDataList1').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetMaterType&CFlag=50-1&CKind=' + sName });
    });


    //  保存:保存物料分类
    $('#MaterTypeSaveBtn').click(function() {
        $("#TypeSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();

        var Kind = $("#txtK").html();  // 添加模块
        var PKind = $("#txtAddKind").val();  // 选中的大类，主要是新增小类时用来判断大类是否有选择
        var MTID = $("#txtMTID").val();  // 唯一标识，用来修改的
        var KNo = $("#txtNo").val();
        var FKID = $("#txtDKL").val();  // 大类的编号，主要给小类使用
        var MName = $("#txtName").val();  // 大类或小类名称
        var sLoca = $("#txtLocation").val();  // 位置：第几位
        var sFType = $("#txtFType").val();  // 小类所属的大类
        var sRemark = $("#txtRemark").val();  //  
        var InMan = $("#txtInMan").val();
        var Flag = $("#txtAEFlag").val();  // 是新增还是修改

        if ((Flag == "2") || (Flag == "4"))  // 操作小类
        {
            if (KNo == "") {
                $("#div_warning").html("请输入物料分类编号，如无，可输入 / ！")
                $("#div_warning").show();
                $("#MaterTypeSaveBtn").removeAttr("disabled");
                return;
            }
            if (MName == "") {
                $("#div_warning").html("请输入物料分类名称！")
                $("#div_warning").show();
                $("#MaterTypeSaveBtn").removeAttr("disabled");
                return;
            }
            if (sLoca == "") {
                $("#div_warning").html("请选择物料编码的第几位！")
                $("#div_warning").show();
                $("#MaterTypeSaveBtn").removeAttr("disabled");
                return;
            }
        }
        else {   // 操作大类
            if (MName == "") {
                $("#div_warning").html("请输入物料分类名称！")
                $("#div_warning").show();
                $("#MaterTypeSaveBtn").removeAttr("disabled");
                return;
            }
        }

        var Data = '';
        var Params = { MTID: MTID, KNo: KNo, FKID: FKID, MName: MName, Location: sLoca, FType: sFType, Remark: sRemark, InMan: InMan, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=AddEditMaterType&CFlag=50-3",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#MaterTypeSaveBtn").removeAttr("disabled");
                    $("#divsuccess").show();
                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none'
                    $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetMaterType&CFlag=50' });
                    $('#dgDataList1').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetMaterType&CFlag=50-1&CKind=' + FKID });
                    $('#txtDelName').val("");
                    $('#txtDelNo').val("");
                    $('#txtNo').val("");
                    $('#txtName').val("");
                    $('#txtLocation').val("");
                    $('#txtRemark').val("");

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#MaterTypeSaveBtn").removeAttr("disabled");
                    $("#loginError").html("您未登陆系统，请先登录！")
                    $("#loginError").show();
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#MaterTypeSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("类别已存在，请确认")
                    $("#div_warning").show();
                }
                else {
                    $("#MaterTypeSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#MaterTypeSaveBtn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });


    //  删除物料分类
    $('#MaterType_Del_Btn').click(function() {
        $("#MaterType_Del_Btn").removeAttr("disabled");
        $("#div_warning2").hide();
        $("#divsuccess2").hide();
        $("#loginError").hide();

        var MTID = $("#txtMTID").val();  // 唯一标识
        var FKNo = $("#txtDKL").val();  // 大类的编号，主要给小类使用
        var Name = $("#txtDelName").val();
        var No = $("#txtDelNo").val();
        var Loca = $("#txtDelLoca").val();
        var InMan = $("#txtInMan").val();
        var Flag = "50-5";


        if (Name == "") {
            $("#div_warning2").html("请选择要删除的分类！")
            $("#div_warning2").show();
            $("#MaterType_Del_Btn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { MTID: MTID, Name: Name, No: No, Loca: Loca, InMan: InMan };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=DelMaterType&CFlag=" + Flag,
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#div_warning2").html("删除成功!");
                    $("#div_warning2").show();
                    $("#MaterType_Del_Btn").removeAttr("disabled");
                    document.getElementById('fade').style.display = 'none'
                    document.getElementById('Div_Del').style.display = 'none';
                    $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetMaterType&CFlag=50' });
                    $('#dgDataList1').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetMaterType&CFlag=50-1&CKind=' + FKNo });

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#div_warning2").html("您未登陆系统，请先登录！")
                    $("#div_warning2").show();
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#MaterType_Del_Btn").removeAttr("disabled");
                    $("#div_warning2").html("请选删除该分类下面的类别！")
                    $("#div_warning2").show();
                } else {
                    $("#MaterType_Del_Btn").removeAttr("disabled");
                    $("#div_warning2").html("系统出错，请重试1！")
                    $("#div_warning2").show();
                }
            },
            error: function(data) {
                $("#div_warning2").html("系统出错，请重试2！")
                $("#div_warning2").show();
            }
        });

    });



    //  保存:保存客供物料信息
    $('#CMaterSaveBtn').click(function () {
        $("#CMaterSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();

        var Kind = $("#NewEdit").html();  // 添加，修改标识
        var KNo = $("#txtMaterNo").val();
        var MName = $("#txtMaterName").val();
        var Spec = $("#txtSpec").val();
        var Stock = $("#txtStock").val();
        var Remark = $("#txtRemark").val();  //  
        var InMan = $("#txtInMan").val();
        var Flag = $("#txtAEFlag").val();  // 是新增还是修改


        if (KNo == "") {
            $("#div_warning").html("请输入物料编码")
            $("#div_warning").show();
            $("#CMaterSaveBtn").removeAttr("disabled");
            return;
        }

        if (MName == "") {
            $("#div_warning").html("请输入物料名称！")
            $("#div_warning").show();
            $("#CMaterSaveBtn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { Kind: Kind, KNo: KNo, MName: MName, Spec: Spec, Stock: Stock, Remark: Remark, InMan: InMan, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=AddEditCMater&CFlag=54-3",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#CMaterSaveBtn").removeAttr("disabled");
                    $("#divsuccess").show();
                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none'
                    $('#CMatertable').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetCustMaterInfo&CFlag=54-2' });
                    $('#txtMaterNo').val("");
                    $('#txtMaterName').val("");
                    $('#txtSpec').val("");
                    $('#txtStock').val("");
                    $('#txtRemark').val("");

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#CMaterSaveBtn").removeAttr("disabled");
                    $("#loginError").html("您未登陆系统，请先登录！")
                    $("#loginError").show();
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#CMaterSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("该客供物料已存在，请确认")
                    $("#div_warning").show();
                }
                else {
                    $("#CMaterSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function (data) {
                $("#CMaterSaveBtn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });


    //  删除客供物料
    $('#CMater_Del_Btn').click(function() {
        $("#CMater_Del_Btn").removeAttr("disabled");
        $("#div_warning2").hide();
        $("#divsuccess2").hide();
        $("#loginError").hide();

        var No = $("#txtDelNo").val();
        var Name = $("#txtDelName").val();
        var InMan = $("#txtInMan").val();
        var Flag = "54-5";


        if (Name == "") {
            $("#div_warning2").html("请选择要删除的客供物料！")
            $("#div_warning2").show();
            $("#CMater_Del_Btn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { No: No, Name: Name, InMan: InMan };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=DelCustMater&CFlag=" + Flag,
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#div_warning2").html("删除成功!");
                    $("#div_warning2").show();
                    $("#CMater_Del_Btn").removeAttr("disabled");
                    document.getElementById('fade').style.display = 'none'
                    document.getElementById('Div_Del').style.display = 'none';
                    $('#CMatertable').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetCustMaterInfo&CFlag=54-2' });

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#div_warning2").html("您未登陆系统，请先登录！")
                    $("#div_warning2").show();
                    //location.href = "Login.htm";
                } else {
                    $("#CMater_Del_Btn").removeAttr("disabled");
                    $("#div_warning2").html("系统出错，请重试1！")
                    $("#div_warning2").show();
                }
            },
            error: function(data) {
                $("#div_warning2").html("系统出错，请重试2！")
                $("#div_warning2").show();
            }
        });

    });


    //  查询 -- 客供物料
    $('#CMaterBut_open').click(function() {
        var sNo = $("#txtSNo").val();  //编码
        var sName = $("#txtSName").val()  // 名称
        var sSpec = $("#txtSSpec").val();  // 规格型号
        var sStock = $("#txtSStock").val();  // 

        var Data = '';
        var Params = { MaterNo: sNo, MaterName: sName, Spec: sSpec, Stock: sStock };
        var Data = JSON.stringify(Params);

        $('#CMatertable').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetCustMaterInfo&CFlag=54-2&Data=' + Data });

    });



    //  获取供应商编码
    $('#txtGrade').blur(function() {
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();

        var sSGo = $("#txtGrade").val();  //类别


        if (sSGo == "") {
            $("#div_warning").html("请选择厂商类别！")
            $("#div_warning").show();
            return;
        }


        var Data = '';
        var Params = { SGo: sSGo };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetSupplierNo&CFlag=41-4",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    $("#txtSupplierNo").val(parsedJson.SNO);


                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'OVER') {
                    $("#div_warning").html("当前厂商代码对应供应商流水号已超过两位，请选择其他代码！")
                    $("#div_warning").show();
                    //location.href = "Login.htm";
                } else {
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });




    //  保存:保存供应商信息
    $('#SupplierSaveBtn').click(function() {
        $("#SupplierSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();

        var Kind = $("#NewEdit").html();  // 添加，修改标识
        var sSGo = $("#txtGrade").val();  //类别
        var sNo = $("#txtSupplierNo").val();
        var sName = $("#txtSName").val();
        var sNameEn = $("#txtNameEn").val();
        var sTJ = $("#txtTJ").val();
        var sFS = $("#txtFS").val();
        var sSL = $("#txtSL").val();
        var sBZ = $("#txtBZ").val();
        var sCode = $("#txtCode").val();
        var sCMan = $("#txtCMan").val();
        var sPhone = $("#txtPhone").val();
        var sFax = $("#txtFax").val();
        var sAddr = $("#txtAddr").val();
        var sEmail = $("#txtEmail").val();
        var sNameTwo = $("#txtNameTwo").val();
        var Remark = $("#txtRemark").val();  //  
        var InMan = $("#txtInMan").val();
        var Flag = $("#txtAEFlag").val();  // 是新增还是修改


        if (sNo == "") {
            $("#div_warning").html("请确认供应商代码是否存在！")
            $("#div_warning").show();
            $("#SupplierSaveBtn").removeAttr("disabled");
            return;
        }

        if (sName == "") {
            $("#div_warning").html("请输入供应商名称！")
            $("#div_warning").show();
            $("#SupplierSaveBtn").removeAttr("disabled");
            return;
        }
        if (sNameEn == "") {
            $("#div_warning").html("请输入供应商简称！")
            $("#div_warning").show();
            $("#SupplierSaveBtn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { Kind: Kind, SGo: sSGo, No: sNo, Name: sName, NameEn: sNameEn, TJ: sTJ, FS: sFS, SL: sSL, BZ: sBZ, Code: sCode, CMan: sCMan, Phone: sPhone, Fax: sFax, Addr: sAddr, Email: sEmail, NameTwo: sNameTwo, Remark: Remark, InMan: InMan, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=AddEditSupplier&CFlag=41-3",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#SupplierSaveBtn").removeAttr("disabled");
                    $("#divsuccess").show();
                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                    $("#txtSNo").val(sNo);
                    $('#SupplierBut_open').click();
                    $("#txtSNo").val('');

                    $("#txtGrade").val('');  //类别
                    $("#txtSupplierNo").val('');
                    $("#txtSName").val('');
                    $("#txtNameEn").val('');
                    $("#txtTJ").val('');
                    $("#txtFS").val('');
                    $("#txtSL").val('');
                    $("#txtBZ").val('');
                    $("#txtCode").val('');
                    $("#txtCMan").val('');
                    $("#txtPhone").val('');
                    $("#txtFax").val('');
                    $("#txtAddr").val('');
                    $("#txtNameTwo").val('');
                    $("#txtRemark").val('');  //
                    $("#txtInMan").val('');
                    $("#txtEmail").val('');
                    $("#txtAEFlag").val('');  // 是新增还是修改

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#SupplierSaveBtn").removeAttr("disabled");
                    $("#loginError").html("您未登陆系统，请先登录！")
                    $("#loginError").show();
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#SupplierSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("该供应商编码已存在，请确认")
                    $("#div_warning").show();
                }
                else {
                    $("#SupplierSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#SupplierSaveBtn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });

    //  根据供应商编码获取供应商信息
    $('#txtSupplierNo').blur(function() {
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();
        $("#txtEn").val("");
        $("#txtSLName").html("");

        var No = $("#txtSupplierNo").val();
        var Flag = "41-99";


        if (No == "") {
            $("#div_warning").html("请输入供应商编码！")
            $("#divsuccess").show();
            return;
        }


        var Data = '';
        var Params = { No: No, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetSupplierInfoByNo&CFlag=" + Flag,
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $("#txtEn").val(sStr.SupplierEn);
                    $("#txtSLName").html(sStr.SupplierName);
                    $("#txtAddr").val(sStr.Addr);
                    $("#txtLXFS").val("电话：" + sStr.Phone + " 传真：" + sStr.Fax + " 邮箱：" + sStr.Email);
                    $("#txtCMan").val(sStr.InchangeMan);

                } else {
                    $("#txtSLName").html("获取不到该供应商，确认是否被禁用！");
                }
            },
            error: function(data) {
                $("#div_warning2").html("系统出错，请重试2！")
                $("#div_warning2").show();
            }
        });

    });


    //  保存:保存供应商验厂信息
    $('#SupplierYCSaveBtn').click(function() {
        $("#SupplierYCSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();

        var sQRNo = $("#txtQRNo").html();
        var sNo = $("#txtSupplierNo").val();
        var sNameEn = $("#txtEn").val();
        var sBJ = $("#txtbackG").val();
        var sNum = $("#txtNum").val();
        var sCoreP = $("#txtCoreProduct").val();
        var sProductT = $("#txtProductType").val();
        var sFinish = $("#txtFinish").val();
        var sPolishing = $("#txtPolishing").val();
        var sTestD = $("#txtTestDevice").val();
        var sGoniometer = $("#txtGoniometer").val();
        var sCapacity = $("#txtCapacity").val();
        var sDistribute = $("#txtDistribute").val();
        var Remark = $("#txtRemark").val();  //  
        var sTime = $("#txtTime").val();
        var InMan = $("#txtInMan").val();
        var Flag = $("#txtAEFlag").val();  // 是新增还是修改


        if (sNo == "") {
            $("#div_warning").html("请确认供应商代码是否存在！")
            $("#div_warning").show();
            $("#SupplierYCSaveBtn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { Kind: sQRNo, No: sNo, NameEn: sNameEn, TJ: sBJ, FS: sNum, SL: sCoreP, BZ: sProductT, Code: sFinish, CMan: sPolishing, Phone: sTestD, Fax: sGoniometer, Addr: sCapacity, Email: sDistribute, NameTwo: sTime, Remark: Remark, InMan: InMan, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=AddEditSupplier&CFlag=43-3",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#SupplierYCSaveBtn").removeAttr("disabled");
                    $("#divsuccess").show();
                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';
                    $('#QualityReportBut_open').click();
                    $("#txtSupplierNo").val("");
                    $("#txtEn").val("");
                    $("#txtbackG").val("");
                    $("#txtNum").val("");
                    $("#txtCoreProduct").val("");
                    $("#txtProductType").val("");
                    $("#txtFinish").val("");
                    $("#txtPolishing").val("");
                    $("#txtTestDevice").val("");
                    $("#txtGoniometer").val("");
                    $("#txtCapacity").val("");
                    $("#txtDistribute").val("");
                    $("#txtRemark").val("");
                    $("#txtInMan").val("");
                    $("#txtAEFlag").val(""); // 是新增还是修改

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#SupplierYCSaveBtn").removeAttr("disabled");
                    $("#loginError").html("您未登陆系统，请先登录！")
                    $("#loginError").show();
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#SupplierYCSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("该供应商编码已存在，请确认")
                    $("#div_warning").show();
                }
                else {
                    $("#SupplierYCSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#SupplierYCSaveBtn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });


    //  根据物料编码获取物料信息
    $('#txtFNo').blur(function() {
        $("#div_warning").hide();
        $("#divsuccess").hide();

        var sNo = $("#txtFNo").val();
        var Flag = "33-99";

        if (sNo == "") {
            $("#div_warning").html("请输入物料编码！")
            $("#divsuccess").show();
            return;
        }

        var Data = '';
        var Params = { No: sNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetMaterInfoByNo&CFlag=" + Flag,
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                $("#txtFName").val("");
                $("#txtModel").val("");
                $("#txtTechNo").val("");
                $("#txtPModel").val("");

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $("#txtFName").val(sStr.MaterName);
                    $("#txtModel").val(sStr.MaterSpec);
                    $("#txtTechNo").val(sStr.TechNo);
                    $("#txtPModel").val(sStr.MaterSpec);
                }

                if ($("#txtFName").val()=="") {
                    $("#div_warning").html("无物料信息，请到物料信息添加。")
                    $("#div_warning").show();
                }

            },
            error: function(data) {
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });


    //  根据物料编码获取物料信息
    $('#txtCMaterNo').blur(function() {
        $("#div_warning").hide();

        var sNo = $("#txtCMaterNo").val();
        var Flag = "33-99";

        if (sNo == "") {
            $("#div_warning").html("请输入物料编码！")
            $("#div_warning").show();
            return;
        }

        var Data = '';
        var Params = { No: sNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetMaterInfoByNo&CFlag=" + Flag,
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                $("#txtCMaterName").val("");
                $("#txtModel").val("");
                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $("#txtCMaterName").val(sStr.MaterName);
                    $("#txtModel").val(sStr.MaterSpec);
                }

                if ($("#txtCMaterName").val()=="") {
                    $("#div_warning").html("无物料信息，请到物料信息添加。")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });


    $('#txtCNo').blur(function() {
        $("#div_warning").hide();
        $("#divsuccess").hide();

        var sNo = $("#txtCNo").val();
        var Flag = "33-99";

        if (sNo == "") {
            $("#div_warning").html("请输入物料编码！")
            $("#divsuccess").show();
            return;
        }

        var Data = '';
        var Params = { No: sNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetMaterInfoByNo&CFlag=" + Flag,
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                $("#txtCName").val("");
                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $("#txtCName").val(sStr.MaterName);
                }

                if ($("#txtCName").val() == "") {
                    $("#div_warning").html("无物料信息，请到物料信息添加。")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });

    });

    $('#txtNewFNo').blur(function() {
        $("#div_warning").hide();

        var sNo = $("#txtNewFNo").val();
        var Flag = "33-99";

        if (sNo == "") {
            $("#div_warning").html("请输入物料编码！")
            $("#div_warning").show();
            return;
        }

        var Data = '';
        var Params = { No: sNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetMaterInfoByNo&CFlag=" + Flag,
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $("#txtNewFName").val(sStr.MaterName);
                }
                else {
                    $("#txtNewFName").val("");
                    $("#div_warning").html("该物料不存在！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });

    });




    //  保存:保存BOM信息
    $('#BOMSaveBtn').click(function() {
        $("#BOMSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();

        var sFNo = $("#txtFNo").val();
        var sFName = $("#txtFName").val();
        var sModel = $("#txtModel").val();
        var sTech = $("#txtTechNo").val();
        var sCNo = $("#txtCNo").val();
        var sCName = $("#txtCName").val();
        var sNum = $("#txtNum").val();
        var sGX = $("#txtGX").val();
        var sNewFNo = $("#txtNewFNo").val();
        var sNewFName = $("#txtNewFName").val();
        var Remark = $("#txtRemark").val();  //  
        var InMan = $("#txtInMan").val();
        var Flag = $("#txtAEFlag").val();  // 是新增还是修改
        var sPack = "";
        var sXu = "";
        var sZSFlag = "";  // 追溯属性：按 序列号，批次,物料
        var sClassFlag = "";   // 是否典型产品


        if (sFNo == "") {
            ErrorMessage("请输入父零件编码！",2000)
            $("#BOMSaveBtn").removeAttr("disabled");
            return;
        }
        if (sFName == "") {
            ErrorMessage("获取不到父零件名称，光标放在父编码内再离开！", 2000)
            $("#BOMSaveBtn").removeAttr("disabled");
            return;
        }

        if (sCNo == "") {
            ErrorMessage("请输入子零件编码！", 2000)
            $("#BOMSaveBtn").removeAttr("disabled");
            return;
        }
        if (sCName == "") {
            ErrorMessage("获取不到子零件名称，光标放在子编码内再离开！", 2000)
            $("#BOMSaveBtn").removeAttr("disabled");
            return;
        }

        if ((sGX == "") || (sGX == "null") || (sGX == null)) {
            ErrorMessage("请选择工序！", 2000)
            $("#BOMSaveBtn").removeAttr("disabled");
            return;
        }

        if ($("#txtSerialC").is(':checked')) {
            sZSFlag = "序列号";
        }
        if ($("#txtBatchC").is(':checked')) {
            sZSFlag = "批次";
        }
        if ($("#txtMaterC").is(':checked')) {
            sZSFlag = "物料";
        }

        //        if (sZSFlag == "") {
        //            $("#div_warning").html("请选择追溯方式！") txtCxu
        //            $("#div_warning").show();
        //            $("#BOMSaveBtn").removeAttr("disabled");
        //            return;
        //        }

        if ($("#txtCPack").is(':checked')) {
            sPack = "是";
        }
        if ($("#txtCxu").is(':checked')) {
            sXu = "是";
        }

        //if ((sPack == "是") && (sZSFlag != "不管控")) {  // 如果勾选是装箱件，则不能勾选追溯属性了
        //    $("#div_warning").html("已选择为装箱件，不需要再勾选批序管控，可勾选不管控！")
        //    $("#div_warning").show();
        //    $("#BOMSaveBtn").removeAttr("disabled");
        //    return;
        //}

        if ($("#cboNewFNo").is(':checked')) {
            if (sNewFNo == "") {
                ErrorMessage("请输入新的父零件编码！", 2000)
                $("#BOMSaveBtn").removeAttr("disabled");
                return;
            }
            if (sNewFName == "") {
                ErrorMessage("获取不到新的父零件名称！", 2000)
                $("#BOMSaveBtn").removeAttr("disabled");
                return;
            }
        }
        else {
            var sNewFNo = sFNo;
            var sNewFName = sFName;
        }

        if ($("#CH_ClassFlag").is(':checked')) {
            sClassFlag = "是";
        }
        else {
            sClassFlag = "否";
        }


        var Data = '';
        var Params = { FNo: sFNo, FName: sFName, CNo: sCNo, CName: sCName, Num: sNum, GX: sGX, Pack: sPack, NewFNo: sNewFNo, NewFName: sNewFName, A: sZSFlag, B: sXu, C: sTech, D: sModel, E: sClassFlag, F: ModuleName, Remark: Remark, InMan: InMan, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=AddEditBOM&CFlag=44-3",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#BOMSaveBtn").removeAttr("disabled");
                    $("#divsuccess").show();

                    closeDialog()

                    layer.msg("提交成功！")

                    $('#BOMBut_open').click();


                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#BOMSaveBtn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#BOMSaveBtn").removeAttr("disabled");
                    $("#txtCNo").val("");
                    $("#txtCName").val("");

                    ErrorMessage("该父零件下已有该子零件，请确认!", 2000)

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTDX') {
                    $("#BOMSaveBtn").removeAttr("disabled");
                    ErrorMessage("该产品代号已设置有典型产品，请确认!", 2000)
                }
                else {
                    $("#BOMSaveBtn").removeAttr("disabled");
                    ErrorMessage("系统出错，请重试1！", 2000)
                }
            },
            error: function(data) {
                $("#BOMSaveBtn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });


    //  保存:保存替代料信息
    $('#ReplaceSaveBtn').click(function () {
        $("#ReplaceSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning2").hide();

        var sFNo = $("#txtRFNo").val();
        var sCNo = $("#txtRCMaterNo").val();
        var sCName = $("#txtRCMaterName").val();
        var sPMaterNo = $("#txtPtRCMaterNo").val()
        var Flag = $("#txtAEFlag").val();  //  4 保存追溯属性   5 删除



        if (sFNo == "") {
            ErrorMessage("请获取不到父零件编码，请重新登录！",2000)
            $("#ReplaceSaveBtn").removeAttr("disabled");
            return;
        }
        if (sCNo == "") {
            ErrorMessage("请输出替代料编码！",2000)
            $("#ReplaceSaveBtn").removeAttr("disabled");
            return;
        }

        if (sCName == "") {
            ErrorMessage("替代料编码获取不到物料信息！",2000)
            $("#ReplaceSaveBtn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { FNo: sFNo, FName: "", CNo: sCNo, CName: sCName, Num: "", GX: "", Pack: "", NewFNo: "", NewFName: "", A: sPMaterNo, B: "", C: "", E: "", F: ModuleName, Remark: "", InMan: "", Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=AddEditBOM&CFlag=44-3",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#ReplaceSaveBtn").removeAttr("disabled");

                    closeDialog()

                    layer.msg("提交成功！")
  
                    $('#BOMBut_open').click();

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#ReplaceSaveBtn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！",2000)
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#ReplaceSaveBtn").removeAttr("disabled");
                    ErrorMessage("该替代料已维护，请确认!",2000)
                }
                else {
                    $("#ReplaceSaveBtn").removeAttr("disabled");
                    ErrorMessage("系统出错，请重试1！",2000)
                }
            },
            error: function (data) {
                $("#ReplaceSaveBtn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！",2000)
            }
        });
    });


    //  判断客户编码是否存在
    $('#txtCustNo').blur(function() {
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();

        var sNo = $("#txtCustNo").val();  //类别


        if (sNo == "") {
            $("#div_warning").html("请输入客户编码！")
            $("#div_warning").show();
            return;
        }


        var Data = '';
        var Params = { No: sNo };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetCustNo&CFlag=40-4",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    //var DataControl = new FConfigInfo();

                    $("#CustSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("该供客户已存在，请确认")
                    $("#div_warning").show();

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#loginError").html("")
                    $("#loginError").hide();
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#loginError").html("您未登陆系统，请先登录！")
                    $("#loginError").show();
                    //location.href = "Login.htm";
                } else {
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });


    //  保存:保存客户信息
    $('#CustSaveBtn').click(function() {
        $("#CustSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();

        var Kind = $("#NewEdit").html();  // 添加，修改标识
        var sNo = $("#txtCustNo").val();
        var sName = $("#txtCName").val();
        var sNameEn = $("#txtNameEn").val();
        var sTJ = $("#txtTJ").val();
        var sFS = $("#txtFS").val();
        var sSL = $("#txtSL").val();
        var sBZ = $("#txtBZ").val();
        var sCode = $("#txtCode").val();
        var sCMan = $("#txtCMan").val();
        var sPhone = $("#txtPhone").val();
        var sFax = $("#txtFax").val();
        var sAddr = $("#txtAddr").val();
        var sEmail = $("#txtEmail").val();
        var Remark = $("#txtRemark").val();  //  
        var InMan = $("#txtInMan").val();
        var Flag = $("#txtAEFlag").val();  // 是新增还是修改


        if (sNo == "") {
            $("#div_warning").html("请输入客户编码！")
            $("#div_warning").show();
            $("#CustSaveBtn").removeAttr("disabled");
            return;
        }

        if (sName == "") {
            $("#div_warning").html("请输入客户商名称！")
            $("#div_warning").show();
            $("#CustSaveBtn").removeAttr("disabled");
            return;
        }
        if (sNameEn == "") {
            $("#div_warning").html("请输入客户简称！")
            $("#div_warning").show();
            $("#CustSaveBtn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { Kind: Kind, No: sNo, Name: sName, NameEn: sNameEn, TJ: sTJ, FS: sFS, SL: sSL, BZ: sBZ, Code: sCode, CMan: sCMan, Phone: sPhone, Fax: sFax, Addr: sAddr, Email: sEmail, Remark: Remark, InMan: InMan, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=AddEditCust&CFlag=40-3",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#CustSaveBtn").removeAttr("disabled");
                    $("#divsuccess").show();
                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';
                    $("#txtSNo").val(sNo);
                    $('#CustBut_open').click();
                    $("#txtSNo").val('');

                    $("#txtCustNo").val('');
                    $("#txtCName").val('');
                    $("#txtNameEn").val('');
                    $("#txtTJ").val('');
                    $("#txtFS").val('');
                    $("#txtSL").val('');
                    $("#txtBZ").val('');
                    $("#txtCode").val('');
                    $("#txtCMan").val('');
                    $("#txtPhone").val('');
                    $("#txtFax").val('');
                    $("#txtAddr").val('');
                    $("#txtRemark").val('');  //
                    $("#txtInMan").val('');
                    $("#txtEmail").val('');
                    $("#txtAEFlag").val();  // 是新增还是修改

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#CustSaveBtn").removeAttr("disabled");
                    $("#loginError").html("您未登陆系统，请先登录！")
                    $("#loginError").show();
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#CustSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("该供客户已存在，请确认")
                    $("#div_warning").show();
                }
                else {
                    $("#CustSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#CustSaveBtn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });



    //  保存:保存物料信息
    $('#MaterSaveBtn').click(function() {
        $("#MaterSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();

         //  没有用到的变量 // pt  hd
        var jj = ""; var dm = ""; var pt = ""; var hd = ""; var gq = ""; var cl = ""; var cc = ""; var xn = ""; var mtf = ""; var PN = ""; var Ft = ""; var Kd = ""; var jk = ""; var Bt = "";
        var rs = ""; var qx = ""; var tx = ""; var bl = ""; var na = ""; var cg = ""; var sm = ""; var xz = ""; var gb = ""; var fov = ""; var zj = ""; var gz = ""; var lx = ""; var bs = "";
        var date = ""; var wllb = "";

        var F = $("#txtAddKind").val();  // 添加，修改标识
        var No = $('#txtMaterNo').val();
        var Name = $('#txtMaterName').val();
        var Spec = $('#txtMaterSpec').val();
        var Un = $('#txtUnit').val(); //
        wllb = $('#txtKind').val();  // 物料类别，
        Bt = $('#txtBigType').val(); //  大类：光学元件，镜头，UDI产品。。。
        Ft = $('#txtFType').val(); //  分类
        lx = $('#txtMaterType').val(); //    类型(Type)
        cl = $('#txtABCKind option:selected').text(); //选中的文本  //  材料(Material)  $('#txtABCKind').val();   获取value 的值
        cc = $('#txtMaterXH').val(); //  尺寸(Size)
        xz = $('#txtTUnit').val(); //  形状(Shape)
        na = $('#txtLOSHFlag').val(); //  NA(F#)
        fov = $('#txtHUnit').val(); //   FOV
        cg = $('#txtStockPlace').val(); //  传感器格式
        gq = $('#txtStockLacation').val(); //  光圈
        zj = $('#txtInspectMg').val(); //  光束直径
        tx = $('#txtEfftMg').val(); //  图像大小
        jk = $('#txtMount').val(); //  接口
        mtf = $('#txtTechNo').val(); //    MTF
        qx = $('#txtDistortion').val(); //  畸变
        gb = $('#txtLWUnit').val(); //  光斑尺寸
        bl = $('#txtKaifengMg').val(); //  倍率
        bs = $('#txtBatchMg').val(); //  放大倍数
        xn = $('#txtSpecialProtection').val(); // 性能
        jj = $('#txtSamplPlan option:selected').text(); //   230831：抽样方案名称
        pt = $('#txtSamplPlan').val(); //    230831：抽样方案编号
        sm = $('#txtBatchNo').val(); //  扫描角度

        // 通用的
        spl = $('#txtZone').val(); //  销售区域
        prd = $('#txtCPX').val(); //  产品线
        jz = $('#txtJZ').val(); //  净重
        mz = $('#txtMZ').val(); //  毛重
        gl = $('#txtGL').val(); //  功率
        pldy = $('#txtPLDY').val(); //  频率与电压
        tz = $('#txtTZ').val(); //  体质
        C1 = $('#txtJSNo').val(); //  (3)	产品技术要求编号
        C2 = $('#txtPartName').val(); //  (4)	部件名称
        C3 = $('#txtPartSpec').val(); //  (5)	部件型号
        C4 = $('#txtUseLDate').val(); //  (6)	使用期限
        C5 = $('#txtCMIITID').val(); //  (7)	CMIIT ID
        C6 = $('#txtCSize').val(); //  (8)	尺寸 
        C7 = $('#txtNote').val(); // (9)	使用方法及注意事项
        C8 = $('#txtSYJJ').val(); //  (10)	适应症及禁忌症 
        C9 = $('#txtStore').val(); //  (11)	储存方法
        C10 = $('#txtTransp').val(); // (12)	运输条件

        var rmk = $('#txtRemark').val();  

        PN = $('#txtPDName').val();   //  产品名称
        cc = $('#txtSPName').val();   //  商品名称 MaterXH
        jk = $('#txtBLCP').val();   //  包类产品 Mount
        mtf = $('#txtYMLDM').val();   //  原目录代码 TechNo
        lx = $('#txtQXLB').val();   //  器械类别 MaterType
        Ft = $('#txtFLBM').val();   //  分类编码 FillMaterKind
        gz = $('#txtZCRCN').val();   //  注册备案人 MHeight
        rs = $('#txtZCREN').val();   //  注册备案人 Diameter
        qx = $('#txtZCBH').val();   //  注册备案编号  Distortion
        Kd = $('#txtCPLB').val();   // 产品类别 MaterKind
        gq = $('#txtAQXG').val();   //  安全相关信息  StockLacation
        tx = $('#txtYCX').val();   //  一次性使用 EfftMg
        bs = $('#txtCFSYCS').val();   //  重复使用次数  BatchMg
        bl = $('#txtWJBZ').val();   //  无菌包装 KaifengMg
        na = $('#txtSYQMJ').val();   //  使用前灭菌  LOSHFlag
        dm = $('#txtMJFS').val();   //  灭菌方式  Coating
        cg = $('#txtQTXX').val();   //  其他信息链接  StockPlace
        sm = $('#txtYBBM').val();   // 医保编码 BatchNo
        date = $('#txtTSRQ').val();   //  退市日期  LastOutDate
        xz = $('#txtPIPH').val();   //  PI 批号  Tunit
        gb = $('#txtPIXLH').val();   //  PI 序列号 LWUnit
        fov = $('#txtPISCRQ').val();   //  PI 生产日期 Hunit
        zj = $('#txtPISXRQ').val();   //  PI 失效日期  InspectMg

        if (No == "") {
            ErrorMessage("请输入物料编码！",2000)
            $("#MaterSaveBtn").removeAttr("disabled");
            return;
        }

        if (Name == "") {
            ErrorMessage("请输入物料名称！",2000)
            $("#MaterSaveBtn").removeAttr("disabled");
            return;
        }

        if (C4 == "") {
            C4 = "0";
        }
        else if (isNaN(C4)) {
            ErrorMessage("使用期限(月)：请填写数字！",2000)
            $("#MaterSaveBtn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { No: No, Name: Name, PN: PN, Spec: Spec, wllb: wllb, Kd: Kd, Un: Un, Bt: Bt, Ft: Ft, lx: lx, cl: cl, cc: cc, xz: xz, na: na, fov: fov, cg: cg, gq: gq, jj: jj, dm: dm, pt: pt, hd: hd, zj: zj, tx: tx, jk: jk, mtf: mtf,
            qx: qx, gb: gb, bl: bl, bs: bs, xn: xn, gz: gz, rs: rs, sm: sm, date: date, spl: spl, prd: prd, jz: jz, mz: mz, gl: gl, pldy: pldy, tz: tz, C1: C1, C2: C2, C3: C3, C4: C4, C5: C5, C6: C6, C7: C7, C8: C8, C9: C9, C10: C10, rmk: rmk, Flag: F
        };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=AddEditMater&CFlag=51",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#MaterSaveBtn").removeAttr("disabled");
                    $("#divsuccess").show();

                    closeDialog()

                    $("#txtSNo").val(No);
                    $('#MaterBut_open').click();
                    $("#txtSNo").val('');

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#MaterSaveBtn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！",2000)
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#MaterSaveBtn").removeAttr("disabled");
                    ErrorMessage("该编码已存在，请确认",2000)
                }
                else {
                    $("#MaterSaveBtn").removeAttr("disabled");
                    ErrorMessage("系统出错，请重试1！",2000)
                }
            },
            error: function(data) {
                $("#MaterSaveBtn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！",2000)
            }
        });
    });




    //  查询 -- 厂商识别码建档
    $('#MCodeBut_open').click(function() {
        var sNo = $("#txtSNo").val();  //编码
        var sCenter = $("#txtSCenter").val()  // 名称
        var sBDate = $("#txtSBDate").val();  // 颁布日期
        var sEDate = $("#txtSEDate").val();  // 颁布日期
        var sBEDate = $("#txtSBEDate").val();  // 有效期
        var sEEDate = $("#txtSEEDate").val();  // 有效期


        var Data = '';
        var Params = { MCode: sNo, CodeCenter: sCenter, BDate: sBDate, EDate: sEDate, BEDate: sBEDate, EEDate: sEEDate };
        var Data = JSON.stringify(Params);

        $('#MCodetable').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetMCodetInfo&CFlag=20&Data=' + Data });

    });




    //  保存:保存厂商识别码
    $('#MCodeSaveBtn').click(function() {
        $("#MCodeSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();

        var sF = "0";
        var sLNum = "0";
        var Kind = $("#NewEdit").html();  // 添加，修改标识
        var sMCode = $("#txtMCode").val();
        var sMCodeType = $("#txtMCodeType").val();
        var sCenter = $("#txtCodeCenter").val();
        var sNowNum = $("#txtNowNum").val();  // 流水号当前值
        var sPDate = $("#txtPublishDate").val();
        var sEDate = $("#txtExpiryDate").val();
        var sInCharge = $("#txtInCharge").val();
        var Remark = $("#txtRemark").val();  //  
        var Flag = $("#txtAEFlag").val();  // 是新增还是修改,删除


        if (sCenter == "") {
            $("#div_warning").html("请输入编码中心！")
            $("#div_warning").show();
            $("#MCodeSaveBtn").removeAttr("disabled");
            return;
        }

        if (sMCode == "") {
            $("#div_warning").html("请输入厂商识别码");
            $("#div_warning").show();
            $("#MCodeSaveBtn").removeAttr("disabled");
            return;
        }

        //厂商识别码GS1的规则 ：前缀690-691，厂商识别码为前7位；前缀692-696，厂商识别码为前8位；前缀697，厂商识别码为前9位；前缀6901028，厂商识别码为前10位；前缀698-699，尚未分配
        if (sCenter == "GS1") {
            if (sMCodeType == "") {
                $("#div_warning").html("请选择厂商识别码类别");
                $("#div_warning").show();
                $("#MCodeSaveBtn").removeAttr("disabled");
                return;
            }

            if ((sMCodeType == "690") || (sMCodeType == "691")) {
                if (sMCode.substr(0, 3) != sMCodeType) {
                    sF = "2";
                }
                if (sMCode.length != 7) {
                    sF = "1";
                }
                sLNum = "5";  // 流水号
            }
            if ((sMCodeType == "692") || (sMCodeType == "693") || (sMCodeType == "694") || (sMCodeType == "695") || (sMCodeType == "696")) {
                if (sMCode.substr(0, 3) != sMCodeType) {
                    sF = "2";
                }

                if (sMCode.length != 8) {
                    sF = "1";
                }
                sLNum = "4";  // 流水号
            }
            if (sMCodeType == "697") {
                if (sMCode.substr(0, 3) != sMCodeType) {
                    sF = "2";
                }

                if (sMCode.length != 9) {
                    sF = "1";
                }
                sLNum = "3";  // 流水号
            }
            if (sMCodeType == "6901028") {
                if (sMCode.substr(0, 7) != sMCodeType) {
                    sF = "2";
                }

                if (sMCode.length != 10) {
                    sF = "1";
                }
                sLNum = "2";  // 流水号
            }

            if (sF == "1") {
                $("#div_warning").html("厂商识别码位数不正确");
                $("#div_warning").show();
                $("#MCodeSaveBtn").removeAttr("disabled");
                return;
            }
            if (sF == "2") {
                $("#div_warning").html("厂商识别码前几位不正确");
                $("#div_warning").show();
                $("#MCodeSaveBtn").removeAttr("disabled");
                return;
            }

            if (sNowNum.length > parseInt(sLNum)) {
                $("#div_warning").html("当前流水号值位数过大，请确认。");
                $("#div_warning").show();
                $("#MCodeSaveBtn").removeAttr("disabled");
                return;
            }

        }

        if (sNowNum == "") {
            $("#div_warning").html("请填写流水号当前值！")
            $("#div_warning").show();
            $("#MCodeSaveBtn").removeAttr("disabled");
            return;
        }
        if (isNaN(sNowNum)) {
            $("#div_warning").html("流水号当前值为整数！")
            $("#div_warning").show();
            $("#MCodeSaveBtn").removeAttr("disabled");
            return;
        }

        if (sPDate > sEDate) {
            $("#div_warning").html("发布日期不能大于有效日期！")
            $("#div_warning").show();
            $("#MCodeSaveBtn").removeAttr("disabled");
            return;
        }



        var Data = '';
        var Params = { MCode: sMCode, Center: sCenter, MCodeType: sMCodeType, NowNum: sNowNum, LNum: sLNum, PDate: sPDate, EDate: sEDate, InCharge: sInCharge, Remark: Remark, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=OPMCodetInfo&CFlag=20",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#MCodeSaveBtn").removeAttr("disabled");
                    $("#divsuccess").show();
                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none'
                    //$('#MCodetable').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetMCodetInfo&CFlag=20&Data=' + Data });
                    $('#MCodeBut_open').click();
                    $('#txtMCode').val("");
                    $('#txtCodeCenter').val("");
                    $('#txtPublishDate').val("");
                    $('#txtExpiryDate').val("");
                    $('#txtInCharge').val("");
                    $('#txtRemark').val("");

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#MCodeSaveBtn").removeAttr("disabled");
                    $("#loginError").html("您未登陆系统，请先登录！")
                    $("#loginError").show();
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#MCodeSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("该厂商识别码已存在，请确认")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_NOWUSE') {
                    $("#MCodeSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("已存在一个启用的厂商识别码，不能新增！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_DELETE') {
                    $("#MCodeSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("该厂商识别码已被使用，不能删除！")
                    $("#div_warning").show();
                } else {
                    $("#MCodeSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#MCodeSaveBtn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });


    //  删除:删除厂商识别码
    $('#MCode_Del_Btn').click(function() {
        $("#MCode_Del_Btn").attr({ "disabled": "disabled" });
        $("#div_warning2").hide();
        $("#divsuccess").hide();

        var sMCode = $("#txtDelNo").val();
        var Flag = $("#txtAEFlag").val();  // 是新增还是修改,删除


        if (sMCode == "") {
            $("#div_warning2").html("请输入厂商识别码");
            $("#div_warning2").show();
            $("#MCode_Del_Btn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { MCode: sMCode, Center: "", PDate: "", EDate: "", InCharge: "", Remark: "", Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=OPMCodetInfo&CFlag=20",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#MCode_Del_Btn").removeAttr("disabled");
                    document.getElementById('Div_Del').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';
                    $('#MCodetable').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetMCodetInfo&CFlag=20' });

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#MCode_Del_Btn").removeAttr("disabled");
                    $("#div_warning2").html("您未登陆系统，请先登录！")
                    $("#div_warning2").show();
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_DELETE') {
                    $("#MCode_Del_Btn").removeAttr("disabled");
                    $("#div_warning2").html("该厂商识别码已被使用，不能删除！")
                    $("#div_warning2").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_QY') {
                    $("#MCode_Del_Btn").removeAttr("disabled");
                    $("#div_warning2").html("已存在一个启用的厂商识别码，请确认")
                    $("#div_warning2").show();
                }
                else {
                    $("#MCode_Del_Btn").removeAttr("disabled");
                    $("#div_warning2").html("系统出错，请重试1！")
                    $("#div_warning2").show();
                }
            },
            error: function(data) {
                $("#MCode_Del_Btn").removeAttr("disabled");
                $("#div_warning2").html("系统出错，请重试2！")
                $("#div_warning2").show();
            }
        });
    });



    //  保存:保存序列号物料-序列号规则
    $('#SerielMaterSaveBtn').click(function () {
        $("#SerielMaterSaveBtn").attr({ "disabled": "disabled" });

        var sS1 = ""; var sS2 = ""; var sS3 = ""; var sS4 = ""; var sS5 = ""; var sS6 = ""; var sS7 = "";
        var sMNo = $("#txtFNo").val();
        var sMName = $("#txtFName").val();
        var sModel = $("#txtModel").val();
        var sOB = "否";  // 默认不是按工单批次发放序列号

        if ($("#Select1").val() == "固定值") {
            sS1 = $("#txtSectionOne").val();
        }
        else {
            sS1 = $("#txtSectionOneL").val();
        }
        if ($("#Select2").val() == "固定值") {
            sS2 = $("#txtSectionTwo").val();
        }
        else {
            sS2 = $("#txtSectionTwoL").val();
        }
        if ($("#Select3").val() == "固定值") {
            sS3 = $("#txtSectionThr").val();
        }
        else {
            sS3 = $("#txtSectionThrL").val();
        }
        if ($("#Select4").val() == "固定值") {
            sS4 = $("#txtSectionFour").val();
        }
        else {
            sS4 = $("#txtSectionFourL").val();
        }
        if ($("#Select5").val() == "固定值") {
            sS5 = $("#txtSectionFive").val();
        }
        else {
            sS5 = $("#txtSectionFiveL").val();
        }
        if ($("#Select6").val() == "固定值") {
            sS6 = $("#txtSectionSix").val();
        }
        else {
            sS6 = $("#txtSectionSixL").val();
        }
        if ($("#Select7").val() == "固定值") {
            sS7 = $("#txtSectionSev").val();
        }
        else {
            sS7 = $("#txtSectionSevL").val();
        }
        var sIValue = $("#txtInitialValue").val();
        var sNValue = $("#txtNowValue").val();
        var sBNum = $("#txtBatchNum").val();
        var sRemark = $("#txtRemark").val();
        var sFlag = $("#txtAEFlag").val();  // 是新增还是修改


        if (sMNo == "") {
            ErrorMessage("请输入物料编码！", 2000)
            $("#SerielMaterSaveBtn").removeAttr("disabled");
            return;
        }

        if (sMName == "") {
            ErrorMessage("请输入物料名称！", 2000)
            $("#SerielMaterSaveBtn").removeAttr("disabled");
            return;
        }

        if ($('#txtCBOrder').is(':checked')) {
            sOB = "是";
        }



        var Data = '';
        var Params = { MNo: sMNo, MName: sMName, Model: sModel, S1: sS1, S2: sS2, S3: sS3, S4: sS4, S5: sS5, S6: sS6, S7: sS7, IValue: sIValue, NValue: sNValue, UseFlag: sOB, BNum: sBNum, Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=OPSerielMater&CFlag=21",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#SerielMaterSaveBtn").removeAttr("disabled");

                    $("#ShowTow").css("display", "none")
                    $("#ShowTow-fade").css("display", "none")

                    $("#txtSMNo").val(sMNo);
                    $('#SerielMaterBut_open').click();
                    $("#txtSMNo").val('');

                    $("#txtFNo").val("");
                    $("#txtFName").val("");
                    $("#txtSectionOne").val("");
                    $("#txtSectionTwo").val("");
                    $("#txtSectionThr").val("");
                    $("#txtSectionFour").val("");
                    $("#txtSectionFive").val("");
                    $("#txtSectionSix").val("");
                    $("#txtSectionSev").val("");
                    $("#txtInitialValue").val("");
                    $("#txtNowValue").val("");
                    $("#txtRemark").val("");
                    $("#txtAEFlag").val('');  // 是新增还是修改

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#SerielMaterSaveBtn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#SerielMaterSaveBtn").removeAttr("disabled");
                    ErrorMessage("该序列号物料已存在，请确认！", 2000)
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTGZ') {
                    $("#SerielMaterSaveBtn").removeAttr("disabled");
                    ErrorMessage("已存在该序列号规则，请确认！", 2000)
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_DELETE') {
                    $("#SerielMaterSaveBtn").removeAttr("disabled");
                    ErrorMessage("该序列号物料已被使用，不能删除！", 2000)
                }
                else {
                    $("#SerielMaterSaveBtn").removeAttr("disabled");
                    ErrorMessage("系统出错，请检查该产品序列号规则是否存在！", 2000)
                }
            },
            error: function (data) {
                $("#SerielMaterSaveBtn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });


    //  根据发号物料编码获取物料信息
    $('#txtPMaterNo').blur(function() {
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $('#sFMsg').html("");

        var sNo = $("#txtPMaterNo").val();
        var Flag = "26-99";

        if (sNo == "") {
            $("#div_warning").html("请输入物料编码！")
            $("#divsuccess").show();
            return;
        }

        var Data = '';
        var Params = { No: sNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetMaterInfoByNo&CFlag=" + Flag,
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                $("#txtPMaterName").val("");
                $("#txtModel").val("");
                $("#txtBNum").val("");
                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $("#txtPMaterName").val(sStr.MaterName);
                    $("#txtModel").val(sStr.Model);
                    $("#txtBNum").val(sStr.BatchNum);
                }
                else if (parsedJson != undefined && parsedJson != '') {
                    $("#sFMsg").html(parsedJson.Msg);
                }
            },
            error: function(data) {
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });


    //  保存:保存公司基本信息
    $('#CompanySaveBtn').click(function () {
        $("#CompanySaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();

        var S1 = $("#txtCompID").val();
        var S2 = $("#txtCompName").val();
        var S3 = $("#txtNameJC").val();
        var S4 = $("#txtCompNameEN").val();
        var S5 = $("#txtLegalMan").val();
        var S6 = $("#txtOrganizationCode").val();
        var S7 = $("#txtCategory").val();
        var S8 = $("#txtBusinessScope").val();
        var S9 = $("#txtRegAmount").val();
        var S10 = $("#txtTaxNumber").val();
        var S11 = $("#txtRegAddr").val();
        var S12 = $("#txtCompAddr").val();
        var S13 = $("#txtPAddr").val();
        var S14 = $("#txtPostalCode").val();
        var S15 = $("#txtCMan").val();
        var S16 = $("#txtPhone").val();
        var S17 = $("#txtFax").val();
        var S18 = $("#txtEMail").val();
        var S19 = $("#txtWebsite").val();
        var S20 = $("#txtAPPID").val();
        var S21 = $("#txtAPPSECRET").val();
        var S22 = $("#txtPrdXKZ").val();
        var sRemark = $("#txtRemark").val();
        var sFlag = $("#txtAEFlag").val();  // 是新增还是修改


        if (S1 == "") {
            ErrorMessage("请输入公司代码！", 2000)
            $("#CompanySaveBtn").removeAttr("disabled");
            return;
        }

        if (S2 == "") {
            ErrorMessage("请输入公司名称！", 2000)
            $("#CompanySaveBtn").removeAttr("disabled");
            return;
        }

        if (S3 == "") {
            ErrorMessage("请输入公司简称！", 2000)
            $("#CompanySaveBtn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { CompID: S1, CName: S2, CJC: S3, CEN: S4, LMan: S5, Code: S6, Category: S7, BScope: S8, RAmount: S9, Number: S10, RAddr: S11, CAddr: S12, PAddr: S13, PCode: S14, CMan: S15, Phone: S16, Fax: S17, EMail: S18, Website: S19, APPID: S20, APPSECRET: S21, XKZ: S22, Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=OPCompanyInfo&CFlag=21",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#CompanySaveBtn").removeAttr("disabled");
                    $("#divsuccess").show();

                    closeDialog()

                    $("#txtSCName").val(S2);
                    $('#CompanyBut_open').click();
                    $("#txtSCName").val('');

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#CompanySaveBtn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#CompanySaveBtn").removeAttr("disabled");
                    ErrorMessage("该公司ID已存在，请确认", 2000)
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_DELETE') {
                    $("#CompanySaveBtn").removeAttr("disabled");
                    ErrorMessage("该公司信息已被使用，不能删除！", 2000)
                }
                else {
                    $("#CompanySaveBtn").removeAttr("disabled");
                    ErrorMessage("系统出错，请重试1！", 2000)
                }
            },
            error: function (data) {
                $("#CompanySaveBtn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });



    // 产品对应标贴界面： 选择标贴点击确定
    $('#PChoogeLabel_Btn').click(function() {
        $("#div_warning2").hide();
        $("#divsuccess").hide();

        var sNo = $("#txtTPNo").val();

        if (sNo == "") {
            $("#div_warning2").html("请选择标贴模板！")
            $("#div_warning2").show();
            return;
        }
        document.getElementById('Div_Chooge').style.display = 'none';

    });



    //  保存:保存产品对应的标贴编码及模板
    $('#PLabelSaveBtn').click(function() {
        $("#PLabelSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();


        var sFMNo = $("#txtFNo").val();
        var sFMName = $("#txtFName").val();
        var sModel = $("#txtModel").val();
        var sMNo = $("#txtCMaterNo").val();
        var sLVer = $("#txtLabelVer").val();
        var sMName = $("#txtCMaterName").val();
        var sTPNo = $("#txtTPNo").val();   // 模板编码 S1
        var sPVer = $("#txtTPVer").val();  // 模板版本 S2
        var sKind = $("#txtCLableKind").val();  // 模板类别 S3
        var sTPName = $("#txtTemplateName").val();  // 模板名称 S4
        var sPathName = $("#txtPathName").val();  // 模板文件名称 S8
        var sLong = $("#txtSizeLong").val();  //  S5
        var sWide = $("#txtSizeWide").val();  //  S6
        var sProcNo = $("#txtProcNo").val();    //  S7
        var sCAuto = "";  // 客户端小程序自动打印的
        var sCAutoWEB = "";  // WEB端作业执行自动打印的
        //var sFile = $("#txtFile").val();
        //var sPath = $("#txtPath").val();
        var sRemark = $("#txtRemark").val();
        var sFlag = $("#txtAEFlag").val();  //  1 新增  2 修改


        if ($("#txtCAutoCS").is(':checked')) {
            sCAuto = "是";
        }
        else {
            sCAuto = "";
        }
        if ($("#txtCAutoWEB").is(':checked')) {
            sCAutoWEB = "是";
        }
        else {
            sCAutoWEB = "";
        }


        if (sFMNo == "") {
            $("#div_warning").html("请输入产品编码！")
            $("#div_warning").show();
            $("#PLabelSaveBtn").removeAttr("disabled");
            return;
        }

        if (sMNo == "") {
            $("#div_warning").html("请输入标贴编码！")
            $("#div_warning").show();
            $("#PLabelSaveBtn").removeAttr("disabled");
            return;
        }
        if (sLVer == "") {
            $("#div_warning").html("请输入标贴版本！")
            $("#div_warning").show();
            $("#PLabelSaveBtn").removeAttr("disabled");
            return;
        }

        if (sTPNo == "") {
            $("#div_warning").html("请选择标贴模板！")
            $("#div_warning").show();
            $("#PLabelSaveBtn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { FMNo: sFMNo, FMName: sFMName, Model: sModel, MNo: sMNo, MName: sMName, S1: sTPNo, S2: sPVer, S3: sKind, S4: sTPName, S5: sLong, S6: sWide, S7: sProcNo, S8: sPathName, S9: sCAutoWEB, S10: sLVer, UseFlag: sCAuto, Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=OPProductLabel&CFlag=28",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#PLabelSaveBtn").removeAttr("disabled");
                    $("#divsuccess").show();
                    //  document.getElementById('light').style.display = 'none';
                    // document.getElementById('fade').style.display = 'none';

                    $("#txtSPMNo").val(sFMNo);
                    $('#LabelBut_open').click();
                    $("#txtSPMNo").val("");

                    $("#txtCMaterNo").val("");
                    $("#txtLabelVer").val("");
                    $("#txtCMaterName").val("");
                    $("#txtSize").val("");
                    $("#txtFile").val("");
                    $("#txtPath").val("");
                    $("#FTr1").hide();

                    $("#txtTPNo").val(""); // 模板编码 S1
                    $("#txtTPVer").val(""); // 模板版本 S2
                    $("#txtCLableKind").val(""); // 模板类别 S3
                    $("#txtTemplateName").val(""); // 模板名称 S4
                    $("#txtCMaterNo").val("");  // 标贴编码
                    $("#txtCMaterName").val("");  // 标贴名称
                    $("#txtSizeLong").val("");  //  S5
                    $("#txtSizeWide").val("");   //  S6

                    if (sFlag == "1") {  // 新增
                        $("#div_warning").html("【标贴编码】添加完成，可继续录入产品的下一个标签模板。")
                        $("#div_warning").show();
                    }
                    else {
                        document.getElementById('light').style.display = 'none';
                        document.getElementById('fade').style.display = 'none';
                    }


                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#PLabelSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#PLabelSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("该产品编码已存在该标贴，请确认")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_AutoPrint') {
                    $("#PLabelSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("该产品对应的标贴已有一个设置为自动打印了，请确认")
                    $("#div_warning").show();
                }
                else {
                    $("#PLabelSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#PLabelSaveBtn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });



    //  保存:保存标贴模板信息
    $('#TemplateSave_Btn').click(function() {
        $("#TemplateSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();

        var sTPNo = $("#txtTPNo").val();   // 模板编码 FMNo
        var sLVer = $("#txtTPVer").val();  // 模板版本MNo
        var sTPName = $("#txtTPName").val();  // 模板名称 FMName
        var sKind = $("#txtKind").val();  // S1
        var sLong = $("#txtSizeLong").val();  //  S2
        var sWide = $("#txtSizeWide").val();  //  S3
        var sDMan = $("#txtDMan").val();    //  S4
        var sDDate = $("#txtDDate").val();    //  S5
        var sFile = $("#txtFile").val(); //  S6
        var sPath = $("#txtPath").val(); //  S7
        var sLangua = $("#txtLangua").val(); //  S8
        var sEChange = $("#txtEChange").val();  //  S9  
        var sRemark = $("#txtRemark").val();
        var sFlag = $('#txtAEFlag').val();  // 7 新增  8 修改  8-1 升版后修改


        if (sTPNo == "") {
            $("#div_warning").html("请输入模板编码！")
            $("#div_warning").show();
            $("#TemplateSave_Btn").removeAttr("disabled");
            return;
        }
        if (sLVer == "") {
            $("#div_warning").html("请输入模板版本！")
            $("#div_warning").show();
            $("#TemplateSave_Btn").removeAttr("disabled");
            return;
        }
        if (sKind == "") {
            $("#div_warning").html("请选择模板类别！")
            $("#div_warning").show();
            $("#TemplateSave_Btn").removeAttr("disabled");
            return;
        }
        if (sTPName == "") {
            $("#div_warning").html("请输入模板名称！")
            $("#div_warning").show();
            $("#TemplateSave_Btn").removeAttr("disabled");
            return;
        }
        if (sPath == "") {
            $("#div_warning").html("请上传标贴模板！")
            $("#div_warning").show();
            $("#TemplateSave_Btn").removeAttr("disabled");
            return;
        }

        if (sDDate.length > 10) {
            $("#div_warning").html("时间格式不正确，请确认！")
            $("#div_warning").show();
            $("#TemplateSave_Btn").removeAttr("disabled");
            return;
        }



        var Data = '';
        var Params = { FMNo: sTPNo, FMName: sTPName, MNo: sLVer, MName: "", S1: sKind, S2: sLong, S3: sWide, S4: sDMan, S5: sDDate, S6: sFile, S7: sPath, S8: sLangua, S9: sEChange, S10: "", UseFlag: "", Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=OPProductLabel&CFlag=29",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#TemplateSave_Btn").removeAttr("disabled");
                    $('#LabelTemplBut_open').click();  // 重新查询

                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';


                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#TemplateSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                    //location.href = "Login.htm";  
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#TemplateSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("该标贴模板及版本已存在，请确认")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                    $("#TemplateSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("状态不正确，不能操作！")
                    $("#div_warning").show();
                } else {
                    $("#TemplateSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#TempSave_Btn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });



    // 上传标贴模板
    $('#btnLabelload').click(function() {
        $("#div_warning").val("");
        $("#div_warning").hide();
        var sUpFile = $("#UpFile").val();  // ,用下面这种方式，解决中文乱码问题。
        var p = $("#txtFNo");
        if (p.val() == "") {
            $("#div_warning").show();
            $("#div_warning").html("请输入产品编码！")
            return;
        }


        var sMVer = $("#txtTPNo").val() + "(" + $("#txtTPVer").val() + ")";

        $("#Loading").show();
        $.ajaxFileUpload({
            url: '../Service/ApplyUpPicFile.aspx?NNo=' + sMVer + '&sFlag=18&sUpFile=' + encodeURI(sUpFile),
            fileElementId: 'UpFile',
            dataType: 'json',
            success: function(data) {
                //                 var parsedJson = jQuery.parseJSON(data);
                data = eval(data);

                if (data.Msg == "Success") {

                    $("#txtPath").val(data.Path);
                    $("#txtFile").val(data.FileName);

                    $("#Loading").hide();
                    $("#div_warning").html("上传成功！");
                    $("#div_warning").show();
                }
                else {
                    $("#div_warning").html("上传文件失败！");
                    $("#div_warning").show();
                }

            }
        });



    });



    // 用户管理，上传签名图片
    $('#btnSignImgload').click(function () {
        $("#sUpMessage").val("");
        var Flag = $("#txtAEFlag").val();   //24-3 新增；24-4 修改

        var p = $("#txtUserNo");
        if (p.val() == "") {
            ErrorMessage("请先填写用户信息，再添加签名图片！", 2000)
            return;
        }
        var arr = new Array();

        var sPath = $("#txtPath").val();
        var sUpFile = $("#UpFile").val();  // ,用下面这种方式，解决中文乱码问题。
        if (Flag == "24-4") {  // 修改时，把这个之前的路径,名称清空
            sPath = "";
            $("#txtPath").val("");
            $("#txtFile").val("");
        }

        arr = sPath.split(';/'); //注split可以用字符或字符串分割
        if (arr.length > 1) {
            ErrorMessage("上传图片不能超过1个！", 2000)
            return;
        }
        else {
            $.ajaxFileUpload({
                url: '../Service/ApplyUpPicFile.aspx?NNo=' + p.val() + '&sFlag=19&sUpFile=' + encodeURI(sUpFile), // ,用这种方式，解决中文乱码问题。
                fileElementId: 'UpFile',
                dataType: 'json',
                success: function (data) {
                    //                 var parsedJson = jQuery.parseJSON(data);
                    data = eval(data);
                    var sPath = $("#txtPath").val();
                    $("#txtPath").val(sPath + ";" + data.Path);
                    var sFile = $("#txtFile").val();
                    $("#txtFile").val(sFile + ";" + data.FileName);

                    $("#txtFName1").val(data.FileName);  //图片名称
                    $("#txtP1").val(data.Path);  // 图片路径
                    $("#img1").attr("src", data.Path);
                    $("#img1").show();

                    $("#UpFile").val("");
                }
            });
        }
    });


    //添加、修改物料批序号
    $("#BatchSerialSubmit").click(function () {
        $("#BatchSerialSubmit").attr("disabled", "disabled")
        var sOPFlag = $("#txtAEFlag").val();
        var sMaterNo = $("#txtMaterNo").val()
        var sMaterName = $("#txtMaterName").val();
        var sBatchNo = $("#txtBatchNo").val();
        var sBatchNoOld = $("#txtBatchNoOld").val()

        if (sMaterNo == "") {
            ErrorMessage("请输入物料编码！", 2000)
            $("#BatchSerialSubmit").removeAttr("disabled")
            return
        } else if (sMaterName == "") {
            ErrorMessage("物料编码不存在，请重新输入！", 2000)
            $("#BatchSerialSubmit").removeAttr("disabled")
            return
        } else if (sBatchNo == "") {
            ErrorMessage("请输入序列号/批号！", 2000)
            $("#BatchSerialSubmit").removeAttr("disabled")
            return
        }

/*        var regex = /^[A-Za-z0-9]+([-][A-Za-z0-9]+)?(,[A-Za-z0-9]+([-][A-Za-z0-9]+)?)*$/;*/

        var regex = /^[A-Za-z0-9.]+([-][A-Za-z0-9.]+)?(,[A-Za-z0-9.]+([-][A-Za-z0-9.]+)?)*$/;

        if (regex.test(sBatchNo) == false) {
            ErrorMessage("输入的序列号/批号格式错误，不能出现特殊符号，如有多个请使用英文逗号隔开！", 3000)
            $("#BatchSerialSubmit").removeAttr("disabled")
            return
        }

        var arrBatchNo = sBatchNo.split(',')

        var IsFlag = false;

        if (arrBatchNo.length > 1) {
            for (var i = 0; i < arrBatchNo.length; i++) {
                if (arrBatchNo.indexOf(arrBatchNo[i]) !== i) {
                    IsFlag = true;
                    break;
                }
            }

        }

        if (IsFlag) {
            ErrorMessage("输入的序列号/批号存在重复项，请重新输入！", 2000)
            $("#BatchSerialSubmit").removeAttr("disabled")
            return
        }

        var Data = JSON.stringify({ MaterNo: sMaterNo, MaterName: sMaterName, BatchNo: sBatchNo, BatchNoOld: sBatchNoOld, OPFlag: sOPFlag })

        $.ajax({
            url: "../Service/BaseModuleAjax.ashx?OP=OPBatchSerial&CFlag=1",
            type: "POST",
            data: {
                Data: Data
            },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.msg == 'Success') {

                    $("#ShowOne").css("display", "none");
                    $("#ShowOne-fade").css("display", "none");

                    layer.msg('提交成功！');

                    $("#BatchSerialSubmit").removeAttr("disabled")
                    $("#BatchSerialSearch").click()

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Existence') {
                    ErrorMessage("序列号/批号已存在！", 2000)
                    $("#BatchSerialSubmit").removeAttr("disabled")
                } else {
                    ErrorMessage("系统出错，请重试1！", 2000)
                    $("#BatchSerialSubmit").removeAttr("disabled")
                }
            },
            error: function () {
                ErrorMessage("系统出错，请重试2！", 2000)
                $("#BatchSerialSubmit").removeAttr("disabled")
            }
        })

    })

    //更具物料编号获取物料描述
    $('#txtMaterNo').blur(function () {
        var sMaterNo = $("#txtMaterNo").val().trim()

        if (sMaterNo == "") {
            $("#txtMaterName").val("");
            return;
        }

        var Data = JSON.stringify({ No: sMaterNo, Flag: "33-99" });

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetMaterInfoByNo&CFlag=33-99",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $("#txtMaterName").val(sStr.MaterName);
                } else {
                    $("#txtMaterName").val("");
                }
            },
            error: function (data) {
                $("#div_warning").html("系统出错，请重试！")
                $("#div_warning").show();
            }
        });
    });

    //更具物料编号获取物料描述
    $('#txtMaterNo').keydown(function (obj) {
        if (obj.keyCode == 13) {
            $('#txtMaterNo').blur()
        }
    })


    //添加、修改软件版本
    $("#btn_SoftwareVerSave").click(function () {
        var sVerNo = $("#txtVerNo").val()
        var sVerDesc = $("#txtVerDesc").val()
        var sRemark = $("#txtRemark").val()
        var sVNo = $("#txtVNo").val();
        var sFlag = $("#txtAEFlag").val()
        var sVerKind = $("#txtVerKind").val()

        if (sVerNo == "") {
            ErrorMessage("请输入版本号",2000)
            $("#txtMaterName").val("");
            return;
        }

        if (sVerKind == "" || sVerKind == null) {
            ErrorMessage("请选择版本类型", 2000)
            return;
        }

        if (sVerDesc == "" || sVerDesc == null) {
            ErrorMessage("请输入版本描述", 2000)
            return;
        }

        $("#btn_SoftwareVerSave").attr("disabled")

        var Data = JSON.stringify({ No: sVerNo, Name: sVerDesc, A: sVNo, B: ModuleName, c: sVerKind, Flag: sFlag, Remark: sRemark });

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=OPSoftwareVersionInfoList",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#SoftwareVerInfoList_open").click()
                    layer.msg("保存成功!")
                    closeDialog()
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'ParamsError') {
                    ErrorMessage("参数错误，请检查输入！", 2000)
                }
                else {
                    ErrorMessage("保存失败，请重试！", 2000)
                }

                $("#btn_SoftwareVerSave").removeAttr("disabled")
            },
            error: function (data) {
                ErrorMessage("系统出错，请重试！", 2000)
                $("#btn_SoftwareVerSave").removeAttr("disabled")
            }
        });


    })

    $("#SoftwareVerDel_Btn").click(function () {
        var sVNo = $("#txtDelVNo").val()
        $("#btn_SoftwareVerSave").attr("disabled")
        var Data = JSON.stringify({ No: sVNo, Name: "", A: "", B: ModuleName, Flag: "3", Remark: "" });

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=OPSoftwareVersionInfoList",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    layer.msg("删除成功!")
                    closeDialog();
                    $("#SoftwareVerInfoList_open").click()
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'ParamsError') {
                    ErrorMessage("参数错误，请检查输入！", 2000)
                }
                else {
                    ErrorMessage("删除失败，请重试！", 2000)
                }

                $("#btn_SoftwareVerSave").removeAttr("disabled")
            },
            error: function (data) {
                ErrorMessage("系统出错，请重试！", 2000)
                $("#btn_SoftwareVerSave").removeAttr("disabled")
            }
        });


    })






























    // 这里增加其他按钮，空间事件方法  










});


// 点击主界面，显示对应的菜单
function ShowModule(num) {
    if (num == 1) {
        window.open("http://www.baidu.com")
    }
    else if (num == 2) {
        window.open("BaseModule/SysKind.htm")
    }
    else if (num == 20) {
        window.open("BaseModule/Cust.htm")
    }
    else if (num == 21) {
        window.open("BaseModule/Supplier.htm")
    }
    else if (num == 22) {
        window.open("BaseModule/CodeRule.htm")
    }
    else if (num == 23) {  // 物料主数据
        window.open("BaseModule/Mater.htm")
    }
    else if (num == 40) {  //采购订单
       window.open("Purch/Purch.htm")
    }
    else if (num == 41) {
       window.open("Purch/PurchAud.htm")
    }
    else if (num == 42) {
       window.open("Purch/ReplyDate.htm")
    }
    else if (num == 43) {
       window.open("Purch/RDateConfirm.htm")
    }
    else if (num == 44) {
       window.open("Purch/Delivery.htm")
    }
    else if (num == 45) {
       window.open("Purch/SHCofirm.htm")
    }
    else if (num == 46) {
       window.open("Purch/PReturn.htm")
    }
    else if (num == 47) {
       window.open("Purch/Inquiry.htm")
    }
    else if (num == 48) {
       window.open("Purch/InquiryAn.htm")
    }
    else if (num == 110) {
        $("#warningStr").html("无权操作，请和管理员联系！")
        $("#warningStr").show();
    }
    else if (num == 111) {
        window.open("BaseModule/SysKind.htm")
    }


}

//消息提示
function SuccessMessage(text, time) {
    var Message = $('#Success');
    Message.html(text)
    Message.show()
    setTimeout(function () {
        Message.hide()
    }, time);
}
function ErrorMessage(text, time) {
    var Message = $('#Error');
    Message.html(text)
    Message.show()
    setTimeout(function () {
        Message.hide()
    }, time);
}
function WarningMessage(text, time) {
    var Message = $('#Warning');
    Message.html(text)
    Message.show()
    setTimeout(function () {
        Message.hide()
    }, time);
}

