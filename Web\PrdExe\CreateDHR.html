﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title></title>
    <script src="../js/jQuery-2.2.0.min.js" type="text/javascript"></script>
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/layer/layer.js"></script>




    <script type="text/javascript">

        function CreateDHR() {
            const orderno = $("#OrderNo").val()

            if (orderno == "") {
                return;
            }

            $('#txtOPRec').val("");

            const Params = { No: "", Name: "", Item: orderno, MNo: "", MName: "", A: "", B: "", C: "", D: "", E: "", F: "", G: "", H: "", I: "", J: "", InMan: "", Remark: "", Flag: "CreateDHR" };

            export_pdf_custom(Params)
        }

        function CreateHZ() {
            const orderno = $("#OrderNo").val()

            if (orderno == "") {
                return;
            }

            $('#txtOPRec').val("");

            const Params = { No: "", Name: "", Item: orderno, MNo: "", MName: "", A: "", B: "", C: "", D: "", E: "", F: "", G: "", H: "", I: "", J: "", InMan: "", Remark: "", Flag: "CreateHZ" };

            export_pdf_custom(Params)
        }

        // 循环导出DHR报表
        function export_pdf_custom(reqdata) {

            $.ajax({
                url: "../Service/DHRAjax.ashx?OP=GetExportConditions",
                data: {
                    Data: JSON.stringify(reqdata)
                },
                type: "POST",
                success: function (res) {
                    var parsedJson = jQuery.parseJSON(res);
                    if (parsedJson.Msg == "LoginError") {
                        layer.msg("登录超时，请重新登录！")
                    }
                    else if (parsedJson.Msg == "Success") {
                        //获取所有要生成的序列号工序
                        const data = parsedJson.Data;
                        var urls = [];
                        for (var i = 0; i < data.length; i++) {
                            console.log(data[i])
                            Params = { No: data[i].SerialNo, Name: "生产管理>生产执行", Item: data[i].OrderNo, MNo: data[i].ProductNo, MName: data[i].MaterBatchNo, A: data[i].ProcedureName, B: data[i].ProcedureNo, C: data[i].ProcedureVer, D: data[i].FlowOrder, E: data[i].OPFlag, F: data[i].EXENo, Remark: "生产执行", Flag: "" };
                            urls.push("../Service/DHRAjax.ashx?OP=GetExportData" + "&Data=" + encodeURIComponent(JSON.stringify(Params)));
                        }
                        $("#Doing").show()
                        $("#fadeDHR").show()
                        //开始生成
                        sendMultipleRequests(urls, 1)
                    } else if (parsedJson.Msg == "Error") {
                        layer.msg("未找到需要生成的序列号！")
                    }
                },
                error: function () {
                    layer.msg("系统出错，请稍后再试！")
                }
            })
        }

        // 定义一个名为 sendRequest 的函数，它接收一个 url 作为参数
        function sendRequest(url) {
            return new Promise((resolve, reject) => {
                if (!navigator.onLine) {
                    reject(new Error("网络未连接"));
                    return;
                }

                $.ajax({
                    url: url,
                    timeout: 20000, // 设置超时时间为20秒
                    success: function (response) {
                        resolve(response);
                    },
                    error: function (error) {
                        reject(error);
                    }
                });
            });
        }
        // 定义一个名为 sendMultipleRequests 的函数，它接收一个 urls 数组和一个 concurrency 数字作为参数
        function sendMultipleRequests(urls, batchSize) {
            let index = 0;
            let completed = 0;
            // 成功的数量
            let successCount = 0;
            //失败的数量
            let failureCount = 0;
            let status;
            let allResult = [];
            function sendBatch() {
                const batch = urls.slice(index, index + batchSize);
                const requests = batch.map(url => sendRequest(url));
                Promise.all(requests)
                    .then(results => {
                        completed += batch.length;
                        allResult = allResult.concat(results)
                        if (completed < urls.length) {
                            sendBatch();
                        } else {
                            let htmlStr = "";
                            allResult.forEach(item => {
                                const parsedItem = JSON.parse(item);
                                if (parsedItem.Msg == "Error" || parsedItem.Msg == "LoginError") {
                                    status = "生成失败"
                                    failureCount++
                                } else {
                                    status = "生成成功"
                                    successCount++
                                }
                                const typeText = parsedItem.DHRType == 'DHR' ? '工序追溯表' : '产品追溯表';
                                htmlStr += `${parsedItem.SerialNo} ${parsedItem.ProcedureName} ${typeText} ${status} ${parsedItem.ExceptionMessage}\n`;
                            });
                            $('#txtOPRec').val(htmlStr);
                            if (failureCount > 0) {
                                $('#LMgs').html("总共：" + urls.length + "份。成功生成：" + successCount + "份，失败：" + failureCount + "份。可再次操作重试，继续进行生成操作。");
                                $("#Doing").hide()
                                $("#fadeDHR").hide()
                            } else {
                                $('#LMgs').html("总共：" + urls.length + "份，已全部成功生成：" + successCount + "份，操作已完成！");
                                $("#Doing").hide()
                                $("#fadeDHR").hide()
                            }
                        }
                    })
                    .catch(error => {
                        console.log(error)
                        $('#LMgs').html("生成已停止，请检查网络是否正常！若网络正常，可再次操作重试，继续进行生成操作。");
                        $("#Doing").hide()
                        $("#fadeDHR").hide()
                    });
                index += batchSize;
            }
            sendBatch();
        }
    </script>
    <style>
        .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: white;
            z-index: 1001;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container-body">
        <div>
            <input type="text" id="OrderNo" />
            <button onclick="CreateDHR()">
                生成工序记录表
            </button>
            <button onclick="CreateHZ()">
                生成产品记录表
            </button>
            <img src="../fonts/loading.gif" width="60px" height="12px" id="Doing" style="display:none;" />
            <span id="LMgs"></span>
            <div id="fadeDHR" class="black_overlay" style="z-index:1024">
            </div>
            <textarea id="txtOPRec" style="height:600px;width:100%">


            </textarea>
        </div>
    </div>
</body>
</html>