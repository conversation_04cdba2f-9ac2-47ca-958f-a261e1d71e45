
update T_OrderInfo set CompanyNo='C0010' where  OrderNo = 'MO-1801-0022' 


select * from T_OrderInfo where  OrderNo = 'MO-1801-0022' 
select * from T_OrderBaseFlow where OrderNo = 'MO-1801-0022' 
select * from T_OSMaterReplace where OSNO = 'MO-1801-0022' 
select * from T_OrderFlowDivce where OrderNo = 'MO-1801-0022' 
select * from T_OrderFlowFile where  OrderNo = 'MO-1801-0022' 
select * from T_OrderFlowTestItem where  OrderNo = 'MO-1801-0022' 

select * from T_OrderBOM where OrderNo = 'MO-1801-0022' 

0000
DELETE T_OrderInfo where  OrderNo = 'MO-1801-0022' 
DELETE T_OrderBaseFlow where OrderNo = 'MO-1801-0022' 
DELETE T_OSMaterReplace where OSNO = 'MO-1801-0022' 
DELETE T_OrderFlowDivce where OrderNo = 'MO-1801-0022' 
DELETE T_OrderFlowFile where  OrderNo = 'MO-1801-0022' 
DELETE T_OrderFlowTestItem where  OrderNo = 'MO-1801-0022' 
DELETE T_OrderBOM where OrderNo = 'MO-1801-0022' 



        //  发放序列号
        function SendSerial_OnClick() {
            var sONo = $("#txtSOrderNo").val();  //工单号
            var sMNo = $("#txtOMaterNo").val();
            var sNum = $("#txtONum").val();
            var sFlag = "2";

            var Data = '';
            var Params = { OrderNo: sONo, MNo: sMNo, Num: sNum, Flag: sFlag };
            var Data = JSON.stringify(Params);

            $.ajax({
                type: "POST",
                url: "../Service/BaseModuleAjax.ashx?OP=OPSerielInfo&CFlag=2",
                data: { Data: Data },
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);


                    if (parsedJson.Msg == "Y_EXIST") { // 说明已发放序列了
                        $("#div_warning2").html("该工单已发放序列号");
                        $("#div_warning2").show();
                    }
                    else {
                        $('#Search_SerialBtn').click();
                    }


                    AddPrdInfoToSerial();  //  刷新工单对应序列号对应的流程及BOM信息
                },
                error: function (data) {
                    $("#div_warning").html("系统出错，请重试2！")
                    $("#div_warning").show();
                }
            });
        }





插入测试项
insert into T_FlowTestItem(ProductNo,ProcedureNo,ProcedureVer,ProcedureName,SpecNo,SNo,NameCH,NameEN,IndexCH,IndexEN,DescCH,DescEN,StandardCH,StandardEN,
SpecKind,SpecUnit,Status,CompanyNo,InMan,Remark)
select '2727-01PZ-0001' ProductNo,'06' ProcedureNo,'V1.0' ProcedureVer,'调试' ProcedureName,
SpecNo,1 as SNo,NameCH,NameEN,IndexCH,IndexEN,DescCH,DescEN,StandardCH,StandardEN,SpecKind,SpecUnit,'在用' Status,'C0010' CompanyNo,'wd10' InMan,''
from PStandant.dbo.T_Spec where SpecNo in
('S12010900001','S12010900002','S13030900001','S13030900002','S13030900003','S13030900004')


select *  from T_SetSyncInfo 同步开关设置
SCNo,SCName,SCV1,SCV2,SCV3,SCV4,SCV5,SCV6,SCV7,SCV7,FrequencyType,Kind,Status,CompanyNo InMan,InDate,Remark



select *  from T_ProductStockInfo
select *  from T_ProductStockInfo
select *  from T_ProductStockMX

select *  from T_MaterStockOutMX
SeqNo,InNo,ObjectNo,MaterNo,MaterVer,MaterName,UseNum,InNum,CUseNum,InMan,Remark
insert into T_MaterStockOutMX(InNo,ObjectNo,MaterNo,MaterVer,MaterName,UseNum,InNum,CUseNum,Status,InMan,CompanyNo)
select *  from T_MaterOptLog


select * from (
select a.PMaterNo,a.MaterNo,a.CUseNum,isnull(b.Stock,0) as Stock from T_ProductStockMX a
left join (select MaterNo,SUM(InStockNum+RBNum+ZCNum) as Stock from T_MaterStock group by MaterNo) b on a.MaterNo=b.MaterNo
where a.Status='已确认'
) aa where CUseNum > Stock






select *  from  T_ProductBatchInfo where ProcedureNo='GX003'
AND BatchNo in ('JF50C222001889','JF50C222001892','JF50C222001950','JF50C222001952','JF50C222001898') 

select *  from  T_MaterConsumeInfo 
WHERE BatchNo in ('JF50C222001889','JF50C222001892','JF50C222001950','JF50C222001952','JF50C222001898') 


select *  from DELETE T_ProductBatchInfo where ProcedureNo='GX003'
AND BatchNo in ('JF50C222001889','JF50C222001892','JF50C222001950','JF50C222001952','JF50C222001898') 

select *  from DELETE T_MaterConsumeInfo 
WHERE BatchNo in ('JF50C222001889','JF50C222001892','JF50C222001950','JF50C222001952','JF50C222001898') 



select *  from T_ProductBatchInfo
select *  from T_MaterConsumeInfo
select *  from T_MaterConsumeInfo where BatchNo='DS50JF01C222000050'  1JF2209060001

select *  from T_MaterConsumeInfo where MaterBatchNo='1JF2208220002' 
select *  from T_MaterConsumeInfo where MaterBatchNo='1JF2209060001' 
select *  from  T_MaterConsumeInfo where MaterBatchNo='2JF2208240002' and BatchNo='1JF2209060001'

-- delete T_MaterConsumeInfo where MaterBatchNo='2JF2208240002' and BatchNo='1JF2209060001'

-- update   T_MaterConsumeInfo set Status='关联中' where MaterBatchNo='2JF2208240002'

select *  from T_ObjectFieldConfig   -- 定义每个流程的字段，需要填写那些字段
select *  from T_FlowInfo  -- 每个流程需要那些审批节点，及设置审批人

select *  from T_BusinessDataAndFlow
select *  from T_BusinessDataApply


select MaterNo, SUBSTRING(MaterNo,1,1) as One,SUBSTRING(MaterNo,2,1) as Two,SUBSTRING(MaterNo,3,1) as Three,SUBSTRING(MaterNo,4,1) as Four  
into #M
from T_MaterInfo where MaterNo='A32C-1011'

SELECT a.*,one+'  '+b.TypeName as MaterType,Two+'  '+c.TypeName as ABCKind,Three+'  '+d.TypeName as MaterXH,Four+'  '+e.TypeName as Coating
into #MS FROM #M a
left join (select * from T_MaterType where FKind=60) b on a.One=b.TypeNo  -- 类型
left join (select * from T_MaterType where FKind=67) c on a.Two=c.TypeNo  -- 材料
left join (select * from T_MaterType where FKind=71) d on a.Three=d.TypeNo  -- 尺寸
left join (select * from T_MaterType where FKind=75) e on a.Four=e.TypeNo  -- 镀膜

SELECT *  FROM #MS
UPDATE T_MaterInfo set MaterXH=b.MaterXH,MaterType=b.MaterType,Coating=b.Coating,ABCKind=b.ABCKind
from T_MaterInfo a join #MS b on a.MaterNo=b.MaterNo


===== 获取采购订单第一次回复交期，重排，收货等数据
 --- 需要获取数据的采购订单
--insert into #Order 
select ltrim(rtrim(ebeln)) as ebeln,ltrim(rtrim(ebelp)) as ebelp,EINDT,MENGE -- into #Order 
from T_EKPO_history where convert(char(4),EINDT,120) >='2022' and MATNR IN 
('082-002234-00','082-003555-00','082-001951-00',

 select * from #Order   


--- 供应商获取供应商第一次回复交期数据  select * from T_EKPO_STAT  drop table #temp,#min
select id,ltrim(rtrim(ebeln)) as ebeln,ltrim(rtrim(ebelp)) as ebelp,field1,fvalue1 into #temp
from T_EKPO_STAT where ltrim(rtrim(ebeln))+ltrim(rtrim(ebelp)) in ( select ebeln+ebelp from #Order  )

select ltrim(rtrim(ebeln)) as ebeln,ltrim(rtrim(ebelp)) as ebelp,Min(id) as id into #min from #temp 
where field1='ldate' group by ebeln,ebelp
-- select *  from #min   select * from #minJQ
select a.id,a.ebeln,a.ebelp,a.fvalue1 into #minJQ  from #temp a join #min b on a.id=b.id  --  得到第一次回复交期

--- 获取重排记录 select top 100 * from  T_REDELIVERY  select *  from #tCP
select id,EBELN,EBELP,CDATE,case when STATUS=0 THEN '取消' when STATUS=1 THEN '提前' when STATUS=2 THEN '推迟' 
when STATUS=3 THEN '强制重排' when STATUS=4 THEN '强制取消' end as STATUS        
into #tCP from T_REDELIVERY 
WHERE ltrim(rtrim(ebeln))+ltrim(rtrim(ebelp)) IN (SELECT ebeln+ebelp FROM #Order)

-- 取最后一次重排状态 select * FROM #MCP
select ltrim(rtrim(ebeln)) as ebeln,ltrim(rtrim(ebelp)) as ebelp,max(id) as id into #TMCP from #tCP
GROUP BY ebeln,ebelp
SELECT id,EBELN,EBELP,CDATE,STATUS INTO #MCP from #tCP WHERE ID IN (SELECT ID FROM #TMCP)


--- 获取收货记录 SELECT  *  FROM #RSH
select PONUM AS ebeln,XC AS ebelp,SUM(RECEIVED_QUANTITY) AS QTY INTO #RSH from T_PPO
WHERE ltrim(rtrim(PONUM))+ltrim(rtrim(XC)) IN (SELECT ebeln+ebelp FROM #Order)
GROUP BY PONUM,XC


SELECT TOP 10 *  FROM T_EKPO 

--连接上面的数据表
SELECT a.ebeln,a.ebelp,aa.lifnr,aa.Matnr,aa.MAKTX,a.EINDT,a.MENGE,b.fvalue1,c.STATUS,
isnull(c.STATUS,'否') as CPFLAG,isnull(d.QTY,0) as qty
from #Order a LEFT JOIN T_EKPO aa on a.ebeln=ltrim(rtrim(aa.ebeln)) and a.ebelp=ltrim(rtrim(aa.ebelp))
left join #minJQ b on ltrim(rtrim(a.ebeln))=b.ebeln and ltrim(rtrim(a.ebelp))=b.ebelp
left join #MCP c on ltrim(rtrim(a.ebeln))=c.ebeln and ltrim(rtrim(a.ebelp))=c.ebelp
left join #RSH d on ltrim(rtrim(a.ebeln))=d.ebeln and ltrim(rtrim(a.ebelp))=d.ebelp
ORDER BY a.ebeln,a.ebelp


----- 资福自动打印程序代码
select a.SeqNo, a.SerielNo,a.GX,a.Flag,b.OrderNo,b.MaterNo,b.MaterName,c.ProcedureNo,c.ProcedureName,c.WorkUnit,d.PrintLable, 
e.MaterNo as LMaterNo,e.MaterName as LMaterName,e.LabelVer,e.TemplateName,e.PathName,e.TPNo,e.TPVer,e.AutoPrint,f.TPPath,f.TPName 
from T_PrePrintSeriel a join T_SerielInfo b on a.SerielNo=b.SerielNo join T_ProductFlow c on b.MaterNo=c.ProductNo 
join T_ProcedureAction d on c.ConditionNo=d.PANo join T_ProductAndLabel e on c.ProductNo=e.PMaterNo and c.ProcedureNo=e.ProcedureNo 
join T_LabelTemplate f on e.TPNo=f.TPNo and e.TPVer=f.TPVer 
where f.Status='启用' and a.Flag='N' and e.AutoPrint='是' and c.WorkUnit like '%" + sWNo + "%'

update T_SerielInfo set OrderNo=b.OrderNo,MaterNo=b.MaterNo,MaterName=b.MaterName,Model=b.MaterSpec,Status='' " +
                    " from T_SerielInfo a join (select OrderNo,MaterNo,MaterName,MaterSpec,CompanyNo from T_OrderInfo where OrderNo='" + sOrderNo + "') b on a.CompanyNo=b.CompanyNo " +
                    " where a.SerielNo in (select top " + sNum + " a.SerielNo from T_PrePrintSeriel a join T_SerielInfo b on a.SerielNo=b.SerielNo where a.Flag='N' and a.CompanyNo='" + sCompanyNo + "' " +
                    "       and a.SerielNo not in (select SerialNo from T_OrderSerial where OrderNo='" + sOrderNo + "') order by a.SerielNo) and a.CompanyNo='" + sCompanyNo + "'


            string sStr = " insert into T_SerialBOM(SerialNo,FMaterNo,FMaterVer,MaterNo,MaterVer,MaterName,OrderNo,ProcedureNo,ProcedureName,ControlWay,UseNum,Pack,CompanyNo,InMan,Remark) " +
                   " select bb.SerielNo,aa.FMaterNo,aa.FMaterVer,aa.MaterNo,aa.MaterVer,aa.MaterName,aa.OrderNo,aa.ProcedureNo,aa.ProcedureName,aa.ControlWay,aa.UseNum,aa.Pack,aa.CompanyNo,'" + sCLOGIN + "','自动打印' " +
                   " from ( " +
                   "       select FMaterNo,FMaterVer,MaterNo,MaterVer,MaterName,OrderNo,ProcedureNo,ProcedureName,ControlWay,UseNum,Pack,CompanyNo from T_OrderBOM WHERE OrderNo='" + sOrderNo + "' and CompanyNo='" + sCompanyNo + "' " +
                   "      ) aa " +
                   " join (select top " + sNum + " a.SerielNo,a.CompanyNo from T_PrePrintSeriel a join T_SerielInfo b on a.SerielNo=b.SerielNo where a.Flag='N' and a.CompanyNo='" + sCompanyNo + "' " +
                   "       and a.SerielNo not in (select SerialNo from T_OrderSerial where OrderNo='" + sOrderNo + "') order by a.SerielNo) bb on aa.CompanyNo=bb.CompanyNo " +
                   " where aa.ProcedureNo+aa.MaterNo+bb.SerielNo not in (select ProcedureNo+MaterNo+SerialNo from T_SerialBOM where  CompanyNo='" + sCompanyNo + "') " +
                   " order by bb.SerielNo,aa.MaterNo ";


            sSQL1 = " insert into T_SerialBaseFlow(SerialNo,OrderNo,ProductNo,ProductName,SectionNo,SectionName,ProcedureNo,ProcedureName,FlowOrder,ConditionNo,ConditionName,WorkUnit,Status,CompanyNo,InMan,Remark) " +
                   " select bb.SerielNo,aa.OrderNo,aa.ProductNo,aa.ProductName,aa.SectionNo,aa.SectionName,aa.ProcedureNo,aa.ProcedureName,aa.FlowOrder+" + sSeq + " as FlowOrder,aa.ConditionNo,aa.ConditionName,aa.WorkUnit,aa.Status,aa.CompanyNo,'" + sCLOGIN + "','自动打印'  " +
                   " from ( " +
                   "     select OrderNo,ProductNo,ProductName,SectionNo,SectionName,ProcedureNo,ProcedureName,FlowOrder,ConditionNo,ConditionName,WorkUnit,Status,CompanyNo " +
                   "     from T_OrderBaseFlow WHERE OrderNo='" + sOrderNo + "' and CompanyNo='" + sCompanyNo + "' " +
                   " ) aa " +
                   " join (select top " + sNum + " a.SerielNo,a.CompanyNo from T_PrePrintSeriel a join T_SerielInfo b on a.SerielNo=b.SerielNo where a.Flag='N' and a.CompanyNo='" + sCompanyNo + "' " +
                   "      and a.SerielNo not in (select SerialNo from T_OrderSerial where OrderNo='" + sOrderNo + "') order by a.SerielNo) bb on aa.CompanyNo=bb.CompanyNo " +
                   " where bb.SerielNo+aa.ProcedureNo not in (select SerialNo+ProcedureNo from T_SerialBaseFlow where  CompanyNo='" + sCompanyNo + "') " +
                   " order by bb.SerielNo,aa.ProcedureNo ";

----- 资福自动打印程序代码






















-- 获取 TOKEN
https://udid.nmpa.gov.cn/api/beta/v2/token/get  -- 测试系统
https://udid.nmpa.gov.cn/api/v2/token/get   -- 正式的

应用(appId)：9d62b5bb4957447a9ee9164f4c4e9343
  授权码(appSecret):4c520be988c8453ba478096ce5672bd0
统一社会信用代码：91440300568536676G
方式一：  -- 这种方式可以
 {
"appId": "9d62b5bb4957447a9ee9164f4c4e9343",
"appSecret": "4c520be988c8453ba478096ce5672bd0",
"TYSHXYDM": "91440300568536676G"
}
方式二：  -- 这种方式不成功
{
 "params": {
  "appId": "9d62b5bb4957447a9ee9164f4c4e9343",
  "appSecret": "4c520be988c8453ba478096ce5672bd0",
  "TYSHXYDM": "91440300568536676G"
 }
}



纤草：
      应用 (appId)：2a49d244909749f18af88ee52f7967ba
      授权码(appSecret):821bbc574dc740d5b5d0efc3197fff6c
      统一社会信用代码：91440300MA5FK9JB49

方式一：
 {
"appId": "2a49d244909749f18af88ee52f7967ba",
"appSecret": "821bbc574dc740d5b5d0efc3197fff6c",
"TYSHXYDM": "91440300MA5FK9JB49"
}
方式二：  -- 不成功
{
 "params": {
  "appId": "2a49d244909749f18af88ee52f7967ba",
  "appSecret": "821bbc574dc740d5b5d0efc3197fff6c",
  "TYSHXYDM": "91440300MA5FK9JB49"
 }
}



--- 资福
{
"accessToken":"82733CB1803EE8B509174871B9852435C02DAF5069FDC4BBFF154CBBBCCE1170D526D99C2F90305ECC865C8A3EF5188D37F8860E49C218028A54EAB1732FE95438B3254ED4D64FEA20555415923EFC908AA0FEF0F9A80BCD067651EAD6A0E71D48584F7AEE71DB01",
 "dataSet": [{
  "uploadType":"MODIFY",
   "deviceRecordKey": "F13DBEB8D8AA7A3531566EA0AF0BA73B",
  "ZXXSDYCPBS": "06971690840242",
  "CPBSBMTXMC": "GS1",
  "ZXXSDYZSYDYDSL": "1",
  "QXLB": "器械",
  "SYDYCPBS": "",
  "FLBM": "06",
  "CPHHHBH": "",
  "YLQXZCRBARMC": "深圳市资福医疗技术有限公司",
  "CPMCTYMC": "磁控胶囊式内窥镜系统/一次性使用无菌胶囊式内窥镜",
  "SPMC": "",
  "GGXH": "MCS-600/CES-510",
  "QTXXDWZLJ": "",
  "CPMS": "该产品由磁控设备（MCE-600）、一次性使用无菌胶囊式内窥镜（CES-510和CES-520）、图像分析软件（DSV-152，版本号：V1）组成。一次性使用无菌胶囊式内窥镜外壳为乳白色",
  "TSRQ": "",
  "SFYBTZJBS": "0",
  "BTCPBSYZXXSDYCPBSSFYZ": "",
  "BTCPBS": "",
  "BSZT":"2",
  "SFYZCBAYZ":"1",
  "SFWBLZTLCP": "0",
  "SCBSSFBHPH": "1",
  "SCBSSFBHXLH": "1",
  "SCBSSFBHSCRQ": "1",
  "SCBSSFBHSXRQ": "1",
  "SFBJWYCXSY": "1",
  "ZDCFSYCS": "",
  "SYQSFXYJXMJ": "0",
  "SFWWJBZ": "1",
  "MJFS": "",
  "YFLBM": "",
  "CPBSFBRQ": "2021-04-20",
  "TYSHXYDM": "91440300568536676G",
  "ZCZBHHZBAPZBH": "国械注准20193060987",
  "TSCCHCZTJ": "",
  "TSCCSM": "",
  "devicePackage": [{
   "CPBZJB": "盒",
   "BZCPBS": "16971690840249",
   "BZNHXYJCPBSSL": "10",
   "BZNHXYJBZCPBS": "06971690840242"
  }, {
   "CPBZJB": "箱",
   "BZCPBS": "26971690840246",
   "BZNHXYJCPBSSL": "6",
   "BZNHXYJBZCPBS": "16971690840249"
  }],
  "deviceStorage": [{ 
   "CCHCZTJ": "",
   "ZDZ": "",
   "ZGZ": "",
   "JLDW": ""
  }],
  "deviceClinical": [{
   "LCSYCCLX": "",
   "CCZ": "",
   "CCDW": ""
  }],
  "CONTACTLIST": [{
   "QYLXRCZ": "0755-86092558",
   "QYLXRYX": "<EMAIL>",
   "QYLXRDH": "0755-82128411"
  }],
  "YLQXZCRBARYWMC": "",
  "CPLB": "耗材",
  "CGZMRAQXGXX": "MR特定条件安全",
  "YBBM": ""
 }]
}






{
"accessToken":"82733CB1803EE8B509174871B9852435C02DAF5069FDC4BBFF154CBBBCCE1170D526D99C2F90305ECC865C8A3EF5188D37F8860E49C218028A54EAB1732FE95438B3254ED4D64FEA20555415923EFC908AA0FEF0F9A80BCD067651EAD6A0E71D48584F7AEE71DB01",
"dataSet":[
{
"uploadType":"add",
"deviceRecordKey":"445634SDFSFE45WEEWREW7DFDSFE",
"ZXXSDYCPBS":"06948546206446",
"ZXXSDYZSYDYDSL":"1",
"SYDYCPBS":"",
"CPBSBMTXMC":"GS1",
"SFYBTZJBS":"0",
"BTCPBSYZXXSDYCPBSSFYZ":"1",
"BTCPBS":"YY",
"BSZT":"2",
"SFYZCBAYZ":"1",
"ZCBACPBS":"1",
"CPBSFBRQ":"2021-12-01",
"CPMCTYMC":"不锈钢Z型支架及输送系统",
"SPMC":"支架及输送系统",
"GGXH":"支架规格:JRZ-34-75 输送系统规格:14F",
"SFWBLZTLCP":"1",
"CPMS":"支架规格:JRZ-34-75 输送系统规格:14F",
"CPHHHBH":"0094-099834-00",
"CPLX":"1",
"FLBM":"01-01-01",
"YFLBM":"",
"YLQXZCRBARMC":"赛诺医疗科学技术股份有限公司",
"YLQXZCRBARYWMC":"",
"TYSHXYDM":"91440300568536676G",
"ZCZBHHZBAPZBH":"国械注准20163772492",
"HCHZSB":"1",
"SFBJWYCXSY":"1",
"ZDCFSYCS":"1",
"SFWWJBZ":"1",
"SYQSFXYJXMJ":"1",
"MJFS":"高温",
"YBBM":"",
"CGZMRAQXGXX":"0",
"devicePackage":[
{
"BZCPBS":"66920228007406",
"BZNHXYJBZCPBS":"06920228007473",
"BZNHXYJCPBSSL":"1",
"CPBZJB":"箱"
},
],
"deviceStorage":[
{
"CCHCZTJ":"储存湿度",
"ZDZ":"0",
"ZGZ":"90",
"JLDW":"%RH"
},
],
"TSCCHCZTJ":"高温",
"deviceClinical":[
{
"LCSYCCLX":"兼容导引导丝外径",
"CCZ":"3",
"CCDW":"mm"
},
],
"TSCCSM":"使用尺寸说明(特殊)",
"SCBSSFBHPH":"1",
"SCBSSFBHXLH":"1",
"SCBSSFBHSCRQ":"1",
"SCBSSFBHSXRQ":"1",
"QTXXDWZLJ":"信息的网址链接(其他)",
"TSRQ":"2022-12-01",
"BGSM":"变更说明(sdfe)"
},
]
}


update t_diinfo set 
--ZXXSDYCPBS='06971690004194',
CPBSBMTXMC='GS1',
ZXXSDYZSYDYDSL='1',
SYDYCPBS='',-------可为空
CPBSFBRQ='2022-12-26',
CPBSZT='2',--------BSZT
SFYZCBACPBSYZ='1',
ZCBACPBS='',-------药监局不需要这个字段
SFYBTZJBS='0',
HCHZSB='0',
TSCCHCZTJ='',------可为空
TSCCSM='',------可为空
BTCPBSYZXXSDYCPBSSFYZ='', -------可为空
BTCPBS='',--------可为空
CPMCTYMC='磁控胶囊式内窥镜系统/一次性使用无菌胶囊式内窥镜',
SPMC='', ----------可为空
SFWBLZTLCP='0',
GGXH='MCS-600/CES-510',
CPMS='该产品由磁控设备（MCE-600）、一次性使用无菌胶囊式内窥镜（CES-510和CES-520）、图像分析软件（DSV-152，版本号：V1）组成。一次性使用无菌胶囊式内窥镜外壳为乳白色',
CPHHHBH='',  ---------可为空
QXLB='器械',
YFLBM='77',
FLBM='12-04-01',
YLQXZCRBARMC='深圳市资福医疗技术有限公司',
YLQXZCRBARYWMC='',-------可为空
TYSHXYDM='91440300568536676G',
ZCZBHHZBAPZBH='国械注准20193060987',
CPLX='1',
CPLB='耗材',
CGZMRAQXGXX='0',
SFBJWYCXSY='0',---------可为空
ZDCFSYCS='',---------可为空
SFWWJBZ='1',
SYQSFXYJXMJ='0',
MJFS='',---------可为空
QTXXDWZLJ='', ---------可为空
YBBM='',--------可为空
TSRQ='', ---------可为空
SCBSSFBHPH='1',
SCBSSFBHXLH='1',
SCBSSFBHSCRQ='1',
SCBSSFBHSXRQ='1',
DIone='16971690004917',
DItwo='26971690004914',
DIthree='',
DIfour='',
Kind='',
Status='启用',
CompanyNo='C0010',
InMan='wd10'
where ZXXSDYCPBS ='06971690004972'

--select * from [dbo].[T_DIPackInfo]
insert into T_DIPackInfo
values ('06971690004972','16971690004979','盒',10,10,'C0010','wd10',getdate(),'');
insert into T_DIPackInfo
values ('06971690004972','26971690004976','箱',6,6,'C0010','wd10',getdate(),'');



SYDYCPBS -- 空可以， 1 不可以
SFYBTZJBS -- 0 可以，是 不可以，更新为 1 看看   --  OK
CPBSZT --  2 可以，二维码不可以，更新为 1 看看   --  OK  ---  请按照[1,2,3,4]填写；
SFWBLZTLCP  -- 0 可以，是 不可以，更新为 1 看看   --  OK
CPLX  -- 必输项，1 是可以，现在更新 0 看看   -- NG
TYSHXYDM  -- 必输
HCHZSB  -- 必输， 0 可以，现在输入 1 看看   --  OK
SFBJWYCXSY  -- 0 可以，是不可以，现在输入 1 看看   --  OK
SFWWJBZ  -- 1 可以，是不可以，现在输入 0 看看   --  OK
SYQSFXYJXMJ   -- 0 可以，是不可以，现在输入 1 看看   --  OK
CGZMRAQXGXX   -- 0 可以，0 安全，现在输入 1 看看   --  OK  --- 如：0，1，2；
SCBSSFBHPH  -- 1 可以，是不可以，现在输入 0 看看     SCBSSFBHXLH   SCBSSFBHSCRQ  SCBSSFBHSXRQ   --  OK
BTCPBSYZXXSDYCPBSSFYZ  -- 可以为空，是不可以，现在更新为 1 看看