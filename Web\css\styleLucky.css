﻿body,ul,ol,li,p,h1,h2,h3,h4,h5,h6,form,fieldset,table,td,img,div{margin:0;padding:0;border:0;}
body{color:#333; font-size:12px;font-family:"Microsoft YaHei"}
ul,ol{list-style-type:none;}
select,input,img,select{vertical-align:middle;}
input{ font-size:12px;}
a{ text-decoration:none; color:#000;}
a:hover{color:#c00; text-decoration:none;}
.clear{clear:both;}

/* 大转盘样式 */
.banner{display:block;width:95%;margin-left:auto;margin-right:auto;margin-bottom: 20px;}
.banner .turnplate{display:block;width:100%;position:relative;}
.banner .turnplate canvas.item{width:100%;}
.banner .turnplate img.pointer{position:absolute;width:31.5%;height:42.5%;left:34.6%;top:23%;}



/* 下面是分页显示效果  6个sheet的*/
.yh_header {
    background: #fff;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 3;
    display: -webkit-flex;
    display: flex;
}

.yh_header li { 
    width: 17%;
    text-align: center;
    position: relative;
}
.yh_header li a {
    color: #666;
    height: 40px;
    line-height: 40px;
}
a {
    color: #333;
    display: block;
}

.yh_header li.current {
    color: #FD567C;
}

.yh_header li.current a {
    color: #FD567C;
    padding: 0 5px;
    height: 40px;
    display: inline-block;
    line-height: 40px;
    border-bottom: 2px solid #FD567C;
    font-weight:bold;
}

.yh_header li:after {
    content: "";
    border-left: 1px solid #d9d9d9;
    height: 0px;
    line-height: 0;
    position: absolute;
    top: 25%;
    right: 0;
    height: 50%;
    -webkit-transform: scaleX(0.5);
    -webkit-transform-origin: 100% 0;
    transform: scaleX(0.5);
    transform-origin: 100% 0;
}
*, *:after, *:before {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
 /* 6个sheet的*/


/* 下面是分页显示效果   2个sheet的*/
.yh_header2 {
    background: #fff;
    position: fixed;
    top: 55px;
    left: 0;
    width: 100%;
    z-index: 3;
    display: -webkit-flex;
    display: flex;
}


.yh_header2 tr > td {  
    width: 50%;
    text-align: center;
    position: relative;
}

.yh_header2 tr > td a {
    color: #666;
    height: 40px;
    line-height: 40px;
}
a {
    color: #333;
    display: block;
}

.yh_header2 tr > td.current {
    color: #FD567C;
}

.yh_header2 tr > td.current a {
    color: #FD567C;
    padding: 0 5px;
    height: 40px;
    display: inline-block;
    line-height: 40px;
    border-bottom: 2px solid #FD567C;
    font-weight:bold;
}


.yh_header2 tr > td:after {
    content: "";
    border-left: 1px solid #d9d9d9;
    height: 0px;
    line-height: 0;
    position: absolute;
    top: 25%;
    right: 0;
    height: 50%;
    -webkit-transform: scaleX(0.5);
    -webkit-transform-origin: 100% 0;
    transform: scaleX(0.5);
    transform-origin: 100% 0;
}
*, *:after, *:before {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
/* 2个sheet的*/


/* 下面是分页显示效果  3个sheet的*/
.yh_header3 {
    background: #fff;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 3;
    display: -webkit-flex;
    display: flex;
}

.yh_header3 li { 
    width: 33%;
    text-align: center;
    position: relative;
}
.yh_header3 li a {
    color: #666;
    height: 40px;
    line-height: 40px;
}
a {
    color: #333;
    display: block;
}

.yh_header3 li.current {
    color: #FD567C;
}

.yh_header3 li.current a {
    color: #FD567C;
    padding: 0 5px;
    height: 40px;
    display: inline-block;
    line-height: 40px;
    border-bottom: 2px solid #FD567C;
    font-weight:bold;
}

.yh_header3 li:after {
    content: "";
    border-left: 1px solid #d9d9d9;
    height: 0px;
    line-height: 0;
    position: absolute;
    top: 25%;
    right: 0;
    height: 50%;
    -webkit-transform: scaleX(0.5);
    -webkit-transform-origin: 100% 0;
    transform: scaleX(0.5);
    transform-origin: 100% 0;
}
*, *:after, *:before {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
 /* 3个sheet的*/
 
 

/* 分页菜单在底部的 */
.yh_bottom {
    background: #F0FFFF;
    position:fixed;
    left: 0;
    bottom: 0px;
    width: 100%;
    z-index: 3;
    display: -webkit-flex;
    display: flex;
}

.yh_bottom li {
    width: 17%;
    text-align: center;
    position: relative;
}

.yh_bottom li a {
    color: #666;
    height: 50px;
    line-height: 40px;
}
a {
    color: #333;
    display: block;
}

.yh_bottom li.current {
    color: #FD567C;
}

.yh_bottom li.current a {
    color: #FD567C;
    padding: 0 5px;
    height: 40px;
    display: inline-block;
    line-height: 40px;
    border-bottom: 2px solid #FD567C;
    font-weight:bold;
    font-size:14px;
}


.yh_bottom li:after {
    content: "";
    border-left: 1px solid #d9d9d9;
    height: 0px;
    line-height: 0;
    position: absolute;
    top: 25%;
    right: 0;
    height: 50%;
    -webkit-transform: scaleX(0.5);
    -webkit-transform-origin: 100% 0;
    transform: scaleX(0.5);
    transform-origin: 100% 0;
}
*, *:after, *:before {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}