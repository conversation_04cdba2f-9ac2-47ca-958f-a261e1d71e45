﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量异常处理</title>
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/XC.css" rel="stylesheet" />
    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/Order.js" type="text/javascript"></script>


    <script type="text/javascript">
        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#PrdECodelist',
                id: 'PrdECodeID',
                url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=242',
                height: 'full-60',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'BatchExceptionNo', title: '批量不合格处理编号', width: 160, sort: true },
                    { field: 'Model', title: '型号', width: 150 },
                    { field: 'OrderNo', title: '工单号', width: 120, sort: true },
                    { field: 'ProductNo', title: '产品编码', width: 150, sort: true },
                    { field: 'ProductName', title: '产品名称', width: 200 },
                    { field: 'Status', title: '状态', width: 90 },
                    { field: 'DeptName', title: '部门', width: 100 },
                    { field: 'ProcedureName', title: '发生环节', width: 100 },
                    { field: 'BatchNum', title: '批数量', width: 90, sort: true },
                    { field: 'InMan', title: '录入人', width: 90 },
                    { field: 'InDate2', title: '录入时间', width: 150, sort: true },
                    { field: 'op', title: '操作', width: 100, toolbar: '#barDemo', fixed: 'right' }
                ]],
                page: true,
            });


            //监听行工具事件
            table.on('tool(PrdECodelist)', function (obj) {
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值

                if (layEvent == 'detail') {
                    $(".content-center-left").css("display", "none")
                    $(".content-center-left").css("width", "0%")
                    $(".content-center-right").css("width", "100%")
                    $("#ShowOne").css("display", "block")
                    $("#ShowOne-fade").css("display", "block")
                    $(".head-title1").html("批量异常详情")

                    $("#txtBatchExceptionNo").val(data.BatchExceptionNo)
                    $("#txtMaterBatchNo").val(data.MaterBatchNo)
                    $("#txtBatchNum").val(data.BatchNum)
                    $("#txtOrderNo").val(data.OrderNo)
                    $("#txtMaterNo").val(data.ProductNo)
                    $("#txtMaterName").val(data.ProductName)
                    $("#txtModel").val(data.Model)
                    $("#txtStatus").val(data.Status)
                    $("#txtFDate").val(data.InDate2)
                    $("#txtDiscoverer").val(data.InMan)
                    $("#txtProcedureName").val(data.ProcedureName)
                    $("#txtDepartment").val(data.DeptName)

                    $("#txtAbnormalCause").val(data.Cause)
                    $("#txtHandleWay").val(data.DealWay)

                    $("#txtAbnormalCause").attr("disabled", "disabled")
                    $("#txtHandleWay").attr("disabled", "disabled")

                    ShowSerialInfo(data.BatchExceptionNo, data.MaterBatchNo)

                    $("#SubmitBatchException").hide()
                    $("#SNInfo").show()
                    $("#HandleWay").show()
                    $("#TechList").hide()
                    $("#DeviceList").hide()
                    $("#QualityList").hide()
                    $("#FileList").hide()
                    $("#TestItem").hide()
                }
            })

            //  查询
            $('#PrdECode_open').click(function () {

                var sWO = $("#txtSOrder").val(); //工单
                var sMNo = $("#txtSMNo").val();  //产品编码
                var sBDate = $("#txtSBDate").val();  //开始时间
                var sEDate = $("#txtSEDate").val();  //结束时间

                var Data = '';
                var Params = { No: sWO, Item: "", Name: "", MNo: sMNo, MName: "", Status: "", BDate: sBDate, EDate: sEDate, A: "", B: "", C: "", D: "" };
                var Data = JSON.stringify(Params);

                table.reload('PrdECodeID', {
                    method: 'post',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=242&Data=' + Data,
                    where: {},
                    page: {
                        curr: 1
                    }
                });
            });



            $("#txtHandleWay").blur(function () {
                switch ($(this).val()) {
                    case "调整检验方式":
                        $("#SetSnspection_btn").show()
                        $("#TechAdd_btn").hide()
                        $("#TechSelect_btn").hide()
                        break;
                    case "返工":
                        $("#SetSnspection_btn").hide()
                        $("#TechAdd_btn").hide()
                        $("#TechSelect_btn").show()
                        break;
                    case "无需处理":
                        $("#SetSnspection_btn").hide()
                        $("#TechAdd_btn").hide()
                        $("#TechSelect_btn").hide()
                        $("#DeviceList").hide()
                        $("#QualityList").hide()
                        $("#FileList").hide()
                        $("#TestItem").hide()
                        break;
                }
            })

            var selectVal = "";
            $("#txtHandleWay").focus(function () {
                selectVal = $(this).val()
            })
            //处理方式选择后就自动保存
            $("#txtHandleWay").change(function () {
                var sCause = $("#txtAbnormalCause").val()
                var sHandleWay = $("#txtHandleWay").val()
                var sOrderNo = $("#txtOrderNo").val()
                var sBatchNo = $("#txtBatchNo").val()
                var sBatchException = $("#txtBatchExceptionNo").val()

                var Params = { No: sBatchNo, Item: sBatchException, Name: "", MNo: "", MName: "", A: "", B: sOrderNo, C: sCause, D: sHandleWay, InMan: "", Remark: "", Flag: "30-6" };
                var Data = JSON.stringify(Params);

                $.ajax({
                    url: "../Service/OrderAjax.ashx?OP=OPPrdSamplingInfo",
                    type: "POST",
                    data: {
                        Data: Data
                    },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);
                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('切换成功！');
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'NOTUPDATE') {
                            layer.msg(sBatchException + ' 单据已选择 ' + selectVal + ' 处理过，不能修改！');
                            $("#txtHandleWay").val(selectVal)
                        }
                        else {
                            ErrorMessage("系统出错1", 2000)
                        }
                    },
                    error: function () {
                        ErrorMessage("系统出错2", 2000)
                    }
                })

            })
        })

        //打开批量异常详情、处理
        function openDialog() {
            $(".content-center-left").css("display", "block")
            $(".content-center-left").css("width", "24%")
            $(".content-center-right").css("width", "76%")
            $("#ShowOne").css("display", "block")
            $("#ShowOne-fade").css("display", "block")
            $(".head-title1").html("批量异常处理")

            $("#txtBatchExceptionNo").val("")
            $("#txtMaterBatchNo").val("")
            $("#txtBatchNum").val("")
            $("#txtOrderNo").val("")
            $("#txtMaterNo").val("")
            $("#txtMaterName").val("")
            $("#txtModel").val("")
            $("#txtStatus").val("")
            $("#txtFDate").val("")
            $("#txtDiscoverer").val("")
            $("#txtProcedureName").val("")
            $("#txtDepartment").val("")

            $("#txtWorkUnitNo").val("")
            $("#txtWorkUnitName").val("")
            $("#txtScanInfo").val("")

            $("#txtAbnormalCause").removeAttr("disabled")
            $("#txtHandleWay").removeAttr("disabled")

            $("#SubmitBatchException").show()
            $("#SNInfo").hide()
            $("#HandleWay").hide()
            $("#TechList").hide()
            $("#DeviceList").hide()
            $("#QualityList").hide()
            $("#FileList").hide()
            $("#TestItem").hide()

            GetInMan()
        }

        //打开添加工艺路线弹层
        function openAddTechFlowDialog() {
            $("#ShowThree").css("display", "block")
            GetProcedureList()

        }

        //打开选择工艺路线弹层
        function openSelectTechFlowDialog() {
            $("#ShowFive").css("display", "block")

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#Productlist',
                    id: 'ProductlistID',
                    url: '../Service/TechAjax.ashx?OP=GetTechInfo&CFlag=111',
                    height: '500',
                    cellMinWidth: 80,
                    count: 50,
                    limit: 20,
                    limits: [10, 20, 30, 40, 50],
                    cols: [[
                        { field: 'TechNo', title: '产品代号', width: 70 },
                        { field: 'ProductNo', title: '产品编码', },
                        { field: 'ProductName', title: '产品名称', },
                        { field: 'ProcedureNo', title: '工序编号', width: 70, align: 'center' },
                        { field: 'ProcedureName', title: '工序名称', align: 'center' },
                        { field: 'ProcedureVer', title: '工序版本', width: 70, align: 'center' },
                        { field: 'FlowOrder', title: '顺序', width: 40, align: 'center' },
                        { field: 'ConditionNo', title: '行为编号', align: 'center' },
                        { field: 'ConditionName', title: '工序行为名称' },
                        { field: 'WorkUnit', title: '作业单元', width: 200 },
                        { field: 'VerStatus', title: '工序状态', width: 80, align: 'center' },
                        { field: 'Status', title: '流程状态', width: 80, align: 'center' },
                        { field: 'op', title: '操作', width: 65, toolbar: '#barDemoS', fixed: 'right' }
                    ]],
                    page: true,
                });

                table.on("tool(Productlist)", function (obj) {
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值

                    if (data.VerStatus == "禁用") {
                        layer.msg("该工序版本已禁用，不可添加");
                        return;
                    }
                    if (data.Status == "禁用") {
                        layer.msg("该产品流程已禁用，不可添加");
                        return;
                    }

                    var sOrderNo = $("#txtOrderNo").val()
                    var sMaterBatchNo = $("#txtMaterBatchNo").val()
                    var sBatchNo = $("#txtBatchNo").val()
                    var sInMan = $("#txtInMan").val()

                    if (layEvent == "select") {
                        layer.confirm('确定添加吗？添加后不可删除。', function (index) {
                            $("#loading-icon").show()
                            var Data = '';
                            var Params = { No: sBatchNo, Item: data.ProductNo, Name: "", MNo: "", MName: "", A: sMaterBatchNo, B: data.ProcedureNo, C: "", D: data.ProcedureVer, E: "", F: "", G: sOrderNo, H: "", I: "", J: "SELECT", InMan: sInMan, Remark: "", Flag: "30-4" };
                            var Data = JSON.stringify(Params);
                            $.ajax({
                                url: "../Service/OrderAjax.ashx?OP=OPPrdSamplingInfo&Data=" + encodeURI(Data),
                                data: {},
                                type: "GET",
                                success: function (res) {
                                    var parseJSON = jQuery.parseJSON(res);
                                    if (parseJSON.Msg == "Success") {
                                        layer.msg("添加成功")
                                        closeSelectTechFlowDialog()
                                        $("#loading-icon").hide()
                                        $("#Search_SerialFowBtn").click()
                                    }
                                    else {
                                        layer.msg(parseJSON.Msg)
                                    }
                                },
                                error: function () {
                                    $("#loading-icon").hide()
                                    layer.msg("系统出错")
                                }
                            })
                        });
                    }
                })

                //  查询 --
                $('#ProductBut_open').click(function () {

                    var stxtTechNo = $("#stxtTechNo").val()//产品代号
                    var sPNo = $("#txtSPNo").val();  //产品编号
                    var sPName = encodeURI($("#txtSPMName").val());  //工序名称
                    var sClassFlag = "";
                    if ($("#CH_SClassFlag").is(':checked')) {
                        sClassFlag = encodeURI("是");
                    }

                    var Data = '';
                    var Params = { No: "", Item: "", Name: sPName, MNo: sPNo, MName: "", Status: "", BDate: "", EDate: "", A: sClassFlag, B: stxtTechNo, C: "", D: "" };
                    var Data = JSON.stringify(Params);

                    table.reload('ProductlistID', {
                        method: 'post',
                        url: '../Service/TechAjax.ashx?OP=GetTechInfo&CFlag=111&Data=' + Data,
                        where: {
                            'No': sPNo,
                            'name': sPName
                        }, page: {
                            curr: 1
                        }
                    });
                });
            });
        }

        //打开设置抽烟方案、工序行为、检验方式弹窗
        function openSetSnspectionDialog() {
            $("#ShowSix").css("display", "block")
            $("#ShowSix-fade").css("display", "block")
            GetProcedureActionList()
            GetSamplingPlanList()
            $("#txtIspectionOrderNo").val($("#txtOrderNo").val())
            $("#txtIspectionMaterBatchNo").val($("#txtMaterBatchNo").val())
            $("#txtIspectionProcedureName").val($("#txtProcedureName").val())
        }

        //打开设置抽烟方案、工序行为、检验方式弹窗
        function closeSetIspectionDialog() {
            $("#ShowSix").css("display", "none")
            $("#ShowSix-fade").css("display", "none")
        }

        //打开选择测试项弹出层
        function openSelectTestItemDialog() {
            $("#ShowSeven").css("display", "block")

            var sBatchNo = $("#txtBatchNo").val()
            var TechNo = $("#txtTechNo").val()
            var ProcNo = $("#txtProcedureNo").val()
            var sProcVer = $("#txtProcedureVer").val()
            var PNo = sBatchNo + sProcVer;

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#CHGetTestItemList',
                    id: 'CHGetTestItemID',
                    url: '../Service/TechAjax.ashx?OP=GetTechInfo&CFlag=118&CNO=' + TechNo + '&Item=' + ProcNo + '&CMNO=' + PNo + "&CStatus=3",
                    height: '350',
                    cellMinWidth: 80,
                    count: 100, //数据总数 服务端获得
                    limit: 100, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [50, 100, 150, 200, 250], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'TechNo', title: '产品代号', width: 80 },
                        { field: 'NameCH', title: '检验项目', width: 300 },
                        { field: 'DescCH', title: '检验要求', width: 350 },
                        { field: 'ValueKind', title: '数据类型', minWidth: 90 },
                        { field: 'NNo', title: '', minWidth: 30 },
                        { field: 'op', title: '操作', width: 110, toolbar: '#barDemoGetTestIte', fixed: 'right' }
                    ]],
                    page: true,
                    done: function (res, curr, count) {
                        var that = this.elem.next();
                        res.data.forEach(function (item, index) {
                            if (item.NNo != "NO") { // = NO 说明没有选择的测试项
                                //$(".layui-table-body tbody tr[data-index='" + index + "']").css({ 'backgroundColor': "#c2f6e4" });
                                $(".layui-table-body tbody tr[data-index='" + index + "']").find('td[data-field="NNo"]').css({ 'color': "blue" });

                            }
                        });
                    }

                });

                //监听行工具事件
                table.on('tool(CHGetTestItemList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值


                    if (layEvent == 'chooge') {

                        if (data.NNo == "已选") {
                            layer.msg('该测试项已选择，无需再选。');
                            return;
                        }

                        var sWONo = $('#txtSOrderNo').val();  // 工单
                        var sSN = $('#txtSSerialNo').val();  // 序列号
                        var sMNo = $("#txtOMaterNo").val();  // 产品编码
                        var sTechNo = $("#txtTechNo").val();
                        var sTNo = $("#txtTProcNo").val(); // (05)总装
                        var sNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
                        var sName = sTNo.substr(sTNo.indexOf(")") + 1, sTNo.length);  //工序  (05)总装 截取字符串，字符位置
                        var sPVer = $("#txtTProVer").val(); // 工序版本
                        var sNVer = sNo + sPVer;
                        var sSpecNo = data.SpecNo;
                        var sNameCH = data.NameCH;
                        var sFlag = $('#txtWOSNFlag').val();  // WO:14-13   SN:14-14
                        var sSNo = $("#txtSeqSNo").val()
                        var sOPFlag = $("#txtSFlag").val();//因为添加到最后面、和添加到指定位置  共用了 14-14 ，所以需要通过这个标记区分，ZDWZ 添加到指定位置

                        var Data = '';
                        var Params = { No: sNVer, Name: sNameCH, Item: sWONo, MNo: sMNo, MName: "", A: sName, B: sPVer, C: "", D: "", E: sSpecNo, F: sSNo, H: "", I: sTechNo, J: sOPFlag, K: sNo, L: sSN, Remark: "", Flag: sFlag };
                        var Data = JSON.stringify(Params);

                        $.ajax({
                            type: "POST",
                            url: "../Service/OrderAjax.ashx?OP=OPOrderInfo&CFlag=" + sFlag,
                            data: { Data: Data },
                            success: function (data) {
                                var parsedJson = jQuery.parseJSON(data);

                                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                    layer.msg('添加成功！');

                                    if (sOPFlag == "ZDWZ") {
                                        GetTestItecloseDialog()
                                    }

                                    $('#CHGetTestItem_SearchOpen').click();

                                    if (sFlag == "14-13") { // 按工单
                                        $('#WOTestItem_SearchOpen').click();
                                    }
                                    else {
                                        $('#SNTestItem_SearchOpen').click();
                                    }
                                }
                                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                                    layer.msg('该工序版本已禁用，不能操作.');
                                }
                                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                                    layer.msg('该测试项已选择，无需再选。');
                                }
                                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_FlOWOVER') {
                                    layer.msg('该工序已完工，无需再选。');
                                }
                                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                                    layer.msg('不能在已测试中选择。');
                                    $('#SNTestItem_SearchOpen').click();
                                }
                                else {
                                    layer.msg('已添加，无需重复添加。');
                                }
                            },
                            error: function (data) {
                                layer.msg('添加失败2，请重试！');
                            }
                        });


                    }


                });


                //  查询
                $('#CHGetTestItem_SearchOpen').off('click').click(function () {

                    var sPNo = $("#txtTPNo").val();   // 产品编码
                    var sTechNo = $("#txtTechNo").val();
                    var sModel = "";//$("#txtModel").val();
                    var sTNo = $("#txtTProcNo").val();
                    var sProcNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
                    var sPName = encodeURI($("#txtSTPNoKind").val());  // 测试项目
                    var sVer = $("#txtTProVer").val();
                    var sPNVer = sPNo + sVer;  // 没有字段放了，只能合并一下了
                    var sWOSNF = "3";  // 模式查询序列号
                    if ($('#txtWOSNFlag').val() == "14-13") {  // 查询工单
                        sWOSNF = "2";
                    }

                    var Data = '';
                    var Params = { No: sTechNo, Item: sProcNo, Name: sModel, MNo: sPNVer, MName: "", Status: sWOSNF, BDate: "", EDate: "", A: sPName, B: "", C: "", D: "" };
                    var Data = JSON.stringify(Params);

                    table.reload('CHGetTestItemID', {
                        method: 'post',
                        url: '../Service/TechAjax.ashx?OP=GetTechInfo&CFlag=118&Data=' + Data,
                        where: {
                            'No': sTechNo
                        }, page: {
                            curr: 1
                        }
                    });
                });

            });

        }

        //打开修改测试项弹出层
        function openTestItemDialog() {
            $("#ShowEight").css("display", "block")
        }

        //关闭选择测试项弹出层
        function closeSelectTestItemDialog() {
            $("#ShowSeven").css("display", "none")
        }

        //关闭修改测试项弹出层
        function closeTestItemDialog() {
            $("#ShowEight").css("display", "none")
        }

        //关闭批量异常详情、处理
        function closeDialog() {
            $('#ShowOne').css("display", "none");
            $('#ShowOne-fade').css("display", "none");
        }

        //关闭不合格处理详情
        function closeDialogDetail() {
            $('#ShowTow').css("display", "none");
        }

        //关闭选择作业单元
        function ChoogeUnit_close() {
            $('#ShowFour').css("display", "none");
            $("#txtFactory").val("")
            $("#txtWorkshop").val("")
            $("#txtLine").val("")
        }

        //关闭添加工艺路线
        function closeDialogTechList() {
            $('#ShowThree').css("display", "none");
        }

        //关闭选择工艺路线
        function closeSelectTechFlowDialog() {
            $('#ShowFive').css("display", "none");
        }

        //获取异常的序列号
        function ShowSerialInfo(BatchExceptionNo, MaterBatchNo) {

            var Params = { No: BatchExceptionNo, Item: MaterBatchNo, Name: "", MNo: "", MName: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "" };
            var Data = JSON.stringify(Params);

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#UnusualSNInfo',
                    id: 'UnusualSNInfo',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=243&Data=' + Data,
                    height: 'full-300',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers', width: 80 },
                        { field: 'BatchNo', title: '序列号' },
                        { field: 'MaterBatchStatus', title: '抽样状态' },
                        { field: 'op', title: '操作', width: 80, toolbar: '#barDemo_UnusualSNInfo', fixed: 'right' }
                    ]],
                    page: true,
                });


                //监听行单击事件（双击事件为：rowDouble）
                table.on('row(UnusualSNInfo)', function (obj) {
                    obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                    obj.tr.addClass('layui-table-click');

                    var data = obj.data; //获得当前行数据
                    ShowSerialRelevInfo(data.BatchNo, data.OrderNo)
                    $("#TechList").show()
                    $("#DeviceList").hide()
                    $("#QualityList").hide()
                    $("#FileList").hide()
                    $("#TestItem").hide()
                    $("#txtBatchNo").val(data.BatchNo);
                    $("#txtFlowOrder").val("")
                });

                //监听行工具事件
                table.on('tool(UnusualSNInfo)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值
                    if (layEvent == 'detail') {

                        var Params = { No: data.BatchNo, Name: "", Item: data.OrderNo, MNo: "", MName: "", A: data.EXENo, B: data.ProcedureNo, C: "", D: "", E: "", F: "", Remark: "", Flag: "31-2" };
                        var Data = JSON.stringify(Params);

                        $.ajax({
                            type: "GET",
                            url: "../Service/OrderAjax.ashx?OP=GetWXInfo&CFlag=31-2",
                            data: { Data: Data },
                            success: function (data) {
                                var parsedJson = jQuery.parseJSON(data);
                                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                    $('#ShowTow').css("display", "block");
                                    var Str = eval(parsedJson.json)[0];

                                    $("#txtDPECode").val(Str.PECode);
                                    $("#txtDSerialNo").val(Str.BatchNo);
                                    $("#txtDOrderNo").val(Str.OrderNo);
                                    $("#txtDFNo").val(Str.MaterNo);
                                    $("#txtDFName").val(Str.MaterName);
                                    $("#txtDeptName").val(Str.DeptName);
                                    $("#txtDModel").val(Str.Model);
                                    $("#txtDGX").val("(" + Str.ProcedureNo + ")" + Str.ProcedureName);
                                    $("#txtDFDate").val(Str.InDate2);
                                    $("#txtDStatus").val(Str.Status);
                                    $("#txtDFMan").val(Str.FMan);
                                    $("#txtDOrderKind").val(Str.OrderKind);

                                    $("#txtDealType").val(Str.DealWay);
                                    $("#txtYUNNo").val(Str.YUNNo);
                                    $("#txtMaintainType").val(Str.MaintainType);
                                    $("#txtMaintainDesc").val(Str.MaintainDesc);
                                    $("#txtRepMaterNo").val(Str.RepMaterNo);
                                    $("#txtRepMaterName").val(Str.RepMaterName);

                                    ShowBLXXInfo(Str.PECode);  // 显示这个不合格单的不良现象
                                    ShowMaterInfo(Str.BatchNo, Str.EXENo); // 已安装的物料

                                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                                    //location.href = "Login.htm";
                                }
                                else {
                                    ErrorMessage("未找到对应的不合格处理单！", 2000)
                                }
                            },
                            error: function () {
                                ErrorMessage("系统出错2！", 2000)
                            }
                        })
                    }
                });
            });
        }

        //显示不良现象
        function ShowBLXXInfo(sNo) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#BLXXList',
                    id: 'BLXXID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-16&CNO=' + sNo,
                    height: '250',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'ECode', title: '不良现象代码', width: 150 },
                        { field: 'EName', title: '不良现象名称', width: 300 },
                        { field: 'EDesc', title: '不良现象描述', width: 150 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate2', title: '录入时间', width: 120 },
                        { field: 'op', title: '操作', width: 80, toolbar: '#barDemo_BLXX', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行单击事件（双击事件为：rowDouble）
                table.on('row(BLXXList)', function (obj) {
                    obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                    obj.tr.addClass('layui-table-click');

                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值

                    ShowBLYYInfo(data.PECode, data.ECode);

                });
            });
        }

        //显示不良原因
        function ShowBLYYInfo(sNo, sENo) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#BLYYList',
                    id: 'BLYYID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-17&CNO=' + sNo + "&Item=" + sENo,
                    height: '250',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'CNo', title: '不良原因代码', width: 150 },
                        { field: 'CName', title: '名称', width: 150 },
                        { field: 'CDesc', title: '描述', width: 200 },
                        { field: 'Location', title: '描述', width: 200 },
                        { field: 'CType', title: '不良原因类型', width: 120 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate2', title: '录入时间', width: 120 }
                    ]],
                    page: true
                });
            });
        }

        //显示这个序列号已安装的物料
        function ShowMaterInfo(sNo, sEXENo) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#MaterList',
                    id: 'MaterID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-19&CNO=' + sNo + "&Item=" + sEXENo,
                    height: '350',
                    cellMinWidth: 80,
                    count: 200, //数据总数 服务端获得
                    limit: 200, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [50, 100, 150, 200], //分页显示每页条目下拉选择
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'ProcedureName', title: '工序', width: 80 },
                        { field: 'MaterNo', title: '物料编码', width: 150 },
                        { field: 'MaterName', title: '物料名称', width: 180 },
                        { field: 'MaterBatch', title: '物料序列号', width: 120 },
                        { field: 'ConsumeNum', title: '数量', width: 50 },
                        { field: 'Kind', title: '类别', width: 50 },
                        { field: 'DealType', title: '旧料处理意见', minWidth: 100 }
                    ]],
                    page: true
                });
            });
        }

        // 序列号工艺路线
        function ShowSerialRelevInfo(sNo, sWO) {
            //序列号：工艺路线
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#OrderInfo_TechList_SN',
                    id: 'OrderInfo_TechList_SN',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=171&CNO=' + sNo + "&CKind=" + sWO,
                    height: 'full-400',
                    cellMinWidth: 80,
                    //toolbar: 'default',//工具栏
                    //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers' },
                        { field: 'SerialNo', title: '序列号', width: 120 },
                        { field: 'ProductNo', title: '产品编码', width: 120 },
                        { field: 'ProductName', title: '产品名称', width: 120 },
                        { field: 'ProcedureNo', title: '工序编号', width: 85, },
                        { field: 'ProcedureVer', title: '工序版本', width: 85, },
                        { field: 'ProcedureName', title: '工序名称', width: 120 },
                        { field: 'FlowOrder', title: '顺序', width: 55, },
                        { field: 'ConditionNo', title: '行为编号', width: 95, },
                        { field: 'ConditionName', title: '工序行为名称', width: 150 },
                        { field: 'WorkUnit', title: '作业单元', width: 200 },
                        { field: 'StartDate', title: '开始时间', width: 150 },
                        { field: 'EndDate', title: '结束时间', width: 150 },
                        { field: 'PStatus', title: '状态', width: 60 },
                        { field: 'OrderNo', title: '工单号', minWidth: 100 },
                        { field: 'InspectType', title: '检验方式', width: 120 },
                        { field: 'InspectScheme', title: '抽样方案', width: 180 },
                        { field: 'MaterBatchNo', title: '上层批次', width: 150 },
                        { field: 'Remark', title: '备注', width: 150 },
                        { field: 'InMan', title: '录入人', width: 90 },
                        { field: 'InDate2', title: '录入时间', width: 150 },
                    ]],
                    page: true
                });


                //监听行单击事件（双击事件为：rowDouble）
                table.on('row(OrderInfo_TechList_SN)', function (obj) {
                    obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                    obj.tr.addClass('layui-table-click');

                    var data = obj.data; //获得当前行数据

                    $("#txtFlowOrder").val(data.FlowOrder)
                    if ($(".head-title1").html() == "批量异常详情") {    //批量异常详情
                        //显示设置设备、测试项、质控品、工艺文件
                        ShowSNPRDInfo(data.SerialNo, data.OrderNo, data.ProcedureNo, data.ProcedureVer);
                    } else {                                            //批量异常处理
                        if ($("#txtHandleWay").val() != "无需处理" && $("#txtHandleWay").val() != "") {
                            //显示设置设备、测试项、质控品、工艺文件
                            ShowSNPRDInfo(data.SerialNo, data.OrderNo, data.ProcedureNo, data.ProcedureVer);
                        } else {
                            $("#DeviceList").hide()
                            $("#QualityList").hide()
                            $("#FileList").hide()
                            $("#TestItem").hide()
                        }
                    }
                });

                //  查询 --序列号流程
                $('#Search_SerialFowBtn').click(function () {

                    var sSerialNo = $("#txtBatchNo").val();  //序列号
                    var sOrderNo = $("#txtOrderNo").val();  //工单

                    table.reload('OrderInfo_TechList_SN', {
                        method: 'post',
                        url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=171&CNO=' + sSerialNo + '&CKind=' + sOrderNo,
                        page: {
                            curr: 1
                        }
                    });
                });
            });
        }

        function ShowSNPRDInfo(SerialNo, OrderNo, ProcedureNo, ProcedureVer) {
            ShowSNDeviceInfo(SerialNo + OrderNo, ProcedureNo + ProcedureVer);  // 设备信息
            ShowSNQualityInfo(SerialNo + OrderNo, ProcedureNo + ProcedureVer);  // 质控品信息
            ShowSNFileInfo(SerialNo + OrderNo, ProcedureNo + ProcedureVer);  // 显示工艺文件
            ShowSNTestItemInfo(SerialNo + OrderNo, ProcedureNo + ProcedureVer); // 显示测试
            $("#DeviceList").show();
            $("#QualityList").show();
            $("#FileList").show();
            $("#TestItem").show();
        }

        // 序列号的设备信息
        function ShowSNDeviceInfo(sNo, sProcNo) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#SNDeviceList',
                    id: 'SNDeviceID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-9&CNO=' + sNo + '&Item=' + sProcNo,
                    height: '180',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 50, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'DeviceKind', title: '类型', width: 120 },
                        { field: 'MaterNo', title: '设备分类编码', width: 200, sort: true },
                        { field: 'DeviceName', title: '名称', width: 300, sort: true },
                        //{ field: 'DeptName', title: '管理部门', minWidth: 130 },
                        //{ field: 'UseDate', title: '校准有效期', minWidth: 150 },
                        //{ field: 'Status', title: '状态', minWidth: 80 },
                        { field: 'DStatus', title: '状态', width: 150 },
                        { field: 'SMInDate', title: '扫描时间', width: 150 },
                        { field: 'InMan', title: '录入人', minWidth: 80 },
                        { field: 'InDate2', title: '录入时间', width: 150 },
                        //{ field: 'op', title: '操作', width: 80, toolbar: '#barDemo_SNDevice', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(SNDeviceList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值


                    if (layEvent == 'edit') {
                        $('#txtAddKind').val("9-1-2999");
                        $("#L_K").html("修改设备信息");
                    }
                    else if (layEvent == 'del') {

                        if (data.DStatus == "已扫描") {
                            layer.msg('该设备已扫描，不能删除！');
                            return
                        }

                        layer.confirm('真的删除设备信息么', function (index) {
                            //向服务端发送删除指令

                            var sWO = $("#txtSOrderNo").val();  // 工单号
                            var sSN = $('#txtSSerialNo').val();
                            var sProcNo = $('#txtWOCHProcNo').val();
                            var sProcName = $('#txtWOCHProcName').val();
                            var sProcVer = $('#txtWOCHProcVer').val();
                            var sFNo = data.MaterNo;
                            var sFlag = "14-4";

                            var Data = '';
                            var Params = { No: sProcNo, Name: "", Item: sSN, MNo: sFNo, MName: "", A: sProcVer, B: "", C: "", D: sWO, E: "", F: "", Remark: "", Flag: sFlag };
                            var Data = JSON.stringify(Params);

                            $.ajax({
                                type: "POST",
                                url: "../Service/OrderAjax.ashx?OP=OPSerialInfo&CFlag=" + sFlag,
                                data: { Data: Data },
                                success: function (data) {
                                    var parsedJson = jQuery.parseJSON(data);

                                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                        layer.msg('删除成功！');
                                        $('#Search_SerialDeviceBtn').click();
                                        obj.del(); //删除对应行（tr）的DOM结构，并更新缓存
                                        layer.close(index);
                                    }
                                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                                        layer.msg('该工序版本已禁用，不能操作！');
                                        $('#Search_SerialDeviceBtn').click();
                                    }
                                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                                        layer.msg('该设备已扫描，不能删除！');
                                        $('#Search_SerialDeviceBtn').click();
                                    }
                                    else {
                                        layer.msg('删除失败，请重试！');
                                        $('#Search_SerialDeviceBtn').click();
                                    }
                                },
                                error: function (data) {
                                    layer.msg('删除失败2，请重试！');
                                    $('#Search_SerialDeviceBtn').click();
                                }
                            });

                        }); // 删除
                    }

                });

                $("#Search_SerialDeviceBtn").off("click").click(function () {
                    table.reload('SNDeviceID', {
                        method: 'post',
                        url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-9&CNO=' + sNo + '&Item=' + sProcNo,
                        page: {
                            curr: 1
                        }
                    });
                })
            });


        }

        // 序列号的质控品信息
        function ShowSNQualityInfo(sNo, sProcNo) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#SNQualityList',
                    id: 'SNQualityID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-9-1&CNO=' + sNo + '&Item=' + sProcNo,
                    height: '180',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 50, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },

                        { field: 'MaterNo', title: '编码', width: 200, sort: true },
                        { field: 'QualityName', title: '名称', width: 300, sort: true },
                        //{ field: 'DeptName', title: '管理部门', minWidth: 130 },
                        //{ field: 'UseDate', title: '校准有效期', minWidth: 150 },
                        //{ field: 'Status', title: '状态', minWidth: 80 },
                        { field: 'InMan', title: '录入人', minWidth: 80 },
                        { field: 'InDate2', title: '录入时间', width: 150 },
                        //{ field: 'op', title: '操作', width: 80, toolbar: '#barDemo_SNQuality', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(SNQualityList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值


                    if (layEvent == 'edit') {
                        $('#txtAddKind').val("9-1-29999");
                        $("#L_K").html("修改质控品信息");
                    }
                    else if (layEvent == 'del') {

                        layer.confirm('真的删除质控品信息么', function (index) {
                            //向服务端发送删除指令

                            var sWO = $("#txtSOrderNo").val();  // 工单号
                            var sSN = $('#txtSSerialNo').val();
                            var sProcNo = $('#txtWOCHProcNo').val();
                            var sProcName = $('#txtWOCHProcName').val();
                            var sProcVer = $('#txtWOCHProcVer').val();
                            var sFNo = data.MaterNo;
                            var sFlag = "14-4-1";

                            var Data = '';
                            var Params = { No: sProcNo, Name: "", Item: sSN, MNo: sFNo, MName: "", A: sProcVer, B: "", C: "", D: sWO, E: "", F: "", Remark: "", Flag: sFlag };
                            var Data = JSON.stringify(Params);

                            $.ajax({
                                type: "POST",
                                url: "../Service/OrderAjax.ashx?OP=OPSerialInfo&CFlag=" + sFlag,
                                data: { Data: Data },
                                success: function (data) {
                                    var parsedJson = jQuery.parseJSON(data);

                                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                        layer.msg('删除成功！');
                                        //$('#Fin_SearchOpen').click();
                                        obj.del(); //删除对应行（tr）的DOM结构，并更新缓存
                                        layer.close(index);
                                    }
                                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                                        layer.msg('该工序版本已禁用，不能操作！');
                                        // $('#Fin_SearchOpen').click();
                                    }
                                    else {
                                        layer.msg('删除失败，请重试！');
                                        // $('#Fin_SearchOpen').click();
                                    }
                                },
                                error: function (data) {
                                    layer.msg('删除失败2，请重试！');
                                    // $('#Fin_SearchOpen').click();
                                }
                            });

                        }); // 删除
                    }

                });

            });
        }

        // 序列号的工艺文件
        function ShowSNFileInfo(sNo, sProcNo) {

            //工艺文件
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#SNFileList',
                    id: 'SNFileID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-10&CNO=' + sNo + '&Item=' + sProcNo,
                    height: '180',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'ProcedureName', title: '工序', width: 120 },
                        { field: 'ProcedureVer', title: '版本', width: 60, sort: true },
                        { field: 'FileNo', title: '文件编码', width: 150, sort: true },
                        { field: 'FileVer', title: '版本', minWidth: 50, sort: true },
                        { field: 'FileName', title: '文件名称', minWidth: 200 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate2', title: '录入时间', width: 150 },
                        { field: 'FilePath', title: '文件路径', minWidth: 400 },
                        //{ field: 'op', title: '操作', width: 130, toolbar: '#barDemo_SNFile', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(SNFileList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值


                    if (layEvent == 'edit') {
                        $('#txtAddKind').val("14-6");
                        $("#L_WOFile").html("修改序列号文件信息");

                        $("#txtWOFPNo").val(data.SerialNo); // 序列号
                        $("#txtWOFProNo").val(data.ProcedureNo);
                        $("#txtWOFProName").val(data.ProcedureName);
                        $("#txtWOFProVer").val(data.ProcedureVer);
                        $("#txtPath").val(data.FilePath);  //  文件的路径
                        $("#txtFile").val(data.DocName); //  文件的名称
                        $("#txtWOFileNo").val(data.FileNo);  // 工艺文件编号，报表表头的
                        $("#txtWOFileVer").val(data.FileVer);
                        $("#txtWOFileName").val(data.FileName);
                        $("#div_warningFile").html("");

                        $("#WOFileSave_Btn").hide();
                        $("#SNFileSave_Btn").show();

                        $("#btnWOFileload").attr({ "disabled": "disabled" });
                        $("#txtWOFileNo").attr({ "disabled": "disabled" });
                        $("#txtWOFileVer").attr({ "disabled": "disabled" });

                        var scrollTop = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
                        document.getElementById('Div_WOFile').style.top = (scrollTop + 30) + "px";

                        document.getElementById('Div_WOFile').style.display = 'block';
                    }
                    else if (layEvent == 'del') {

                        layer.confirm('真的删除工艺文件么', function (index) {
                            //向服务端发送删除指令 A: sPVer, B: sPath, C: sFile, D: sFNo, E: sFVer, F: sFName,

                            var sOrderNo = $('#txtSOrderNo').val();
                            var sWONo = data.SerialNo;
                            var sNo = data.ProcedureNo;
                            var sPVer = data.ProcedureVer;
                            var sFNo = data.FileNo;
                            var sFVer = data.FileVer;
                            var sFlag = "14-7";

                            var Data = '';
                            var Params = { No: sNo, Name: "", Item: sWONo, MNo: sOrderNo, MName: "", A: sPVer, B: "", C: "", D: sFNo, E: sFVer, F: "", Remark: "", Flag: sFlag };
                            var Data = JSON.stringify(Params);

                            $.ajax({
                                type: "POST",
                                url: "../Service/OrderAjax.ashx?OP=OPSerialInfo&CFlag=" + sFlag,
                                data: { Data: Data },
                                success: function (data) {
                                    var parsedJson = jQuery.parseJSON(data);

                                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                        layer.msg('删除成功！');
                                        //$('#Fin_SearchOpen').click();
                                        obj.del(); //删除对应行（tr）的DOM结构，并更新缓存
                                        layer.close(index);
                                    }
                                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                                        layer.msg('该文件已使用，不能操作！');
                                        // $('#Fin_SearchOpen').click();
                                    } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                                        layer.msg('该工序版本已禁用，不能操作！');
                                        // $('#Fin_SearchOpen').click();
                                    }
                                    else {
                                        layer.msg('删除失败，请重试！');
                                        // $('#Fin_SearchOpen').click();
                                    }
                                },
                                error: function (data) {
                                    layer.msg('删除失败2，请重试！');
                                    // $('#Fin_SearchOpen').click();
                                }
                            });

                        }); // 删除
                    }

                });

            });  // layui.use('table', function () {


        }

        // 序列号的测试项
        function ShowSNTestItemInfo(sNo, sProcNo) {

            //测试项
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;

                table.render({
                    elem: '#SNTestItemList',
                    id: 'SNTestItemID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=235&CNO=' + sNo + '&Item=' + sProcNo,
                    height: '600',
                    cellMinWidth: 80,
                    count: 100, //数据总数 服务端获得
                    limit: 100, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [20, 30, 40, 50, 100, 150, 200], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'NameCH', title: '检验项目', width: 180, sort: true },
                        { field: 'DescCH', title: '检验要求', width: 400, sort: true },  // 检验条件说明
                        //{ field: 'StandardCH', title: '检验要求', width: 300 },
                        { field: 'ValueKind', title: '数据类型', minWidth: 80 },
                        { field: 'Status', title: '状态', width: 80 },
                        //{ field: 'TestDate', title: '测试时间', width: 150 },
                        { field: 'NotNeed', title: '是否涉及', width: 80 },
                        { field: 'TestInspectFlag', title: '是否抽样', width: 80 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate2', title: '录入时间', width: 150 },
                        { field: 'op', title: '操作', width: 120, toolbar: '#barDemo_SNTestItem', fixed: 'right' }
                    ]],
                    page: true,
                    done: function (res, curr, count) {

                        if ($(".head-title1").html() == "批量异常详情") {
                            $('div[lay-id="SNTestItemID"] td[data-field="op"]').hide(); // 隐藏操作列
                            $('div[lay-id="SNTestItemID"] th[data-field="op"]').hide(); // 隐藏操作列
                        } else {
                            $('div[lay-id="SNTestItemID"] td[data-field="op"]').show(); // 显示操作列
                            $('div[lay-id="SNTestItemID"] th[data-field="op"]').show(); // 显示操作列
                        }
                    }
                });

                //监听行工具事件
                table.on('tool(SNTestItemList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值

                    if (layEvent == 'edit') {
                        if (data.RStatus == "已测试") {
                            layer.msg("该测试项已测试，不能修改！")
                            return
                        }

                        openTestItemDialog()


                        $("#txtTIPNo").val(data.SerialNo);
                        $("#txtTIProNo").val("(" + data.ProcedureNo + ")" + data.ProcedureName);
                        $("#txtTIProVer").val(data.ProcedureVer);
                        $("#txtSpecNo").val(data.SpecNo);
                        $("#txtNameCH").val(data.NameCH);  //
                        $("#txtDescCH").val(data.DescCH); //
                        $("#txtUnit").val(data.SpecUnit); // 单位
                        $("#txtStandardCH").val(data.StandardCH);
                        $("#txtValueKind").val(data.ValueKind); // 数据类型
                        $("#txtRangeKind").val(data.RangeKind);   // 请填写区间值
                        $("#txtDownV").val(data.ThisValue);   // 下限
                        $("#txtUpV").val(data.ToValue);   // 上限值
                        $("#CH_Down").removeProp("checked"); //设置为不选中状态
                        $("#CH_Up").removeProp("checked"); //设置为不选中状态


                        $("#div_QJ").hide();
                        if (data.ValueKind == "数值类型") {
                            $("#div_QJ").show();

                            if (data.IncludeOne == "1") {  //  包含下限值
                                $("#CH_Down").prop("checked", "checked");
                            }
                            if (data.IncludeTwo == "1") {  //  包含下限值
                                $("#CH_Up").prop("checked", "checked");
                            }

                            $("#CH_Down").hide();
                            $("#L_BHD").hide();
                            $("#txtDownV").hide();
                            $("#L_Down").hide();
                            $("#L_Deng").hide();
                            $("#L_Up").hide();
                            $("#txtUpV").hide();
                            $("#CH_Up").hide();
                            $("#L_BHU").hide();
                            if (data.RangeKind == "大于") {
                                $("#CH_Down").show();
                                $("#L_BHD").show();
                                $("#txtDownV").show();
                                $("#L_Down").show();
                            }
                            else if (data.RangeKind == "小于") {
                                $("#L_Up").show();
                                $("#txtUpV").show();
                                $("#CH_Up").show();
                                $("#L_BHU").show();
                            }
                            else if (data.RangeKind == "介于") {
                                $("#CH_Down").show();
                                $("#L_BHD").show();
                                $("#txtDownV").show();
                                $("#L_Down").show();
                                $("#L_Up").show();
                                $("#txtUpV").show();
                                $("#CH_Up").show();
                                $("#L_BHU").show();
                            }
                            else if (data.RangeKind == "等于") {
                                $("#L_Deng").show();
                                $("#txtUpV").show();
                            }
                        }


                    } else if (layEvent == "CY") {
                        var sOrderNo = $('#txtOrderNo').val();
                        var sNo = $("#txtBatchNo").val()
                        var sbatchNo = $("#txtMaterBatchNo").val();  // 序列号
                        var sFlowOrder = $("#txtFlowOrder").val()
                        var sPeocNo = data.ProcedureNo
                        var sPVer = data.ProcedureVer
                        var sSpecNo = data.SpecNo
                        var sFlag = "30-9";


                        var Data = '';
                        var Params = { No: sNo, Name: "", Item: "", MNo: "", MName: "", A: sbatchNo, B: sPeocNo, C: "", D: sPVer, E: sSpecNo, F: "", G: sOrderNo, H: "", I: "", J: "", Remark: sFlowOrder, Flag: sFlag };
                        var Data = JSON.stringify(Params);

                        $.ajax({
                            type: "POST",
                            url: "../Service/OrderAjax.ashx?OP=OPPrdSamplingInfo",
                            data: { Data: Data },
                            success: function (data) {
                                var parsedJson = jQuery.parseJSON(data);

                                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                                    layer.msg("提交成功!")

                                    ShowSNTestItemInfo(sNo + sOrderNo, sPeocNo + sPVer); // 显示测试

                                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                                    ErrorMessage("该测试记录已存在，请确认！", 2000)
                                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                                    ErrorMessage("该工序版本已禁用，不能操作！", 2000)
                                }
                                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_FlOWOVER') {
                                    ErrorMessage("该工序已完工，不能添加！", 2000)
                                }
                                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                                    ErrorMessage("测试项已测试，不可操作！", 2000)
                                }
                                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_FlOWPRD') {
                                    $("#TestItemSave_Btn").removeAttr("disabled");
                                    ErrorMessage("该工序已经生产了，不能操作！", 2000)
                                }
                                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_YCGX') {
                                    $("#TestItemSave_Btn").removeAttr("disabled");
                                    ErrorMessage("该工序不是异常处理添加的了，不可操作！", 2000)
                                }
                                else {
                                    ErrorMessage(parsedJson.Msg, 2000)
                                }
                            },
                            error: function (data) {
                                ErrorMessage("系统出错，请重试2！", 2000)
                            }
                        });
                    }

                });

                //  查询 --测试项信息
                $('#SNTestItem_SearchOpen').off('click').click(function () {

                    var sSN = $("#txtSSerialNo").val();
                    var sProcNo = $("#txtWOCHProcNo").val(); //
                    var sVer = $("#txtWOCHProcVer").val();
                    var sNV = sProcNo + sVer;

                    if (sSN == "") {
                        return;
                    }

                    var Data = '';
                    var Params = { No: sSN, Item: sNV, Name: "", MNo: "", MName: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "" };
                    var Data = JSON.stringify(Params);

                    table.reload('SNTestItemID', {
                        method: 'post',
                        url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=235&Data=' + Data,
                        where: {
                            'No': sSN,
                            'name': sProcNo
                        }, page: {
                            curr: 1
                        }
                    });
                });
            });

        }

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        //获取当前登录的用户
        function GetInMan() {
            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        $('#txtOPInMan').val(parsedJson.Man + "(" + parsedJson.InName + ")");
                        $('#txtInMan').val(parsedJson.Man);
                        $('#txtInName').val(parsedJson.InName);
                    }
                }
            })
        }

        //扫描作业单元
        function ScanUnit_keydown(event) {
            if (event.keyCode == 13) {

                $('#txtWorkUnitName').val(""); // 作业单元名称
                var sFlag = "28-2";

                var sWUnit = $('#txtWorkUnitNo').val();
                if (sWUnit == "") {
                    ErrorMessage("请输入作业单元！", 2000)
                    return;
                }

                var Data = '';
                var Params = { No: sWUnit, Name: "", Item: "", MNo: "", MName: "", A: "", B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);;

                $.ajax({
                    type: "POST",
                    url: "../Service/OrderAjax.ashx?OP=GetWXInfo&CFlag=" + sFlag + "&CNO=" + sWUnit,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            var sStr = eval(parsedJson.json)[0];
                            $('#txtWorkUnitName').val(parsedJson.BZBatch); // 作业单元名称
                            $("#txtScanInfo").removeAttr("disabled");
                            $("#txtScanInfo").focus();
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_EXIST') {
                            ErrorMessage("该作业单元不存在，请确认！", 2000)
                            $("#txtScanInfo").attr({ "disabled": "disabled" });
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_USEREXIST') {
                            ErrorMessage("该用户不能操作该作业单元，请确认！", 2000)
                            $("#txtScanInfo").attr({ "disabled": "disabled" });
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_USEEXIST') {
                            ErrorMessage("该作业单元已在生产中，请确认！", 2000)
                            $("#txtScanInfo").attr({ "disabled": "disabled" });
                        }
                        else {
                            ErrorMessage("系统错误，请确认该员工是否有该作业单元权限！", 2000)
                            $("#txtScanInfo").attr({ "disabled": "disabled" });
                        }
                    },
                    error: function (data) {
                        ErrorMessage("系统出错，请联系管理员2！", 2000)
                        $("#txtScanInfo").attr({ "disabled": "disabled" });
                    }
                });
            }
        }

        // 输入 扫描 序列号，回车
        function ScanSNCode_keydown(event) {
            if (event.keyCode == 13) {
                var sFlag = "31-1"

                var sCode = $('#txtScanInfo').val();
                var sUnit = $('#txtWorkUnitNo').val();  // 作业单元

                if (sCode == "") {
                    return;
                }
                if (sUnit == "") {
                    ErrorMessage("请输入作业单元！", 2000)
                    return;
                }


                var Data = '';
                var Params = { No: sCode, Name: "", Item: "", MNo: "", MName: "", A: "", B: "", C: "", D: "", E: sUnit, F: "", Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);

                $.ajax({
                    type: "POST",
                    url: "../Service/OrderAjax.ashx?OP=GetWXInfo&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                            $("#SNInfo").show()
                            $("#HandleWay").show()
                            $("#TechList").show()

                            var Str = eval(parsedJson.json)[0];
                            $("#txtBatchExceptionNo").val(Str.BatchExceptionNo)
                            $("#txtMaterBatchNo").val(Str.MaterBatchNo)
                            $("#txtBatchNum").val(Str.BatchSumNum)
                            $("#txtOrderNo").val(Str.OrderNo)
                            $("#txtMaterNo").val(Str.ProductNo)
                            $("#txtMaterName").val(Str.ProductName)
                            $("#txtModel").val(Str.Model)
                            $("#txtStatus").val(Str.Status)
                            $("#txtFDate").val(Str.InDate2)
                            $("#txtDiscoverer").val(Str.InMan)
                            $("#txtProcedureName").val(Str.ProcedureName)
                            $("#txtProcedureNo").val(Str.ProcedureNo)
                            $("#txtProcedureVer").val(Str.ProcedureVer)
                            $("#txtDepartment").val(Str.DeptName)
                            $("#txtBatchNo").val(Str.BatchNo)
                            $("#txtTechNo").val(Str.TechNo)
                            $("#txtAbnormalCause").val(Str.Cause)
                            $("#txtHandleWay").val(Str.DealWay)
                            $("#txtScanInfo").val("")
                            ShowSerialInfo(Str.BatchExceptionNo, Str.MaterBatchNo)
                            ShowSerialRelevInfo(Str.BatchNo, Str.OrderNo);

                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                            ErrorMessage("您未登陆系统，请先登录！", 2000)
                            //location.href = "Login.htm";
                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_NOTRepair') {
                            ErrorMessage("该序列号不需要维修！", 2000)
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_CREATE') {
                            ErrorMessage("该序列号对应不合格单还在创建状态，请到作业执行提交！", 2000)
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_ISOK') {
                            ErrorMessage("未找到需要处理的批量异常单据！", 2000)
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_UNITNOT') {
                            ErrorMessage("扫描的序列号当前工序和登录的作业单元不匹配！", 2000)
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_WXISOK') {
                            ErrorMessage("批量异常处理前必须先把维修以及返回工序处理完，请确认！", 2000)
                        }
                        else {
                            ErrorMessage("请确认扫描的序列号是否正确！", 2000)
                        }
                    },
                    error: function (data) {
                        ErrorMessage("系统出错，请重试2！", 2000)
                    }
                });
            }
        }

        //获取作业单元
        function ChoogeSNUnit(FWNo, FNo, Kind) {
            $('#ShowFour').css("display", "block");
            $("#InTable").empty();

            // 加载用户权限
            $.ajax({  //ww-01
                url: "../Service/TechAjax.ashx?OP=GetWorkUnit&CFlag=123&CSPNO=" + encodeURI(FWNo) + "&CNO=" + encodeURI(FNo) + "&Item=" + encodeURI(Kind),
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    var iy = 0;
                    var sCon = "";
                    var sStr = "";

                    for (var i = 0; i < parsedJson.length; i++) {

                        sCon = sCon + "<td> <input type='checkbox' value=" + parsedJson[i].WNo + " name='check' style='height:14px; width:14px;margin:0px;vertical-align:middle;'> " + parsedJson[i].FullName + "</input></td>";

                        iy = (i + 1) % 4;
                        if ((iy == 0) && (i >= 1)) {
                            sStr = sStr + "<tr style='height:25px; width:20%;font-size:12px;'>" + sCon + "</tr> ";
                            sCon = "";
                        }
                    }
                    if (iy > 0) {  // 说明最后一行是不满 4 个的，需要单独增加一行（一个tr）
                        sStr = sStr + "<tr style='height:25px; width:20%;font-size:12px;'>" + sCon + "</tr> ";
                    }

                    $("#InTable").append(sStr);
                }

            })


            GetWorkUnitConList("工厂", "");
        }

        // 工单，选择作业单元时：加载作业单号下的工厂
        function GetWorkUnitConList(Kind, FNo) {

            var keywords = encodeURI(Kind);  // 解决中文乱码的问题。
            var sNo = encodeURI(FNo);  // 解决中文乱码的问题。

            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/TechAjax.ashx?OP=GetWorkUnitConList&CFlag=111-3-1&CKind=" + keywords + "&CNO=" + sNo,
                success: function (data) {
                    var parsedJson = eval(data).Msg;

                    var sKong = "<option value=''> </option>";

                    if (Kind == "工厂") {
                        var sKind = $("#txtFactory").val();
                        $("#txtFactory").empty();
                        $("#txtFactory").append(sKong + parsedJson);
                        $("#txtFactory").val(sKind);
                    }
                    else if (Kind == "车间") {
                        var sKind = $("#txtWorkshop").val();
                        $("#txtWorkshop").empty();
                        $("#txtWorkshop").append(sKong + parsedJson);
                        $("#txtWorkshop").val(sKind);
                    }
                    else {  // 线体
                        var sKind = $("#txtLine").val();
                        $("#txtLine").empty();
                        $("#txtLine").append(sKong + parsedJson);
                        $("#txtLine").val(sKind);
                    }
                }
            });
        }

        //获取工厂
        function GetFactory() {
            var sFNo = encodeURI($("#txtFactory").val());
            $("#txtWorkshop").empty();
            $("#txtLine").empty();
            ChoogeSNUnit("", sFNo, "工厂");  // 显示这个工厂对应的作业单元
            GetWorkUnitConList("车间", sFNo);  // 显示这个工厂下的车间
        }

        //获取车间
        function GetWorkshop() {
            var sFNo = encodeURI($("#txtFactory").val());
            var sWNo = encodeURI($("#txtWorkshop").val());
            $("#txtLine").empty();

            ChoogeSNUnit(sFNo, sWNo, "车间");  // 显示这个车间对应的作业单元

            GetWorkUnitConList("线体", sWNo);  // 显示这个车间下的线体
        }

        //获取线体
        function GetLine() {
            var sFNo = encodeURI($("#txtWorkshop").val());
            var sWNo = encodeURI($("#txtLine").val());

            ChoogeSNUnit(sFNo, sWNo, "线体");  // 显示这个线体对应的作业单元
        }

        //选择的作业单元提交
        function SubmitUnit_Btn() {

            var selectUnit = selectedCheckboxes.join(";");

            if (selectUnit == "") {
                ErrorMessage("请选择作业单元！", 2000)
                return;
            }

            $("#txtCWUnit").val(selectUnit);

            $("#InTable").empty();

            selectedCheckboxes = []

            ChoogeUnit_close()
        }

        // 加载工序
        function GetProcedureList() {
            var keywords = encodeURI("工序");  // 解决中文乱码的问题。
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/TechAjax.ashx?OP=GetProcedureList&CFlag=111-1&CKind=" + keywords,
                success: function (data) {
                    var parsedJson = eval(data).Msg;
                    var sKong = "<option value=''> </option>";
                    // 工单流程
                    $("#txtProcNo").empty();
                    $("#txtProcNo").append(sKong + parsedJson);
                }
            });
        }

        // 加载工序行为
        function GetProcedureActionList() {
            var keywords = encodeURI("工序行为");  // 解决中文乱码的问题。
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/TechAjax.ashx?OP=GetProcedureActionList&CFlag=111-2&CKind=" + keywords,
                success: function (data) {
                    var parsedJson = eval(data).Msg;

                    var sKong = "<option value=''> </option>";
                    $("#txtConditionNo").empty();
                    $("#txtConditionNo").append(sKong + parsedJson);
                }
            });
        }

        // 获取抽样方案
        function GetSamplingPlanList() {
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/BaseModuleAjax.ashx?OP=GetSamplingPlanList&CFlag=60-1&CKind=",
                success: function (data) {
                    var sKong = "<option value=''></option>";
                    $("#txtInspectionlPlan").empty();
                    $("#txtInspectionlPlan").append(sKong + data.Msg);
                }
            });
        }

        //设置检验方式
        function SetIspection() {
            var sCondition = $("#txtConditionNo").val()
            var sPlanNo = $('#txtInspectionlPlan').val();
            var sSamplWay = $('#txtSamplWay').val();

            if (sCondition == "" || sCondition == null) {
                ErrorMessage("请选择工序行为！", 2000)
                return;
            }


            const str = sCondition.match(/\((.*?)\)(.*)/);
            var sConditionNo = str[1]
            var sConditionName = str[2]
            var sPlanName = $('#txtInspectionlPlan option:selected').text(); //抽样方案名称
            var sPlanNo = $('#txtInspectionlPlan').val();             //抽样方案编号

            var sOrderNo = $("#txtOrderNo").val()
            var sBatchNo = $("#txtBatchNo").val()
            var sMaterBatchNo = $("#txtMaterBatchNo").val()
            var sInMan = $("#txtInMan").val()
            var sProcNo = $("#txtProcedureNo").val()
            var sProcVer = $("#txtProcedureVer").val()
            var sWay = $("#txtHandleWay").val()
            var sBatchException = $("#txtBatchExceptionNo").val()

            var Params = { No: sBatchNo, Item: sWay, Name: sPlanName, MNo: "", MName: "", A: sMaterBatchNo, B: sProcNo, C: sSamplWay, D: sProcVer, E: sConditionNo, F: sConditionName, G: sOrderNo, H: sPlanNo, I: "", J: "COPY", InMan: sInMan, Remark: "", Flag: "30-4" };
            var Data = JSON.stringify(Params);

            layer.confirm("确定设置吗？设置后不可修改！", function () {
                $.ajax({
                    url: "../Service/OrderAjax.ashx?OP=OPPrdSamplingInfo&Data=" + encodeURI(Data),
                    data: {},
                    type: "POST",
                    success: function (res) {
                        var parsedJson = jQuery.parseJSON(res);
                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg("设置成功")
                            closeSetIspectionDialog()
                            $("#Search_SerialFowBtn").click()
                            $("#txtConditionNo").val("")
                            $('#txtInspectionlPlan').val("");
                            $('#txtSamplWay').val("");
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_NOTEST') {
                            layer.msg("工序测试项抽样必须先设置抽样的测试项，请切换工序版本确认所有工序版本都已设置！")
                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_FAFSNULL') {
                            layer.msg("抽样工序行为必须设置抽样方案和检验方式，请确认！")
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_FAFSNOTNULL') {
                            layer.msg("该工序没有设置抽样工序行为，不能设置抽样方案和检验方式，请确认！")
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_ISTEST') {
                            layer.msg("工序抽样的检验方式下无需设置测试项，请确认并取消所有工序版本的测试项！")
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_REPEATSET') {
                            layer.msg(sBatchException + "该单据已经设置过检验方式，请确认 ！");
                        }
                        else {
                            layer.msg(parseJSON.Msg)
                        }
                    },
                    error: function () {
                        layer.msg("系统出错")
                    }
                })
            })
        }

        //修改测试项
        function SaveTestItem() {
            $("#TestItemSave_Btn").attr({ "disabled": "disabled" });

            var sOrderNo = $('#txtOrderNo').val();
            var sWONo = $("#txtTIPNo").val()// 序列号
            var sbatchNo = $("#txtMaterBatchNo").val();
            var sTNo = $("#txtTIProNo").val();  //工序  (05)总
            var sNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
            var sPVer = $("#txtTIProVer").val();  //工序版本

            var sNCH = $("#txtNameCH").val();  //  检验项目
            var sDCH = $("#txtDescCH").val();  // 231101：修改为：检验要求       这个不用： 检验条件说明
            var sUnit = $("#txtUnit").val();  // 单位

            var sVKind = $("#txtValueKind").val();   // 数据类型
            var sRKind = $("#txtRangeKind").val();   // 请填写区间值

            var sDV = $("#txtDownV").val();   // 下限
            var sUV = $("#txtUpV").val();   // 上限值
            var sFlowOrder = $("#txtFlowOrder").val()
            var sSpecNo = $("#txtSpecNo").val(); // 测试项编号
            var sFlag = "30-8";
            var sDF = "";
            var sUF = "";

            if ($("#CH_Down").is(':checked')) {  //  包含下限值
                sDF = "1";
            }
            if ($("#CH_Up").is(':checked')) {  // 包含上限值
                sUF = "1";
            }


            if (sRKind == "大于") {
                if (sDV == "") {
                    $("#div_warningTestItem").html("请填写大于值！")
                    $("#div_warningTestItem").show();
                    $("#TestItemSave_Btn").removeAttr("disabled");
                    return;
                }
            }
            if (sRKind == "小于") {
                if (sUV == "") {
                    $("#div_warningTestItem").html("请填写小于值！")
                    $("#div_warningTestItem").show();
                    $("#TestItemSave_Btn").removeAttr("disabled");
                    return;
                }
            }
            if (sRKind == "介于") {
                if ((sDV == "") || (sUV == "")) {
                    $("#div_warningTestItem").html("请填写区间值！")
                    $("#div_warningTestItem").show();
                    $("#TestItemSave_Btn").removeAttr("disabled");
                    return;
                }
            }
            if (sRKind == "等于") {
                if (sUV == "") {
                    $("#div_warningTestItem").html("请填写等于值！")
                    $("#div_warningTestItem").show();
                    $("#TestItemSave_Btn").removeAttr("disabled");
                    return;
                }
            }

            if (sNCH == "") {
                $("#div_warningTestItem").html("请输入检验项目！")
                $("#div_warningTestItem").show();
                $("#TestItemSave_Btn").removeAttr("disabled");
                return;
            }
            if (sDCH == "") {
                $("#div_warningTestItem").html("请输入检验条件说明！")
                $("#div_warningTestItem").show();
                $("#TestItemSave_Btn").removeAttr("disabled");
                return;
            }
            if ((sVKind == "") || (sVKind == "null") || (sVKind == null)) {
                $("#div_warningTestItem").html("请选择数据类型！")
                $("#div_warningTestItem").show();
                $("#TestItemSave_Btn").removeAttr("disabled");
                return;
            }

            if (sDV != "") {
                if (isNaN(sDV)) {
                    $("#div_warningTestItem").html("请填写数值！")
                    $("#div_warningTestItem").show();
                    $("#TestItemSave_Btn").removeAttr("disabled");
                    return;
                }
            }
            if (sUV != "") {
                if (isNaN(sUV)) {
                    $("#div_warningTestItem").html("请填写数值！")
                    $("#div_warningTestItem").show();
                    $("#TestItemSave_Btn").removeAttr("disabled");
                    return;
                }
            }

            var Data = '';
            var Params = { No: sWONo, Name: sNCH, Item: sDCH, MNo: sDF, MName: sUF, A: sbatchNo, B: sNo, C: sDV, D: sPVer, E: sSpecNo, F: sUV, G: sOrderNo, H: sRKind, I: sVKind, J: sUnit, Remark: sFlowOrder, Flag: sFlag };
            var Data = JSON.stringify(Params);

            $.ajax({
                type: "POST",
                url: "../Service/OrderAjax.ashx?OP=OPPrdSamplingInfo",
                data: { Data: Data },
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);

                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                        layer.msg("提交成功!")
                        $("#TestItemSave_Btn").removeAttr("disabled");

                        ShowSNTestItemInfo(sWONo + sOrderNo, sNo + sPVer) // 显示测试
                        closeTestItemDialog()

                    } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                        $("#TestItemSave_Btn").removeAttr("disabled");
                        ErrorMessage("您未登陆系统，请先登录！", 2000)
                    } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                        $("#TestItemSave_Btn").removeAttr("disabled");
                        ErrorMessage("该测试记录已存在，请确认！", 2000)
                    } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                        $("#TestItemSave_Btn").removeAttr("disabled");
                        ErrorMessage("该工序版本已禁用，不能操作！", 2000)
                    }
                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_FlOWOVER') {
                        $("#TestItemSave_Btn").removeAttr("disabled");
                        ErrorMessage("该工序已完工，不能添加！", 2000)
                    }
                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                        $("#TestItemSave_Btn").removeAttr("disabled");
                        ErrorMessage("测试项已测试，不可操作！", 2000)
                    }
                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_FlOWPRD') {
                        $("#TestItemSave_Btn").removeAttr("disabled");
                        ErrorMessage("该工序已经生产了，不能操作！", 2000)
                    }
                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_YCGX') {
                        $("#TestItemSave_Btn").removeAttr("disabled");
                        ErrorMessage("该工序不是异常处理添加的了，不可操作！", 2000)
                    }
                    else {
                        $("#TestItemSave_Btn").removeAttr("disabled");
                        ErrorMessage(parsedJson.Msg, 2000)
                    }
                },
                error: function (data) {
                    $("#TestItemSave_Btn").removeAttr("disabled");
                    ErrorMessage("系统出错，请重试2！", 2000)
                }
            });
        }

        //提交批量异常处理单
        function SubmitBatchException() {
            var sOrderNo = $("#txtOrderNo").val()
            var sBatchException = $("#txtBatchExceptionNo").val()
            var sMaterBatchNo = $("#txtMaterBatchNo").val()
            var sBatchNo = $("#txtBatchNo").val()
            var sInMan = $("#txtInMan").val()
            var sProcNo = $("#txtProcedureNo").val()
            var sProcVer = $("#txtProcedureVer").val()
            var sRUnitNo = $("#txtWorkUnitNo").val()
            var sCause = $("#txtAbnormalCause").val()
            var sWay = $("#txtHandleWay").val()

            if (sBatchException == "") {
                ErrorMessage("获取不到需要处理单据！", 2000)
                return;
            }
            else if (sCause == "") {
                ErrorMessage("请输入分析原因 ！", 2000)
                return;
            }
            else if (sWay == "" || sWay == null) {
                ErrorMessage("请选择处理方式！", 2000)
                return;
            }



            var Params = { No: sBatchNo, Item: sBatchException, Name: "", MNo: "", MName: "", A: sMaterBatchNo, B: sProcNo, C: sCause, D: sProcVer, E: sWay, F: "", G: sOrderNo, H: sRUnitNo, I: "", J: "", InMan: sInMan, Remark: "", Flag: "30-5" };
            var Data = JSON.stringify(Params);

            $.ajax({
                url: "../Service/OrderAjax.ashx?OP=OPPrdSamplingInfo&Data=" + encodeURI(Data),
                data: {},
                type: "POST",
                success: function (res) {
                    var parsedJson = jQuery.parseJSON(res);
                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == "Success") {
                        layer.msg("处理完成")
                        closeDialog()
                        $('#PrdECode_open').click()
                    }
                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_NOTEST') {
                        layer.msg("工序测试项抽样必须先设置抽样的测试项，请确认！")
                    } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_FAFSNULL') {
                        layer.msg("抽样工序行为必须设置抽样方案和检验方式，请确认！")
                    }
                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_FAFSNOTNULL') {
                        layer.msg("该工序没有设置抽样工序行为，不能设置抽样方案和检验方式，请确认！")
                    }
                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_ISTEST') {
                        layer.msg("工序抽样的检验方式下无需设置测试项，请确认并取消所有工序版本的测试项！")
                    }
                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_SETCHECKWAY') {
                        layer.msg("请先设置检验方式，当前未设置，请确认！")
                    }
                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_BACKPROC') {
                        layer.msg("请先选择返工工序，当前未选择，请确认！");
                    }
                    else {
                        layer.msg(parseJSON.Msg)
                    }
                },
                error: function () {
                    layer.msg("系统出错")
                }
            })
        }
    </script>

    <style>
        #TechEdit_btn, #TechAdd_btn, #TechDel_btn, #TechSelect_btn, #SetSnspection_btn, #Search_SerialFowBtn {
            display: none
        }

        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }



        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }

        .content-center {
            display: flex;
            height: 100%
        }

        .content-center-left, .content-center-right {
            padding: 5px;
            height: 100%
        }

        .content-center-left {
            border: 1px #E5E5E5 solid;
            padding: 20px 20px 10px 0px;
        }

        .content-center-right td {
            font-size: 12px;
        }

        .content-center-right .XC-Input-block {
            margin-left: 5px;
            width: 100%;
        }

        .Head_Title {
            font-size: 13px;
            padding: 0px 5px;
            border-left: 5px #0c873d solid;
            margin: 10px 0px;
            height: 23px;
            line-height: 23px;
        }

        #ShowTow .XC-Input-block, #ShowThree .XC-Input-block {
            margin-left: 5px;
            width: 100%;
        }

        #div_SBLYY {
            margin: 10px 0px 10px 0px;
        }

        .Head_Title {
            display: flex;
            justify-content: space-between
        }
    </style>
</head>
<body>
    <div class="div_find">
        <p>
            <label class="find_labela">序列号：</label><input type="text" id="txtSOrder" class="find_input" />
            <label class="find_labela">产品编码：</label><input type="text" id="txtSMNo" class="find_input" />
            <label class="find_labela">提交时间：</label><input type="date" id="txtSBDate" class="find_input" style=" width:120px;" /><input type="date" id="txtSEDate" class="find_input" style=" width:120px;" />
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="PrdECode_open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="openDialog()">处理</button>
        </p>
        <p id="open" style="display:none;"></p>
    </div>
    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="PrdECodelist" lay-filter="PrdECodelist"></table>

        <script type="text/html" id="barDemo">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detail">详情</button>
        </script>
    </div>

    <!--批量不合格处理单详情、处理-->
    <div class="XC-modal XC-modal-xl" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title1">标题</span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <div class="content-center">
                    <div class="content-center-left">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">操作者</span>
                                <input type="text" class="XC-Input-block" id="txtOPInMan" name="txtOPInMan" readonly />
                                <input type="text" class="XC-Input-block" id="txtInMan" name="txtInMan" style="display:none" />
                                <input type="text" class="XC-Input-block" id="txtInName" name="txtInName" style="display:none" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">作业单元</span>
                                <input type="text" class="XC-Input-block" id="txtWorkUnitNo" name="txtWorkUnitNo" onkeydown="ScanUnit_keydown(event)" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">单元名称</span>
                                <input type="text" class="XC-Input-block" id="txtWorkUnitName" name="txtWorkUnitName" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">扫描信息</span>
                                <input type="text" class="XC-Input-block" id="txtScanInfo" name="txtScanInfo" onkeydown="ScanSNCode_keydown(event)" disabled=disabled />
                            </div>
                        </form>
                    </div>
                    <div class="content-center-right">
                        <table cellspacing="0" cellpadding="0" border='0' style="width: 100%;">
                            <tr style=" height:40px;">
                                <td style=" width:100px; text-align:right;">
                                    批量异常处理单号
                                </td>
                                <td>
                                    <input type="text" class="XC-Input-block" id="txtBatchExceptionNo" name="txtBatchExceptionNo" style="height: 28px;" readonly=readonly />
                                </td>
                                <td style=" width: 100px; text-align: right;">
                                    上层批号
                                </td>
                                <td>
                                    <input type="text" class="XC-Input-block" id="txtMaterBatchNo" name="txtMaterBatchNo" style="height: 28px;" readonly=readonly />
                                    <input type="text" class="XC-Input-block" id="txtBatchNo" name="txtBatchNo" style="height: 28px;display:none" readonly=readonly />
                                    <input type="text" class="XC-Input-block" id="txtTechNo" name="txtTechNo" style="height: 28px; display: none" readonly=readonly />
                                    <input type="text" class="XC-Input-block" id="txtFlowOrder" name="txtFlowOrder" style="height: 28px; display: none" readonly=readonly />
                                </td>
                                <td style=" width: 100px; text-align: right;">
                                    批量
                                </td>
                                <td>
                                    <input type="text" class="XC-Input-block" id="txtBatchNum" name="txtBatchNum" style="height: 28px;" readonly=readonly />
                                </td>
                            </tr>
                            <tr style=" height: 40px;">
                                <td style=" width: 100px; text-align: right;">
                                    工单
                                </td>
                                <td>
                                    <input type="text" class="XC-Input-block" id="txtOrderNo" name="txtOrderNo" style="height: 28px;" readonly=readonly />
                                </td>
                                <td style=" width: 100px; text-align: right;">
                                    物料编码
                                </td>
                                <td>
                                    <input type="text" class="XC-Input-block" id="txtMaterNo" name="txtMaterNo" style="height: 28px;" readonly=readonly />
                                </td>
                                <td style=" width: 100px; text-align: right;">
                                    物料描述
                                </td>
                                <td>
                                    <input type="text" class="XC-Input-block" id="txtMaterName" name="txtMaterName" style="height: 28px;" readonly=readonly />
                                </td>
                            </tr>
                            <tr style=" height: 40px;">
                                <td style=" width: 100px; text-align: right;">
                                    型号
                                </td>
                                <td>
                                    <input type="text" class="XC-Input-block" id="txtModel" name="txtModel" style="height: 28px;" readonly=readonly />
                                </td>
                                <td style=" width: 100px; text-align: right;">
                                    状态
                                </td>
                                <td>
                                    <input type="text" class="XC-Input-block" id="txtStatus" name="txtStatus" style="height: 28px;" readonly=readonly />
                                </td>
                                <td style=" width: 100px; text-align: right;">
                                    提交日期
                                </td>
                                <td>
                                    <input type="date" class="XC-Input-block" id="txtFDate" name="txtFDate" style="height: 28px;" readonly=readonly />
                                </td>
                            </tr>
                            <tr style=" height: 40px;">
                                <td style=" width: 100px; text-align: right;">
                                    发生环节
                                </td>
                                <td>
                                    <input type="text" class="XC-Input-block" id="txtProcedureName" name="txtProcedureName" style="height: 28px;" readonly=readonly />
                                    <input type="text" class="XC-Input-block" id="txtProcedureNo" name="txtProcedureNo" style="height: 28px;display:none" readonly=readonly />
                                    <input type="text" class="XC-Input-block" id="txtProcedureVer" name="txtProcedureVer" style="height: 28px; display: none" readonly=readonly />
                                </td>
                                <td style=" width: 100px; text-align: right;">
                                    发现人
                                </td>
                                <td>
                                    <input type="text" class="XC-Input-block" id="txtDiscoverer" name="txtDiscoverer" style="height: 28px;" readonly=readonly />
                                </td>
                                <td style=" width: 100px; text-align: right;">
                                    部门
                                </td>
                                <td>
                                    <input type="text" class="XC-Input-block" id="txtDepartment" name="txtDepartment" style="height: 28px;" readonly=readonly />
                                </td>
                            </tr>
                        </table>

                        <div id="SNInfo">
                            <div class="Head_Title">
                                序列号
                            </div>
                            <table class="layui-hide" id="UnusualSNInfo" lay-filter="UnusualSNInfo"></table>
                            <script type="text/html" id="barDemo_UnusualSNInfo">
                                <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detail">详情</button>
                            </script>
                        </div>

                        <div id="HandleWay">
                            <div class="Head_Title">
                                处置方案
                            </div>
                            <input type="text" class="XC-Input-block" id="txtAbnormalCause" name="txtAbnormalCause" style="height: 28px; width: 30%" placeholder="原因分析" />
                            <select class="XC-Select-block" id="txtHandleWay" name="txtHandleWay" style="height: 28px; width: 30%">
                                <option value=""></option>
                                <option value="无需处理">无需处理</option>
                                <option value="调整检验方式">调整检验方式</option>
                                <option value="返工">返工</option>
                            </select>
                        </div>

                        <div id="TechList">
                            <div class="Head_Title">
                                <div>工艺路线</div>
                                <div>
                                    <button id="Search_SerialFowBtn" class="XC-Btn-md XC-Btn-Green XC-Size-xs">刷新</button>
                                    <button id="TechAdd_btn" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="openAddTechFlowDialog()">添加</button>
                                    <button id="TechSelect_btn" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="openSelectTechFlowDialog()">选择</button>
                                    <button id="SetSnspection_btn" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="openSetSnspectionDialog()" style="width:90px">设置检验方式</button>
                                </div>
                            </div>
                            <table class="layui-hide" id="OrderInfo_TechList_SN" lay-filter="OrderInfo_TechList_SN"></table>
                        </div>

                        <div id="DeviceList">
                            <div class="Head_Title">
                                设备信息
                            </div>
                            <table class="layui-hide" id="SNDeviceList" lay-filter="SNDeviceList"></table>
                            <script type="text/html" id="barDemo_SNDevice">
                                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                                <a class="layui-btn layui-btn-xs" lay-event="add66666" style=" background-color:white;">.</a>
                            </script>
                        </div>

                        <div id="QualityList">
                            <div class="Head_Title">
                                质控品信息
                            </div>
                            <table class="layui-hide" id="SNQualityList" lay-filter="SNQualityList"></table>
                            <script type="text/html" id="barDemo_SNQuality">
                                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                                <a class="layui-btn layui-btn-xs" lay-event="add66666" style=" background-color:white;">.</a>
                            </script>
                        </div>

                        <div id="FileList">
                            <div class="Head_Title">
                                工艺文件
                            </div>
                            <table class="layui-hide" id="SNFileList" lay-filter="SNFileList"></table>
                            <script type="text/html" id="barDemo_SNFile">
                                <a class="layui-btn layui-btn-xs" lay-event="edit">修改</a>
                                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                            </script>
                        </div>

                        <div id="TestItem">
                            <div class="Head_Title">
                                <div>测试项信息</div>
                                <div>
                                    <button id="SNTestItem_SearchOpen" class="XC-Btn-md XC-Btn-Green XC-Size-xs" style="display:none">刷新</button>
                                    <!--<button id="TestItemSelect_btn" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="openSelectTestItemDialog()">选择</button>-->
                                </div>
                            </div>
                            <table class="layui-hide" style="padding:0px; margin:0px;" id="SNTestItemList" lay-filter="SNTestItemList"></table>
                            <script type="text/html" id="barDemo_SNTestItem">
                                <button class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="edit">修改</button>
                                <button class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="CY">抽样</button>
                                <!--<button class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="select">选择</button>
                                <button class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>-->
                            </script>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtEditKind" name="txtEditKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="SubmitBatchException" onclick="SubmitBatchException()">提交</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="closeDialog" onclick='closeDialog()'>关闭</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>

    <!--不合格处理单详情-->
    <div class="XC-modal XC-modal-xl" id="ShowTow">
        <div class="XC-modal-head">
            <span class="head-title2">不合格处理单详情</span>
            <span class="head-close" onclick="closeDialogDetail()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <table cellspacing="0" cellpadding="0" border='0' style="width: 99%;">
                    <tr style=" height:40px;">
                        <td style=" width:100px; text-align:right;">
                            不合格处理单
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtDPECode" name="txtDPECode" style="height: 28px;" readonly=readonly />
                        </td>
                        <td style=" width:100px; text-align:right;">
                            序列号
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtDSerialNo" name="txtDSerialNo" style="height: 28px;" readonly=readonly />
                        </td>
                        <td style=" width:100px; text-align:right;">
                            工单号
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtDOrderNo" name="txtDOrderNo" style="height: 28px;" readonly=readonly />
                        </td>
                    </tr>
                    <tr style=" height: 40px;">
                        <td style=" width:100px; text-align:right;">
                            产品编码
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtDFNo" name="txtDFNo" style="height: 28px;" readonly=readonly />
                        </td>
                        <td style=" width:100px; text-align:right;">
                            产品描述
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtDFName" name="txtDFName" style="height: 28px;" readonly=readonly />
                        </td>
                        <td style=" width:100px; text-align:right;">
                            部门
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtDeptName" name="txtDeptName" style="height:28px;" readonly=readonly />
                        </td>
                    </tr>
                    <tr style=" height: 40px;">
                        <td style=" width:100px; text-align:right;">
                            型号
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtDModel" name="txtDModel" style="height: 28px;" readonly=readonly />
                        </td>
                        <td style=" width:100px; text-align:right;">
                            发生环节
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtDGX" name="txtDGX" style="height: 28px;" readonly=readonly />
                        </td>
                        <td style=" width:100px; text-align:right;">
                            日期
                        </td>
                        <td>
                            <input type="date" class="XC-Input-block" id="txtDFDate" name="txtDFDate" style="height: 28px;" readonly="readonly" />
                        </td>
                    </tr>
                    <tr style=" height: 40px;">
                        <td style=" width:100px; text-align:right;">
                            状态
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtDStatus" name="txtDStatus" style="height: 28px;" readonly=readonly />
                        </td>
                        <td style=" width:100px; text-align:right;">
                            发现人
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtDFMan" name="txtDFMan" style="height: 28px;" readonly="readonly" />
                        </td>
                        <td style=" width:100px; text-align:right;">
                            作业类型
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtDOrderKind" name="txtDOrderKind" style="height: 28px;" />
                        </td>
                    </tr>
                </table>

                <div id="div_SBLYY" style="font-weight: bold; color: blue;">
                    不良现象
                </div>
                <div class="wangid_conbox">
                    <table class="layui-hide" id="BLXXList" lay-filter="BLXXList"></table>
                </div>

                <div id="div_SBLYY" style="font-weight: bold; color: blue;">
                    不良原因
                </div>
                <div class="wangid_conbox" id="Div_BLYY">
                    <table class="layui-hide" id="BLYYList" lay-filter="BLYYList"></table>
                </div>

                <div id="div_SBLYY" style="font-weight: bold; color: blue;">
                    处理方式
                    <input type="text" class="XC-Input-block" id="txtDealType" name="txtDealType" style="height: 28px; width: 10%; display: inline-block " readonly="readonly" />
                    <input type="text" class="XC-Input-block" id="txtYUNNo" name="txtYUNNo" style="height: 28px; width: 25%; display: inline-block " readonly="readonly" />
                </div>

                <div id="div_SBLYY" style="font-weight: bold; color: blue;">
                    维修方式
                    <input type="text" class="XC-Input-block" id="txtMaintainType" name="txtMaintainType" style="height: 28px; width: 10%; display: inline-block " readonly="readonly" />
                    <input type="text" class="XC-Input-block" id="txtMaintainDesc" name="txtMaintainDesc" style="height: 28px; width: 25%; display: inline-block " readonly="readonly" />
                    <input type="text" class="XC-Input-block" id="txtRepMaterNo" name="txtRepMaterNo" style="height: 28px; width: 25%; display: inline-block " readonly="readonly" />
                    <input type="text" class="XC-Input-block" id="txtRepMaterName" name="txtRepMaterName" style="height: 28px; width: 25%; display: inline-block " readonly="readonly" />
                </div>

                <div id="div_SBLYY" style="font-weight: bold; color: blue;">
                    物料信息
                </div>

                <div class="wangid_conbox" id="Div_MaterInfo">
                    <table class="layui-hide" id="MaterList" lay-filter="MaterList"></table>
                </div>
            </div>
        </div>


        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="closeDialogDetail" onclick='closeDialogDetail()'>关闭</button>
            </div>
        </div>
    </div>


    <!--添加工艺路线-->
    <div class="XC-modal XC-modal-xl" id="ShowThree">
        <div class="XC-modal-head">
            <span class="head-title3">标题</span>
            <span class="head-close" onclick="closeDialogTechList()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <table cellspacing="0" cellpadding="0" border='0' style="width:98%; ">

                    <tr style=" height: 40px;">
                        <td style=" width:100px; text-align:right;">
                            产品编码
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtProductNo" name="txtProductNo" style="height:28px;" />
                        </td>
                    </tr>
                    <tr style="height: 40px; ">
                        <td style=" width:100px; text-align:right;">
                            产品名称
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtProductName" name="txtProductName" style="height:28px;" readonly=readonly />
                        </td>
                    </tr>
                    <tr style="height: 40px; ">
                        <td style=" width:100px; text-align:right;">
                            工序
                        </td>
                        <td>
                            <select class="XC-Input-block" style="height:28px;" id="txtProcNo">
                                <option></option>
                            </select>
                        </td>
                    </tr>

                    <tr style="height: 40px; ">
                        <td style=" width:100px; text-align:right;">
                            工序版本
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtProcVer" name="txtProcVer" style="height:28px;" />
                        </td>
                    </tr>
                    <tr style="height: 40px; ">
                        <td style=" width:100px; text-align:right;">
                            工序顺序
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtFlowOrder" name="txtFlowOrder" style="height:28px;" readonly=readonly placeholder="系统自动产生" />
                        </td>
                    </tr>
                    <!--<tr style="height: 40px; ">
                        <td style=" width:100px; text-align:right;">
                            工序行为
                        </td>
                        <td>
                            <select class="XC-Input-block" style="height:28px;" id="txtConditionNo">
                                <option></option>
                            </select>
                        </td>
                    </tr>-->
                    <tr style=" height: 40px;">
                        <td style=" width:100px; text-align:right;">
                            作业单元
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtCWUnit" name="txtCWUnit" readonly=readonly style="width:90%" />
                            <input type='button' id="ChoogeUnit_Btn" value='选' onclick='ChoogeSNUnit("","","工厂")' class="XC-Btn-md XC-Btn-Green XC-Size-xs" style="width: 5%; height: 28px;" />
                        </td>
                    </tr>
                    <tr>
                        <td style=" width:80px; text-align:right;">
                            版本说明
                        </td>
                        <td>
                            <textarea class="XC-Input-block" id="txtVerDesc" name="txtVerDesc" style="height:80px;"> </textarea>
                        </td>
                    </tr>
                </table>
            </div>
        </div>


        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="SubmitUnit_Btn" onclick="SubmitUnit_Btn()">提交</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="closeDialogTechList" onclick='closeDialogTechList()'>关闭</button>
            </div>
        </div>
    </div>

    <!-- 选择作业单元-->
    <div class="XC-modal XC-modal-xl" id="ShowFour">
        <div class="XC-modal-head">
            <span class="head-title4">请选择作业单元</span>
            <span class="head-close" onclick="ChoogeUnit_close()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <form style="display:flex;margin-bottom:15px">
                    <div style="flex:1">
                        <span class="XC-Span-Select-block">工厂</span>
                        <select class="XC-Select-block" style="height:28px;width:80%" id="txtFactory" onchange="GetFactory()">
                            <option></option>
                        </select>
                    </div>
                    <div style="flex:1">
                        <span class="XC-Span-Select-block">车间</span>
                        <select class="XC-Select-block" style="height: 28px; width: 80%" id="txtWorkshop" onchange="GetWorkshop()">
                            <option></option>
                        </select>
                    </div>
                    <div style="flex:1">
                        <span class="XC-Span-Select-block">线体</span>
                        <select class="XC-Select-block" style="height: 28px; width: 80%" id="txtLine" onchange="GetLine()">
                            <option></option>
                        </select>
                    </div>
                </form>
                <table id="InTable" style=" width:100%;">
                </table>
            </div>
        </div>


        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="SubmitUnit_Btn" onclick="SubmitUnit_Btn()">提交</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="ChoogeUnit_close" onclick='ChoogeUnit_close()'>关闭</button>
            </div>
        </div>
    </div>


    <!--选择添加工艺路线-->
    <div class="XC-modal XC-modal-xl" id="ShowFive">
        <div class="XC-modal-head">
            <span class="head-title5">添加工艺路线</span>
            <span class="head-close" onclick="closeSelectTechFlowDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <div class="div_find">
                    <label class="find_labela">产品代号：</label> <input type="text" id="stxtTechNo" class="find_input" />
                    <label class="find_labela">产品编码：</label> <input type="text" id="txtSPNo" class="find_input" />
                    <label class="find_labela">工序名称：</label><input type="text" id="txtSPMName" class="find_input" />
                    <label class="find_labela">是否典型产品：</label><input type="checkbox" id="CH_SClassFlag" style=" width:35px; width:35px;" />
                    <input type="button" value="搜索" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="ProductBut_open" />
                </div>
                <div class="wangid_conbox">
                    <script type="text/html" id="barDemoS">
                        <button class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="select">添加</button>
                        <a class="layui-btn layui-btn-xs" lay-event="" style="display:none"></a>
                    </script>
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="Productlist" lay-filter="Productlist"></table>
                </div>

                <div id="loading-icon" class="black_overlay" style="z-index: 29991021;">
                    <div style="position: fixed;  top: 50%;  left: 50%;  transform: translate(-50%, -50%); ">
                        <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style=" -webkit-animation-name: fadeIn !important; animation-name: fadeIn !important;"></i>
                    </div>
                    <div class="docs-icon-name" style="position: fixed;  top: 55%;  left: 50%;  transform: translate(-46.5%, -50%);font-size:12px " id="loading-text">正在添加中，请稍等！</div>
                </div>
            </div>
        </div>
    </div>

    <!--设置抽样方案、工序行为-->
    <div class="XC-modal XC-modal-md" id="ShowSix" style="height:390px;z-index:1005">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title6">设置检验方式</span>
            <span class="head-close" onclick="closeSetIspectionDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <form class="XC-Form-block">
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">工单</span>
                    <input class="XC-Input-block" style="height:28px;" id="txtIspectionOrderNo" disabled />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">上层批次</span>
                    <input class="XC-Input-block" style="height:28px;" id="txtIspectionMaterBatchNo" disabled />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">工序</span>
                    <input class="XC-Input-block" style="height:28px;" id="txtIspectionProcedureName" disabled />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">工序行为<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <select class="XC-Select-block" style="height:28px;" id="txtConditionNo">
                        <option></option>
                    </select>
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">抽样方案</span>
                    <select class="XC-Select-block" id="txtInspectionlPlan" style="height: 28px;"></select>
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">检验方式</span>
                    <select class="XC-Select-block" id="txtSamplWay" style="height: 28px;">
                        <option value=""></option>
                        <option value="工序抽样">工序抽样</option>
                        <option value="工序测试项抽样">工序测试项抽样</option>
                    </select>
                </div>
            </form>
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="SetIspection_Btn" onclick="SetIspection()">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeSetIspectionDialog()">取消</button>
            </div>
        </div>
    </div>
    <div id="ShowSix-fade" class="black_overlay" style="z-index:1003">
    </div>


    <div class="XC-modal XC-modal-xl" id="ShowSeven">
        <div class="XC-modal-head">
            <span class="head-title5">选择测试项</span>
            <span class="head-close" onclick="closeSelectTestItemDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <div class="div_find">
                    <label class="find_labela">单号</label> <input type="text" id="txtTPNo" class="find_input" readonly="readonly" />
                    <label class="find_labela">工序</label> <input type="text" id="txtTProcNo" class="find_input" readonly="readonly" />
                    <label class="find_labela">工序版本</label><input type="text" id="txtTProVer" class="find_input" readonly="readonly" />
                    <label class="find_labela">查询条件</label><input type="text" id="txtSTPNoKind" class="find_input" />
                    <input type="button" value="搜索" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="CHGetTestItem_SearchOpen" />
                </div>
                <div class="wangid_conbox">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="CHGetTestItemList" lay-filter="CHGetTestItemList"></table>

                    <script type="text/html" id="barDemoGetTestIte">
                        <button class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="select">选择</button>
                    </script>
                </div>
            </div>
        </div>
    </div>


    <div class="XC-modal XC-modal-xl" id="ShowEight">
        <div class="XC-modal-head">
            <span class="head-title5">修改序列号测试信息</span>
            <span class="head-close" onclick="closeTestItemDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <table cellspacing="0" cellpadding="0" border='0' style="width:98%; ">
                    <tr style=" height:40px;">
                        <td style=" width:120px; text-align:center;">
                            编号
                        </td>
                        <td>
                            <input type="text" class="form-control" id="txtTIPNo" name="txtTIPNo" style=" height:30px; width:90%;" readonly="readonly" />
                        </td>
                        <td style=" width:120px; text-align:center;">
                            工序
                        </td>
                        <td>
                            <input type="text" class="form-control" id="txtTIProNo" name="txtTIProNo" style=" height:30px; width:90%;" readonly="readonly" />
                            <input type="text" class="form-control" id="txtSeqNo" name="txtSeqNo" style=" height:30px; width:50px;display:none;" />
                            <input type="text" class="form-control" id="txtSpecNo" name="txtSpecNo" style=" height:30px; width:50px;display:none;" />
                        </td>
                        <td style=" width:120px; text-align:center;">
                            工序版本
                        </td>
                        <td>
                            <input type="text" class="form-control" id="txtTIProVer" name="txtTIProVer" style=" height:30px; width:90%;" readonly="readonly" />
                        </td>
                    </tr>
                    <tr style=" height:40px;">
                        <td style=" width:120px; text-align:center;">
                            检验项目
                        </td>
                        <td colspan="5">
                            <textarea class="form-control" id="txtNameCH" name="txtNameCH" style="height: 70px; width: 99%;"> </textarea>
                        </td>
                    </tr>
                    <tr style=" height:162px;">
                        <td style=" width:120px; text-align:center;">
                            检验要求
                        </td>
                        <td colspan="5">
                            <textarea class="form-control" id="txtDescCH" name="txtDescCH" style="height: 160px; width: 99%;"> </textarea>
                        </td>
                    </tr>
                    <tr style=" height:40px;display:none;">
                        <td style=" width:120px; text-align:center;">
                            检验要求333
                        </td>
                        <td colspan="5">
                            <textarea class="form-control" id="txtStandardCH" name="txtStandardCH" style="height: 80px; width: 99%;"> </textarea>
                        </td>
                    </tr>
                    <tr style=" height:40px;">
                        <td style=" width:120px; text-align:center;">
                            单位
                        </td>
                        <td colspan="5">
                            <input type="text" class="form-control" id="txtUnit" name="txtUnit" style=" height: 30px; width: 99%;" />
                        </td>
                    </tr>
                    <tr style=" height:40px;">
                        <td style=" width:120px; text-align:center;">
                            数据类型
                        </td>
                        <td colspan="5">
                            <select class="form-control" id="txtValueKind" style="width:99%;">
                                <option value="文本类型">文本类型</option>
                                <option value="数值类型">数值类型</option>
                                <option value="布尔类型">布尔类型</option>
                                <option value="日期类型">日期类型</option>
                            </select>
                        </td>
                    </tr>

                </table>
                <div id="div_QJ" style="width:99%;display:none; ">
                    <hr />
                    <table cellspacing="0" cellpadding="0" border='0' style="width:98%; ">
                        <tr style=" height:30px;">
                            <td style=" width: 120px; color: blue;">
                                请填写区间值：
                            </td>
                            <td style=" width:120px; text-align:center;">
                                <select class="form-control" id="txtRangeKind" style="width:99%;height:25px;">
                                    <option value=""></option>
                                    <option value="大于">大于</option>
                                    <option value="小于">小于</option>
                                    <option value="介于">介于</option>
                                    <option value="等于">等于</option>
                                </select>
                            </td>
                            <td style="width:10px;">
                            </td>
                            <td>
                                <input type="checkbox" id="CH_Down" /> <label id="L_BHD"> 包含下限 </label>
                                <input type="text" id="txtDownV" name="txtDownV" style=" height:25px; width:55px" />
                                <label id="L_Down"> < </label>
                                <label style="color:blue;font-size:14px;"> 测试值 </label>
                                <label id="L_Deng" style="display:none;"> = </label>
                                <label id="L_Up"> < </label>
                                <input type="text" id="txtUpV" name="txtUpV" style=" height:25px; width:55px" />
                                <input type="checkbox" id="CH_Up" /><label id="L_BHU"> 包含上限 </label>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="XC-modal-footer">
                    <div class="OPBtn">
                        <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="TestItemSave_Btn" onclick="SaveTestItem()">保存</button>
                        <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="TestItemSave_Close" onclick="closeTestItemDialog()">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>
</html>