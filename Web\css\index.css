﻿/*清除默认样式  并设置简单样式*/
body {
    margin: 0;
    padding: 0;
    font-size: 16px;
    background: #CDCDCD;
}

.header {
    height: 50px;
    background: #333;
    /*background: rgba(47,47,47,0.98);*/
}

.header .box,.content{
    width: 700px;
    padding: 0 10px;
    margin: 0 auto;
}
/*.content{
    margin: 0 auto;
}*/

label {
    float: left;
    width: 100px;
    line-height: 50px;
    color: #DDD;
    font-size: 24px;
    /*鼠标悬停样式 一只手*/
    cursor: pointer;
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
}

.header input {
    float: right;
    width: 60%;
    height: 24px;
    margin-top: 12px;
    /*首行缩进10px*/
    text-indent: 10px;
    /*圆角边框  好看不止一点点*/
    border-radius: 5px;
    /*盒子阴影 inset内阴影*/
    box-shadow: 0 1px 0 rgba(255,255,255,0.24), 0 1px 6px rgba(0,0,0,0.45) inset;
    border: none
}
/*选中输入框  轮廓的宽度为0*/
input:focus {
    outline-width: 0;
}
/*父相子绝*/
h2 {
    position: relative;
}

span {
    position: absolute;
    top: 2px;
    right: 5px;
    /*设置行内块 有宽高*/
    display: inline-block;
    padding: 0 5px;
    height: 20px;
    border-radius: 20px;
    background: #E6E6FA;
    line-height: 22px;
    text-align: center;
    color: #666;
    font-size: 14px;
}
/*清除ol和ul标签的默认样式*/
ol,ul {
    padding: 0;
    list-style: none;
}

li {
    height: 32px;
    line-height: 32px;
    background: #fff;
    position: relative;
    margin-bottom: 10px;
    padding: 0 45px;
    border-radius: 3px;
    border-left: 5px solid #629A9C;
    box-shadow: 0 1px 2px rgba(0,0,0,0.07);
}
/*任务单选框*/
li input {
    position: absolute;
    top: 2px;
    left: 10px;
    width: 22px;
    height: 22px;
    cursor: pointer;
}
/*任务内容*/
p {
    margin: 0;
}
/*任务内容的文本输入框，用来修改里面的内容*/
li p input {
    top: 3px;
    left: 40px;
    width: 70%;
    height: 20px;
    line-height: 14px;
    text-indent: 5px;
    font-size: 14px;
}

ul li {
    border-left: 5px solid #999;
    /*不透明度 0完全透明~1完全不透明*/
    opacity: 0.5;
}
/*勾选按钮*/
li a {
    position: absolute;
    top: 2px;
    right: 5px;
    display: inline-block;
    width: 14px;
    height: 12px;
    border-radius: 14px;
    border: 6px double #FFF;
    background: #CCC;
    line-height: 14px;
    text-align: center;
    color: #FFF;
    font-weight: bold;
    font-size: 14px;
    cursor: pointer;
}

.footer {
    color: #666;
    font-size: 14px;
    text-align: center;
}

.footer a {
    /*color: #666;*/
    text-decoration: none;
    color: #999;
}
