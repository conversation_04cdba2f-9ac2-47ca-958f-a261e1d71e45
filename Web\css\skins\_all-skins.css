
/* * Skin: Blue * ----------*/ .skin-blue .main-header .navbar {
}

    .skin-blue .main-header .navbar .nav > li > a {
        color: #ffffff;
    }

        .skin-blue .main-header .navbar .nav > li > a:hover,
        .skin-blue .main-header .navbar .nav > li > a:active,
        .skin-blue .main-header .navbar .nav > li > a:focus,
        .skin-blue .main-header .navbar .nav .open > a,
        .skin-blue .main-header .navbar .nav .open > a:hover,
        .skin-blue .main-header .navbar .nav .open > a:focus,
        .skin-blue .main-header .navbar .nav > .active > a {
            background: rgba(0, 0, 0, 0.1);
            color: #f6f6f6;
        }

    .skin-blue .main-header .navbar .sidebar-toggle {
        color: #ffffff;
    }

        .skin-blue .main-header .navbar .sidebar-toggle:hover {
            color: #f6f6f6;
            background: rgba(0, 0, 0, 0.1);
        }

    .skin-blue .main-header .navbar .sidebar-toggle {
        color: #fff;
    }

        .skin-blue .main-header .navbar .sidebar-toggle:hover {
            background-color: #367fa9;
        }

@media (max-width: 767px) {
    .skin-blue .main-header .navbar .dropdown-menu li.divider {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .skin-blue .main-header .navbar .dropdown-menu li a {
        color: #fff;
    }

        .skin-blue .main-header .navbar .dropdown-menu li a:hover {
            background: #367fa9;
        }
}

.skin-blue .main-header .logo {
    color: #ffffff;
    border-bottom: 0 solid transparent;
}

    .skin-blue .main-header .logo:hover {
        background-color: #42d4d2;
    }

.skin-blue .main-header li.user-header {
    background-color: #3c8dbc;
}

.skin-blue .content-header {
    background: transparent;
}

.skin-blue .wrapper,
.skin-blue .main-sidebar,
.skin-blue .left-side {
    background-color: white;
    border-right: solid 1px #cccccc; /*border-left:solid 1px #26d0a1;border-bottom:solid 1px #cccccc;*/
}

.wrapper {
    border: 0 3px 3px 3px;
    border-color: #33cc99
}

.skin-blue .user-panel > .info,
.skin-blue .user-panel > .info > a {
    color: #fff;
}
/*.skin-blue .sidebar-menu > li > a{border-left: 3px solid transparent;}*/
.skin-blue .sidebar-menu > li.header {
    color: #4b646f;
    background: white;
}

.skin-blue .sidebar-menu > li:hover > a,
.skin-blue .sidebar-menu > li.active > a {
    color: #0c873d;
    border-bottom: solid 1px #cccccc; /*border-left: solid 6px #0c873d;*/
}

.skin-blue .sidebar-menu > li > a {
    color: #222222;
    padding-left: 35px;
    background: white;
    border-bottom: solid 1px #cccccc;
}

.skin-blue .sidebar-menu > li > .treeview-menu { 
    /*margin: 0 1px;*/
    border-bottom:solid 1px #cccccc;
    background: white;
}

.skin-blue .sidebar a {
    color: #ffffff;
    font-weight: bold;
}

    .skin-blue .sidebar a:hover {
        text-decoration: none;
        fa fa-desktop
    }

.skin-blue .treeview-menu > li > a {
    color: #222222;
    font-weight: normal;
}


    .skin-blue .treeview-menu > li.active > a,
    .skin-blue .treeview-menu > li > a:hover {
        color: #0c873d;
    }

.skin-blue .sidebar-form {
    border-radius: 3px;
}

    .skin-blue .sidebar-form input[type="text"],
    .skin-blue .sidebar-form .btn {
        box-shadow: none;
        background-color: #374850;
        border: 1px solid transparent;
        height: 35px;
        -webkit-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out;
    }

    .skin-blue .sidebar-form input[type="text"] {
        color: #666;
        border-top-left-radius: 2px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 2px;
    }

        .skin-blue .sidebar-form input[type="text"]:focus,
        .skin-blue .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
            background-color: #fff;
            color: #666;
        }

            .skin-blue .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
                border-left-color: #fff;
            }

    .skin-blue .sidebar-form .btn {
        color: #999;
        border-top-left-radius: 0;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        border-bottom-left-radius: 0;
    }

.skin-blue.layout-top-nav .main-header > .logo {
    background-color: #3c8dbc;
    color: #ffffff;
    border-bottom: 0 solid transparent;
}

    .skin-blue.layout-top-nav .main-header > .logo:hover {
        background-color: #3b8ab8;
    }
/* * Skin: Blue * ----------*/ .skin-blue-light .main-header .navbar {
    background-color: #3c8dbc;
}

    .skin-blue-light .main-header .navbar .nav > li > a {
        color: #ffffff;
    }

        .skin-blue-light .main-header .navbar .nav > li > a:hover,
        .skin-blue-light .main-header .navbar .nav > li > a:active,
        .skin-blue-light .main-header .navbar .nav > li > a:focus,
        .skin-blue-light .main-header .navbar .nav .open > a,
        .skin-blue-light .main-header .navbar .nav .open > a:hover,
        .skin-blue-light .main-header .navbar .nav .open > a:focus,
        .skin-blue-light .main-header .navbar .nav > .active > a {
            background: rgba(0, 0, 0, 0.1);
            color: #f6f6f6;
        }

    .skin-blue-light .main-header .navbar .sidebar-toggle {
        color: #ffffff;
    }

        .skin-blue-light .main-header .navbar .sidebar-toggle:hover {
            color: #f6f6f6;
            background: rgba(0, 0, 0, 0.1);
        }

    .skin-blue-light .main-header .navbar .sidebar-toggle {
        color: #fff;
    }

        .skin-blue-light .main-header .navbar .sidebar-toggle:hover {
            background-color: #367fa9;
        }

@media (max-width: 767px) {
    .skin-blue-light .main-header .navbar .dropdown-menu li.divider {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .skin-blue-light .main-header .navbar .dropdown-menu li a {
        color: #fff;
    }

        .skin-blue-light .main-header .navbar .dropdown-menu li a:hover {
            background: #367fa9;
        }
}

.skin-blue-light .main-header .logo {
    background-color: #3c8dbc;
    color: #ffffff;
    border-bottom: 0 solid transparent;
}

    .skin-blue-light .main-header .logo:hover {
        background-color: #3b8ab8;
    }

.skin-blue-light .main-header li.user-header {
    background-color: #3c8dbc;
}

.skin-blue-light .content-header {
    background: transparent;
}

.skin-blue-light .wrapper,
.skin-blue-light .main-sidebar,
.skin-blue-light .left-side {
    background-color: #f9fafc;
}

.skin-blue-light .content-wrapper,
.skin-blue-light .main-footer {
    border-left: 1px solid #d2d6de;
}

.skin-blue-light .user-panel > .info,
.skin-blue-light .user-panel > .info > a {
    color: #444444;
}

.skin-blue-light .sidebar-menu > li {
    -webkit-transition: border-left-color 0.3s ease;
    -o-transition: border-left-color 0.3s ease;
    transition: border-left-color 0.3s ease;
}

    .skin-blue-light .sidebar-menu > li.header {
        color: #848484;
        background: #f9fafc;
    }

    .skin-blue-light .sidebar-menu > li > a {
        border-left: 3px solid transparent;
        font-weight: 600;
    }

    .skin-blue-light .sidebar-menu > li:hover > a,
    .skin-blue-light .sidebar-menu > li.active > a {
        color: #000000;
        background: #f4f4f5;
    }

    .skin-blue-light .sidebar-menu > li.active {
        border-left-color: #3c8dbc;
    }

        .skin-blue-light .sidebar-menu > li.active > a {
            font-weight: 600;
        }

    .skin-blue-light .sidebar-menu > li > .treeview-menu {
        background: #f4f4f5;
    }

.skin-blue-light .sidebar a {
    color: #444444;
}

    .skin-blue-light .sidebar a:hover {
        text-decoration: none;
    }

.skin-blue-light .treeview-menu > li > a {
    color: #777777;
}

    .skin-blue-light .treeview-menu > li.active > a,
    .skin-blue-light .treeview-menu > li > a:hover {
        color: #000000;
    }

.skin-blue-light .treeview-menu > li.active > a {
    font-weight: 600;
}

.skin-blue-light .sidebar-form {
    border-radius: 3px;
    border: 1px solid #d2d6de;
    margin: 10px 10px;
}

    .skin-blue-light .sidebar-form input[type="text"],
    .skin-blue-light .sidebar-form .btn {
        box-shadow: none;
        background-color: #fff;
        border: 1px solid transparent;
        height: 35px;
        -webkit-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out;
    }

    .skin-blue-light .sidebar-form input[type="text"] {
        color: #666;
        border-top-left-radius: 2px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 2px;
    }

        .skin-blue-light .sidebar-form input[type="text"]:focus,
        .skin-blue-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
            background-color: #fff;
            color: #666;
        }

            .skin-blue-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
                border-left-color: #fff;
            }

    .skin-blue-light .sidebar-form .btn {
        color: #999;
        border-top-left-radius: 0;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        border-bottom-left-radius: 0;
    }

@media (min-width: 768px) {
    .skin-blue-light.sidebar-mini.sidebar-collapse .sidebar-menu > li > .treeview-menu {
        border-left: 1px solid #d2d6de;
    }
}

.skin-blue-light .main-footer {
    border-top-color: #d2d6de;
}

.skin-blue.layout-top-nav .main-header > .logo {
    background-color: #3c8dbc;
    color: #ffffff;
    border-bottom: 0 solid transparent;
}

    .skin-blue.layout-top-nav .main-header > .logo:hover {
        background-color: #3b8ab8;
    }
/* * Skin: Black * -----------*/ /* skin-black navbar*/

.skin-black .main-header {
    -webkit-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
    box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
}

    .skin-black .main-header .navbar-toggle {
        color: #333;
    }

    .skin-black .main-header .navbar-brand {
        color: #333;
        border-right: 1px solid #eee;
    }

    .skin-black .main-header > .navbar {
        background-color: #ffffff;
    }

        .skin-black .main-header > .navbar .nav > li > a {
            color: #333333;
        }

            .skin-black .main-header > .navbar .nav > li > a:hover,
            .skin-black .main-header > .navbar .nav > li > a:active,
            .skin-black .main-header > .navbar .nav > li > a:focus,
            .skin-black .main-header > .navbar .nav .open > a,
            .skin-black .main-header > .navbar .nav .open > a:hover,
            .skin-black .main-header > .navbar .nav .open > a:focus,
            .skin-black .main-header > .navbar .nav > .active > a {
                background: #ffffff;
                color: #999999;
            }

        .skin-black .main-header > .navbar .sidebar-toggle {
            color: #333333;
        }

            .skin-black .main-header > .navbar .sidebar-toggle:hover {
                color: #999999;
                background: #ffffff;
            }

        .skin-black .main-header > .navbar > .sidebar-toggle {
            color: #333;
            border-right: 1px solid #eee;
        }

        .skin-black .main-header > .navbar .navbar-nav > li > a {
            border-right: 1px solid #eee;
        }

        .skin-black .main-header > .navbar .navbar-custom-menu .navbar-nav > li > a,
        .skin-black .main-header > .navbar .navbar-right > li > a {
            border-left: 1px solid #eee;
            border-right-width: 0;
        }

    .skin-black .main-header > .logo {
        background-color: #ffffff;
        color: #333333;
        border-bottom: 0 solid transparent;
        border-right: 1px solid #eee;
    }

        .skin-black .main-header > .logo:hover {
            background-color: #fcfcfc;
        }

@media (max-width: 767px) {
    .skin-black .main-header > .logo {
        background-color: #222222;
        color: #ffffff;
        border-bottom: 0 solid transparent;
        border-right: none;
    }

        .skin-black .main-header > .logo:hover {
            background-color: #1f1f1f;
        }
}

.skin-black .main-header li.user-header {
    background-color: #222;
}

.skin-black .content-header {
    background: transparent;
    box-shadow: none;
}

.skin-black .wrapper,
.skin-black .main-sidebar,
.skin-black .left-side {
    background-color: #222d32;
}

.skin-black .user-panel > .info,
.skin-black .user-panel > .info > a {
    color: #fff;
}

.skin-black .sidebar-menu > li.header {
    color: #4b646f;
    background: #1a2226;
}

.skin-black .sidebar-menu > li > a {
    border-left: 3px solid transparent;
}

.skin-black .sidebar-menu > li:hover > a,
.skin-black .sidebar-menu > li.active > a {
    color: #ffffff;
    background: #1e282c;
    border-left-color: #ffffff;
}

.skin-black .sidebar-menu > li > .treeview-menu {
    margin: 0 1px;
    background: #2c3b41;
}

.skin-black .sidebar a {
    color: #b8c7ce;
}

    .skin-black .sidebar a:hover {
        text-decoration: none;
    }

.skin-black .treeview-menu > li > a { /*color: #8aa4af;*/
}

    .skin-black .treeview-menu > li.active > a,
    .skin-black .treeview-menu > li > a:hover {
        color: #ffffff;
    }

.skin-black .sidebar-form {
    border-radius: 3px;
    border: 1px solid #374850;
    margin: 10px 10px;
}

    .skin-black .sidebar-form input[type="text"],
    .skin-black .sidebar-form .btn {
        box-shadow: none;
        background-color: #374850;
        border: 1px solid transparent;
        height: 35px;
        -webkit-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out;
    }

    .skin-black .sidebar-form input[type="text"] {
        color: #666;
        border-top-left-radius: 2px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 2px;
    }

        .skin-black .sidebar-form input[type="text"]:focus,
        .skin-black .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
            background-color: #fff;
            color: #666;
        }

            .skin-black .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
                border-left-color: #fff;
            }

    .skin-black .sidebar-form .btn {
        color: #999;
        border-top-left-radius: 0;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        border-bottom-left-radius: 0;
    }

.skin-black .pace .pace-progress {
    background: #222;
}

.skin-black .pace .pace-activity {
    border-top-color: #222;
    border-left-color: #222;
}
/* * Skin: Black * -----------*/ /* skin-black navbar*/

.skin-black-light .main-header {
    -webkit-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
    box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
}

    .skin-black-light .main-header .navbar-toggle {
        color: #333;
    }

    .skin-black-light .main-header .navbar-brand {
        color: #333;
        border-right: 1px solid #eee;
    }

    .skin-black-light .main-header > .navbar {
        background-color: #ffffff;
    }

        .skin-black-light .main-header > .navbar .nav > li > a {
            color: #333333;
        }

            .skin-black-light .main-header > .navbar .nav > li > a:hover,
            .skin-black-light .main-header > .navbar .nav > li > a:active,
            .skin-black-light .main-header > .navbar .nav > li > a:focus,
            .skin-black-light .main-header > .navbar .nav .open > a,
            .skin-black-light .main-header > .navbar .nav .open > a:hover,
            .skin-black-light .main-header > .navbar .nav .open > a:focus,
            .skin-black-light .main-header > .navbar .nav > .active > a {
                background: #ffffff;
                color: #999999;
            }

        .skin-black-light .main-header > .navbar .sidebar-toggle {
            color: #333333;
        }

            .skin-black-light .main-header > .navbar .sidebar-toggle:hover {
                color: #999999;
                background: #ffffff;
            }

        .skin-black-light .main-header > .navbar > .sidebar-toggle {
            color: #333;
            border-right: 1px solid #eee;
        }

        .skin-black-light .main-header > .navbar .navbar-nav > li > a {
            border-right: 1px solid #eee;
        }

        .skin-black-light .main-header > .navbar .navbar-custom-menu .navbar-nav > li > a,
        .skin-black-light .main-header > .navbar .navbar-right > li > a {
            border-left: 1px solid #eee;
            border-right-width: 0;
        }

    .skin-black-light .main-header > .logo {
        background-color: #ffffff;
        color: #333333;
        border-bottom: 0 solid transparent;
        border-right: 1px solid #eee;
    }

        .skin-black-light .main-header > .logo:hover {
            background-color: #fcfcfc;
        }

@media (max-width: 767px) {
    .skin-black-light .main-header > .logo {
        background-color: #222222;
        color: #ffffff;
        border-bottom: 0 solid transparent;
        border-right: none;
    }

        .skin-black-light .main-header > .logo:hover {
            background-color: #1f1f1f;
        }
}

.skin-black-light .main-header li.user-header {
    background-color: #222;
}

.skin-black-light .content-header {
    background: transparent;
    box-shadow: none;
}

.skin-black-light .wrapper,
.skin-black-light .main-sidebar,
.skin-black-light .left-side {
    background-color: #f9fafc;
}

.skin-black-light .content-wrapper,
.skin-black-light .main-footer {
    border-left: 1px solid #d2d6de;
}

.skin-black-light .user-panel > .info,
.skin-black-light .user-panel > .info > a {
    color: #444444;
}

.skin-black-light .sidebar-menu > li {
    -webkit-transition: border-left-color 0.3s ease;
    -o-transition: border-left-color 0.3s ease;
    transition: border-left-color 0.3s ease;
}

    .skin-black-light .sidebar-menu > li.header {
        color: #848484;
        background: #f9fafc;
    }

    .skin-black-light .sidebar-menu > li > a {
        border-left: 3px solid transparent;
        font-weight: 600;
    }

    .skin-black-light .sidebar-menu > li:hover > a,
    .skin-black-light .sidebar-menu > li.active > a {
        color: #000000;
        background: #f4f4f5;
    }

    .skin-black-light .sidebar-menu > li.active {
        border-left-color: #ffffff;
    }

        .skin-black-light .sidebar-menu > li.active > a {
            font-weight: 600;
        }

    .skin-black-light .sidebar-menu > li > .treeview-menu {
        background: #f4f4f5;
    }

.skin-black-light .sidebar a {
    color: #444444;
}

    .skin-black-light .sidebar a:hover {
        text-decoration: none;
    }

.skin-black-light .treeview-menu > li > a {
    color: #777777;
}

    .skin-black-light .treeview-menu > li.active > a,
    .skin-black-light .treeview-menu > li > a:hover {
        color: #000000;
    }

.skin-black-light .treeview-menu > li.active > a {
    font-weight: 600;
}

.skin-black-light .sidebar-form {
    border-radius: 3px;
    border: 1px solid #d2d6de;
    margin: 10px 10px;
}

    .skin-black-light .sidebar-form input[type="text"],
    .skin-black-light .sidebar-form .btn {
        box-shadow: none;
        background-color: #fff;
        border: 1px solid transparent;
        height: 35px;
        -webkit-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out;
    }

    .skin-black-light .sidebar-form input[type="text"] {
        color: #666;
        border-top-left-radius: 2px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 2px;
    }

        .skin-black-light .sidebar-form input[type="text"]:focus,
        .skin-black-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
            background-color: #fff;
            color: #666;
        }

            .skin-black-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
                border-left-color: #fff;
            }

    .skin-black-light .sidebar-form .btn {
        color: #999;
        border-top-left-radius: 0;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        border-bottom-left-radius: 0;
    }

@media (min-width: 768px) {
    .skin-black-light.sidebar-mini.sidebar-collapse .sidebar-menu > li > .treeview-menu {
        border-left: 1px solid #d2d6de;
    }
}
/* * Skin: Green * -----------*/ .skin-green .main-header .navbar {
    background-color: #00a65a;
}

    .skin-green .main-header .navbar .nav > li > a {
        color: #ffffff;
    }

        .skin-green .main-header .navbar .nav > li > a:hover,
        .skin-green .main-header .navbar .nav > li > a:active,
        .skin-green .main-header .navbar .nav > li > a:focus,
        .skin-green .main-header .navbar .nav .open > a,
        .skin-green .main-header .navbar .nav .open > a:hover,
        .skin-green .main-header .navbar .nav .open > a:focus,
        .skin-green .main-header .navbar .nav > .active > a {
            background: rgba(0, 0, 0, 0.1);
            color: #f6f6f6;
        }

    .skin-green .main-header .navbar .sidebar-toggle {
        color: #ffffff;
    }

        .skin-green .main-header .navbar .sidebar-toggle:hover {
            color: #f6f6f6;
            background: rgba(0, 0, 0, 0.1);
        }

    .skin-green .main-header .navbar .sidebar-toggle {
        color: #fff;
    }

        .skin-green .main-header .navbar .sidebar-toggle:hover {
            background-color: #008d4c;
        }

@media (max-width: 767px) {
    .skin-green .main-header .navbar .dropdown-menu li.divider {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .skin-green .main-header .navbar .dropdown-menu li a {
        color: #fff;
    }

        .skin-green .main-header .navbar .dropdown-menu li a:hover {
            background: #008d4c;
        }
}

.skin-green .main-header .logo {
    background-color: #008d4c;
    color: #ffffff;
    border-bottom: 0 solid transparent;
}

    .skin-green .main-header .logo:hover {
        background-color: #008749;
    }

.skin-green .main-header li.user-header {
    background-color: #00a65a;
}

.skin-green .content-header {
    background: transparent;
}

.skin-green .wrapper,
.skin-green .main-sidebar,
.skin-green .left-side {
    background-color: #222d32;
}

.skin-green .user-panel > .info,
.skin-green .user-panel > .info > a {
    color: #fff;
}

.skin-green .sidebar-menu > li.header {
    color: #4b646f;
    background: #1a2226;
}

.skin-green .sidebar-menu > li > a {
    border-left: 3px solid transparent;
}

.skin-green .sidebar-menu > li:hover > a,
.skin-green .sidebar-menu > li.active > a {
    color: #ffffff;
    background: #1e282c;
    border-left-color: #00a65a;
}

.skin-green .sidebar-menu > li > .treeview-menu {
    margin: 0 1px;
    background: #2c3b41;
}

.skin-green .sidebar a {
    color: #b8c7ce;
}

    .skin-green .sidebar a:hover {
        text-decoration: none;
    }

.skin-green .treeview-menu > li > a { /*color: #8aa4af;*/
}

    .skin-green .treeview-menu > li.active > a,
    .skin-green .treeview-menu > li > a:hover {
        color: #ffffff;
    }

.skin-green .sidebar-form {
    border-radius: 3px;
    border: 1px solid #374850;
    margin: 10px 10px;
}

    .skin-green .sidebar-form input[type="text"],
    .skin-green .sidebar-form .btn {
        box-shadow: none;
        background-color: #374850;
        border: 1px solid transparent;
        height: 35px;
        -webkit-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out;
    }

    .skin-green .sidebar-form input[type="text"] {
        color: #666;
        border-top-left-radius: 2px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 2px;
    }

        .skin-green .sidebar-form input[type="text"]:focus,
        .skin-green .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
            background-color: #fff;
            color: #666;
        }

            .skin-green .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
                border-left-color: #fff;
            }

    .skin-green .sidebar-form .btn {
        color: #999;
        border-top-left-radius: 0;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        border-bottom-left-radius: 0;
    }
/* * Skin: Green * -----------*/ .skin-green-light .main-header .navbar {
    background-color: #00a65a;
}

    .skin-green-light .main-header .navbar .nav > li > a {
        color: #ffffff;
    }

        .skin-green-light .main-header .navbar .nav > li > a:hover,
        .skin-green-light .main-header .navbar .nav > li > a:active,
        .skin-green-light .main-header .navbar .nav > li > a:focus,
        .skin-green-light .main-header .navbar .nav .open > a,
        .skin-green-light .main-header .navbar .nav .open > a:hover,
        .skin-green-light .main-header .navbar .nav .open > a:focus,
        .skin-green-light .main-header .navbar .nav > .active > a {
            background: rgba(0, 0, 0, 0.1);
            color: #f6f6f6;
        }

    .skin-green-light .main-header .navbar .sidebar-toggle {
        color: #ffffff;
    }

        .skin-green-light .main-header .navbar .sidebar-toggle:hover {
            color: #f6f6f6;
            background: rgba(0, 0, 0, 0.1);
        }

    .skin-green-light .main-header .navbar .sidebar-toggle {
        color: #fff;
    }

        .skin-green-light .main-header .navbar .sidebar-toggle:hover {
            background-color: #008d4c;
        }

@media (max-width: 767px) {
    .skin-green-light .main-header .navbar .dropdown-menu li.divider {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .skin-green-light .main-header .navbar .dropdown-menu li a {
        color: #fff;
    }

        .skin-green-light .main-header .navbar .dropdown-menu li a:hover {
            background: #008d4c;
        }
}

.skin-green-light .main-header .logo {
    background-color: #00a65a;
    color: #ffffff;
    border-bottom: 0 solid transparent;
}

    .skin-green-light .main-header .logo:hover {
        background-color: #00a157;
    }

.skin-green-light .main-header li.user-header {
    background-color: #00a65a;
}

.skin-green-light .content-header {
    background: transparent;
}

.skin-green-light .wrapper,
.skin-green-light .main-sidebar,
.skin-green-light .left-side {
    background-color: #f9fafc;
}

.skin-green-light .content-wrapper,
.skin-green-light .main-footer {
    border-left: 1px solid #d2d6de;
}

.skin-green-light .user-panel > .info,
.skin-green-light .user-panel > .info > a {
    color: #444444;
}

.skin-green-light .sidebar-menu > li {
    -webkit-transition: border-left-color 0.3s ease;
    -o-transition: border-left-color 0.3s ease;
    transition: border-left-color 0.3s ease;
}

    .skin-green-light .sidebar-menu > li.header {
        color: #848484;
        background: #f9fafc;
    }

    .skin-green-light .sidebar-menu > li > a {
        border-left: 3px solid transparent;
        font-weight: 600;
    }

    .skin-green-light .sidebar-menu > li:hover > a,
    .skin-green-light .sidebar-menu > li.active > a {
        color: #000000;
        background: #f4f4f5;
    }

    .skin-green-light .sidebar-menu > li.active {
        border-left-color: #00a65a;
    }

        .skin-green-light .sidebar-menu > li.active > a {
            font-weight: 600;
        }

    .skin-green-light .sidebar-menu > li > .treeview-menu {
        background: #f4f4f5;
    }

.skin-green-light .sidebar a {
    color: #444444;
}

    .skin-green-light .sidebar a:hover {
        text-decoration: none;
    }

.skin-green-light .treeview-menu > li > a {
    color: #777777;
}

    .skin-green-light .treeview-menu > li.active > a,
    .skin-green-light .treeview-menu > li > a:hover {
        color: #000000;
    }

.skin-green-light .treeview-menu > li.active > a {
    font-weight: 600;
}

.skin-green-light .sidebar-form {
    border-radius: 3px;
    border: 1px solid #d2d6de;
    margin: 10px 10px;
}

    .skin-green-light .sidebar-form input[type="text"],
    .skin-green-light .sidebar-form .btn {
        box-shadow: none;
        background-color: #fff;
        border: 1px solid transparent;
        height: 35px;
        -webkit-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out;
    }

    .skin-green-light .sidebar-form input[type="text"] {
        color: #666;
        border-top-left-radius: 2px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 2px;
    }

        .skin-green-light .sidebar-form input[type="text"]:focus,
        .skin-green-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
            background-color: #fff;
            color: #666;
        }

            .skin-green-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
                border-left-color: #fff;
            }

    .skin-green-light .sidebar-form .btn {
        color: #999;
        border-top-left-radius: 0;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        border-bottom-left-radius: 0;
    }

@media (min-width: 768px) {
    .skin-green-light.sidebar-mini.sidebar-collapse .sidebar-menu > li > .treeview-menu {
        border-left: 1px solid #d2d6de;
    }
}
/* * Skin: Red * ---------*/ .skin-red .main-header .navbar {
    background-color: #dd4b39;
}

    .skin-red .main-header .navbar .nav > li > a {
        color: #ffffff;
    }

        .skin-red .main-header .navbar .nav > li > a:hover,
        .skin-red .main-header .navbar .nav > li > a:active,
        .skin-red .main-header .navbar .nav > li > a:focus,
        .skin-red .main-header .navbar .nav .open > a,
        .skin-red .main-header .navbar .nav .open > a:hover,
        .skin-red .main-header .navbar .nav .open > a:focus,
        .skin-red .main-header .navbar .nav > .active > a {
            background: rgba(0, 0, 0, 0.1);
            color: #f6f6f6;
        }

    .skin-red .main-header .navbar .sidebar-toggle {
        color: #ffffff;
    }

        .skin-red .main-header .navbar .sidebar-toggle:hover {
            color: #f6f6f6;
            background: rgba(0, 0, 0, 0.1);
        }

    .skin-red .main-header .navbar .sidebar-toggle {
        color: #fff;
    }

        .skin-red .main-header .navbar .sidebar-toggle:hover {
            background-color: #d73925;
        }

@media (max-width: 767px) {
    .skin-red .main-header .navbar .dropdown-menu li.divider {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .skin-red .main-header .navbar .dropdown-menu li a {
        color: #fff;
    }

        .skin-red .main-header .navbar .dropdown-menu li a:hover {
            background: #d73925;
        }
}

.skin-red .main-header .logo {
    background-color: #d73925;
    color: #ffffff;
    border-bottom: 0 solid transparent;
}

    .skin-red .main-header .logo:hover {
        background-color: #d33724;
    }

.skin-red .main-header li.user-header {
    background-color: #dd4b39;
}

.skin-red .content-header {
    background: transparent;
}

.skin-red .wrapper,
.skin-red .main-sidebar,
.skin-red .left-side {
    background-color: #222d32;
}

.skin-red .user-panel > .info,
.skin-red .user-panel > .info > a {
    color: #fff;
}

.skin-red .sidebar-menu > li.header {
    color: #4b646f;
    background: #1a2226;
}

.skin-red .sidebar-menu > li > a {
    border-left: 3px solid transparent;
}

.skin-red .sidebar-menu > li:hover > a,
.skin-red .sidebar-menu > li.active > a {
    color: #ffffff;
    background: #1e282c;
    border-left-color: #dd4b39;
}

.skin-red .sidebar-menu > li > .treeview-menu {
    margin: 0 1px;
    background: #2c3b41;
}

.skin-red .sidebar a {
    color: #b8c7ce;
}

    .skin-red .sidebar a:hover {
        text-decoration: none;
    }

.skin-red .treeview-menu > li > a { /*color: #8aa4af;*/
}

    .skin-red .treeview-menu > li.active > a,
    .skin-red .treeview-menu > li > a:hover {
        color: #ffffff;
    }

.skin-red .sidebar-form {
    border-radius: 3px;
    border: 1px solid #374850;
    margin: 10px 10px;
}

    .skin-red .sidebar-form input[type="text"],
    .skin-red .sidebar-form .btn {
        box-shadow: none;
        background-color: #374850;
        border: 1px solid transparent;
        height: 35px;
        -webkit-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out;
    }

    .skin-red .sidebar-form input[type="text"] {
        color: #666;
        border-top-left-radius: 2px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 2px;
    }

        .skin-red .sidebar-form input[type="text"]:focus,
        .skin-red .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
            background-color: #fff;
            color: #666;
        }

            .skin-red .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
                border-left-color: #fff;
            }

    .skin-red .sidebar-form .btn {
        color: #999;
        border-top-left-radius: 0;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        border-bottom-left-radius: 0;
    }
/* * Skin: Red * ---------*/ .skin-red-light .main-header .navbar {
    background-color: #dd4b39;
}

    .skin-red-light .main-header .navbar .nav > li > a {
        color: #ffffff;
    }

        .skin-red-light .main-header .navbar .nav > li > a:hover,
        .skin-red-light .main-header .navbar .nav > li > a:active,
        .skin-red-light .main-header .navbar .nav > li > a:focus,
        .skin-red-light .main-header .navbar .nav .open > a,
        .skin-red-light .main-header .navbar .nav .open > a:hover,
        .skin-red-light .main-header .navbar .nav .open > a:focus,
        .skin-red-light .main-header .navbar .nav > .active > a {
            background: rgba(0, 0, 0, 0.1);
            color: #f6f6f6;
        }

    .skin-red-light .main-header .navbar .sidebar-toggle {
        color: #ffffff;
    }

        .skin-red-light .main-header .navbar .sidebar-toggle:hover {
            color: #f6f6f6;
            background: rgba(0, 0, 0, 0.1);
        }

    .skin-red-light .main-header .navbar .sidebar-toggle {
        color: #fff;
    }

        .skin-red-light .main-header .navbar .sidebar-toggle:hover {
            background-color: #d73925;
        }

@media (max-width: 767px) {
    .skin-red-light .main-header .navbar .dropdown-menu li.divider {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .skin-red-light .main-header .navbar .dropdown-menu li a {
        color: #fff;
    }

        .skin-red-light .main-header .navbar .dropdown-menu li a:hover {
            background: #d73925;
        }
}

.skin-red-light .main-header .logo {
    background-color: #dd4b39;
    color: #ffffff;
    border-bottom: 0 solid transparent;
}

    .skin-red-light .main-header .logo:hover {
        background-color: #dc4735;
    }

.skin-red-light .main-header li.user-header {
    background-color: #dd4b39;
}

.skin-red-light .content-header {
    background: transparent;
}

.skin-red-light .wrapper,
.skin-red-light .main-sidebar,
.skin-red-light .left-side {
    background-color: #f9fafc;
}

.skin-red-light .content-wrapper,
.skin-red-light .main-footer {
    border-left: 1px solid #d2d6de;
}

.skin-red-light .user-panel > .info,
.skin-red-light .user-panel > .info > a {
    color: #444444;
}

.skin-red-light .sidebar-menu > li {
    -webkit-transition: border-left-color 0.3s ease;
    -o-transition: border-left-color 0.3s ease;
    transition: border-left-color 0.3s ease;
}

    .skin-red-light .sidebar-menu > li.header {
        color: #848484;
        background: #f9fafc;
    }

    .skin-red-light .sidebar-menu > li > a {
        border-left: 3px solid transparent;
        font-weight: 600;
    }

    .skin-red-light .sidebar-menu > li:hover > a,
    .skin-red-light .sidebar-menu > li.active > a {
        color: #000000;
        background: #f4f4f5;
    }

    .skin-red-light .sidebar-menu > li.active {
        border-left-color: #dd4b39;
    }

        .skin-red-light .sidebar-menu > li.active > a {
            font-weight: 600;
        }

    .skin-red-light .sidebar-menu > li > .treeview-menu {
        background: #f4f4f5;
    }

.skin-red-light .sidebar a {
    color: #444444;
}

    .skin-red-light .sidebar a:hover {
        text-decoration: none;
    }

.skin-red-light .treeview-menu > li > a {
    color: #777777;
}

    .skin-red-light .treeview-menu > li.active > a,
    .skin-red-light .treeview-menu > li > a:hover {
        color: #000000;
    }

.skin-red-light .treeview-menu > li.active > a {
    font-weight: 600;
}

.skin-red-light .sidebar-form {
    border-radius: 3px;
    border: 1px solid #d2d6de;
    margin: 10px 10px;
}

    .skin-red-light .sidebar-form input[type="text"],
    .skin-red-light .sidebar-form .btn {
        box-shadow: none;
        background-color: #fff;
        border: 1px solid transparent;
        height: 35px;
        -webkit-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out;
    }

    .skin-red-light .sidebar-form input[type="text"] {
        color: #666;
        border-top-left-radius: 2px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 2px;
    }

        .skin-red-light .sidebar-form input[type="text"]:focus,
        .skin-red-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
            background-color: #fff;
            color: #666;
        }

            .skin-red-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
                border-left-color: #fff;
            }

    .skin-red-light .sidebar-form .btn {
        color: #999;
        border-top-left-radius: 0;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        border-bottom-left-radius: 0;
    }

@media (min-width: 768px) {
    .skin-red-light.sidebar-mini.sidebar-collapse .sidebar-menu > li > .treeview-menu {
        border-left: 1px solid #d2d6de;
    }
}
/* * Skin: Yellow * ------------*/ .skin-yellow .main-header .navbar {
    background-color: #f39c12;
}

    .skin-yellow .main-header .navbar .nav > li > a {
        color: #ffffff;
    }

        .skin-yellow .main-header .navbar .nav > li > a:hover,
        .skin-yellow .main-header .navbar .nav > li > a:active,
        .skin-yellow .main-header .navbar .nav > li > a:focus,
        .skin-yellow .main-header .navbar .nav .open > a,
        .skin-yellow .main-header .navbar .nav .open > a:hover,
        .skin-yellow .main-header .navbar .nav .open > a:focus,
        .skin-yellow .main-header .navbar .nav > .active > a {
            background: rgba(0, 0, 0, 0.1);
            color: #f6f6f6;
        }

    .skin-yellow .main-header .navbar .sidebar-toggle {
        color: #ffffff;
    }

        .skin-yellow .main-header .navbar .sidebar-toggle:hover {
            color: #f6f6f6;
            background: rgba(0, 0, 0, 0.1);
        }

    .skin-yellow .main-header .navbar .sidebar-toggle {
        color: #fff;
    }

        .skin-yellow .main-header .navbar .sidebar-toggle:hover {
            background-color: #e08e0b;
        }

@media (max-width: 767px) {
    .skin-yellow .main-header .navbar .dropdown-menu li.divider {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .skin-yellow .main-header .navbar .dropdown-menu li a {
        color: #fff;
    }

        .skin-yellow .main-header .navbar .dropdown-menu li a:hover {
            background: #e08e0b;
        }
}

.skin-yellow .main-header .logo {
    background-color: #e08e0b;
    color: #ffffff;
    border-bottom: 0 solid transparent;
}

    .skin-yellow .main-header .logo:hover {
        background-color: #db8b0b;
    }

.skin-yellow .main-header li.user-header {
    background-color: #f39c12;
}

.skin-yellow .content-header {
    background: transparent;
}

.skin-yellow .wrapper,
.skin-yellow .main-sidebar,
.skin-yellow .left-side {
    background-color: #222d32;
}

.skin-yellow .user-panel > .info,
.skin-yellow .user-panel > .info > a {
    color: #fff;
}

.skin-yellow .sidebar-menu > li.header {
    color: #4b646f;
    background: #1a2226;
}

.skin-yellow .sidebar-menu > li > a {
    border-left: 3px solid transparent;
}

.skin-yellow .sidebar-menu > li:hover > a,
.skin-yellow .sidebar-menu > li.active > a {
    color: #ffffff;
    background: #1e282c;
    border-left-color: #f39c12;
}

.skin-yellow .sidebar-menu > li > .treeview-menu {
    margin: 0 1px;
    background: #2c3b41;
}

.skin-yellow .sidebar a {
    color: #b8c7ce;
}

    .skin-yellow .sidebar a:hover {
        text-decoration: none;
    }

.skin-yellow .treeview-menu > li > a { /*color: #8aa4af;*/
}

    .skin-yellow .treeview-menu > li.active > a,
    .skin-yellow .treeview-menu > li > a:hover {
        color: #ffffff;
    }

.skin-yellow .sidebar-form {
    border-radius: 3px;
    border: 1px solid #374850;
    margin: 10px 10px;
}

    .skin-yellow .sidebar-form input[type="text"],
    .skin-yellow .sidebar-form .btn {
        box-shadow: none;
        background-color: #374850;
        border: 1px solid transparent;
        height: 35px;
        -webkit-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out;
    }

    .skin-yellow .sidebar-form input[type="text"] {
        color: #666;
        border-top-left-radius: 2px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 2px;
    }

        .skin-yellow .sidebar-form input[type="text"]:focus,
        .skin-yellow .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
            background-color: #fff;
            color: #666;
        }

            .skin-yellow .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
                border-left-color: #fff;
            }

    .skin-yellow .sidebar-form .btn {
        color: #999;
        border-top-left-radius: 0;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        border-bottom-left-radius: 0;
    }
/* * Skin: Yellow * ------------*/ .skin-yellow-light .main-header .navbar {
    background-color: #f39c12;
}

    .skin-yellow-light .main-header .navbar .nav > li > a {
        color: #ffffff;
    }

        .skin-yellow-light .main-header .navbar .nav > li > a:hover,
        .skin-yellow-light .main-header .navbar .nav > li > a:active,
        .skin-yellow-light .main-header .navbar .nav > li > a:focus,
        .skin-yellow-light .main-header .navbar .nav .open > a,
        .skin-yellow-light .main-header .navbar .nav .open > a:hover,
        .skin-yellow-light .main-header .navbar .nav .open > a:focus,
        .skin-yellow-light .main-header .navbar .nav > .active > a {
            background: rgba(0, 0, 0, 0.1);
            color: #f6f6f6;
        }

    .skin-yellow-light .main-header .navbar .sidebar-toggle {
        color: #ffffff;
    }

        .skin-yellow-light .main-header .navbar .sidebar-toggle:hover {
            color: #f6f6f6;
            background: rgba(0, 0, 0, 0.1);
        }

    .skin-yellow-light .main-header .navbar .sidebar-toggle {
        color: #fff;
    }

        .skin-yellow-light .main-header .navbar .sidebar-toggle:hover {
            background-color: #e08e0b;
        }

@media (max-width: 767px) {
    .skin-yellow-light .main-header .navbar .dropdown-menu li.divider {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .skin-yellow-light .main-header .navbar .dropdown-menu li a {
        color: #fff;
    }

        .skin-yellow-light .main-header .navbar .dropdown-menu li a:hover {
            background: #e08e0b;
        }
}

.skin-yellow-light .main-header .logo {
    background-color: #f39c12;
    color: #ffffff;
    border-bottom: 0 solid transparent;
}

    .skin-yellow-light .main-header .logo:hover {
        background-color: #f39a0d;
    }

.skin-yellow-light .main-header li.user-header {
    background-color: #f39c12;
}

.skin-yellow-light .content-header {
    background: transparent;
}

.skin-yellow-light .wrapper,
.skin-yellow-light .main-sidebar,
.skin-yellow-light .left-side {
    background-color: #f9fafc;
}

.skin-yellow-light .content-wrapper,
.skin-yellow-light .main-footer {
    border-left: 1px solid #d2d6de;
}

.skin-yellow-light .user-panel > .info,
.skin-yellow-light .user-panel > .info > a {
    color: #444444;
}

.skin-yellow-light .sidebar-menu > li {
    -webkit-transition: border-left-color 0.3s ease;
    -o-transition: border-left-color 0.3s ease;
    transition: border-left-color 0.3s ease;
}

    .skin-yellow-light .sidebar-menu > li.header {
        color: #848484;
        background: #f9fafc;
    }

    .skin-yellow-light .sidebar-menu > li > a {
        border-left: 3px solid transparent;
        font-weight: 600;
    }

    .skin-yellow-light .sidebar-menu > li:hover > a,
    .skin-yellow-light .sidebar-menu > li.active > a {
        color: #000000;
        background: #f4f4f5;
    }

    .skin-yellow-light .sidebar-menu > li.active {
        border-left-color: #f39c12;
    }

        .skin-yellow-light .sidebar-menu > li.active > a {
            font-weight: 600;
        }

    .skin-yellow-light .sidebar-menu > li > .treeview-menu {
        background: #f4f4f5;
    }

.skin-yellow-light .sidebar a {
    color: #444444;
}

    .skin-yellow-light .sidebar a:hover {
        text-decoration: none;
    }

.skin-yellow-light .treeview-menu > li > a {
    color: #777777;
}

    .skin-yellow-light .treeview-menu > li.active > a,
    .skin-yellow-light .treeview-menu > li > a:hover {
        color: #000000;
    }

.skin-yellow-light .treeview-menu > li.active > a {
    font-weight: 600;
}

.skin-yellow-light .sidebar-form {
    border-radius: 3px;
    border: 1px solid #d2d6de;
    margin: 10px 10px;
}

    .skin-yellow-light .sidebar-form input[type="text"],
    .skin-yellow-light .sidebar-form .btn {
        box-shadow: none;
        background-color: #fff;
        border: 1px solid transparent;
        height: 35px;
        -webkit-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out;
    }

    .skin-yellow-light .sidebar-form input[type="text"] {
        color: #666;
        border-top-left-radius: 2px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 2px;
    }

        .skin-yellow-light .sidebar-form input[type="text"]:focus,
        .skin-yellow-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
            background-color: #fff;
            color: #666;
        }

            .skin-yellow-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
                border-left-color: #fff;
            }

    .skin-yellow-light .sidebar-form .btn {
        color: #999;
        border-top-left-radius: 0;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        border-bottom-left-radius: 0;
    }

@media (min-width: 768px) {
    .skin-yellow-light.sidebar-mini.sidebar-collapse .sidebar-menu > li > .treeview-menu {
        border-left: 1px solid #d2d6de;
    }
}
/* * Skin: Purple * ------------*/ .skin-purple .main-header .navbar {
    background-color: #605ca8;
}

    .skin-purple .main-header .navbar .nav > li > a {
        color: #ffffff;
    }

        .skin-purple .main-header .navbar .nav > li > a:hover,
        .skin-purple .main-header .navbar .nav > li > a:active,
        .skin-purple .main-header .navbar .nav > li > a:focus,
        .skin-purple .main-header .navbar .nav .open > a,
        .skin-purple .main-header .navbar .nav .open > a:hover,
        .skin-purple .main-header .navbar .nav .open > a:focus,
        .skin-purple .main-header .navbar .nav > .active > a {
            background: rgba(0, 0, 0, 0.1);
            color: #f6f6f6;
        }

    .skin-purple .main-header .navbar .sidebar-toggle {
        color: #ffffff;
    }

        .skin-purple .main-header .navbar .sidebar-toggle:hover {
            color: #f6f6f6;
            background: rgba(0, 0, 0, 0.1);
        }

    .skin-purple .main-header .navbar .sidebar-toggle {
        color: #fff;
    }

        .skin-purple .main-header .navbar .sidebar-toggle:hover {
            background-color: #555299;
        }

@media (max-width: 767px) {
    .skin-purple .main-header .navbar .dropdown-menu li.divider {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .skin-purple .main-header .navbar .dropdown-menu li a {
        color: #fff;
    }

        .skin-purple .main-header .navbar .dropdown-menu li a:hover {
            background: #555299;
        }
}

.skin-purple .main-header .logo {
    background-color: #555299;
    color: #ffffff;
    border-bottom: 0 solid transparent;
}

    .skin-purple .main-header .logo:hover {
        background-color: #545096;
    }

.skin-purple .main-header li.user-header {
    background-color: #605ca8;
}

.skin-purple .content-header {
    background: transparent;
}

.skin-purple .wrapper,
.skin-purple .main-sidebar,
.skin-purple .left-side {
    background-color: #222d32;
}

.skin-purple .user-panel > .info,
.skin-purple .user-panel > .info > a {
    color: #fff;
}

.skin-purple .sidebar-menu > li.header {
    color: #4b646f;
    background: #1a2226;
}

.skin-purple .sidebar-menu > li > a {
    border-left: 3px solid transparent;
}

.skin-purple .sidebar-menu > li:hover > a,
.skin-purple .sidebar-menu > li.active > a {
    color: #ffffff;
    background: #1e282c;
    border-left-color: #605ca8;
}

.skin-purple .sidebar-menu > li > .treeview-menu {
    margin: 0 1px;
    background: #2c3b41;
}

.skin-purple .sidebar a {
    color: #b8c7ce;
}

    .skin-purple .sidebar a:hover {
        text-decoration: none;
    }

.skin-purple .treeview-menu > li > a { /*color: #8aa4af;*/
}

    .skin-purple .treeview-menu > li.active > a,
    .skin-purple .treeview-menu > li > a:hover {
        color: #ffffff;
    }

.skin-purple .sidebar-form {
    border-radius: 3px;
    border: 1px solid #374850;
    margin: 10px 10px;
}

    .skin-purple .sidebar-form input[type="text"],
    .skin-purple .sidebar-form .btn {
        box-shadow: none;
        background-color: #374850;
        border: 1px solid transparent;
        height: 35px;
        -webkit-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out;
    }

    .skin-purple .sidebar-form input[type="text"] {
        color: #666;
        border-top-left-radius: 2px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 2px;
    }

        .skin-purple .sidebar-form input[type="text"]:focus,
        .skin-purple .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
            background-color: #fff;
            color: #666;
        }

            .skin-purple .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
                border-left-color: #fff;
            }

    .skin-purple .sidebar-form .btn {
        color: #999;
        border-top-left-radius: 0;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        border-bottom-left-radius: 0;
    }
/* * Skin: Purple * ------------*/ .skin-purple-light .main-header .navbar {
    background-color: #605ca8;
}

    .skin-purple-light .main-header .navbar .nav > li > a {
        color: #ffffff;
    }

        .skin-purple-light .main-header .navbar .nav > li > a:hover,
        .skin-purple-light .main-header .navbar .nav > li > a:active,
        .skin-purple-light .main-header .navbar .nav > li > a:focus,
        .skin-purple-light .main-header .navbar .nav .open > a,
        .skin-purple-light .main-header .navbar .nav .open > a:hover,
        .skin-purple-light .main-header .navbar .nav .open > a:focus,
        .skin-purple-light .main-header .navbar .nav > .active > a {
            background: rgba(0, 0, 0, 0.1);
            color: #f6f6f6;
        }

    .skin-purple-light .main-header .navbar .sidebar-toggle {
        color: #ffffff;
    }

        .skin-purple-light .main-header .navbar .sidebar-toggle:hover {
            color: #f6f6f6;
            background: rgba(0, 0, 0, 0.1);
        }

    .skin-purple-light .main-header .navbar .sidebar-toggle {
        color: #fff;
    }

        .skin-purple-light .main-header .navbar .sidebar-toggle:hover {
            background-color: #555299;
        }

@media (max-width: 767px) {
    .skin-purple-light .main-header .navbar .dropdown-menu li.divider {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .skin-purple-light .main-header .navbar .dropdown-menu li a {
        color: #fff;
    }

        .skin-purple-light .main-header .navbar .dropdown-menu li a:hover {
            background: #555299;
        }
}

.skin-purple-light .main-header .logo {
    background-color: #605ca8;
    color: #ffffff;
    border-bottom: 0 solid transparent;
}

    .skin-purple-light .main-header .logo:hover {
        background-color: #5d59a6;
    }

.skin-purple-light .main-header li.user-header {
    background-color: #605ca8;
}

.skin-purple-light .content-header {
    background: transparent;
}

.skin-purple-light .wrapper,
.skin-purple-light .main-sidebar,
.skin-purple-light .left-side {
    background-color: #f9fafc;
}

.skin-purple-light .content-wrapper,
.skin-purple-light .main-footer {
    border-left: 1px solid #d2d6de;
}

.skin-purple-light .user-panel > .info,
.skin-purple-light .user-panel > .info > a {
    color: #444444;
}

.skin-purple-light .sidebar-menu > li {
    -webkit-transition: border-left-color 0.3s ease;
    -o-transition: border-left-color 0.3s ease;
    transition: border-left-color 0.3s ease;
}

    .skin-purple-light .sidebar-menu > li.header {
        color: #848484;
        background: #f9fafc;
    }

    .skin-purple-light .sidebar-menu > li > a {
        border-left: 3px solid transparent;
        font-weight: 600;
    }

    .skin-purple-light .sidebar-menu > li:hover > a,
    .skin-purple-light .sidebar-menu > li.active > a {
        color: #000000;
        background: #f4f4f5;
    }

    .skin-purple-light .sidebar-menu > li.active {
        border-left-color: #605ca8;
    }

        .skin-purple-light .sidebar-menu > li.active > a {
            font-weight: 600;
        }

    .skin-purple-light .sidebar-menu > li > .treeview-menu {
        background: #f4f4f5;
    }

.skin-purple-light .sidebar a {
    color: #444444;
}

    .skin-purple-light .sidebar a:hover {
        text-decoration: none;
    }

.skin-purple-light .treeview-menu > li > a {
    color: #777777;
}

    .skin-purple-light .treeview-menu > li.active > a,
    .skin-purple-light .treeview-menu > li > a:hover {
        color: #000000;
    }

.skin-purple-light .treeview-menu > li.active > a {
    font-weight: 600;
}

.skin-purple-light .sidebar-form {
    border-radius: 3px;
    border: 1px solid #d2d6de;
    margin: 10px 10px;
}

    .skin-purple-light .sidebar-form input[type="text"],
    .skin-purple-light .sidebar-form .btn {
        box-shadow: none;
        background-color: #fff;
        border: 1px solid transparent;
        height: 35px;
        -webkit-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
        transition: all 0.3s ease-in-out;
    }

    .skin-purple-light .sidebar-form input[type="text"] {
        color: #666;
        border-top-left-radius: 2px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 2px;
    }

        .skin-purple-light .sidebar-form input[type="text"]:focus,
        .skin-purple-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
            background-color: #fff;
            color: #666;
        }

            .skin-purple-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
                border-left-color: #fff;
            }

    .skin-purple-light .sidebar-form .btn {
        color: #999;
        border-top-left-radius: 0;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        border-bottom-left-radius: 0;
    }

@media (min-width: 768px) {
    .skin-purple-light.sidebar-mini.sidebar-collapse .sidebar-menu > li > .treeview-menu {
        border-left: 1px solid #d2d6de;
    }
}




.bg-red,
.bg-yellow,
.bg-aqua,
.bg-blue,
.bg-light-blue,
.bg-green,
.bg-navy,
.bg-teal,
.bg-olive,
.bg-lime,
.bg-orange,
.bg-fuchsia,
.bg-purple,
.bg-maroon,
.bg-black,
.bg-red-active,
.bg-yellow-active,
.bg-aqua-active,
.bg-blue-active,
.bg-light-blue-active,
.bg-green-active,
.bg-navy-active,
.bg-teal-active,
.bg-olive-active,
.bg-lime-active,
.bg-orange-active,
.bg-fuchsia-active,
.bg-purple-active,
.bg-maroon-active,
.bg-black-active,
.callout.callout-danger,
.callout.callout-warning,
.callout.callout-info,
.callout.callout-success,
.alert-success,
.alert-danger,
.alert-error,
.alert-warning,
.alert-info,
.label-danger,
.label-info,
.label-warning,
.label-primary,
.label-success,
.modal-primary .modal-body,
.modal-primary .modal-header,
.modal-primary .modal-footer,
.modal-warning .modal-body,
.modal-warning .modal-header,
.modal-warning .modal-footer,
.modal-info .modal-body,
.modal-info .modal-header,
.modal-info .modal-footer,
.modal-success .modal-body,
.modal-success .modal-header,
.modal-success .modal-footer,
.modal-danger .modal-body,
.modal-danger .modal-header,
.modal-danger .modal-footer {
    color: #fff !important;
}

.bg-gray {
    color: #000;
    background-color: #d2d6de !important;
}

.bg-gray-light {
    background-color: #f7f7f7;
}

.bg-black {
    background-color: #111111 !important;
}

.bg-red,
.callout.callout-danger,
.alert-danger,
.alert-error,
.label-danger,
.modal-danger .modal-body {
    background-color: #dd4b39 !important;
}

.bg-yellow,
.callout.callout-warning,
.alert-warning,
.label-warning,
.modal-warning .modal-body {
    background-color: #f39c12 !important;
}

.bg-aqua,
.callout.callout-info,
.alert-info,
.label-info,
.modal-info .modal-body {
    background-color: #00c0ef !important;
}

.bg-blue {
    background-color: #0073b7 !important;
}

.bg-light-blue,
.label-primary,
.modal-primary .modal-body {
    background-color: #3c8dbc !important;
}

.bg-green,
.callout.callout-success,
.alert-success,
.label-success,
.modal-success .modal-body {
    background-color: #00a65a !important;
}

.bg-navy {
    background-color: #001f3f !important;
}

.bg-teal {
    background-color: #39cccc !important;
}

.bg-olive {
    background-color: #3d9970 !important;
}

.bg-lime {
    background-color: #01ff70 !important;
}

.bg-orange {
    background-color: #ff851b !important;
}

.bg-fuchsia {
    background-color: #f012be !important;
}

.bg-purple {
    background-color: #605ca8 !important;
}

.bg-maroon {
    background-color: #d81b60 !important;
}

.bg-gray-active {
    color: #000;
    background-color: #b5bbc8 !important;
}

.bg-black-active {
    background-color: #000000 !important;
}

.bg-red-active,
.modal-danger .modal-header,
.modal-danger .modal-footer {
    background-color: #d33724 !important;
}

.bg-yellow-active,
.modal-warning .modal-header,
.modal-warning .modal-footer {
    background-color: #db8b0b !important;
}

.bg-aqua-active,
.modal-info .modal-header,
.modal-info .modal-footer {
    background-color: #00a7d0 !important;
}

.bg-blue-active {
    background-color: #005384 !important;
}

.bg-light-blue-active,
.modal-primary .modal-header,
.modal-primary .modal-footer {
    background-color: #357ca5 !important;
}

.bg-green-active,
.modal-success .modal-header,
.modal-success .modal-footer {
    background-color: #008d4c !important;
}

.bg-navy-active {
    background-color: #001a35 !important;
}

.bg-teal-active {
    background-color: #30bbbb !important;
}

.bg-olive-active {
    background-color: #368763 !important;
}

.bg-lime-active {
    background-color: #00e765 !important;
}

.bg-orange-active {
    background-color: #ff7701 !important;
}

.bg-fuchsia-active {
    background-color: #db0ead !important;
}

.bg-purple-active {
    background-color: #555299 !important;
}

.bg-maroon-active {
    background-color: #ca195a !important;
}

[class^="bg-"].disabled {
    opacity: 0.65;
    filter: alpha(opacity=65);
}

.text-red {
    color: #dd4b39 !important;
}

.text-yellow {
    color: #f39c12 !important;
}

.text-aqua {
    color: #00c0ef !important;
}

.text-blue {
    color: #0073b7 !important;
}

.text-black {
    color: #111111 !important;
}

.text-light-blue {
    color: #3c8dbc !important;
}

.text-green {
    color: #00a65a !important;
}

.text-gray {
    color: #d2d6de !important;
}

.text-navy {
    color: #001f3f !important;
}

.text-teal {
    color: #39cccc !important;
}

.text-olive {
    color: #3d9970 !important;
}

.text-lime {
    color: #01ff70 !important;
}

.text-orange {
    color: #ff851b !important;
}

.text-fuchsia {
    color: #f012be !important;
}

.text-purple {
    color: #605ca8 !important;
}

.text-maroon {
    color: #d81b60 !important;
}

.link-muted {
    color: #7a869d;
}

    .link-muted:hover,
    .link-muted:focus {
        color: #606c84;
    }

.link-black {
    color: #666;
}

    .link-black:hover,
    .link-black:focus {
        color: #999;
    }

.hide {
    display: none !important;
}

.no-border {
    border: 0 !important;
}

.no-padding {
    padding: 0 !important;
}

.no-margin {
    margin: 0 !important;
}

.no-shadow {
    box-shadow: none !important;
}

.list-unstyled,
.chart-legend,
.contacts-list,
.users-list,
.mailbox-attachments {
    list-style: none;
    margin: 0;
    padding: 0;
}

.list-group-unbordered > .list-group-item {
    border-left: 0;
    border-right: 0;
    border-radius: 0;
    padding-left: 0;
    padding-right: 0;
}

.flat {
    border-radius: 0 !important;
}

.text-bold,
.text-bold.table td,
.text-bold.table th {
    font-weight: 700;
}

.text-sm {
    font-size: 12px;
}

.jqstooltip {
    padding: 5px !important;
    width: auto !important;
    height: auto !important;
}

.bg-teal-gradient {
    background: #39cccc !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #39cccc), color-stop(1, #7adddd)) !important;
    background: -ms-linear-gradient(bottom, #39cccc, #7adddd) !important;
    background: -moz-linear-gradient(center bottom, #39cccc 0%, #7adddd 100%) !important;
    background: -o-linear-gradient(#7adddd, #39cccc) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#7adddd', endColorstr='#39cccc', GradientType=0) !important;
    color: #fff;
}

.bg-light-blue-gradient {
    background: #3c8dbc !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #3c8dbc), color-stop(1, #67a8ce)) !important;
    background: -ms-linear-gradient(bottom, #3c8dbc, #67a8ce) !important;
    background: -moz-linear-gradient(center bottom, #3c8dbc 0%, #67a8ce 100%) !important;
    background: -o-linear-gradient(#67a8ce, #3c8dbc) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#67a8ce', endColorstr='#3c8dbc', GradientType=0) !important;
    color: #fff;
}

.bg-blue-gradient {
    background: #0073b7 !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #0073b7), color-stop(1, #0089db)) !important;
    background: -ms-linear-gradient(bottom, #0073b7, #0089db) !important;
    background: -moz-linear-gradient(center bottom, #0073b7 0%, #0089db 100%) !important;
    background: -o-linear-gradient(#0089db, #0073b7) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0089db', endColorstr='#0073b7', GradientType=0) !important;
    color: #fff;
}

.bg-aqua-gradient {
    background: #00c0ef !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #00c0ef), color-stop(1, #14d1ff)) !important;
    background: -ms-linear-gradient(bottom, #00c0ef, #14d1ff) !important;
    background: -moz-linear-gradient(center bottom, #00c0ef 0%, #14d1ff 100%) !important;
    background: -o-linear-gradient(#14d1ff, #00c0ef) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#14d1ff', endColorstr='#00c0ef', GradientType=0) !important;
    color: #fff;
}

.bg-yellow-gradient {
    background: #f39c12 !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #f39c12), color-stop(1, #f7bc60)) !important;
    background: -ms-linear-gradient(bottom, #f39c12, #f7bc60) !important;
    background: -moz-linear-gradient(center bottom, #f39c12 0%, #f7bc60 100%) !important;
    background: -o-linear-gradient(#f7bc60, #f39c12) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f7bc60', endColorstr='#f39c12', GradientType=0) !important;
    color: #fff;
}

.bg-purple-gradient {
    background: #605ca8 !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #605ca8), color-stop(1, #9491c4)) !important;
    background: -ms-linear-gradient(bottom, #605ca8, #9491c4) !important;
    background: -moz-linear-gradient(center bottom, #605ca8 0%, #9491c4 100%) !important;
    background: -o-linear-gradient(#9491c4, #605ca8) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#9491c4', endColorstr='#605ca8', GradientType=0) !important;
    color: #fff;
}

.bg-green-gradient {
    background: #00a65a !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #00a65a), color-stop(1, #00ca6d)) !important;
    background: -ms-linear-gradient(bottom, #00a65a, #00ca6d) !important;
    background: -moz-linear-gradient(center bottom, #00a65a 0%, #00ca6d 100%) !important;
    background: -o-linear-gradient(#00ca6d, #00a65a) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00ca6d', endColorstr='#00a65a', GradientType=0) !important;
    color: #fff;
}

.bg-red-gradient {
    background: #dd4b39 !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #dd4b39), color-stop(1, #e47365)) !important;
    background: -ms-linear-gradient(bottom, #dd4b39, #e47365) !important;
    background: -moz-linear-gradient(center bottom, #dd4b39 0%, #e47365 100%) !important;
    background: -o-linear-gradient(#e47365, #dd4b39) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#e47365', endColorstr='#dd4b39', GradientType=0) !important;
    color: #fff;
}

.bg-black-gradient {
    background: #111111 !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #111111), color-stop(1, #2b2b2b)) !important;
    background: -ms-linear-gradient(bottom, #111111, #2b2b2b) !important;
    background: -moz-linear-gradient(center bottom, #111111 0%, #2b2b2b 100%) !important;
    background: -o-linear-gradient(#2b2b2b, #111111) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#2b2b2b', endColorstr='#111111', GradientType=0) !important;
    color: #fff;
}

.bg-maroon-gradient {
    background: #d81b60 !important;
    background: -webkit-gradient(linear, left bottom, left top, color-stop(0, #d81b60), color-stop(1, #e73f7c)) !important;
    background: -ms-linear-gradient(bottom, #d81b60, #e73f7c) !important;
    background: -moz-linear-gradient(center bottom, #d81b60 0%, #e73f7c 100%) !important;
    background: -o-linear-gradient(#e73f7c, #d81b60) !important;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#e73f7c', endColorstr='#d81b60', GradientType=0) !important;
    color: #fff;
}
