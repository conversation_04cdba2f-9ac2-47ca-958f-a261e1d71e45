﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>不良原因</title>
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <script src="../js/ajaxfileupload.js" type="text/javascript"></script>
    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all">
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/PrdAssist.js" type="text/javascript"></script>

    <script src="../js/layer/layer.js" type="text/javascript"></script>
    <link href="../css/XC.css" rel="stylesheet" />

    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        //$('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=28-1' });
                    }
                }
            })
        });



    </script>


    <script type="text/javascript">


        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#Defectslist',
                id: 'DefectslistID',
                url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=11',
                height: 'full-80',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'CNo', title: '不良原因编号', width: 120, sort: true },
                    { field: 'CDesc', title: '不良原因名称', sort: true },
                    { field: 'CKind', title: '不良原因分类', sort: true },
                    { field: 'Remark', title: '备注', width: 300 },
                    { field: 'InMan', title: '录入人' },
                    { field: 'InDate2', title: '录入时间' },
                    { field: 'op', title: '操作', width: 140, toolbar: '#barDemo', fixed: 'right' }
                ]],
                page: true,
                //  done: function(res, curr, count) {
                //     var that = this.elem.next();
                //     res.data.forEach(function(item, index) {
                //         if (item.Flag == "0") { // 说明评估日期已到期
                //             $(".layui-table-body tbody tr[data-index='" + index + "']").css({ 'backgroundColor': "Pink" });
                //             $(".layui-table-body tbody tr[data-index='" + index + "']").find('td[data-field="LastContactTime"]').css({ 'color': "red" });
                //
                //         }
                //     });
                // }


            });




            //监听是否选中操作
            table.on('checkbox(Defectslist)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);


            });


            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(Defectslist)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

            });

            //监听单元格编辑
            table.on('edit(Defectslist)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });


            //监听行工具事件
            table.on('tool(Defectslist)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值

                if (layEvent === 'del') {

                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("确定要删除该不良原因吗？不良编号：")

                    //设置删除的对象
                    $("#hint-value").html(data.CNo)

                    $("#txtDelCNo").val(data.CNo)

                } else if (layEvent === 'edit') {
                    $('#head-title1').html("修改不良原因信息");
                    $('#txtAEFlag').val("5");//修改标记

                    $("#txtWNo").val(data.CNo);
                    $("#txtWDesc").val(data.CDesc);
                    $("#txtWKind").val(data.CKind);
                    $("#txtRemark").val(data.Remark);


                    $("#txtWNo").attr({ "disabled": "disabled" });

                    $("#div_warning").html("");
                    $("#div_warning").hide();


                    $("#ShowOne").css("display", "block")
                    $("#ShowOne-fade").css("display", "block")

                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }





            });


            //  查询 --
            $('#DefectsBut_open').click(function () {

                var sWNo = $("#txtSWNo").val();  //
                var sName = $("#txtSWDesc").val();  //
                var sCKind = $("#txtSCKind").val();

                var Data = '';
                var Params = { No: sWNo, Name: sName, Item: "", Status: "", BDate: "", EDate: "", A: sCKind, B: "", C: "", D: "", E: "" };
                var Data = JSON.stringify(Params);

                table.reload('DefectslistID', {
                    method: 'post',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=11&Data=' + Data,
                    where: {
                        'No': sWNo,
                        'name': sName
                    }, page: {
                        curr: 1
                    }
                });
            });

            $("#DefectsCauseDel_Btn").click(function () {

                //向服务端发送禁用指令
                var sWNo = $("#txtDelCNo").val();
                var sFlag = "6";//删除标记

                var Data = '';
                var Params = { No: sWNo, Name: "", Item: "", A: "", B: "", C: "", D: "", E: "", F: "", I: "", J: "", K: "", L: "", Kind: "", Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);
                $.ajax({
                    type: "POST",
                    url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('删除成功！');

                            $('#DefectsBut_open').click();  // 重新查询

                            closeDialog()

                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                            layer.msg('工序已用于工艺流程，不能删除！');

                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST1') {
                            layer.msg('工序已用于BOM设计，不能删除！');

                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST2') {
                            layer.msg('工序已用于产品对应标贴，不能删除！');

                        }
                        else {
                            layer.msg('删除失败，请重试！');
                            $('#DefectsBut_open').click();  // 重新查询
                        }
                    },
                    error: function (data) {
                        layer.msg('删除失败2，请重试！');
                        $('#DefectsBut_open').click();  // 重新查询
                    }
                });

            })




        });




        function openDialog(n) {  // 新增
            $('#head-title1').html("新增不良原因信息");
            $('#txtAEFlag').val("4");//新增的类型标记


            $("#txtWNo").val("");
            $("#txtWDesc").val("");
            $("#txtWKind").val("");
            $("#txtRemark").val("");

            $("#div_warning").html("");
            $("#div_warning").hide();

            $("#ShowOne").css("display", "block")
            $("#ShowOne-fade").css("display", "block")
        }

        function closeDialog() {
            $("#ShowOne").css("display", "none")
            $("#ShowOne-fade").css("display", "none")
            $("#ShowDel").css("display", "none")
            $("#ShowDel-fade").css("display", "none")
        }





    </script>


    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

        })



    </script>

    <style type="text/css">

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        /* .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: #bbbcc7;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=60);
        }*/

        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }

        .LabelDelBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #da542e;
        }

        .LabelAddBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #579fd8;
        }
    </style>

</head>
<body>
    <div class="div_find">

        <p>
            <label class="find_labela">不良原因编号</label> <input type="text" id="txtSWNo" class="find_input" />
            <label class="find_labela">描述</label><input type="text" id="txtSWDesc" class="find_input" />
            <label class="find_labela">不良类型</label><input type="text" id="txtSCKind" class="find_input" />
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="DefectsBut_open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="javascript:openDialog(1)">添加</button>
        </p>
    </div>

    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="Defectslist" lay-filter="Defectslist"></table>

        <script type="text/html" id="barDemo">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="edit">修改</button>
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>
        </script>
    </div>


    <!--弹出层-->
    <div class="XC-modal XC-modal-md" id="ShowOne" style="height:350px">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1"></span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <form class="XC-Form-block">
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block" style="width:85px">不良原因编号</span>
                    <input type="text" class="XC-Input-block" id="txtWNo" name="txtWNo" readonly=readonly placeholder="系统自动产生" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block" style="width:85px">不良原因名称<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <input type="text" class="XC-Input-block" id="txtWDesc" name="txtWDesc" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block" style="width:85px">不良类型<span class="XC-Font-Red XC-Size-sm">*</span></span>
                    <input type="text" class="XC-Input-block" id="txtWKind" name="txtWKind" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Textarea-block" style="width:85px">备注</span>
                    <textarea class="XC-Textarea-block" id="txtRemark" name="txtRemark"></textarea>
                </div>
            </form>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="DefectsCauseSave_Btn">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="DefectsCauseSave_Btn" onclick="closeDialog()">取消</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>

    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelCNo" name="txtDelCNo" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="DefectsCauseDel_Btn">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>

    <!--消息提示-->
    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>
</html>