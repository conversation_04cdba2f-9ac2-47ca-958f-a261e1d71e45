﻿using System;
using System.Collections;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml.Linq;
using BLL;
using Newtonsoft.Json;
using System.Web.SessionState;
using Common;
using System.Collections.Generic;
using WXCommon;
using System.Text;
using Newtonsoft.Json.Linq;
using System.Net;
using System.IO;
using System.Reflection;
using System.Web.Script.Serialization;

namespace Web.Service
{
    /// <summary>
    /// $codebehindclassname$ 的摘要说明
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class UDIAjax : IHttpHandler, IRequiresSessionState
    {
        string sAMFlag = string.Empty;

        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "text/plain";
            string Result = string.Empty;
            string Operate = context.Request.Params["OP"];
            string DataParams = context.Request.Params["Data"];
            string sItem = context.Request.Params["Item"];
            sAMFlag = context.Request.Params["CFlag"];
            string sKind = context.Request.Params["CKind"];  // 接收所有类别
            string sStatus = context.Request.Params["CStatus"];  // 状态
            string sCNo = context.Request.Params["CNO"];  // 接收所有编号  
            string sMNo = context.Request.Params["CMNO"];  // 物料编码 
            string sPNo = context.Request.Params["CPNO"];  //  
            string sSPNo = context.Request.Params["CSPNO"];  //  
            string sCName = context.Request.Params["CNAME"];  // 接收所有名称
            string sCustNo = context.Request.Params["CCustNo"];
            string sAddNum = context.Request.Params["AddNum"];  // 数据加载的次数
            int slimit = 0;
            int spage = 0;

            switch (Operate)
            {
                case "GetPrintInfo":  // 获取打印信息
                    Result = GetPrintInfo(DataParams, sCNo, sPNo, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "APPUDIInfo": //  一键申报（支持单个，批量）
                    Result = APPUDIInfo(DataParams, sCNo);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;
                case "GETUDIInfo": //  
                    Result = GETUDIInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "OPUDIInfo": //DI 码操作
                    Result = OPUDIInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetDIOtherInfo": //获取DI码的其他信息-包装信息-储存信息-临床尺寸
                    Result = GetDIOtherInfo(sCNo, sAMFlag);
                    break;

                case "PrintDItoExcel":  //导出DI到EXCEL
                    Result = PrintDItoExcel(DataParams, sCNo, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;










            }
        }









        #region 获取UDI信息
        public string GetPrintInfo(string Params, string sNo, string sOrder, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sKind = string.Empty;
            string sFlag = string.Empty;


            if (string.IsNullOrEmpty(sOrder)){
                sOrder = "";
            }

            if (string.IsNullOrEmpty(Flag)) {
                sFlag = "1";  // 预览
            }
            else {
                sFlag = Flag;
            }

            DataTable list = BaseModuleBll.GetPrintLabelInfo("", "", sOrder, sNo, "", "", 2000, 1, "", sComp, sFlag);
            if (list.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }
            HttpContext context = HttpContext.Current;

            Result = JsonConvert.SerializeObject(new { Table = list });

            return Result;
        }
        #endregion





        #region 一键申报（支持单个，批量）
        public string APPUDIInfo(string Params, string sNo)
        {
            string result = string.Empty;
            string Result = "填报数据超时";
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sFlag = string.Empty;
            string sOKFlag = string.Empty;
            string stokenURL = string.Empty;
            string sSaveURL = string.Empty;
            string sCommitURL = string.Empty;
            string appID = string.Empty;
            string appSecret = string.Empty;
            string sTYSHXYDM = string.Empty; 


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }


            var AnonymousUser = new { DI = String.Empty, Flag = String.Empty };
            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            string[] diList = new string[] { Item.DI };


            // 获取一下信息：如填报的地址，appID 等
            DataTable UrlDt = BaseModuleBll.GetURLAndBaseInfo("", "", "", sComp, "1");
            if (UrlDt.Rows.Count > 0){
                stokenURL = UrlDt.Rows[0]["C1"].ToString();
                sSaveURL = UrlDt.Rows[0]["C2"].ToString();
                sCommitURL = UrlDt.Rows[0]["C3"].ToString();
            }
            else {
                Message = "No_URL";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }

            // 获取一下信息：如填报的地址，appID 等
            DataTable UrlCP = BaseModuleBll.GetURLAndBaseInfo("", "", "", sComp, "2");
            if (UrlCP.Rows.Count > 0){
                appID = UrlCP.Rows[0]["APPID"].ToString();
                appSecret = UrlCP.Rows[0]["APPSECRET"].ToString();
                sTYSHXYDM = UrlCP.Rows[0]["TaxNumber"].ToString();
            }
            else{
                Message = "No_APP";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }


            try
            {
                string token = GetToken(stokenURL, appID, appSecret, sTYSHXYDM);
                string pDeviceRecordKey = "";
                string jsonStr = JsonConvert.SerializeObject(GetReturnObj(token, diList, ref pDeviceRecordKey)).ToString().Replace('"', '\'');

                //推送DI信息
                string returnStr1 = Post(sSaveURL, jsonStr);  // 测试的：https://udid.nmpa.gov.cn/api/beta/v2/company/identifier/save

                //测试Json数据->对象化操作
                JavaScriptSerializer javaScriptSerializer = new JavaScriptSerializer();
                try
                {
                    DeclareResult dResult = javaScriptSerializer.Deserialize<DeclareResult>(returnStr1);

                    if (returnStr1.IndexOf("业务执行成功") >= 0)
                    {
                        result = "1.数据填报成功!";
                        jsonStr = @"{'accessToken':'" + token + "'";

                        for (var i = 0; i < diList.Length; i++)
                        {
                            string vDi = diList[i];
                            successList successList = dResult.successList.Find(o => o.ZXXSDYCPBS == vDi);
                            string vdeviceRecordKey = successList.deviceRecordKey;
                            jsonStr += ",'dataSet':[";
                            jsonStr += @"{'deviceRecordKey':'" + vdeviceRecordKey + "', 'ZXXSDYCPBS':'" + vDi + "'}";
                            jsonStr += "]";
                        }

                        jsonStr += "}";

                        //提交DI信息到药监局
                        string returnStr2 = Post(sCommitURL, jsonStr);  // 测试的  "https://udid.nmpa.gov.cn/api/beta/v2/company/identifier/submit"
                        if (returnStr2 == "{}" || returnStr2.IndexOf("业务执行成功") >= 0)
                        {
                            result += "2.数据提交成功!";
                        }
                        else
                        {
                            result += "2.数据提交失败!药监局接口返回值：" + returnStr1.ToString();
                        }
                    }
                    else
                    {
                        result = "1.数据填报失败!药监局接口返回值：" + returnStr1.ToString();
                    }
                }
                catch (Exception ex)
                {
                    result = "1.数据填报失败,填报数据异常，请检查!";
                }

                return JsonConvert.SerializeObject(new { Msg = Message, MsgTxt = result });
                //return result;
            }
            catch (Exception ex)
            {
                return Result;
            }
        }
        #endregion


        private string GetToken(string Url, string appId, string appSecret, string TYSHXYDM)
        {
            //string returnStr = GetReturnJson("https://udid.nmpa.gov.cn/api/v2/token/get", @"params={appId:'9d62b5bb4957447a9ee9164f4c4e9343',appSecret:'4c520be988c8453ba478096ce5672bd0',TYSHXYDM:'91440300568536676G'}");
            string returnStr = GetReturnJson(Url, @"params={appId:'" + appId + "',appSecret:'" + appSecret + "',TYSHXYDM:'" + TYSHXYDM + "'}");
            JObject jo = (JObject)JsonConvert.DeserializeObject(returnStr);
            return jo["accessToken"].ToString();
        }


        private ReturnOb2 GetReturnObj(string token, string[] di, ref string pDeviceRecordKey)
        {
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }

            string[] sDi = di;
            List<dataSet> list = new List<dataSet>();

            for (int i = 0; i < sDi.Length; i++)
            {
                string vDi = sDi[i];
                DataTable dtDIInfo = BaseModuleBll.GetSerielInfo("", "", vDi, "", "2021-01-01", "2999-12-31", 5000, 1, sMan, sComp, "27");
                DataTable dtDEVICEPACKAGE = BaseModuleBll.GetSerielInfo("", "", vDi, "", "2021-01-01", "2999-12-31", 5000, 1, sMan, sComp, "27-1");  // 查询DI-包装信息
                DataTable dtDEVICESTORAGE = BaseModuleBll.GetSerielInfo("", "", vDi, "", "2021-01-01", "2999-12-31", 5000, 1, sMan, sComp, "27-2");  // 查询DI-储存条件
                DataTable dtDEVICECLINICAL = BaseModuleBll.GetSerielInfo("", "", vDi, "", "2021-01-01", "2999-12-31", 5000, 1, sMan, sComp, "27-3");  // 查询DI-临床尺寸

                foreach (DataRow drDI in dtDIInfo.Rows)
                {
                    dataSet ds = new dataSet();
                    ds.uploadType = "add";//add或modify
                    ds.deviceRecordKey = "";// "445634SDFSFE45WEEWREW7DFDSFE" + DateTime.Now.ToString("YYYYMMDDmmss");
                    pDeviceRecordKey += "445634SDFSFE45WEEWREW7DFDSFE" + DateTime.Now.ToString("YYYYMMDDmmss");
                    ds.ZXXSDYCPBS = drDI["ZXXSDYCPBS"].ToString();
                    ds.ZXXSDYZSYDYDSL = drDI["ZXXSDYZSYDYDSL"].ToString();
                    ds.SYDYCPBS = "";//drDI["SYDYCPBS"].ToString();  // 使用单元产品标识  ,空才可以上传
                    ds.CPBSBMTXMC = drDI["CPBSBMTXMC"].ToString();
                    ds.SFYBTZJBS = drDI["SFYBTZJBS"].ToString() == "是" ? "1" : "0";   // 是否有本体直接标识
                    ds.BTCPBSYZXXSDYCPBSSFYZ = drDI["BTCPBSYZXXSDYCPBSSFYZ"].ToString() == "是" ? "1" : "";  
                    ds.BTCPBS = drDI["BTCPBS"].ToString();
                    ds.BSZT = drDI["CPBSZT"].ToString().Replace("一维码", "1").Replace("二维码", "2").Replace("RFID", "3").Replace("其他", "4");  // 标识载体 请按照[1,2,3,4]填写；   CPBSZT='2', drDI["CPBSZT"].ToString()
                    ds.SFYZCBAYZ = drDI["SFYZCBACPBSYZ"].ToString() == "是" ? "1" : "0";  //是否与注册/备案产品标识一致  
                    ds.ZCBACPBS = drDI["ZCBACPBS"].ToString();
                    ds.CPBSFBRQ = drDI["CPBSFBRQ"].ToString();
                    ds.CPMCTYMC = drDI["CPMCTYMC"].ToString();
                    ds.SPMC = drDI["SPMC"].ToString();
                    ds.GGXH = drDI["GGXH"].ToString();
                    ds.SFWBLZTLCP = drDI["SFWBLZTLCP"].ToString() == "是" ? "1" : "0";   
                    ds.CPMS = drDI["CPMS"].ToString();
                    ds.CPHHHBH = drDI["CPHHHBH"].ToString();
                    ds.CPLX = drDI["QXLB"].ToString() == "器械" ? "1" : "2";      // CPLX":"产品类型：1 器械，2 体外诊断试剂   drDI["CPLX"].ToString() == "器械" ? "1" : "2";   drDI["CPLX"].ToString(); 
                    ds.FLBM = drDI["FLBM"].ToString();
                    ds.YFLBM = drDI["YFLBM"].ToString();
                    ds.YLQXZCRBARMC = drDI["YLQXZCRBARMC"].ToString();
                    ds.YLQXZCRBARYWMC = drDI["YLQXZCRBARYWMC"].ToString();
                    ds.TYSHXYDM = drDI["TYSHXYDM"].ToString();
                    ds.ZCZBHHZBAPZBH = drDI["ZCZBHHZBAPZBH"].ToString();
                    ds.HCHZSB = drDI["HCHZSB"].ToString() == "耗材" ? "0" : "1";    // HCHZSB":"耗材或者设备:0 耗材， 1 设备",  HCHZSB  -- 必输
                    ds.QXLB = drDI["QXLB"].ToString();
                    ds.SFBJWYCXSY = drDI["SFBJWYCXSY"].ToString() == "是" ? "1" : "0"; 
                    ds.ZDCFSYCS = drDI["ZDCFSYCS"].ToString();
                    ds.SFWWJBZ = drDI["SFWWJBZ"].ToString() == "是" ? "1" : "0";
                    ds.SYQSFXYJXMJ = drDI["SYQSFXYJXMJ"].ToString() == "是" ? "1" : "0"; 
                    ds.MJFS = drDI["MJFS"].ToString();
                    ds.YBBM = drDI["YBBM"].ToString();
                    ds.CPLB = drDI["CPLB"].ToString();
                    ds.CGZMRAQXGXX = drDI["CGZMRAQXGXX"].ToString().Substring(0,1);  // 0 安全   1 条件安全   2 不安全  3 说明书或标签不包含MR安全性信息

                    List<devicePackage> dpDevicePackage = new List<devicePackage>();
                    foreach (DataRow dr in dtDEVICEPACKAGE.Rows)
                    {
                        devicePackage dp = new devicePackage();
                        dp.CPBZJB = dr["CPBZJB"].ToString();
                        dp.BZCPBS = dr["BZCPBS"].ToString();
                        dp.BZNHXYJCPBSSL = dr["BZNHXYJCPBSSL"].ToString();
                        dp.BZNHXYJBZCPBS = drDI["ZXXSDYCPBS"].ToString();
                        dpDevicePackage.Add(dp);
                    }
                    ds.devicePackage = dpDevicePackage;

                    List<deviceStorage> dsDeviceStorage = new List<deviceStorage>();

                    if (dsDeviceStorage.Count == 0)
                    {
                        deviceStorage dp = new deviceStorage();
                        dp.CCHCZTJ = "";
                        dp.ZDZ = "";
                        dp.ZGZ = "";
                        dp.JLDW = "";
                        dsDeviceStorage.Add(dp);
                    }
                    else
                    {
                        foreach (DataRow dr in dtDEVICESTORAGE.Rows)
                        {
                            deviceStorage dp = new deviceStorage();
                            dp.CCHCZTJ = dr["CCHCZTJ"].ToString();
                            dp.ZDZ = dr["ZDZ"].ToString();
                            dp.ZGZ = dr["ZGZ"].ToString();
                            dp.JLDW = dr["JLDW"].ToString();
                            dsDeviceStorage.Add(dp);
                        }
                    }
                    ds.deviceStorage = dsDeviceStorage;

                    List<deviceClinical> dcDeviceClinical = new List<deviceClinical>();
                    foreach (DataRow dr in dtDEVICECLINICAL.Rows)
                    {
                        deviceClinical dp = new deviceClinical();
                        dp.LCSYCCLX = dr["LCSYCCLX"].ToString();
                        dp.CCZ = dr["CCZ"].ToString();
                        dp.CCDW = dr["CCDW"].ToString();
                        dcDeviceClinical.Add(dp);
                    }
                    ds.deviceClinical = dcDeviceClinical;


                    List<contactList> dcContactList = new List<contactList>();
                    contactList dpContactList = new contactList();
                    dpContactList.QYLXRCZ = "0755-86092558";
                    dpContactList.QYLXRYX = "<EMAIL>";
                    dpContactList.QYLXRDH = "0755-82128411";
                    dcContactList.Add(dpContactList);
                    ds.contactList = dcContactList;


                    ds.TSCCHCZTJ = drDI["TSCCHCZTJ"].ToString();  // "TSCCHCZTJ":"特殊存储或操作条件",
                    ds.TSCCSM = drDI["TSCCSM"].ToString();  // TSCCSM":"特殊使用尺寸说明",
                    ds.SCBSSFBHPH = drDI["SCBSSFBHPH"].ToString() == "是" ? "1" : "0";
                    ds.SCBSSFBHXLH = drDI["SCBSSFBHXLH"].ToString() == "是" ? "1" : "0";
                    ds.SCBSSFBHSCRQ = drDI["SCBSSFBHSCRQ"].ToString() == "是" ? "1" : "0";
                    ds.SCBSSFBHSXRQ = drDI["SCBSSFBHSXRQ"].ToString() == "是" ? "1" : "0"; 
                    ds.QTXXDWZLJ = drDI["QTXXDWZLJ"].ToString();
                    ds.TSRQ = drDI["TSRQ"].ToString();
                    ds.BGSM = drDI["BGSM"].ToString();
                    list.Add(ds);
                }

            }
            // 把DataTable转换为IList------用来将数据库中的数据自动转换json格式的
            ReturnOb2 obj2 = new ReturnOb2();
            obj2.accessToken = token;
            obj2.dataSet = list;
            return obj2;
        }

        #region 转换table

        public static IList<deviceClinical> ConvertTo<deviceClinical>(DataTable table)
        {
            if (table == null)
            {
                return null;
            }

            List<DataRow> rows = new List<DataRow>();

            foreach (DataRow row in table.Rows)
            {
                rows.Add(row);
            }

            return ConvertTo<deviceClinical>(rows);
        }

        public static IList<deviceClinical> ConvertTo<deviceClinical>(IList<DataRow> rows)
        {
            IList<deviceClinical> list = null;

            if (rows != null)
            {
                list = new List<deviceClinical>();

                foreach (DataRow row in rows)
                {
                    deviceClinical item = CreateItem<deviceClinical>(row);
                    list.Add(item);
                }
            }

            return list;
        }

        public static deviceClinical CreateItem<deviceClinical>(DataRow row)
        {
            deviceClinical obj = default(deviceClinical);
            if (row != null)
            {
                obj = Activator.CreateInstance<deviceClinical>();

                foreach (DataColumn column in row.Table.Columns)
                {
                    PropertyInfo prop = obj.GetType().GetProperty(column.ColumnName);
                    try
                    {
                        object value = row[column.ColumnName];
                        prop.SetValue(obj, value, null);
                    }
                    catch
                    {  //You can log something here     
                        //throw;    
                    }
                }
            }

            return obj;
        }

        #endregion

        private string GetReturnJson(string url, string param)
        {

            System.Net.WebClient wCient = new System.Net.WebClient();
            wCient.Headers.Add("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
            wCient.Headers.Add("Access-Control-Allow-Origin", "*");
            wCient.Headers.Add("UserAgent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.2; SV1; .NET CLR 1.1.4322; .NET CLR 2.0.50727)");
            wCient.Encoding = System.Text.Encoding.UTF8;
            byte[] postData = Encoding.ASCII.GetBytes(param);
            byte[] responseData = wCient.UploadData(url, "POST", postData);
            string returnStr = Encoding.UTF8.GetString(responseData);//返回接受的数据 
            return returnStr;
        }

        /// <summary>
        /// POST整个字符串到URL地址中
        /// </summary>
        /// <param name="Url"></param>
        /// <param name="jsonParas"></param>
        /// <returns></returns>
        public string Post(string Url, string jsonParas)
        {
            string strURL = Url;

            //创建一个HTTP请求
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(strURL);
            //Post请求方式
            request.Method = "POST";
            request.Timeout = 360000;
            //内容类型
            request.ContentType = "application/x-www-form-urlencoded";

            //设置参数，并进行URL编码
            string paraUrlCoded = "&params=" + System.Web.HttpUtility.UrlEncode(jsonParas);

            byte[] payload;
            //将Json字符串转化为字节
            payload = System.Text.Encoding.UTF8.GetBytes(paraUrlCoded);
            //设置请求的ContentLength
            request.ContentLength = payload.Length;
            //发送请求，获得请求流

            Stream writer;
            try
            {
                writer = request.GetRequestStream();//获取用于写入请求数据的Stream对象
            }
            catch (Exception)
            {
                writer = null;
                Console.Write("连接服务器失败!");
            }
            //将请求参数写入流
            writer.Write(payload, 0, payload.Length);
            writer.Close();//关闭请求流

            //String strValue = "";//strValue为http响应所返回的字符流
            HttpWebResponse response;
            try
            {
                //获得响应流
                response = (HttpWebResponse)request.GetResponse();
            }
            catch (WebException ex)
            {
                response = ex.Response as HttpWebResponse;
            }

            Stream s = response.GetResponseStream();

            //Stream postData = Request.InputStream;
            Stream postData = HttpContext.Current.Request.InputStream;
            StreamReader sRead = new StreamReader(s);
            string postContent = sRead.ReadToEnd();
            sRead.Close();

            return postContent;//返回Json数据
        }




        #region 获取UDI信息
        public string GETUDIInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sFlag = string.Empty;
            string sOKFlag = string.Empty;
            string sKind = string.Empty;
            string stokenURL = string.Empty;
            string sSaveURL = string.Empty;
            string sCommitURL = string.Empty;
            string sGetDIURL = string.Empty;
            string appID = string.Empty;
            string appSecret = string.Empty;
            string sTYSHXYDM = string.Empty;



            var AnonymousUser = new{ MNo = String.Empty, MName = String.Empty, Model = String.Empty, };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);



            // 获取一下信息：如填报的地址，appID 等
            DataTable UrlDt = BaseModuleBll.GetURLAndBaseInfo("", "", "", sComp, "1");
            if (UrlDt.Rows.Count > 0) {
                stokenURL = UrlDt.Rows[0]["C1"].ToString();
                sSaveURL = UrlDt.Rows[0]["C2"].ToString();
                sCommitURL = UrlDt.Rows[0]["C3"].ToString();
                sGetDIURL = UrlDt.Rows[0]["C6"].ToString();
            }
            else{
                Message = "No_URL";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }

            // 获取一下信息：如填报的地址，appID 等
            DataTable UrlCP = BaseModuleBll.GetURLAndBaseInfo("", "", "", sComp, "2");
            if (UrlCP.Rows.Count > 0)  {
                appID = UrlCP.Rows[0]["APPID"].ToString();
                appSecret = UrlCP.Rows[0]["APPSECRET"].ToString();
                sTYSHXYDM = UrlCP.Rows[0]["TaxNumber"].ToString();
            }
            else{
                Message = "No_APP";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }

            string token = GetToken(stokenURL, appID, appSecret, sTYSHXYDM);

            // -- 1 -- 获取上传的DI数据
            string returnStr1 = GetReturnJson(sGetDIURL, @"params={accessToken:'" + token + "',startTime:'2021-01-01 12:12:12',endTime:'2021-12-31 12:12:12',currentPageNumber:'1' }");
            HttpContext.Current.Response.Write(returnStr1);

            Result = JsonConvert.SerializeObject(new { Msg = returnStr1 });

            return Result;
        }
        #endregion



        #region 插入或更新 删除 DI 码
        public string OPUDIInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sFlag = string.Empty;
            string sOKFlag = string.Empty;
            string sDI = string.Empty;
            string sDI1 = string.Empty;
            string sDI2 = string.Empty;
            string sDI3 = string.Empty;
            string sMNo = string.Empty;


            var AnonymousUser = new
            {
                MNo = String.Empty, Spec = String.Empty, DI = String.Empty, S1 = String.Empty, S2 = String.Empty, S3 = String.Empty, S4 = String.Empty, S5 = String.Empty, S6 = String.Empty,
                S7 = String.Empty, S8 = String.Empty,S9 = String.Empty, S10 = String.Empty, S11 = String.Empty, S12 = String.Empty, S13 = String.Empty,S14 = String.Empty,S15 = String.Empty,
                S16 = String.Empty, S17 = String.Empty,S18 = String.Empty, Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sMNo = Item.MNo;
            sDI = Item.DI;

            if (Item.Flag == "1")  // 发放DI码
            {
                sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.MNo, Item.Spec, "", sComp, "27-1");
                if (sOKFlag == "Y")  //  判断物料编码或型号是否已发放过DI码
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                //产生DI码
                sDI = CreateDICode(sComp, "0");

                if (Item.S1 == "是")
                {
                    sDI1 = CreateDICode(sComp, "1");
                }
                if (Item.S2 == "是")
                {
                    sDI2 = CreateDICode(sComp, "2");
                }
                if (Item.S3 == "是")
                {
                    sDI3 = CreateDICode(sComp, "3");
                }
            }
            else if (Item.Flag == "2")  // 修改DI信息，主要修改中，外，其他包装的发放DI码
            {
                if (Item.DI != Item.S7)  // 说明修改主DI码 Item.DI:新的DI码；Item.S7 ：老的DI码
                {
                    sOKFlag = BaseModuleBll.JudgeSysKindExist(Item.DI, "", "", sComp, "27-2");
                    if (sOKFlag == "Y")  //  判断物料编码或型号是否已发放过DI码
                    {
                        Message = "Y_EXISTeDI";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }

                if ((Item.S1 == "是") && (Item.S4 == ""))
                {  // 如果 勾选了需要发中包装，而 现在又还没有发DI码
                    sDI1 = CreateDICode(sComp, "1");
                }
                if ((Item.S1 == "") && (Item.S4 != ""))
                {
                    sDI1 = "删除";
                }

                if ((Item.S2 == "是") && (Item.S5 == ""))
                {  // 如果 勾选了需要发外包装，而 现在又还没有发DI码
                    sDI2 = CreateDICode(sComp, "2");
                }
                if ((Item.S2 == "") && (Item.S5 != ""))
                {
                    sDI2 = "删除";
                }

                if ((Item.S3 == "是") && (Item.S6 == ""))
                {  // 如果 勾选了需要发其他包装，而 现在又还没有发DI码
                    sDI3 = CreateDICode(sComp, "3");
                }
                if ((Item.S3 == "") && (Item.S6 != ""))
                {
                    sDI3 = "删除";
                }
            }

            if (sDI == "系统找不到厂商识别码")
            {
                Message = "Y_NOMCODE";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }

            // 已申报的，不能修改，不能删除




            sOKFlag = BaseModuleBll.OPUDIInfo(sDI, sMNo, Item.Spec, sDI1, sDI2, sDI3, Item.S4, Item.S5, Item.S6, Item.S7, Item.S8, Item.S9, Item.S10, Item.S11, Item.S12, Item.S13, Item.S14, Item.S15, Item.S16, Item.S17, Item.S18, sComp, sLogin, Item.Flag);
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }


            Result = JsonConvert.SerializeObject(new { Msg = Message, DI = sDI });

            return Result;
        }
        #endregion


        #region 获取DI码的其他信息-包装信息-储存信息-临床尺寸信息
        public string GetDIOtherInfo(string sDI, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sKFlag = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            DataTable dt = BaseModuleBll.GetSerielInfo("", "", sDI, "", "", "", 1000, 1, sMan, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion








        // 导出DI信息
        public string PrintDItoExcel(string Params, string DI, string Flag)
        {
            string Message = string.Empty;
            string Result = string.Empty;
            string sMan = string.Empty;
            string sComp = string.Empty;
            string sReturnFile = "\\ExcelFile\\" + DI + " " + DateTime.Now.ToString("yyyymmddss") + ".xls";
            string sFile = System.AppDomain.CurrentDomain.BaseDirectory + "\\Template\\DIInfo.xls";
            string sExcelFilePath = System.AppDomain.CurrentDomain.BaseDirectory + sReturnFile;
            System.IO.File.Copy(sFile, sExcelFilePath, true);
            Microsoft.Office.Interop.Excel.Application app = new Microsoft.Office.Interop.Excel.Application();
            object missing = Type.Missing;
            //Microsoft.Office.Interop.Excel.Workbook workbooks = null;
            Microsoft.Office.Interop.Excel.Workbook workbooks = app.Workbooks.Open(sExcelFilePath, missing, missing, missing, "", "", missing, missing, missing, missing, missing, missing, missing, missing, missing);

            Microsoft.Office.Interop.Excel.Worksheet WorkSheet1 = (Microsoft.Office.Interop.Excel.Worksheet)workbooks.Sheets[1];  // 获取第 1 个sheet
            Microsoft.Office.Interop.Excel.Worksheet WorkSheet2 = (Microsoft.Office.Interop.Excel.Worksheet)workbooks.Sheets[2];  // 获取第 2 个sheet
            Microsoft.Office.Interop.Excel.Worksheet WorkSheet3 = (Microsoft.Office.Interop.Excel.Worksheet)workbooks.Sheets[3];  // 获取第 3 个sheet
            Microsoft.Office.Interop.Excel.Worksheet WorkSheet4 = (Microsoft.Office.Interop.Excel.Worksheet)workbooks.Sheets[4];  // 获取第 4 个sheet
            WorkSheet2.Cells[2, 1] = "0WUDONG";  //最小销售单元产品标识(DI)

            // 取单元格的值：ordernum = ws.Cells[i, dic["订单号"]].Value2.ToString();//取单元格值

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { PO = String.Empty, POItem = String.Empty };
                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            }
            else
            {

            }

            if (string.IsNullOrEmpty(DI))
            {
                DI = "";
            }


            // 获取主DI信息
            DataTable dt = BaseModuleBll.GetSerielInfo("", "", DI, "", "2021-01-01", "2999-12-31", 50000, 1, sMan, sComp, Flag);
            DataTable dtPack = BaseModuleBll.GetSerielInfo("", DI, "", "", "2021-01-01", "2999-12-31", 50000, 1, sMan, sComp, "27-4"); // 获取包装信息
            DataTable dtCC = BaseModuleBll.GetSerielInfo("", "", DI, "", "2021-01-01", "2999-12-31", 50000, 1, sMan, sComp, "27-5");  // 获取储存信息
            DataTable dtLC = BaseModuleBll.GetSerielInfo("", "", DI, "", "2021-01-01", "2999-12-31", 50000, 1, sMan, sComp, "27-6");  // 获取临床尺寸信息

            try
            {   // app.Cells[5 + i, 1] = i + 1;//序号    5,1  :第5行，第 1 列      5,6  第 5 行， 第 6 列   说明：app.Cells[5 + i, 1]  -- 这种赋值，默认赋值第一个sheet

                if (dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        WorkSheet1.Cells[2 + i, 1] = "0" + dt.Rows[i]["ZXXSDYCPBS"].ToString();  //最小销售单元产品标识(DI)
                        WorkSheet1.Cells[2 + i, 2] = dt.Rows[i]["CPBSBMTXMC"].ToString();//医疗器械唯一标识编码体系名称
                        WorkSheet1.Cells[2 + i, 3] = dt.Rows[i]["ZXXSDYZSYDYDSL"].ToString();//最小销售单元中使用单元的数量
                        WorkSheet1.Cells[2 + i, 4] = dt.Rows[i]["SYDYCPBS"].ToString();//使用单元产品标识
                        WorkSheet1.Cells[2 + i, 5] = dt.Rows[i]["CPBSFBRQ"].ToString();//产品标识发布日期
                        WorkSheet1.Cells[2 + i, 6] = dt.Rows[i]["SFYBTZJBS"].ToString();   //是否有本体直接标识
                        WorkSheet1.Cells[2 + i, 7] = dt.Rows[i]["BTCPBSYZXXSDYCPBSSFYZ"].ToString();//本体产品标识与最小销售单元产品标识是否一致
                        WorkSheet1.Cells[2 + i, 8] = dt.Rows[i]["BTCPBS"].ToString();//本体产品标识
                        WorkSheet1.Cells[2 + i, 9] = dt.Rows[i]["CPMCTYMC"].ToString();//产品名称/通用名称
                        WorkSheet1.Cells[2 + i, 10] = dt.Rows[i]["SPMC"].ToString();//商品名称
                        WorkSheet1.Cells[2 + i, 11] = dt.Rows[i]["GGXH"].ToString(); //规格型号
                        WorkSheet1.Cells[2 + i, 12] = dt.Rows[i]["SFWBLZTLCP"].ToString();//是否为包类/组套类产品
                        WorkSheet1.Cells[2 + i, 13] = dt.Rows[i]["CPMS"].ToString();//产品描述
                        WorkSheet1.Cells[2 + i, 14] = dt.Rows[i]["CPHHHBH"].ToString();//产品货号或编号
                        WorkSheet1.Cells[2 + i, 15] = dt.Rows[i]["QXLB"].ToString();//器械类别
                        WorkSheet1.Cells[2 + i, 16] = dt.Rows[i]["YFLBM"].ToString();//原分类编码
                        WorkSheet1.Cells[2 + i, 17] = dt.Rows[i]["FLBM"].ToString();//分类编码
                        WorkSheet1.Cells[2 + i, 18] = dt.Rows[i]["YLQXZCRBARMC"].ToString();//医疗器械注册人/备案人名称
                        //WorkSheet1.Cells[2 + i, 19] = dt.Rows[i]["SPCDate"].ToString();//统一社会信用代码
                        WorkSheet1.Cells[2 + i, 20] = dt.Rows[i]["YLQXZCRBARYWMC"].ToString();//医疗器械注册人/备案人英文名称
                        WorkSheet1.Cells[2 + i, 21] = dt.Rows[i]["ZCZBHHZBAPZBH"].ToString();//注册证编号或者备案凭证编号
                        WorkSheet1.Cells[2 + i, 22] = dt.Rows[i]["CPLB"].ToString();//产品类别
                        WorkSheet1.Cells[2 + i, 23] = dt.Rows[i]["CGZMRAQXGXX"].ToString();//磁共振（MR）安全相关信息
                        WorkSheet1.Cells[2 + i, 24] = dt.Rows[i]["SFBJWYCXSY"].ToString();//是否标记为一次性使用
                        WorkSheet1.Cells[2 + i, 25] = dt.Rows[i]["ZDCFSYCS"].ToString();//最大重复使用次数
                        WorkSheet1.Cells[2 + i, 26] = dt.Rows[i]["SFWWJBZ"].ToString();//是否为无菌包装
                        WorkSheet1.Cells[2 + i, 27] = dt.Rows[i]["SYQSFXYJXMJ"].ToString();//使用前是否需要进行灭菌
                        WorkSheet1.Cells[2 + i, 28] = dt.Rows[i]["MJFS"].ToString();//灭菌方式
                        WorkSheet1.Cells[2 + i, 29] = dt.Rows[i]["QTXXDWZLJ"].ToString();//其他信息的网址链接
                        WorkSheet1.Cells[2 + i, 30] = dt.Rows[i]["YBBM"].ToString();//医保编码
                        WorkSheet1.Cells[2 + i, 31] = dt.Rows[i]["TSRQ"].ToString();//退市日期
                        WorkSheet1.Cells[2 + i, 32] = dt.Rows[i]["SCBSSFBHPH"].ToString();//生产标识是否包含批号
                        WorkSheet1.Cells[2 + i, 33] = dt.Rows[i]["SCBSSFBHXLH"].ToString();//生产标识是否包含序列号
                        WorkSheet1.Cells[2 + i, 34] = dt.Rows[i]["SCBSSFBHSCRQ"].ToString();//生产标识是否包含生产日期
                        WorkSheet1.Cells[2 + i, 35] = dt.Rows[i]["SCBSSFBHSXRQ"].ToString();//生产标识是否包含失效日期
                        //WorkSheet1.Cells[2 + i, 36] = dt.Rows[i]["CustPN"].ToString();//特殊储存或操作条件
                        //WorkSheet1.Cells[2 + i, 37] = dt.Rows[i]["IDesc"].ToString();//特殊尺寸说明
                        WorkSheet1.Cells[2 + i, 38] = "ESUDFP89-90-SLKDSFLJESDF-SDFLDJFKWELKDSF";//主键编号
                        WorkSheet1.Cells[2 + i, 39] = "1";//公开的版本号
                        WorkSheet1.Cells[2 + i, 40] = DateTime.Now.ToString("yyyy-MM-dd");//版本的发布时间
                        //app.Cells[2 + i, 41] = dt.Rows[i]["CustPN"].ToString();//
                    }
                }

                // 循环包装信息
                if (dtPack.Rows.Count > 0)
                {
                    for (int i = 0; i < dtPack.Rows.Count; i++)
                    {
                        WorkSheet2.Cells[2 + i, 1] = "723BEA72581462832" + DateTime.Now.ToString("yyyyMMddss");  //主键编号
                        WorkSheet2.Cells[2 + i, 2] = dtPack.Rows[i]["BZCPBS"].ToString();//包装产品标识
                        WorkSheet2.Cells[2 + i, 3] = dtPack.Rows[i]["CPBZJB"].ToString();//产品包装级别
                        WorkSheet2.Cells[2 + i, 4] = dtPack.Rows[i]["BZNHXYJCPBSSL"].ToString();//本级包装内包含小一级相同产品标识的包装数量
                        WorkSheet2.Cells[2 + i, 5] = dtPack.Rows[i]["BZNHXYJBZCPBS"].ToString();//包装内含小一级包装产品标识
                    }
                }

                // 循环储存信息
                if (dtCC.Rows.Count > 0)
                {
                    for (int i = 0; i < dtCC.Rows.Count; i++)
                    {
                        WorkSheet3.Cells[2 + i, 1] = "723BEA72581462832" + DateTime.Now.ToString("yyyyMMddss");  //主键编号
                        WorkSheet3.Cells[2 + i, 2] = dtCC.Rows[i]["CCHCZTJ"].ToString();//储存或操作条件
                        WorkSheet3.Cells[2 + i, 3] = dtCC.Rows[i]["ZDZ"].ToString();//最低值
                        WorkSheet3.Cells[2 + i, 4] = dtCC.Rows[i]["ZGZ"].ToString();//最高值
                        WorkSheet3.Cells[2 + i, 5] = dtCC.Rows[i]["JLDW"].ToString();//计量单位
                        WorkSheet3.Cells[2 + i, 6] = dtCC.Rows[i]["TSCCHCZTJ"].ToString();//特殊储存或操作条件
                    }
                }

                // 循环临床信息
                if (dtLC.Rows.Count > 0)
                {
                    for (int i = 0; i < dtLC.Rows.Count; i++)
                    {
                        WorkSheet4.Cells[2 + i, 1] = "723BEA72581462832" + DateTime.Now.ToString("yyyyMMddss");  //主键编号
                        WorkSheet4.Cells[2 + i, 2] = dtLC.Rows[i]["LCSYCCLX"].ToString();//临床使用尺寸类型
                        WorkSheet4.Cells[2 + i, 3] = dtLC.Rows[i]["CCZ"].ToString();//尺寸值
                        WorkSheet4.Cells[2 + i, 4] = dtLC.Rows[i]["CCDW"].ToString();//尺寸单位
                        WorkSheet4.Cells[2 + i, 5] = dtLC.Rows[i]["TSCCSM"].ToString();//特殊尺寸说明
                    }
                }





                app.Visible = true;
                app.ActiveWorkbook.Save();
            }
            catch (Exception ex)
            {
            }
            finally
            {
                workbooks.Close(false, missing, missing);
                workbooks = null;
                app.Quit();
                app = null;

                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }


            Result = JsonConvert.SerializeObject(new { sFilePath = ".." + sReturnFile });
            return Result;
        }























        #region  产生DI码
        public static string CreateDICode(string sComp, string sFlag)  // 0 初包装的DI，  1 中包装的， 2 外包装的， 3 其他的
        {

            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sNo = string.Empty;
            int i = 0;
            int iOne = 0;
            int iTwo = 0;
            int iSum = 0;
            int iQZ = 0;
            int iJY = 0;
            string sLS = string.Empty;
            string sTLS = string.Empty;
            int iLS = 0;
            int iLNum = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_MCode where CompanyNo='" + sComp + "' and IsUse='是' ", "MCode");
            if (sMaxNo == "")
            {
                sNo = "系统找不到厂商识别码";
            }
            else
            {    // 检验码计算方法：

                // 流水号
                sTLS = DBHelper.GetMaxNo("T_MCode where CompanyNo='" + sComp + "' and IsUse='是' ", "NowNum");
                iLS = int.Parse(sTLS) + 1;  // 如果在 + int.Parse(sFlag) 表示中包装DI，外包装DI的流水号都需要变化，即逐个 + 1，现在业务说中包装，外包装DI流水号不变，因此不需要加这个

                // 获取流水号
                iLNum = 12 - sMaxNo.Length;   // DI码共 14 位，如： 06971690004625，处理第一位是标识位，最后一位是校验码，其他的就是厂商识别码和流水号（项目号）


                int len = iLS.ToString().Length;
                i = iLNum - len;  // 如果厂商识别码是8 位，流水号是 4 位数，如果厂商识别码是 7 为，流水是 5 位  -- 现在是 7 位厂商识别码
                sLS = CreateAllNo.GetZoreNumString(i) + iLS.ToString();

                //  如厂商识别码 	0693554610035 5  
                //步骤一：自右自左，从代码位置序号2开始，所有的偶数位的数字代码求和：3＋0＋6＋5+3+6＝23
                //步骤二：自右自左，从代码位置序号1开始，所有的奇数位的数字代码求和：5＋0＋1+4+5+9＝24
                //步骤三：将步骤2的和乘以3：24×3＝72
                //步骤四：将步骤1和步骤3的结果相加：23＋72＝95
                //步骤五：用大于或等于步骤4所得结果且为10最小整数倍的数（往上按十取整）减去步骤4所得结果，其差即为检验码的值：100-95＝5


                sMaxNo = sFlag + sMaxNo + sLS;  // 1697169000069

                // 6 9 3 5 5 4 6 1 0 0 3 5   5 
                // 所有的偶数位的数字代码求和
                iOne = int.Parse(sMaxNo.Substring(11, 1)) + int.Parse(sMaxNo.Substring(9, 1)) + int.Parse(sMaxNo.Substring(7, 1)) + int.Parse(sMaxNo.Substring(5, 1)) + int.Parse(sMaxNo.Substring(3, 1)) + int.Parse(sMaxNo.Substring(1, 1));
                // 所有的奇数位的数字代码求和
                iTwo = int.Parse(sMaxNo.Substring(12, 1)) + int.Parse(sMaxNo.Substring(10, 1)) + int.Parse(sMaxNo.Substring(8, 1)) + int.Parse(sMaxNo.Substring(6, 1)) + int.Parse(sMaxNo.Substring(4, 1)) + int.Parse(sMaxNo.Substring(2, 1)) + int.Parse(sMaxNo.Substring(0, 1));
                iTwo = iTwo * 3;
                iSum = iOne + iTwo;
                iQZ = ((iSum + 9) / 10) * 10; //向上取整十
                iJY = iQZ - iSum;  // 这个是检验码


                sNo = sMaxNo + iJY.ToString();
            }

            return sNo;

        }
        #endregion



        #region  生成发料编号   F2104230001  CreateInStockNo
        public static string CreateFillNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_MaterFillInfo where convert(char(10),InDate,120)='" + sDate + "' ", "FillNo");//  F210423 0001
            if (sMaxNo == "")
            {
                sNo = "F" + CreateAllNo.CreateBillNo(2, 3) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(7, 4);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 4 - len;

                sNo = "F" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion






        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }






    public class ReturnObj
    {
        string expiresIn;

        public string ExpiresIn
        {
            get { return expiresIn; }
            set { expiresIn = value; }
        }
        string currentTime;
        public string CurrentTime
        {
            get { return currentTime; }
            set { currentTime = value; }
        }
        string returnCode;

        public string ReturnCode
        {
            get { return returnCode; }
            set { returnCode = value; }
        }
        string returnMsg;

        public string ReturnMsg
        {
            get { return returnMsg; }
            set { returnMsg = value; }
        }
        string todayRemainVisitCount;

        public string TodayRemainVisitCount
        {
            get { return todayRemainVisitCount; }
            set { todayRemainVisitCount = value; }
        }
        string accessToken;

        public string AccessToken
        {
            get { return accessToken; }
            set { accessToken = value; }
        }

    }

    public class deviceClinical
    {
        private string _LCSYCCLX;

        public string LCSYCCLX
        {
            get { return _LCSYCCLX; }
            set { _LCSYCCLX = value; }
        }
        private string _CCZ;

        public string CCZ
        {
            get { return _CCZ; }
            set { _CCZ = value; }
        }
        private string _CCDW;

        public string CCDW
        {
            get { return _CCDW; }
            set { _CCDW = value; }
        }

    }

    public class contactList
    {

        private string _QYLXRCZ;

        public string QYLXRCZ
        {
            get { return _QYLXRCZ; }
            set { _QYLXRCZ = value; }
        }
        private string _QYLXRYX;

        public string QYLXRYX
        {
            get { return _QYLXRYX; }
            set { _QYLXRYX = value; }
        }
        private string _QYLXRDH;

        public string QYLXRDH
        {
            get { return _QYLXRDH; }
            set { _QYLXRDH = value; }
        }
    }

    public class deviceStorage
    {
        private string _CCHCZTJ;

        public string CCHCZTJ
        {
            get { return _CCHCZTJ; }
            set { _CCHCZTJ = value; }
        }
        private string _ZDZ;

        public string ZDZ
        {
            get { return _ZDZ; }
            set { _ZDZ = value; }
        }
        private string _ZGZ;

        public string ZGZ
        {
            get { return _ZGZ; }
            set { _ZGZ = value; }
        }
        private string _JLDW;

        public string JLDW
        {
            get { return _JLDW; }
            set { _JLDW = value; }
        }

    }





    public class devicePackage
    {

        private string _BZCPBS;

        public string BZCPBS
        {
            get { return _BZCPBS; }
            set { _BZCPBS = value; }
        }
        private string _BZNHXYJBZCPBS;

        public string BZNHXYJBZCPBS
        {
            get { return _BZNHXYJBZCPBS; }
            set { _BZNHXYJBZCPBS = value; }
        }
        private string _BZNHXYJCPBSSL;

        public string BZNHXYJCPBSSL
        {
            get { return _BZNHXYJCPBSSL; }
            set { _BZNHXYJCPBSSL = value; }
        }
        private string _CPBZJB;

        public string CPBZJB
        {
            get { return _CPBZJB; }
            set { _CPBZJB = value; }
        }
    }

    public class dataSet
    {
        private string _uploadType;

        public string uploadType
        {
            get { return _uploadType; }
            set { _uploadType = value; }
        }
        private string _deviceRecordKey;

        public string deviceRecordKey
        {
            get { return _deviceRecordKey; }
            set { _deviceRecordKey = value; }
        }
        private string _ZXXSDYCPBS;

        public string ZXXSDYCPBS
        {
            get { return _ZXXSDYCPBS; }
            set { _ZXXSDYCPBS = value; }
        }
        private string _ZXXSDYZSYDYDSL;

        public string ZXXSDYZSYDYDSL
        {
            get { return _ZXXSDYZSYDYDSL; }
            set { _ZXXSDYZSYDYDSL = value; }
        }

        private string _QXLB;

        public string QXLB
        {
            get { return _QXLB; }
            set { _QXLB = value; }
        }

        private string _SYDYCPBS;

        public string SYDYCPBS
        {
            get { return _SYDYCPBS; }
            set { _SYDYCPBS = value; }
        }
        private string _CPBSBMTXMC;

        public string CPBSBMTXMC
        {
            get { return _CPBSBMTXMC; }
            set { _CPBSBMTXMC = value; }
        }
        private string _SFYBTZJBS;

        public string SFYBTZJBS
        {
            get { return _SFYBTZJBS; }
            set { _SFYBTZJBS = value; }
        }
        private string _BTCPBSYZXXSDYCPBSSFYZ;

        public string BTCPBSYZXXSDYCPBSSFYZ
        {
            get { return _BTCPBSYZXXSDYCPBSSFYZ; }
            set { _BTCPBSYZXXSDYCPBSSFYZ = value; }
        }
        private string _BTCPBS;

        public string BTCPBS
        {
            get { return _BTCPBS; }
            set { _BTCPBS = value; }
        }
        private string _BSZT;

        public string BSZT
        {
            get { return _BSZT; }
            set { _BSZT = value; }
        }
        private string _SFYZCBAYZ;

        public string SFYZCBAYZ
        {
            get { return _SFYZCBAYZ; }
            set { _SFYZCBAYZ = value; }
        }
        private string _ZCBACPBS;

        public string ZCBACPBS
        {
            get { return _ZCBACPBS; }
            set { _ZCBACPBS = value; }
        }
        private string _CPBSFBRQ;

        public string CPBSFBRQ
        {
            get { return _CPBSFBRQ; }
            set { _CPBSFBRQ = value; }
        }
        private string _CPMCTYMC;

        public string CPMCTYMC
        {
            get { return _CPMCTYMC; }
            set { _CPMCTYMC = value; }
        }
        private string _SPMC;

        public string SPMC
        {
            get { return _SPMC; }
            set { _SPMC = value; }
        }
        private string _GGXH;

        public string GGXH
        {
            get { return _GGXH; }
            set { _GGXH = value; }
        }
        private string _SFWBLZTLCP;

        public string SFWBLZTLCP
        {
            get { return _SFWBLZTLCP; }
            set { _SFWBLZTLCP = value; }
        }
        private string _CPMS;

        public string CPMS
        {
            get { return _CPMS; }
            set { _CPMS = value; }
        }
        private string _CPHHHBH;

        public string CPHHHBH
        {
            get { return _CPHHHBH; }
            set { _CPHHHBH = value; }
        }
        private string _CPLX;

        public string CPLX
        {
            get { return _CPLX; }
            set { _CPLX = value; }
        }
        private string _FLBM;

        public string FLBM
        {
            get { return _FLBM; }
            set { _FLBM = value; }
        }
        private string _YFLBM;

        public string YFLBM
        {
            get { return _YFLBM; }
            set { _YFLBM = value; }
        }
        private string _YLQXZCRBARMC;

        public string YLQXZCRBARMC
        {
            get { return _YLQXZCRBARMC; }
            set { _YLQXZCRBARMC = value; }
        }
        private string _YLQXZCRBARYWMC;

        public string YLQXZCRBARYWMC
        {
            get { return _YLQXZCRBARYWMC; }
            set { _YLQXZCRBARYWMC = value; }
        }

        private string _CPLB;

        public string CPLB
        {
            get { return _CPLB; }
            set { _CPLB = value; }
        }

        private string _TYSHXYDM;

        public string TYSHXYDM
        {
            get { return _TYSHXYDM; }
            set { _TYSHXYDM = value; }
        }
        private string _ZCZBHHZBAPZBH;

        public string ZCZBHHZBAPZBH
        {
            get { return _ZCZBHHZBAPZBH; }
            set { _ZCZBHHZBAPZBH = value; }
        }
        private string _HCHZSB;

        public string HCHZSB
        {
            get { return _HCHZSB; }
            set { _HCHZSB = value; }
        }
        private string _SFBJWYCXSY;

        public string SFBJWYCXSY
        {
            get { return _SFBJWYCXSY; }
            set { _SFBJWYCXSY = value; }
        }
        private string _ZDCFSYCS;

        public string ZDCFSYCS
        {
            get { return _ZDCFSYCS; }
            set { _ZDCFSYCS = value; }
        }
        private string _SFWWJBZ;

        public string SFWWJBZ
        {
            get { return _SFWWJBZ; }
            set { _SFWWJBZ = value; }
        }
        private string _SYQSFXYJXMJ;

        public string SYQSFXYJXMJ
        {
            get { return _SYQSFXYJXMJ; }
            set { _SYQSFXYJXMJ = value; }
        }
        private string _MJFS;

        public string MJFS
        {
            get { return _MJFS; }
            set { _MJFS = value; }
        }
        private string _YBBM;

        public string YBBM
        {
            get { return _YBBM; }
            set { _YBBM = value; }
        }
        private string _CGZMRAQXGXX;

        public string CGZMRAQXGXX
        {
            get { return _CGZMRAQXGXX; }
            set { _CGZMRAQXGXX = value; }
        }
        private List<devicePackage> _devicePackage;

        public List<devicePackage> devicePackage
        {
            get { return _devicePackage; }
            set { _devicePackage = value; }
        }
        private List<deviceStorage> _deviceStorage;

        public List<deviceStorage> deviceStorage
        {
            get { return _deviceStorage; }
            set { _deviceStorage = value; }
        }
        private string _TSCCHCZTJ;

        public string TSCCHCZTJ
        {
            get { return _TSCCHCZTJ; }
            set { _TSCCHCZTJ = value; }
        }
        private List<contactList> _contactList;

        public List<contactList> contactList
        {
            get { return _contactList; }
            set { _contactList = value; }
        }
        private List<deviceClinical> _deviceClinical;

        public List<deviceClinical> deviceClinical
        {
            get { return _deviceClinical; }
            set { _deviceClinical = value; }
        }
        private string _TSCCSM;

        public string TSCCSM
        {
            get { return _TSCCSM; }
            set { _TSCCSM = value; }
        }
        private string _SCBSSFBHPH;

        public string SCBSSFBHPH
        {
            get { return _SCBSSFBHPH; }
            set { _SCBSSFBHPH = value; }
        }
        private string _SCBSSFBHXLH;

        public string SCBSSFBHXLH
        {
            get { return _SCBSSFBHXLH; }
            set { _SCBSSFBHXLH = value; }
        }
        private string _SCBSSFBHSCRQ;


        public string SCBSSFBHSCRQ
        {
            get { return _SCBSSFBHSCRQ; }
            set { _SCBSSFBHSCRQ = value; }
        }
        private string _SCBSSFBHSXRQ;

        public string SCBSSFBHSXRQ
        {
            get { return _SCBSSFBHSXRQ; }
            set { _SCBSSFBHSXRQ = value; }
        }
        private string _QTXXDWZLJ;

        public string QTXXDWZLJ
        {
            get { return _QTXXDWZLJ; }
            set { _QTXXDWZLJ = value; }
        }
        private string _TSRQ;

        public string TSRQ
        {
            get { return _TSRQ; }
            set { _TSRQ = value; }
        }
        private string _BGSM;

        public string BGSM
        {
            get { return _BGSM; }
            set { _BGSM = value; }
        }

    }

    public class ReturnOb2
    {

        private string _accessToken;

        public string accessToken
        {
            get { return _accessToken; }
            set { _accessToken = value; }
        }
        private List<dataSet> _dataSet;

        public List<dataSet> dataSet
        {
            get { return _dataSet; }
            set { _dataSet = value; }
        }
    }

    public class DeclareResult
    {
        private string _returnCode;
        private string _returnMsg;
        private string _todayRemainVisitCount;
        private List<successList> _successList;

        public string returnCode
        {
            get { return _returnCode; }
            set { _returnCode = value; }
        }
        public string todayRemainVisitCount
        {
            get { return _todayRemainVisitCount; }
            set { _todayRemainVisitCount = value; }
        }
        public List<successList> successList
        {
            get { return _successList; }
            set { _successList = value; }
        }
        public string returnMsg
        {
            get { return _returnMsg; }
            set { _returnMsg = value; }
        }
    }

    public class successList
    {
        private string _deviceRecordKey;
        private string _TYSHXYDM;
        private string _ZXXSDYCPBS;

        public string deviceRecordKey
        {
            get { return _deviceRecordKey; }
            set { _deviceRecordKey = value; }
        }
        public string TYSHXYDM
        {
            get { return _TYSHXYDM; }
            set { _TYSHXYDM = value; }
        }
        public string ZXXSDYCPBS
        {
            get { return _ZXXSDYCPBS; }
            set { _ZXXSDYCPBS = value; }
        }
    }
}
