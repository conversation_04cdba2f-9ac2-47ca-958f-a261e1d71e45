﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;

namespace Common
{
    public class TreeNode
    {
        public string OrderNo { get; set; }
        public string SerialNo { get; set; }
        public string MaterBatchNo { get; set; }
        public string ProcedureNo { get; set; }
        public string ProcedureName { get; set; }
        public string ProcedureVer { get; set; }
        public string FlowOrder { get; set; }
        public string OPFlag { get; set; }


        public List<TreeNode> Children { get; set; }

        public TreeNode()
        {
            Children = new List<TreeNode>();
        }
    }
    public class DHRHelper
    {
        public static DataTable ConvertDataTableToTree(DataTable dataTable, string sn)
        {

            Dictionary<string, TreeNode> nodeMap = new Dictionary<string, TreeNode>();

            // 创建节点并将其放入字典中
            foreach (DataRow row in dataTable.Rows)
            {
                TreeNode node = new TreeNode
                {
                    SerialNo = row["SerialNo"].ToString(),
                    MaterBatchNo = row["MaterBatchNo"].ToString(),
                    // 相应地设置其他属性
                };

                // 将当前节点添加到节点映射中
                nodeMap[node.MaterBatchNo] = node;
            }

            // 连接节点
            foreach (var node in nodeMap.Values)
            {
                if (!string.IsNullOrEmpty(node.SerialNo) && nodeMap.ContainsKey(node.SerialNo))
                {
                    // 如果节点有父节点，则将其添加到父节点的子节点列表中
                    nodeMap[node.SerialNo].Children.Add(node);
                }
            }

            // 找到根节点并返回
            TreeNode root = null;
            foreach (var node in nodeMap.Values)
            {
                if (string.IsNullOrEmpty(node.SerialNo))
                {
                    root = node;
                    break;
                }
            }

            var assembly = ConvertToCollection(root, false);

            return ConvertResult(assembly, sn);
        }

        // 递归函数，将节点及其子节点转换为集合
        public static List<TreeNode> ConvertToCollection(TreeNode root, bool includeRoot)
        {
            List<TreeNode> nodeList = new List<TreeNode>();

            if (includeRoot)
            {
                nodeList.Add(root); // 如果包含root节点，则添加到集合中
            }

            foreach (var child in root.Children)
            {
                nodeList.AddRange(ConvertToCollection(child, true)); // 递归处理子节点，包含子节点的值
            }

            return nodeList;
        }


        public static DataTable ConvertResult(List<TreeNode> assembly, string sn)
        {

            DataTable dt = new DataTable();

            if (assembly.Count > 0)
            {
                string result = "(" + string.Join(",", assembly.Select(node => "'" + node.MaterBatchNo + "'").ToArray()) + ")";

                string sSQL = "select RPath+FileName FilePath from T_DHRFile " +
                    "where SerialNo in " + result + " order by OrderNo,SerialNo,CONVERT(int,FlowOrder)";

                dt = DBHelper.GetDataTable(sSQL);
            }

            return dt;
        }
    }

}
