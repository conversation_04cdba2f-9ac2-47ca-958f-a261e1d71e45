﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>客户套餐管理</title>
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <script src="../js/ajaxfileupload.js" type="text/javascript"></script>
    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all">
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/PrdAssist.js" type="text/javascript"></script>
    <script src="../js/layer/layer.js" type="text/javascript"></script>

    <link href="../css/XC.css" rel="stylesheet" />

    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        // 初始化客户下拉框
        function initCompanySelect() {
            $.ajax({
                type: "POST",
                url: "../Service/CompanyPricing.ashx?OP=GetCompanyList&limit=1000&page=1",
                data: { Data: JSON.stringify({}) },
                success: function (result) {
                    var parsedJson = jQuery.parseJSON(result);
                    if (parsedJson.code == 0 && parsedJson.data && parsedJson.data.length > 0) {
                        var companyList = parsedJson.data;
                        var html = '<option value="">请选择客户</option>';
                        for (var i = 0; i < companyList.length; i++) {
                            html += '<option value="' + companyList[i].CompID + '">' + companyList[i].CompName + '</option>';
                        }
                        $("#txtCustName").html(html);
                    } else {
                        layer.msg('获取客户列表失败：' + (parsedJson.msg || '未知错误'));
                    }
                },
                error: function () {
                    layer.msg('获取客户列表失败，请重试！');
                }
            });
        }

        // 初始化套餐下拉框
        function initMenuSelect() {
            $.ajax({
                type: "POST",
                url: "../Service/ProductPricing.ashx?OP=GetSetMenuInfo&limit=1000&page=1",
                data: { Data: JSON.stringify({}) },
                success: function (result) {
                    var parsedJson = jQuery.parseJSON(result);
                    if (parsedJson.code == 0 && parsedJson.data && parsedJson.data.length > 0) {
                        var menuList = parsedJson.data;
                        var html = '<option value="">请选择套餐</option>';
                        for (var i = 0; i < menuList.length; i++) {
                            // 只显示未禁用的套餐
                            if (menuList[i].Status !== "已禁用") {
                                html += '<option value="' + menuList[i].MenuNo + '">' + menuList[i].MenuName + '</option>';
                            }
                        }
                        $("#txtMenuNo").html(html);
                        
                        // 同时初始化查询条件中的套餐下拉框
                        var searchHtml = '<option value="">全部</option>';
                        for (var i = 0; i < menuList.length; i++) {
                            searchHtml += '<option value="' + menuList[i].MenuNo + '">' + menuList[i].MenuName + '</option>';
                        }
                        $("#txtSMenuNo").html(searchHtml);
                    } else {
                        layer.msg('获取套餐列表失败：' + (parsedJson.msg || '未知错误'));
                    }
                },
                error: function () {
                    layer.msg('获取套餐列表失败，请重试！');
                }
            });
        }

        // 加载套餐信息
        function loadMenuInfo() {
            var menuNo = $("#txtMenuNo").val();
            if (!menuNo) return;

            $.ajax({
                type: "POST",
                url: "../Service/ProductPricing.ashx?OP=GetSetMenuInfo",
                data: { Data: JSON.stringify({ MenuNo: menuNo }) },
                success: function (result) {
                    var parsedJson = jQuery.parseJSON(result);
                    if (parsedJson.code == 0 && parsedJson.data && parsedJson.data.length > 0) {
                        var menuInfo = parsedJson.data[0];
                        $("#txtBasePrice").val(menuInfo.BasePrice);
                        $("#txtBUP").val(menuInfo.BUP);
                        $("#txtTVA").val(menuInfo.TVA);
                        $("#txtAUserPrice").val(menuInfo.AUserPrice);
                        $("#txtDFunction").val(menuInfo.DFunction);
                        
                        // 设置价格梯度表格
                        setPriceData(menuInfo.AWOPrice);
                        
                        // 设置套餐选项的复选框
                        $("#txtSLA").prop("checked", menuInfo.SLA === true);
                        $("#txtDepthTrain").prop("checked", menuInfo.DepthTrain === true);
                        $("#txtIMServices").prop("checked", menuInfo.IMServices === true);
                        $("#txtCustomDev").prop("checked", menuInfo.CustomDev === true);
                        $("#txtInterfaceDev").prop("checked", menuInfo.InterfaceDev === true);
                        $("#txtOnsiteSV").prop("checked", menuInfo.OnsiteSV === true);
                    } else {
                        layer.msg('获取套餐信息失败：' + (parsedJson.msg || '未知错误'));
                    }
                },
                error: function () {
                    layer.msg('获取套餐信息失败，请重试！');
                }
            });
        }

        // 打开解密对话框
        function openDecryptDialog() {
            $("#txtDecryptCode").val("");
            $("#txtDecryptResult").val("");
            $("#ShowDecrypt").fadeIn(300);
            $("#ShowDecrypt-fade").fadeIn(300);
            $("#txtDecryptCode").focus();
        }

        // 关闭解密对话框
        function closeDecryptDialog() {
            $("#ShowDecrypt").fadeOut(300);
            $("#ShowDecrypt-fade").fadeOut(300);
        }

        $(function () {
        // 解密按钮点击事件
        $("#btnDecrypt").click(function() {
            var encryptedCode = $("#txtDecryptCode").val().trim();
            if (!encryptedCode) {
                layer.msg('请输入需要解密的注册码！');
                $("#txtDecryptCode").focus();
                return;
            }
            
            $.ajax({
                type: "POST",
                url: "../Service/CompanyCombo.ashx?OP=DecryptCode",
                data: { Data: JSON.stringify({ Code: encryptedCode }) },
                success: function(result) {
                    var parsedJson = jQuery.parseJSON(result);
                    if (parsedJson.Msg == 'Success') {
                        $("#txtDecryptResult").val(parsedJson.DecryptedData);
                        layer.msg('解密成功！');
                    } else {
                        $("#txtDecryptResult").val('');
                        layer.msg(parsedJson.Msg);
                    }
                },
                error: function() {
                    $("#txtDecryptResult").val('');
                    layer.msg('解密失败，请重试！');
                }
            });
        });
        


            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        //$('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=28-1' });
                        
                        // 初始化客户和套餐下拉框
                        initCompanySelect();
                        initMenuSelect();
                    }
                }
            })
        });



    </script>


    <script type="text/javascript">


        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#Defectslist',
                id: 'DefectslistID',
                url: '../Service/CompanyCombo.ashx?OP=GetCustMenuList',
                height: 'full-80',
                cellMinWidth: 80,
                cols: [[
                    { type: 'numbers' },
                    { field: 'ApplyNo', title: '申请编码', width: 120, sort: true },
                    { field: 'CustNo', title: '客户编码', width: 120 },
                    { field: 'CustName', title: '客户名称', width: 180 },
                    { field: 'AccountType', title: '结算方式', width: 100 },
                    { field: 'MenuName', title: '套餐名称', width: 120 },
                    { field: 'SettlementStartTime', title: '结算开始时间', width: 120 },
                    { field: 'SettlementEndTime', title: '结算结束时间', width: 120 },
                    { field: 'RegType', title: '注册类型', width: 100 },
                    { field: 'Code', title: '注册码', width: 120 },
                    { field: 'EffectiveDate', title: '生效日期', width: 120 },
                    { field: 'ExpiringDate', title: '失效日期', width: 120 },
                    { field: 'SettlementCycle', title: '结算周期(天)', width: 100 },
                    { field: 'BasePrice', title: '基础价格(月费)', width: 120 },
                    { field: 'BUP', title: '包含活跃用户数', width: 120 },
                    { field: 'TVA', title: '包含工单数', width: 120 },
                    { field: 'AUserPrice', title: '额外用户单价', width: 120 },
                    { field: 'AWOPrice', title: '工单超量价格', width: 200, templet: function(d) {
                        try {
                            var priceData = JSON.parse(d.AWOPrice);
                            var formattedPrice = priceData.map(function(item, index) {
                                if (index === priceData.length - 1) {
                                    return item.start + '以上:' + item.price;
                                } else {
                                    return item.start + '-' + item.end + ':' + item.price;
                                }
                            }).join('，');
                            return formattedPrice;
                        } catch (e) {
                            return d.AWOPrice || '';
                        }
                    }},
                    { field: 'DFunction', title: '核心功能差异', width: 200 },
                    { field: 'SLA', title: '标准在线支持', width: 120, templet: function(d) { return d.SLA ? '是' : '否'; } },
                    { field: 'DepthTrain', title: '深度培训', width: 120, templet: function(d) { return d.DepthTrain ? '是' : '否'; } },
                    { field: 'IMServices', title: '实施服务', width: 120, templet: function(d) { return d.IMServices ? '是' : '否'; } },
                    { field: 'CustomDev', title: '高级定制开发', width: 120, templet: function(d) { return d.CustomDev ? '是' : '否'; } },
                    { field: 'InterfaceDev', title: '特定接口开发', width: 120, templet: function(d) { return d.InterfaceDev ? '是' : '否'; } },
                    { field: 'OnsiteSV', title: '专人驻场服务', width: 120, templet: function(d) { return d.OnsiteSV ? '是' : '否'; } },
                    { field: 'NowVer', title: '在用版本', width: 80, templet: function(d) { return d.NowVer ? '是' : '否'; } },
                    { field: 'Status', title: '状态', width: 100 },
                    { field: 'CHMan', title: '变更人', width: 100 },
                    { field: 'CHDate', title: '变更时间', width: 150 },
                    { field: 'InMan', title: '登录账号', width: 100 },
                    { field: 'InDate', title: '创建时间', width: 150 },
                    { field: 'Remark', title: '备注', width: 200 },
                    { field: 'op', title: '操作', width: 280, toolbar: '#barDemo', fixed: 'right' }
                ]],
                page: true
            });




            //监听是否选中操作
            table.on('checkbox(Defectslist)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);
            });

            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(Defectslist)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');
            });

            //监听单元格编辑
            table.on('edit(Defectslist)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });

            //监听行工具事件
            table.on('tool(Defectslist)', function(obj) {
                var data = obj.data;
                var layEvent = obj.event;

                if (layEvent === 'detail') {  // 详情
                    $('#head-title1').html("套餐详情");
                    
                    // 显示注册码行
                    $("#trCode").show();
                    
                    // 设置申请编号
                    $("#txtApplyNo").val(data.ApplyNo);
                    
                    // 设置客户信息
                    $("#txtCustName").val(data.CustNo);
                    
                    // 设置表单值
                    $("#txtAccountType").val(data.AccountType);
                    $("#txtSettlementCycle").val(data.SettlementCycle);
                    $("#txtSettlementStartTime").val(data.SettlementStartTime ? data.SettlementStartTime.substring(0, 7) : '');
                    $("#txtSettlementEndTime").val(data.SettlementEndTime ? data.SettlementEndTime.substring(0, 7) : '');
                    $("#txtEffectiveDate").val(data.EffectiveDate ? data.EffectiveDate.substring(0, 10) : '');
                    $("#txtExpiringDate").val(data.ExpiringDate ? data.ExpiringDate.substring(0, 10) : '');
                    $("#txtMenuNo").val(data.MenuNo);
                    $("#txtBasePrice").val(data.BasePrice);
                    $("#txtBUP").val(data.BUP);
                    $("#txtTVA").val(data.TVA);
                    $("#txtAUserPrice").val(data.AUserPrice);
                    setPriceData(data.AWOPrice); // 使用函数设置价格梯度表格
                    $("#txtDFunction").val(data.DFunction);
                    $("#txtSLA").prop("checked", data.SLA === true);
                    $("#txtDepthTrain").prop("checked", data.DepthTrain === true);
                    $("#txtIMServices").prop("checked", data.IMServices === true);
                    $("#txtCustomDev").prop("checked", data.CustomDev === true);
                    $("#txtInterfaceDev").prop("checked", data.InterfaceDev === true);
                    $("#txtOnsiteSV").prop("checked", data.OnsiteSV === true);
                    $("#txtCode").val(data.Code);
                    $("#txtRemark").val(data.Remark);

                    // 禁用所有输入框
                    var inputElements = [
                        "#txtApplyNo",
                        "#txtCustName",
                        "#txtAccountType",
                        "#txtSettlementCycle",
                        "#txtSettlementStartTime",
                        "#txtSettlementEndTime",
                        "#txtEffectiveDate",
                        "#txtExpiringDate",
                        "#txtMenuNo",
                        "#txtBasePrice",
                        "#txtBUP",
                        "#txtTVA",
                        "#txtAUserPrice",
                        "#txtDFunction",
                        "#txtSLA",
                        "#txtDepthTrain",
                        "#txtIMServices",
                        "#txtCustomDev",
                        "#txtInterfaceDev",
                        "#txtOnsiteSV",
                        "#txtRemark"
                    ];
                    
                    // 禁用价格梯度表格
                    $("#priceTable input").attr("disabled", "disabled")
                        .addClass("disabled-input")
                        .css({
                            'background-color': '#f5f5f5',
                            'cursor': 'not-allowed',
                            'color': '#666'
                        });
                    $("#priceTable button").attr("disabled", "disabled")
                        .addClass("disabled-input")
                        .css({
                            'background-color': '#f5f5f5',
                            'cursor': 'not-allowed',
                            'color': '#666',
                            'opacity': '0.6'
                        });

                    inputElements.forEach(function(element) {
                        $(element).attr("disabled", "disabled")
                                .addClass("disabled-input")
                                .css({
                                    'background-color': '#f5f5f5',
                                    'cursor': 'not-allowed',
                                    'color': '#666'
                                });
                        
                        // 对于复选框特殊处理
                        if (element.includes("txt") && $(element).is(":checkbox")) {
                            $(element).next("label").css({
                                'color': '#666',
                                'cursor': 'not-allowed'
                            });
                        }
                    });

                    // 特殊处理注册码输入框 - 允许选择但不允许编辑
                    $("#txtCode").removeAttr("disabled")  // 移除禁用属性
                              .removeClass("disabled-input")  // 移除禁用样式
                              .css({
                                  'background-color': '#fff',
                                  'cursor': 'text',
                                  'color': '#000'
                              })
                              .attr("readonly", "readonly");  // 设置为只读

                    // 隐藏保存按钮，只显示关闭按钮
                    $("#MenuSaveBtn").hide();
                    $("#MenuSaveClose").text("关闭");

                    $("#ShowOne").css("display", "block");
                    $("#ShowOne-fade").css("display", "block");
                } else if (layEvent === 'jy') {  // 禁用
                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置禁用的标题
                    $("#hint-title").html("确定要禁用该套餐吗？套餐编号：")

                    //设置禁用的对象
                    $("#hint-value").html(data.MenuNo)

                    $("#txtMenuNo").val(data.MenuNo)
                    $("#txtOPFlag").val("jy")

                } else if (layEvent === 'qy') {  // 启用
                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Green")
                    $("#hint-value").addClass("XC-Font-Green")

                    //设置启用的标题
                    $("#hint-title").html("确定要启用该套餐吗？套餐编号：")

                    //设置启用的对象
                    $("#hint-value").html(data.MenuNo)

                    $("#txtMenuNo").val(data.MenuNo)
                    $("#txtOPFlag").val("qy")

                } else if (layEvent === 'edit') {
                    $('#head-title1').html("修改套餐信息");
                    $('#txtAEFlag').val("2");

                    // 隐藏注册码行
                    $("#trCode").hide();

                    // 设置申请编号
                    $("#txtApplyNo").val(data.ApplyNo);
                    $("#txtApplyNo").attr("disabled", "disabled");  // 申请编号不可修改
                    
                    // 设置客户信息 - 客户信息始终不可修改
                    $("#txtCustName").val(data.CustNo);
                    $("#txtCustName").attr("disabled", "disabled");  // 客户编码不可修改

                    // 设置按钮文本为"生成激活码"
                    $("#btnGenerateCode").text("生成激活码");

                    // 检查是否在生效日期范围内
                    var now = new Date();
                    now.setHours(0, 0, 0, 0); // 设置时间为当天的0点
                    
                    var effectiveDate = new Date(data.EffectiveDate.split(' ')[0]); // 只取日期部分
                    effectiveDate.setHours(0, 0, 0, 0);
                    
                    var expiringDate;
                    if (data.ExpiringDate) {
                        expiringDate = new Date(data.ExpiringDate.split(' ')[0]);
                        expiringDate.setHours(23, 59, 59, 999); // 设置为当天的最后一毫秒
                    } else {
                        expiringDate = new Date(9999, 11, 31, 23, 59, 59, 999);
                    }
                    
                    // 修改判断条件：如果当前日期等于失效日期，也允许修改
                    var isInEffectivePeriod = now > effectiveDate && now < expiringDate;
                    
                    // 定义需要处理的所有输入元素
                    var inputElements = [
                        "#txtAccountType",
                        "#txtSettlementCycle",
                        "#txtSettlementStartTime",
                        "#txtSettlementEndTime",
                        "#txtEffectiveDate",
                        "#txtExpiringDate",
                        "#txtMenuNo",
                        "#txtBasePrice",
                        "#txtBUP",
                        "#txtTVA",
                        "#txtAUserPrice",
                        "#txtDFunction",
                        "#txtSLA",
                        "#txtDepthTrain",
                        "#txtIMServices",
                        "#txtCustomDev",
                        "#txtInterfaceDev",
                        "#txtOnsiteSV",
                        "#txtCode",
                        "#txtRemark",
                        "#btnGenerateCode"
                    ];
                    
                    // 如果在生效期内，禁用所有字段
                    if (isInEffectivePeriod) {
                        // 禁用价格梯度表格
                        $("#priceTable input").attr("disabled", "disabled")
                            .addClass("disabled-input")
                            .css({
                                'background-color': '#f5f5f5',
                                'cursor': 'not-allowed',
                                'color': '#666'
                            });
                        $("#priceTable button").attr("disabled", "disabled")
                            .addClass("disabled-input")
                            .css({
                                'background-color': '#f5f5f5',
                                'cursor': 'not-allowed',
                                'color': '#666',
                                'opacity': '0.6'
                            });
                            
                        inputElements.forEach(function(element) {
                            $(element).attr("disabled", "disabled")
                                    .addClass("disabled-input")
                                    .css({
                                        'background-color': '#f5f5f5',
                                        'cursor': 'not-allowed',
                                        'color': '#666'
                                    });
                            
                            // 对于复选框特殊处理
                            if (element.includes("txt") && $(element).is(":checkbox")) {
                                $(element).next("label").css({
                                    'color': '#666',
                                    'cursor': 'not-allowed'
                                });
                            }
                        });

                        // 显示警告信息
                        $("#div_warning").html("该套餐在生效期内，所有信息不可修改")
                            .show()
                            .css({
                                'color': 'red',
                                'margin': '10px 0',
                                'padding': '10px',
                                'background-color': '#fff3f3',
                                'border': '1px solid #ffcaca',
                                'border-radius': '4px',
                                'text-align': 'center',
                                'font-weight': 'bold'
                            });
                    } else {
                        // 不在生效期内，启用除客户信息外的所有字段
                        // 启用价格梯度表格
                        $("#priceTable input").removeAttr("disabled")
                            .removeClass("disabled-input")
                            .css({
                                'background-color': '',
                                'cursor': '',
                                'color': ''
                            });
                        $("#priceTable button").removeAttr("disabled")
                            .removeClass("disabled-input")
                            .css({
                                'background-color': '',
                                'cursor': '',
                                'color': '',
                                'opacity': ''
                            });
                            
                        inputElements.forEach(function(element) {
                            $(element).removeAttr("disabled")
                                    .removeClass("disabled-input")
                                    .css({
                                        'background-color': '',
                                        'cursor': '',
                                        'color': ''
                                    });
                            
                            // 对于复选框特殊处理
                            if (element.includes("txt") && $(element).is(":checkbox")) {
                                $(element).next("label").css({
                                    'color': '',
                                    'cursor': ''
                                });
                            }
                        });

                        $("#div_warning").html("").hide();
                    }

                    // 设置表单值
                    $("#txtAccountType").val(data.AccountType);
                    $("#txtSettlementCycle").val(data.SettlementCycle);
                    $("#txtSettlementStartTime").val(data.SettlementStartTime ? data.SettlementStartTime.substring(0, 7) : '');
                    $("#txtSettlementEndTime").val(data.SettlementEndTime ? data.SettlementEndTime.substring(0, 7) : '');
                    $("#txtEffectiveDate").val(data.EffectiveDate ? data.EffectiveDate.substring(0, 10) : '');
                    $("#txtExpiringDate").val(data.ExpiringDate ? data.ExpiringDate.substring(0, 10) : '');
                    $("#txtMenuNo").val(data.MenuNo);
                    $("#txtBasePrice").val(data.BasePrice);
                    $("#txtBUP").val(data.BUP);
                    $("#txtTVA").val(data.TVA);
                    $("#txtAUserPrice").val(data.AUserPrice);
                    setPriceData(data.AWOPrice); // 使用函数设置价格梯度表格
                    $("#txtDFunction").val(data.DFunction);
                    $("#txtSLA").prop("checked", data.SLA === true);
                    $("#txtDepthTrain").prop("checked", data.DepthTrain === true);
                    $("#txtIMServices").prop("checked", data.IMServices === true);
                    $("#txtCustomDev").prop("checked", data.CustomDev === true);
                    $("#txtInterfaceDev").prop("checked", data.InterfaceDev === true);
                    $("#txtOnsiteSV").prop("checked", data.OnsiteSV === true);
                    $("#txtCode").val(data.Code);
                    $("#txtRemark").val(data.Remark);

                    // 保存原始数据用于后续比较
                    window.originalData = { ...data };

                    $("#ShowOne").css("display", "block")
                    $("#ShowOne-fade").css("display", "block")
                } else if (layEvent === 'del') {  // 删除
                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("确定要删除该客户套餐吗？<br/>客户名称：" + data.CustName + "<br/>套餐名称：")

                    //设置删除的对象
                    $("#hint-value").html(data.MenuName)

                    // 保存申请编号用于删除操作
                    window.deleteData = data;  // 保存整行数据供删除时使用

                    $("#txtOPFlag").val("del")
                } else if (layEvent === 'renewal') {  // 续费
                    $('#head-title1').html("套餐续费");
                    $('#txtAEFlag').val("3");  // 3表示续费操作

                    // 隐藏注册码行
                    $("#trCode").hide();

                    // 设置申请编号 - 新记录，自动生成
                    $("#txtApplyNo").val("");
                    $("#txtApplyNo").attr("disabled", "disabled");
                    
                    // 设置客户信息 - 客户信息不可修改
                    $("#txtCustName").val(data.CustNo);
                    $("#txtCustName").attr("disabled", "disabled");
                    
                    // 套餐信息不可修改
                    $("#txtMenuNo").val(data.MenuNo);
                    $("#txtMenuNo").attr("disabled", "disabled");
                    
                    // 其他信息可修改
                    var inputElements = [
                        "#txtAccountType",
                        "#txtSettlementCycle",
                        "#txtSettlementStartTime",
                        "#txtSettlementEndTime",
                        "#txtEffectiveDate",
                        "#txtExpiringDate",
                        "#txtBasePrice",
                        "#txtBUP",
                        "#txtTVA",
                        "#txtAUserPrice",
                        "#txtDFunction",
                        "#txtSLA",
                        "#txtDepthTrain",
                        "#txtIMServices",
                        "#txtCustomDev",
                        "#txtInterfaceDev",
                        "#txtOnsiteSV",
                        "#txtCode",
                        "#txtRemark",
                        "#btnGenerateCode"
                    ];
                    
                    // 启用价格梯度表格
                    $("#priceTable input").removeAttr("disabled")
                        .removeClass("disabled-input")
                        .css({
                            'background-color': '',
                            'cursor': '',
                            'color': ''
                        });
                    $("#priceTable button").removeAttr("disabled")
                        .removeClass("disabled-input")
                        .css({
                            'background-color': '',
                            'cursor': '',
                            'color': '',
                            'opacity': ''
                        });

                    // 启用除客户信息和套餐外的所有字段
                    inputElements.forEach(function(element) {
                        $(element).removeAttr("disabled")
                                .removeClass("disabled-input")
                                .css({
                                    'background-color': '',
                                    'cursor': '',
                                    'color': ''
                                });
                        
                        // 对于复选框特殊处理
                        if (element.includes("txt") && $(element).is(":checkbox")) {
                            $(element).next("label").css({
                                'color': '',
                                'cursor': ''
                            });
                        }
                    });

                    // 修改生成注册码按钮文本
                    $("#btnGenerateCode").text("生成续费码");

                    // 设置表单值
                    $("#txtAccountType").val(data.AccountType);
                    $("#txtSettlementCycle").val(data.SettlementCycle);
                    
                    // 保留原有的结算时间和生效日期，不进行计算
                    $("#txtSettlementStartTime").val(data.SettlementStartTime ? data.SettlementStartTime.substring(0, 7) : '');
                    $("#txtSettlementEndTime").val(data.SettlementEndTime ? data.SettlementEndTime.substring(0, 7) : '');
                    $("#txtEffectiveDate").val(data.EffectiveDate ? data.EffectiveDate.substring(0, 10) : '');
                    
                    // 其他字段复制原值
                    if (data.ExpiringDate) {
                        $("#txtExpiringDate").val(data.ExpiringDate.substring(0, 10));
                    } else {
                        $("#txtExpiringDate").val('');
                    }
                    $("#txtBasePrice").val(data.BasePrice);
                    $("#txtBUP").val(data.BUP);
                    $("#txtTVA").val(data.TVA);
                    $("#txtAUserPrice").val(data.AUserPrice);
                    setPriceData(data.AWOPrice); // 使用函数设置价格梯度表格
                    $("#txtDFunction").val(data.DFunction);
                    $("#txtSLA").prop("checked", data.SLA === true);
                    $("#txtDepthTrain").prop("checked", data.DepthTrain === true);
                    $("#txtIMServices").prop("checked", data.IMServices === true);
                    $("#txtCustomDev").prop("checked", data.CustomDev === true);
                    $("#txtInterfaceDev").prop("checked", data.InterfaceDev === true);
                    $("#txtOnsiteSV").prop("checked", data.OnsiteSV === true);
                    $("#txtCode").val("");  // 清空注册码
                    $("#txtRemark").val("");  // 不设置默认备注
                    
                    // 保存原始数据用于后续处理
                    window.originalData = { ...data };

                    $("#div_warning").html("");
                    $("#div_warning").hide();

                    $("#ShowOne").css("display", "block")
                    $("#ShowOne-fade").css("display", "block")
                }
            });

            //禁用\启用\删除
            $("#MenuOPBtn").click(function () {
                var Flag = $("#txtOPFlag").val()
                var sMenuNo = $("#txtMenuNo").val()

                var Data = '';
                var Params = {};
                var url = '';

                if (Flag === "del") {
                    Params = {
                        ApplyNo: window.deleteData.ApplyNo  // 修改为传递申请编号
                    };
                    url = "../Service/CompanyCombo.ashx?OP=DeleteCustMenuInfo";
                } else {
                    var sStatus = Flag == "jy" ? "已禁用" : "未禁用";
                    Params = {
                        MenuNo: sMenuNo,
                        Status: sStatus
                    };
                    url = "../Service/ProductPricing.ashx?OP=UpdateSetMenuStatus";
                }

                Data = JSON.stringify(Params);

                $.ajax({
                    type: "POST",
                    url: url,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson.Msg == 'Success') {
                            if (Flag === "del") {
                                layer.msg('删除成功！');
                            } else {
                                layer.msg(Flag == "jy" ? '禁用成功！' : '启用成功！');
                            }
                            $('#MenuBut_open').click();  // 重新查询
                            closeDialog();
                        }
                        else if (parsedJson.Msg == 'LoginError') {
                            layer.msg('登录已过期，请重新登录！');
                            setTimeout(function() {
                                window.location.href = "../Login.htm";
                            }, 1500);
                        }
                        else if (parsedJson.Msg == '该套餐在生效期内，不能删除') {
                            layer.msg('该套餐在生效期内，不能删除！');
                            closeDialog();
                        }
                        else {
                            var operation = Flag === "del" ? "删除" : (Flag == "jy" ? "禁用" : "启用");
                            layer.msg(operation + '失败：' + parsedJson.Msg);
                        }
                    },
                    error: function (data) {
                        var operation = Flag === "del" ? "删除" : (Flag == "jy" ? "禁用" : "启用");
                        layer.msg(operation + '失败，请重试！');
                    }
                });
            });

            // 搜索按钮事件
            $('#MenuBut_open').click(function () {
                var sCustNo = $("#txtSCustNo").val();
                var sCustName = $("#txtSCustName").val();
                var sMenuNo = $("#txtSMenuNo").val();

                var Data = '';
                var Params = { 
                    CustNo: sCustNo,
                    CustName: sCustName, 
                    MenuNo: sMenuNo
                };
                var Data = JSON.stringify(Params);

                table.reload('DefectslistID', {
                    method: 'post',
                    url: '../Service/CompanyCombo.ashx?OP=GetCustMenuList',
                    where: {
                        Data: Data
                    }, 
                    page: {
                        curr: 1
                    }
                });
            });

            // 保存按钮事件
            $("#MenuSaveBtn").click(function () {
                var flag = $("#txtAEFlag").val(); // 1=新增, 2=修改, 3=续费
                var isAdd = flag == "1";
                var isRenewal = flag == "3";

                if (!isAdd && !isRenewal) {
                    // 修改操作需要检查是否在生效期内
                    var now = new Date();
                    now.setHours(0, 0, 0, 0); // 设置时间为当天的0点
                    
                    var effectiveDate = new Date($("#txtEffectiveDate").val());
                    effectiveDate.setHours(0, 0, 0, 0);
                    
                    var expiringDate;
                    if ($("#txtExpiringDate").val()) {
                        expiringDate = new Date($("#txtExpiringDate").val());
                        expiringDate.setHours(23, 59, 59, 999); // 设置为当天的最后一毫秒
                    } else {
                        expiringDate = new Date(9999, 11, 31, 23, 59, 59, 999);
                    }
                    
                    // 修改判断条件：如果当前日期等于失效日期，也允许修改
                    if (now > effectiveDate && now < expiringDate) {
                        layer.msg('该套餐在生效期内，不能修改！');
                        return;
                    }
                }

                var custName = $("#txtCustName").val().trim();
                var accountType = $("#txtAccountType").val();
                var settlementCycle = $("#txtSettlementCycle").val();
                var settlementStartTime = $("#txtSettlementStartTime").val();
                var settlementEndTime = $("#txtSettlementEndTime").val();
                var effectiveDate = $("#txtEffectiveDate").val();
                var menuNo = $("#txtMenuNo").val();
                var expiringDate = $("#txtExpiringDate").val();

                if (custName == "") {
                    layer.msg('请选择客户！');
                    $("#txtCustName").focus();
                    return;
                }
                if (accountType == "") {
                    layer.msg('请选择结算方式！');
                    $("#txtAccountType").focus();
                    return;
                }
                if (settlementCycle == "") {
                    layer.msg('请输入结算周期！');
                    $("#txtSettlementCycle").focus();
                    return;
                }
                if (settlementStartTime == "") {
                    layer.msg('请选择结算开始时间！');
                    $("#txtSettlementStartTime").focus();
                    return;
                }
                if (settlementEndTime == "") {
                    layer.msg('请选择结算结束时间！');
                    $("#txtSettlementEndTime").focus();
                    return;
                }
                if (effectiveDate == "") {
                    layer.msg('请选择生效日期！');
                    $("#txtEffectiveDate").focus();
                    return;
                }
                if (menuNo == "") {
                    layer.msg('请选择套餐！');
                    $("#txtMenuNo").focus();
                    return;
                }

                // 检查试用版套餐的失效日期
                var selectedMenuText = $("#txtMenuNo option:selected").text();
                if (selectedMenuText.indexOf('试用版') > -1 && !expiringDate) {
                    layer.msg('试用版套餐必须填写失效日期！');
                    $("#txtExpiringDate").focus();
                    return;
                }
                
                // 获取并验证价格梯度数据
                var priceData = getPriceData();
                if (!priceData.length) {
                    layer.msg('请至少添加一个工单超量价格梯度！');
                    return;
                }

                // 验证梯度数据的合法性
                for (var i = 0; i < priceData.length; i++) {
                    if (!priceData[i].price || priceData[i].price <= 0) {
                        layer.msg('请输入有效的价格！');
                        return;
                    }
                    if (i < priceData.length - 1 && !priceData[i].end) {
                        layer.msg('除最后一个梯度外，其他梯度必须设置结束数量！');
                        return;
                    }
                    if (i > 0 && priceData[i].start <= priceData[i-1].start) {
                        layer.msg('每个梯度的起始数量必须大于前一个梯度！');
                        return;
                    }
                }

                // 获取并验证价格梯度数据
                var priceData = getPriceData();
                if (!priceData.length) {
                    layer.msg('请至少添加一个工单超量价格梯度！');
                    return;
                }

                // 验证梯度数据的合法性
                for (var i = 0; i < priceData.length; i++) {
                    if (!priceData[i].price || priceData[i].price <= 0) {
                        layer.msg('请输入有效的价格！');
                        return;
                    }
                    if (i < priceData.length - 1 && !priceData[i].end) {
                        layer.msg('除最后一个梯度外，其他梯度必须设置结束数量！');
                        return;
                    }
                    if (i > 0 && priceData[i].start <= priceData[i-1].start) {
                        layer.msg('每个梯度的起始数量必须大于前一个梯度！');
                        return;
                    }
                }

                var params = {
                    CustNo: custName,
                    CustName: $("#txtCustName option:selected").text(),
                    AccountType: accountType,
                    SettlementCycle: parseInt(settlementCycle),
                    SettlementStartTime: settlementStartTime,
                    SettlementEndTime: settlementEndTime,
                    EffectiveDate: effectiveDate,
                    ExpiringDate: expiringDate,
                    MenuNo: menuNo,
                    MenuName: $("#txtMenuNo option:selected").text(),
                    BasePrice: $("#txtBasePrice").val(),
                    BUP: $("#txtBUP").val(),
                    TVA: $("#txtTVA").val(),
                    AUserPrice: $("#txtAUserPrice").val(),
                    AWOPrice: JSON.stringify(priceData),  // 使用JSON字符串保存梯度价格数据
                    DFunction: $("#txtDFunction").val(),
                    SLA: $("#txtSLA").prop("checked"),
                    DepthTrain: $("#txtDepthTrain").prop("checked"),
                    IMServices: $("#txtIMServices").prop("checked"),
                    CustomDev: $("#txtCustomDev").prop("checked"),
                    InterfaceDev: $("#txtInterfaceDev").prop("checked"),
                    OnsiteSV: $("#txtOnsiteSV").prop("checked"),
                    Code: $("#txtCode").val(),
                    Remark: $("#txtRemark").val()
                };

                // 设置注册类型
                if (isRenewal) {
                    params.RegType = "续费";
                    params.OriginalApplyNo = window.originalData.ApplyNo;  // 记录原申请编号
                } else {
                    params.RegType = "激活";
                }

                // 修改时需要包含ApplyNo
                if (!isAdd && !isRenewal) {
                    params.ApplyNo = $("#txtApplyNo").val();
                }

                var data = JSON.stringify(params);
                var operation = isRenewal ? "RenewCustMenuInfo" : (isAdd ? "AddCustMenuInfo" : "UpdateCustMenuInfo");

                $.ajax({
                    type: "POST",
                    url: "../Service/CompanyCombo.ashx?OP=" + operation,
                    data: { Data: data },
                    success: function (result) {
                        var parsedJson = jQuery.parseJSON(result);
                        if (parsedJson.Msg == 'Success') {
                            if (isRenewal) {
                                layer.msg('续费成功！');
                            } else {
                            layer.msg(isAdd ? '新增成功！' : '修改成功！');
                            }
                            
                            $('#MenuBut_open').click(); // 重新查询
                            closeDialog();
                        } else {
                            layer.msg(parsedJson.Msg);
                        }
                    },
                    error: function () {
                        layer.msg('网络错误，请重试');
                    }
                });
            });




        });




        function openDialog(n) {  // 新增
            $('#head-title1').html("新增套餐信息");
            $('#txtAEFlag').val("1");

            $("#txtApplyNo").val(""); // 清空申请编号
            $("#txtCustName").val("");
            $("#txtCustName")
                .removeAttr("disabled")  // 移除禁用属性
                .removeClass("disabled-input")  // 移除禁用样式类
                .css({  // 重置CSS样式
                    'background-color': '',
                    'cursor': '',
                    'color': ''
                });
            $("#txtAccountType").val("月度");
            $("#txtSettlementCycle").val("");
            $("#txtSettlementStartTime").val("");
            $("#txtSettlementEndTime").val("");
            $("#txtEffectiveDate").val("");
            $("#txtExpiringDate").val("");
            $("#txtMenuNo").val("");
            $("#txtBasePrice").val("");
            $("#txtBUP").val("");
            $("#txtTVA").val("");
            $("#txtAUserPrice").val("");
            $("#txtDFunction").val("");
            setPriceData(""); // 清空价格梯度表格
            $("#txtSLA").prop("checked", false);
            $("#txtDepthTrain").prop("checked", false);
            $("#txtIMServices").prop("checked", false);
            $("#txtCustomDev").prop("checked", false);
            $("#txtInterfaceDev").prop("checked", false);
            $("#txtOnsiteSV").prop("checked", false);
            $("#txtCode").val("");
            $("#txtRemark").val("");

            // 隐藏注册码行
            $("#trCode").hide();

            // 新增时启用所有输入框
            var inputElements = [
                "#txtAccountType",
                "#txtSettlementCycle",
                "#txtSettlementStartTime",
                "#txtSettlementEndTime",
                "#txtEffectiveDate",
                "#txtExpiringDate",
                "#txtMenuNo",
                "#txtBasePrice",
                "#txtBUP",
                "#txtTVA",
                "#txtAUserPrice",
                "#txtAWOPrice",
                "#txtDFunction",
                "#txtSLA",
                "#txtDepthTrain",
                "#txtIMServices",
                "#txtCustomDev",
                "#txtInterfaceDev",
                "#txtOnsiteSV",
                "#txtRemark"
            ];

            inputElements.forEach(function(element) {
                $(element).removeAttr("disabled")
                        .removeClass("disabled-input")
                        .css({
                            'background-color': '',
                            'cursor': '',
                            'color': ''
                        });
                
                // 对于复选框特殊处理
                if (element.includes("txt") && $(element).is(":checkbox")) {
                    $(element).next("label").css({
                        'color': '',
                        'cursor': ''
                    });
                }
            });

            $("#txtMenuNo").removeAttr("disabled");

            $("#div_warning").html("");
            $("#div_warning").hide();

            // 显示保存按钮，文本改为"保存"
            $("#MenuSaveBtn").show();
            $("#MenuSaveClose").text("取消");

            $("#ShowOne").css("display", "block")
            $("#ShowOne-fade").css("display", "block")
        }

        function closeDialog() {
            $("#ShowOne").css("display", "none")
            $("#ShowOne-fade").css("display", "none")
            $("#ShowDel").css("display", "none")
            $("#ShowDel-fade").css("display", "none")
            
            // 重置保存按钮状态
            $("#MenuSaveBtn").show();
            $("#MenuSaveClose").text("取消");
        }





    </script>


    <script type="text/javascript">
        // 添加价格梯度行
        function addPriceRow(btn) {
            var tbody = document.querySelector('#priceTable tbody');
            var newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td><input type="number" class="layui-input price-input" name="start" min="0" /></td>
                <td><input type="number" class="layui-input price-input" name="end" min="0" /></td>
                <td><input type="number" class="layui-input price-input" name="price" min="0" step="0.01" /></td>
                <td>
                    <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="addPriceRow(this)">添加</button>
                    <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" onclick="deletePriceRow(this)">删除</button>
                </td>
            `;
            tbody.appendChild(newRow);
            updateStartValues();
        }

        // 删除价格梯度行
        function deletePriceRow(btn) {
            var tbody = document.querySelector('#priceTable tbody');
            if (tbody.children.length > 1) {
                var row = btn.closest('tr');
                row.remove();
                updateStartValues();
            } else {
                layer.msg('至少保留一行价格梯度');
            }
        }

        // 更新所有行的起始值
        function updateStartValues() {
            var rows = document.querySelectorAll('#priceTable tbody tr');
            rows.forEach(function(row, index) {
                var startInput = row.querySelector('[name="start"]');
                var endInput = row.querySelector('[name="end"]');
                
                if (index === 0) {
                    startInput.value = "0";
                } else {
                    var prevEndInput = rows[index - 1].querySelector('[name="end"]');
                    startInput.value = prevEndInput.value ? (parseInt(prevEndInput.value) + 1) : '';
                }

                // 添加结束数量变化事件
                if (!endInput.hasEventListener) {
                    endInput.addEventListener('change', function() {
                        updateStartValues();
                    });
                    endInput.hasEventListener = true;
                }
            });
        }

        // 获取价格梯度数据
        function getPriceData() {
            var rows = document.querySelectorAll('#priceTable tbody tr');
            var data = [];
            rows.forEach(function(row, index) {
                var startVal = row.querySelector('[name="start"]').value;
                var endVal = row.querySelector('[name="end"]').value;
                var priceVal = row.querySelector('[name="price"]').value;

                if (startVal !== '' && priceVal !== '') {
                    data.push({
                        start: parseInt(startVal),
                        end: endVal ? parseInt(endVal) : null,
                        price: parseFloat(priceVal)
                    });
                }
            });
            return data;
        }

        // 设置价格梯度数据
        function setPriceData(jsonStr) {
            if (!jsonStr) {
                // 如果没有数据，至少显示一行空行
                var tbody = document.querySelector('#priceTable tbody');
                tbody.innerHTML = `
                    <tr>
                        <td><input type="number" class="layui-input price-input" name="start" min="0" value="0" /></td>
                        <td><input type="number" class="layui-input price-input" name="end" min="0" /></td>
                        <td><input type="number" class="layui-input price-input" name="price" min="0" step="0.01" /></td>
                        <td>
                            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="addPriceRow(this)">添加</button>
                            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" onclick="deletePriceRow(this)">删除</button>
                        </td>
                    </tr>
                `;
                return;
            }
            
            try {
                var data = JSON.parse(jsonStr);
                var tbody = document.querySelector('#priceTable tbody');
                tbody.innerHTML = ''; // 清空现有行
                
                data.forEach(function(item, index) {
                    var row = document.createElement('tr');
                    row.innerHTML = `
                        <td><input type="number" class="layui-input price-input" name="start" min="0" value="${item.start}" /></td>
                        <td><input type="number" class="layui-input price-input" name="end" min="0" value="${item.end || ''}" /></td>
                        <td><input type="number" class="layui-input price-input" name="price" min="0" step="0.01" value="${item.price}" /></td>
                        <td>
                            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="addPriceRow(this)">添加</button>
                            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" onclick="deletePriceRow(this)">删除</button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
                updateStartValues();
            } catch (e) {
                console.error('Invalid price data:', e);
                setPriceData(); // 显示一行空行
            }
        }

        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

        })



    </script>

    <style type="text/css">

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        /* 禁用输入框样式 */
        input[disabled], select[disabled], .XC-Input-block[disabled] {
            background-color: #f5f5f5 !important;
            cursor: not-allowed !important;
            opacity: 1 !important;  /* 确保文本清晰可见 */
            color: #666 !important; /* 文本颜色设置为灰色 */
        }

        /* 弹窗中的禁用输入框样式 */
        .XC-modal .XC-Input-block[disabled] {
            background-color: #f5f5f5 !important;
            border-color: #e6e6e6 !important;
        }

        /* 删除提示框样式 */
        #hint-title {
            line-height: 1.8;
        }

        #hint-value {
            display: inline-block;
            margin-top: 5px;
        }

        .XC-modal-content {
            padding: 15px !important;
        }

        .wangid_conbox td, .wangid_conbox th, #ShowOne td {
            font-size: 12px
        }

        #ShowOne tr {
            height: 40px;
        }

        #ShowOne .XC-Input-block {
            width: 100%;
            margin-left: 5px
        }

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
            padding: 0 8px;
        }

        select.find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
            padding: 0 8px;
            background-color: white;
        }

        .LabelDelBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #da542e;
        }

        .LabelAddBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #579fd8;
        }
        /* 已有样式保持不变 */
        .price-table-container {
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
            border: 1px solid #e6e6e6;
            padding: 10px;
            background: #fff;
        }
        .price-input {
            width: 90% !important;
            display: inline-block !important;
            height: 30px !important;
        }
        #priceTable th {
            text-align: center;
            background-color: #f2f2f2;
        }
        #priceTable td {
            text-align: center;
            padding: 5px !important;
        }
        #priceTable .XC-Btn-md {
            margin: 0 3px;
        }
    </style>




</head>
<body>
    <div class="div_find">
        <p>
            <label class="find_labela">客户编码</label><input type="text" id="txtSCustNo" class="find_input" />
            <label class="find_labela">客户名称</label><input type="text" id="txtSCustName" class="find_input" />
            <label class="find_labela">套餐名称</label>
            <select id="txtSMenuNo" class="find_input">
                <option value="">全部</option>
            </select>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="MenuBut_open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="javascript:openDialog(1)">添加</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="javascript:openDecryptDialog()">解密</button>
        </p>
    </div>
                
    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="Defectslist" lay-filter="Defectslist"></table>

        <script type="text/html" id="barDemo">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detail">详情</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="edit">修改</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="renewal">续费</button>
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>
        </script>
    </div>

    <!--弹出层-->
    <div class="XC-modal XC-modal-xl" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title1" id="head-title1">套餐信息</span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <table cellspacing="20" cellpadding="20" border='0' style="width:99%; ">
                    <tr>
                        <td style="width:120px; text-align:right;">
                            申请编码<span class="XC-Font-Red">*</span>
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtApplyNo" name="txtApplyNo" readonly=readonly placeholder="系统自动产生" />
                        </td>
                        <td style="width:120px; text-align:right;">
                            客户名称<span class="XC-Font-Red">*</span>
                        </td>
                        <td>
                            <select class="XC-Input-block" id="txtCustName" name="txtCustName">
                                <option value="">请选择客户</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td style="width:120px; text-align:right;">
                            结算方式<span class="XC-Font-Red">*</span>
                        </td>
                        <td>
                            <select class="XC-Input-block" id="txtAccountType" name="txtAccountType">
                                <option value="月度">月度</option>
                                <option value="年度">年度</option>
                                <option value="一次性">一次性</option>
                            </select>
                        </td>
                        <td style="width:120px; text-align:right;">
                            结算周期(天)<span class="XC-Font-Red">*</span>
                        </td>
                        <td>
                            <input type="number" class="XC-Input-block" id="txtSettlementCycle" name="txtSettlementCycle" />
                        </td>
                    </tr>
                    <tr>
                        <td style="width:120px; text-align:right;">
                            结算开始时间<span class="XC-Font-Red">*</span>
                        </td>
                        <td>
                            <input type="month" class="XC-Input-block" id="txtSettlementStartTime" name="txtSettlementStartTime" />
                        </td>
                        <td style="width:120px; text-align:right;">
                            结算结束时间<span class="XC-Font-Red">*</span>
                        </td>
                        <td>
                            <input type="month" class="XC-Input-block" id="txtSettlementEndTime" name="txtSettlementEndTime" />
                        </td>
                    </tr>
                    <tr>
                        <td style="width:120px; text-align:right;">
                            生效日期<span class="XC-Font-Red">*</span>
                        </td>
                        <td>
                            <input type="date" class="XC-Input-block" id="txtEffectiveDate" name="txtEffectiveDate" />
                        </td>
                        <td style="width:120px; text-align:right;">
                            失效日期
                        </td>
                        <td>
                            <input type="date" class="XC-Input-block" id="txtExpiringDate" name="txtExpiringDate" />
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 4px;">
                                <h4 style="margin-top: 0; margin-bottom: 15px; padding-bottom: 8px; border-bottom: 1px dashed #eee;">套餐详情</h4>
                                <table cellspacing="20" cellpadding="20" border='0' style="width:100%;">
                                    <tr>
                                        <td style="width:120px; text-align:right;">
                                            套餐选择<span class="XC-Font-Red">*</span>
                                        </td>
                                        <td>
                                            <select class="XC-Input-block" id="txtMenuNo" name="txtMenuNo" onchange="loadMenuInfo()">
                                                <option value="">请选择套餐</option>
                                            </select>
                                        </td>
                                        <td style="width:120px; text-align:right;">
                                            基础价格(月费)
                                        </td>
                                        <td>
                                            <input type="number" step="0.01" min="0" class="XC-Input-block" id="txtBasePrice" name="txtBasePrice" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="width:120px; text-align:right;">
                                            包含活跃用户数
                                        </td>
                                        <td>
                                            <input type="number" min="0" class="XC-Input-block" id="txtBUP" name="txtBUP" />
                                        </td>
                                        <td style="width:120px; text-align:right;">
                                            包含工单数
                                        </td>
                                        <td>
                                            <input type="number" min="0" class="XC-Input-block" id="txtTVA" name="txtTVA" />
                                        </td>
                                    </tr>
                                                        <tr>
                        <td style="width:120px; text-align:right;">
                            额外用户单价
                        </td>
                        <td>
                            <input type="number" step="0.01" min="0" class="XC-Input-block" id="txtAUserPrice" name="txtAUserPrice" />
                        </td>
                        <td style="width:120px; text-align:right;">
                            核心功能差异
                        </td>
                        <td>
                            <textarea class="XC-Input-block" id="txtDFunction" name="txtDFunction" style="height:60px; padding: 8px;"></textarea>
                        </td>
                    </tr>
                    <tr>
                        <td style="width:120px; text-align:right; vertical-align:top;">
                            工单超量价格<span class="XC-Font-Red">*</span>
                        </td>
                        <td colspan="3">
                            <div class="price-table-container">
                                <table class="layui-table" id="priceTable">
                                    <thead>
                                        <tr>
                                            <th width="25%">起始数量</th>
                                            <th width="25%">结束数量</th>
                                            <th width="25%">单价(元/工单)</th>
                                            <th width="25%">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><input type="number" class="layui-input price-input" name="start" min="0" /></td>
                                            <td><input type="number" class="layui-input price-input" name="end" min="0" /></td>
                                            <td><input type="number" class="layui-input price-input" name="price" min="0" step="0.01" /></td>
                                            <td>
                                                <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="addPriceRow(this)">添加</button>
                                                <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" onclick="deletePriceRow(this)">删除</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <input type="hidden" id="txtAWOPrice" name="txtAWOPrice" />
                        </td>
                    </tr>
                                    
                                    <tr>
                                        <td style="width:120px; text-align:right;">
                                            服务选项
                                        </td>
                                        <td colspan="3" style="padding-left: 15px;">
                                            <div class="XC-Checkbox-group" style="padding-top:10px;">
                                                <input type="checkbox" id="txtSLA" name="txtSLA" /> <label for="txtSLA" style="margin-right:15px;">标准在线支持</label>
                                                <input type="checkbox" id="txtDepthTrain" name="txtDepthTrain" /> <label for="txtDepthTrain" style="margin-right:15px;">深度培训</label>
                                                <input type="checkbox" id="txtIMServices" name="txtIMServices" /> <label for="txtIMServices" style="margin-right:15px;">实施服务</label>
                                                <input type="checkbox" id="txtCustomDev" name="txtCustomDev" /> <label for="txtCustomDev" style="margin-right:15px;">高级定制开发</label>
                                                <input type="checkbox" id="txtInterfaceDev" name="txtInterfaceDev" /> <label for="txtInterfaceDev" style="margin-right:15px;">特定接口开发</label>
                                                <input type="checkbox" id="txtOnsiteSV" name="txtOnsiteSV" /> <label for="txtOnsiteSV" style="margin-right:15px;">专人驻场服务</label>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td style="width:120px; text-align:right;">
                            备注
                        </td>
                        <td colspan="3">
                            <textarea class="XC-Input-block" id="txtRemark" name="txtRemark" style="height:60px; padding: 8px;"></textarea>
                        </td>
                    </tr>
                    <tr id="trCode">
                        <td style="width:120px; text-align:right;">
                            注册码
                        </td>
                        <td colspan="3">
                            <textarea class="XC-Input-block" id="txtCode" name="txtCode" style="height:60px; padding: 8px; width:100%;" readonly></textarea>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
            <input type="text" class="form-control" id="txtCode" name="txtCode" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="MenuSaveBtn">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="MenuSaveClose" onclick="closeDialog()">取消</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>

    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
                </div>
                
            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtMenuNo" name="txtMenuNo" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtOPFlag" name="txtOPFlag" />
            </div>
            
            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="MenuOPBtn">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>

    <!--消息提示-->
    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>

    <!--解密对话框-->
    <div class="XC-modal XC-modal-md" id="ShowDecrypt" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%, -50%); z-index:1001; background-color: #fff; border-radius: 4px; box-shadow: 0 2px 10px rgba(0,0,0,0.2); width:600px;">
        <div class="XC-modal-head">
            <span class="head-title">解密注册码</span>
            <span class="head-close" onclick="closeDecryptDialog()">X</span>
        </div>
        <div class="XC-modal-body" style="padding: 15px;">
            <table cellspacing="10" cellpadding="5" border='0' style="width:100%;">
                <tr>
                    <td style="width:100px; text-align:right;">
                        注册码<span class="XC-Font-Red">*</span>
                    </td>
                    <td>
                        <textarea class="XC-Input-block" id="txtDecryptCode" name="txtDecryptCode" style="height:80px; padding: 8px; width:100%; font-family:Consolas,monospace;" placeholder="请输入需要解密的注册码"></textarea>
                    </td>
                </tr>
                <tr>
                    <td style="width:100px; text-align:right; vertical-align:top; padding-top:15px;">
                        解密结果
                    </td>
                    <td>
                        <textarea class="XC-Input-block" id="txtDecryptResult" name="txtDecryptResult" style="height:250px; padding: 8px; width:100%; font-family:Consolas,monospace;" readonly></textarea>
                    </td>
                </tr>
            </table>
        </div>
        <div class="XC-modal-footer" style="margin-top:0; padding:10px;">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="btnDecrypt">解密</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDecryptDialog()">取消</button>
            </div>
        </div>
    </div>
    <div id="ShowDecrypt-fade" class="black_overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background-color:rgba(0,0,0,0.5); z-index:1000;"></div>
</body>
</html>