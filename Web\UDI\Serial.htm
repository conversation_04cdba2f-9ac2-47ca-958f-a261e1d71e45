﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html" ;=; charset="utf-8" />
    <title>序列号发放</title>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/BaseModule.js" type="text/javascript"></script>
    <script src="../js/UDI.js" type="text/javascript"></script>


    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=26&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=26&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        //$('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=25-1' });
                    }
                }
            })
        });



    </script>


    <script type="text/javascript">

        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#SerielInfolist',
                id: 'SerielInfolistID',
                url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=176',
                height: 'full-100',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'SerielNo', title: '序列号/批号', minWidth: 200, sort: true },
                    { field: 'BatchNum', title: '批数量', width: 60, sort: true },
                    { field: 'MaterNo', title: '物料编码', width: 120, sort: true },
                    { field: 'MaterName', title: '物料名称', width: 200 },
                    { field: 'Model', title: '型号', width: 120, sort: true },
                    { field: 'OrderNo', title: '工单', minWidth: 150 },
                    { field: 'InMan', title: '发号人', width: 80 },
                    { field: 'InDate2', title: '发号时间', width: 150 },
                    { field: 'SGetFlag', title: '提供烧录', minWidth: 70 },
                    { field: 'SGetDate', title: '提供烧录时间', minWidth: 130 },
                    { field: 'SPrintFlag', title: '烧录完成', minWidth: 70 },
                    { field: 'SPrintDate', title: '烧录完成时间', minWidth: 130 }
                    //{ field: 'op', title: '操作', width: 100, toolbar: '#barDemo', fixed: 'right' }
                ]],
                page: true,
                //  done: function(res, curr, count) {
                //     var that = this.elem.next();
                //     res.data.forEach(function(item, index) {
                //         if (item.Flag == "0") { // 说明评估日期已到期
                //             $(".layui-table-body tbody tr[data-index='" + index + "']").css({ 'backgroundColor': "Pink" });
                //             $(".layui-table-body tbody tr[data-index='" + index + "']").find('td[data-field="LastContactTime"]').css({ 'color': "red" });
                //
                //         }
                //     });
                // }


            });




            //监听是否选中操作
            table.on('checkbox(SerielInfolist)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);


            });


            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(SerielInfolist)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

            });

            //监听单元格编辑
            table.on('edit(SerielInfolist)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });


            //监听行工具事件
            table.on('tool(SerielInfolist)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值
                if (layEvent === 'del') {
                    layer.confirm('您确实要删除该序列号么？', function (index) {

                        //向服务端发送禁用指令
                        var sMNo = data.MaterNo;
                        var sFlag = "3";
                        var Data = '';
                        var Params = { MNo: sMNo, MName: "", S1: "", S2: "", S3: "", S4: "", S5: "", S6: "", S7: "", IValue: "", NValue: "", UseFlag: "", Remark: "", Flag: sFlag };
                        var Data = JSON.stringify(Params);
                        $.ajax({
                            type: "POST",
                            url: "../Service/BaseModuleAjax.ashx?OP=OPSerielMater&CFlag=3",
                            data: { Data: Data },
                            success: function (data) {
                                var parsedJson = jQuery.parseJSON(data);

                                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                    layer.msg('删除成功！');

                                    $('#SerielBut_open').click();  // 重新查询


                                } else {
                                    layer.msg('删除失败，请重试！')
                                }
                            },
                            error: function (data) {
                                layer.msg('删除失败2，请重试！')
                            }
                        });

                    }); // 删除

                } else if (layEvent === 'edit') {
                    $('#txtK').html("修改序列号信息");
                    $('#txtAEFlag').val("2");

                    $("#txtMaterNo").val(data.MaterNo);
                    $("#txtMaterName").val(data.MaterName);




                } else if (layEvent === 'qy') {  // 启用
                    layer.confirm('您确实要启用该序列号规则么？', function (index) {

                        //向服务端发送启用指令
                        var sMNo = data.MaterNo;
                        var sFlag = "5";
                        var Data = '';
                        var Params = { MNo: sMNo, MName: "", S1: "", S2: "", S3: "", S4: "", S5: "", S6: "", S7: "", IValue: "", NValue: "", UseFlag: "是", Remark: "", Flag: sFlag };
                        var Data = JSON.stringify(Params);
                        $.ajax({
                            type: "POST",
                            url: "../Service/BaseModuleAjax.ashx?OP=OPSerielMater&CFlag=5",
                            data: { Data: Data },
                            success: function (data) {
                                var parsedJson = jQuery.parseJSON(data);

                                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                    layer.msg('启用成功！');

                                    $('#SerielMaterBut_open').click();  // 重新查询


                                } else {
                                    layer.msg('启用失败，请重试！')
                                }
                            },
                            error: function (data) {
                                layer.msg('启用失败2，请重试！')
                            }
                        });

                    }); // 启用

                } else if (layEvent === 'jy') {  // 禁用
                    layer.confirm('您确实要禁用该序列号规则么？', function (index) {

                        //向服务端发送启用指令
                        var sMNo = data.MaterNo;
                        var sFlag = "4";
                        var Data = '';
                        var Params = { MNo: sMNo, MName: "", S1: "", S2: "", S3: "", S4: "", S5: "", S6: "", S7: "", IValue: "", NValue: "", UseFlag: "否", Remark: "", Flag: sFlag };
                        var Data = JSON.stringify(Params);
                        $.ajax({
                            type: "POST",
                            url: "../Service/BaseModuleAjax.ashx?OP=OPSerielMater&CFlag=4",
                            data: { Data: Data },
                            success: function (data) {
                                var parsedJson = jQuery.parseJSON(data);

                                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                    layer.msg('禁用成功！');

                                    $('#SerielMaterBut_open').click();  // 重新查询


                                } else {
                                    layer.msg('禁用失败，请重试！')
                                }
                            },
                            error: function (data) {
                                layer.msg('禁用失败2，请重试！')
                            }
                        });

                    }); // 禁用

                }




            });


            //  查询 -- 发号信息
            $('#SerielBut_open').click(function () {

                var sMNo = $("#txtSMNo").val();  //编码
                var sModel = encodeURI($("#txtSModel").val());
                var sSerial = $("#txtSSerial").val();
                var sOrder = $("#txtSOrder").val();
                var sBDate = $("#txtSBDate").val();
                var sEDate = $("#txtSEDate").val();

                var Data = '';
                var Params = { No: sOrder, Item: sSerial, Name: "", MNo: sMNo, MName: "", Status: "", BDate: sBDate, EDate: sEDate, A: sModel, B: "", C: "", D: "" };
                var Data = JSON.stringify(Params);

                table.reload('SerielInfolistID', {
                    method: 'post',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=176&Data=' + Data,
                    where: {
                        'No': sMNo,
                        'name': sModel
                    }, page: {
                        curr: 1
                    }
                });
            });

            //  显示，隐藏发号输入框
            $('#ShowBtn').click(function () {


                if ($("#SendSDiv").is(":hidden")) {
                    $("#SendSDiv").show();
                }
                else {
                    $("#SendSDiv").hide();
                }

            });

            //  导出工单的发号记录
            $('#Serial_Info_btn').click(function () {

                var sMNo = $("#txtSMNo").val();  //编码
                var sSerial = $("#txtSSerial").val();
                var sOrder = $("#txtSOrder").val();

                if ((sMNo == "") && (sSerial == "") && (sOrder == "")) {
                    layer.msg('工单号，序列号，物料编码查询条件必须输入其中一个！');
                    return;
                }

                var Data = '';
                var Params = { No: sOrder, Item: sSerial, Name: "", MNo: sMNo, MName: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "" };
                var Data = JSON.stringify(Params);

                $("#Loading").show();
                $.ajax({
                    type: "POST",
                    url: '../Service/OrderAjax.ashx?OP=SerialToExcel&CFlag=174',
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);
                        window.open(parsedJson.sFilePath);
                        $("#Loading").hide();
                    }
                });


            });







        });




        function openDialog(n) {  // 提交发号

            if ($("#txtPMaterNo").val() == "") {
                $('#sFMsg').html("请输入发号物料编码！");
                return;
            }
            if ($("#txtFNum").val() == "") {
                $('#sFMsg').html("请输入发号数量！");
                return;
            }

            if (isNaN($("#txtFNum").val())) {
                $("#sFMsg").html("发号数量为数字！");
                return;
            }

            if ($("#txtMaterSX").val().trim() == "") {
                $('#sFMsg').html("请选择物料属性（自制/外购）！");
                return;
            }


            $('#txtK').html("请确认如下发号信息");
            $('#txtAEFlag').val("2");   // 1 是预留， 2 是发放序列号

            $("#txtOrderNoT").val($("#txtOrderNo").val());
            $("#txtMaterNoT").val($("#txtPMaterNo").val());
            $("#txtMaterNameT").val($("#txtPMaterName").val());
            $("#txtModelT").val($("#txtModel").val());
            $("#txtFNumT").val($("#txtFNum").val());
            $("#txtMaterSXT").val($("#txtMaterSX").val());
            $("#SerielSaveBtn").removeAttr("disabled");

            $("#div_warning").html("");
            $("#div_warning").hide();

            document.getElementById('light3').style.display = 'block';
            document.getElementById('fade').style.display = 'block';


        }



        function DeleteDialog(n) {  // 删除
            var sOr = $("#txtOrderNo").val();

            if (sOr == "") {
                $('#sFMsg').html("请输入要删除的工单号！");
                return;
            }

            $('#txtAEFlag').val("3");

            $("#txtDelNo").val(sOr);

            $("#div_warning2").html("");
            $("#div_warning2").hide();

            document.getElementById('Div_Del').style.display = 'block';
            document.getElementById('fade').style.display = 'block'
        }

        function closeDialog() {
            document.getElementById('light3').style.display = 'none';
            document.getElementById('fade').style.display = 'none';
            document.getElementById('Div_Del').style.display = 'none';
        }






    </script>


    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

        })



    </script>

    <style type="text/css">

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: white;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=60);
        }

        .LabelDelBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #da542e;
        }

        .LabelAddBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #579fd8;
        }
    </style>




</head>
<body>
    <div class="div_find">

        <p>
            <label class="find_labela">工单号：</label><input type="text" id="txtSOrder" class="find_input" />
            <label class="find_labela">物料编码：</label> <input type="text" id="txtSMNo" class="find_input" />
            <label class="find_labela">型号：</label><input type="text" id="txtSModel" class="find_input" />
            <label class="find_labela">序列号：</label><input type="text" id="txtSSerial" class="find_input" />
            <input type="button" value="搜索" class="find_but" id="SerielBut_open" />  &nbsp;&nbsp;&nbsp;&nbsp;<input type="button" value="发号" class="find_but" id="ShowBtn" />
            &nbsp;&nbsp;&nbsp;&nbsp;<input type="button" value="发号记录" class="find_but" id="Serial_Info_btn" />
            <span class="find_span"><i class="i_open01"></i>展开</span>
            <span class="find_span1"><i class="i_close01"></i>收起</span>
        </p>
        <p id="open" style="display:none;">
            <label class="find_labela" id="Label1">发号日期：</label><input type="date" id="txtSBDate" class="find_input" />- <input type="date" id="txtSEDate" class="find_input" />
        </p>
    </div>
    <div style="text-align:left; margin-top:5px; background-color:#90d7ec; height:50px; display:none;" id="SendSDiv">
        <div style="font-weight:bold; color:Blue;">->请输入发号信息：   <label id="sFMsg" style=" color:Red; font-size:10px;"></label> </div>
        <label class="find_labela">工单号:</label><input type="text" id="txtOrderNo" style=" width:90px;" />
        <label class="find_labela">物料编码:</label><input type="text" id="txtPMaterNo" style=" width:140px;" />
        <label class="find_labela">发号数:</label><input type="text" id="txtFNum" style=" width:50px;" />
        <label class="find_labela">属性:</label>
        <select style=" width:60px;height:22px;" id="txtMaterSX">
            <option value=""></option>
            <option value="自制">自制</option>
            <option value="外购">外购</option>
        </select>
        <label class="find_labela">物料描述:</label><input type="text" id="txtPMaterName" readonly=readonly style="background-color:#90d7ec; border:0.5px;" />
        <label class="find_labela">型号:</label><input type="text" id="txtModel" readonly=readonly style=" width:130px;background-color:#90d7ec; border:0.5px;" />
        <label class="find_labela">批数量:</label><input type="text" id="txtBNum" style=" width:40px;background-color:#90d7ec; border:0.5px;" readonly=readonly />

        <i class="add2_i"></i><a href="JavaScript:void(0)" onclick="openDialog(1)" style="color: Blue">发号</a>
        <i class="del_i"></i><a href="JavaScript:void(0)" onclick="DeleteDialog(1)" style="color: Red">删除</a>
        <!--   <i class="down_i" ></i><a href="JavaScript:void(0)" onclick="EXCELDialog(1)" class="add_a">导出</a> -->
    </div>

    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="SerielInfolist" lay-filter="SerielInfolist"></table>

        <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="jy">禁用</a>
            <a class="layui-btn layui-btn-xs" lay-event="qy">启用</a>
        </script>

    </div>
    <div id="light3" style="display: none;position: absolute;top: 5%;left: 10%;right: 10%;width: 80%;height:320px;border: 2px solid #21b6b4; background-color: white;z-index: 1002;overflow: auto;">
        <div style="background-color:#21b6b4; height:40px;">
            <label id="txtK" style=" padding:10px;font-size: 14px; color:White; "></label>
        </div>
        <div id="div_warning" role="alert" style="text-align: center; display:none;color: Red ">
            <strong id="divsuccess" style="color: Red"></strong>
        </div>
        <table cellspacing="0" cellpadding="0" border='0' style="width:98%; ">
            <tr style=" height:30px;">
                <td style=" width:100px; text-align:right;">
                    工单号:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtOrderNoT" name="txtOrderNoT" style="height:28px;" readonly=readonly />
                </td>
            </tr>
            <tr style=" height:30px;">
                <td style=" width:100px; text-align:right;">
                    物料编码:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtMaterNoT" name="txtMaterNoT" style="height:28px;" readonly=readonly />
                </td>
            </tr>
            <tr style=" height:30px;">
                <td style=" width:100px; text-align:right;">
                    物料名称:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtMaterNameT" name="txtMaterNameT" style="height:28px;" readonly=readonly />
                </td>
            </tr>
            <tr style=" height:30px;">
                <td style=" width:100px; text-align:right;">
                    型号:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtModelT" name="txtModelT" style="height:28px;" readonly=readonly />
                </td>
            </tr>
            <tr style=" height:30px;">
                <td style=" width:100px; text-align:right;">
                    数量:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtFNumT" name="txtFNumT" style="height:28px;" readonly=readonly />
                </td>
            </tr>
            <tr style=" height:30px;">
                <td style=" width:100px; text-align:right;">
                    物料属性:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtMaterSXT" name="txtMaterSXT" style="height:28px;" readonly=readonly />
                </td>
            </tr>
        </table>


        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <br />
        <div align="center">
            <input type='button' id="SerielSaveBtn" value='发号' style="width: 50px; height: 30px;" />
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <input type='button' id="SerielSaveClose" value='关闭' onclick='closeDialog()' style="width: 50px; height: 30px;" />
        </div>
    </div>

    <div id="Div_Del" style="display: none;position: absolute;top: 25%;left: 25%;right: 25%;width: 50%;height: 220px;border: 2px solid orange; background-color: white;z-index: 1002;overflow: auto;">
        <div style="background-color:Red; height:40px; text-align:center; ">
            <label style="font-size: 14px; color:White; padding:10px;">*&nbsp;您即将删除下面工单发号信息，请确认！</label>
        </div>

        <label id="Label_Type" style="font-size: 10px; color: Red;"> </label>
        <label id="LMID" style="font-size: 10px; color: Red;"> </label>
        <div class="input-group" style=" width:98%;">
            <span class="input-group-addon">发号信息：</span>
            <input type="text" class="form-control" id="txtDelNo" name="txtDelNo" readonly=readonly />
        </div>
        <div class="input-group" style=" width:98%;">
            <span class="input-group-addon">发号信息：</span>
            <input type="text" class="form-control" id="Text1" name="txtDelNo" readonly=readonly />
        </div>
        <div id="div_warning2" role="alert" style="text-align: center; display: none; color: Red">
            <strong id="divsuccess2" style="color: Red"></strong>
        </div>
        <br />
        <div align="center">
            <input type='button' id="Seriel_Del_Btn" value='删除' style="width: 50px; height: 30px;font-size: 16px; color: Red;" />
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <input type='button' id="Button2" value='取消' onclick='closeDialog()' style="width: 50px;height: 30px;" />
        </div>
    </div>

    <div id="fade" class="black_overlay">
    </div>

    <div id="Loading" style="display: none; z-index: 1000; width:100%; text-align:right;">
        导出中...  <img src="../fonts/loading.gif" width="60px" height="12px" />
    </div>


</body>
</html>