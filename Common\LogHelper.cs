﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;

namespace Common
{
    public class LogHelper
    {
        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="operationType">操作类型</param>
        /// <param name="message">日志消息</param>
        /// <param name="iPAddress">IP地址</param>
        /// <param name="companyNo">公司编号</param>
        /// <param name="inMan">操作人</param>
        /// <param name="newValue">新值</param>
        /// <param name="oldValue">旧值</param>
        /// <param name="remark">备注</param>
        public static void LogInfo(string operationType, string message, string iPAddress, string companyNo, string inMan, string moduleName, string newValue = "", string oldValue = "", string remark = "")
        {
            Log("Info", operationType, message, iPAddress, newValue, oldValue, "", moduleName, "成功", companyNo, inMan, remark);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="operationType">操作类型</param>
        /// <param name="message">日志消息</param>
        /// <param name="iPAddress">IP地址</param>
        /// <param name="companyNo">公司编号</param>
        /// <param name="inMan">操作人</param>
        /// <param name="sql">记录发生错误SQL</param>
        public static void LogError(string operationType, string message, string iPAddress, string companyNo, string inMan, string moduleName, string sql = "")
        {
            Log("Error", operationType, message, iPAddress, "", "", sql, moduleName, "失败", companyNo, inMan, "");
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="operationType">操作类型</param>
        /// <param name="message">日志消息</param>
        /// <param name="iPAddress">IP地址</param>
        /// <param name="companyNo">公司编号</param>
        /// <param name="inMan">操作人</param>
        /// <param name="sql">记录调试SQL</param>
        public static void LogDebug(string operationType, string message, string iPAddress, string companyNo, string inMan, string moduleName, string sql = "")
        {
            Log("Debug", operationType, message, iPAddress, "", "", sql, moduleName, "成功", companyNo, inMan, "");
        }

        /// <summary>
        /// 通过这个方法获取操作前、后的值
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="condition">条件</param>
        /// <param name="Column">列名</param>
        /// <returns></returns>
        public static string GetValue(string tableName, string condition, string Column = "*")
        {
            if (string.IsNullOrEmpty(tableName) || string.IsNullOrEmpty(condition))
            {
                return "";
            }

            try
            {
                string query = "SELECT TOP 1 " + Column + " FROM " + tableName + " WHERE " + condition;
                DataTable dt = DBHelper.GetDataTable(query);
                string[] str=new string[dt.Columns.Count];
                if (dt.Rows.Count == 0)
                {
                    return "";
                }

                for (int i = 0; i < dt.Columns.Count; i++)
                {
                    str[i] = dt.Columns[i].ColumnName + ":" + dt.Rows[0][i].ToString();
                }

                return JsonConvert.SerializeObject(string.Join(",", str));
            }
            catch (Exception ex)
            {
                string contentToWrite = DateTime.Now.ToString();
                contentToWrite = contentToWrite + tableName + "表，" + Column + "列, " + condition + "条件，未找到指定的数据，错误原因：" + ex.Message;
                WriteContent(contentToWrite);
                return "";
            }
        }

        /// <summary>
        /// 将日志保存到数据库空
        /// </summary>
        /// <param name="logType">日志类型</param>
        /// <param name="operationType">操作类型</param>
        /// <param name="message">日志信息</param>
        /// <param name="iPAddress">IP地址</param>
        /// <param name="newValue">新值</param>
        /// <param name="oldValue">旧值</param>
        /// <param name="status">状态</param>
        /// <param name="companyNo">公司编号</param>
        /// <param name="inMan">操作人</param>
        /// <param name="remark">备注</param>
        private static void Log(string logType, string operationType, string message, string iPAddress, string newValue, string oldValue, string sql, string moduleName, string status, string companyNo, string inMan, string remark)
        {
            string query = "INSERT INTO T_SystemLog (LogType, OperationType, Message, IPAddress, NewValue, OldValue, Sql, ModuleName, Status, CompanyNo, InMan, Remark) " +
                "VALUES (@LogType, @OperationType, @Message, @IPAddress, @NewValue, @OldValue , @Sql , @ModuleName, @Status, @CompanyNo, @InMan, @Remark)";
            SqlParameter[] parameters =
            {
                new SqlParameter("@LogType",logType),
                new SqlParameter("@OperationType",operationType),
                new SqlParameter("@Message",message),
                new SqlParameter("@IPAddress",iPAddress),
                new SqlParameter("@NewValue",newValue),
                new SqlParameter("@OldValue",oldValue),
                new SqlParameter("@Sql",sql),
                new SqlParameter("@ModuleName",moduleName),
                new SqlParameter("@Status",status),
                new SqlParameter("@CompanyNo",companyNo),
                new SqlParameter("@InMan",inMan),
                new SqlParameter("@Remark",remark),
            };

            try
            {
                //日志记录保存到数据库中
                DBHelper.ExecuteCommand(query, parameters);
            }
            catch (Exception ex)
            {
                string contentToWrite = DateTime.Now.ToString();

                //把所有参数和值循环拼接
                foreach (SqlParameter parameter in parameters)
                {
                    contentToWrite += parameter.ParameterName + ":" + parameter.Value + " ";
                }

                contentToWrite = contentToWrite + " 写入失败！失败原因：" + ex.ToString();

                WriteContent(contentToWrite);
            }
        }

        private static void WriteContent(string contentToWrite)
        {
            string xuPath = "\\RecordFile\\Log";
            string serverPath = System.AppDomain.CurrentDomain.BaseDirectory + xuPath;
            //判断路径是否存在如不存在则创建
            if (!Directory.Exists(serverPath))
            {
                Directory.CreateDirectory(serverPath);
            }

            //文件存放路径
            string path = Path.Combine(serverPath, "Log_" + DateTime.Now.ToString("yyyy-MM-dd") + ".txt");

            using (StreamWriter writer = File.AppendText(path))
            {
                //把内容写入文件
                writer.WriteLine(contentToWrite);
            }
        }
    }
}
