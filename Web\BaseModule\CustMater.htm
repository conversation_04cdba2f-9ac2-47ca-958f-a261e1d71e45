﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" >
<head>
<meta http-equiv="Content-Type" content="text/html"; charset="utf-8" />
<title>客供料录入</title>
    <link href="../css/all.css" rel="stylesheet" type="text/css"/>
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css"/>
    <script src="../js/jquery-1.9.1.min.js" type="text/javascript"></script>
    <script type="text/javascript" src="../js/jQuery-2.2.0.min.js"></script>
    <script type="text/javascript" src="../js/bstable/js/bootstrap.min.js"></script>
    <script  type="text/javascript" src="../js/bstable/js/bootstrap-table.js"></script>
    <script  type="text/javascript" src="../js/bstable/js/bootstrap-table-zh-CN.min.js"></script>
    <script  type="text/javascript" src="../js/date/js/laydate.js"></script>
    <script type="text/javascript"  src="../js/layer/layer.js"></script>
    <script src="../js/BaseModule.js" type="text/javascript"></script>
    
    
    <script type="text/javascript">


        $(function() {

            var sCorpID = "we";  // GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&sFlag=10&CorpID=" + sCorpID,
                success: function(data) {
                    var parsedJson = jQuery.parseJSON(data);
                    //$('#Usertable').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetUserInfo&sFlag=10' });
                }
            })
        });

   </script>
   
   <script type="text/javascript">
       !function() {
           laydate.skin('danlan'); //切换皮肤，请查看skins下面皮肤库
           laydate({ elem: '#demo' });
           laydate({ elem: '#demo1' }); //绑定元素
       } ();
  </script>

  <script type="text/javascript">
      $(function() {
          $('#CMatertable').bootstrapTable({
              method: "get",
              striped: true,
              singleSelect: false,
              url: "../Service/BaseModuleAjax.ashx?OP=GetCustMaterInfo&sFlag=54-2",
              dataType: "json",
              pagination: true, //分页
              pageSize: 10,
              pageNumber: 1,
              search: false, //显示搜索框
              height: window.screen.height - 310,
              contentType: "application/x-www-form-urlencoded",
              queryParams: null,
              columns: [
                {
                    checkbox: "true",
                    field: 'check',
                    align: 'center',
                    valign: 'middle'
                }
                ,
                {
                    title: "物料编码",
                    field: 'MaterNo',
                    width: 120,
                    align: 'center',
                    valign: 'middle'
                },
                {
                    title: '物料名称',
                    field: 'MaterName',
                    width: 250,
                    align: 'center',
                    valign: 'middle'
                },
                {
                    title: '规格型号',
                    field: 'MaterSpec',
                    align: 'center',
                    valign: 'middle'
                },
                {
                    title: '库存',
                    field: 'Stock',
                    align: 'center'
                },
                {
                    title: '备注',
                    width: 200,
                    field: 'Remark',
                    align: 'center'
                },
                {
                    title: '录入人',
                    field: 'InMan',
                    align: 'center'
                },
                {
                    title: '录入时间',
                    field: 'CInDate',
                    width: 150,
                    align: 'center'
                },
                {
                    title: '操作',
                    field: 'opear',
                    align: 'center',
                    formatter: function(value, row) {
                    var e = '<label class="find_labela" onclick="openDialog(\'' + row.MaterNo + '\')" style="color:White;font-size:10px;width: 35px; cursor:pointer;border-radius: 5px;padding: 3px 5px;background: #579fd8;">修改</label> ' +
                            '<label class="find_labela" onclick="DelCMater(\'' + row.MaterNo + '\')" style="color:White;font-size:10px;width: 35px; cursor:pointer;border-radius: 5px;padding: 3px 5px;background: #da542e;">删除</label>';

                        return e;
                    }
                }
            ]
          });
      })

  </script>


  <script type="text/javascript">
      function openlayer(id) {   // 这个方法是在本界面打开一个新的界面，放入一个层，不过有点慢
          layer.open({
              type: 2,
              title: '添加信息',
              shadeClose: true,
              shade: 0.5,
              skin: 'layui-layer-rim',
              //            maxmin: true,
              closeBtn: 1,
              area: ['98%', '92%'],
              shadeClose: true,
              closeBtn: 1,
              content: 'AddCMater.htm'
              //iframe的url
          });
      }
  </script>
  

  <script type="text/javascript">
      function handleEvent(id) {

          alert(id);
      }
  </script>

  <script type="text/javascript">
      $(function() {
          $(".find_span").click(function() {
              $("#open").show();
              $(this).hide();
              $(".find_span1").show();
              $("#but_close02").hide();
              $("#but_open").hide();

          })
          $(".find_span1").click(function() {
              $("#open").hide();
              $(this).hide();
              $(".find_span").show();
              $("#but_open").show();
              $("#but_close02").show();

          })

      })
  </script>
  

  <script type="text/javascript">

      function openDialog(n) {
          var sFlag = "";

          if (n == "1") {   // 点击新增用户
              sFlag = "25-1";
              $("#NewEdit").html("录入客供料")  // 这几个字不能随便修改，JS那边用到
              $("#CMaterSaveBtn").removeAttr("disabled");
              $("#txtMaterNo").removeAttr("disabled");
              $('#txtAEFlag').val("1");

              $('#txtMaterNo').val("");
              $('#txtMaterName').val("");
              $('#txtSpec').val("");
              $('#txtStock').val("");
              $('#txtRemark').val("");


              //document.getElementById('TRNO').style.display = 'none';  // 显示/隐藏 表格的内容 tr
              //document.getElementById('TRSEQ').style.display = 'block';
              //document.getElementById('TRURL').style.display = 'none';
              // document.getElementById('Label1').style.display = 'none';
          }
          else {  // 增加功能菜单
              sFlag = "25-2";
              $("#NewEdit").html("修改客供料")
              $("#CMaterSaveBtn").removeAttr("disabled");
              $("#txtMaterNo").attr({ "disabled": "disabled" });
              $('#txtAEFlag').val("2");

              //var dl = $('#txtAddKind').val();
              // if (typeof dl == "undefined" || dl == null || dl == "") {
              //     $("#warningCH").html("请选择模块！")
              //     $("#warningCH").show();
              //      return;
              //  }
              //  $("#txtDL").html(dl)

          }

          document.getElementById('light').style.display = 'block';
          document.getElementById('fade').style.display = 'block'
      }

      function closeDialog() {
          document.getElementById('light').style.display = 'none';
          document.getElementById('fade').style.display = 'none'

          $("#div_warning").hide();
          $("#divsuccess").hide();
          $("#CMaterSaveBtn").removeAttr("disabled");
      }


  </script>

    <style type="text/css">
        #HDiv
        {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }
        .table > tbody > tr > td
        {
            border: 0px;
        }
        .black_overlay
        {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: black;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=88);
        }
    </style>
    

    <script type="text/javascript">
        $(function() {
            var $result = $('#events-result');
            $('#CMatertable').bootstrapTable({
                search: true,
                pagination: false,
                pageSize: 5,
                pageList: [5, 10, 15, 20],
                showColumns: true,
                showRefresh: false,
                showToggle: true,
                locale: "zh-CN",
                striped: true

            }).on('all.bs.table', function(e, name, args) {
                console.log('Event:', name, ', data:', args);
            }).on('click-row.bs.table', function(e, row, $element) {
                var sKind = encodeURI(row.MaterNo);

                $('#txtMaterNo').val(row.MaterNo);
                $('#txtMaterName').val(row.MaterName);
                $('#txtSpec').val(row.MaterSpec);
                $('#txtStock').val(row.Stock);
                $('#txtRemark').val(row.Remark);

                $('#txtDelNo').val(row.MaterNo);
                $('#txtDelName').val(row.MaterName);

                $result.text('Event: click-row.bs.table, data: ' + sKind);
            }).on('dbl-click-row.bs.table', function(e, row, $element) {
                $result.text('Event: dbl-click-row.bs.table, data: ' + JSON.stringify(row));
            }).on('search.bs.table', function(e, text) {
                $result.text('Event: search.bs.table, data: ' + text);
            });


        });

    </script>


    <script type="text/javascript">

        function DelCMater(n) {
            document.getElementById('Div_Del').style.display = 'block';
            document.getElementById('fade').style.display = 'block'
        }

        function closeDialog() {
            document.getElementById('light').style.display = 'none';
            document.getElementById('fade').style.display = 'none'
            document.getElementById('Div_Del').style.display = 'none';
        }

    </script>
	


</head>
<body>

<div class="div_find">

    <p style=" height:18px;">
      <label class="find_labela">物料编码：</label> <input type="text" id="txtSNo" class="find_input"/>
      <label class="find_labela">物料名称：</label><input type="text" id="txtSName" class="find_input"/>
      <label class="find_labela">规格型号：</label><input type="text" id="txtSSpec" class="find_input"/>
      <label class="find_labela">库存：</label>
      <select class="find_input" id="txtSStock">
              <option>全部</option>
              <option>大于0</option>
              <option>小于等于0</option>
      </select>
      <input type="button" value="搜索" class="find_but" id="CMaterBut_open"> 
      <span class="find_span" ><i class="i_open01"></i>展开</span>
      <span class="find_span1" ><i class="i_close01"></i>收起</span> 
    </p>
    <p id="open" style="display:none;height:18px;" >
        <label class="find_labela" id = "QDate">录入时间：</label><input  type="date" id="txtSBDate" class="find_input"/> --- <input  type="date" id="txtSEDate" class="find_input"/>
    </p>
</div>
    <p class="p_but" style="text-align:right">
        <i class="down_i" ></i><a href="#" class="add_a">导出</a>
        <i class="add2_i" ></i><a href="JavaScript:void(0)" onclick="openDialog(1)" style="color: Blue">添加</a>
   </p>
<p>
    <table data-url="json/data_alae_list.json" id="CMatertable" class="table_style" style="margin: 0 auto" >
    </table>
</p>

        <div  id="light" style="display: none;position: absolute;top: 5%;left: 20%;right: 20%;width: 60%;height: 400px;border: 2px solid #21b6b4; background-color: white;z-index: 1002;overflow: auto;">
          <div>
            <table cellspacing="0" cellpadding="0" border='0' style="width: 100%;" class="table">
              <tbody>
                <tr style="background-color:#21b6b4; height:40px;">
                    <td colspan="4"  style=" font-size:14px; font-weight:bold; color:White;" ><label id="NewEdit"></label></td>
                </tr>
                <tr>
                    <td align="right">物料编码<span style="color: Red">*</span></td>
                    <td><input type="text" id="txtMaterNo"  class="form-control"/></td>
                </tr>
                <tr>
                    <td align="right">物料名称<span style="color: Red">*</span></td>
                    <td><input type="text" id="txtMaterName"  class="form-control"/></td>
                </tr>
                <tr>
                    <td align="right">规格型号</td>
                    <td>
                       <input type="text" id="txtSpec" class="form-control"/>
                    </td>
                </tr>
                <tr>
                    <td align="right">库存</span></td>
                    <td>
                       <input type="text" id="txtStock" class="form-control"/>
                    </td>
                </tr>
                <tr>
                  <td style=" width:100px; text-align:right;">
                    备注: 
                  </td>
                  <td>
                    <textarea class="form-control" id="txtRemark" name="txtRemark"  style="height:60px;"> </textarea>
                  </td>
                </tr>
                
              </tbody>  
            </table>
            <div id="div_warning" role="alert" style="text-align: center; display: none; color: Red">
                <strong id="divsuccess" style="color: Red"></strong>
            </div>
               <div class="input-group" style="display:none; ">
                  <span class="input-group-addon">.</span>
                  <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
                  <span class="input-group-addon">.</span>
                  <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
                  <span class="input-group-addon">.</span>
                  <input type="text" class="form-control" id="txtInName" name="txtInName" />
               </div>
            <div align="center">
                <input type='button' id="CMaterSaveBtn" value='保存' style="width: 50px; height: 30px;" />
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <input type='button' id="CMaterSaveClose" value='关闭' onclick='closeDialog()' style="width: 50px;  height: 30px;" />
            </div>
          </div>  
        </div>
        <div id="fade" class="black_overlay">
        </div>

        <div id="Div_Del"  style="display: none;position: absolute;top: 25%;left: 25%;right: 25%;width: 50%;height: 200px;border: 2px solid orange; background-color: white;z-index: 1002;overflow: auto;">
           <div style="background-color:Red; height:40px; text-align:center; ">
              <label style="font-size: 14px; color:White; padding:10px;">*&nbsp;您即将删除下面这个客供料，请确认！</label>
           </div>
           
           <label id="Label_Type" style="font-size: 10px; color: Red;"> </label>
           <label id="LMID" style="font-size: 10px; color: Red;"> </label>
           <div class="input-group">
               <span class="input-group-addon">物料名称：</span>
                    <input type="text" class="form-control" id="txtDelName" name="txtDelName" readonly=readonly />
                    <input type="text" class="form-control" id="txtDelNo" name="txtDelNo" readonly=readonly style="width: 50px; display:none;" />
           </div>
           <div id="div_warning2" role="alert" style="text-align: center; display: none; color: Red">
                    <strong id="divsuccess2" style="color: Red"></strong>
           </div>
           <br />
           <div align="center">
                    <input type='button' id="CMater_Del_Btn" value='删除' style="width: 50px; height: 30px;font-size: 16px; color: Red;" />
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input type='button' id="Button2" value='取消' onclick='closeDialog()' style="width: 50px;height: 30px;" />
           </div>
        </div>

</body>
</html>