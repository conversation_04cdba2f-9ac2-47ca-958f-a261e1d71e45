﻿.XC-Form-inline-block {
    margin-top: 5px;
}

    .XC-Form-inline-block .XC-Form-Item {
        display: inline-block;
    }

.XC-Form-block .XC-Form-Item {
    display: block;
}

.XC-Form-Item {
    margin-bottom: 10px;
}

    .XC-Form-Item input {
        padding: 0px 10px;
    }

    .XC-Form-Item select {
        padding: 0px 10px;
    }




/*输入框*/
.XC-Input-xl {
    border: 1px solid #A6A6A6;
    width: 360px;
    height: 40px;
    border-radius: 5px;
}

    .XC-Input-xl:focus {
        outline: none;
        border: 1px solid #6a94ff;
    }

.XC-Span-xl {
    display: inline-block;
    font-size: 16px;
    color: #383838;
    margin: 0px 5px;
    width: 70px;
    text-align: right;
}

.XC-Cue-xl {
    font-size: 12px;
    color: #D43030;
    margin-top: 5px;
    margin-left: 87px;
}





.XC-Input-lg {
    font-size: 12px;
    color: #383838;
    width: 260px;
    height: 30px;
    border: 1px solid #E5E5E5;
    border-radius: 2px;
}

.XC-Span-lg {
    display: inline-block;
    width: 60px;
    font-size: 12px;
    color: #808080;
    margin: 0px 5px;
    text-align: right;
}




.XC-Input-md {
    font-size: 12px;
    color: #808080;
    width: 180px;
    height: 30px;
    border: 1px solid #E5E5E5;
    border-radius: 50px;
}





.XC-Input-sm {
    font-size: 12px;
    color: #808080;
    width: 250px;
    height: 35px;
    border: 1px solid #E5E5E5;
    border-radius: 2px;
}

.XC-Span-sm {
    display: inline-block;
    width: 60px;
    font-size: 12px;
    color: #808080;
    margin: 0px 5px;
    text-align: right;
}




.XC-Input-xs {
    font-size: 12px;
    color: #808080;
    width: 220px;
    height: 27px;
    border: 1px solid #E5E5E5;
    border-radius: 2px;
}

.XC-Span-xs {
    display: inline-block;
    width: 26px;
    font-size: 12px;
    color: #808080;
    margin: 0px 5px;
    text-align: right;
}

.XC-Cue-xs {
    color: #D43030;
    margin-top: 5px;
    margin-left: 85px;
}


.XC-Form-block-Item {
    display: flex;
    margin-bottom: 15px
}

.XC-Input-block {
    font-size: 12px;
    color: #383838;
    height: 30px;
    border: 1px solid #E5E5E5;
    border-radius: 2px;
    flex: 2;
    padding: 0px 10px;
}

.XC-Span-Input-block {
    width: 60px;
    font-size: 12px;
    color: #808080;
    margin: 0px 7px;
    text-align: right;
    height: 30px;
    line-height: 30px;
}

.XC-Textarea-block {
    font-size: 12px;
    color: #383838;
    max-height: 70px;
    border: 1px solid #E5E5E5;
    border-radius: 2px;
    flex: 2;
    padding: 5px 10px;
}

.XC-Span-Textarea-block {
    width: 60px;
    font-size: 12px;
    color: #808080;
    margin: 0px 7px;
    text-align: right;
    height: 70px;
    line-height: 20px;
}


.XC-Select-block {
    font-size: 12px;
    color: #383838;
    height: 30px;
    border: 1px solid #E5E5E5;
    border-radius: 2px;
    flex: 2;
    padding: 0px 10px;
}

.XC-Span-Select-block {
    width: 60px;
    font-size: 12px;
    color: #808080;
    margin: 0px 7px;
    text-align: right;
    height: 30px;
    line-height: 20px;
}





.XC-Select-xs {
    font-size: 12px;
    color: #808080;
    width: 240px;
    height: 27px;
    border: 1px solid #E5E5E5;
    border-radius: 2px;
}

.XC-Span-xs {
    display: inline-block;
    width: 60px;
    font-size: 12px;
    color: #808080;
    margin: 0px 5px;
    text-align: right;
}

.XC-Cue-xs {
    color: #D43030;
    margin-top: 5px;
    margin-left: 85px;
}





.XC-Select-lg {
    font-size: 12px;
    color: #383838;
    width: 280px;
    height: 30px;
    border: 1px solid #E5E5E5;
    border-radius: 2px;
}

.XC-Span-lg {
    display: inline-block;
    width: 60px;
    font-size: 12px;
    color: #808080;
    margin: 0px 5px;
    text-align: right;
}





/*按钮大小*/
.XC-Btn-xl {
    width: 145px;
    height: 46px;
    border-radius: 5px;
    cursor: pointer;
}

.XC-Btn-lg {
    width: 70px;
    height: 35px;
    border-radius: 2px;
    cursor: pointer;
}

.XC-Btn-md {
    width: 50px;
    height: 28px;
    border-radius: 2px;
    cursor: pointer;
}

.XC-Btn-sm {
    width: 70px;
    height: 20px;
    border-radius: 2px;
    cursor: pointer;
}

.XC-Btn-xs {
    width: 36px;
    height: 26px;
    border-radius: 2px;
    cursor: pointer;
}



/*字体大小*/
.XC-Size-xl {
    font-size: 20px;
}

.XC-Size-lg {
    font-size: 18px;
}

.XC-Size-md {
    font-size: 16px;
}

.XC-Size-sm {
    font-size: 14px;
}

.XC-Size-xs {
    font-size: 12px;
}




/*设置按钮颜色*/
.XC-Btn-Green {
    background-color: #0c873d;
    color: white;
    border: #06891b;
}

.XC-Btn-Red {
    background-color: #d42e2e;
    color: white;
    border: #d42e2e;
}

.XC-Btn-Orange {
    background-color: #ff8d1a;
    color: white;
    border: #ff8d1a;
}

.XC-Btn-Black {
    background-color: #383838;
    color: white;
    border: #383838;
}

.XC-Btn-Gray {
    background-color: #a6a6a6;
    color: white;
    border: #a6a6a6;
}

.XC-Btn-Cyan {
    background-color: #edf5f0;
    color: white;
    border: #edf5f0;
}

/*设置字体颜色*/
.XC-Font-Green {
    color: #06891b;
}

.XC-Font-Red {
    color: #d42e2e;
}

.XC-Font-Orange {
    color: #ff8d1a;
}

.XC-Font-Black {
    color: #383838;
}

.XC-Font-Gray {
    color: #a6a6a6;
}

.XC-Font-Cyan {
    color: #edf5f0;
}



/*图标*/
.XC-Icon {
    width: 20px;
    height: 20px;
    display: inline-block;
    border-radius: 20px;
    text-align: center;
}


.XC-modal-xl {
    display: none;
    position: absolute;
    top: 1%;
    left: 1%;
    right: 1%;
    width: 98%;
    height: 98%;
    background-color: white;
    z-index: 1002;
    border-radius: 2px;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.4)
}

.XC-modal-xl-center {
    overflow: auto;
    height: calc(100vh - 150px);
}
    /* 滚动条样式 */
    .XC-modal-xl-center::-webkit-scrollbar {
        width: 0px;
        height: 0px;
    }



.XC-modal-lg {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    min-height: 300px;
    max-height: 650px;
    background-color: white;
    z-index: 1002;
    overflow: auto;
    border-radius: 2px;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.4)
}

.XC-modal-lg-top {
    max-height: 56.33px;
}

.XC-modal-lg-center {
    overflow: auto;
    height: 440px;
}
    /* 滚动条样式 */
    .XC-modal-lg-center::-webkit-scrollbar {
        width: 0px;
        height: 0px;
    }


.XC-modal-md {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 65%;
    min-height: 300px;
    height: 450px;
    background-color: white;
    z-index: 1002;
    overflow: auto;
    border-radius: 2px;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.4)
}

.XC-modal-md-center {
    overflow: auto;
    height: calc(100vh - 525px);
}
    /* 滚动条样式 */
    .XC-modal-md-center::-webkit-scrollbar {
        width: 0px;
        height: 0px;
    }


.XC-modal-xs {
    display: none;
    position: absolute;
    top: 10%;
    width: 500px;
    min-height: 200px;
    background-color: white;
    z-index: 1002;
    overflow: auto;
    border-radius: 2px;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.4)
}

.XC-modal .XC-modal-head {
    height: 40px;
    background-color: #0c873d;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 15px;
    padding: 0px 15px;
    color: white;
}

.XC-modal .XC-modal-body {
    overflow: auto;
}

.XC-modal .XC-modal-head {
    cursor: pointer;
}

.XC-modal-xl .XC-modal-body {
    padding: 20px;
}

.XC-modal-lg .XC-modal-body {
    padding: 10px 10px 40px 10px;
}

.XC-modal-md .XC-modal-body {
    padding: 20px;
}

.XC-modal-xs .XC-modal-body {
    padding: 0px 20px;
}




.XC-modal .XC-modal-footer {
    padding-top: 20px;
}

.OPBtn {
    position: absolute;
    right: 20px;
    bottom: 20px;
}

.XC-modal-content {
    text-align: center;
    padding-top: 40px;
}

.black_overlay {
    display: none;
    position: absolute;
    top: 0%;
    left: 0%;
    width: 100%;
    height: 100%;
    background-color: white;
    z-index: 1001;
    opacity: 0.5;
}









.XC-Tab1, .XC-Tab2, .XC-Tab3 {
    border: 1px solid #dee1e6;
    background-color: #f8f9f9;
    border-radius: 1px;
    cursor: pointer;
}

.XC-tabs1, .XC-tabs2, .XC-tabs3 {
    height: 30px;
    line-height: 30px;
    list-style: none;
    font-size: 12px;
    padding: 0px;
    margin: 0px;
}

    .XC-tabs1 li, .XC-tabs2 li, .XC-tabs li {
        display: inline-block;
        padding: 0px 10px;
        text-align: center;
        width: 70px;
    }

.XC-Active {
    background-color: #0c873d;
    color: white;
    border-radius: 2px;
    font-size: 15px;
}


.XC-success {
    background-color: #06891b;
    color: white;
    text-align: left;
    padding: 0px 10px;
    position: fixed;
    top: 2%;
    min-width: 300px;
    height: 50px;
    line-height: 50px;
    border-radius: 3px;
    z-index: 9999;
    display: none;
    font-size: 15px;
    /*animation: fadeInOut 2.5s ease-in-out forwards;*/
}

.XC-error {
    background-color: #d42e2e;
    color: white;
    text-align: left;
    padding: 0px 10px;
    position: fixed;
    top: 2%;
    min-width: 300px;
    height: 50px;
    line-height: 50px;
    border-radius: 3px;
    z-index: 9999;
    display: none;
    font-size: 15px;
    /*animation: fadeInOut 2.5s ease-in-out forwards;*/
}

.XC-warning {
    background-color: #F6986D;
    padding: 0px 10px;
    color: white;
    text-align: left;
    padding: 10px;
    position: fixed;
    top: 2%;
    min-width: 300px;
    height: 50px;
    line-height: 50px;
    border-radius: 3px;
    z-index: 9999;
    display: none;
    font-size: 15px;
    /*animation: fadeInOut 2.5s ease-in-out forwards;*/
}

/*
@keyframes fadeInOut {
    0% {
        opacity: 0;
    }

    50% {
        opacity: 1;
        transform: translateY(0);
        opacity: 1;
    }

    75% {
        transform: translateY(-100%);
        opacity: 0;
    }

    100% {
        opacity: 0;
    }
}*/

.Expand {
    display: none;
    transition: height 0.5s;

}

    .Expand .XC-left-item {
        flex: 1
    }

.Expand-item {
    margin-top: 5px;
    display: flex;
}


.XC-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.XC-container {
    display: flex;
}

.XC-container-item{
    flex:1;
}

.XC-left {
    width: 78%;
    display: flex
}

.XC-right {
    padding: 0px 5px;
    width: 24%;
}

.XC-left > .XC-left-item {
    flex: 1
}

.Expand-item {
    width: 77%;
}

.Expand-item > .XC-left-item {
    flex: 1
}

.XC-container .XC-left-span, .Expand .XC-left-span {
    display: inline-block;
    width: 55px;
    text-align: right;
    font-size: 12px;
    color: #808080
}

.XC-container .XC-left-input, .Expand .XC-left-input {
    width: 67%;
    height: 28px;
    font-size: 12px;
    color: #808080;
    padding: 0px 5px;
    border: 1px solid #E5E5E5;
    border-radius: 2px;
}
