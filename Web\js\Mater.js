﻿


$(function() {

    function EntityMsg(item, Mesage) {
        $("#div_warning").show();
        item.focus();
        $("#sMessage").html(Mesage);

    }
    // 重新登录
    $('#BtnRelationLogin').click(function() {
        var Num = parseInt(Math.random() * 1000);
        location.href = "../Login.htm?CFlag=30&RA" + Num;

    });









    //  根据客户编码获取简称
    $('#txtBCustNo').blur(function() {
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();
        var sCNo = $('#txtBCustNo').val();
        var Flag = "41-2";
        var time = new Date();
        var sM = time.getMonth() + 1;
        if (sM < 10) {
            sM = "0" + sM
        }
        var sD = time.getDate()
        if (sD < 10) {
            sD = "0" + sD
        }

        var s1 = time.getFullYear() + "-" + sM + "-" + sD;
        $('#txtPPDate').val(s1);

        if (sCNo == "") {
            $("#div_warning").html("请输入客户编码！")
            $("#divsuccess").show();
            return;
        }


        var Data = '';
        var Params = { CNo: sCNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetCustInfoByNo&CFlag=41-2",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson.length > 0) {
                    // var sStr = eval(parsedJson.json)[0];
                    //$('#txtRNo').val(row.ReceiveNo);
                    $('#txtNameEn').val(parsedJson[0].CustEn);
                    $('#txtPPNo').val(parsedJson[0].CustNo);
                    $('#txtPPNo').focus();


                } else {
                    $("#txtNameEn").html("系统不存在该客户信息");
                }
            },
            error: function(data) {
                $("#div_warning2").html("系统出错，请重试2！")
                $("#div_warning2").show();
            }
        });

    });



    //  保存:收货信息
    $('#SH_Btn').click(function() {
        $("#SH_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();

        var ck1 = ""; var ck2 = ""; var ck3 = ""; var ck4 = ""; var ck5 = "";
        var RNo = $("#txtRNo").val();
        var PONo = $("#txtPurchNo").val();
        var POItem = $("#txtPurchItem").val();
        var DNum = $("#txtDNum").val();  // 送货数量
        var OldNum = $("#txtOldNum").val(); // 修改前数量
        var Num = $("#txtNum").val();
        var ZCNum = $("#txtZCNum").val();
        var DNo = $("#txtDNo").val();
        var SHNo = $("#txtDeliveryNo").val();
        var EffDate = $("#txtEffDate").val();
        var Remark = $("#txtRemark").val();
        var sQT = $("#txtCHQT").val();
        var Flag = $("#txtAEFlag").val();


        if (PONo == "") {
            $("#div_warning").html("获取不到PO单号，请重试。")
            $("#div_warning").show();
            $("#SH_Btn").removeAttr("disabled");
            return;
        }
        if (Num == "") {
            $("#div_warning").html("请填写收货数量。")
            $("#div_warning").show();
            $("#SH_Btn").removeAttr("disabled");
            return;
        }
        if (isNaN(Num)) {
            $("#div_warning").html("数量请填写数字！")
            $("#div_warning").show();
            $("#SH_Btn").removeAttr("disabled");
            return;
        }

        // 收货数量不能大于送货数量
        if (parseFloat(Num) > parseFloat(DNum)) {
            $("#div_warning").html("收货数量大于送货数量，不允许！")
            $("#div_warning").show();
            $("#SH_Btn").removeAttr("disabled");
            return;
        }

        if ($("#Checkbox1").is(':checked')) {
            ck1 = "1";
        }
        if ($("#Checkbox2").is(':checked')) {
            ck2 = "1";
        }
        if ($("#Checkbox3").is(':checked')) {
            ck3 = "1";
        }
        if ($("#Checkbox4").is(':checked')) {
            ck4 = "1";
        }
        if ($("#Checkbox5").is(':checked')) {
            ck5 = "1";
        }


        var Data = '';
        var Params = { RNo: RNo, DNo: DNo, PONo: PONo, POItem: POItem, DNum: DNum, OldNum: OldNum, Num: Num, ZCNum: ZCNum, EffDate: EffDate, SHNo: SHNo, ck1: ck1, ck2: ck2, ck3: ck3, ck4: ck4, ck5: ck5, cQT: sQT, Remark: Remark, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/MaterAjax.ashx?OP=AddEditSH&CFlag=10",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#SH_Btn").removeAttr("disabled");

                    $("#div_warning").html("提交成功！")
                    $("#div_warning").show();

                    if (Flag == "1") {  // 新增
                        $('#POAud_open').click();
                    }
                    else {   // 修改
                        GetSHList(PONo, "", "10");
                        $('#POAud_open').click();
                    }

                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#SH_Btn").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_OVER') {
                    $("#SH_Btn").removeAttr("disabled");
                    $("#div_warning").html("该采购订单已收货完成！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_NOTIQC') {
                    $("#SH_Btn").removeAttr("disabled");
                    $("#div_warning").html("该采购订单有一笔收货记录，未进入IQC检验，修改信息即可，无需新增收货记录！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#SH_Btn").removeAttr("disabled");
                    $("#div_warning").html("该收货记录IQC已检验，不可修改！")
                    $("#div_warning").show();
                } else {
                    $("#SH_Btn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#SH_Btn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });



    //  合格数量变更时
    $('#txtOKNum').blur(function() {
        var PNum = $("#txtPurchNum").val(); // 采购数量
        var DNum = $("#txtNum").val(); // 实收数量
        //var ZCNum = $("#txtZCNum").val(); // 暂存仓数量
        var Num = $("#txtOKNum").val();  // 合格数量

        if (Num == "") {
            $("#div_warning").html("请填写合格数量。")
            $("#div_warning").show();
            $("#IQC_Btn").removeAttr("disabled");
            return;
        }
        if (isNaN(Num)) {
            $("#div_warning").html("合格数量请填写数字！")
            $("#div_warning").show();
            $("#IQC_Btn").removeAttr("disabled");
            return;
        }

        if (parseFloat(DNum) < parseFloat(Num)) {
            $("#div_warning").html("数量填写不正确！")
            $("#div_warning").show();
            $("#IQC_Btn").removeAttr("disabled");
            $("#txtOKNum").val("");
            $("#txtZCNum").val("");
            $("#txtBadNum").val("");
            return;
        }

        // 暂存仓数量
        var ZCNum = parseFloat(Num) - parseFloat(PNum);
        if (ZCNum > 0) {
            $("#txtZCNum").val(ZCNum.toFixed(2));
            $("#txtRBNum").val("0");
        }
        else {
            $("#txtZCNum").val("0");
        }

        var RBNum = $("#txtRBNum").val(); // 让步接收数量 txtBadNum

        var BadNum = parseFloat(DNum) - parseFloat(Num) - parseFloat(RBNum);
        if (BadNum < 0) {
            $("#div_warning").html("数量填写不正确！")
            $("#div_warning").show();
            $("#IQC_Btn").removeAttr("disabled");
            return;
        }
        else {
            $("#txtBadNum").val(BadNum.toFixed(2));
        }


    });
    //  让步数量变更时
    $('#txtRBNum').blur(function() {
        var PNum = $("#txtPurchNum").val(); // 采购数量
        var DNum = $("#txtNum").val(); // 收货数量
        //var ZCNum = $("#txtZCNum").val(); // 暂存仓数量
        var Num = $("#txtOKNum").val();  // 合格数量
        var RBNum = $("#txtRBNum").val(); // 让步接收数量 txtBadNum

        if (RBNum == "") {
            $("#div_warning").html("请填写让步接收数量。")
            $("#div_warning").show();
            $("#IQC_Btn").removeAttr("disabled");
            return;
        }
        if (isNaN(RBNum)) {
            $("#div_warning").html("让步接收数量请填写数字！")
            $("#div_warning").show();
            $("#IQC_Btn").removeAttr("disabled");
            return;
        }
        if (Num == "") {
            Num = "0";
        }
        if (isNaN(Num)) {
            $("#div_warning").html("合格数量请填写数字！")
            $("#div_warning").show();
            $("#IQC_Btn").removeAttr("disabled");
            return;
        }

        if (parseFloat(DNum) < parseFloat(RBNum)) {
            $("#div_warning").html("数量填写不正确！")
            $("#div_warning").show();
            $("#IQC_Btn").removeAttr("disabled");
            $("#txtOKNum").val("");
            $("#txtZCNum").val("");
            $("#txtBadNum").val("");
            $("#txtRBNum").val("");
            return;
        }

        // 计算暂存仓数量
        if (parseFloat(RBNum) > 0) {
            $("#txtZCNum").val(0);   //有让步接收数量，就不会有暂存仓数量
        }
        else {
            var ZCNum = parseFloat(Num) - parseFloat(PNum);
            if (ZCNum > 0) {
                $("#txtZCNum").val(ZCNum.toFixed(2));
            }
            else {
                $("#txtZCNum").val(0);
            }
        }

        var BadNum = parseFloat(DNum) - parseFloat(Num) - parseFloat(RBNum);
        if (BadNum < 0) {
            $("#div_warning").html("数量填写不正确！")
            $("#div_warning").show();
            $("#IQC_Btn").removeAttr("disabled");
            return;
        }
        else {
            $("#txtBadNum").val(BadNum.toFixed(2));
        }

    });


    //  保存:IQC检验
    $('#IQC_Btn').click(function() {
        $("#IQC_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();

        var IQCNo = $("#txtIQCNo").val();
        var RNo = $("#txtRNo").val();
        var DoWay = $("#txtDoWay").val();  // 检验类型
        var Accept = $("#txtAccept").val(); // 处理方式
        var DNum = $("#txtNum").val(); // 实收数量
        var Num = $("#txtOKNum").val();  // 合格数量
        var RBNum = $("#txtRBNum").val(); // 让步接收数量
        var ZCNum = $("#txtZCNum").val(); // 暂存仓数据
        var OldNum = $("#txtBadNum").val(); // 不合格数量
        var Desc = $("#txtBadDesc").val(); // 不合格原因
        var Remark = $("#txtRemark").val();
        var Flag = $("#txtAEFlag").val();


        if (RNo == "") {
            $("#div_warning").html("获取不到收货记录，请重试。")
            $("#div_warning").show();
            $("#IQC_Btn").removeAttr("disabled");
            return;
        }
        if (Num == "") {
            $("#div_warning").html("请填写合格数量。")
            $("#div_warning").show();
            $("#IQC_Btn").removeAttr("disabled");
            return;
        }
        if (isNaN(Num)) {
            $("#div_warning").html("合格数量请填写数字！")
            $("#div_warning").show();
            $("#IQC_Btn").removeAttr("disabled");
            return;
        }
        if (RBNum == "") {
            $("#div_warning").html("请填写让步接收数量。")
            $("#div_warning").show();
            $("#IQC_Btn").removeAttr("disabled");
            return;
        }
        if (isNaN(RBNum)) {
            $("#div_warning").html("让步接收数量请填写数字！")
            $("#div_warning").show();
            $("#IQC_Btn").removeAttr("disabled");
            return;
        }

        if (parseFloat(OldNum) < 0) {
            $("#div_warning").html("不合格数量为负数：合格数量或让步接收数量不正确！")
            $("#div_warning").show();
            $("#IQC_Btn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { RNo: RNo, DNo: IQCNo, PONo: DoWay, POItem: Accept, DNum: DNum, OldNum: OldNum, Num: Num, ZCNum: ZCNum, EffDate: RBNum, SHNo: Desc, Remark: Remark, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/MaterAjax.ashx?OP=AddEditSH&CFlag=11",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#IQC_Btn").removeAttr("disabled");

                    $("#div_warning").html("提交成功！")
                    $("#div_warning").show();
                    if (Flag != "5") {  // =  5 是修改检验记录
                        var sKong = "<option>待IQC检验</option><option>IQC已检验</option>";
                        $("#txtSStatus").empty();
                        $("#txtSStatus").append(sKong);
                    }
                    $('#SH_open').click();
                    $('#IQC_open').click();

                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#IQC_Btn").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_OVER') {
                    $("#IQC_Btn").removeAttr("disabled");
                    $("#div_warning").html("该收货批次已IQC检验，无需再检验！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#IQC_Btn").removeAttr("disabled");
                    $("#div_warning").html("该检验批次已入库，不可修改！")
                    $("#div_warning").show();
                } else {
                    $("#IQC_Btn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#IQC_Btn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });


    $('#txtTHKind').blur(function() {

        if ($("#txtTHKind").val() == "IQC不合格") {
            $("#txtFXNum").removeAttr("readonly");
            $("#txtRNum").removeAttr("readonly");
            $("#txtTLNum").attr({ "readonly": "readonly" });
            $("#txtTLZCNum").attr({ "readonly": "readonly" });
            $("#txtTLRBNum").attr({ "readonly": "readonly" });
            $("#txtTLNum").val("0");
            $("#txtTLZCNum").val("0");
            $("#txtTLRBNum").val("0");
        }
        else {
            $("#txtRNum").attr({ "readonly": "readonly" });
            $("#txtFXNum").attr({ "readonly": "readonly" });
            $("#txtTLNum").removeAttr("readonly");
            $("#txtTLZCNum").removeAttr("readonly");
            $("#txtTLRBNum").removeAttr("readonly");
            $("#txtRNum").val("0");
        }

    });


    //  保存:退货信息-物料退货
    $('#Return_Btn').click(function() {
        $("#Return_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();

        var Desc = "";
        var RNo = $("#txtReturnNo").val();  //
        var BNo = $("#txtBatchNo").val();  // 入库批次
        var DoWay = $("#txtTHKind").val(); // 退料类别， 
        var TKCNum = $("#txtTLNum").val();  // 退库存数量
        var TZCNum = $("#txtTLZCNum").val();  // 退暂存仓数量
        var TRBNum = $("#txtTLRBNum").val();  // 退让步接收数量
        var Num = $("#txtRNum").val();  // 退IQC不合格数量
        var OldNum = $("#txtOldNum").val();
        var FXNum = $("#txtFXNum").val();  // 再次放行数量
        Desc = $("#txtBadDesc").val(); // 不合格原因
        var Remark = $("#txtRemark").val();
        var Flag = $("#txtAEFlag").val();
        var IsPass = $("#txtIsPass").val();


        if (Flag == "12") {  // 审核
            Desc = $("#txtPDesc").val(); // 审核说明
        }
        else {
            if (BNo == "") {
                $("#div_warning").html("获取不到需要退货的库存，请重试。")
                $("#div_warning").show();
                $("#Return_Btn").removeAttr("disabled");
                return;
            }
            if ((TKCNum == "") || (TZCNum == "") || (TRBNum == "") || (Num == "")) {
                $("#div_warning").html("请填写退货数量，如无请填写0。")
                $("#div_warning").show();
                $("#Return_Btn").removeAttr("disabled");
                return;
            }
            if ((parseFloat(TKCNum) <= 0) && (parseFloat(TZCNum) <= 0) && (parseFloat(TRBNum) <= 0) && (parseFloat(Num) <= 0)) {
                $("#div_warning").html("退货数量必须大于0")
                $("#div_warning").show();
                $("#Return_Btn").removeAttr("disabled");
                return;
            }

            if ((isNaN(TKCNum)) && (isNaN(TZCNum)) && (isNaN(TRBNum)) && (isNaN(Num))) {
                $("#div_warning").html("退货数量请填写数字！")
                $("#div_warning").show();
                $("#Return_Btn").removeAttr("disabled");
                return;
            }
            if (FXNum == "") {
                FXNum = "0";
            }
            if (isNaN(FXNum)) {
                $("#div_warning").html("再次放行数请填写数字！")
                $("#div_warning").show();
                $("#Return_Btn").removeAttr("disabled");
                return;
            }

            if (parseFloat(Num) < parseFloat(FXNum)) {
                $("#div_warning").html("再次放行数量不能大于IQC退货数量")
                $("#div_warning").show();
                $("#Return_Btn").removeAttr("disabled");
                return;
            }


            if (parseFloat(TKCNum) > parseFloat($("#txtNum").val())) {
                $("#div_warning").html("退库数量大于库存数量")
                $("#div_warning").show();
                $("#Return_Btn").removeAttr("disabled");
                return;
            }

            if (parseFloat(TZCNum) > parseFloat($("#txtZCNum").val())) {
                $("#div_warning").html("退库数量大于暂存仓数量")
                $("#div_warning").show();
                $("#Return_Btn").removeAttr("disabled");
                return;
            }

            if (parseFloat(TRBNum) > parseFloat($("#txtRBNum").val())) {
                $("#div_warning").html("退库数量大于让步接收数量")
                $("#div_warning").show();
                $("#Return_Btn").removeAttr("disabled");
                return;
            }

        }

        var Data = '';
        var Params = { RNo: RNo, DNo: BNo, PONo: DoWay, POItem: "", DNum: TKCNum, OldNum: OldNum, Num: Num, ZCNum: TZCNum, RBNum: TRBNum, FXNum: FXNum, EffDate: Desc, SHNo: IsPass, Remark: Remark, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/MaterAjax.ashx?OP=AddEditSH&CFlag=15",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#Return_Btn").removeAttr("disabled");

                    $("#div_warning").html("提交成功！")
                    $("#div_warning").show();

                    $('#Stock_open').click();
                    $('#Return_open').click();

                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#Return_Btn").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_OVER') {
                    $("#Return_Btn").removeAttr("disabled");
                    $("#div_warning").html(parsedJson.Txt)
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_STATUS') {
                    $("#Return_Btn").removeAttr("disabled");
                    $("#div_warning").html("该退料单已审核，不能操作！")
                    $("#div_warning").show();
                } else {
                    $("#Return_Btn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#Return_Btn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });




    // 出库记录，上传检验文件
    $('#btnMaterOutload').click(function() {
        $("#sUpMessage").val("");
        var p = $("#txtOutNo");
        if (p.val() == "") {
            EntityMsg(p, "获取不到出库单号，请关闭界面重试！");
        }
        var arr = new Array();
        var sPath = $("#txtPath").val();
        var sUpFile = $("#UpFile").val();  // ,用下面这种方式，解决中文乱码问题。
        arr = sPath.split(';/'); //注split可以用字符或字符串分割
        if (arr.length > 5) {
            //EntityMsg(p, "上传图片不能超过五个");
            $("#div_warning").show();
            $("#sUpMessage").html("上传图纸文件不能超过5个！")
            return;
        }
        else {
            $("#Loading").show();
            $.ajaxFileUpload({
                url: '../Service/ApplyUpPicFile.aspx?NNo=' + p.val() + '&sFlag=17&sUpFile=' + encodeURI(sUpFile),
                fileElementId: 'UpFile',
                dataType: 'json',
                success: function(data) {
                    //                 var parsedJson = jQuery.parseJSON(data);
                    data = eval(data);
                    var sPath = $("#txtPath").val();
                    $("#txtPath").val(sPath + ";" + data.Path);
                    var sFile = $("#txtFile").val();
                    $("#txtFile").val(sFile + ";" + data.FileName);

                    for (var i = 1; i < 6; i++) {
                        if ($("#txtF" + i).val() == "") {
                            $("#txtF" + i).val(data.Path);
                            $("#la_F" + i).html(data.FileName);
                            $("#FTr" + i).show();
                            break;
                        }
                    }
                    $("#Loading").hide();
                    $("#sUpMessage").html("上传成功！")
                    //alert("上传成功");

                }
            });
        }
    });



    //  保存:上传的出库检验文件
    $('#OutDoc_Btn').click(function() {
        $("#OutDoc_Btn").attr({ "disabled": "disabled" });
        $("#div_warning2").hide();


        var sCNo = $("#txtOutNo").val();
        var sDocName = $("#txtFile").val();
        var sDocPath = $("#txtPath").val();
        var InMan = $("#txtInMan").val();
        var Flag = $("#txtAEFlag").val();  //   7-2

        if (sCNo == "") {
            $("#div_warning2").html("获取不到出库单号！")
            $("#div_warning2").show();
            $("#OutDoc_Btn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { RNo: sCNo, DNo: sDocName, PONo: sDocPath, POItem: "", MNo: "", DNum: "", OldNum: "", Num: "", ZCNum: "", EffDate: "", SHNo: "", Remark: "", Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/MaterAjax.ashx?OP=AddEditSH&CFlag=59",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#OutDoc_Btn").removeAttr("disabled");

                    document.getElementById('Div_Del').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';
                    $('#Out_open').click();

                    $("#txtPath").val("");
                    $("#txtInMan").val("");
                    $("#txtAEFlag").val("");  // 是新增还是修改
                    $("#UpFile").val("");

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#Out_open").removeAttr("disabled");
                    $("#loginError").html("您未登陆系统，请先登录！")
                    $("#loginError").show();
                    //location.href = "Login.htm";  
                } 
                else {
                    $("#Out_open").removeAttr("disabled");
                    $("#div_warning2").html("系统出错，请重试1！")
                    $("#div_warning2").show();
                }
            },
            error: function(data) {
                $("#Out_open").removeAttr("disabled");
                $("#div_warning2").html("系统出错，请重试2！")
                $("#div_warning2").show();
            }
        });
    });



    //  保存:发料信息
    $('#MaterOut_Btn').click(function() {
        $("#MaterOut_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();

        var RNo = $("#txtMOutNo").val();  //
        var BNo = $("#txtBatchNo").val();  // 入库批次
        var FKCNum = $("#txtFLNum").val();  // 发库存数量
        var FZCNum = $("#txtFLZCNum").val();  // 发暂存仓数量
        var FRBNum = $("#txtFLRBNum").val();  // 发让步接收数量
        var sObj = $("#txtObj").val();  // 发料对象
        var Remark = $("#txtRemark").val();
        var Flag = $("#txtAEFlag").val();


        if (BNo == "") {
            $("#div_warning").html("获取不到需要发料的库存，请重试。")
            $("#div_warning").show();
            $("#MaterOut_Btn").removeAttr("disabled");
            return;
        }
        if ((FKCNum == "") || (FZCNum == "") || (FRBNum == "")) {
            $("#div_warning").html("请填写发料数量，如无请填写0 ")
            $("#div_warning").show();
            $("#MaterOut_Btn").removeAttr("disabled");
            return;
        }
        if ((parseFloat(FKCNum) <= 0) && (parseFloat(FZCNum) <= 0) && (parseFloat(FRBNum) <= 0)) {
            $("#div_warning").html("发料数量必须大于0")
            $("#div_warning").show();
            $("#MaterOut_Btn").removeAttr("disabled");
            return;
        }

        if ((isNaN(FKCNum)) && (isNaN(FZCNum)) && (isNaN(FRBNum))) {
            $("#div_warning").html("发料数量请填写数字！")
            $("#div_warning").show();
            $("#MaterOut_Btn").removeAttr("disabled");
            return;
        }

        if (parseFloat(FKCNum) > parseFloat($("#txtNum").val())) {
            $("#div_warning").html("发料数量大于库存数量")
            $("#div_warning").show();
            $("#MaterOut_Btn").removeAttr("disabled");
            return;
        }

        if (parseFloat(FZCNum) > parseFloat($("#txtZCNum").val())) {
            $("#div_warning").html("发料数量大于暂存仓数量")
            $("#div_warning").show();
            $("#MaterOut_Btn").removeAttr("disabled");
            return;
        }

        if (parseFloat(FRBNum) > parseFloat($("#txtRBNum").val())) {
            $("#div_warning").html("发料数量大于让步接收数量")
            $("#div_warning").show();
            $("#MaterOut_Btn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { RNo: "", DNo: BNo, PONo: sObj, POItem: "", DNum: FKCNum, OldNum: "0", Num: "0", ZCNum: FZCNum, RBNum: FRBNum, FXNum: "0", EffDate: "", SHNo: "", Remark: Remark, Flag: Flag };
        
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/MaterAjax.ashx?OP=AddEditSH&CFlag=14",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#MaterOut_Btn").removeAttr("disabled");

                    $("#div_warning").html("提交成功！")
                    $("#div_warning").show();

                    $('#Stock_open').click();
                    $('#Out_open').click();

                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#MaterOut_Btn").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_OVER') {
                    $("#MaterOut_Btn").removeAttr("disabled");
                    $("#div_warning").html(parsedJson.Txt)
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_STATUS') {
                    $("#MaterOut_Btn").removeAttr("disabled");
                    $("#div_warning").html("该退料单已审核，不能操作！")
                    $("#div_warning").show();
                } else {
                    $("#MaterOut_Btn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#MaterOut_Btn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });



    //  根据物料编码获取物料信息
    $('#txtPMNo').blur(function() {
        $("#div_warning").hide();
        $("#divsuccess").hide();

        var sNo = $("#txtPMNo").val();
        var Flag = "33-99";

        if (sNo == "") {
            $("#div_warning").html("请输入物料编码！")
            $("#divsuccess").show();
            return;
        }

        var Data = '';
        var Params = { No: sNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetMaterInfoByNo&CFlag=" + Flag,
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $("#txtPMName").val(sStr.MaterName);
                    $("#txtSpec").val(sStr.MaterSpec);

                }
            },
            error: function(data) {
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });



    //  保存:产品入库信息
    $('#PStockSaveBtn').click(function() {
        $("#PStockSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();

        var RNo = $("#txtInNo").val();  //入库单号
        var MNo = $("#txtPMNo").val();
        var BNo = $("#txtOrderNo").val();
        var Num = $("#txtInNum").val();
        var Remark = $("#txtRemark").val();
        var Flag = $("#txtAEFlag").val();



        if (MNo == "") {
            $("#div_warning").html("请输入产品编码。")
            $("#div_warning").show();
            $("#PStockSaveBtn").removeAttr("disabled");
            return;
        }

        if (Num == "") {
            $("#div_warning").html("请输入入库数量。")
            $("#div_warning").show();
            $("#PStockSaveBtn").removeAttr("disabled");
            return;
        }

        if (isNaN(Num)) {
            $("#div_warning").html("入库数量请填写数字！")
            $("#div_warning").show();
            $("#PStockSaveBtn").removeAttr("disabled");
            return;
        }



        var Data = '';
        var Params = { RNo: RNo, DNo: BNo, PONo: MNo, POItem: "", DNum: "", OldNum: "", Num: Num, ZCNum: "", EffDate: "", SHNo: "", Remark: Remark, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/MaterAjax.ashx?OP=AddEditSH&CFlag=17",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#PStockSaveBtn").removeAttr("disabled");

                    $("#div_warning").html("提交成功！")
                    $("#div_warning").show();

                    $('#PStockBut_open').click();

                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#PStockSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#PStockSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("该产品编码不存在，或在物料主数据定义非成品或半成品！")
                    $("#div_warning").show();
                } else {
                    $("#PStockSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#PStockSaveBtn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });



    // 根据物料编码，获取入库批次及物料信息
    $('#txtBFMNo').blur(function() {

        var MNo = $("#txtBFMNo").val(); // 物料编码

        if (MNo == "") {
            $("#div_warning").html("请输入物料编码。")
            $("#div_warning").show();
            return;
        }


        $.ajax({
            type: "get",
            dataType: "json",
            contentType: "application/json;charset=utf-8",
            url: "../Service/MaterAjax.ashx?OP=GetMaterBatchList&CFlag=18-2" + "&CMNO=" + MNo,
            success: function(data) {
                var parsedJson = eval(data).Msg;

                $("#txtBatchNo").empty();
                $("#txtBatchNo").append(parsedJson);

                $('#txtPMName').val(eval(data).NAME);
            }
        });




    });


    //  保存:零件报废
    $('#MFaultySaveBtn').click(function() {
        $("#MFaultySaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();

        var RNo = $("#txtBFNo").val();  //报废单号
        var MNo = $("#txtBFMNo").val();
        var BNo = $("#txtBatchNo").val();
        var OldBNo = $("#txtOldBatchNo").val();
        var Num = $("#txtBFNum").val();
        var OldNum = $("#txtOldBFNum").val();
        var Cause = $("#txtCause").val();
        var Flag = $("#txtAEFlag").val();


        if (MNo == "") {
            $("#div_warning").html("请输入产品编码。")
            $("#div_warning").show();
            $("#MFaultySaveBtn").removeAttr("disabled");
            return;
        }

        if (Num == "") {
            $("#div_warning").html("请输入入库数量。")
            $("#div_warning").show();
            $("#MFaultySaveBtn").removeAttr("disabled");
            return;
        }

        if (isNaN(Num)) {
            $("#div_warning").html("入库数量请填写数字！")
            $("#div_warning").show();
            $("#MFaultySaveBtn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { RNo: RNo, DNo: BNo, PONo: MNo, POItem: OldBNo, DNum: "", OldNum: OldNum, Num: Num, ZCNum: "", EffDate: Cause, SHNo: "", Remark: "", Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/MaterAjax.ashx?OP=AddEditSH&CFlag=20",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#MFaultySaveBtn").removeAttr("disabled");

                    $("#div_warning").html("提交成功！")
                    $("#div_warning").show();

                    $('#MFaultyBut_open').click();

                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#MFaultySaveBtn").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_OVER') {
                    $("#MFaultySaveBtn").removeAttr("disabled");
                    $("#div_warning").html("库存不足或物料不存在，不能报废！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#MFaultySaveBtn").removeAttr("disabled");
                    $("#div_warning").html("该零件编码不存在，请确认！")
                    $("#div_warning").show();
                } else {
                    $("#MFaultySaveBtn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#MFaultySaveBtn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });
















    // 这里增加其他按钮，空间事件方法











});




// 用这个方法，主要是保证勾选不需要人勾，不设置disabled=disabled  ，这样用户看见的勾选比较清晰点。  2019  
function CheckBox_OnClick(ob) {
    var id = $(ob).attr("id");
    var sCD = id.substr(2, id.length);  // CB0  CB1  CB2


    if ($('#CB' + sCD).is(':checked')) {
        // $('#CB' + sCD).prop("checked", "checked");
        $('#CB' + sCD).removeProp("checked"); //设置为选中状态
    }
    else {
        $('#CB' + sCD).prop("checked", "checked");
    }


}




// 删除服务器的文件 --出库检验报告
function btn_OutDelFile(n) {
    var sFileName = $("#la_F" + n).html();
    var sDPath = $("#txtF" + n).val();

    $.ajaxFileUpload({
        url: '../Service/ApplyUpPicFile.aspx?sFlag=400&DelPath=' + encodeURI(sDPath),
        dataType: 'json',
        success: function(data, status) {
            //                 var parsedJson = jQuery.parseJSON(data);
            //    data = eval(data);
            if (data.Msg == "Success") {
                var sPath = $("#txtPath").val();
                sPath = sPath.replace(";" + sDPath, "");
                $("#txtPath").val(sPath);
                var sFile = $("#txtFile").val();
                sFile = sFile.replace(";" + sFileName, "");
                $("#txtFile").val(sFile);
                $("#txtF" + n).val("");
                $("#la_F" + n).html("");
                $("#JDFTr" + n).hide();

                // 更新数据库
                update_MaterOut("50");
            }
            else {
                $("#div_warning2").show();
                $("#div_warning2").html("删除记录失败！")
            }

            //  $("#sUpMessage").html("删除成功！")
            //alert("上传成功");

        }
    });

}




function update_MaterOut(n) {
    var sCNo = $("#txtOutNo").val();
    var sDocName = $("#txtFile").val();
    var sDocPath = $("#txtPath").val();


    var Data = '';
    var Params = { RNo: sCNo, DNo: sDocName, PONo: sDocPath, POItem: "", MNo: "", DNum: "", OldNum: "", Num: "", ZCNum: "", EffDate: "", SHNo: "", Remark: "", Flag: "7-2" };
    var Data = JSON.stringify(Params);

    $.ajax({
        type: "POST",
        url: "../Service/MaterAjax.ashx?OP=AddEditSH&CFlag=7-2",
        data: { Data: Data },
        success: function(data) {
            var parsedJson = jQuery.parseJSON(data);

            if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                $("#div_warning2").show();
                $("#div_warning2").html("删除成功！")
            }
            else {
                $("#div_warning2").html("系统出错，请重试1！")
                $("#div_warning2").show();
            }
        },
        error: function(data) {
            $("#div_warning2").html("系统出错，请重试2！")
            $("#div_warning2").show();
        }
    });
}