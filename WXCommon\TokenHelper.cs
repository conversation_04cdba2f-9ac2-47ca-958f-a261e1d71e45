﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using Common;

namespace WXCommon
{
    public class TokenHelper
    {

        //public string CorpID = "wx9824d60e1f9ee4ab";
        // 20160321：从后台读取
        //public string CorpSecret = "xUmC9WFVH5QZYDaD6SGdq79qjSxBJ4a_XfKufWboExNrEtL1tk3Xrtu3-Z1XK44B"; //"keqWvEOslySBpzkGyU7LYIerkVe95_GXMaWJ39lYHmxWyGvnqMCVUmPsI2Vj6CXq";


        //wx9824d60e1f9ee4ab
        //keqWvEOslySBpzkGyU7LYIerkVe95_GXMaWJ39lYHmxWyGvnqMCVUmPsI2Vj6CXq  -- 管理助手
        //xUmC9WFVH5QZYDaD6SGdq79qjSxBJ4a_XfKufWboExNrEtL1tk3Xrtu3-Z1XK44B  -- 利兴隆
        //yWKWtrS1gPPrOhE13cBBlZbT74phIs9wM3wuq2ESEQMLGSudwvL_Ffnof0khQkud  -- daneng



        #region 属性

        private string _Token;

        public string Token
        {
            get { return _Token; }
            set { _Token = value; }
        }

        public string GetToken(string URL, string CorpID, string Secret, string GroupID, string Company)
        {
            /*获取AccessToken*/

            // 从数据库读取 CorpID 及 CorpSecret
            //DataTable Dt = WeiXinInfo.GetCorpSecret(GroupID, "", Company);
            string CorpSecret = Secret; //= Dt.Rows[0]["Secret"].ToString();
            //string CorpID = Dt.Rows[0]["CorpID"].ToString();


            string Access_Token = string.Format(@"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={0}&corpsecret={1}", CorpID, CorpSecret);
            if (!string.IsNullOrEmpty(URL))
            {
                Access_Token = URL;
            }
            HttpWebRequest request = WebRequest.Create(Access_Token) as HttpWebRequest;
            CookieContainer cookieContainer = new CookieContainer();
            request.CookieContainer = cookieContainer;
            request.AllowAutoRedirect = true;
            request.Method = "POST";
            request.ContentType = "text/html";
            request.Headers.Add("charset", "utf-8");
            HttpWebResponse response = request.GetResponse() as HttpWebResponse;
            Stream responseStream = response.GetResponseStream();
            StreamReader sr = new StreamReader(responseStream, Encoding.UTF8);
            Token = sr.ReadToEnd();

            return Token;
        }


        /// <summary>
        /// 获取Token:主要是服务号，获取用户基本信息使用
        /// </summary>
        /// <param name="URL"></param>
        /// <param name="CorpID"></param>
        /// <param name="Secret"></param>
        /// <param name="GroupID"></param>
        /// <param name="Code"></param>
        /// <returns></returns>
        public string GetTokenTwo(string URL, string CorpID, string Secret, string GroupID, string Code)
        {
            /*获取AccessToken*/

            // 从数据库读取 CorpID 及 CorpSecret
            //DataTable Dt = WeiXinInfo.GetCorpSecret(GroupID, "", Company);
            string CorpSecret = Secret; //= Dt.Rows[0]["Secret"].ToString();
            //string CorpID = Dt.Rows[0]["CorpID"].ToString();

            //string Access_Token = string.Format(@"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={0}&corpsecret={1}", CorpID, CorpSecret);
            // https://api.weixin.qq.com/sns/oauth2/access_token?appid=APPID&secret=SECRET&code=CODE&grant_type=authorization_code
            string Access_Token = string.Format(@"https://api.weixin.qq.com/sns/oauth2/access_token?appid={0}&secret={1}&code={2}&grant_type=authorization_code", CorpID, CorpSecret, Code);
            
            if (!string.IsNullOrEmpty(URL))
            {
                Access_Token = URL;
            }
            HttpWebRequest request = WebRequest.Create(Access_Token) as HttpWebRequest;
            CookieContainer cookieContainer = new CookieContainer();
            request.CookieContainer = cookieContainer;
            request.AllowAutoRedirect = true;
            request.Method = "POST";
            request.ContentType = "text/html";
            request.Headers.Add("charset", "utf-8");
            HttpWebResponse response = request.GetResponse() as HttpWebResponse;
            Stream responseStream = response.GetResponseStream();
            StreamReader sr = new StreamReader(responseStream, Encoding.UTF8);
            Token = sr.ReadToEnd();

            return Token;
        }

        /// <summary>
        ///  获取Token
        /// </summary>
        /// <param name="URL"></param>
        /// <param name="GroupID">组id（部门ID）</param>
        /// <returns></returns>
        public string GetTokenString(string URL, string CorpID, string Secret, string GroupID, string Company)
        {
            string Token = GetToken(URL, CorpID, Secret, GroupID, Company);

            /*测试Token是否过期*/
            if (TokenExpired(Token, CorpID, Secret, GroupID, Company))
            {
                /*重新获取*/
                Token = GetTokenNew(CorpID, Secret, GroupID, Company);
            }
            return Token;

        }
        #endregion


        /// <summary>
        ///  获取Token：网页授权接口调用凭证,注意：此access_token与基础支持的access_token不同
        /// </summary>
        /// <param name="URL"></param>
        /// <param name="GroupID">组id（部门ID）</param>
        /// <returns></returns>
        public string GetTokenStringTwo(string URL, string CorpID, string Secret, string GroupID, string Code)
        {
            string Token = GetTokenTwo(URL, CorpID, Secret, GroupID, Code);

            /*测试Token是否过期*/
            if (TokenExpired(Token, CorpID, Secret, GroupID, ""))
            {
                /*重新获取*/
                Token = GetTokenNew(CorpID, Secret, GroupID, "");
            }
            return Token;

        }



        public string GetCode(string token, string code)
        {
            string Bcode = string.Empty;
            string sCode = string.Format(@"https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token={0}&code={1}", token, code);


            HttpWebRequest request = WebRequest.Create(sCode) as HttpWebRequest;
            CookieContainer cookieContainer = new CookieContainer();
            request.CookieContainer = cookieContainer;
            request.AllowAutoRedirect = true;
            request.Method = "POST";
            request.ContentType = "text/html";
            request.Headers.Add("charset", "utf-8");
            HttpWebResponse response = request.GetResponse() as HttpWebResponse;
            Stream responseStream = response.GetResponseStream();
            StreamReader sr = new StreamReader(responseStream, Encoding.UTF8);
            Bcode = sr.ReadToEnd();

            Bcode = GetJsonValue(Bcode, "UserId");

            return Bcode;
        }


        public string GetUseInfo(string token, string openID)
        {
            string Bcode = string.Empty;
            //string sCode = string.Format(@"https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token={0}&code={1}", token, code);

            string sCode = string.Format(@"https://api.weixin.qq.com/sns/userinfo?access_token={0}&openid={1}&lang=zh_CN", token, openID);

            // https://api.weixin.qq.com/sns/userinfo?access_token=ACCESS_TOKEN&openid=OPENID&lang=zh_CN


            HttpWebRequest request = WebRequest.Create(sCode) as HttpWebRequest;
            CookieContainer cookieContainer = new CookieContainer();
            request.CookieContainer = cookieContainer;
            request.AllowAutoRedirect = true;
            request.Method = "POST";
            request.ContentType = "text/html";
            request.Headers.Add("charset", "utf-8");
            HttpWebResponse response = request.GetResponse() as HttpWebResponse;
            Stream responseStream = response.GetResponseStream();
            StreamReader sr = new StreamReader(responseStream, Encoding.UTF8);
            Bcode = sr.ReadToEnd();

            //Bcode = GetJsonValue(Bcode, "UserId");

            return Bcode;
        }


        #region 验证Token是否过期
        /// <summary>
        /// 验证Token是否过期
        /// </summary>
        public static bool TokenExpired(string Token, string CorpID, string Secret, string GroupID, string Company)
        {
            string Access_Token = GetJsonValue(Token, "access_token");
            TokenHelper iTokenHlper = new TokenHelper();
            string jsonStr = iTokenHlper.GetToken(string.Format("https://qyapi.weixin.qq.com/cgi-bin/menu/get?access_token={0}&agentid=0", Access_Token), CorpID, Secret, GroupID, Company);
            if (GetJsonValue(jsonStr, "errcode") == "42001")
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 重新获取Token
        /// </summary>
        /// <param name="Access_Token"></param>
        /// <param name="GroupID">组id（部门ID）</param>
        /// <returns></returns>
        public string GetTokenNew(string CorpID, string Secret, string GroupID, string Company)
        {
            // 从数据库读取 CorpID 及 CorpSecret
            //DataTable Dt = WeiXinInfo.GetCorpSecret(GroupID, "", Company);
            string CorpSecret = Secret;//= Dt.Rows[0]["Secret"].ToString();
            //string CorpID = Dt.Rows[0]["CorpID"].ToString();

            TokenHelper iTokenHlper = new TokenHelper();
            return iTokenHlper.GetToken(string.Format("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={0}&secret={1}", CorpID, CorpSecret), CorpID, Secret, GroupID, Company);
        }

        #endregion

        #region 获取Json字符串某节点的值
        /// <summary>
        /// 获取Json字符串某节点的值
        /// </summary>
        public static string GetJsonValue(string jsonStr, string key)
        {
            string result = string.Empty;
            if (!string.IsNullOrEmpty(jsonStr))
            {
                key = "\"" + key.Trim('"') + "\"";
                int index = jsonStr.IndexOf(key) + key.Length + 1;
                if (index > key.Length + 1)
                {
                    //先截逗号，若是最后一个，截“｝”号，取最小值
                    int end = jsonStr.IndexOf(',', index);
                    if (end == -1)
                    {
                        end = jsonStr.IndexOf('}', index);
                    }

                    result = jsonStr.Substring(index, end - index);
                    result = result.Trim(new char[] { '"', ' ', '\'' }); //过滤引号或空格
                }
            }
            return result;
        }
        #endregion

        #region 管理通讯录：获取部门成员

        public string GetDepartmentSimpleList(string Access_Token, string CorpID, string Secret, string GroupID, string Company)
        {
            string fetch_child = "0";//1/0：是否递归获取子部门下面的成员
            string status = "0"; //0获取全部员工，1获取已关注成员列表，2获取禁用成员列表，4获取未关注成员列表。status可叠加
            string URL = string.Format(@"https://qyapi.weixin.qq.com/cgi-bin/user/simplelist?access_token={0}&department_id={1}&fetch_child=1&status=0", Access_Token, GroupID);
            TokenHelper iTokenHlper = new TokenHelper();
            return iTokenHlper.GetToken(URL, CorpID, Secret, GroupID, Company);
        }

        #endregion
    }
}
