﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using DAL;


namespace BLL
{
    public class MaterBll
    {

        /// <summary>
        /// 判断类别是否存在
        /// </summary>
        /// <param name="Kind"></param>
        /// <param name="KindList"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string JudgeSysKindExist(string Kind, string KindList, string QNo, string QT, string sComp, string sFlag)
        {
            return MaterDal.JudgeSysKindExist(Kind, KindList, QNo, QT, sComp, sFlag);
        }

        /// <summary>
        /// 获取询价信息
        /// </summary>
        /// <param name="PPNo"></param>
        /// <param name="CustNo"></param>
        /// <param name="CustName"></param>
        /// <param name="BDate"></param>
        /// <param name="EDate"></param>
        /// <param name="Status"></param>
        /// <param name="BDate"></param>
        /// <param name="EDate"></param>
        /// <param name="sInMan"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetPurchProject(string PPNo, string Item, string CustNo, string CustName, string BDate, string EDate, string Status, string sCheck, int Row, int num, string sInMan, string sComp, string sFlag)
        {
            return MaterDal.GetPurchProject(PPNo, Item, CustNo, CustName, BDate, EDate, Status, sCheck, Row, num, sInMan, sComp, sFlag);
        }



        // 查询物料控制相关数据
        public static DataTable GetMaterFlowInfo(string PONo, string MNo, string MName, string BDate, string EDate, string Status, string A, string B, string C, string D, int Row, int num, string sInMan, string sComp, string sFlag)
        {
            return MaterDal.GetMaterFlowInfo(PONo, MNo, MName, BDate, EDate, Status, A, B, C, D, Row, num, sInMan, sComp, sFlag);
        }



        // 插入出库信息
        public static string SaveProductOutInfo(string CKNo, string PPNo, string num, string InMan, string sComp, string sFlag)
        {
            return MaterDal.SaveProductOutInfo(CKNo, PPNo, num, InMan, sComp, sFlag);
        }


        // 保存物料相关的操作信息
        public static string AddEditSHInfo(string RNo, string DNo, string PONo, string POItem, string DNum, string OldNum, string Num, string ZCNum, string RBNum, string FXNum, string EffDate, string SHNo, string C1, string C2, string C3, string C4, string C5, string CQT, string sComp, string InMan, string InName, string Remark, string sFlag)
        {
            return MaterDal.AddEditSHInfo(RNo, DNo, PONo, POItem, DNum, OldNum, Num, ZCNum, RBNum, FXNum, EffDate, SHNo, C1, C2, C3, C4, C5, CQT, sComp, InMan, InName, Remark, sFlag);
        }


        /// <summary>
        /// 产生装箱单号，并返回装箱单明细
        /// </summary>
        /// <param name="sPStr"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable CreatePackList(string sPStr, string sComp, string sFlag)
        {
            return MaterDal.CreatePackList(sPStr, sComp, sFlag);

        }


        /// <summary>
        /// 删除装箱单信息：更新出库信息装箱单编号为空
        /// </summary>
        /// <param name="sOutNo"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string OPPackList(string sOutNo, string sComp, string sFlag)
        {
            return MaterDal.OPPackList(sOutNo, sComp, sFlag);

        }



        public static DataTable GetMaterBatchList(string MNo, string sComp, string sFlag)
        {
            return MaterDal.GetMaterBatchList(MNo, sComp, sFlag);
        }



        /// <summary>
        /// 获取出库明细
        /// </summary>
        /// <param name="POut"></param>
        /// <param name="Item"></param>
        /// <param name="RNo"></param>
        /// <param name="MNo"></param>
        /// <param name="sInMan"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetPOutMX(string POut, string Item, string RNo, string MNo, string sInMan, string sComp, string sFlag)
        {
            return MaterDal.GetPOutMX(POut, Item, RNo, MNo, sInMan, sComp, sFlag);
        }
















    }
}
