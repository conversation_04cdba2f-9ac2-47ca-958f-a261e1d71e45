﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志管理</title>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/XC.css" rel="stylesheet" />
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/BaseModule.js" type="text/javascript"></script>

    <script>
        //获取当前所在的模块、菜单
        const currentTabId = sessionStorage.getItem("currentTabId");
        const ModuleName = currentTabId != null ? JSON.parse(currentTabId).ModuleName : null;

        layui.use('table', function () {
            var table = layui.table;

            table.render({
                elem: '#LogInfoList',
                id: 'LogInfoListID',
                url: '../Service/BaseModuleAjax.ashx?OP=GetLogInfoList&CFlag=11',
                height: 'full-50',
                cols: [[ //标题栏
                    { type: 'numbers', title: '序号', width: 45, align: "center" },
                    { field: 'ModuleName', title: '模块', width: 180 },
                    { field: 'Message', title: '日志信息' },
                    { field: 'OperationType', title: '操作类型', width: 80, align: "center" },
                    {
                        field: 'Status', width: 60, title: '操作状态', width: 80, align: 'center', templet: function (d) {
                            if (d.Status == '失败') {
                                return '<span style="background-color: Red; color: white; font-size: 10px; padding: 4px;border-radius: 6px;">' + d.Status + '</span>';
                            } else if (d.Status == '成功') {
                                return '<span style="background-color: #0c873d; color: white; font-size: 10px; padding: 4px;border-radius: 6px;">' + d.Status + '</span>';
                            }
                        }
                    },
                    { field: 'FullName', title: '操作人', width: 100, align: "center" },
                    { field: 'IPAddress', title: '主机', width: 135 },
                    { field: 'InDate2', title: '操作时间', width: 150 },
                    { field: 'op', title: '操作', width: 100, toolbar: '#barDemo', fixed: 'right', align: "center" }
                ]],
                skin: 'line', // 表格风格
                page: true, // 是否显示分页
                count: 50, //数据总数 服务端获得
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
            });

            table.on('tool(LogInfoList)', function (obj) {
                var data = obj.data, layEvent = obj.event;
                if (layEvent == 'detail') {
                    $("#DModlueName").html(data.ModuleName)
                    $("#DFullName").html(data.FullName)
                    $("#DDatetime").html(data.InDate2)
                    $("#DMessage").html(data.Message)
                    $("#DIPAddress").html(data.IPAddress)
                    highlightDifferences(data.OldValue, data.NewValue)
                    $('#ShowOne').css("display", "block")
                    $('#ShowOne-fade').css("display", "block")
                }
            })

            // 获取当前日期（格式：YYYY-MM-DD）
            let currentEDate = new Date().toISOString().split('T')[0];
            $("#EndTime").val(currentEDate)
            // 获取7天前的日期（格式：YYYY-MM-DD）
            let currentBDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
            $("#StartTime").val(currentBDate)

            //获取模块名称
            GetModuleName()
            //获取操作类型
            GetOPType()

            $("#ModuleName").change(function () {
                //获取菜单名称
                GetMenuName($(this).val())
            })

            $("#LogInfoList_open").click(function () {
                var ModuleName = $("#ModuleName").val() || "";
                var MenuName = $("#MenuName").val() || "";
                var StartTime = $("#StartTime").val();
                var EndTime = $("#EndTime").val();
                var OPType = $("#OPType").val();
                var sMessage = $("#txtMessage").val()

                var ModuleMenu = "";

                if (ModuleName != "") {
                    //说明只选了模块
                    ModuleMenu = ModuleName;
                }

                if (ModuleName != "" || MenuName != "") {
                    //说明模块和菜单都选了
                    ModuleMenu = ModuleName + ">" + MenuName;
                }

                var Params = { No: "", Name: ModuleMenu, A: OPType, B: sMessage, C: "", D: "", E: "", F: "", G: "", H: "", BDate: StartTime, EDate: EndTime, };
                var Data = JSON.stringify(Params);
                table.reload('LogInfoListID', {
                    method: 'post',
                    url: '../Service/BaseModuleAjax.ashx?OP=GetLogInfoList&CFlag=11&Data=' + encodeURIComponent(Data),
                    page: {
                        curr: 1
                    }
                });
            })
        });

        function highlightDifferences(oldValue, newValue) {

            //只要新值与旧值 有一个为空 就不用作比较了
            if (oldValue == "" || newValue == "") {
                $("#DOldData").html(oldValue)
                $("#DNewData").html(newValue)
                return
            }

            //过滤去除的字段值
            const oldParts = oldValue.split(',')
            const newParts = newValue.split(',')

            let oldOutput = '';
            let newOutput = '';
            //循环判断新值与旧值 把发生变化的数据用颜色区分
            for (let i = 0; i < oldParts.length; i++) {
                const str = (i != oldParts.length - 1 ? "," : "");
                if (oldParts[i] !== newParts[i]) {
                    newOutput += `<span class="diff">${newParts[i]}</span>` + str;
                    oldOutput += oldParts[i] + str;
                }
            }

            $("#DOldData").html(oldOutput)
            $("#DNewData").html(newOutput)
        }


        //获取模块名称
        function GetModuleName() {
            const moduleNameSelect = $("#ModuleName");
            moduleNameSelect.empty();

            // 发起Ajax请求
            $.ajax({
                url: "../Service/BaseModuleAjax.ashx?OP=GetModule&CFlag=23-1",
                data: {},
                dataType: "json",
                success: function (paresData) {
                    moduleNameSelect.append("<option></option>");
                    const options = paresData.map((data) => `<option value="${data.ModuleName}">${data.ModuleName}</option>`);
                    moduleNameSelect.append(options);
                },
                error: function (err) {
                    console.error("获取模块名称时出错：", err);
                }
            });
        }

        //获取菜单名称
        function GetMenuName(ModuleName) {
            // 清空菜单名称下拉列表
            const menuNameSelect = $("#MenuName");
            menuNameSelect.empty();

            // 发起Ajax请求
            $.ajax({
                url: `../Service/BaseModuleAjax.ashx?OP=GetModule&CFlag=23-2&CKind=${encodeURIComponent(ModuleName)}`,
                data: {},
                dataType: "json",
                success: function (paresData) {
                    menuNameSelect.append("<option></option>");
                    const options = paresData.map((data) => `<option value="${data.ModuleName}">${data.ModuleName}</option>`);
                    menuNameSelect.append(options);
                },
                error: function (err) {
                    console.error("获取菜单名称时出错：", err);
                }
            });
        }

        function GetOPType() {
            var CNo = "";
            var CKind = encodeURI("操作类型");
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/BaseModuleAjax.ashx?OP=GetSysTypeChoogeList&CFlag=39&CMNO=" + CNo + "&CKind=" + CKind,
                success: function (data) {
                    $("#OPType").empty();
                    var sKong = "<option value=''> </option>";
                    $("#OPType").append(sKong + data.Msg);
                }
            });
        }

        function closeDialog() {
            $('#ShowOne').css("display", "none")
            $('#ShowOne-fade').css("display", "none")
        }
    </script>
    <style>
        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }

        .wangid_conbox {
            margin-right: 5px;
        }

        .div_find {
            width: 99%;
        }

        .find_input {
            width: 10%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }

        .table-td {
            width: 70px;
            text-align: right;
            font-size: 12px
        }

        .table-row {
            height: 65px
        }

        .layui-badge {
            width: 95%;
            height: 100%;
            min-height: 10px;
            text-align: left;
            margin-left: 15px;
            padding: 10px
        }

        .diff {
            color: red;
        }
    </style>
</head>
<body>
    <div class="div_find">
        <p>
            <label class="find_labela">操作模块</label>
            <select class="find_input" id="ModuleName"></select>
            <select class="find_input" id="MenuName"></select>
            <label class="find_labela">操作时间</label>
            <input type="datetime-local" id="StartTime" class="find_input" />
            <input type="datetime-local" id="EndTime" class="find_input" />
            <label class="find_labela">操作类型</label>
            <select class="find_input" id="OPType"></select>
            <label class="find_labela">日志信息</label>
            <input type="text" id="txtMessage" class="find_input" placeholder="支持模糊查询" style="padding-left:5px;width:12%" />
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="LogInfoList_open">搜索</button>
        </p>
    </div>
    <div class="wangid_conbox">
        <table class="layui-hide" id="LogInfoList" lay-filter="LogInfoList"></table>
        <script type="text/html" id="barDemo">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detail">详情</button>
        </script>
    </div>

    <!--弹出层-->
    <div class="XC-modal XC-modal-xl" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1">日志详情</span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <table style="width:100%">
                    <tr class="table-row">
                        <td class="table-td">操作模块</td>
                        <td>
                            <div class="layui-badge layui-bg-gray" id="DModlueName"></div>
                        </td>
                    </tr>
                    <tr class="table-row">
                        <td class="table-td">操作人</td>
                        <td>
                            <div class="layui-badge layui-bg-gray" id="DFullName"></div>
                        </td>
                    </tr>
                    <tr class="table-row">
                        <td class="table-td">操作时间</td>
                        <td>
                            <div class="layui-badge layui-bg-gray" id="DDatetime"></div>
                        </td>
                    </tr>
                    <tr class="table-row">
                        <td class="table-td">主机</td>
                        <td>
                            <div class="layui-badge layui-bg-gray" id="DIPAddress"></div>
                        </td>
                    </tr>
                    <tr class="table-row">
                        <td class="table-td">日志信息</td>
                        <td>
                            <div class="layui-badge layui-bg-gray" id="DMessage"></div>
                        </td>
                    </tr>
                    <tr class="table-row">
                        <td class="table-td">旧数据</td>
                        <td>
                            <div class="layui-badge layui-bg-gray" id="DOldData"></div>
                        </td>
                    </tr>
                    <tr class="table-row">
                        <td class="table-td">新数据</td>
                        <td>
                            <div class="layui-badge layui-bg-gray" id="DNewData"></div>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="btn_DeviceInfoSaveClose" onclick='closeDialog()'>关闭</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>
</body>
</html>