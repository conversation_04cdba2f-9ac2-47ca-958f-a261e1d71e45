﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using Common;
using System.Text;
using System.IO;

namespace DAL
{
    public class PrdAssistDal
    {

        /// 判断信息是否存在 <summary>
        /// 判断信息是否存在
        /// </summary>
        /// <param name="Kind"></param>
        /// <param name="KindList"></param>
        /// <param name="QT"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string JudgeObjectExist(string Kind, string KindList, string QT, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string sStatus = string.Empty;

            if (sFlag == "1") // 判断不良现象名称是否存在
            {
                sSQL = " select InMan from T_DefectsDesc where CompanyNo= '" + sComp + "' and WDesc = '" + Kind + "' ";
            }
            else if (sFlag == "3")  // 删除工序，判断是否已用于产品流程
            {
                sSQL = " select InMan from T_ProductFlow where ProcedureNo = '" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "3-1")  // 删除工序，判断是否已用于BOM
            {
                sSQL = " select InMan from T_BOMInfo where ProcedureNo = '" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "6")  // 删除不良原因，判断是否存在
            {
                sSQL = " select InMan from T_DefectsCause where CDesc = '" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "7")  // 删除不良原因，判断是否存在
            {
                sSQL = " select InMan from T_DeptInfo where DeptName = '" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "9")  // 删除部门信息，判断是否存在
            {
                sSQL = " select InMan from T_DeptInfo where DeptName = '" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "10-2")  // 删除清场计划信息，判断是否存在
            {
                sSQL = " select InMan from T_ClearPlanHead where CPNo = '" + Kind + "' and  CPVer ='" + KindList + "' and  CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "12")  // 删除清场计划信息，判断是否存在
            {
                sSQL = " select InMan from T_OrderClearRec where CPNo = '" + Kind + "' and  CPVer ='" + KindList + "' and  CompanyNo= '" + sComp + "' ";
            }

            else if (sFlag == "12-1")  // 删除清场计划明细信息，判断是否存在
            {
                sSQL = " select InMan from T_ClearPlanDetail where CPNo = '" + Kind + "' and  CPVer ='" + KindList + "'  and ItemNo ='" + QT + "' and  CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "16")  // 清仓记录中判断是否存在清场计划明细数据,如无数据则提示不可以提交
            {
                sSQL = " select InMan from T_ClearPlanDetail where CPNo = '" + Kind + "' and  CPVer ='" + KindList + "'   and  CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "15")  // 删除仓库信息，判断是否存在
            {
                sSQL = " select InMan from T_Warehouse where FWNo = '" + Kind + "'  and  CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "19")  // 删除设备信息，判断是否存在
            {
                sSQL = " select InMan from T_DeviceInfo where DeviceNo = '" + Kind + "'  and  CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "22")  // 新增设备分类信息，判断编号是否重复存在
            {
                sSQL = " select InMan from T_DeviceClassHead where MaterNo = '" + Kind + "'  and  CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "24")  // 删除设备信息，判断是否存在
            {
                sSQL = " select InMan from T_DeviceInfo where MaterNo = '" + Kind + "'  and  CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "30-2")  // 删除质量控制计划信息，判断是否存在
            {
                sSQL = " select InMan from T_QAControlPlanHead where QPNo = '" + Kind + "'  and  Ver = '" + KindList + "'  and  CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "32")  // 删除质量控制计划信息，判断是否存在
            {
                sSQL = " select InMan from T_QAControlEXEHead where QPNo = '" + Kind + "'  and   Ver = '" + KindList + "'  and  CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "33-1")  // 删除质量控制计划信息，判断是否存在
            {
                sSQL = " select InMan from T_QAControlEXEDetail  where ENo = '" + Kind + "'  and   ItemNo = '" + KindList + "' and SerialNo='"+ QT+"' and  CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "36-2")  // 升级iPQC巡查计划信息，判断是否存在重复编号和版本
            {
                sSQL = " select InMan from T_InspectPlanHead where IPNo = '" + Kind + "'   and  Ver = '" + KindList + "'  and  CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "38")  // 删除IPQC巡查计划信息，判断是否已经被IPQC巡查执行引用
            {
                sSQL = " select InMan from T_InspectEXE where IPNo = '" + Kind + "' and Ver= '" + KindList + "'  and  CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "41")  // 删除巡查信息信息，判断是否存在
            {
                sSQL = " select InMan from T_InspectEXE where ENo = '" + Kind + "'  and   Status in ('执行中' ,'已完成') and  CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "35")  // 删除质量控制执行信息，判断是否存在
            {
                sSQL = " select InMan from T_QAControlEXEHead  where ENo = '" + Kind + "'  and   Status in ('执行中' ,'已完成') and  CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "42")  // 新增试剂/质控品库，判断是否存在
            {
                sSQL = " select InMan from T_QCWarehouse  where LotNo = '" + Kind + "'  and    CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "45")  // 工艺文件库文件编号是否存在，存在则不让重复新增
            {
                sSQL = " select InMan from T_ProcessFile  where FileNo = '" + Kind + "'  and  CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "48")
            {
                sSQL = "select * from T_RecordFile where [No] = '" + Kind + "' and FileKind='" + KindList + "' and  CompanyNo= '" + sComp + "' ";
            }

            DataTable dt = DBHelper.GetDataTable(sSQL);
            if (dt.Rows.Count > 0)
            {
                sStatus = "Y";
            }
            else
            {
                sStatus = "N";
            }

            return sStatus;
        }


        /// <summary>
        /// 公司信息获取
        /// </summary>
        /// <param name="LoginMan"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>

        public static DataTable GetCompanyInfo(string LoginMan, string sComp, string sFlag)
        {
            string sSQL = string.Empty;

            DataTable sdt = new DataTable();

            if (sFlag == "66")  // 显示公司信息
            {
                sSQL = " SELECT  * FROM T_CompanyInfo where CompanyNo = '" + sComp + "' ";
            }
            else
            {
                sSQL = " SELECT top 5 * FROM T_CompanyInfo  ";
            }

            sdt = DBHelper.GetDataTable(sSQL);

            return sdt;
        }

        /// <summary>
        /// 生产信息更新状态获取，通过编号获取
        /// </summary>
        /// <param name="No"></param>
        /// <param name="Ver"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetPrdAssistStatusForNo(string sNo, string sVer, string sComp, string sFlag)

        {
            string sSQL = string.Empty;

            DataTable sdt = new DataTable();

            if (sFlag == "65")  // 显示公司信息
            {
                sSQL = " SELECT  * FROM T_OrderClearRec where CRNo = '" + sNo + "' and CompanyNo = '" + sComp + "' ";
            }
            else if(sFlag == "66")  // 显示公司信息
            {
                sSQL = " SELECT  * FROM T_QAControlEXEHead where ENo = '" + sNo + "'  and CompanyNo = '" + sComp + "' ";
            }
            else if (sFlag == "66-1")  // 显示公司信息
            {
                sSQL = " SELECT  * FROM T_QAControlEXEHead where ENo = '" + sNo + "' and ItemNo='"+ sVer + "'   and CompanyNo = '" + sComp + "' ";
            }
            else if (sFlag == "67")  // 显示公司信息
            {
                sSQL = " SELECT  * FROM T_InspectEXE where ENo = '" + sNo + "'  and CompanyNo = '" + sComp + "' ";
            }
            else if (sFlag == "68")  // 显示公司信息
            {
                sSQL = " SELECT  * FROM T_DeviceCheckTaskHead where TNo = '" + sNo + "'   and CompanyNo = '" + sComp + "' ";
            }
            else if (sFlag == "69")  // 显示公司信息
            {
                sSQL = " SELECT  * FROM T_DeviceMaintainTaskHead where TNo = '" + sNo + "'  and CompanyNo = '" + sComp + "' ";
            }
            else
            {
                sSQL = " SELECT  * FROM T_InspectEXE  where ENo = '' ";
            }

            sdt = DBHelper.GetDataTable(sSQL);

            return sdt;
        }
        

        /// 获取生产相关的基础信息 <summary>
        /// 获取生产相关的基础信息，如不良现象、不良原因、部门信息、清场计划
        /// </summary>
        /// <param name="No"></param>
        /// <param name="Name"></param>
        /// <param name="Item"></param>
        /// <param name="Status"></param>
        /// <param name="BDate"></param>
        /// <param name="EDate"></param>
        /// <param name="A"></param>
        /// <param name="B"></param>
        /// <param name="C"></param>
        /// <param name="D"></param>
        /// <param name="E"></param>
        /// <param name="Row"></param>
        /// <param name="num"></param>
        /// <param name="sInMan"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetPrdAssistInfo(string No, string Name, string Item, string Status, string BDate, string EDate, string A, string B, string C, string D, string E, int Row, int num, string sInMan, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string sChStr = string.Empty;
            string sNum = string.Empty;
            DataTable sdt = new DataTable();

            if (sFlag == "1")//获取不良现象
            {
                sSQL = " select ProcedureNo,'('+ProcedureNo+')'+ProcedureName as PNName,ProcedureName from T_WorkProcedure where CompanyNo='" + sComp + "' order by ProcedureNo ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "2")//获取不良原因
            {
                sSQL = " select ProcedureNo,'('+ProcedureNo+')'+ProcedureName as PNName,ProcedureName from T_WorkProcedure where CompanyNo='" + sComp + "' order by ProcedureNo ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "3")//获取部门
            {
                sSQL = " select ProcedureNo,'('+ProcedureNo+')'+ProcedureName as PNName,ProcedureName from T_WorkProcedure where CompanyNo='" + sComp + "' order by ProcedureNo ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "4")//获取清场计划信息
            {
                sSQL = " select ProcedureNo,'('+ProcedureNo+')'+ProcedureName as PNName,ProcedureName from T_WorkProcedure where CompanyNo='" + sComp + "' order by ProcedureNo ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "aa")//获取公司名称
            {
                sSQL = " select ProcedureNo,'('+ProcedureNo+')'+ProcedureName as PNName,ProcedureName from T_WorkProcedure where CompanyNo='" + sComp + "' order by ProcedureNo ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else  //获取生产基础信息相关分页查询数据
            {
                SqlParameter[] parameters = {
                    new SqlParameter("@lnNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnItem", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnStatus", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnE", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,10),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
                parameters[0].Value = No;
                parameters[1].Value = Item;
                parameters[2].Value = Name;
                parameters[3].Value = Status;
                parameters[4].Value = BDate;
                parameters[5].Value = EDate;
                parameters[6].Value = A; // A
                parameters[7].Value = B;
                parameters[8].Value = C;
                parameters[9].Value = D;
                parameters[10].Value = E;
                parameters[11].Value = Row;
                parameters[12].Value = num;
                parameters[13].Value = sInMan;
                parameters[14].Value = sFlag;
                parameters[15].Value = "";

                DataSet DS = DBHelper.RunProcedureForDS("P_GetPrdAssistInfoForPage", parameters);
                sdt = DS.Tables[0];
            }

            return sdt;
        }


        /// 操作生产相关急促信息 <summary>
        /// 操作生产相关基础信息，如不良现象、不良原因、部门信息
        /// </summary>
        /// <param name="No"></param>
        /// <param name="Name"></param>
        /// <param name="Item"></param>
        /// <param name="A"></param>
        /// <param name="B"></param>
        /// <param name="C"></param>
        /// <param name="D"></param>
        /// <param name="E"></param>
        /// <param name="F"></param>
        /// <param name="G"></param>
        /// <param name="H"></param>
        /// <param name="I"></param>
        /// <param name="J"></param>
        /// <param name="K"></param>
        /// <param name="L"></param>
        /// <param name="Kind"></param>
        /// <param name="sComp"></param>
        /// <param name="InMan"></param>
        /// <param name="Remark"></param>
        /// <param name="sFlag">1：新增；2：修改；3：删除</param>
        /// <returns></returns>
        public static string OPPrdAssistInfo(string No, string Name, string Item, string A,
            string B, string C, string D, string E, string F, string G, string H, string I, string J, string K,
            string L, string Kind, string sComp, string InMan, string Remark, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sstr = string.Empty;
            string sNum = string.Empty;
            string sSt = string.Empty;

            string sCPVer = string.Empty;
            string sCPNo = string.Empty;
            string sSNO = string.Empty;
            string sItemNo = string.Empty;
            DataTable sdt = new DataTable();


            if (sFlag == "1")  // 新增不良现象信息  
            {
                sSQL = " insert into T_DefectsDesc (WNo,WDesc,WKind,Status,CompanyNo,InMan,Remark) " +
                       " values('" + No + "','" + Name + "','" + Kind + "','在用','" + sComp + "','" + InMan + "','" + Remark + "') ";
            }
            else if (sFlag == "2")  // 修改不良现象信息
            {
                sSQL = " update T_DefectsDesc set WDesc='" + Name + "', WKind='" + Kind + "',Remark='" + Remark + "' where WNo='" + No + "' ";
            }
            else if (sFlag == "3")  // 删除不良现象信息
            {
                sSQL = " delete T_DefectsDesc  where WNo='" + No + "'  ";
            }
            else if (sFlag == "4")  // 新增不良原因
            {
                sSQL = " insert into T_DefectsCause (CNo,CDesc,CKind,Status,CompanyNo,InMan,Remark) " +
                       " values('" + No + "','" + Name + "','" + Kind + "','在用','" + sComp + "','" + InMan + "','" + Remark + "') ";
            }
            else if (sFlag == "5")  // 修改不良原因
            {
                sSQL = " update T_DefectsCause set CDesc='" + Name + "', CKind='" + Kind + "',Remark='" + Remark + "' where CNo='" + No + "' ";
            }
            else if (sFlag == "6")  // 删除不良原因信息
            {
                sSQL = " delete T_DefectsCause  where CNo='" + No + "'  ";
            }
            else if (sFlag == "7")  // 新增部门信息
            {
                sSQL = " insert into T_DeptInfo (DeptNo,DeptName,DeptAddr,Status,CompanyNo,InMan,Remark) " +
                       " values('" + No + "','" + Name + "','" + Kind + "','在用','" + sComp + "','" + InMan + "','" + Remark + "') ";
            }
            else if (sFlag == "8")  // 修改部门信息
            {
                sSQL = " update T_DeptInfo set DeptName='" + Name + "',DeptAddr='" + Kind + "',Remark='" + Remark + "' where DeptNo='" + No + "' ";
            }
            else if (sFlag == "9")  // 删除部门信息
            {
                sSQL = " delete T_DeptInfo  where DeptNo='" + No + "'  ";
            }
            else if (sFlag == "10")  // 新增清场计划信息
            {
                sSQL = " insert into T_ClearPlanHead (CPNo, CPVer,Status,CompanyNo,InMan,Remark) " +
                       " values('" + No + "','" + A + "','在用','" + sComp + "','" + InMan + "','" + Remark + "') ";
                //CPNo清场计划编号No， CPVer清场计划版本A，ItemNo清场计划序号Item，CPTxt清场计划事项描述Name，Type清场计划数据类型Kind
            }
            else if (sFlag == "10-2")  // 新增清场计划升版
            {
                sSQLs = " insert into T_ClearPlanHead (CPNo, CPVer,Status,CompanyNo,InMan,Remark) " +
                       "  select  CPNo,'" + A + "',Status,CompanyNo,'" + InMan + "','" + Remark + "'  from  T_ClearPlanHead  where  CPNo='" + No + "' and CPVer='"+Kind+"' ";
                sSQL = " insert into T_ClearPlanDetail (CPNo, CPVer,ItemNo,SNO,CPTxt,Type,Status,CompanyNo,InMan,Remark) " +
                  " select CPNo, '" + A + "',ItemNo,SNO,CPTxt,Type,Status,CompanyNo,'" + InMan + "',Remark from  T_ClearPlanDetail where   CPNo='" + No + "' and CPVer='"+Kind+"' ";

                //CPNo清场计划编号No， CPVer清场计划版本A，ItemNo清场计划序号Item，CPTxt清场计划事项描述Name，Type清场计划数据类型Kind
            }
            else if (sFlag == "11")  // 修改清场计划信息
            {
                sSQL = " update T_ClearPlanHead set Remark='" + Remark + "' where CPNo='" + No + "'  and  CPVer='" + A + "'";
            }
            else if (sFlag == "12")  // 删除清场计划信息
            {
                sSQLs = " delete T_ClearPlanHead  where CPNo='" + No + "'  and  CPVer='" + A + "'";
                sSQL = " delete T_ClearPlanDetail  where CPNo='" + No + "'  and  CPVer='" + A + "'";
            }
            else if (sFlag == "10-1")  // 新增清场计划项目信息
            {
                sSQL = " insert into T_ClearPlanDetail (CPNo, CPVer,ItemNo,SNO,CPTxt,Type,Status,CompanyNo,InMan,Remark) " +
                       " values('" + No + "','" + A + "','" + Item + "','" + L + "','" + Name + "','" + Kind + "','在用','" + sComp + "','" + InMan + "','" + Remark + "') ";
                //CPNo清场计划编号No， CPVer清场计划版本A，ItemNo清场计划序号Item，CPTxt清场计划事项描述Name，Type清场计划数据类型Kind
            }
            else if (sFlag == "11-1")  // 修改清场计划项目信息
            {
                sSQL = " update T_ClearPlanDetail set CPTxt='" + Name + "',Type='" + Kind + "',Remark='" + Remark + "' where CPNo='" + No + "'  and  CPVer='" + A + "' and  ItemNo ='" + Item + "'";
            }
            else if (sFlag == "11-2")  //调整清场计划明细项的先后顺序
            {
                if (F == "up")   // 上移：先判断准备上移的这个工序的上一个ID是多少。
                {
                    sSt = " select top 1 CPNo,CPVer,ItemNo,SNO from T_ClearPlanDetail where CPNo='" + No + "' and  CPVer='" + A + "' and   CAST(SNO as int)  < '" + L + "' order by SNO desc ";
                    sdt = DBHelper.GetDataTable(sSt);
                    if (sdt.Rows.Count > 0)
                    {
                        sCPNo = sdt.Rows[0]["CPNo"].ToString();
                        sCPVer = sdt.Rows[0]["CPVer"].ToString();
                        sSNO = sdt.Rows[0]["SNO"].ToString();
                        sItemNo = sdt.Rows[0]["ItemNo"].ToString();
                    }
                    else
                    {
                        sCPNo = "";
                        sCPVer = "";
                        sSNO = "";
                        sItemNo = Item;
                    }
                }
                else  // 下移
                {
                    sSt = " select top 1 CPNo,CPVer,ItemNo,SNO from T_ClearPlanDetail where CPNo='" + No + "' and  CPVer='" + A + "' and   CAST(SNO as int)  > '" + L + "' order by SNO asc ";
                    sdt = DBHelper.GetDataTable(sSt);
                    if (sdt.Rows.Count > 0)
                    {
                        sCPNo = sdt.Rows[0]["CPNo"].ToString();
                        sCPVer = sdt.Rows[0]["CPVer"].ToString();
                        sSNO = sdt.Rows[0]["SNO"].ToString();
                        sItemNo = sdt.Rows[0]["ItemNo"].ToString();
                    }
                    else
                    {
                        sCPNo = "";
                        sCPVer = "";
                        sSNO = "";
                        sItemNo = Item;
                    }
                }

                sSQLs = " update T_ClearPlanDetail set SNO='" + L + "' where CPNo='" + sCPNo + "' and CPVer='" + sCPVer + "' and ItemNo ='" + sItemNo + "'";  // 更新上/ 下 一个工序
                sSQL = " update T_ClearPlanDetail set SNO='" + sSNO + "' where CPNo='" + sCPNo + "' and CPVer='" + A + "'   and ItemNo='" + Item + "'";  //更新本次工序的顺序

            }
            else if (sFlag == "12-1")  // 删除清场计划项目信息
            {
                sSQL = " delete T_ClearPlanDetail  where CPNo='" + No + "'  and  CPVer='" + A + "' and  ItemNo ='" + Item + "'";
            }
            else if (sFlag == "13")  // 新增仓库信息
            {
                sSQL = " insert into T_Warehouse(FWNo,FWName,WNo,WName,Kind,UseFlag,IsAutoSubmitAndAudit,DeptNo,DeptName,CompanyNo,InMan,Remark)" +
                      " values('" + No + "','" + Name + "','" + Item + "','" + B + "','" + A + "','是','" + F + "','" + C + "','" + D + "','" + sComp + "','" + InMan + "','" + Remark + "') ";
            }
            else if (sFlag == "14")  // 修改仓库信息
            {
                sSQL = " update T_Warehouse set FWName='" + Name + "',WName='" + B + "',Kind='" + A + "',IsAutoSubmitAndAudit='" + F + "',DeptNo='" + C + "',DeptName='" + D + "',Remark='" + Remark + "' where FWNo='" + No + "' and WNo='" + Item + "' ";
            }
            else if (sFlag == "15")  // 删除仓库信息
            {
                sSQL = " delete T_Warehouse  where FWNo='" + No + "' and WNo='" + Item + "' ";
            }
            else if (sFlag == "16")  // 新增清场记录信息
            {
                // No,  Name,  Item,      A,         B,       C,       D,     E,       F,        G,  H
                //CRNo,OrderNo,MaterNo,MaterName,MAterSpac,DealMan,DealDate,DeptName,GroupName,CPNo,CPVer
                sSQL = " insert into T_OrderClearRec (CRNo,OrderNo,MaterNo,MaterName,MAterSpec,DealMan,DealDate,DeptName,GroupName,Result,Status,CPNo,CPVer,ItemNo,CPTxt,Type,CompanyNo,InMan,Remark)" +
                       " select  '" + No + "', '" + Name + "', '" + Item + "', '" + A + "', '" + B + "', '" + C + "', '" + D + "', '" + E + "', '" + F + "','待处理','未执行',CPNo,CPVer,ItemNo,CPTxt,Type," +
                       "'" + sComp + "','" + InMan + "','" + Remark + "'  from T_ClearPlanDetail where CPNo ='" + G + "' and CPVer='" + H + "'";
            }
            else if (sFlag == "17")  // 修改清场记录信息
            {
                sSQL = " update T_OrderClearRec set Result='" + A + "',DealMan='" + InMan + "',DealDate=CONVERT(varchar(19),getdate(),120) where CRNo='" + No + "' and ItemNo='" + Item + "' ";
                DBHelper.ExecuteCommand(sSQL);
                sSQL = "";
                //sSQLs = " update T_OrderClearRec set Status='执行中' where CRNo='" + No + "'";
                sSQLs = "declare  @c  varchar(10) , @b  int ,  @a   int  ;select @a= COUNT(1)  from  T_OrderClearRec  where CRNo='" + No + "' ;" +
                    "select @b= count(1)  from T_OrderClearRec  where CRNo='" + No + "'  and result ='OK'\r\n\r\nif @a=@b \r\nbegin\r\n   set @c='已完成'\r\nend\r\nelse if  @a>0\r\nbegin\r\n  " +
                    "set  @c='执行中'\r\nend\r\nupdate T_OrderClearRec set Status=@c where CRNo='" + No + "' ";
            }
            else if (sFlag == "18")  // 删除清场记录
            {
                sSQL = " delete T_OrderClearRec  where CRNo='" + No + "'  ";
            }
            else if (sFlag == "19")  // 新增设备信息
            {
                //No: sDeviceNo, Name: sDeviceName, Item: sDeviceDesc, A: sDeviceSpec, B: sDeviceKind, C: sDeptNo, D: sDeptName, E: sStatus, F: sMaterNo,G: sInventoryCycle, H: sCheckReq, I: sMaintainReq, K: "", L: "", Kind: "", Remark: s
                sSQL = " insert into T_DeviceInfo (DeviceNo,DeviceName,DeviceDesc,DeviceSpec,DeviceKind,DeptNo,DeptName,Status,MaterNo,InventoryCycle,CheckReq,MaintainReq,UseDate,Remark,CompanyNo,InMan)" +
                       " values('" + No + "', '" + Name + "', '" + Item + "', '" + A + "', '" + B + "', '" + C + "', '" + D + "', '" + E + "', '" + F + "', '" + G + "','" + H + "', '" + I + "', '" + J + "'" +
                       ",'" + Remark + "','" + sComp + "','" + InMan + "' )";
            }
            else if (sFlag == "20")  // 修改设备信息
            {
                sSQL = " update T_DeviceInfo set DeviceName ='" + Name + "',DeviceDesc ='" + Item + "',DeviceSpec='" + A + "',DeviceKind ='" + B + "',DeptNo ='" + C + "'," +
                    "DeptName ='" + D + "',Status ='" + E + "',MaterNo ='" + F + "',InventoryCycle ='" + G + "',CheckReq ='" + H + "' ,MaintainReq ='" + I + "',UseDate='" + J + "',Remark ='" + Remark + "' where DeviceNo='" + No + "' ";
            }
            else if (sFlag == "21")  // 删除设备信息
            {
                sSQL = " delete T_DeviceInfo  where DeviceNo='" + No + "' ";
            }
            else if (sFlag == "22")  // 新增设备分类信息
            {
                //No: sMaterNo, Name: sMaterName, Item: sDeptNo, A: sDeptName, B: sDiviceKind, C: sCheckReq, D: sMaintainReq
                sSQL = " insert into T_DeviceClassHead (MaterNo,MaterName,DeptNo,DeptName,DeviceKind,CheckReq,MaintainReq,Remark,CompanyNo,InMan)" +
                       " values('" + No + "', '" + Name + "', '" + Item + "', '" + A + "', '" + B + "', '" + C + "', '" + D + "'" +
                       ",'" + Remark + "','" + sComp + "','" + InMan + "' )";
            }
            else if (sFlag == "23")  // 修改设备分类信息
            {
                //No: sMaterNo, Name: sMaterName, Item: sDeptNo, A: sDeptName, B: sDeviceKind, C: sCheckReq, D: sMaintainReq
                sSQL = " update T_DeviceClassHead set MaterName ='" + Name + "',DeptNo ='" + Item + "',DeptName='" + A + "',DeviceKind ='" + B + "',CheckReq ='" + C + "'," +
                    "MaintainReq ='" + D + "',Remark ='" + Remark + "' where MaterNo='" + No + "' ";
            }
            else if (sFlag == "24")  // 删除设备分类信息
            {
                sSQLs = " delete T_DeviceClassDetail  where  MaterNo='" + No + "' ";
                sSQL = " delete T_DeviceClassHead  where  MaterNo='" + No + "' ";

            }
            else if (sFlag == "22-1")  // 新增设备分类明细信息
            {
                //NO:sMaterNo, Name: sMaterName, Item: sDCItem, A: sChedkTxt, B: sType, C: sDeptNo, D: sDeptName, E: sKind
                sSQL = " insert into T_DeviceClassDetail (MaterNo,MaterName,DCItem,CheckTxt,Type,DeptNo,DeptName,Status,SNO,Kind,Remark,CompanyNo,InMan)" +
                       " values('" + No + "', '" + Name + "', '" + Item + "', '" + A + "', '" + B + "', '" + C + "', '" + D + "', '在用'," +
                       "'" + L + "','" + Kind + "','" + Remark + "','" + sComp + "','" + InMan + "' )";
            }
            else if (sFlag == "23-1")  // 修改设备分类明细信息
            {
                sSQL = " update T_DeviceClassDetail set MaterName ='" + Name + "',CheckTxt ='" + A + "',Type='" + B + "', " +
                    " DeptNo ='" + C + "',DeptName ='" + D + "'," + "Status ='" + E + "',SNO ='" + L + "',Remark ='" + Remark + "'" +
                    "  where MaterNo='" + No + "'  and DCItem ='" + Item + "' and Kind ='" + Kind + "'";
            }
            else if (sFlag == "23-2")  //调整清场计划明细项的先后顺序
            {
                if (F == "up")   // 上移：先判断准备上移的这个工序的上一个ID是多少。
                {
                    sSt = " select top 1 MaterNo,MaterName,DCItem,SNO from T_DeviceClassDetail where MaterNo='" + No + "' and    Kind ='" + Kind + "' and   CAST(SNO as int)  < '" + L + "' order by SNO desc ";
                    sdt = DBHelper.GetDataTable(sSt);
                    if (sdt.Rows.Count > 0)
                    {
                        sCPNo = sdt.Rows[0]["MaterNo"].ToString();
                        sCPVer = sdt.Rows[0]["MaterName"].ToString();
                        sSNO = sdt.Rows[0]["SNO"].ToString();
                        sItemNo = sdt.Rows[0]["DCItem"].ToString();
                    }
                    else
                    {
                        sCPNo = "";
                        sCPVer = "";
                        sSNO = "";
                        sItemNo = "";
                    }
                }
                else  // 下移
                {
                    sSt = " select top 1 MaterNo,MaterName,DCItem,SNO from T_DeviceClassDetail where MaterNo='" + No + "' and    Kind ='" + Kind + "' and   CAST(SNO as int) > '" + L + "' order by SNO desc";
                    sdt = DBHelper.GetDataTable(sSt);
                    if (sdt.Rows.Count > 0)
                    {
                        sCPNo = sdt.Rows[0]["MaterNo"].ToString();
                        sCPVer = sdt.Rows[0]["MaterName"].ToString();
                        sSNO = sdt.Rows[0]["SNO"].ToString();
                        sItemNo = sdt.Rows[0]["DCItem"].ToString();
                    }
                    else
                    {
                        sCPNo = "";
                        sCPVer = "";
                        sSNO = "";
                        sItemNo = "";
                    }
                }

                sSQLs = " update T_DeviceClassDetail set SNO='" + L + "' where MaterNo='" + No + "' and Kind='" + Kind + "' and DCItem ='" + sItemNo + "'";  // 更新上/ 下 一个工序
                sSQL = " update T_DeviceClassDetail set SNO='" + sSNO + "' where MaterNo='" + No + "' and Kind='" + Kind + "'   and DCItem='" + Item + "'";  //更新本次工序的顺序

            }
            else if (sFlag == "24-1")  // 删除设备分类明细信息
            {
                sSQL = " delete T_DeviceClassDetail  where MaterNo='" + No + "'  and DCItem ='" + Item + "' and Kind ='" + Kind + "'";
            }

            else if (sFlag == "25")  // 新增点检任务表头和明细
            {
                //No: sTNo, Name: sDeviceNo, Item: sMaterNo, A: sDeptName, B: sDiviceKind, C: sCheckReq, D: sMaintainReq
                sSQL = " insert into T_DeviceCheckTaskHead (TNo,DeviceNo,DeviceName,DeviceKind,DeviceDesc,CheckReq,Status,CheckMonth,MaterNo,DeptNo,DeptName,Remark,CompanyNo,InMan)" +
                       "  select '" + No + "', b.DeviceNo,DeviceName,a.DeviceKind,b.DeviceDesc,b.CheckReq,'待点检',CONVERT(varchar(7),getdate(),120),a.MaterNo,b.DeptNo,b.DeptName,'" + Remark + "','" + sComp + "','" + InMan + "'  " +
                       "  from  T_DeviceInfo b inner join T_DeviceClassHead a on a.MaterNo=b.MaterNo where   b.DeviceNO ='" + Name + "'";
                sSQLs = " insert into T_DeviceCheckTaskDetail (TNo,CItem,DeviceNo,CheckTxt,CheckResult,Status,Type,Remark,CompanyNo,InMan)" +
                    "  select '" + No + "', DCItem,DeviceNo,CheckTxt,'未点检','待点检',Type,'" + Remark + "','" + sComp + "','" + InMan + "' " +
                    "  from T_DeviceClassDetail  a   left join  T_DeviceInfo b  on a.MaterNo=b.MaterNo  where Kind ='Check' and DeviceNo ='" + Name + "'  and   a.MaterNo ='" + Item + "' ";
            }
            else if (sFlag == "26")  // 修改点检任务表头
            {
                //No: sMaterNo, Name: sMaterName, Item: sDeptNo, A: sDeptName, B: sDeviceKind, C: sCheckReq, D: sMaintainReq
                sSQL = " update T_DeviceCheckTaskHead set MaterName ='" + Name + "',DeptNo ='" + Item + "',DeptName='" + A + "',DeviceKind ='" + B + "',CheckReq ='" + C + "'," +
                    "MaintainReq ='" + D + "',Remark ='" + Remark + "' where TNo='" + No + "' ";
            }
            else if (sFlag == "26-1")  // 修改点检任务明细
            {

                sSQL = " update T_DeviceCheckTaskDetail set CheckResult='" + A + "' ,  Status='" + B + "' ,CheckMan='" + InMan + "'  ," +
                    " CheckDate = CONVERT(varchar(20),getdate(),120)  where TNo='" + No + "' and CItem='" + Item + "' ";
                DBHelper.ExecuteCommand(sSQL);
                sSQL = "";
                //sSQLs = " update T_OrderClearRec set Status='执行中' where TNo='" + No + "'";
                sSQLs = "declare  @c  varchar(10) , @b  int ,  @a   int  ;select @a= COUNT(1)  from  T_DeviceCheckTaskDetail  where TNo='" + No + "' ;" +
                    "select @b= count(1)  from T_DeviceCheckTaskDetail  where TNo='" + No + "'  and Status ='已点检'\r\n\r\nif @a=@b \r\nbegin\r\n   set @c='已完成'\r\nend\r\nelse if  @a>0\r\nbegin\r\n  " +
                    "set  @c='执行中'\r\nend\r\nupdate T_DeviceCheckTaskHead set Status=@c where TNo='" + No + "' ";

            }
            else if (sFlag == "27")  // 删除点检记录
            {
                sSQL = " delete T_DeviceCheckTaskHead  where  TNo='" + No + "' and CompanyNo ='" + sComp + "' ";
                sSQLs = " delete T_DeviceCheckTaskDetail  where  TNo='" + No + "'  and CompanyNo ='" + sComp + "' ";
            }
            else if (sFlag == "28")  // 新增保养任务表头和明细
            {
                //No: sTNo, Name: sDeviceNo, Item: sMaterNo, A: sDeptName, B: sDiviceKind, C: sCheckReq, D: sMaintainReq
                sSQL = " insert into T_DeviceMaintainTaskHead (TNo,DeviceNo,DeviceName,DeviceKind,DeviceDesc,MaintainReq,Status,MaintainMonth,MaterNo,DeptNo,DeptName,Remark,CompanyNo,InMan)" +
                       "  select '" + No + "', b.DeviceNo,DeviceName,a.DeviceKind,b.DeviceDesc,b.MaintainReq,'待保养',CONVERT(varchar(7),getdate(),120),a.MaterNo,b.DeptNo,b.DeptName,'" + Remark + "','" + sComp + "','" + InMan + "'  " +
                       "  from  T_DeviceInfo b inner join T_DeviceClassHead a on a.MaterNo=b.MaterNo where   b.DeviceNO ='" + Name + "'";
                sSQLs = " insert into T_DeviceMaintainTaskDetail (TNo,MItem,DeviceNo,MaintainTxt,MaintainResult,Status,Type,Remark,CompanyNo,InMan)" +
                    "  select '" + No + "', DCItem,DeviceNo,CheckTxt,'未保养','待保养',Type,'" + Remark + "','" + sComp + "','" + InMan + "' " +
                    "  from T_DeviceClassDetail  a   left join  T_DeviceInfo b  on a.MaterNo=b.MaterNo  where Kind ='Maintain' and DeviceNo ='" + Name + "'  and   a.MaterNo ='" + Item + "' ";
            }
            else if (sFlag == "29")  // 修改保养任务表头
            {
                //No: sMaterNo, Name: sMaterName, Item: sDeptNo, A: sDeptName, B: sDeviceKind, C: sMaintainReq, D: sMaintainReq
                sSQL = " update T_DeviceMaintainTaskHead set MaterName ='" + Name + "',DeptNo ='" + Item + "',DeptName='" + A + "',DeviceKind ='" + B + "',MaintainReq ='" + C + "'," +
                    "MaintainReq ='" + D + "',Remark ='" + Remark + "' where TNo='" + No + "' ";
            }
            else if (sFlag == "29-1")  // 修改保养任务明细
            {

                sSQL = " update T_DeviceMaintainTaskDetail set MaintainResult='" + A + "' ,  Status='" + B + "' ,MaintainMan='" + InMan + "'  ," +
                    " MaintainDate = CONVERT(varchar(20),getdate(),120) where TNo='" + No + "' and MItem='" + Item + "' ";
                DBHelper.ExecuteCommand(sSQL);
                sSQL = "";
                //sSQLs = " update T_OrderClearRec set Status='执行中' where TNo='" + No + "'";
                sSQLs = "declare  @c  varchar(10) , @b  int ,  @a   int  ;select @a= COUNT(1)  from  T_DeviceMaintainTaskDetail  where TNo='" + No + "' ;" +
                    "select @b= count(1)  from T_DeviceMaintainTaskDetail  where TNo='" + No + "'  and Status ='已保养'\r\n\r\nif @a=@b \r\nbegin\r\n   set @c='已完成'\r\nend\r\nelse if  @a>0\r\nbegin\r\n  " +
                    "set  @c='执行中'\r\nend\r\nupdate T_DeviceMaintainTaskHead set Status=@c where TNo='" + No + "' ";

            }
            else if (sFlag == "29-2")  // 删除保养记录
            {
                sSQL = " delete T_DeviceMaintainTaskHead  where  TNo='" + No + "' and CompanyNo ='" + sComp + "' ";
                sSQLs = " delete T_DeviceMaintainTaskDetail  where  TNo='" + No + "'  and CompanyNo ='" + sComp + "' ";
            }

            List<string> links = new List<string>();
            links.Add(sSQLs);  // 用于更新上移或下移顺序清场计划项的顺序
            links.Add(sSQL);


            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }


        /// <summary>
        /// 质量控制相关数据更新
        /// </summary>
        /// <param name="No"></param>
        /// <param name="Name"></param>
        /// <param name="Item"></param>
        /// <param name="A"></param>
        /// <param name="B"></param>
        /// <param name="C"></param>
        /// <param name="D"></param>
        /// <param name="E"></param>
        /// <param name="F"></param>
        /// <param name="G"></param>
        /// <param name="H"></param>
        /// <param name="I"></param>
        /// <param name="J"></param>
        /// <param name="K"></param>
        /// <param name="L"></param>
        /// <param name="Kind"></param>
        /// <param name="sComp"></param>
        /// <param name="InMan"></param>
        /// <param name="Remark"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string OPPrdAssistQCInfo(string No, string Name, string Item, string SNo, string A,
           string B, string C, string D, string E, string F, string G, string H, string I, string J, string K,
           string L, string M, string N, string O, string P, string Q, string R, string S, string T, string U, string V, string W,
           string X, string Kind, string sComp, string InMan, string Remark, string sFlag)
        {
            //No: sQPNo, Name: sQPVer, Item: txtItemNo, A: sQPSpec, B: txtTechNode, C: txtTechStep, D: txtTSource, E: txtPFMEA, F: txtDevice, I: txtIDNO, J: txtProduct, K: txtProcess, L: txtProcessTX,
            //M: txtMeasurement, N: txtSampleNum, O: txtSampleFreq, P: txtControlWay, Q: txtResponsePlan, R: txtThisValue, S: txtIncludeOne, T: txtToValue, U: txtIncludeTwo, V: txtRangeKind, W: txtDataType, X: txtStatus,

            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sstr = string.Empty;
            string sNum = string.Empty;
            string sSt = string.Empty;

            string sQPVer = string.Empty;
            string sQPNo = string.Empty;
            string sSNO = string.Empty;
            string sItemNo = string.Empty;
            DataTable sdt = new DataTable();



            if (sFlag == "30")  // 新增质量控制计划信息
            {
                sSQL = " insert into T_QAControlPlanHead (QPNo, Ver,Spec,CompanyNo,InMan,Remark) " +
                       " values('" + No + "','" + Name + "','" + A + "','" + sComp + "','" + InMan + "','" + Remark + "') ";
                //CPNo质量控制计划编号No， CPVer质量控制计划版本A，ItemNo质量控制计划序号Item，CPTxt质量控制计划事项描述Name，Type质量控制计划数据类型Kind
            }
            else if (sFlag == "31")  // 修改质量控制计划信息
            {
                sSQL = " update T_QAControlPlanHead set Remark='" + Remark + "' ,Spec='" + A + "'  where QPNo='" + No + "'  and  Ver='" + Name + "'";
            }
            else if (sFlag == "32")  // 删除质量控制计划信息
            {
                sSQLs = " delete T_QAControlPlanHead  where QPNo='" + No + "'  and  Ver='" + Name + "'";
                sSQL = " delete T_QAControlPlanDetail  where QPNo='" + No + "'  and  Ver='" + Name + "'";
            }
            else if (sFlag == "30-1")  // 新增质量控制计划项目信息
            {
                //No: sQPNo, Name: sQPVer, Item: txtItemNo, SNO: "", A: sQPSpec, B: txtTechNode, C: txtTechStep, D: txtTSource, E: txtPFMEA, F: txtDevice, G: txtIDNO, H: txtProduct, I: txtProcess, J: txtProcessTX, K: txtMeasurement, L: txtSampleNum,
                //M: txtSampleFreq, N: txtControlWay, O: txtResponsePlan, P: txtThisValue, Q: txtIncludeOne, R: txtToValue, S: txtIncludeTwo, T: txtRangeKind, U: txtDataType, V: "", W: "", X: "", Kind: "", Remark: sRemark, Flag: sFlag

                sSQL = " insert into T_QAControlPlanDetail (QPNo, Ver,ItemNo,SNO,Spec,TechNode,TechStep,TSource,PFMEA,Device,IDNO,Product,Process,ProcessTX," +
                    "Measurement,SampleNum,SampleFreq,ControlWay,ResponsePlan,ThisValue,IncludeOne,ToValue,IncludeTwo,RangeKind,DataType,Status,CompanyNo,InMan,Remark) " +
                       " values('" + No + "','" + Name + "','" + Item + "','" + SNo + "','" + A + "','" + B + "','" + C + "','" + D + "','" + E + "','" + F + "','" + G + "','" + H + "','" + I + "','" + J + "','" + K + "'," +
                       "'" + L + "','" + M + "','" + N + "','" + O + "','" + P + "','" + Q + "','" + R + "','" + S + "','" + T + "','" + U + "','在用','" + sComp + "','" + InMan + "','" + Remark + "') ";
                //CPNo质量控制计划编号No， CPVer质量控制计划版本A，ItemNo质量控制计划序号Item，CPTxt质量控制计划事项描述Name，Type质量控制计划数据类型Kind
                //sNo, It.Name, sItemNo,sSNO, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, It.K, It.L, It.M, It.N, It.O, It.P, It.Q, It.R, It.S, It.T, It.U, It.V, It.W, It.X, It.Kind
            }
            else if (sFlag == "30-2")  // 质量控制计划版本升级
            {
                //No: sQPNo, Name: sQPVer, Item: txtItemNo, SNO: "", A: sQPSpec, B: txtTechNode, C: txtTechStep, D: txtTSource, E: txtPFMEA, F: txtDevice, G: txtIDNO, H: txtProduct, I: txtProcess, J: txtProcessTX, K: txtMeasurement, L: txtSampleNum,
                //M: txtSampleFreq, N: txtControlWay, O: txtResponsePlan, P: txtThisValue, Q: txtIncludeOne, R: txtToValue, S: txtIncludeTwo, T: txtRangeKind, U: txtDataType, V: "", W: "", X: "", Kind: "", Remark: sRemark, Flag: sFlag
                sSQLs = " insert into T_QAControlPlanHead (QPNo, Ver,Spec,CompanyNo,InMan,Remark) " +
                       " select QPNo, '"+ Name + "','"+A+"',CompanyNo,'"+ InMan + "','"+ Remark + "' from T_QAControlPlanHead where QPNo='"+ No + "' and  Ver='"+Kind+"' ";
                sSQL = " insert into T_QAControlPlanDetail (QPNo, Ver,ItemNo,SNO,Spec,TechNode,TechStep,TSource,PFMEA,Device,IDNO,Product,Process,ProcessTX," +
                    " Measurement,SampleNum,SampleFreq,ControlWay,ResponsePlan,ThisValue,IncludeOne,ToValue,IncludeTwo,RangeKind,DataType,Status,CompanyNo,InMan,Remark) " +
                       " select QPNo,  '"+ Name + "',ItemNo,SNO,'"+A+"',TechNode,TechStep,TSource,PFMEA,Device,IDNO,Product,Process,ProcessTX," +
                       "  Measurement,SampleNum,SampleFreq,ControlWay,ResponsePlan,ThisValue,IncludeOne,ToValue,IncludeTwo,RangeKind,DataType,Status,CompanyNo,'"+ InMan + "',Remark  " +
                       " from T_QAControlPlanDetail  where QPNo='"+ No + "' and  Ver='"+Kind+"' ";
                //CPNo质量控制计划编号No， CPVer质量控制计划版本A，ItemNo质量控制计划序号Item，CPTxt质量控制计划事项描述Name，Type质量控制计划数据类型Kind
                //sNo, It.Name, sItemNo,sSNO, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, It.K, It.L, It.M, It.N, It.O, It.P, It.Q, It.R, It.S, It.T, It.U, It.V, It.W, It.X, It.Kind
            }
            else if (sFlag == "31-1")  // 修改质量控制计划项目信息
            {
                sSQL = " update T_QAControlPlanDetail set Spec='" + A + "',TechNode='" + B + "',TechStep='" + C + "',TSource='" + D + "',PFMEA='" + E + "',Device='" + F + "',IDNO='" + G + "',Product='" + H + "',Process='" + I + "',ProcessTX='" + J + "',Measurement='" + K + "'," +
                    "SampleNum='" + L + "',SampleFreq='" + M + "',ControlWay='" + N + "',ResponsePlan='" + O + "',ThisValue='" + P + "',IncludeOne='" + Q + "',ToValue='" + R + "',IncludeTwo='" + S + "',RangeKind='" + T + "',DataType='" + U + "',Remark='" + Remark + "' where QPNo='" + No + "'  and  Ver='" + Name + "' and  ItemNo ='" + Item + "'";
            }
            else if (sFlag == "31-2")  //调整质量控制计划明细项的先后顺序
            {
                if (F == "up")   // 上移：先判断准备上移的这个工序的上一个ID是多少。
                {
                    sSt = " select top 1 QPNo,Ver,ItemNo,SNO from T_QAControlPlanDetail where QPNo='" + No + "' and  Ver='" + Name + "' and   CAST(SNo as int)  < '" + SNo + "' order by SNO desc ";
                    sdt = DBHelper.GetDataTable(sSt);
                    if (sdt.Rows.Count > 0)
                    {
                        sQPNo = sdt.Rows[0]["QPNo"].ToString();
                        sQPVer = sdt.Rows[0]["Ver"].ToString();
                        sSNO = sdt.Rows[0]["SNO"].ToString();
                        sItemNo = sdt.Rows[0]["ItemNo"].ToString();
                    }
                    else
                    {
                        sQPNo = "";
                        sQPVer = "";
                        sSNO = "";
                        sItemNo = "";
                    }
                }
                else  // 下移
                {
                    sSt = " select top 1 QPNo,Ver,ItemNo,SNO from T_QAControlPlanDetail where QPNo='" + No + "' and  Ver='" + Name + "' and   CAST(SNo as int)   > '" + SNo + "' order by SNO asc ";
                    sdt = DBHelper.GetDataTable(sSt);
                    if (sdt.Rows.Count > 0)
                    {
                        sQPNo = sdt.Rows[0]["QPNo"].ToString();
                        sQPVer = sdt.Rows[0]["Ver"].ToString();
                        sSNO = sdt.Rows[0]["SNO"].ToString();
                        sItemNo = sdt.Rows[0]["ItemNo"].ToString();
                    }
                    else
                    {
                        sQPNo = "";
                        sQPVer = "";
                        sSNO = "";
                        sItemNo = "";
                    }
                }

                sSQLs = " update T_QAControlPlanDetail set SNo='" + SNo + "' where QPNo='" + sQPNo + "' and Ver='" + sQPVer + "' and ItemNo ='" + sItemNo + "'";  // 更新上/ 下 一个工序
                sSQL = " update T_QAControlPlanDetail set SNo='" + sSNO + "' where QPNo='" + sQPNo + "' and Ver='" + Name + "'   and ItemNo='" + Item + "'";  //更新本次工序的顺序

            }
            else if (sFlag == "32-1")  // 删除质量控制计划项目信息
            {
                sSQL = " delete T_QAControlPlanDetail  where QPNo='" + No + "'  and  Ver='" + Name + "' and  ItemNo ='" + Item + "'";
            }
            else if (sFlag == "33")  // 新增质量控制执行信息
            {
                sSQL = " insert into T_QAControlEXEHead (ENo,OrderNo,Spec,MaterNo,MaterName,QPNo,Ver,ItemNo,[TechNode],[TechStep] ,[TSource],[PFMEA],[Device],[IDNO],[Product],[Process],[ProcessTX]," +
                       " [Measurement],[SampleNum],[SampleFreq],[ControlWay],[ResponsePlan],[ThisValue],[IncludeOne],[ToValue],[IncludeTwo] ,[RangeKind],[DataType],[SNo],Status,ItemStatus,CompanyNo,InMan,Remark) " +
                       " select '" + No + "','" + A + "','" + B + "','" + C + "','" + D + "',QPNo,Ver,ItemNo,[TechNode],[TechStep] ,[TSource],[PFMEA],[Device],[IDNO],[Product],[Process],[ProcessTX],[Measurement],[SampleNum]," +
                       " [SampleFreq],[ControlWay],[ResponsePlan],[ThisValue],[IncludeOne],[ToValue],[IncludeTwo] ,[RangeKind],[DataType],[SNo],'未执行','未启动','" + sComp + "','" + InMan + "','" + Remark + "'  from  T_QAControlPlanDetail where QPNo ='" + Name + "'  and  ver='" + Item + "'";
                //CPNo质量控制计划编号No， CPVer质量控制计划版本A，ItemNo质量控制计划序号Item，CPTxt质量控制计划事项描述Name，Type质量控制计划数据类型Kind
            }
            else if (sFlag == "33-1")  // 新增质量控制执行结果
            {
                sSQL = " insert into T_QAControlEXEDetail ([ENo],[ItemNo],[SerialNo],[Times],[EXEValue],[EXEResult],[EXEMan],[CompanyNo],[InMan],[Remark]) " +
                       " values ( '" + No + "','" + Item + "','" + A + "','" + B + "','" + C + "','" + D + "','" + InMan + "','" + sComp + "','" + InMan + "','" + Remark + "')";
                sSQLs = "  update T_QAControlEXEHead set Status='执行中'  where ENo='" + No + "'    " +
                       "  update T_QAControlEXEHead set ItemStatus='执行中'  where ENo='" + No + "'  and  ItemNo='" + Item + "' ";

                //CPNo质量控制计划编号No， CPVer质量控制计划版本A，ItemNo质量控制计划序号Item，CPTxt质量控制计划事项描述Name，Type质量控制计划数据类型Kind
            }
            else if (sFlag == "34")  // 修改质量控制执行信息
            {
                sSQL = " update T_QAControlEXEHead set ItemStatus='已完成'  where ENo='" + No + "'  and  ItemNo='" + Item + "'";
                DBHelper.ExecuteCommand(sSQL);
                sSQL = "";
                sSQLs = " declare  @c  varchar(10) , @b  int ,  @a   int  ;\r\nselect @a= COUNT(1)  from  T_QAControlEXEHead  where ENo='" + No + "' ;" +
                    "  \r\nselect @b= count(1)  from T_QAControlEXEHead  where ENo='" + No + "'  and  ItemStatus ='已完成';\r\n\r\nif @a=@b \r\nbegin\r\n   set @c='已完成'\r\nend\r\nelse if  @a>0\r\nbegin\r\n  set  @c='执行中'\r\nend" +
                    "  \r\nupdate T_QAControlEXEHead set Status=@C where   ENo='" + No + "' ";
            }
            else if (sFlag == "34-1")  // 修改质量控制执行结果信息
            {
                sSQL = " update T_QAControlEXEDetail set SerialNo='" + A + "',Times='" + B + "',EXEValue='" + C + "',EXEResult='" + D + "',EXEMan='" + InMan + "',EXEDate= CONVERT(varchar(20),getdate(),120)  where ENo='" + No + "'  and  ItemNo='" + Item + "' and  SerialNo ='" + E + "'";
            }
            else if (sFlag == "35")  // 删除质量控制执行结果信息
            {
                sSQL = " delete T_QAControlEXEHead  where ENo='" + No + "' ";
                sSQLs = " delete T_QAControlEXEDetail  where ENo='" + No + "' ";
            }
            else if (sFlag == "35-1")  // 删除质量控制执行结果信息
            {
                sSQL = " delete T_QAControlEXEDetail  where ENo='" + No + "'  and  ItemNo='" + Item + "'  and  SerialNo ='" + A + "'";
            }

            else if (sFlag == "36")  // 新增IPQC巡查计划信息
            {
                sSQL = " insert into T_InspectPlanHead (IPNo, Ver,Type,CompanyNo,InMan,Remark) " +
                       " values('" + No + "','" + Name + "','" + A + "','" + sComp + "','" + InMan + "','" + Remark + "') ";
                //CPNoIPQC巡查计划编号No， CPVerIPQC巡查计划版本A，ItemNoIPQC巡查计划序号Item，CPTxtIPQC巡查计划事项描述Name，TypeIPQC巡查计划数据类型Kind
            }
            else if (sFlag == "36-2")  // 升级IPQC巡查计划信息
            {
                sSQLs = " insert into T_InspectPlanHead (IPNo, Ver,Type,CompanyNo,InMan,Remark) " +
                       " select  IPNo, '"+ Name + "','"+A+"',CompanyNo,'"+ InMan + "','"+ Remark + "' from  T_InspectPlanHead where  IPNo='"+ No + "'   and  Ver='"+Kind+"' ";
                sSQL = " insert into T_InspectPlanDetail (IPNo, Ver,ItemNo,SNO,Type,ItemName,CFPoint,BuildFloor,Range,Proof,Compliant,Cause,Status,CompanyNo,InMan,Remark) " +
                       " select  IPNo, '"+ Name + "',ItemNo,SNO,'"+A+"',ItemName,CFPoint,BuildFloor,Range,Proof,Compliant,Cause,Status,CompanyNo,InMan,Remark  " +
                       "  from T_InspectPlanDetail where   IPNo='" + No + "'   and  Ver='"+Kind+"' ";

            }
            else if (sFlag == "37")  // 修改IPQC巡查计划信息
            {
                sSQL = " update T_InspectPlanHead set Remark='" + Remark + "' ,Type='" + A + "'  where IPNo='" + No + "'  and  Ver='" + Name + "'";
            }
            else if (sFlag == "38")  // 删除IPQC巡查计划信息
            {
                sSQLs= " delete T_InspectPlanHead  where IPNo='" + No + "'  and  Ver='" + Name + "'";
                sSQL = " delete T_InspectPlanDetail  where IPNo='" + No + "'  and  Ver='" + Name + "'";
            }
            else if (sFlag == "36-1")  // 新增IPQC巡查计划项目信息
            {
                //No: sIPNo, Name: sIPVer, Item: txtItemNo, SNO: "", A: sIPType, B: txtItemName, C: txtCFPoint, D: txtBuildFloor, E: txtRange, F: txtProof, G: txtCompliant, H: txtCause, 
                sSQL = " insert into T_InspectPlanDetail (IPNo, Ver,ItemNo,SNO,Type,ItemName,CFPoint,BuildFloor,Range,Proof,Compliant,Cause,Status,CompanyNo,InMan,Remark) " +
                       " values('" + No + "','" + Name + "','" + Item + "','" + SNo + "','" + A + "','" + B + "','" + C + "','" + D + "','" + E + "','" + F + "','" + G + "','" + H + "','在用','" + sComp + "','" + InMan + "','" + Remark + "') ";
                //CPNoIPQC巡查计划编号No， CPVerIPQC巡查计划版本A，ItemNoIPQC巡查计划序号Item，CPTxtIPQC巡查计划事项描述Name，TypeIPQC巡查计划数据类型Kind
                //sNo, It.Name, sItemNo,sSNO, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, It.K, It.L, It.M, It.N, It.O, It.P, It.Q, It.R, It.S, It.T, It.U, It.V, It.W, It.X, It.Kind
            }
            else if (sFlag == "37-1")  // 修改IPQC巡查计划项目信息
            {
                sSQL = " update T_InspectPlanDetail set ItemName='" + B + "',CFPoint='" + C + "',BuildFloor='" + D + "',Range='" + E + "',Proof='" + F + "',Compliant='" + G + "',Cause='" + H + "',Remark='" + Remark + "' where IPNo='" + No + "'  and  Ver='" + Name + "' and  ItemNo ='" + Item + "'";
            }
            else if (sFlag == "37-2")  //调整IPQC巡查计划明细项的先后顺序
            {
                if (F == "up")   // 上移：先判断准备上移的这个工序的上一个ID是多少。
                {
                    sSt = " select top 1 IPNo,Ver,ItemNo,SNO from T_InspectPlanDetail where IPNo='" + No + "' and  Ver='" + Name + "' and   CAST(SNo as int)  < '" + SNo + "' order by SNO desc ";
                    sdt = DBHelper.GetDataTable(sSt);
                    if (sdt.Rows.Count > 0)
                    {
                        sQPNo = sdt.Rows[0]["IPNo"].ToString();
                        sQPVer = sdt.Rows[0]["Ver"].ToString();
                        sSNO = sdt.Rows[0]["SNO"].ToString();
                        sItemNo = sdt.Rows[0]["ItemNo"].ToString();
                    }
                    else
                    {
                        sQPNo = "";
                        sQPVer = "";
                        sSNO = "";
                        sItemNo = "";
                    }
                }
                else  // 下移
                {
                    sSt = " select top 1 IPNo,Ver,ItemNo,SNO from T_InspectPlanDetail where IPNo='" + No + "' and  Ver='" + Name + "' and   CAST(SNo as int)   > '" + SNo + "' order by SNO asc ";
                    sdt = DBHelper.GetDataTable(sSt);
                    if (sdt.Rows.Count > 0)
                    {
                        sQPNo = sdt.Rows[0]["IPNo"].ToString();
                        sQPVer = sdt.Rows[0]["Ver"].ToString();
                        sSNO = sdt.Rows[0]["SNO"].ToString();
                        sItemNo = sdt.Rows[0]["ItemNo"].ToString();
                    }
                    else
                    {
                        sQPNo = "";
                        sQPVer = "";
                        sSNO = "";
                        sItemNo = "";
                    }
                }

                sSQLs = " update T_InspectPlanDetail set SNo='" + SNo + "' where IPNo='" + sQPNo + "' and Ver='" + sQPVer + "' and ItemNo ='" + sItemNo + "'";  // 更新上/ 下 一个工序
                sSQL = " update T_InspectPlanDetail set SNo='" + sSNO + "' where IPNo='" + sQPNo + "' and Ver='" + Name + "'   and ItemNo='" + Item + "'";  //更新本次工序的顺序

            }
            else if (sFlag == "38-1")  // 删除IPQC巡查计划项目信息
            {
                sSQL = " delete T_InspectPlanDetail  where IPNo='" + No + "'  and  Ver='" + Name + "' and  ItemNo ='" + Item + "'";
            }
            else if (sFlag == "39")  // 新增巡查执行信息
            {
                sSQL = " insert into T_InspectEXE (ENo,Type,IPNo,Ver,ItemNo,ItemName,CFPoint,SNo,Status,Result,[CompanyNo],[InMan],[Remark])" +
                       " select '" + No + "',Type,IPNo,Ver,ItemNo,ItemName,CFPoint,SNo,'未执行','待确认','" + sComp + "','" + InMan + "','" + Remark + "'  from  T_InspectPlanDetail where IPNo ='" + Name + "'  and  Ver='" + Item + "'";
            }
            else if (sFlag == "40")  // 修改巡查执行信息
            {
                //No: txtItemENo, Name: "", Item: txtItemNo, SNO: "", A: txtItemName, B: txtItemBuildFloor, C: txtItemRange, D: txtItemProof, E: txtItemCompliant, F: txtItemCause,
                sSQL = " update T_InspectEXE set ItemName='" + A + "' ,BuildFloor='" + B + "',Range='" + C + "' ,Proof='" + D + "' ,Compliant='" + E + "' , Result='OK' ,Cause='" + F + "',CFPoint='" + G + "',EXEMan='" + InMan + "',EXEDate=CONVERT(varchar(20),getdate(),120)   where ENo='" + No + "'  and  ItemNo='" + Item + "'";
                DBHelper.ExecuteCommand(sSQL);
                sSQL = "";
                sSQLs = " declare  @c  varchar(10) , @b  int ,  @a   int  ;\r\nselect @a= COUNT(1)  from  T_InspectEXE  where ENo='" + No + "' ;" +
                    "  \r\nselect @b= count(1)  from T_InspectEXE  where ENo='" + No + "'  and  Result ='OK';\r\n\r\nif @a=@b \r\nbegin\r\n   set @c='已完成'\r\nend\r\nelse if  @a>0\r\nbegin\r\n  set  @c='执行中'\r\nend" +
                    "  \r\nupdate T_InspectEXE set Status=@C where   ENo='" + No + "' ";
                //sSQLs = " update T_InspectEXE set  Status='执行中'   where ENo='" + No + "' ";
            }
            else if (sFlag == "40-1")  // 修改巡查执行信息,已取消执行此方法
            {
                //No: txtItemENo, Name: "", Item: txtItemNo, SNO: "", A: txtItemName, B: txtItemBuildFloor, C: txtItemRange, D: txtItemProof, E: txtItemCompliant, F: txtItemCause,
                sSQL = " update T_InspectEXE set  Result='OK'   where ENo='" + No + "'  and  ItemNo='" + Item + "'";
                DBHelper.ExecuteCommand(sSQL);
                sSQL = "";
                sSQLs = " declare  @c  varchar(10) , @b  int ,  @a   int  ;\r\nselect @a= COUNT(1)  from  T_InspectEXE  where ENo='" + No + "' ;" +
                    "  \r\nselect @b= count(1)  from T_InspectEXE  where ENo='" + No + "'  and  Result ='OK';\r\n\r\nif @a=@b \r\nbegin\r\n   set @c='已完成'\r\nend\r\nelse if  @a>0\r\nbegin\r\n  set  @c='执行中'\r\nend" +
                    "  \r\nupdate T_InspectEXE set Status=@C where   ENo='" + No + "' ";
            }
            else if (sFlag == "41")  // 删除巡查执行信息
            {
                //No: txtItemENo, Name: "", Item: txtItemNo, SNO: "", A: txtItemName, B: txtItemBuildFloor, C: txtItemRange, D: txtItemProof, E: txtItemCompliant, F: txtItemCause,
                sSQL = " delete T_InspectEXE  where ENo='" + No + "' ";

            }
            else if (sFlag == "42")  // 新增试剂/质控品信息
            {
                // No: txtLotNo, Name: txtMaterNo, Item: txtMaterName,sNO:"", A: txtSpec, B: txtQty, C: txtStorageConditions, D: txtExpirationDate,
                sSQL = " insert into T_QCWarehouse (LotNo,MaterNo,MaterName,Spec,Qty,StorageConditions,ExpirationDate,[CompanyNo],[InMan],[Remark]) " +
                       " values( '" + No + "','" + Name + "','" + Item + "','" + A + "','" + B + "','" + C + "','" + D + "','" + sComp + "','" + InMan + "','" + Remark + "') ";
            }
            else if (sFlag == "43")  // 修改试剂/质控品信息
            {
                //No: txtItemENo, Name: "", Item: txtItemNo, SNO: "", A: txtItemName, B: txtItemBuildFloor, C: txtItemRange, D: txtItemProof, E: txtItemCompliant, F: txtItemCause,
                sSQL = " update T_QCWarehouse set MaterNo='" + Name + "',MaterName='" + Item + "',Spec='" + A + "' ,Qty='" + B + "',StorageConditions='" + C + "' ,ExpirationDate='" + D + "',Remark='" + Remark + "'   where LotNo='" + No + "' ";

            }
            else if (sFlag == "44")  // 删除试剂/质控品信息
            {
                //No: txtItemENo, Name: "", Item: txtItemNo, SNO: "", A: txtItemName, B: txtItemBuildFloor, C: txtItemRange, D: txtItemProof, E: txtItemCompliant, F: txtItemCause,
                sSQL = " delete T_QCWarehouse  where LotNo='" + No + "' ";

            }
            else if (sFlag == "45")  // 新增工艺文件信息
            {
                //No: txtFileNo, Name: txtFileName, Item: txtFileVer, sNO: "", A: txtUserArea, B: txtKind, C: txtStatus, D: txtLanguage, E: txtDOCName, F: txtFilePath, G: txtECN
                sSQL = " insert into T_ProcessFile (FileNo,FileName,FileVer,UserArea,Kind,Status,Language,DOCName,FilePath,ECN,[CompanyNo],[InMan],[Remark]) " +
                       " values( '" + No + "','" + Name + "','" + Item + "','" + A + "','" + B + "','在用','" + D + "','" + E + "','" + F + "','" + G + "','" + sComp + "','" + InMan + "','" + Remark + "') ";
            }
            else if (sFlag == "46")  // 修改工艺文件信息
            {

                sSQL = " update T_ProcessFile set FileName='" + Name + "',FileVer='" + Item + "',UserArea='" + A + "' ,Kind='" + B + "',Status='" + C + "' ,Language='" + D + "',DOCName='" + E + "',FilePath='" + F + "',ECN='" + G + "',Remark='" + Remark + "'   where FileNo='" + No + "' ";

            }
            else if (sFlag == "47")  // 删除艺文件信息
            {

                sSQL = " delete T_ProcessFile  where FileNo='" + No + "' ";

            }

            List<string> links = new List<string>();
            links.Add(sSQLs);  // 用于更新上移或下移顺序IPQC巡查计划项的顺序
            links.Add(sSQL);


            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }


        /// <summary>
        /// 返回仓库信息树形结构
        /// </summary>
        /// <param name="FNo"></param>
        /// <param name="FName"></param>
        /// <param name="CNo"></param>
        /// <param name="CName"></param>
        /// <param name="GX"></param>
        /// <param name="Row"></param>
        /// <param name="num"></param>
        /// <param name="Comp"></param>
        /// <returns></returns>
        public static DataTable GetWareHouseInfo(string FNo, string FName, string CNo, string CName, string GX, int Row, int num, string Comp)
        {
            DataTable sdt = new DataTable();

            SqlParameter[] parameters = {
                    new SqlParameter("@lnFNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnFName", SqlDbType.NVarChar,200),
                    new SqlParameter("@lnCNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnCName", SqlDbType.NVarChar,200),
                    new SqlParameter("@lnGX", SqlDbType.NVarChar,60),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnComp", SqlDbType.NVarChar,30),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
            parameters[0].Value = FNo;
            parameters[1].Value = FName;
            parameters[2].Value = CNo;
            parameters[3].Value = CName;
            parameters[4].Value = GX;
            parameters[5].Value = Row;
            parameters[6].Value = num;
            parameters[7].Value = Comp;
            parameters[8].Value = "";

            DataSet DS = DBHelper.RunProcedureForDS("p_CreateWareHouseForTree", parameters);
            sdt = DS.Tables[0];

            return sdt;
        }


        public static Dictionary<string, object> GenerateRecordFile(string No, string Name, string Item, string Status, string A, string B, string C, string D, string Remark, string sComp, string sInMan, string Flag)
        {
            Dictionary<string, object> dic = new Dictionary<string, object>();
            DataTable dt = new DataTable();

            SqlParameter[] parameters = {
                    new SqlParameter("@lnNo", SqlDbType.NVarChar,2000),
                    new SqlParameter("@lnItem", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnStatus", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnE", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnF", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,30),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)
                };
            parameters[0].Value = No;
            parameters[1].Value = "";
            parameters[2].Value = "";
            parameters[3].Value = "";
            parameters[4].Value = "";
            parameters[5].Value = "";
            parameters[6].Value = "";
            parameters[7].Value = "";
            parameters[8].Value = ""; // A
            parameters[9].Value = "";
            parameters[10].Value = "";
            parameters[11].Value = "";
            parameters[12].Value = "";
            parameters[13].Value = "";
            parameters[14].Value = 1;
            parameters[15].Value = 20;
            parameters[16].Value = sInMan;
            parameters[17].Value = Flag;
            parameters[18].Value = "";

            DataSet DS = DBHelper.RunProcedureForDS("P_GetDHRInfoForPage", parameters);
            foreach (DataTable item in DS.Tables)
            {
                dt = CheckData(item);
                dic.Add(item.TableName, dt);
            }
            return dic;
        }

        private static DataTable CheckData(DataTable dt)
        {
            foreach (DataRow row in dt.Rows)
            {
                foreach (DataColumn col in dt.Columns)
                {
                    var value = row[col].ToString().Trim();
                    // 判断当前列的值是否为空
                    if (value == null || string.IsNullOrEmpty(value))
                    {
                        // 如果为空值，替换为斜杠/
                        row[col] = "/";
                    }
                }
            }
            return dt;
        }
    }
}

