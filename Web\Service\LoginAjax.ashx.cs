﻿using System;
using System.Collections;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml.Linq;
using BLL;
using Newtonsoft.Json;
using System.Web.SessionState;
using Common;
using System.Collections.Generic;
using WXCommon;
using System.Text;
using System.IO;
using iTextSharp.xmp.impl.xpath;

namespace Web.Service
{
    /// <summary>
    /// $codebehindclassname$ 的摘要说明
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class Login : IHttpHandler, IRequiresSessionState
    {
        string sAMFlag = string.Empty;
        string sCCorpID = string.Empty;
        string IP = string.Empty;

        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "text/plain";
            string Result = string.Empty;
            string Operate = context.Request.Params["OP"];
            string DataParams = context.Request.Params["Data"];
            string sApplyNo = context.Request.Params["sApplyNo"];
            sAMFlag = context.Request.Params["CFlag"];
            sCCorpID = context.Request.Params["CorpID"];
            IP = context.Request.ServerVariables.Get("Remote_Addr").ToString().Trim();  //获取IP 地址

            switch (Operate) 
            {

                case "ClearSession": //清空 Session
                    Result = ClearSession(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "ApplyLogin":  // 自动登录用
                    Result = ApplyLogin(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "SDLogin":  // 手动登录
                    Result = SDLogin(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "JustRight": //判断是否有操作权限
                    Result = JustOperateRight(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "UserRegister": // 用户注册 -- 手动填写注册信息
                    Result = UserRegisterInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;




            }

        }


        #region 清空Session
        public string ClearSession(string Params)
        {
            string Result = string.Empty;
            string Message = "YES";

            HttpContext.Current.Session["LoginName"] = "";
            HttpContext.Current.Session["FullName"] = "";
            HttpContext.Current.Session["CompanyNo"] = "";


            Result = JsonConvert.SerializeObject(new { Msg = Message});  
            return Result;
        }
        #endregion



        #region 判断用户是否有操作权限
        public string JustOperateRight(string Params)
        {
            string Result = string.Empty;
            string Message = "YES";
            string sInMan = string.Empty;
            string sInName = string.Empty;
            //string sCOMPANY = System.Configuration.ConfigurationManager.AppSettings["COMPANY"];
            string sCOMPANY = string.Empty;
            string HttpUrl = System.Configuration.ConfigurationManager.AppSettings["HttpUrl"];
            string AppUrl = System.Configuration.ConfigurationManager.AppSettings["AppUrl"];

            HttpContext.Current.Session["AMFlag"] = sAMFlag;
            HttpContext.Current.Session["CCorpID"] = sCCorpID; //"wx9f1cde5f70b71497"; 

            //--@_@--   测试使用，发布到测试系统需要屏蔽 -- 不需要调用微信接口（调用微信接口需要用微信调用的，）
            //HttpContext.Current.Session["LoginName"] = "wudong";
            //HttpContext.Current.Session["FullName"] = "吴东";
            //HttpContext.Current.Session["CompanyNo"] = "C0001";
            //--@_@--

            if (string.IsNullOrEmpty(HttpContext.Current.Session["LoginName"].ToString()))// HttpContext.Current.Session["LoginName"] != null
            {
                Message = "loginError";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }
            else
            {
                sInMan = HttpContext.Current.Session["LoginName"].ToString();
                sInName = HttpContext.Current.Session["FullName"].ToString();
                sCOMPANY = HttpContext.Current.Session["CompanyNo"].ToString();

                string sNo = "判断当前登录人是否有操作权限：" + sInMan + "BZ:" + sAMFlag + "LoginName:" + HttpContext.Current.Session["LoginName"].ToString();
                string sSQL = " insert into T_Temp(MaterNo) values('" + sNo + "')";
                Common.DBHelper.ExecuteCommand(sSQL);
            }


            //string sNo = "判断当前登录人是否有操作权限：" + sInMan + "BZ:" + sAMFlag;
            //string sSQL = " insert into T_Temp(MaterNo) values('" + sNo + "')";
            //Common.DBHelper.ExecuteCommand(sSQL);


            if (sAMFlag == "1") // 店招申请
            {
                Message = LoginBll.JustOperateRight(sInMan, "DZSQ"); // DZSQ 这种编号是功能菜单的编号，固定的。
            }
            else if (sAMFlag == "2") // 店招主管审核（列表）
            {
                Message = LoginBll.JustOperateRight(sInMan, "DZZGSH");
            }
            else if (sAMFlag == "3") // 店招办事处审批（列表） 
            {
                Message = LoginBll.JustOperateRight(sInMan, "DZBSCSP");
            }
            else if (sAMFlag == "4") //  效果图业代审核
            {
                Message = LoginBll.JustOperateRight(sInMan, "XGTSH");
            }
            else if (sAMFlag == "5") // 店招验收
            {
                Message = LoginBll.JustOperateRight(sInMan, "DZYS");
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, Man = sInMan, InName = sInName, COMPANY = sCOMPANY, HttpUrl = HttpUrl, AppUrl = AppUrl });  //20160331：如果在前端的函数调用这个函数，需要这个语句
            return Result;
        }
        #endregion


        #region 自动登录逻辑
        public string ApplyLogin(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string ssCode = string.Empty;
            string sUserID = string.Empty;
            string sSQL = string.Empty;
            string sNo = string.Empty;
            string sFlag = string.Empty;
            string sSQLs = string.Empty;
            string sUpdate = string.Empty;
            string stokens = string.Empty;
            string iToken = string.Empty;
            int iFlag = 0;
            string CorpID = string.Empty;        // 那就是这里有问题。这种情况发生，那么在公众号不能设置多个组。
            string Secret = string.Empty;
            string toparty = string.Empty;
            string agentid = string.Empty;
            string sGZH = string.Empty;
            string sURI = string.Empty;
            string HttpUrl = System.Configuration.ConfigurationManager.AppSettings["HttpUrl"];

            HttpContext.Current.Session["LoginName"] = null;
            HttpContext.Current.Session["FullName"] = null;
            HttpContext.Current.Session["CompanyNo"] = null;
            HttpContext.Current.Session["WeiXinNo"] = null;
            HttpContext.Current.Session["QYNo"] = null;


            sNo = "》进入方法体;";     // 插入日志

            var AnonymousUser = new { User = String.Empty, Pwd = String.Empty, Code = String.Empty, State = String.Empty, sLFlag = String.Empty, Add = String.Empty };
            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            ssCode = Item.Code;

            WXCommon.TokenHelper token = new TokenHelper();

            try
            {   // wx9f1cde5f70b71497	PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a	1	1
                // 达能公众号	http://www.20gli.com/Cash_Phone/LoginAuto.htm


                if (HttpContext.Current.Session["CCorpID"] != null)  // HttpContext.Current.Session["LoginName"] != null
                {
                    CorpID = HttpContext.Current.Session["CCorpID"].ToString();   //"wx9f1cde5f70b71497";//Dt.Rows[0]["CorpID"].ToString(); 
                }

                if (string.IsNullOrEmpty(CorpID) || (CorpID == "null"))
                {
                    CorpID = "wx9f1cde5f70b71497";  // 默认是华南的 "wx9824d60e1f9ee4ab"; //
                }

                sNo = sNo + "》企业号ID:" + CorpID;     // 插入日志

                DataTable Dt = WeiXinInfo.GetCorpSecret(CorpID, "", "AT");  // 这里会取得多个记录，所以“Secret” 是随机的，如果无法自动登录（无法自动获得USERID），
                if (Dt.Rows.Count > 0)
                {
                    Secret = Dt.Rows[0]["Secret"].ToString();  //"PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a"    keqWvEOslySBpzkGyU7LYIerkVe95_GXMaWJ39lYHmxWyGvnqMCVUmPsI2Vj6CXq
                    toparty = "1";//Dt.Rows[0]["toparty"].ToString();  获取USERID暂时不用
                    agentid = "1";//Dt.Rows[0]["agentid"].ToString();  获取USERID暂时不用
                    sGZH = Dt.Rows[0]["GZHKind"].ToString();//"达能公众号";
                    sURI = Dt.Rows[0]["redirect_uri"].ToString();  // "http://www.20gli.com/Cash_Phone/LoginAuto.htm"; 

                    // -- 这个获取 token 代码和下面代码互斥
                    //WXCommon.TokenHelper token = new TokenHelper();
                    //stokens = token.GetTokenString("", CorpID, Secret, toparty, sGZH);
                    //iToken = WXCommon.TokenHelper.GetJsonValue(stokens, "access_token");


                    // -- 这个获取token代码和上面代码互斥
                    // 先判断 iToken 是否有效，如果有效，则直接获取即可。
                    try
                    {
                        iToken = LoginBll.GetExistToken(CorpID, "");
                    }
                    catch (Exception ex)
                    {
                        Message = "ex";    // 插入日志
                        string ss = ex.Message.Length > 1000 ? ex.Message.Substring(1, 1000) : ex.Message;
                        sNo = sNo + " 》取后台Token出错：" + ss;

                        iToken = "";
                    }

                    if (iToken == "")  // 如果不存在，则重新获取
                    {
                        stokens = token.GetTokenString("", CorpID, Secret, toparty, sGZH);
                        iToken = WXCommon.TokenHelper.GetJsonValue(stokens, "access_token");
                        iFlag = LoginBll.InsertUpdateToken(CorpID, Secret, iToken, "1");
                    }
                }
                else
                {
                    sNo = sNo + " 》根据企业号ID找到相关信息，CorpID：" + CorpID;
                    Message = "CorpIDErrg";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, });
                    return Result;
                }
            }
            catch (Exception ex)
            {
                Message = "ex";   // 插入日志
                string ss = ex.Message.Length > 1000 ? ex.Message.Substring(1, 1000) : ex.Message;
                sNo = sNo + " 》获取微信Token，或插入出错，" + iToken + " ;ex:" + ss;
            }

            // 插入日志
            string sTK = iToken.Length > 20 ? iToken.Substring(1, 20) : iToken;
            sNo = sNo + " 》准备获取Code，iToken：" + sTK + " ,Code: " + Item.Code; // 插入日志


            if (!string.IsNullOrEmpty(ssCode))
            {
                sUserID = token.GetCode(iToken, ssCode);  // 获取USERID

                if (!string.IsNullOrEmpty(sUserID))
                {
                    sNo = sNo + " 》获取USERID：" + sUserID;

                    try
                    {
                        DataTable dt = LoginBll.GetList(" LoginName = '" + sUserID + "' "); // 使用微信获取的USERID自动登录
                        if (dt.Rows.Count > 0)
                        {
                            Message = "Success";

                            sFlag = HttpContext.Current.Session["AMFlag"].ToString();

                            HttpContext.Current.Session["LoginName"] = dt.Rows[0]["LoginName"].ToString();
                            HttpContext.Current.Session["FullName"] = dt.Rows[0]["FullName"].ToString();
                            HttpContext.Current.Session["CompanyNo"] = dt.Rows[0]["CompanyNo"].ToString();
                            HttpContext.Current.Session["WeiXinNo"] = dt.Rows[0]["WeiXinNo"].ToString();
                            HttpContext.Current.Session["QYNo"] = dt.Rows[0]["QYNo"].ToString();

                            // 插入日志
                            sNo = sNo + " 》登录成功,USERID：" + sUserID + " ，姓名：" + dt.Rows[0]["FullName"].ToString() + ",标识：" + sFlag;
                        }
                        else
                        {
                            Message = "NoUser";
                            sNo = sNo + " 》用户不存在，NoUser1";

                            iFlag = LoginBll.InsertUpdateToken(CorpID, Secret, iToken, "2");

                            //WXSendMessage.SentNewsToWeiXin("wudong001|wudong", "系统登录失败", "系统登录异常", "", CorpID, Secret, toparty, agentid, "G", "吴东公众号"); // 以新闻形式发，可以发链接的
                            WXSendMessage.SentNewsToWeiXin("wd", "系统登录失败", "系统登录异常，用户：" + sUserID, "", CorpID, Secret, toparty, agentid, "G", "达能公众号"); // 以新闻形式发，可以发链接的
                        }
                    }
                    catch (Exception ex)
                    {
                        Message = "ex";  // 插入日志
                        string ss = ex.Message.Length > 1000 ? ex.Message.Substring(1, 1000) : ex.Message;
                        sNo = sNo + " 》无法获取用户，NoUser2" + " ;ex:" + ss;

                        WXSendMessage.SentNewsToWeiXin("wd", "系统登录失败", "获取不到USERID，用户：" + sUserID, "", CorpID, Secret, toparty, agentid, "G", "达能公众号");
                    }

                }
                else  // 获取不到用户id
                {
                    Message = "NoUser";
                    sNo = sNo + " 》获取不到USERID，NoUser3"; // 插入日志
                }
            }
            else
            {
                Message = "Success";  //  是为了进入自动获取登录账号
                //sNo = sNo + " 》无法获取Code，NoCode";  // 插入日志
            }
            //执行： 插入日志
            sSQL = " insert into T_Temp(MaterNo) values('" + sNo + "')";
            Common.DBHelper.ExecuteCommand(sSQL);


            Result = JsonConvert.SerializeObject(new { Msg = Message, CorpID = CorpID, URI = sURI, tokens = iToken, UserID = sUserID, Flag = sFlag, Update = sUpdate });
            return Result;
        }
        #endregion



        #region 手动登录逻辑
        public string SDLogin(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sPwd = string.Empty;
            string sFlag = string.Empty;
            string sNo = string.Empty;
            string sSQL = string.Empty;
            string sEndDate = string.Empty;
            string sStatus = string.Empty;
            string sComp=string.Empty;



            HttpContext.Current.Session["LoginName"] = null;
            HttpContext.Current.Session["FullName"] = null;
            HttpContext.Current.Session["CompanyNo"] = null;
            HttpContext.Current.Session["WeiXinNo"] = null;
            HttpContext.Current.Session["QYNo"] = null;


            var AnonymousUser = new { User = String.Empty, Pwd = String.Empty };
            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            sLogin = Item.User.Trim();
            sPwd = Item.Pwd.Trim();

            if (!string.IsNullOrEmpty(sLogin))
            {
                /*登录逻辑*/
                DataTable dt = LoginBll.SDLogin(sLogin, sPwd);

                //if (!string.IsNullOrEmpty(sPwd)) // 说明是手动输入账号密码登录
                //{
                //    dt = LoginBll.SDLogin(sLogin, sPwd); // 使用微信获取的USERID自动登录
                //}
                //else  // 说明是系统自动登录时出现异常，用户输入账号登录，不需要输入秘密
                //{
                //    dt = LoginBll.GetList(" LoginName = '" + sLogin + "' "); // 使用微信获取的USERID自动登录
                //}
                
                if (dt.Rows.Count > 0)
                {
                    Message = "Success";

                    HttpContext.Current.Session["LoginName"] = dt.Rows[0]["LoginName"].ToString();
                    HttpContext.Current.Session["FullName"] = dt.Rows[0]["FullName"].ToString();
                    HttpContext.Current.Session["CompanyNo"] = dt.Rows[0]["CompanyNo"].ToString();
                    HttpContext.Current.Session["WeiXinNo"] = dt.Rows[0]["WeiXinNo"].ToString();
                    HttpContext.Current.Session["QYNo"] = dt.Rows[0]["QYNo"].ToString();
                    sEndDate = dt.Rows[0]["ExpireTime"].ToString();
                    sStatus = dt.Rows[0]["Status"].ToString();
                    sComp = dt.Rows[0]["CompanyNo"].ToString();


                    LogHelper.LogInfo("登录", "登录成功!", IP, sComp, sLogin, "登录"); //因为模块和菜单存放在一个字段 需要使用 
                    // 插入日志
                    //sNo = "登录6： 登录用户：" + dt.Rows[0]["FullName"].ToString() + "，" + "#02 " + sLogin + "BZ:" + sFlag;
                    //sSQL = " insert into T_Temp(MaterNo) values('" + sNo + "')";
                    //Common.DBHelper.ExecuteCommand(sSQL);
                }
                else  // 获取不到用户id
                {
                    Message = "NoUser";
                }
            }
            // 插入日志
            //sNo = "登录7： sLogin：" + sLogin + " ；Message:" + Message;
            //sSQL = " insert into T_Temp(MaterNo) values('" + sNo + "')";
            //Common.DBHelper.ExecuteCommand(sSQL);


            Result = JsonConvert.SerializeObject(new { Msg = Message, UserID = sLogin, Flag = "", Status = sStatus, EndDate = sEndDate });
            return Result;
        }
        #endregion



        #region 清空登录信息
        public string CleanLogin(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            var AnonymousUser = new { User = String.Empty, Pwd = String.Empty };

            var Item2 = JsonConvert.DeserializeObject(Params);

            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            HttpContext.Current.Session["LoginName"] = null;
            HttpContext.Current.Session["FullName"] = null;
            HttpContext.Current.Session["CompanyNo"] = null;
            HttpContext.Current.Session["WeiXinNo"] = null;

            Result = JsonConvert.SerializeObject(new { Msg = Message });
            return Result;
        }
        #endregion





        #region 用户注册  --  手动填写信息
        public string UserRegisterInfo(string Params)
        {
            string flag = string.Empty;
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sBStr = string.Empty;
            string sCode = string.Empty;
            string sComp = string.Empty;
            string sDatetime = string.Empty;


            var AnonymousUser = new
            {
                Phone = String.Empty,
                WX = String.Empty,
                Comp = String.Empty,
                CMan = String.Empty,
                HY = String.Empty,
                Remark = String.Empty,
                Code = String.Empty,
                Kind = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            sCode = Item.Code; //
            if (sCode == "wx6211cce6c5e6197d")  //  宋工的公司：深圳纤草科技有限公司
            {
                sComp = "C0043";
            }
            else
            {
                sComp = "C0038";
            }

            DataTable dt = LoginBll.GetList(" Phone = '" + Item.Phone + "'  ");
            if (dt.Rows.Count > 0)
            {
                Message = "CZ";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Phone = Item.Phone });
                return Result;
            }


            Random rad = new Random();//实例化随机数产生器rad；
            int value = rad.Next(100, 999);//用rad生成大于等于100，小于等于999的随机数；
            string sR = value.ToString(); //转化为字符串；

            sDatetime = DateTime.Now.Year.ToString() + DateTime.Now.Month.ToString() + DateTime.Now.Day.ToString() + DateTime.Now.Hour.ToString();
            sLogin = sDatetime + sR;



            flag = LoginBll.InsertUserInfo(Item.Phone, Item.WX, Item.Comp, sLogin, Item.CMan, Item.HY, Item.Remark, sCode, Item.Kind, sComp);


            if (flag.Length <= 2)
            {
                Message = "Success";



                // 发微信信息给相关人。 @all
                try
                {
                    DataTable Dt = WeiXinInfo.GetCorpSecret("", "JXCGL", "");  // 吴东公众号  达能公众号  // 吴东公众号  达能公众号 -- WDCX:旺点促销管理
                    if (Dt.Rows.Count > 0)
                    {
                        string CorpID = Dt.Rows[0]["CorpID"].ToString();
                        string Secret = Dt.Rows[0]["Secret"].ToString();
                        string toparty = Dt.Rows[0]["toparty"].ToString();
                        string agentid = Dt.Rows[0]["agentid"].ToString();
                        string sGZH = Dt.Rows[0]["GZHKind"].ToString();


                        //// 下面代码为添加一个账号到公众号  // 给微信公众号增加成员
                        //var data = new
                        //{
                        //    userid = sLogin,
                        //    name = Item.CMan,
                        //    department = toparty,
                        //    position = Item.SF,
                        //    mobile = Item.Phone,
                        //    gender = "1",
                        //    tel = "",
                        //    email = "",
                        //    weixinid = ""
                        //};
                        ////var url = string.Format(urlFormat, accessToken);
                        //var postData = JsonConvert.SerializeObject(data);
                        ////return Helper.GetCorpExecuteResult(url, postData);  
                        //sBStr = WXSendMessage.AddDepartmentUser(CorpID, Secret, agentid, toparty, sComp, postData);
                        //// 上面代码为添加一个账号到公众号


                        //发送信息
                        string sSentMan = "wudong";//FlowerBll.GetWeiXinNo(Item.Login, sComp, "1");  // 找到主管企业号对应账号。
                        string sMsg = "有新的客户注册，请关注：" + "\n" + "公司名称：" + Item.Comp + "\n" + "联  系  人：" + Item.CMan + "\n" + "电        话：" + Item.Phone + "\n" + "行        业：" + Item.HY + "\n" + "备注说明：" + Item.Remark;
                        string sUrl = "";
                        WXSendMessage.SentNewsToWeiXin(sSentMan, "新用户注册提醒", sMsg, sUrl, CorpID, Secret, toparty, agentid, "G", sGZH); // 以新闻形式发，可以发链接的


                    }

                }
                catch
                {
                    Message = "SentError";
                    Result = JsonConvert.SerializeObject(new { Msg = Message }); // CLNo 主要是ChenLieList文件用
                }
            }
            else
            {
                Message = "Error";
            }


            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion












        #region 测试生成一个新的编号
        public static string CreateNewBillNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_ShopSignsApplyInfo where convert(char(10),InDate,120)='" + sDate + "' ", "ApplyNo");// DZ160116 00003
            if (sMaxNo == "")
            {
                sNo = "DZ" + CreateAllNo.CreateBillNo(2, 4) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(8, 5);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 5 - len;

                sNo = "DZ" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion






        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}
