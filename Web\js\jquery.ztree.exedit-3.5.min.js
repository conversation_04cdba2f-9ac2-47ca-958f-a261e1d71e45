/*
 * JQuery zTree exedit 3.5.14
 * http://zTree.me/
 *
 * Copyright (c) 2010 Hunter.z
 *
 * Licensed same as jquery - MIT License
 * http://www.opensource.org/licenses/mit-license.php
 *
 * email: <EMAIL>
 * Date: 2013-06-28
 */
(function(u){var H={event:{DRAG:"ztree_drag",DROP:"ztree_drop",REMOVE:"ztree_remove",RENAME:"ztree_rename"},id:{EDIT:"_edit",INPUT:"_input",REMOVE:"_remove"},move:{TYPE_INNER:"inner",TYPE_PREV:"prev",TYPE_NEXT:"next"},node:{CURSELECTED_EDIT:"curSelectedNode_Edit",TMPTARGET_TREE:"tmpTargetzTree",TMPTARGET_NODE:"tmpTargetNode"}},x={onHoverOverNode:function(b,a){var c=m.getSetting(b.data.treeId),d=m.getRoot(c);if(d.curHoverNode!=a)x.onHoverOutNode(b);d.curHoverNode=a;f.addHoverDom(c,a)},onHoverOutNode:function(b){var b=
m.getSetting(b.data.treeId),a=m.getRoot(b);if(a.curHoverNode&&!m.isSelectedNode(b,a.curHoverNode))f.removeTreeDom(b,a.curHoverNode),a.curHoverNode=null},onMousedownNode:function(b,a){function c(b){if(A.dragFlag==0&&Math.abs(M-b.clientX)<e.edit.drag.minMoveSize&&Math.abs(N-b.clientY)<e.edit.drag.minMoveSize)return!0;var a,c,g,j,k;k=e.data.key.children;L.css("cursor","pointer");if(A.dragFlag==0){if(h.apply(e.callback.beforeDrag,[e.treeId,l],!0)==!1)return n(b),!0;for(a=0,c=l.length;a<c;a++){if(a==0)A.dragNodeShowBefore=
[];g=l[a];g.isParent&&g.open?(f.expandCollapseNode(e,g,!g.open),A.dragNodeShowBefore[g.tId]=!0):A.dragNodeShowBefore[g.tId]=!1}A.dragFlag=1;t.showHoverDom=!1;h.showIfameMask(e,!0);g=!0;j=-1;if(l.length>1){var s=l[0].parentTId?l[0].getParentNode()[k]:m.getNodes(e);k=[];for(a=0,c=s.length;a<c;a++)if(A.dragNodeShowBefore[s[a].tId]!==void 0&&(g&&j>-1&&j+1!==a&&(g=!1),k.push(s[a]),j=a),l.length===k.length){l=k;break}}g&&(G=l[0].getPreNode(),Q=l[l.length-1].getNextNode());C=o("<ul class='zTreeDragUL'></ul>",
e);for(a=0,c=l.length;a<c;a++)if(g=l[a],g.editNameFlag=!1,f.selectNode(e,g,a>0),f.removeTreeDom(e,g),j=o("<li id='"+g.tId+"_tmp'></li>",e),j.append(o(g,d.id.A,e).clone()),j.css("padding","0"),j.children("#"+g.tId+d.id.A).removeClass(d.node.CURSELECTED),C.append(j),a==e.edit.drag.maxShowNodeNum-1){j=o("<li id='"+g.tId+"_moretmp'><a>  ...  </a></li>",e);C.append(j);break}C.attr("id",l[0].tId+d.id.UL+"_tmp");C.addClass(e.treeObj.attr("class"));C.appendTo(L);z=o("<span class='tmpzTreeMove_arrow'></span>",
e);z.attr("id","zTreeMove_arrow_tmp");z.appendTo(L);e.treeObj.trigger(d.event.DRAG,[b,e.treeId,l])}if(A.dragFlag==1){r&&z.attr("id")==b.target.id&&v&&b.clientX+E.scrollLeft()+2>u("#"+v+d.id.A,r).offset().left?(g=u("#"+v+d.id.A,r),b.target=g.length>0?g.get(0):b.target):r&&(r.removeClass(d.node.TMPTARGET_TREE),v&&u("#"+v+d.id.A,r).removeClass(d.node.TMPTARGET_NODE+"_"+d.move.TYPE_PREV).removeClass(d.node.TMPTARGET_NODE+"_"+H.move.TYPE_NEXT).removeClass(d.node.TMPTARGET_NODE+"_"+H.move.TYPE_INNER));
v=r=null;I=!1;i=e;g=m.getSettings();for(var D in g)if(g[D].treeId&&g[D].edit.enable&&g[D].treeId!=e.treeId&&(b.target.id==g[D].treeId||u(b.target).parents("#"+g[D].treeId).length>0))I=!0,i=g[D];D=E.scrollTop();j=E.scrollLeft();k=i.treeObj.offset();a=i.treeObj.get(0).scrollHeight;g=i.treeObj.get(0).scrollWidth;c=b.clientY+D-k.top;var p=i.treeObj.height()+k.top-b.clientY-D,q=b.clientX+j-k.left,x=i.treeObj.width()+k.left-b.clientX-j;k=c<e.edit.drag.borderMax&&c>e.edit.drag.borderMin;var s=p<e.edit.drag.borderMax&&
p>e.edit.drag.borderMin,J=q<e.edit.drag.borderMax&&q>e.edit.drag.borderMin,F=x<e.edit.drag.borderMax&&x>e.edit.drag.borderMin,p=c>e.edit.drag.borderMin&&p>e.edit.drag.borderMin&&q>e.edit.drag.borderMin&&x>e.edit.drag.borderMin,q=k&&i.treeObj.scrollTop()<=0,x=s&&i.treeObj.scrollTop()+i.treeObj.height()+10>=a,O=J&&i.treeObj.scrollLeft()<=0,P=F&&i.treeObj.scrollLeft()+i.treeObj.width()+10>=g;if(b.target.id&&i.treeObj.find("#"+b.target.id).length>0){for(var B=b.target;B&&B.tagName&&!h.eqs(B.tagName,"li")&&
B.id!=i.treeId;)B=B.parentNode;var R=!0;for(a=0,c=l.length;a<c;a++)if(g=l[a],B.id===g.tId){R=!1;break}else if(o(g,e).find("#"+B.id).length>0){R=!1;break}if(R&&b.target.id&&(b.target.id==B.id+d.id.A||u(b.target).parents("#"+B.id+d.id.A).length>0))r=u(B),v=B.id}g=l[0];if(p&&(b.target.id==i.treeId||u(b.target).parents("#"+i.treeId).length>0)){if(!r&&(b.target.id==i.treeId||q||x||O||P)&&(I||!I&&g.parentTId))r=i.treeObj;k?i.treeObj.scrollTop(i.treeObj.scrollTop()-10):s&&i.treeObj.scrollTop(i.treeObj.scrollTop()+
10);J?i.treeObj.scrollLeft(i.treeObj.scrollLeft()-10):F&&i.treeObj.scrollLeft(i.treeObj.scrollLeft()+10);r&&r!=i.treeObj&&r.offset().left<i.treeObj.offset().left&&i.treeObj.scrollLeft(i.treeObj.scrollLeft()+r.offset().left-i.treeObj.offset().left)}C.css({top:b.clientY+D+3+"px",left:b.clientX+j+3+"px"});k=a=0;if(r&&r.attr("id")!=i.treeId){var y=v==null?null:m.getNodeCache(i,v);c=b.ctrlKey&&e.edit.drag.isMove&&e.edit.drag.isCopy||!e.edit.drag.isMove&&e.edit.drag.isCopy;a=!!(G&&v===G.tId);k=!!(Q&&v===
Q.tId);j=g.parentTId&&g.parentTId==v;g=(c||!k)&&h.apply(i.edit.drag.prev,[i.treeId,l,y],!!i.edit.drag.prev);a=(c||!a)&&h.apply(i.edit.drag.next,[i.treeId,l,y],!!i.edit.drag.next);F=(c||!j)&&!(i.data.keep.leaf&&!y.isParent)&&h.apply(i.edit.drag.inner,[i.treeId,l,y],!!i.edit.drag.inner);if(!g&&!a&&!F){if(r=null,v="",w=d.move.TYPE_INNER,z.css({display:"none"}),window.zTreeMoveTimer)clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null}else{c=u("#"+v+d.id.A,r);k=y.isLastNode?null:u("#"+
y.getNextNode().tId+d.id.A,r.next());s=c.offset().top;j=c.offset().left;J=g?F?0.25:a?0.5:1:-1;F=a?F?0.75:g?0.5:0:-1;b=(b.clientY+D-s)/c.height();(J==1||b<=J&&b>=-0.2)&&g?(a=1-z.width(),k=s-z.height()/2,w=d.move.TYPE_PREV):(F==0||b>=F&&b<=1.2)&&a?(a=1-z.width(),k=k==null||y.isParent&&y.open?s+c.height()-z.height()/2:k.offset().top-z.height()/2,w=d.move.TYPE_NEXT):(a=5-z.width(),k=s,w=d.move.TYPE_INNER);z.css({display:"block",top:k+"px",left:j+a+"px"});c.addClass(d.node.TMPTARGET_NODE+"_"+w);if(S!=
v||T!=w)K=(new Date).getTime();if(y&&y.isParent&&w==d.move.TYPE_INNER&&(b=!0,window.zTreeMoveTimer&&window.zTreeMoveTargetNodeTId!==y.tId?(clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null):window.zTreeMoveTimer&&window.zTreeMoveTargetNodeTId===y.tId&&(b=!1),b))window.zTreeMoveTimer=setTimeout(function(){w==d.move.TYPE_INNER&&y&&y.isParent&&!y.open&&(new Date).getTime()-K>i.edit.drag.autoOpenTime&&h.apply(i.callback.beforeDragOpen,[i.treeId,y],!0)&&(f.switchNode(i,y),i.edit.drag.autoExpandTrigger&&
i.treeObj.trigger(d.event.EXPAND,[i.treeId,y]))},i.edit.drag.autoOpenTime+50),window.zTreeMoveTargetNodeTId=y.tId}}else if(w=d.move.TYPE_INNER,r&&h.apply(i.edit.drag.inner,[i.treeId,l,null],!!i.edit.drag.inner)?r.addClass(d.node.TMPTARGET_TREE):r=null,z.css({display:"none"}),window.zTreeMoveTimer)clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null;S=v;T=w}return!1}function n(b){if(window.zTreeMoveTimer)clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null;T=S=null;
E.unbind("mousemove",c);E.unbind("mouseup",n);E.unbind("selectstart",g);L.css("cursor","auto");r&&(r.removeClass(d.node.TMPTARGET_TREE),v&&u("#"+v+d.id.A,r).removeClass(d.node.TMPTARGET_NODE+"_"+d.move.TYPE_PREV).removeClass(d.node.TMPTARGET_NODE+"_"+H.move.TYPE_NEXT).removeClass(d.node.TMPTARGET_NODE+"_"+H.move.TYPE_INNER));h.showIfameMask(e,!1);t.showHoverDom=!0;if(A.dragFlag!=0){A.dragFlag=0;var a,k,j;for(a=0,k=l.length;a<k;a++)j=l[a],j.isParent&&A.dragNodeShowBefore[j.tId]&&!j.open&&(f.expandCollapseNode(e,
j,!j.open),delete A.dragNodeShowBefore[j.tId]);C&&C.remove();z&&z.remove();var p=b.ctrlKey&&e.edit.drag.isMove&&e.edit.drag.isCopy||!e.edit.drag.isMove&&e.edit.drag.isCopy;!p&&r&&v&&l[0].parentTId&&v==l[0].parentTId&&w==d.move.TYPE_INNER&&(r=null);if(r){var q=v==null?null:m.getNodeCache(i,v);if(h.apply(e.callback.beforeDrop,[i.treeId,l,q,w,p],!0)==!1)f.selectNodes(x,l);else{var s=p?h.clone(l):l;a=function(){if(I){if(!p)for(var a=0,c=l.length;a<c;a++)f.removeNode(e,l[a]);if(w==d.move.TYPE_INNER)f.addNodes(i,
q,s);else if(f.addNodes(i,q.getParentNode(),s),w==d.move.TYPE_PREV)for(a=0,c=s.length;a<c;a++)f.moveNode(i,q,s[a],w,!1);else for(a=-1,c=s.length-1;a<c;c--)f.moveNode(i,q,s[c],w,!1)}else if(p&&w==d.move.TYPE_INNER)f.addNodes(i,q,s);else if(p&&f.addNodes(i,q.getParentNode(),s),w!=d.move.TYPE_NEXT)for(a=0,c=s.length;a<c;a++)f.moveNode(i,q,s[a],w,!1);else for(a=-1,c=s.length-1;a<c;c--)f.moveNode(i,q,s[c],w,!1);f.selectNodes(i,s);o(s[0],e).focus().blur();e.treeObj.trigger(d.event.DROP,[b,i.treeId,s,q,
w,p])};w==d.move.TYPE_INNER&&h.canAsync(i,q)?f.asyncNode(i,q,!1,a):a()}}else f.selectNodes(x,l),e.treeObj.trigger(d.event.DROP,[b,e.treeId,l,null,null,null])}}function g(){return!1}var k,j,e=m.getSetting(b.data.treeId),A=m.getRoot(e),t=m.getRoots();if(b.button==2||!e.edit.enable||!e.edit.drag.isCopy&&!e.edit.drag.isMove)return!0;var p=b.target,q=m.getRoot(e).curSelectedList,l=[];if(m.isSelectedNode(e,a))for(k=0,j=q.length;k<j;k++){if(q[k].editNameFlag&&h.eqs(p.tagName,"input")&&p.getAttribute("treeNode"+
d.id.INPUT)!==null)return!0;l.push(q[k]);if(l[0].parentTId!==q[k].parentTId){l=[a];break}}else l=[a];f.editNodeBlur=!0;f.cancelCurEditNode(e);var E=u(e.treeObj.get(0).ownerDocument),L=u(e.treeObj.get(0).ownerDocument.body),C,z,r,I=!1,i=e,x=e,G,Q,S=null,T=null,v=null,w=d.move.TYPE_INNER,M=b.clientX,N=b.clientY,K=(new Date).getTime();h.uCanDo(e)&&E.bind("mousemove",c);E.bind("mouseup",n);E.bind("selectstart",g);b.preventDefault&&b.preventDefault();return!0}};u.extend(!0,u.fn.zTree.consts,H);u.extend(!0,
u.fn.zTree._z,{tools:{getAbs:function(b){b=b.getBoundingClientRect();return[b.left+(document.body.scrollLeft+document.documentElement.scrollLeft),b.top+(document.body.scrollTop+document.documentElement.scrollTop)]},inputFocus:function(b){b.get(0)&&(b.focus(),h.setCursorPosition(b.get(0),b.val().length))},inputSelect:function(b){b.get(0)&&(b.focus(),b.select())},setCursorPosition:function(b,a){if(b.setSelectionRange)b.focus(),b.setSelectionRange(a,a);else if(b.createTextRange){var c=b.createTextRange();
c.collapse(!0);c.moveEnd("character",a);c.moveStart("character",a);c.select()}},showIfameMask:function(b,a){for(var c=m.getRoot(b);c.dragMaskList.length>0;)c.dragMaskList[0].remove(),c.dragMaskList.shift();if(a)for(var d=o("iframe",b),g=0,f=d.length;g<f;g++){var j=d.get(g),e=h.getAbs(j),j=o("<div id='zTreeMask_"+g+"' class='zTreeMask' style='top:"+e[1]+"px; left:"+e[0]+"px; width:"+j.offsetWidth+"px; height:"+j.offsetHeight+"px;'></div>",b);j.appendTo(o("body",b));c.dragMaskList.push(j)}}},view:{addEditBtn:function(b,
a){if(!(a.editNameFlag||o(a,d.id.EDIT,b).length>0)&&h.apply(b.edit.showRenameBtn,[b.treeId,a],b.edit.showRenameBtn)){var c=o(a,d.id.A,b),n="<span class='"+d.className.BUTTON+" edit' id='"+a.tId+d.id.EDIT+"' title='"+h.apply(b.edit.renameTitle,[b.treeId,a],b.edit.renameTitle)+"' treeNode"+d.id.EDIT+" style='display:none;'></span>";c.append(n);o(a,d.id.EDIT,b).bind("click",function(){if(!h.uCanDo(b)||h.apply(b.callback.beforeEditName,[b.treeId,a],!0)==!1)return!1;f.editNode(b,a);return!1}).show()}},
addRemoveBtn:function(b,a){if(!(a.editNameFlag||o(a,d.id.REMOVE,b).length>0)&&h.apply(b.edit.showRemoveBtn,[b.treeId,a],b.edit.showRemoveBtn)){var c=o(a,d.id.A,b),n="<span class='"+d.className.BUTTON+" remove' id='"+a.tId+d.id.REMOVE+"' title='"+h.apply(b.edit.removeTitle,[b.treeId,a],b.edit.removeTitle)+"' treeNode"+d.id.REMOVE+" style='display:none;'></span>";c.append(n);o(a,d.id.REMOVE,b).bind("click",function(){if(!h.uCanDo(b)||h.apply(b.callback.beforeRemove,[b.treeId,a],!0)==!1)return!1;f.removeNode(b,
a);b.treeObj.trigger(d.event.REMOVE,[b.treeId,a]);return!1}).bind("mousedown",function(){return!0}).show()}},addHoverDom:function(b,a){if(m.getRoots().showHoverDom)a.isHover=!0,b.edit.enable&&(f.addEditBtn(b,a),f.addRemoveBtn(b,a)),h.apply(b.view.addHoverDom,[b.treeId,a])},cancelCurEditNode:function(b,a){var c=m.getRoot(b),n=b.data.key.name,g=c.curEditNode;if(g){var k=c.curEditInput,j=a?a:k.val(),e=!!a;if(h.apply(b.callback.beforeRename,[b.treeId,g,j,e],!0)===!1)return!1;else g[n]=j?j:k.val(),b.treeObj.trigger(d.event.RENAME,
[b.treeId,g,e]);o(g,d.id.A,b).removeClass(d.node.CURSELECTED_EDIT);k.unbind();f.setNodeName(b,g);g.editNameFlag=!1;c.curEditNode=null;c.curEditInput=null;f.selectNode(b,g,!1)}return c.noSelection=!0},editNode:function(b,a){var c=m.getRoot(b);f.editNodeBlur=!1;if(m.isSelectedNode(b,a)&&c.curEditNode==a&&a.editNameFlag)setTimeout(function(){h.inputFocus(c.curEditInput)},0);else{var n=b.data.key.name;a.editNameFlag=!0;f.removeTreeDom(b,a);f.cancelCurEditNode(b);f.selectNode(b,a,!1);o(a,d.id.SPAN,b).html("<input type=text class='rename' id='"+
a.tId+d.id.INPUT+"' treeNode"+d.id.INPUT+" >");var g=o(a,d.id.INPUT,b);g.attr("value",a[n]);b.edit.editNameSelectAll?h.inputSelect(g):h.inputFocus(g);g.bind("blur",function(){f.editNodeBlur||f.cancelCurEditNode(b)}).bind("keydown",function(c){c.keyCode=="13"?(f.editNodeBlur=!0,f.cancelCurEditNode(b)):c.keyCode=="27"&&f.cancelCurEditNode(b,a[n])}).bind("click",function(){return!1}).bind("dblclick",function(){return!1});o(a,d.id.A,b).addClass(d.node.CURSELECTED_EDIT);c.curEditInput=g;c.noSelection=
!1;c.curEditNode=a}},moveNode:function(b,a,c,n,g,k){var j=m.getRoot(b),e=b.data.key.children;if(a!=c&&(!b.data.keep.leaf||!a||a.isParent||n!=d.move.TYPE_INNER)){var h=c.parentTId?c.getParentNode():j,t=a===null||a==j;t&&a===null&&(a=j);if(t)n=d.move.TYPE_INNER;j=a.parentTId?a.getParentNode():j;if(n!=d.move.TYPE_PREV&&n!=d.move.TYPE_NEXT)n=d.move.TYPE_INNER;if(n==d.move.TYPE_INNER)if(t)c.parentTId=null;else{if(!a.isParent)a.isParent=!0,a.open=!!a.open,f.setNodeLineIcos(b,a);c.parentTId=a.tId}var p;
t?p=t=b.treeObj:(!k&&n==d.move.TYPE_INNER?f.expandCollapseNode(b,a,!0,!1):k||f.expandCollapseNode(b,a.getParentNode(),!0,!1),t=o(a,b),p=o(a,d.id.UL,b),t.get(0)&&!p.get(0)&&(p=[],f.makeUlHtml(b,a,p,""),t.append(p.join(""))),p=o(a,d.id.UL,b));var q=o(c,b);q.get(0)?t.get(0)||q.remove():q=f.appendNodes(b,c.level,[c],null,!1,!0).join("");p.get(0)&&n==d.move.TYPE_INNER?p.append(q):t.get(0)&&n==d.move.TYPE_PREV?t.before(q):t.get(0)&&n==d.move.TYPE_NEXT&&t.after(q);var l=-1,u=0,x=null,t=null,C=c.level;if(c.isFirstNode){if(l=
0,h[e].length>1)x=h[e][1],x.isFirstNode=!0}else if(c.isLastNode)l=h[e].length-1,x=h[e][l-1],x.isLastNode=!0;else for(p=0,q=h[e].length;p<q;p++)if(h[e][p].tId==c.tId){l=p;break}l>=0&&h[e].splice(l,1);if(n!=d.move.TYPE_INNER)for(p=0,q=j[e].length;p<q;p++)j[e][p].tId==a.tId&&(u=p);if(n==d.move.TYPE_INNER){a[e]||(a[e]=[]);if(a[e].length>0)t=a[e][a[e].length-1],t.isLastNode=!1;a[e].splice(a[e].length,0,c);c.isLastNode=!0;c.isFirstNode=a[e].length==1}else a.isFirstNode&&n==d.move.TYPE_PREV?(j[e].splice(u,
0,c),t=a,t.isFirstNode=!1,c.parentTId=a.parentTId,c.isFirstNode=!0,c.isLastNode=!1):a.isLastNode&&n==d.move.TYPE_NEXT?(j[e].splice(u+1,0,c),t=a,t.isLastNode=!1,c.parentTId=a.parentTId,c.isFirstNode=!1,c.isLastNode=!0):(n==d.move.TYPE_PREV?j[e].splice(u,0,c):j[e].splice(u+1,0,c),c.parentTId=a.parentTId,c.isFirstNode=!1,c.isLastNode=!1);m.fixPIdKeyValue(b,c);m.setSonNodeLevel(b,c.getParentNode(),c);f.setNodeLineIcos(b,c);f.repairNodeLevelClass(b,c,C);!b.data.keep.parent&&h[e].length<1?(h.isParent=!1,
h.open=!1,a=o(h,d.id.UL,b),n=o(h,d.id.SWITCH,b),e=o(h,d.id.ICON,b),f.replaceSwitchClass(h,n,d.folder.DOCU),f.replaceIcoClass(h,e,d.folder.DOCU),a.css("display","none")):x&&f.setNodeLineIcos(b,x);t&&f.setNodeLineIcos(b,t);b.check&&b.check.enable&&f.repairChkClass&&(f.repairChkClass(b,h),f.repairParentChkClassWithSelf(b,h),h!=c.parent&&f.repairParentChkClassWithSelf(b,c));k||f.expandCollapseParentNode(b,c.getParentNode(),!0,g)}},removeEditBtn:function(b,a){o(a,d.id.EDIT,b).unbind().remove()},removeRemoveBtn:function(b,
a){o(a,d.id.REMOVE,b).unbind().remove()},removeTreeDom:function(b,a){a.isHover=!1;f.removeEditBtn(b,a);f.removeRemoveBtn(b,a);h.apply(b.view.removeHoverDom,[b.treeId,a])},repairNodeLevelClass:function(b,a,c){if(c!==a.level){var f=o(a,b),g=o(a,d.id.A,b),b=o(a,d.id.UL,b),c=d.className.LEVEL+c,a=d.className.LEVEL+a.level;f.removeClass(c);f.addClass(a);g.removeClass(c);g.addClass(a);b.removeClass(c);b.addClass(a)}},selectNodes:function(b,a){for(var c=0,d=a.length;c<d;c++)f.selectNode(b,a[c],c>0)}},event:{},
data:{setSonNodeLevel:function(b,a,c){if(c){var d=b.data.key.children;c.level=a?a.level+1:0;if(c[d])for(var a=0,g=c[d].length;a<g;a++)c[d][a]&&m.setSonNodeLevel(b,c,c[d][a])}}}});var G=u.fn.zTree,h=G._z.tools,d=G.consts,f=G._z.view,m=G._z.data,o=h.$;m.exSetting({edit:{enable:!1,editNameSelectAll:!1,showRemoveBtn:!0,showRenameBtn:!0,removeTitle:"remove",renameTitle:"rename",drag:{autoExpandTrigger:!1,isCopy:!0,isMove:!0,prev:!0,next:!0,inner:!0,minMoveSize:5,borderMax:10,borderMin:-5,maxShowNodeNum:5,
autoOpenTime:500}},view:{addHoverDom:null,removeHoverDom:null},callback:{beforeDrag:null,beforeDragOpen:null,beforeDrop:null,beforeEditName:null,beforeRename:null,onDrag:null,onDrop:null,onRename:null}});m.addInitBind(function(b){var a=b.treeObj,c=d.event;a.bind(c.RENAME,function(a,c,d,f){h.apply(b.callback.onRename,[a,c,d,f])});a.bind(c.REMOVE,function(a,c,d){h.apply(b.callback.onRemove,[a,c,d])});a.bind(c.DRAG,function(a,c,d,f){h.apply(b.callback.onDrag,[c,d,f])});a.bind(c.DROP,function(a,c,d,f,
e,m,o){h.apply(b.callback.onDrop,[c,d,f,e,m,o])})});m.addInitUnBind(function(b){var b=b.treeObj,a=d.event;b.unbind(a.RENAME);b.unbind(a.REMOVE);b.unbind(a.DRAG);b.unbind(a.DROP)});m.addInitCache(function(){});m.addInitNode(function(b,a,c){if(c)c.isHover=!1,c.editNameFlag=!1});m.addInitProxy(function(b){var a=b.target,c=m.getSetting(b.data.treeId),f=b.relatedTarget,g="",k=null,j="",e=null,o=null;if(h.eqs(b.type,"mouseover")){if(o=h.getMDom(c,a,[{tagName:"a",attrName:"treeNode"+d.id.A}]))g=h.getNodeMainDom(o).id,
j="hoverOverNode"}else if(h.eqs(b.type,"mouseout"))o=h.getMDom(c,f,[{tagName:"a",attrName:"treeNode"+d.id.A}]),o||(g="remove",j="hoverOutNode");else if(h.eqs(b.type,"mousedown")&&(o=h.getMDom(c,a,[{tagName:"a",attrName:"treeNode"+d.id.A}])))g=h.getNodeMainDom(o).id,j="mousedownNode";if(g.length>0)switch(k=m.getNodeCache(c,g),j){case "mousedownNode":e=x.onMousedownNode;break;case "hoverOverNode":e=x.onHoverOverNode;break;case "hoverOutNode":e=x.onHoverOutNode}return{stop:!1,node:k,nodeEventType:j,
nodeEventCallback:e,treeEventType:"",treeEventCallback:null}});m.addInitRoot(function(b){var b=m.getRoot(b),a=m.getRoots();b.curEditNode=null;b.curEditInput=null;b.curHoverNode=null;b.dragFlag=0;b.dragNodeShowBefore=[];b.dragMaskList=[];a.showHoverDom=!0});m.addZTreeTools(function(b,a){a.cancelEditName=function(a){var d=m.getRoot(b),g=b.data.key.name,h=d.curEditNode;d.curEditNode&&f.cancelCurEditNode(b,a?a:h[g])};a.copyNode=function(a,n,g,k){if(!n)return null;if(a&&!a.isParent&&b.data.keep.leaf&&
g===d.move.TYPE_INNER)return null;var j=h.clone(n);if(!a)a=null,g=d.move.TYPE_INNER;g==d.move.TYPE_INNER?(n=function(){f.addNodes(b,a,[j],k)},h.canAsync(b,a)?f.asyncNode(b,a,k,n):n()):(f.addNodes(b,a.parentNode,[j],k),f.moveNode(b,a,j,g,!1,k));return j};a.editName=function(a){a&&a.tId&&a===m.getNodeCache(b,a.tId)&&(a.parentTId&&f.expandCollapseParentNode(b,a.getParentNode(),!0),f.editNode(b,a))};a.moveNode=function(a,n,g,k){function j(){f.moveNode(b,a,n,g,!1,k)}if(!n)return n;if(a&&!a.isParent&&b.data.keep.leaf&&
g===d.move.TYPE_INNER)return null;else if(a&&(n.parentTId==a.tId&&g==d.move.TYPE_INNER||o(n,b).find("#"+a.tId).length>0))return null;else a||(a=null);h.canAsync(b,a)&&g===d.move.TYPE_INNER?f.asyncNode(b,a,k,j):j();return n};a.setEditable=function(a){b.edit.enable=a;return this.refresh()}});var M=f.cancelPreSelectedNode;f.cancelPreSelectedNode=function(b,a){for(var c=m.getRoot(b).curSelectedList,d=0,g=c.length;d<g;d++)if(!a||a===c[d])if(f.removeTreeDom(b,c[d]),a)break;M&&M.apply(f,arguments)};var N=
f.createNodes;f.createNodes=function(b,a,c,d){N&&N.apply(f,arguments);c&&f.repairParentChkClassWithSelf&&f.repairParentChkClassWithSelf(b,d)};var U=f.makeNodeUrl;f.makeNodeUrl=function(b,a){return b.edit.enable?null:U.apply(f,arguments)};var K=f.removeNode;f.removeNode=function(b,a){var c=m.getRoot(b);if(c.curEditNode===a)c.curEditNode=null;K&&K.apply(f,arguments)};var O=f.selectNode;f.selectNode=function(b,a,c){var d=m.getRoot(b);if(m.isSelectedNode(b,a)&&d.curEditNode==a&&a.editNameFlag)return!1;
O&&O.apply(f,arguments);f.addHoverDom(b,a);return!0};var P=h.uCanDo;h.uCanDo=function(b,a){var c=m.getRoot(b);return a&&(h.eqs(a.type,"mouseover")||h.eqs(a.type,"mouseout")||h.eqs(a.type,"mousedown")||h.eqs(a.type,"mouseup"))?!0:!c.curEditNode&&(P?P.apply(f,arguments):!0)}})(jQuery);
