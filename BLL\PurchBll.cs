﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using DAL;

namespace BLL
{
    public class PurchBll
    {

        /// <summary>
        /// 判断类别是否存在
        /// </summary>
        /// <param name="Kind"></param>
        /// <param name="KindList"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string JudgeSysKindExist(string Kind, string KindList, string QT, string sComp, string sFlag)
        {
            return PurchDal.JudgeSysKindExist(Kind, KindList, QT, sComp, sFlag);
        }

        /// <summary>
        /// 获取询价信息
        /// </summary>
        /// <param name="PPNo"></param>
        /// <param name="CustNo"></param>
        /// <param name="CustName"></param>
        /// <param name="BDate"></param>
        /// <param name="EDate"></param>
        /// <param name="Status"></param>
        /// <param name="BDate"></param>
        /// <param name="EDate"></param>
        /// <param name="sInMan"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetPurchProject(string No, string Item, string Name, string MNo, string MName, string Status, string BDate, string EDate, string A, string B, string C, int Row, int num, string sInMan, string sComp, string sFlag)
        {
            return PurchDal.GetPurchProject(No, Item, Name, MNo, MName, Status, BDate, EDate, A, B, C, Row, num, sInMan, sComp, sFlag);
        }

        /// <summary>
        /// 维护询价信息-需求询价
        /// </summary>
        /// <param name="PPNo"></param>
        /// <param name="PPVer"></param>
        /// <param name="CustNo"></param>
        /// <param name="CustEn"></param>
        /// <param name="PPMan"></param>
        /// <param name="PPDate"></param>
        /// <param name="PPDesc"></param>
        /// <param name="DocName"></param>
        /// <param name="DocPath"></param>
        /// <param name="sComp"></param>
        /// <param name="InMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string AddEditPurchProject(string PPNo, string PPVer, string CustNo, string CustEn, string PPMan, string PPDate, string PPDesc, string DocName, string DocPath, string sComp, string InMan, string Remark, string sFlag)
        {
            return PurchDal.AddEditPurchProject(PPNo, PPVer, CustNo, CustEn, PPMan, PPDate, PPDesc, DocName, DocPath, sComp, InMan, Remark, sFlag);
        }


        // 新增，修改，删除，审核 询价单信息
        public static string OPQsPrice(string QNo, string QD, string CPN, string Desc, string RFQNo, string PNo, string MNo, string Ver, string Num, string FP, string P, string UP, string Tax, string Rate, string L, string W, string H,
            string Ds, string WH, string TWH, string Wkg, string WZS, string FT, string AP, string LT, string MS, string SPNO, string SOPN, string DF, string InMan, string sComp, string Remark, string sFlag)
        {
            return PurchDal.OPQsPrice(QNo, QD, CPN, Desc,RFQNo, PNo, MNo, Ver, Num, FP, P, UP, Tax, Rate, L, W, H, Ds, WH, TWH, Wkg, WZS, FT, AP, LT, MS, SPNO, SOPN, DF, InMan, sComp, Remark, sFlag);
        }

        // 维护PR信息
        public static string AddEditPRInfo(string PPNo, string PRNo, string PRItem, string Item, string QsNo, string Ver, string Date, string CustNo, string PONO, string Quote, string ShipTo, string AddDesc, string ReqDesc, string CBy, string CType, string MNo, string KG, string IDesc, string Qty, string Tax, string SPCDate, string DDate, string sComp, string InMan, string Remark, string sFlag)
        {
            return PurchDal.AddEditPRInfo(PPNo, PRNo, PRItem, Item, QsNo, Ver, Date, CustNo, PONO, Quote, ShipTo, AddDesc, ReqDesc, CBy, CType, MNo, KG, IDesc, Qty, Tax, SPCDate, DDate, sComp, InMan, Remark, sFlag);
        }


        // 维护PO信息 
        public static string AddEditPOInfo(string PO, string POItem, string PPNo, string PRNo, string PRItem, string SupplierNo, string MNo, string PName, string JQDate, string Pay, string C1, string C2, string C3, string C4, string C5, string QT, string HTNo, string InMan, string InName, string Remark, string sFlag)
        {
            return PurchDal.AddEditPOInfo(PO, POItem, PPNo, PRNo, PRItem, SupplierNo, MNo, PName, JQDate, Pay, C1, C2, C3, C4, C5, QT, HTNo, InMan, InName, Remark, sFlag);
        }



        // 维护账务相关信息
        public static string AddEditFinInfo(string SNo, string ANo, string BNo, string CNo, string RNo, string MNo, string C1, string C2, string C3, string C4, string InMan, string InName, string sComp, string Remark, string sFlag)
        {
            return PurchDal.AddEditFinInfo(SNo, ANo, BNo, CNo, RNo, MNo, C1, C2, C3, C4, InMan, InName, sComp, Remark, sFlag);
        }


        /// <summary>
        /// 获取PO勾选记录
        /// </summary>
        /// <param name="PO"></param>
        /// <param name="Item"></param>
        /// <param name="RNo"></param>
        /// <param name="MNo"></param>
        /// <param name="sInMan"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetChoosePO(string PO, string Item, string RNo, string MNo, string sInMan, string sComp, string sFlag)
        {
            return PurchDal.GetChoosePO(PO, Item, RNo, MNo, sInMan, sComp, sFlag);
        }


        // 查询供应商质量相关数据
        public static DataTable GetSupplierQA(string SPNo, string SPName, string BDate, string EDate, string A, string B, string C, int Row, int num, string sInMan, string sComp, string sFlag)
        {
            return PurchDal.GetSupplierQA(SPNo, SPName, BDate, EDate, A, B, C, Row, num, sInMan, sComp, sFlag);
        }






















    }
}
