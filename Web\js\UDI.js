﻿


$(function() {

    function EntityMsg(item, Mesage) {
        $("#div_warning").show();
        item.focus();
        $("#sMessage").html(Mesage);

    }
    // 重新登录
    $('#BtnRelationLogin').click(function() {
        var Num = parseInt(Math.random() * 1000);
        location.href = "../Login.htm?CFlag=30&RA" + Num;

    });









    //  根据客户编码获取简称
    $('#txtBCustNo').blur(function() {
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();
        var sCNo = $('#txtBCustNo').val();
        var Flag = "41-2";
        var time = new Date();
        var sM = time.getMonth() + 1;
        if (sM < 10) {
            sM = "0" + sM
        }
        var sD = time.getDate()
        if (sD < 10) {
            sD = "0" + sD
        }

        var s1 = time.getFullYear() + "-" + sM + "-" + sD;
        $('#txtPPDate').val(s1);

        if (sCNo == "") {
            $("#div_warning").html("请输入客户编码！")
            $("#divsuccess").show();
            return;
        }


        var Data = '';
        var Params = { CNo: sCNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetCustInfoByNo&CFlag=41-2",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson.length > 0) {
                    // var sStr = eval(parsedJson.json)[0];
                    //$('#txtRNo').val(row.ReceiveNo);
                    $('#txtNameEn').val(parsedJson[0].CustEn);
                    $('#txtPPNo').val(parsedJson[0].CustNo);
                    $('#txtPPNo').focus();


                } else {
                    $("#txtNameEn").html("系统不存在该客户信息");
                }
            },
            error: function(data) {
                $("#div_warning2").html("系统出错，请重试2！")
                $("#div_warning2").show();
            }
        });

    });







    //  保存:发号信息
    $('#SerielSaveBtn').click(function() {
        $("#SerielSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();

        var sONo = $("#txtOrderNo").val();  //工单号
        var sMNo = $("#txtPMaterNo").val();
        var sNum = $("#txtFNum").val();
        var sBNo = $("#txtBNum").val();
        var sMSX = $("#txtMaterSX").val(); // 物料属性：自制，外购。外购的，需要插入批序号关系表
        var sFlag = $("#txtAEFlag").val();   // 2 发放序列号


        var Data = '';
        var Params = { OrderNo: sONo, MNo: sMNo, Num: sNum, A: sMSX,B:"",C:"", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=OPSerielInfo&CFlag=2",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == '发号成功') {

                    $("#div_warning").html(parsedJson.Msg);

                    $("#txtSOrder").val(sONo);
                    $('#SerielBut_open').click();
                    $("#txtSOrder").val('');

                    document.getElementById('light3').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#SerielSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#SerielSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("该工单已发放序列号！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_TECHFLOW') {
                    $("#SerielSaveBtn").removeAttr("disabled");
                    $("#div_warning").html(sMNo + "编码没有工艺路线，不能选择自制，请确认！");
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_TECHFLOW') {
                    $("#SerielSaveBtn").removeAttr("disabled");
                    $("#div_warning").html(sMNo + "编码有工艺路线，不能选择外购，请确认！");
                    $("#div_warning").show();
                } else {
                    $("#SerielSaveBtn").removeAttr("disabled");
                    $("#div_warning").html(parsedJson.Msg);
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#SerielSaveBtn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });




    //  根据工单号，带出相关性信息
    $('#txtOrderNo').blur(function() {
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $('#sFMsg').html("");

        var sNo = $("#txtOrderNo").val();
        var Flag = "111-3";


        var Data = '';
        var Params = { No: sNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=GetOrderInfoByNo&CFlag=" + Flag,
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $("#txtPMaterNo").val(sStr.MaterNo);
                    $("#txtPMaterName").val(sStr.MaterName);
                    $("#txtFNum").val(sStr.OrderNum);
                    $("#txtModel").val(sStr.MaterSpec);
                    $("#txtBNum").val(sStr.BatchNum);

                    $("#txtPMaterNo").attr({ "disabled": "disabled" });
                    $("#txtFNum").attr({ "disabled": "disabled" });

                }
                else if (parsedJson != undefined && parsedJson != '') {
                    $("#sFMsg").html(parsedJson.Msg);
                    $("#txtPMaterNo").removeAttr("disabled");
                    $("#txtFNum").removeAttr("disabled");
                    $("#txtPMaterNo").val("");
                    $("#txtFNum").val("");
                }
            },
            error: function(data) {
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });



    //  删除已发放的序列号
    $('#Seriel_Del_Btn').click(function() {
        $("#Seriel_Del_Btn").removeAttr("disabled");
        $("#div_warning2").hide();
        $("#divsuccess2").hide();

        var sONo = $("#txtOrderNo").val();  // 需要删除的工单序列号
        var sFlag = $("#txtAEFlag").val();  // 3

        if (sONo == "") {
            $("#div_warning2").html("请输入要删除的工单！")
            $("#div_warning2").show();
            $("#Seriel_Del_Btn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { OrderNo: sONo, MNo: "", Num: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=OPSerielInfo&CFlag=3",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#sFMsg").html("删除成功！")

                    $("#txtSOrder").val(sONo);
                    $('#SerielBut_open').click();
                    $("#txtSOrder").val('');

                    document.getElementById('Div_Del').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#Seriel_Del_Btn").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#Seriel_Del_Btn").removeAttr("disabled");
                    $("#div_warning2").html("该工单对应序列号已使用，不能删除！")
                    $("#div_warning2").show();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_DELETE') {
                    $("#Seriel_Del_Btn").removeAttr("disabled");
                    $("#div_warning2").html("该工单对应序列号已创建流程，不能删除！")
                    $("#div_warning2").show();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                    $("#Seriel_Del_Btn").removeAttr("disabled");
                    $("#div_warning2").html("工单对应序列号已生产，不能删除！")
                    $("#div_warning2").show();
                }
                else {
                    $("#Seriel_Del_Btn").removeAttr("disabled");
                    $("#div_warning2").html(parsedJson.Msg);
                    $("#div_warning2").show();
                }
            },
            error: function(data) {
                $("#Seriel_Del_Btn").removeAttr("disabled");
                $("#div_warning2").html("系统出错，请重试2！")
                $("#div_warning2").show();
            }
        });
    });



    //  保存:发放DI码
    $('#PubDI_But').click(function() {
        $("#PubDI_But").attr({ "disabled": "disabled" });
        $("#div_warning").hide();

        var sMNo = $("#txtUDIMaterNo").val();  //产品编码
        var sSpec = $("#txtSpecW").val();   // 型号
        var sDI = $("#txtDICode").val();   //DI码
        var sOldDI = $("#txtDICodeOld").val();   //DI码 -- 用于修改
        var sDI4 = $("#txtMDI").val();   //中包装DI码
        var sDI5 = $("#txtWDI").val();   //外包装DI码
        var sDI6 = $("#txtQTDI").val();   //其他包装DI码
        var sFlag = $('#txtAEFlag').val();      //  1 新增   2 修改DI信息，即收集信息
        var sMDI = "";
        var sWDI = "";
        var sQTDI = "";

        if ($("#txtCMDI").is(':checked')) {
            sMDI = "是";
        }
        if ($("#txtCWDI").is(':checked')) {
            sWDI = "是";
        }
        if ($("#txtCQTDI").is(':checked')) {
            sQTDI = "是";
        }

        if (sMNo == "") {
            $("#div_warning").html("请输入产品编码！")
            $("#div_warning").show();
            $("#PubDI_But").removeAttr("disabled");
            return;
        }

        if ((sDI != '') && (sDI.length != 14)) {
            $("#div_warning").html("DI码位数不正确！")
            $("#div_warning").show();
            $("#PubDI_But").removeAttr("disabled");
            return;
        }
        if ((sDI4 != '') && (sDI4.length != 14)) {
            $("#div_warning").html("中包装DI码位数不正确！")
            $("#div_warning").show();
            $("#PubDI_But").removeAttr("disabled");
            return;
        }
        if ((sDI5 != '') && (sDI5.length != 14)) {
            $("#div_warning").html("外包装DI码位数不正确！")
            $("#div_warning").show();
            $("#PubDI_But").removeAttr("disabled");
            return;
        }
        if ((sDI6 != '') && (sDI6.length != 14)) {
            $("#div_warning").html("其他级别DI码位数不正确！")
            $("#div_warning").show();
            $("#PubDI_But").removeAttr("disabled");
            return;
        }






        var Data = '';
        var Params = { MNo: sMNo, Spec: sSpec, DI: sDI, S1: sMDI, S2: sWDI, S3: sQTDI, S4: sDI4, S5: sDI5, S6: sDI6, S7: sOldDI, S8: "", S9: "", S10: "", S11: "", S12: "", S13: "", S14: "", S15: "", S16: "", S17: "", S18: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/UDIAjax.ashx?OP=OPUDIInfo&CFlag=26",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#txtDICode").val(parsedJson.DI)
                    $("#PubDI_But").removeAttr("disabled");
                    $('#AddDIBut_open').click();  // 重新查询

                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#PubDI_But").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#PubDI_But").removeAttr("disabled");
                    $("#div_warning").html("该产品编码或型号已发放了DI码！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTeDI') {
                    $("#PubDI_But").removeAttr("disabled");
                    $("#div_warning").html("修改后的DI已存在，请确认！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_NOMCODE') {
                    $("#PubDI_But").removeAttr("disabled");
                    $("#div_warning").html("系统找不到厂商识别码！")
                    $("#div_warning").show();
                }
                else {
                    $("#PubDI_But").removeAttr("disabled");
                    $("#div_warning").html(parsedJson.Msg);
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#PubDI_But").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });


    //  根据UDI物料编码获取物料信息
    $('#txtUDIMaterNo').blur(function() {
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $('#sFMsg').html("");

        var sNo = $("#txtUDIMaterNo").val();
        var Flag = "33-100";

        if (sNo == "") {
            $("#div_warning").html("请输入产品编码！")
            $("#div_warning").show();
            return;
        }

        // 清空所有记录
        $('#txtMaterNameW').val("");   //  产品描述
        $('#txtSpecW').val("");    //  产品型号
        $('#txtPDNameW').val("");    //  产品名称 PName
        $('#txtSPNameW').val("");    //  商品名称 MaterXH
        $('#txtBLCPW').val("");   //  包类产品 Mount
        $('#txtYMLDMW').val("");   //  原目录代码 TechNo
        $('#txtQXLBW').val("");    //  器械类别 MaterType
        $('#txtFLBMW').val("");   //  分类编码 FillMaterKind
        $('#txtZCRCNW').val("");   //  注册备案人 MHeight
        $('#txtZCRENW').val("");  //  注册备案人 Diameter
        $('#txtZCBHW').val("");    //  注册备案编号  Distortion
        $('#txtCPLBW').val("");    // 产品类别 MaterKind
        $('#txtAQXGW').val("");    //  安全相关信息  StockLacation
        $('#txtYCXW').val("");   //  一次性使用 EfftMg
        $('#txtCFSYCSW').val("");   //  重复使用次数  BatchMg
        $('#txtWJBZW').val("");   //  无菌包装 KaifengMg
        $('#txtSYQMJW').val("");    //  使用前灭菌  LOSHFlag
        $('#txtMJFSW').val("");   //  灭菌方式  Coating
        $('#txtQTXXW').val("");   //  其他信息链接  StockPlace
        $('#txtYBBMW').val("");   // 医保编码 BatchNo
        $('#txtTSRQW').val("");   //  退市日期  LastOutDate
        $('#txtPIPHW').val("");    //  PI 批号  TUnit
        $('#txtPIXLHW').val("");   //  PI 序列号 LWUnit
        $('#txtPISCRQW').val("");  //  PI 生产日期 HUnit
        $('#txtPISXRQW').val("");    //  PI 失效日期  InspectMg

        //        $("#txtDICode").val("");   //DI码
        //        $("#txtBMTXMC").val("");   //医疗器械唯一标识编码体系名称
        //        $("#txtUnitNum").val("");  //单元数量
        //        $("#txtDYCPBS").val("");  //单元产品标识
        //        $("#txtPubDate").val("");  //发布日期
        //        $("#txtBSZT").val("");   //标识载体
        //        $("#txtZCBAYZ").val("");  //注册备案标识标识一致
        //        $("#txtZCBABS").val("");  //注册备案编号  注册/备案产品标识
        //        $("#txtSFZJBS").val("");  //是否直接标识
        //        $("#txtXSDYYZ").val("");  //销售单元一致
        //        $("#txtBTCPBS").val("");  //本体产品标识

        var Data = '';
        var Params = { No: sNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetMaterInfoByNo&CFlag=" + Flag,
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];

                    $('#txtMaterNameW').val(sStr.MaterName);   //  产品描述
                    $('#txtSpecW').val(sStr.MaterSpec);   //  产品型号
                    $('#txtPDNameW').val(sStr.PName);   //  产品名称 PName
                    $('#txtSPNameW').val(sStr.MaterXH);   //  商品名称 MaterXH
                    $('#txtBLCPW').val(sStr.Mount);   //  包类产品 Mount
                    $('#txtYMLDMW').val(sStr.TechNo);   //  原目录代码 TechNo
                    $('#txtQXLBW').val(sStr.MaterType);   //  器械类别 MaterType
                    $('#txtFLBMW').val(sStr.FillMaterKind);   //  分类编码 FillMaterKind
                    $('#txtZCRCNW').val(sStr.MHeight);   //  注册备案人 MHeight
                    $('#txtZCRENW').val(sStr.Diameter);   //  注册备案人 Diameter
                    $('#txtZCBHW').val(sStr.Distortion);   //  注册备案编号  Distortion
                    $('#txtCPLBW').val(sStr.MaterKind);   // 产品类别 MaterKind
                    $('#txtAQXGW').val(sStr.StockLacation);   //  安全相关信息  StockLacation
                    $('#txtYCXW').val(sStr.EfftMg);   //  一次性使用 EfftMg
                    $('#txtCFSYCSW').val(sStr.BatchMg);   //  重复使用次数  BatchMg
                    $('#txtWJBZW').val(sStr.KaifengMg);   //  无菌包装 KaifengMg
                    $('#txtSYQMJW').val(sStr.LOSHFlag);   //  使用前灭菌  LOSHFlag
                    $('#txtMJFSW').val(sStr.Coating);   //  灭菌方式  Coating
                    $('#txtQTXXW').val(sStr.StockPlace);   //  其他信息链接  StockPlace
                    $('#txtYBBMW').val(sStr.BatchNo);   // 医保编码 BatchNo
                    $('#txtTSRQW').val(sStr.LastOutDate);   //  退市日期  LastOutDate
                    $('#txtPIPHW').val(sStr.TUnit);   //  PI 批号  TUnit
                    $('#txtPIXLHW').val(sStr.LWUnit);   //  PI 序列号 LWUnit
                    $('#txtPISCRQW').val(sStr.HUnit);   //  PI 生产日期 HUnit
                    $('#txtPISXRQW').val(sStr.InspectMg);   //  PI 失效日期  InspectMg

                    $("#txtDICode").val(sStr.ZXXSDYCPBS);   //DI码



                }
            },
            error: function(data) {
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });



    //  保存:DI信息收集
    $('#CollectDI_But').click(function() {
        $("#CollectDI_But").attr({ "disabled": "disabled" });
        $("#div_warning").hide();

        var sMNo = $("#txtUDIMaterNo").val();  //产品编码
        var sDI = $("#txtDICode").val();   //DI码
        var sBMTX = $("#txtBMTXMC").val();  //编码体系名称
        var sUNum = $("#txtUnitNum").val();  //单元数量 
        var sDYBS = $("#txtDYCPBS").val();  //单元产品标识
        var sPDate = $("#txtPubDate").val(); //发布日期
        var sBSZT = "";  //标识载体  $("#txtBSZT").val()
        var sZCBAYZ = $("#txtZCBAYZ").val();  //注册备案标识标识一致
        var sZCBABH = $("#txtZCBABH").val();  //注册备案编号  注册/备案产品标识
        var sSFZJBS = $("#txtSFZJBS").val();  //是否直接标识
        var sXSDYYZ = $("#txtXSDYYZ").val();  //销售单元一致
        var sBTCPBS = $("#txtBTCPBS").val();  //本体产品标识
        var sFlag = "2-1";      //


        if (sUNum == "") {
            $("#div_warning").html("请输入最小销售单元中使用单元的数量！")
            $("#div_warning").show();
            $("#L_Mgs").html("请输入最小销售单元中使用单元的数量！");
            $("#CollectDI_But").removeAttr("disabled");
            return;
        }
        if (isNaN(sUNum)) {
            $("#div_warning").html("最小销售单元中使用单元的数量请填写数字！")
            $("#div_warning").show();
            $("#L_Mgs").html("最小销售单元中使用单元的数量请填写数字！");
            $("#CollectDI_But").removeAttr("disabled");
            return;
        }

        if (sPDate == "") {
            $("#div_warning").html("请输入产品标识发布日期！")
            $("#div_warning").show();
            $("#L_Mgs").html("请输入产品标识发布日期！");
            $("#CollectDI_But").removeAttr("disabled");
            return;
        }

        if ($("#CH_One").is(':checked')) {
            sBSZT = "一维码";
        }
        if ($("#CH_Two").is(':checked')) {
            sBSZT = sBSZT + "," + "二维码";
        }
        if ($("#CH_Three").is(':checked')) {
            sBSZT = sBSZT + "," + "RFID";
        }
        if ($("#CH_Four").is(':checked')) {
            sBSZT = sBSZT + "," + "其他";
        }
        if (sBSZT == "") {
            $("#div_warning").html("请选择标识载体！")
            $("#div_warning").show();
            $("#L_Mgs").html("请选择标识载体！");
            $("#CollectDI_But").removeAttr("disabled");
            return;
        }
        if (sBSZT.substr(0, 1) == ",") {
            sBSZT = sBSZT.substr(1, sBSZT.length);
        }





        var Data = '';
        var Params = { MNo: sMNo, Spec: "", DI: sDI, S1: "", S2: "", S3: "", S4: sBMTX, S5: sUNum, S6: sDYBS, S7: sPDate, S8: sBSZT, S9: sZCBAYZ, S10: sZCBABH, S11: sSFZJBS, S12: sXSDYYZ, S13: sBTCPBS, S14: "", S15: "", S16: "", S17: "", S18: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/UDIAjax.ashx?OP=OPUDIInfo&CFlag=26",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#div_warning").html("信息提交成功，您看录入其他信息或关闭！")
                    $("#div_warning").show();
                    $("#L_Mgs").html("信息提交成功，您看录入其他信息或关闭！");

                    $("#txtDICode").val(parsedJson.DI)
                    $("#CollectDI_But").removeAttr("disabled");
                    $('#DIInfoBut_open').click();  // 重新查询

                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#CollectDI_But").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#CollectDI_But").removeAttr("disabled");
                    $("#div_warning").html("该产品编码或型号已发放了DI码！")
                    $("#div_warning").show();
                } else {
                    $("#CollectDI_But").removeAttr("disabled");
                    $("#div_warning").html(parsedJson.Msg);
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#CollectDI_But").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });




    //  保存:DI码相关信息：包装，储存，临床
    $('#UDIOtherSave_Btn').click(function() {
        $("#UDIOtherSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();

        var sDI = $("#txtDICode").val();   //DI码
        var sSNO = $('#txtSNO').val();
        var sS1 = $("#txtT1").val();
        var sS2 = $("#txtT2").val();
        var sS3 = $("#txtT3").val();
        var sS4 = $("#txtT4").val();
        var sS5 = $("#txtT5").val();
        var sFlag = $("#txtAEFlag").val();  //标识 1 :包装，2：储存  3：临床

        if (sDI == "") {
            $("#div_warning").html("请输入产品编码，带出DI码，再添加其他信息！")
            $("#div_warning").show();
            $("#UDIOtherSave_Btn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { MNo: sSNO, Spec: "", DI: sDI, S1: "", S2: "", S3: "", S4: sS1, S5: sS2, S6: sS3, S7: sS4, S8: sS5, S9: "", S10: "", S11: "", S12: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/UDIAjax.ashx?OP=OPUDIInfo&CFlag=26",
            data: { Data: Data },
            success: function(data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#txtDICode").val(parsedJson.DI)

                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';
                    $("#UDIOtherSave_Btn").removeAttr("disabled");

                    if ((sFlag = "11") || (sFlag = "11-1")) {
                        $('#dgDataList1').bootstrapTable('refresh', { url: '../Service/UDIAjax.ashx?OP=GetDIOtherInfo&CFlag=27-1&CNO=' + sDI });
                    }
                    if ((sFlag = "12") || (sFlag = "12-1")) {
                        $('#dgDataList2').bootstrapTable('refresh', { url: '../Service/UDIAjax.ashx?OP=GetDIOtherInfo&CFlag=27-2&CNO=' + sDI });
                    }
                    if ((sFlag = "13") || (sFlag = "13-1")) {
                        $('#dgDataList3').bootstrapTable('refresh', { url: '../Service/UDIAjax.ashx?OP=GetDIOtherInfo&CFlag=27-3&CNO=' + sDI });
                    }

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#UDIOtherSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                } else {
                    $("#UDIOtherSave_Btn").removeAttr("disabled");
                    $("#div_warning").html(parsedJson.Msg);
                    $("#div_warning").show();
                }
            },
            error: function(data) {
                $("#UDIOtherSave_Btn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });































    // 这里增加其他按钮，空间事件方法











});




// 用这个方法，主要是保证勾选不需要人勾，不设置disabled=disabled  ，这样用户看见的勾选比较清晰点。  2019  
function CheckBox_OnClick(ob) {
    var id = $(ob).attr("id");
    var sCD = id.substr(2, id.length);  // CB0  CB1  CB2


    if ($('#CB' + sCD).is(':checked')) {
        // $('#CB' + sCD).prop("checked", "checked");
        $('#CB' + sCD).removeProp("checked"); //设置为选中状态
    }
    else {
        $('#CB' + sCD).prop("checked", "checked");
    }


}


// 删除服务器的文件 -- 原图纸
function btn_PDelFile(n) {
    var sFileName = $("#la_F" + n).html();
    var sDPath = $("#txtF" + n).val();

    $.ajaxFileUpload({
        url: '../Service/ApplyUpPicFile.aspx?sFlag=400&DelPath=' + encodeURI(sDPath),
        dataType: 'json',
        success: function(data, status) {
            //                 var parsedJson = jQuery.parseJSON(data);
            //    data = eval(data);
            if (data.Msg == "Success") {
                
                $("#txtPath").val("");
                $("#txtFile").val("");
                $("#txtF" + n).val("");
                $("#la_F" + n).html("");
                $("#FTr" + n).hide();

                // 更新数据库
                update_Data("50");
                $('#PurchProject_open').click();
            }
            else {
                $("#div_warning").show();
                $("#sUpMessage").html("删除记录失败！")
            }

            //  $("#sUpMessage").html("删除成功！")
            //alert("上传成功");

        }
    });

}

function update_Data(n) {
    var sPMNo = $("#txtPPNo").val();
    var sMNo = $("#txtFile").val();


    var Data = '';
    var Params = { CNo: "", NameEn: "", PPNo: sPPNo, PPMan: "", PPDate: "", PPDesc: "", DocName: sDocName, DocPath: sDocPath, Remark: "", InMan: "", Flag: n };
    var Data = JSON.stringify(Params);

    $.ajax({
        type: "POST",
        url: "../Service/PurchAjax.ashx?OP=OPPurchProject&CFlag=" + n,
        data: { Data: Data },
        success: function(data) {
            var parsedJson = jQuery.parseJSON(data);

            if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                $("#divsuccess").show();
                if (n != "50-2") {  // 图纸转化 界面暂时不需要刷新。
                    $('#PurchProject_open').click();
                }
            }
            else {
                $("#div_warning").html("系统出错，请重试1！")
                $("#div_warning").show();
            }
        },
        error: function(data) {
            $("#div_warning").html("系统出错，请重试2！")
            $("#div_warning").show();
        }
    });
}
