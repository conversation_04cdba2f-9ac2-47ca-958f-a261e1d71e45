王工，好！
    简单跟你汇报一下今天沟通事情：
   1、LMS系统历史重大问题回顾：已输出清单，并对问题分析，是否需要继续跟进，如磁盘空间不足是需要的。
   2、LMS系统备份：海东已帮做备份到存储上；海东建议也在本地做一个备份，我已和许昌沟通，准备申请一个存储空间做本地本分。
   3、短信接口，按天翔说法，短信平台大概今年完成迁移到新架构，如按10月份不用LMS系统，可以不需要把LMS挂接到新短信平台。
   4、每天巡检：LMS已输出巡检清单，并周一到周五，每天巡检。



            if (sFlag == "63-6")  // 根据合同编号，获取PO信息
            {
                sSQL = " select a.SaleOrderNo,a.Tax,a.SupplierNo,b.SupplierEn,b.Currency,PayWay,SUM(a.Amount) as Amount,SUM(a.Amount*(1+a.Tax/100)) as <PERSON><PERSON><PERSON>,max(a.PurchDate) as PurchDate " +
                       " from T_PurchaseItemInfo a LEFT JOIN T_SupplierInfo b on a.SupplierNo=b.SupplierNo "+
                       " where a.SaleOrderNo='" + RNo + "'group by a.SaleOrderNo,a.Tax,a.SupplierNo,b.SupplierEn,b.Currency,PayWay ";
            }


花面逢迎，世情如鬼。嗜痂之癖，举世一辙

确定可参加：
四哥，光德，张琳，吴东，刘潜，潘祖亿，吴海东，

不一定，尽量过来：
黄喆，阿文，徐东河，叶华东

估计不来的：
凌美，

未回复：
李良生，吴春艺，庞剑锋，黄海燕

未通知到：
五狗伯


早期(种子天和A轮)  中期 (*B、C、D、E轮)   后期 (pre-IPO)
CStageOne     CStageTwo   CStageThree



SCMP供应链管理专家



-- 架构优化

-- 21

需求：
1、进入开发之前，需要重新和开发评审需求，内部拉通一轮。


开发：
1、开发人员变更，对模块影响很大。
吴丹鹏：对账模块
袁虎：对账意外的
代码层面的问题，比较明显。
加强前后端的沟通
同时加强代码评审。在测试应该模拟生产环境，比如权限配置。
2、DDS需要加强评审。



SIT:
1、测试能发现问题。
2、测试场景需要充分考虑。


UAT：
1、用户没有发现问题。


上线：
1、权限配置问题。
2、参考权限，需要参考正式系统的。不能参考测试系统。













王工，好！
   昨天下午有业务同事反馈SRM系统产生的库存批次重复，导致仓库无法收货。昨天下午组织开发一起排查问题，并修复。针对重复的库存批次，主要是VMI业务模块，因此把VMI模块20号以后的送货单取消就没有问题了，我已把有异常的送货单发给采购处理。有11个已收货到SAP系统，也和业务核对好，明天再和业务确认是否有问题。
   针对这些开发BUG，测试完整性等等问题，我明天和周舟，德林一起开会讨论，避免后续再次出现这样的问题。
   由于昨天晚上处理问题一直到凌晨，我就填写了半天的加班，你看是否合理，如果合理帮我审批一下哈。谢谢了。


王敏，好！
   昨天下午有业务同事反馈SRM系统产生的库存批次重复，导致仓库无法收货。昨天下午组织开发一起排查问题，并修复。针对重复的库存批次，主要是VMI业务模块，因此把VMI模块20号以后的送货单取消就没有问题了，我已把有异常的送货单发给采购处理。有11个已收货到SAP系统，也和业务核对好，明天再和业务确认是否有问题。
   针对这些开发BUG，测试完整性等等问题，我明天和周舟，德林一起开会讨论，避免后续再次出现这样的问题。
   由于昨天晚上处理问题一直到凌晨，我就填写了半天的加班，你看是否合理，如果流程到你那边了，帮审批一下哈，谢谢了。


各位，早。SRM系统对账模块8号上线，特别提醒几个事情:
1、UAT测试报告，需要签字才可以上线，所以这段时间尽快输出--海明负责，周舟跟进
2、用户培训，培训文档，培训记录 -- 海明负责，周舟跟进
3、数据初始化 -- 采购海明负责，财务黎工负责，周舟跟进
4、单点登录实现 -- 业务乔工负责，IT周舟负责，周舟每天在IT内部群反馈进度。总体王娜跟进。
5、发布脚本整理 -- SRM，ERP，税务平台等各自IT同事负责，周舟跟进
6、发布申请 -- 吴东负责，小风跟进
7、发布验证 --海明负责，周舟跟进
--- 更多上线准备事项请检查我们的上线清单
--- 整体业务负责同事乔工，IT负责同事周舟；





马云飞
    对生产流程不是很熟悉，主要是做和上位机对接的，比如：和医院B超设备对接。有开发小程序能力。对SQL也很熟悉。
    沟通能力好，理解能力也不错。
    住福永那边，对在光明这边上班没有啥意见。
    薪资大概1.2
朱海峰
   对生产流程很熟悉，而且做过和K3等系统的对接。沟通能力，理解能力都不错。目前在贵州出差，大概24号回深圳。
   住光明这边，24号后可随时面试。
   薪资大概1.3--1.5
纤草
曾景裕   18378093695
没接电话，直接挂断了

马嘉宝  13318502320
      负责MES, WINFORM，对接自动化设备。    负责打印模块，CS调用打印软件。刚接触MES大概，做过U8二次开发。
      大概一周左右可以入职。
      薪资12K
      评价：沟通方面不是特别好，技术还可以。特别有U8二次开发及接口开发。

揣欣媛  15530838705
     现在在秦皇岛，准备到深圳发展；做上位机的的开发，数据采集。也会和客户谈需求，做需求调研，包括后续运维。对B/S开发比较少；主要开发是C/S。对MES流程有了解。
     已提离职，大概3月中，4月初可以入职。
     薪资12K
    评价：沟通能力好，也有和用户谈需求的经验；不足：没有MES的经验，BS开发弱一些。

 章紫柔  152-7302-4250
     没有接电话

杨文翼 15086214726 
      C#有做过项目。对存储过程比较熟悉，和U8做对接。做过OA流程。去年9离职，现在在工作。
      薪资8K
      评价：沟通能可以，听起来也比较实诚。

周艳 15873494098
      10软件开发经验，有CS的开发经验，一直做BS开发，做CRM，ERP系统，门户网站。没有涉及MES，仓储的软件。前端H5+CSS。主要开发电脑端，没有涉及手机端开发。对SQL比较熟悉，对过程也比较熟悉。去年年底到深圳。
      薪资：12K
      评价：沟通能力好，有一定需求分析基础，使用技术也可以。不足：对生产流程管理不是很熟悉。

谢祥鹏 18702629805
      主要是做上位机开发，WPF，对生产流程大概流程。
      薪资：差不多就行，18K
      评价：沟通能力一般，不知自己做啥工作。不建议再面试。

李良峰 ：15211022609
     挂断了。

贺海燕  13728654841
     听说是光明，暂不考虑了。

范许燕  178-5809-5539
     没接电话

李鼎铭 18569541406
     从事MES工作，做二次开发。Jquery，JS比较熟悉。现在做WMS系统，来料检验，到仓库管理。数据库主要用SQL,主要通过过程过程实现一些方法。目前还在职，已提离职。
     薪资：15K
     评价：工作经验可以。使用的技术也比较多。

邹雨江 13786221575
     开发上位机和PLM通信，对MES系统流程基本熟悉。主要写WPF，和CS程序。BS没有开发过。对一些技术不怎么了解。
     薪资：11K
     评价：技术不是很好。沟通能力可以。工作经历不多。

刘玄 15623931467 
      主要做BS开发，数据库使用SQL，这两年使用PCSQL，对ORACLE也熟悉。使用CORE 写API，前端不是在行，不过也能看懂。对MES流程还是比较流程。
      目前还没提离职，收到office后，大概需要1个月。
      薪资：16K
      评价：沟通能力好，使用技术比较前沿。

骆振飞 15068743531
      做仓库管理系统，使用.NET CORE ，使用EF框架。前端有主要同事负责，主要提供API，SQL,MYSQL，对存储过程还必须熟悉，不过这两年没怎么用。
      薪资：9--11K
      评价：沟通能力好，也使用过不少技术，技术还可以。




邹颖颖  15710756336
  周一再面试

刘键  13824560066
     没去在沃尔玛做 运维开发，桌面自动化开发，3年报表开发经验，前后使用过不少技术，技术还可以，主要以BS为主。数据库不是非常精通，基本增删改查是没有问题的。
      薪资：当前13K
      评价：沟通能力可以，技术前后端技术也有使用过。不足点：对生产流程不是很熟悉。
      建议可以再次面试。

廖俊龙  18565713234
      工作经验5年，MES，WMS的开发，WMS系统的迭代开发。去年年底过去，也是做MES二次开发。一个人做好几个项目。SQL比较熟悉，有一点技术，MVC，定期服务也很在行，串口也使用过。有和ERP系统做过接口对接。

     评价：估计抗压能力不太好，说去年过那个公司，由于负责多点项目，现在就想走了。
     他建议先线上面试。
     建议可不面试。

肖尚金  18948700707
      做政府行业项目，使用WPF,涉及和硬件驱动的接口。redis做微服务。还做了系统分析，需求管理，还做过框架。项目做的比较多。
      薪资：当前22K
      评价：沟通能力可以，听起来经验是很足的。
      建议再次面试。
 
洪柳杰   13249576849
     主要是调用SAAS平台接口数据，把数据拉过来，做二次开发。负责后端开发，能做一些前端代码调试，不是很熟悉。对SQL还是比较熟悉。有做过项目实施，然后
      薪资：1.8W
      评价：沟通能力好，估计期望比较高。
      建议再次面试。   


杨 宇 15736705469
已关机

边振国  18163549426 
      主要做MES系统，ERP系统开发，接口开发，C#使用，主要用VUE.开发，后端用.net core ，能独立开发。SQL使用比较熟悉。离职1个多星期，做开发大概3年底多。
      薪资：1.3W
      评价：沟通能力好，建议可再次面试。


谭志峰   18570551156
       主要负责MES，实施维护，做一些小的功能开发。主要使用.net, 不涉及前端开发，主要使用SQL写报表。过程，视图还可以。索引不是很熟悉。
       薪资：
       评价：沟通一般，对工作有些挑剔。大概一个月能上班。
       不建议面试。

余佳欣   19983394647
       广州实时，做上位机软件，基于MAC开发，和上位机对接。C#不会使用。数据库SQL使用还可以，过程，视图还可以。索引不是很熟悉。离职状态，住南山。
      薪资：13K
      评价：沟通还可以，对工作热情也OK.
      可以再次面试

周新兴   18229420839
      已找到工作

朱茂琛    13192266680
       现在不方便面试。
       打两次电话没有接。


王健华  15221042223
      主要做JAVA， C#没怎么用过，看看应该也能懂。SQL也会使用，不过用得不多。
      薪资：
      评价：沟通能力可以，不过使用的语言和我们现在不太相符，数据库一般。
     不建议面试

刘宇晨  185-6576-9767
     没接电话

何德华   15923286036
       现在惠州；一年JAVA,三年C#. .NET CORE ，前端jqurey,对JS比较熟悉。后端做了3年。mysql使用比较错。使用过阿里技术做连接池。对缓存技术比较了解。在工厂做开发，住工厂不太习惯。
      薪资：惠州现在是11K，估计需要14K
      评价：沟通能力一般，总结不够好。不过技术还是可以的。
      可再次面试一下

张鸿伟  18676695180
      做了很长的开发，之前在比亚迪做ME；主要做.net,CS  BS 都有做过。sharepoit 无纸化平台。bootrasp 前端，MSSQL，使用批处理技术。也做过不少报表，使用存储过程技术。插件使用比较在行。也做过加密方面的功能。自己还会做一些技术研究。
      薪资：23K--24K
      评价：技术可以，使用的技术很多，也用过很多。沟通能力也可以。
      可以再次面试。

苟耕铨  19938301471 <EMAIL>
      工作2年多。主要做工作流，考勤管理，采购管理；SPRING，MQ等技术。mySQL。做APP和WEB，做过安卓。主要做后端，也帮做前端。
       薪资：10K， 
       沟通可以，建议再次面试

袁梁  17628178297 
       已找到工作了。

李先生  1867661846
      空号。

兰仕龙  15773960325 
     不考虑深圳，现在在广州

罗家滔   15820175501
     使用.net CORE ,前端用easy UI，主要做物流管理系统。数据库使用SQL比较熟悉。主要做二开，也做一些需求调用，代码更新。离职半年未找工作，一般找一般玩。
     薪资：12K,
     沟通方面：不是很好。
     可以面试一下。

邱福鑫    183-7437-0064
     挂断，估计不方便接听；
     已找到工作

张英浩 15340668602 
      接触.NET项目，MES也比较顺序。JS还可以。上一个工作主要做MES项目，CS结构,MVC结构。也使用过串口项目，和机床对接。BS比较少一些。负责数据库管理，数据表结构设计比较熟悉，数据备份。存储过程用得比较少。
      薪资：15K * 14 
      沟通方面可以，使用的技术也比较吻合我们，建议再次面试

黎耀 19973559908
      -- 电话错误

曾琳茜 17330994941
      C#很久以前用过。H5网页方面还可以。JS也可以。MYSQL比较熟悉；SQL不是很熟悉了。做外包项目，有开发任务就开发。
      薪资：9K 
      估计需要一些时间学习C#,SQL,可以再面试一下。

胡雨婷 18312483192 
       C# 3 年前用的，能很快拿回来。H5网页方面还可以。JS也可以。SQL也用过。主要做快递，电商，做仓储管理系统。SAAS 也比较熟悉。
       薪资：8K，
       沟通能力还可以。再次面试。

杨苏 17665237372
      -- 没接电话

魏英伦 18574788512
      --说晚上面试

黎晶晶  17318027320
       数据库使用SQL，存储过程一般，前端使用QGIRD，使用MVC结构。用LINQ做数据库查找。使用rides做缓存。
       薪资：13K，
       沟通能力一般。可以面试一下。

周高鹏   18682254323
      C#,数据，SQL，用得比较熟悉。主要开发ERP，MES。手机端开发比较熟悉
      薪资：1.2K--1.5K
      沟通能力一般。可以面试一下。

厉杰为  18230556752
      -- 现在不方便，大概7点有空

刘江平  18826522806 
      主要做后端管理系统，FRW3.5， 也用过CORE，前端也用过VEU ,JQUERY ，也比较熟悉。数据库SQL，ORACAL都比较熟悉。小程序也开发过。比较擅长后台和数据库。
      薪资：15K
      沟通能力可以，可再次面试

肖龙 13647163072 
      -- 不方便面试，感觉沟通不好。

刘旭 18912701320
      -- 对 .NET不熟悉，只是听说过。

盛建辉  15623579744
       无人接听


张志峰 13126683418
    无人接听

谢甘林  13528746400
    负责CRM开发，扩展开发，偏后台开发，主要使用CRM系统和财务系统的对接。做过需求，也做代码。对前端BS,CS，及JS等都比较熟悉。数据库主要SQL，也比较熟悉。
    薪资：20K
    沟通能力可以，不过薪资偏高，估计要求比较高。可以不面试。

张勇 15823757129
    在吃饭，不方便

庞浩 18933785357
    开发电商网站，ERP系统开发。后台程序员。SQL，ORACAL有用过。
    薪资：11-12K
    表达能力不行，沟通能力一般，不建议面试

吴宗徽 13554647434
     主要做ERP系统，SQL和MYSQL都比较熟悉。C#开发还可以。H5和JS也还可以，使用过VEU框架。近期主要做成本系统。对开发流程比较熟悉。也有一些业务分析能力。
     薪资：13K左右
     沟通能力可以，可以再次面试。

陈健 17674785874
     用户不接听。




纤草科技

万和科技大厦 


2023年1月13日晚班运维情况如下：
1.问题描述：更改工单对应的配置信息，满足装箱清单使用。
  该问题按之前和产线、质量达成的决议：鉴于当前物料紧缺，随时需要修改BOM及配置，之前已记录过该问题，本次本次不再记录。
系统运行稳定。没有需要交接给白班处理的问题。


2023年1月12日晚班运维情况如下：
1.问题描述：产线发现工单发放的序列号有ME自动发放的流水号，也有辅助系统推送标准的序列号，对产线使用有干扰。
问题原因：还在排查中
临时方案：和用户沟通，先使用辅助系统发放的标准序列号进行生产作业。
长期方案：还在沟通中。
系统运行稳定。以上问题需要白班同事持续跟进处理。



2023年1月11日晚班运维情况如下：
1.昨天用户反馈一个问题：组长没有修改DHR的权限。提示：不是生产经理，无法修改；后面工程师再次核对，是因为这个序列号已IPQC放行，所以不是问题。
问题原因：提示信息不正确
解决方案：后续优化，把准确信息提示给用户。
系统运行稳定，没有需要交接给白班处理的问题


问题现象：工单13021896 数量200台，一岗位实际才下了146台，怎么里面详细步骤显示下了162 ？是不是MES数据数量有问题
问题原因：
解决方案：



2023年1月10日晚班运维情况如下：
1.接到用户两个账号处理的电话，并协助服务台帮处理完成。
系统运行稳定，没有需要交接给白班处理的问题



2023年1月9日晚班运维情况如下：
1.存储扩容：扩容后，跟进产线使用情况，没有出现异常
2.帮助用户重置密码，也宣导后续解锁，重置密码找服务台88999
没有需要交接处理的问题


2023年1月9日白班运维情况如下：
1.问题现象：
今天晚上7点05分，MQ服务器************突然发送预警邮件，E盘空间已使用88%，超过之前的预警上限85%，晚上7点11分，继续发送预警邮件，E盘空间已使用94%，超过预警上限90%;
问题原因：
暂未真正查明原因，待查明原因后汇报；
解决方案：
1.已经与监护，麻醉，输注泵，试剂、探头和PCBA用户进行了窗口沟通，确定了窗口为今晚23点10分到23点30分，共20分钟；
2.暂定于今晚23点10分到23点30分进行服务器扩容；






吴工，115-017711-00，VS-900 panel assembly(without SpO2)，物料信息库更新了没？

		<add key="SqlConnectionStr" value="server=MRITOPINVCNSVC.mindray.corp;user id=sawang;pwd=***********;database=part"></add>
		<add key="LDAPString" value="LDAP://10.1.88.65:389/dc=mindray,dc=com"/>


供应商是否交货延迟
王敏，恒颖，好！

       我咨询了章工（俊峰）、林道云、王俊（TDP平台），把物料信息库相关需求跟他们进行沟通。从沟通结论看，物料信息库相关功能放到PLM系统比较合适。林道云也这样建议，并提了一点，说有些信息PLM系统还不一定有，如：工厂数据，PA编码等。所以建议还是分业务域看，那些业务同事需要看那些信息，最终评估落在那个系统，就类似把物料信息库一些查询功能分散到各系统。从评估看，大部分物料主数据信息在PLM都有。
       居于当前用户需要做预算，所以我建议按重新做一个物料信息库的工作量评估，最后开发物料信息库相关功能的系统再使用这些费用。

       现在要每个系统对物料信息库进行评估，可能有点来不及，我建议先按我们这边重新开发的评估一下，适当增加一下，就作为物料信息库的预算，我上周和彭工他们初步估算，大概需要40个人天，8万元，我就做10万的预算，你们看是否可行。


        我跟用户回邮件，就这样回。

        肖琴，好！

              这两周我们都在多方评估物料信息库，包括章俊峰，PLM林道云，TDP王俊等，也对物料信息库自身进行评估。形成如下结论：

              1、当前物料信息库已无法开发，因为系统太老旧，技术不在支持。
              2、初步建议在PLM（或TIS）系统承载当前物料信息库的功能。
              3、从做预算角度看，我们按“重新开发一套物料信息库”的工作量评估，建议你们做10万元的预算。用于在PLM(TIS)系统物料信息库重构，具体费用，按实施项目时重新评估的为准。
              4、关于PLM（或TIS）系统，可以再次跟刘星辰沟通，如果是刘工承接这个需求，让他帮做预算即可。



9120001843

select *  from T_ProductOutInfo
SDely	IsJT	FDely	DCause	Progress   Remark	

044-001922-00   --- 应该显示部分收获

---  分批回复交期是否随在途减少而删除一些记录。


http://www.20gli.top:8080/login.htm

王娜，杨工，周舟，好！
      关于SAP系统优化FCST的需求，今天和SAP同事沟通讨论，预计大概20号才能提交给SRM系统测试，我这边也尽快催促他们快一下。
      主要是因为FCST这个接口，目前已被APS项目锁定，所以无法单独更改，我和APS项目组同事沟通了，让他们帮一起更改，到时和APS项目一起发布上线。
      鉴于FCST数据是SAP系统推送给SRM系统的，现在接口没有下来，那我们模拟SAP推送数据给SRM的方式，让UAT测试继续。这种方式就是业务同事把数据整理好放到EXCEL，由我们IT帮导入系统。（注：如果现在试剂工厂导入的方式和我们后台导入一致，那可以用试剂导入的方式导入数据测试，这个需要周舟评估一下接口过来数据是否和手工导入的一样）     
      国庆后，我们就按这种方式继续测试FCST模块，保证SRM系统功能是正常的。我初步想了一下，这个FCST模块貌似没有数据回传给SAP系统的，周舟也帮看看是不是这样。

     存在一个风险是：如APS不能正常在10月底上线（原计划24号），那这个接口是无法单独发布的，会对我们有影响。

-- 物料扫描信息





大家好，下面是个人工作日报，请查阅
一、SRM架构优化项目
1）项目周列会，汇报项目进度情况
2）和SAP同事沟通接口开发情况，安排同事明天到中电一起联调
二、沟通交流
1）EWM系统和SRM系统对接讨论
2）武汉标贴打印变更方案讨论
三、运维:
1）编写备件随货同行单实现方案
2）编写IQC追溯码需求的实现方案
3）编写采购订单增加最新货架寿命的实现方案
4）处理辅助 系统，SRM系统运维问题


各位同学，好！
    2001年9月的某一天，我们背着行囊，相遇屏风山下，时光飞逝，我们相识已22年；光阴荏苒，我们慢慢变老。疫情三年，我们只能网上谈论，诉说情怀，当年的容颜是否依旧？懵懂青年，我们同窗共读，每每回想起来都温馨如昨。
    应同学们的盛情，拟在桂林举办毕业18周年同学大聚会，让同学友情如一杯老酒越久越浓。
    时间初定：15（周六）16（周天） 17（周一）
    地点：桂林
    形式：聚餐+户外活动







各位同学，好！
    时光飞逝，光阴荏苒，疫情三年阻隔我们见面的机会，但阻隔不了我们浓浓的情谊。懵懂青年，我们同窗共读，每每回想起来都温馨如昨。
    本周班长凯春到访深圳，也邀请大家到深圳聚聚，商讨一下今年桂林聚会事宜，大家抽空过来聚聚，先露露脸，看看多年未见的容颜。


目前对账模块用户已测试完成，签字文档已在业务那边，一会就发出来。对了，签字文档有一个需要你签字的，我下周拿给你签字哈。


王敏，恒颖，好！
      我看了研发的容雪给BLACK发了一个邮件，关于标贴打印获取不到数据的。初步问题是PLM系统到ERP系统数据异常。需要ERP同事帮处理。昨晚电话给ERP阿发，阿发说有跟容雪沟通，但沟通不是很顺畅，说容雪不愿意提单让ERP系统导入数据（不过今天一早我电话给容雪，说提单了）
      这个事情之前容雪也发邮件给BLACK请求支援，主要是他们判断涉及多个系统，所以就没有单独找我了。
      我今天上午电话和MPI运营管理部的黎勇沟通（研发项目一般都是有MPI评估），建议他来统筹这个事情，他说好的，先拉通一下IT各个系统同事。估计近期会做这个事情。
      让你们知晓一下这个事情，主要是容雪两次邮寄给BLACK了，BLACK问起不知道也不好。接下来看看黎勇那边怎么协调IT同事处理一些问题，到时需要我也会积极参与。这个事情之前也没通知IT参加，也不知啥背景，所以做起来有些被动。



大家好，下面是个人工作日报，请查阅
一、SRM架构优化项目
1）和APS项目进行联调测试
2）讨论研发采购上线方案，并编写，协助整理脚本
3）和ERP同事讲解FCST新增的功能点
二、南京生物SRM系统实施项目
1）协助测试对账模板功能
三、沟通交流
1）项目晨会。
2）武汉骨科标贴打印测试用例评审
四、运维:
1）处理BI项目组提出的数据问题 
2）处理SRM系统，辅助系统运维问题 
五、深迈动
1）跟进FTP服务器的部署完成情况，目前已完成。




    //根据扫描的UDI码，获取序列号
    function GetSerialNo(sTCNo) {  //   [GS]  这种标识是GS1编码规则特有的，而且需要设置扫描枪才能显示
    
        if (sTCNo.length > 20) {  // 如果长度打印20 ，说明扫描的是UDI码,目前定义的UDI码规则是：DI +  生产批次（可选） + 生产日期（可选）+ 失效日期（可选）+ 序列号（可选）
            sTCNo = sTCNo.substr(16, sTCNo.length); // 删除DI码，删除后：10RD22003[GS]112206091723120821JF50C922000352[CR]
            if (sTCNo.substr(0, 2) == "10") {  // (10)生产批次
                sTCNo = sTCNo.substr(sTCNo.indexOf("[GS]") + 4, sTCNo.length); // 删除生产批次 ，删除后：112206091723120821JF50C922000352[CR]
                if (sTCNo.substr(0, 2) == "11") {  //（11）生产日期
                    sTCNo = sTCNo.substr(8, sTCNo.length); // 删除生产日期 ，删除后：1723120821JF50C922000352[CR]
                    if (sTCNo.substr(0, 2) == "17") {//（17） 失效日期
                        sTCNo = sTCNo.substr(8, sTCNo.length); // 删除失效日期 ，删除后：21JF50C922000352[CR]
                        sCNo = sTCNo.substr(2, sTCNo.indexOf("[CR]") - 2)// 获取序列号：21JF50C922000352[CR]
                    }
                    else if (sTCNo.substr(0, 2) == "21") {//（21） 序列号
                        if (sTCNo.indexOf("[GS]") > 0) {   // 说明序列号后面还有其他组成部分: 21JF50C922000352[GS]17231208[CR]
                            sCNo = sTCNo.substr(2, sTCNo.indexOf("[GS]") - 2) 
                        }
                        else {
                            sCNo = sTCNo.substr(2, sTCNo.indexOf("[CR]") - 2)// 获取序列号：21JF50C922000352[CR]
                        }
                    }
                }
                else if (sTCNo.substr(0, 2) == "17") {//（17） 失效日期
                    sTCNo = sTCNo.substr(8, sTCNo.length); // 删除失效日期 ，删除后：21JF50C922000352[CR]
                    if (sTCNo.indexOf("[GS]") > 0) {   // 说明序列号后面还有其他组成部分: 21JF50C922000352[GS]17231208[CR]
                        sCNo = sTCNo.substr(2, sTCNo.indexOf("[GS]") - 2)
                    }
                    else {
                        sCNo = sTCNo.substr(2, sTCNo.indexOf("[CR]") - 2)// 获取序列号：21JF50C922000352[CR]
                    }
                }
                else if (sTCNo.substr(0, 2) == "21") {//（21） 序列号
                     if (sTCNo.indexOf("[GS]") > 0) {   // 说明序列号后面还有其他组成部分: 21JF50C922000352[GS]17231208[CR]
                        sCNo = sTCNo.substr(2, sTCNo.indexOf("[GS]") - 2)
                     }
                    else {
                        sCNo = sTCNo.substr(2, sTCNo.indexOf("[CR]") - 2)// 获取序列号：21JF50C922000352[CR]
                    }
                }
            }  // == "10"
            else if (sTCNo.substr(0, 2) == "11") {  //（11）生产日期
                sTCNo = sTCNo.substr(8, sTCNo.length); // 删除生产日期 ，删除后：1122060910RD22003[GS]1723120821JF50C922000352[CR
                if (sTCNo.substr(0, 2) == "10") {//（10） 生产批，删除后：10RD22003[GS]1723120821JF50C922000352[CR
                    sTCNo = sTCNo.substr(sTCNo.indexOf("[GS]") + 4, sTCNo.length); // 删除生产批次 ，删除后：21JF50C922000352[CR]
                    if (sTCNo.substr(0, 2) == "17") {//（17） 失效日期
                        sTCNo = sTCNo.substr(8, sTCNo.length); // 删除失效日期 ，删除后：21JF50C922000352[CR]
                        if (sTCNo.indexOf("[GS]") > 0) {   // 说明序列号后面还有其他组成部分: 21JF50C922000352[GS]17231208[CR]
                            sCNo = sTCNo.substr(2, sTCNo.indexOf("[GS]") - 2)
                        }
                        else {
                            sCNo = sTCNo.substr(2, sTCNo.indexOf("[CR]") - 2)// 获取序列号：21JF50C922000352[CR]
                        }
                    }
                }
                else if (sTCNo.substr(0, 2) == "17") {//（17） 失效日期：172312082110RD22003[GS]21JF50C922000352[CR]
                    sTCNo = sTCNo.substr(8, sTCNo.length); // 删除失效日期 ，删除后：10RD22003[GS]21JF50C922000352[CR]
                    if (sTCNo.substr(0, 2) == "10") {//（10） 生产批，删除后：
                        sTCNo = sTCNo.substr(sTCNo.indexOf("[GS]") + 4, sTCNo.length); // 删除生产批次 ，删除后：21JF50C922000352[CR]
                        sCNo = sTCNo.substr(2, sTCNo.indexOf("[CR]") - 2)// 获取序列号：21JF50C922000352[CR]
                    } 
                    else if (sTCNo.indexOf("[GS]") > 0) {   // 说明序列号后面还有其他组成部分: 21JF50C922000352[GS]17231208[CR]
                        sCNo = sTCNo.substr(2, sTCNo.indexOf("[GS]") - 2)
                    }
                    else {
                        sCNo = sTCNo.substr(2, sTCNo.indexOf("[CR]") - 2)// 获取序列号：21JF50C922000352[CR]
                    }
                }
                else if (sTCNo.substr(0, 2) == "21") {//（21） 序列号
                    if (sTCNo.indexOf("[GS]") > 0) {   // 说明序列号后面还有其他组成部分: 21JF50C922000352[GS]17231208[CR]
                        sCNo = sTCNo.substr(2, sTCNo.indexOf("[GS]") - 2)
                    }
                    else {
                        sCNo = sTCNo.substr(2, sTCNo.indexOf("[CR]") - 2)// 获取序列号：21JF50C922000352[CR]
                    }
                }
            }  // == "11"
            else if (sTCNo.substr(0, 2) == "17") {//（17） 失效日期:1723120810RD22003[GS]1122060921JF50C922000352[CR]
                 if (sTCNo.substr(0, 2) == "10") {//（10） 失效日期，删除后： 
                    sTCNo = sTCNo.substr(sTCNo.indexOf("[GS]") + 4, sTCNo.length); // 删除生产批次 ，删除后：
                    if (sTCNo.substr(0, 2) == "11") {//（11） 生产日期：1122060921JF50C922000352[CR]
                        sTCNo = sTCNo.substr(2, 8)// 获取序列号：21JF50C922000352[CR]
                        sCNo = sTCNo.substr(2, sTCNo.indexOf("[CR]") - 2)// 获取序列号：21JF50C922000352[CR]
                    }
                    else if (sTCNo.substr(0, 2) == "21") {//（21） 序列号
                        if (sTCNo.indexOf("[GS]") > 0) {   // 说明序列号后面还有其他组成部分: 21JF50C922000352[GS]17231208[CR]
                            sCNo = sTCNo.substr(2, sTCNo.indexOf("[GS]") - 2)
                        }
                        else {
                            sCNo = sTCNo.substr(2, sTCNo.indexOf("[CR]") - 2)// 获取序列号：21JF50C922000352[CR]
                        }
                    }
                }
                else if (sTCNo.substr(0, 2) == "11") {
                    if (sTCNo.substr(0, 2) == "10") {//（10） 生产日期，删除后：
                        sTCNo = sTCNo.substr(sTCNo.indexOf("[GS]") + 4, sTCNo.length); // 删除生产批次 ，删除后：
                        sCNo = sTCNo.substr(2, sTCNo.indexOf("[CR]") - 2)// 获取序列号：21JF50C922000352[CR]
                    }
                    else if (sTCNo.substr(0, 2) == "21") {//（21） 序列号
                        if (sTCNo.indexOf("[GS]") > 0) {   // 说明序列号后面还有其他组成部分: 21JF50C922000352[GS]17231208[CR]
                            sCNo = sTCNo.substr(2, sTCNo.indexOf("[GS]") - 2)
                        }
                        else {
                            sCNo = sTCNo.substr(2, sTCNo.indexOf("[CR]") - 2)// 获取序列号：21JF50C922000352[CR]
                        }
                    }
                }
                else if (sTCNo.substr(0, 2) == "21") {//（21） 序列号
                    if (sTCNo.indexOf("[GS]") > 0) {   // 说明序列号后面还有其他组成部分: 21JF50C922000352[GS]17231208[CR]
                        sCNo = sTCNo.substr(2, sTCNo.indexOf("[GS]") - 2)
                    }
                    else {
                        sCNo = sTCNo.substr(2, sTCNo.indexOf("[CR]") - 2)// 获取序列号：21JF50C922000352[CR]
                    }
                }
            }  // == "17"
            else if (sTCNo.substr(0, 2) == "21") {//（21） 序列号
                if (sTCNo.indexOf("[GS]") > 0) {   // 说明序列号后面还有其他组成部分: 21JF50C922000352[GS]17231208[CR]
                    sCNo = sTCNo.substr(2, sTCNo.indexOf("[GS]") - 2)
                }
                else {
                    sCNo = sTCNo.substr(2, sTCNo.indexOf("[CR]") - 2)// 获取序列号：21JF50C922000352[CR]
                }
            }  // == "21"
        }  // if (sTCNo.length > 20) 
        else {
            sCNo = sTCNo.substr(0, sTCNo.indexOf("[CR]"))// 获取序列号：21JF50C922000352[CR]
        }

        return sCNo;
        
    }





select * from  T_MaterMngInfo  
select * from  T_MaterMngLog

MNo,MSpec,Unit,Stock,NowStock,BNum,Loca,Status,CompanyNo,InMan,InDate,Remark

T_MaterMngLog

1.罗非鱼，偏小点的
2.鲍鱼3个
3.活虾大概3两
4.韭黄
5.排骨1条，可以砍小点
6.鸡半只，大概18元一斤的
7.番茄5个，帮挑下好的
8.
9.玉米2个
10.青瓜1条，帮挑一下好的
11.麒麟西瓜1/4个，偏小点



未开始
员工对应工作中心
    #light table{margin:0px; padding:0px;}
--- 3.0.0.1
3.0.0

.table>tfoot>tr>td

.table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td {
    /* padding: 8px; */
    line-height: 1.42857143;
    vertical-align: top;
    border-top: 1px solid #ddd;
}

http://www.20gli.top:8080/login.htm
MCodeBut_open
length
一、SRM架构优化项目
1）跟进对账模块的开发进度，目前进度正常。
二、沟通交流
1）项目晨会。
2）季度之星评选会。
三、运维:
1）处理SRM系统辅助系统5个问题，主要分析试剂标贴打印问题。
2）E-LEANNING学习考试。


新增字段 T_MaterInfo
(3)	产品技术要求编号 txtJSNo
(4)	部件名称  txtPartName
(5)	部件型号  txtPartSpec
(6)	使用期限   txtUseLDate
(7)	CMIIT ID  txtCMIITID
(8)	尺寸  txtCSize
(9)	使用方法及注意事项  txtNote
(10)	适应症及禁忌症  txtSYJJ
(11)	储存方法   txtStore
(12)	运输条件 txtTransp


BI项目跟进
和周舟对齐一下项目计划，有比较大的调整
和开发沟通达周的需求。
整理辅助系统的归档文件，主要做一个案例出来，让许春波他们按照这个编写。




1、只校验合同含税金额，不用校验本次发票金额。
2、在付款审核界面也要显示上面几个金额
3、打印合同模板预留20个记录
4、图纸转换，PO 审核后不能删除
5、在PR交期确认界面导出档案袋，按P.O#条件导出。

440306201902073637

1、修改 T_InspectInfo Status  长度为 20

22108646\",\"sSerialNo\":\"GN-58002292\"}
8440002760

王工，好！有三个事情跟你汇报一下：
1、Seatable：我跟吕工沟通了找几个账号模拟角色测试。他说正有此意，已在内部讨论这个事情了。我偶尔跟进一下他们测试情况。
2、魏总提的TI供应商供需报表-TI库存展示功能：目前已开发完成，正在部署PO，我下午找业务先测试一次。比预期提前一天。
3、大概2月份说的采购单价、金额异常影响后端付款事宜：业务同时说统一在现在的对账梳理流程考虑，我也提醒他们，不单单考虑SRM系统，ERP系统，还有GTS关务系统等。因此这个任务我们不单独跟进了。

4栋1209 & A0321（思科会议号：30089014；密码：9014）

明天会议邀请：王鹏，吕建勋，尹建良，王俊，魏总，

4503561420

本周主要工作：
1、DI码手工发放的功能已开发完成。
2、接口已优化完成并提供接口给IDP平台。
下周计划：
1、对DI码发放进行单元测试
2、连续用户，可进行DI码手工发放测试
3、根据IDP平台进度进行接口联调。


吕建勋
--  

10 - 2 - 王俊，我，吕建勋，抄送：王鹏，
1、不用复杂了，急，先用起来。
2、10个模板，那些放到系统，那些在网盘，可以多人编辑；需要外部编辑的。
会议纪要告诉王鹏，王敏，季浩。




王工，好
    这两周我和周舟在讨论SRM系统架构优化项目开展事项，也和业务定义了模块实施的优先级。
    我和周舟写了一个启动材料，想着IT内部先评审，你看星期四下午有没有空，给我们一些指导意见。



1、因为合同编号是按照日期 20220213（220213）产生的，因此勾选的单据，合同签订日期必须相同。否则系统就不知按那个合同签订日期产生合同编号了。
2、系统按年份判断当前年份最大的，如果系统存在的合同签订日期是2021-12-20（合同编号A01-211220-01），现在勾选的记录，合同签订日期是2021-03-12，系统产生的合同编号是：A01-210312-02;如果在勾选另外一个记录，合同签订日期是2021-05-05，那么产生的合同编号是：A01-210505-02,即流水号不变。



王工，好！
    针对之前付款发生的一些案例，如：单价大100倍，单价修改，税率修改等问题。今天找达周初步沟通了，建议他那边组织同事一起梳理。他也很支持做这个事情，说元旦后会安排同事和我们一起梳理。我们初步的基调是在一些业务环节做管控，提醒为主。流程优化，OCR校验会提到乔工那边统一管理实施。







