﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css">
    <link rel="stylesheet" href="https://unpkg.com/bootstrap-table@1.18.3/dist/bootstrap-table.min.css">

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>
    <script src="https://unpkg.com/bootstrap-table@1.18.3/dist/bootstrap-table.min.js"></script>

    <table id="table"
           data-toggle="table"
           data-toolbar="#toolbar"
           data-search="true"
           data-show-refresh="true"
           data-show-toggle="true"
           data-show-fullscreen="true"
           data-show-columns="true"
           data-pagination="true"
           data-click-to-select="true"
           data-buttons-class="primary"
           data-buttons-prefix="btn-"
           data-buttons-align="left"
           data-columns="columns">
    </table>

    <script>
        var $table = $('#table');
        var columns = [
            {
                field: 'id',
                title: 'ID'
            },
            {
                field: 'name',
                title: 'Name'
            },
            {
                field: 'operate',
                title: 'Operate',
                align: 'center',
                events: operateEvents,
                formatter: operateFormatter
            }
        ];

        function operateFormatter(value, row, index) {
            return [
                '<button class="btn btn-primary btn-xs btn-edit" style="margin-right:15px;">Edit</button>',
                '<button class="btn btn-danger btn-xs btn-delete">Delete</button>'
            ].join('');
        }

        window.operateEvents = {
            'click .btn-edit': function (e, value, row, index) {
                // 编辑行操作
                console.log('Edit', row);
            },
            'click .btn-delete': function (e, value, row, index) {
                // 删除行操作
                console.log('Delete', row);
            }
        };

        $table.bootstrapTable({
            columns: columns
        });
    </script>

</head>
<body>

</body>
</html>