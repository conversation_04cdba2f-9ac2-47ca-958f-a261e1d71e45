﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>物料批序号</title>
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <script src="../js/ajaxfileupload.js" type="text/javascript"></script>
    <link href="../css/XC.css" rel="stylesheet" />
    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/BaseModule.js"></script>

    <script type="text/javascript">
        layui.use("table", function () {
            var table = layui.table;
            table.render({
                elem: "#BatchSerial",
                id: "BatchSerialID",
                url: "../Service/BaseModuleAjax.ashx?OP=GetBatchSerial&CFlag=24",
                height: "full-70",
                cellMinWidth: 80,
                count: 50,
                limit: 20,
                limits: [10, 20, 30, 40, 50],
                cols: [[
                    { type: "numbers", width: 60 },
                    { field: "MaterNo", title: "物料编码", width: 145, sort: true },
                    { title: "物料描述", field: "MaterName", sort: true },
                    { title: "序列号/批号", field: "BatchNo", sort: true },
                    { title: "状态", field: "Status" },
                    { title: "创建人", field: "InMan" },
                    { title: "创建日期", field: "InDate2" },
                    { field: 'op', title: '操作', toolbar: '#barDemo', fixed: 'right', width: 120 }
                ]],
                page: true
            })





            table.on("tool(BatchSerial)", function (obj) {
                var data = obj.data;
                var event = obj.event;

                if (event == "edit") {
                    if (data.Status == "已使用") {
                        layer.msg('已使用记录不可修改');
                        return;
                    }

                    //关闭弹出层
                    $("#ShowOne").css("display", "block");
                    $("#ShowOne-fade").css("display", "block");

                    $("#head-title1").html("修改物料批序号")

                    $("#txtAEFlag").val("Edit")

                    //获取修改前的批量编号
                    $("#txtBatchNoOld").val(data.BatchNo)

                    $("#div_warning").hide();
                    $("#txtMaterNo").val(data.MaterNo);
                    $("#txtMaterName").val(data.MaterName);
                    $("#txtBatchNo").val(data.BatchNo);
                    $("#txtMaterNo").attr("disabled", true);

                } else if (event == "delete") {
                    if ((data.Status == "已使用") || (data.Status == "使用中")) {
                        layer.msg('已使用，使用中的记录不可删除');
                        return;
                    }

                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("您确定要删除该序列号/批号吗？ 序列号/批号：")
                    //设置删除的对象
                    $("#hint-value").html(data.BatchNo)

                    $("#txtKind").val(data.BatchNo)


                }
            })

            //查询
            $("#BatchSerialSearch").click(function () {
                var sMarterNo = $("#txtSMNo").val()
                var sMarterName = $("#txtSMName").val()
                var sBatchNo = $("#txtBNo").val()

                var Data = '';
                var Params = { CMNO: sMarterNo, MName: sMarterName, BNo: sBatchNo };
                var Data = JSON.stringify(Params);

                console.log(Data)

                table.reload("BatchSerialID", {
                    method: "post",
                    url: "../Service/BaseModuleAjax.ashx?OP=GetBatchSerial&CFlag=24&Data=" + Data,

                    page: {
                        curr: 1
                    }
                })
            })

            $("#BatchMSerial_OP_Btn").click(function () {

                var sBatch = $("#txtKind").val()

                var Data = JSON.stringify({ MaterNo: "", MaterName: "", BatchNo: sBatch, BatchNoOld: "", OPFlag: "" })
                $.ajax({
                    url: "../Service/BaseModuleAjax.ashx?OP=OPBatchSerial&CFlag=2",
                    type: "POST",
                    data: {
                        Data: Data
                    },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);
                        if (parsedJson != undefined && parsedJson != '' && parsedJson.msg == 'Success') {
                            layer.msg('删除成功！');
                            $("#BatchSerialSearch").click()
                            $("#ShowDel").css("display", "none")
                            $("#ShowDel-fade").css("display", "none")
                        } else {
                            layer.msg('系统出错，请重试1！');
                        }
                    },
                    error: function () {
                        layer.msg('系统出错，请重试2！');
                    }
                })
            })
        })

        function openDialog() {
            $("#ShowOne").css("display", "block");
            $("#ShowOne-fade").css("display", "block");
            $("#head-title1").html("新增物料批序号")
            $("#txtAEFlag").val("Add")
            $("#txtMaterNo").val("");
            $("#txtMaterName").val("");
            $("#txtBatchNo").val("");
            $("#txtMaterNo").removeAttr("disabled", "false");
        }

        function closeDialog() {
            $("#ShowOne").css("display", "none");
            $("#ShowOne-fade").css("display", "none");
            $("#ShowDel").css("display", "none")
            $("#ShowDel-fade").css("display", "none")
            $("#ShowUpload").css("display", "none");
            $("#ShowUpload-fade").css("display", "none");
        }

        function openUpload() {
            $("#ShowUpload").css("display", "block");
            $("#ShowUpload-fade").css("display", "block");
            $("#SelectFileName").html("")
            $('#fileInput').val("")
        }
    </script>

    <script>
        $(function () {


            $("#OpenUpload").click(function () {
                $("#fileInput").click();
            })

            $("#fileInput").change(function () {
                $("#SelectFileName").html($('#fileInput')[0].files[0].name)
            })

            $('#BatchSerialSubmitUpload').click(function () {
                if ($("#fileInput").val() == "") {
                    ErrorMessage("请选择要提交的文件",2000)
                    return
                }

                var fileInput = $('#fileInput')[0];
                var file = fileInput.files[0];
                var formData = new FormData();
                formData.append('file', file);

                // 发送POST请求
                $.ajax({
                    url: '../Service/BaseModuleAjax.ashx?OP=BatchSerialToExcel',
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        if (response == "Success") {
                            layer.msg('导入成功！');
                            $("#BatchSerialSearch").click()
                            closeDialog()
                        } else if (response == "FileTypeError") {
                            layer.msg('不支持此文件类型！');
                        } else if (response == "RepeaData") {
                            layer.msg('导入失败！出现重复数据');
                        } else {
                            layer.msg('系统出错，请稍后再试！');
                        }
                    }, error: function () {
                        layer.msg('网络繁忙，请稍后再试！');
                    }
                });
            });
        })
    </script>
    <style>
        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }

        #ShowTow .XC-modal-body span {
            width: 85px
        }

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }

        #ShowOne .XC-Form-block-Item > span {
            width: 80px;
        }
    </style>
</head>
<body>
    <div class="div_find">
        <label class="find_labela">物料编码</label> <input type="text" id="txtSMNo" class="find_input" />
        <label class="find_labela">物料描述</label><input type="text" id="txtSMName" class="find_input" />
        <label class="find_labela">序列号/批号</label><input type="text" id="txtBNo" class="find_input" />
        <input type="button" value="搜索" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="BatchSerialSearch" />
        <input type="button" value="导入" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="BatchSerialLeading" onclick="openUpload()" />
        <input type="button" value="添加" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="BatchSerialAdd" onclick="openDialog()" />
    </div>

    <div class="wangid_conbox">
        <table class="layui-hide" id="BatchSerial" lay-filter="BatchSerial"></table>
        <script type="text/html" id="barDemo">
            <button id="edit" lay-event="edit" class="XC-Btn-md XC-Btn-Green XC-Size-xs">修改</button>
            <button id="edit" lay-event="delete" class="XC-Btn-md XC-Btn-Red XC-Size-xs">删除</button>
        </script>
    </div>

    <div class="XC-modal XC-modal-md" id="ShowOne" style="height:360px">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1"></span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <form class="XC-Form-block">
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">物料编码<span class="XC-Font-Red">*</span></span>
                    <input type="text" class="XC-Input-block" id="txtMaterNo" name="txtMaterNo" placeholder="" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">物料描述<span class="XC-Font-Red">*</span></span>
                    <input type="text" class="XC-Input-block" disabled id="txtMaterName" name="txtMaterName" placeholder="" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Textarea-block">序列号/批号<span class="XC-Font-Red">*</span></span>
                    <textarea class="XC-Textarea-block" id="txtBatchNo" name="txtBatchNo" placeholder="请输入序列号/批号。可以输入多个序列号/批号，中间使用英文逗号隔开"> </textarea>
                </div>
            </form>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtBatchNoOld" name="txtBatchNoOld" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="BatchSerialSubmit">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="BatchSerialClose" onclick='closeDialog()'>关闭</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>

    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtKind" name="txtKind" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="BatchMSerial_OP_Btn">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>

    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>


    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowUpload">
            <div class="XC-modal-head">
                <span class="head-title">导入数据</span>
                <span class="head-close" onclick="closeDialog()">X</span>
            </div>
            <div class="XC-modal-body" style="padding:35px 20px">
                <div style="display:flex">
                    <div>
                        <button type="button" class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="OpenUpload" style="width:100px">
                            <i class="layui-icon layui-icon-upload"></i> 选择文件
                        </button>
                    </div>
                    <div style="height: 34px; width: 355px; margin-left: 10px; line-height: 34px; border-bottom: 1px solid #0c873d; color: #0c873d; ">
                        <div id="SelectFileName"></div>
                        <input type="file" name="file" style="display:none" id="fileInput" />
                    </div>
                </div>
            </div>
            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="BatchSerialSubmitUpload">上传文件</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowUpload-fade" class="black_overlay">
    </div>
</body>
</html>