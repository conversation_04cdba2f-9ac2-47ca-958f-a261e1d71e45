       // 工艺文件相关信息
        function ShowOrderFileInfo(sNo, sProcNo, sVer) {

            //工艺文件
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#OrderFileList',
                    id: 'OrderFileID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-7&CNO=' + sNo + '&Item=' + sProcNo,
                    height: '260',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'ProcedureName', title: '工序', width: 100 },
                        { field: 'ProcedureVer', title: '版本', width: 50 },
                        { field: 'FileNo', title: '文件编码', width: 150 },
                        { field: 'FileVer', title: '版本', minWidth: 50 },
                        { field: 'FileName', title: '文件名称', minWidth: 200 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate2', title: '录入时间', width: 150 },
                        { field: 'op', title: '操作', width: 130, toolbar: '#barDemo_WOFile', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(OrderFileList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值


                    if (layEvent == 'edit') {
                        $('#txtAddKind').val("14-6");
                        $("#L_WOFile").html("修改文件信息");

                        $("#txtWOFPNo").val(data.OrderNo); // 工单号
                        $("#txtWOFProNo").val(data.ProcedureNo);
                        $("#txtWOFProName").val(data.ProcedureName);
                        $("#txtWOFProVer").val(data.ProcedureVer);
                        $("#txtPath").val(data.FilePath);  //  文件的路径
                        $("#txtFile").val(data.DocName); //  文件的名称
                        $("#txtWOFileNo").val(data.FileNo);  // 工艺文件编号，报表表头的
                        $("#txtWOFileVer").val(data.FileVer);
                        $("#txtWOFileName").val(data.FileName);
                        $("#div_warningFile").html("");

                        $("#txtWOFileNo").attr({ "disabled": "disabled" });
                        $("#txtWOFileVer").attr({ "disabled": "disabled" });

                        var scrollTop = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
                        document.getElementById('Div_WOFile').style.top = (scrollTop + 30) + "px";

                        document.getElementById('Div_WOFile').style.display = 'block';
                    }
                    else if (layEvent == 'del') {

                        layer.confirm('真的删除工艺文件么', function (index) {
                            //向服务端发送删除指令 A: sPVer, B: sPath, C: sFile, D: sFNo, E: sFVer, F: sFName,

                            var sWONo = data.OrderNo;
                            var sNo = data.ProcedureNo;
                            var sPVer = data.ProcedureVer;
                            var sFNo = data.FileNo;
                            var sFVer = data.FileVer;
                            var sFlag = "14-7";

                            var Data = '';
                            var Params = { No: sNo, Name: "", Item: sWONo, MNo: "", MName: "", A: sPVer, B: "", C: "", D: sFNo, E: sFVer, F: "", Remark: "", Flag: sFlag };
                            var Data = JSON.stringify(Params);

                            $.ajax({
                                type: "POST",
                                url: "../Service/OrderAjax.ashx?OP=OPOrderInfo&CFlag=" + sFlag,
                                data: { Data: Data },
                                success: function (data) {
                                    var parsedJson = jQuery.parseJSON(data);

                                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                        layer.msg('删除成功！');
                                        //$('#Fin_SearchOpen').click();
                                        obj.del(); //删除对应行（tr）的DOM结构，并更新缓存
                                        layer.close(index);
                                    }
                                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                                        layer.msg('该文件已使用，不能操作！');
                                        // $('#Fin_SearchOpen').click();
                                    } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                                        layer.msg('该工序版本已禁用，不能操作！');
                                        // $('#Fin_SearchOpen').click();
                                    }
                                    else {
                                        layer.msg('删除失败，请重试！');
                                        // $('#Fin_SearchOpen').click();
                                    }
                                },
                                error: function (data) {
                                    layer.msg('删除失败2，请重试！');
                                    // $('#Fin_SearchOpen').click();
                                }
                            });

                        }); // 删除
                    }

                });

            });  // layui.use('table', function () {


        } // function ShowProcFileInfo(sPNo, sNo, sVer) {