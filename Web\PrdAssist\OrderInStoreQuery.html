﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>工单入库记录查询</title>
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <script src="../js/ajaxfileupload.js" type="text/javascript"></script>
    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all">
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/PrdAssist.js" type="text/javascript"></script>

    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        //$('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=28-1' });
                    }
                }
            })
        });

    </script>

    <script type="text/javascript">

        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#table_OrderInStoreQueryList',
                id: 'OrderInStoreQueryListID',
                url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=26',
                height: 'full-80',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'InNo', title: '入库编号', width: 120, sort: true },
                    { field: 'K3RKNo', title: 'K3入库编号', width: 120 },
                    { field: 'OrderNo', title: '工单号', width: 200, sort: true },
                    { field: 'InType', title: '入库类型', width: 100 },
                    { field: 'BomVer', title: 'BOM版本', width: 100 },
                    { field: 'MaterNo', title: '物料编码', width: 100, sort: true },
                    { field: 'MaterName', title: '物料名称', width: 100 },
                    { field: 'MaterSpec', title: '物料规格', width: 100 },
                    { field: 'InKind', title: '入库类别', width: 100 },
                    { field: 'InNum', title: '入库数量', width: 80 },
                    { field: 'Wno', title: '仓库编号', width: 100, sort: true },
                    { field: 'WName', title: '仓库名称', width: 80 },
                    { field: 'Status', title: '状态', width: 80 },
                    { field: 'MaterBatchNo', title: '批次号', width: 100 },
                    { field: 'prdDate', title: '生产日期', width: 100 },
                    { field: 'InDate', title: '入库日期', width: 200 },
                    { field: 'InMan', title: '入库人', width: 90 },
                    { field: 'op', title: '操作', width: 200, toolbar: '#barDemo', fixed: 'right' }
                ]],
                page: true,

            });

            //监听是否选中操作
            table.on('checkbox(table_OrderInStoreQueryList)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);

            });

            table.on('tool(table_OrderInStoreQueryList)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值
                if (layEvent === 'view') {
                    $("#div_QueryDetail").css("display", "block")
                    $("#fade").css("display", "block");

                    var Data = '';
                    var Params = { No: data.InNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "", E: "" };
                    var Data = JSON.stringify(Params);

                    table.render({
                        elem: "#table_InStoreQueryDetailList",
                        id: "table_InStoreQueryDetailList",
                        url: "../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=27&Data=" + Data,
                        //height: "full-50",
                        cellMinHeight: 80,
                        cols: [[
                            { type: "numbers" },
                            { field: "BatchNo", title: "序列号" },
                            { field: "OrderNo", title: "工单号" },
                            { field: "MaterNo", title: "物料编码" },
                            { field: "MaterName", title: "物料描述" },
                            { field: "InNum", title: "入库数量" },
                            { field: "PrdDate", title: "生产日期" },
                            { field: "Status", title: "状态" },
                            { field: "InMan", title: "操作人" },
                            { field: "InDate2", title: "入库时间" },
                        ]]
                    })

                }

            });


            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(table_OrderInStoreQueryList)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

            });

            //监听单元格编辑
            table.on('edit(table_OrderInStoreQueryList)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });

            //  查询 --
            $('#btn_OrderInStoreQuery_Open').click(function () {

                var sOrderNo = $("#txtsOrderNo").val();  //
                var sWareHouseNo = $("#txtsWareHouseNo").val()
                var sMaterNo = $("#txtsMaterNo").val()
                //var sName = encodeURI($("#txtSDeviceName").val());  //
                //var sMaterNo = encodeURI($("#txtSMMaterNo").val());  //


                var Data = '';
                var Params = { No: sOrderNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: sMaterNo, B: sWareHouseNo, C: "", D: "", E: "" };
                var Data = JSON.stringify(Params);

                table.reload('OrderInStoreQueryListID', {
                    method: 'post',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=26&Data=' + Data,
                    where: {
                        'No': sOrderNo
                    }, page: {
                        curr: 1
                    }
                });
            });



        });



        function closeDialog(n) {
            if (n == 1)//关闭详细信息弹窗
            {
                document.getElementById('div_OrderInStoreQueryDetail').style.display = 'none';
                document.getElementById('fade').style.display = 'none';
                $("#div_QueryDetail").css("display", "none")
                $("#fade").css("display", "none")
            }

        }


    </script>


    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

        })


    </script>

    <style type="text/css">

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: white;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=60);
        }

        .LabelDelBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #da542e;
        }

        .LabelAddBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #579fd8;
        }
    </style>


</head>
<body>
    <div class="div_find">

        <p>
            <label class="find_labela">工单号：</label> <input type="text" id="txtsOrderNo" class="find_input" />
            <label class="find_labela">物料编码：</label> <input type="text" id="txtsMaterNo" class="find_input" />
            <label class="find_labela">仓库编号：</label> <input type="text" id="txtsWareHouseNo" class="find_input" />
            <!--<label class="find_labela">设备名称：</label><input type="text" id="txtSDeviceName" class="find_input" />
            <label class="find_labela">设备分类编号：</label><input type="text" id="txtSMMaterNo" class="find_input" />-->

            <input type="button" value="搜索" class="find_but" id="btn_OrderInStoreQuery_Open">
        </p>
    </div>

    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="table_OrderInStoreQueryList" lay-filter="table_OrderInStoreQueryList"></table>

        <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-xs" lay-event="view">入库详情</a>
        </script>
    </div>


    <!--设备信息修改和新增弹窗-->
    <div id="div_QueryDetail" style="display: none;position: absolute;top: 1%;left: 5%;right: 5%;width: 90%;height: 700px;border: 2px solid #21b6b4; background-color: white;z-index: 1002;overflow: auto;">
        <div id="div_OrderInStoreQueryDetailHead" style="background-color: #26d0a1; height: 40px;">
            <!--<label id="label_ShowOrderInStoreQuery" style=" padding:10px;font-size: 14px; color:White; "></label>-->
            <label id="label_ShowOrderInStoreQuery" style=" padding:5px;font-size: 14px; color:White; ">入库详情</label>
            <label onclick="closeDialog(1)" style="float:right; margin:5px 5px 0 0; cursor:pointer;">X</label>
        </div>
        <div id="div_warning" role="alert" style="text-align: center; display:none;color: Red ">
            <strong id="divsuccess" style="color: Red"></strong>
        </div>

        <div style=" font-weight: bold; padding: 5px; height: 35px; line-height: 25px; margin-top: 5px; border: 0px; border-bottom: solid 1px #ccc;">
          序列号信息
        </div>

        <table class="layui-hide" id="table_InStoreQueryDetailList" lay-filter="table_InStoreQueryDetailList"></table>
 
    </div>

    <!--设备信息修改和新增弹窗-->
    <div id="div_OrderInStoreQueryDetail" style="display: none;position: absolute;top: 1%;left: 5%;right: 5%;width: 90%;height: 500px;border: 2px solid #21b6b4; background-color: white;z-index: 1002;overflow: auto;">
        <div id="div_OrderInStoreQueryDetailHead" style="background-color: #26d0a1; height: 40px;">
            <!--<label id="label_ShowOrderInStoreQuery" style=" padding:10px;font-size: 14px; color:White; "></label>-->
            <label id="label_ShowOrderInStoreQuery" style=" padding:5px;font-size: 14px; color:White; ">设备信息</label>
            <label onclick="closeDialog(1)" style="float:right; margin:5px 5px 0 0; cursor:pointer;">X</label>
        </div>
        <div id="div_warning" role="alert" style="text-align: center; display:none;color: Red ">
            <strong id="divsuccess" style="color: Red"></strong>
        </div>

        <div style=" font-weight: bold; padding: 5px; height: 35px; line-height: 25px; margin-top: 5px; border: 0px; border-bottom: solid 1px #ccc;">
            基本信息
        </div>

        <table cellspacing="0" cellpadding="0" border='0' style="width:99%; ">
            <tr style=" height:40px;">
                <td style=" width:100px; text-align:right;">
                    设备编号:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDeviceNo" name="txtDeviceNo" style="height:30px;" />
                </td>
                <td style=" width:100px; text-align:right;">
                    设备名称:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDeviceName" name="txtDeviceName" style="height:30px;" />
                </td>
            </tr>

            <tr style="height:40px;">
                <td style="width:100px; text-align:right;">
                    使用场景:
                </td>
                <td>
                    <select class="form-control" id="txtDeviceDesc">
                        <option></option>
                        <option>工位固定使用</option>
                        <option>工位间移动使用</option>

                    </select>
                </td>
                <td style=" width:100px; text-align:right;">
                    设备规格:
                </td>
                <td>
                    <input class="form-control" id="txtDeviceSpec" name="txtDeviceSpec" style="height:30px;" />
                </td>
            </tr>

        </table>

        <div style=" font-weight: bold; padding: 5px; height: 35px; line-height: 25px; margin-top: 5px; border: 0px; border-bottom: solid 1px #ccc;">
            管理信息
        </div>

        <table cellspacing="0" cellpadding="0" border='0' style="width:99%; ">
            <tr style=" height:40px;">
                <td style=" width:100px; text-align:right;">
                    管理部门编号:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDeptNo" name="txtDeptNo" readonly="readonly" style="height:30px;" />
                </td>
                <td style=" width:10px; text-align:left;">
                    <input type="button" value="选择" class="find_but" id="btn_Dept_open" onclick="openDialog(2)" style="height:30px;" />
                </td>
                <td style=" width:100px; text-align:right;">
                    管理部门:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDeptName" name="txtDeptName" readonly="readonly" style="height:30px;" />
                </td>
            </tr>

            <tr style="height:40px;">
                <td style="width:100px; text-align:right;">
                    设备状态:
                </td>
                <td>
                    <select class="form-control" id="txtStatus">
                        <option></option>
                        <option>启用</option>
                        <option>封存</option>
                        <option>停用</option>
                        <option>验收中</option>
                    </select>

                </td>
                <td style="width:100px; text-align:right;">
                </td>
                <td style=" width:40px; text-align:right;">
                    有效期（年）:
                </td>
                <td>
                    <input class="form-control" id="txtInventoryCycle" name="txtInventoryCycle" style="height:30px;" autocomplete="off" autofocus="autofocus" onkeyup="value=value.replace(/[^\d^\.]+/g,'').replace('.','$#$').replace(/\./g,'').replace('$#$','.')" />     <!--onchange="if(/\D/.test(this.value)){alert('有效期只能输入数字');this.value='';}"-->
                </td>
            </tr>
            <tr style=" height:40px;">
                <td style=" width:100px; text-align:right;">
                    校准有效期:
                </td>
                <td>
                    <input type="date" class="form-control" id="txtDCUseDate" name="txtDCUseDate" style="height:30px;" />
                </td>
                <td style=" width:100px; text-align:right;">
                    <!--维护要求:-->
                </td>
                <td>
                    <!--<input type="text" class="form-control" id="txtDMaintainReq" name="txtDMaintainReq" readonly=readonly style="height:30px;" />-->
                </td>
            </tr>
        </table>

        <div style=" font-weight: bold; padding: 5px; height: 35px; line-height: 25px; margin-top: 5px; border: 0px; border-bottom: solid 1px #ccc;">
            维护信息
        </div>
        <table cellspacing="0" cellpadding="0" border='0' style="width:99%; ">
            <tr>
                <td style="width:100px; text-align:right;">
                    设备分类编号:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtMaterNo" name="txtMaterNo" readonly="readonly" style="height:30px;" />
                </td>
                <td style=" width:10px; text-align:left;">
                    <input type="button" value="选择" class="find_but" id="btn_DeviceClass_open" onclick="openDialog(3)" style="height:30px;" />
                </td>
                <td style=" width:40px; text-align:right;">
                    设备类型:
                </td>
                <td>
                    <select class="form-control" id="txtDeviceKind">
                        <option></option>
                        <option>设备</option>
                        <option>工装</option>
                        <option>仪器</option>

                    </select>
                </td>
                <!--<td style="width:100px; text-align:right;">
                    备注:
                </td>
                <td colspan="4">
                    <textarea type="text" class="form-control" id="txtRemark" name="txtRemark" style="height:30px;" />
                </td>-->
            </tr>
            <tr style=" height:40px;">
                <td style=" width:100px; text-align:right;">
                    点检要求:
                </td>
                <td>
                    <select class="form-control" id="txtCheckReq">
                        <option></option>
                        <option>工作日点检</option>
                        <option>使用前点检</option>
                        <option>周点检</option>
                    </select>
                </td>
                <td></td>
                <td style=" width:100px; text-align:right;">
                    保养要求:
                </td>
                <td>
                    <select class="form-control" id="txtMaintainReq">
                        <option></option>
                        <option>年度</option>
                        <option>半年</option>
                        <option>季度</option>
                        <option>月度</option>
                        <option>周度</option>
                    </select>
                </td>
            </tr>
        </table>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div align="center">
            <input type='button' id="btn_OrderInStoreQuerySave" value='保存' style="width: 50px; height: 30px;" />
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <input type='button' id="btn_OrderInStoreQuerySaveClose" value='关闭' onclick='closeDialog(1)' style="width: 50px; height: 30px;" />
        </div>
        <div id="fade_SelectDeptInfo" class="black_overlay"></div>
    </div>

    <div id="fade" class="black_overlay">

    </div>
</body>
</html>