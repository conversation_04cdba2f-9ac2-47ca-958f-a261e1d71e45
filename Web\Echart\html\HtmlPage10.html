﻿<!DOCTYPE html
          PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title></title>
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/echarts.js"></script>
    <script>
        $(function () {
            init()
        })
        function init() {


           


            var date = new Date()
            var ChartXOneData = [];
            var ChartOneYData = [];
            var ChartXTowata = [];
            var ChartTowYData = [];

            for (var i = 1; i <= date.getMonth()+1; i++) {
                ChartXOneData.push(i)
                ChartXTowata.push(i)
                ChartOneYData.push(Math.floor(Math.random() * 90) + 10)
                ChartTowYData.push(Math.floor(Math.random() * 90) + 10)
            }
            

            var ChartOne = echarts.init(document.getElementById('ChartOne'));
            ChartOne.setOption({
                xAxis: {
                    type: 'category',
                    data: ChartXOneData,
                    axisLine: {
                        lineStyle: {
                            color: '#5bc0de'  // 设置 y 轴线颜色为黑色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#fff',
                            fontWeight: "bold"
                        }
                    },
                },
                toolbox: {
                    show: true,
                    feature: {
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ['line', 'bar'] },
                        restore: { show: true },
                        saveAsImage: { show: true }
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                grid: {
                    left: '2%',
                    right: '2%',
                    bottom: '5%',
                    containLabel: true
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        show: true,  // 是否显示坐标轴轴线。
                        lineStyle: {
                            color: '#5bc0de'  // 设置 x 轴线颜色为黑色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#fff',
                            fontWeight: "bold"
                        }
                    },
                    axisTick: {
                        show: true,  // 是否显示坐标轴刻度。
                    },
                    splitLine: {
                        show: false
                    },
                },
                series: [
                    {
                        name: "直通率",
                        data: ChartOneYData,
                        type: 'line',
                        symbol: 'circle',     //设定为实心点
                        symbolSize: 8,   //设定实心点的大小
                        itemStyle: {
                            normal: {
                                color: '#5bc0de', //改变折线点的颜色
                                
                            }
                        },
                        label: {
                            fontSize: "12",
                            show: true,
                            color: "white",
                            fontWeight: "bold",
                            formatter(param) {
                                return param.value + "%"
                            },
                        },
                    }
                ]
            })

            var ChartTow = echarts.init(document.getElementById('ChartTow'));
            ChartTow.setOption({
                xAxis: {
                    type: 'category',
                    data: ChartXTowata,
                    axisLine: {
                        lineStyle: {
                            color: '#5bc0de'  // 设置 y 轴线颜色为黑色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#fff',
                            fontWeight: "bold"
                        }
                    },
                },
                toolbox: {
                    show: true,
                    feature: {
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ['line', 'bar'] },
                        restore: { show: true },
                        saveAsImage: { show: true }
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                grid: {
                    left: '2%',
                    right: '2%',
                    bottom: '5%',
                    containLabel: true
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        show: true,  // 是否显示坐标轴轴线。
                        lineStyle: {
                            color: '#5bc0de'  // 设置 x 轴线颜色为黑色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#fff',
                            fontWeight: "bold"
                        }
                    },
                    axisTick: {
                        show: true,  // 是否显示坐标轴刻度。
                    },
                    splitLine: {
                        show: false
                    },
                },
                series: [
                    {
                        name: "维修率",
                        data: ChartTowYData,
                        type: 'line',
                        symbol: 'circle',     //设定为实心点
                        symbolSize: 8,   //设定实心点的大小
                        itemStyle: {
                            normal: {
                                color: '#5bc0de', //改变折线点的颜色

                            }
                        },
                        label: {
                            fontSize: "12",
                            show: true,
                            color: "white",
                            fontWeight: "bold",
                            formatter(param) {
                                return param.value + "%"
                            },
                        },
                    }
                ]
            })

            $(window).resize(function () {
                ChartOne.resize();
                ChartTow.resize()
            })
        }

    </script>

    <style>
        * {
            margin: 0;
            padding: 0;
            list-style: none;
            text-decoration: none;
        }

        .container-div {
            height: 100vh;
            background: #15181d;
            overflow: hidden;
            position: relative;
            color: #ffffff;
        }

        .container-top {
            height: 47.75%;
        }

        .container-bottom {
            margin-top:1.5%;
            height: 47.75%;
        }



        .left {
            float: left;
        }

        .div_any01 {
            width: 23%;
            margin-right: 2%;
        }

        .div_any_child {
            width: 100%;
            height: 330px;
            box-shadow: -5px 0px 10px #034c6a inset, 0px -10px 10px #034c6a inset, 5px 0px 10px #034c6a inset, 0px 10px 10px #034c6a inset;
            border: 1px solid #034c6a;
            box-sizing: border-box;
            position: relative;
            margin-top: 15px;
        }

        .div_any_title {
            background-color: #034c6a;
            border-radius: 18px;
            position: absolute;
            height: 25px;
            width: 60%;
            top: -15px;
            color: #ffffff;
            font-weight: bold;
            font-size: 16px;
            left: 20%;
            line-height: 25px;
            text-align: center;
        }


    </style>
</head>


<body>
    <div class="container-div">
        <div class="container-top">
            <div class="left div_any01" style="width:100%;height:100%">
                <div class="div_any_child" style="height: 100%;">
                    <div class="div_any_title">直通率</div>
                    <div id="ChartOne" style="width:100%;height:100%"></div>
                </div>
            </div>
        </div>
        <div class="container-bottom">
            <div class="left div_any01" style="width:100%;height:100%">
                <div class="div_any_child" style="height: 100%;">
                    <div class="div_any_title">维修率</div>
                    <div id="ChartTow" style="width:100%;height:100%"></div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>