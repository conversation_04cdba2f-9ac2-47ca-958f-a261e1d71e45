﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using DAL;

namespace BLL
{
    public class TechBll
    {


        // 判断信息是否存在
        public static string JudgeObjectExist(string Kind, string KindList, string QT, string sComp, string sFlag)
        {
            return TechDal.JudgeObjectExist(Kind, KindList, QT, sComp, sFlag);
        }

        // 获取工艺相关的信息
        public static DataTable GetTechInfo(string No, string Item, string Name, string MNo, string MName, string Status, string BDate, string EDate, string A, string B, string C, string D, int Row, int num, string sInMan, string sComp, string sFlag)
        {
            return TechDal.GetTechInfo(No, Item, Name, MNo, MName, Status, BDate, EDate, A, B, C, D, Row, num, sInMan, sComp, sFlag);
        }


        // 操作工艺相关信息
        public static string OPTechInfo(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string G, string H, string I, string J, string K, string L, string M, string N, string O, string P, string sComp, string InMan, string Remark, string sFlag,string IP)
        {
            return TechDal.OPTechInfo(No, Name, Item, MNo, MName, A, B, C, D, E, F, G, H, I, J, K, L, M, N, O, P, sComp, InMan, Remark, sFlag, IP);
        }



        //获取样本量字码
        public static DataTable GetSampleSizeCode(int limit, int page, string Flag, string InMan)
        {
            return TechDal.GetSampleSizeCode(limit, page, Flag, InMan);
        }

        //操作样本量字码
        public static string OPSampleSizeCodeInfo(string sSCNo, string sStrictness, string sBatch, string sBatch_start, string sBatch_end, string sInspection_level, string sSample_code, string sComp, string sMan, string sRemark, string sPresentOrder, string sOPFlag, string sFlag)
        {
            return TechDal.OPSampleSizeCodeInfo(sSCNo, sStrictness, sBatch, sBatch_start, sBatch_end, sInspection_level, sSample_code, sComp, sMan, sRemark, sPresentOrder, sOPFlag, sFlag);
        }


        //获取抽样标准
        public static DataTable GetSamplingStd(int limit, int page, string Flag, string InMan)
        {
            return TechDal.GetSamplingStd(limit, page, Flag, InMan);
        }

        //操作抽样标准
        public static string OPSamplingStd(string sSSNo, string sSampleCode, string sAql, string sStringency, string sSampleSize, string sRNum, string sBNum, string sComp, string sMan, string sRemark, string sPresentOrder, string sOPFlag, string sFlag)
        {
            return TechDal.OPSamplingStd(sSSNo, sSampleCode, sAql, sStringency, sSampleSize, sRNum, sBNum, sComp, sMan, sRemark, sPresentOrder, sOPFlag, sFlag);
        }

        //获取抽样方案
        public static DataTable GetSamplingPlan(int limit, int page, string sMan, string SPNo, string SPName, string Flag)
        {
            return TechDal.GetSamplingPlan(limit, page, sMan, SPNo, SPName, Flag);
        }

        //操作抽样方案
        public static string OPSamplingPlan(string SPNo, string SPName, string Type, string InspectLevel, string Stringency, string Aql, string Comp, string InMan, string Flag, string OPFlag)
        {
            return TechDal.OPSamplingPlan(SPNo, SPName, Type, InspectLevel, Stringency, Aql, Comp, InMan, Flag, OPFlag);
        }

        public static List<DataTable> GetExportConditions(string sNo, string A, string B, string C, string sInMan, string Comp, string Flag)
        {
            return TechDal.GetExportConditions(sNo, A, B, C, sInMan, Comp, Flag);
        }

        public static string InitializeMater(string sNo, string sSNo, string sMNo, string A, string B, string C, string D, string E, string F, string G, string sRemark, string sMan, string sFlag,string sComp,string IP)
        {
            return TechDal.InitializeMater(sNo, sSNo, sMNo, A, B, C, D, E, F, G, sRemark, sMan, sFlag, sComp, IP);
        }
















    }
}
