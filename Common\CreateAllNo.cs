﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Common
{
    public class CreateAllNo
    {
       #region  按要求有规则的编号
        /// <summary>
        /// 按要求，生成有规则的编号。
        /// </summary>
        /// <param name="sFlag">sFlag=1生成（XX+20151223+流水）=2：XX+151223+流水；=3：XX+201512+流水；=4：XX+1512+流水；=5：15122314+流水</param>
        /// <param name="icount">需要生成多少个 0 ，补充流水号位数</param>
        /// <returns></returns>
        public static string CreateBillNo(int sFlag,int icount)
        {
            string sSer = "0000000000000000000000000000";
            string sStr = string.Empty;
            string sTemp = string.Empty;

           

            if (sFlag == 1)
            {
                sTemp = DateTime.Now.ToString("yyyyMMdd"); // 20151223
                sStr = sTemp + sSer.Substring(1, icount);
            }
            else if (sFlag == 2)
            {
                sTemp = DateTime.Now.ToString("yyMMdd"); // 151223
                sStr = sTemp + sSer.Substring(1, icount);
            }
            else if (sFlag == 3)
            {
                sTemp = DateTime.Now.ToString("yyyyMM"); // 201512
                sStr = sTemp + sSer.Substring(1, icount);
            }
            else if (sFlag == 4)
            {
                sTemp = DateTime.Now.ToString("yyMM"); // 1512
                sStr = sTemp + sSer.Substring(1, icount);
            }
            else if (sFlag == 5)
            {
                sTemp = DateTime.Now.ToString("yyMMddhh"); // 15122314（14时）
                sStr = sTemp + sSer.Substring(1, icount);
            };


            return sStr;
        }

        #endregion



        /// <summary>
        /// 获取时间字符串
        /// </summary>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string GetDateString(int sFlag)
        {
            string sStr = string.Empty;

            if (sFlag == 1)
            {
                sStr = DateTime.Now.ToString("yyyyMMdd"); // 20151223
            }
            else if (sFlag == 2)
            {
                sStr = DateTime.Now.ToString("yyMMdd"); // 151223
            }
            else if (sFlag == 3)
            {
                sStr = DateTime.Now.ToString("yyyyMM"); // 201512
            }
            else if (sFlag == 4)
            {
                sStr = DateTime.Now.ToString("yyMM"); // 1512
            }
            else if (sFlag == 5)
            {
                sStr = DateTime.Now.ToString("yyMMddhh"); // 15122314（14时）
            };


            return sStr;
        }


        /// <summary>
        /// 获取0 的个数
        /// </summary>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string GetZoreNumString(int icount)
        {


            string sStr = string.Empty;
            string sSer = "0000000000000000000000000000";

            sStr = sSer.Substring(0, icount);

            return sStr;
        }
















    }

}
