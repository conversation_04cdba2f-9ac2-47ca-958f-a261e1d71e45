﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>IPQC巡查执行记录</title>

    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <link href="../css/orderinfo.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <script src="../layui/layui.js" type="text/javascript"></script>
    <link href="../js/skin/layer.css" rel="stylesheet" />

    <script src="../js/layer/layer.js" type="text/javascript"></script>
    <script src="../js/PrdAssist.js" type="text/javascript"></script>

    <link href="../css/XC.css" rel="stylesheet" />

    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }
        //
        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        $('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=28-1' });
                    }
                }
            })
        });

    </script>


    <script type="text/javascript">
        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#InspectEXElist',
                id: 'InspectEXElistID',
                url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=22',
                height: 'full-80',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'ENo', title: '巡查编号', width: 150, sort: true },
                    { field: 'Type', title: '类型', width: 100, sort: true },
                    { field: 'IPNo', title: '巡查计划编号', width: 100 },
                    { field: 'Ver', title: '版本', width: 80 },
                    { field: 'Status', title: '状态', width: 80 },
                    { field: 'InMan', title: '创建人', width: 90 },
                    { field: 'InDate', title: '创建时间', width: 150, sort: true },
                    { field: 'op', title: '操作', width: 130, toolbar: '#barDemo', fixed: 'right' }
                ]],
                page: true,

                //ENo, Type, IPNo, Ver, InMan, InDate
            });

            //监听是否选中操作
            table.on('checkbox(InspectEXElist)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);

            });


            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(InspectEXElist)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

            });

            //监听单元格编辑
            table.on('edit(InspectEXElist)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });


            //监听行工具事件
            table.on('tool(InspectEXElist)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值
                if (layEvent === 'del') {
                    if (data.Status != "未执行") {
                        layer.msg('执行中/已完成数据，不可以删除！！');
                        return;
                    }

                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("确定要删除该IPQC巡查执行记录吗？巡查编号：")

                    //设置删除的对象
                    $("#hint-value").html(data.ENo )

                    $("#txtDelENo").val(data.ENo)

                }
                else if (layEvent === 'detail')//显示明细页面
                {
                    $('#head-title1').html("IPQC巡查执行详情");
                    $('#txtAEFlag').val("22-1");




                    $("#txtENo").val(data.ENo);
                    $("#txtIERType").val(data.Type);
                    $("#txtIERIPNo").val(data.IPNo);
                    $("#txtIERVer").val(data.Ver);

                    $("#txtIERInMan").val(data.InMan);

                    $("#txtIERInDate").val(data.InDate);

                    $("#txtIERStatus").val(data.Status);

                    document.getElementById('btn_CRCP_open').style.display = 'none';
                    //if (data.Status == "执行中" || data.Status == "已完成") {
                    //    document.getElementById('btn_CRCP_open').style.display = 'none';
                    //}

                    $("#div_warning").html("");
                    $("#div_warning").hide();

                    //弹窗显示IPQC巡查执行记录明细页
                    ShowInspectEXEDetail(data.ENo);

                    /*document.getElementById('div_InspectPlanInfoSelect').style.display = 'block';*/
                    $('#ShowOne').css("display", "block")
                    $('#ShowOne-fade').css("display", "block")


                    $('#btn_InspectEXEDetailSave').hide();
                    $("#div_IPQCPDFList").show()
                    GetIPQCPdf()
                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }


            });


            //  查询表头 --
            $('#InspectEXEBut_open').click(function () {

                var sQPNo = $("#txtSENo").val();  //IPQC巡查执行记录编号
                var sVer = encodeURI($("#txtSType").val());  //工单号


                var Data = '';
                var Params = { No: sQPNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: sVer, B: "", C: "", D: "", E: "" };
                var Data = JSON.stringify(Params);

                table.reload('InspectEXElistID', {
                    method: 'post',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=22&Data=' + Data,
                    where: {
                        'No': sQPNo,
                        'name': sVer
                    }, page: {
                        curr: 1
                    }
                });
            });


            $("#btn_InspectEXEDetailDel").click(function () {
                //向服务端发送禁用指令
                var sENo = $("#txtDelENo").val();
                //var sVer = data.Ver;
                var sFlag = "41";

                var Data = '';
                var Params = { No: sENo, Ver: "", A: "", B: "", C: "", D: "", E: "", F: "", I: "", J: "", K: "", L: "", Kind: "", Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);
                $.ajax({
                    type: "POST",
                    url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);


                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('删除成功！');

                            $('#InspectEXEBut_open').click();  // 重新查询

                            closeDelDialog()


                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                            layer.msg('执行中/已完成巡查记录，不可以删除！');

                        }
                        else {
                            layer.msg('删除失败，请重试！');
                            $('#InspectEXEBut_open').click();  // 重新查询
                        }
                    },
                    error: function (data) {
                        layer.msg('删除失败2，请重试！');
                        $('#InspectEXEBut_open').click();  // 重新查询
                    }
                });
            })



        });

        function openDialog(n) {
            if (n == 1) { // 新增IPQC巡查执行记录弹窗
                $('#head-title1').html("新增IPQC巡查执行信息");
                $('#txtAEFlag').val("0");

                $("#txtENo").val("系统自动产生");
                $("#txtIERType").val("");
                $("#txtIERIPNo").val("");

                $("#txtIERVer").val("");

                $("#txtIERInMan").val($('#txtInMan').val());

                //$("#txtIERInDate").val("");

                $("#txtIERStatus").val("");

                var date = new Date();
                var datetime = date.toLocaleString(); // 获取本地时间
                $("#txtIERInDate").val(datetime);

                $("#div_warning").html("");
                $("#div_warning").hide();

                ShowInspectPlanDetailAdd("", "");


                document.getElementById('btn_CRCP_open').style.display = 'block';

                $("#ShowOne").css("display", "block")
                $("#ShowOne-fade").css("display", "block")

                $("#div_IPQCPDFList").hide()
                $('#btn_InspectEXEDetailSave').show();
            }
            else if (n == 2)//选择IPQC巡查计划弹窗层
            {
                $('#head-title2').html("选择IPQC巡查计划");
                $('#txtAEFlag').val("0");

                layui.use('table', function () {
                    var table = layui.table,
                        form = layui.form;
                    table.render({
                        elem: '#table_InspectPlanSelectList',
                        id: 'InspectPlanSelectListID',
                        url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=21',
                        height: '450',
                        cellMinWidth: 80,
                        //toolbar: 'default',//工具栏
                        //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                        count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                        limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                        limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                        cols: [[
                            { type: 'numbers' },
                            { field: 'IPNo', title: 'IPQC巡查计划编号', width: 200 },
                            { field: 'Ver', title: '版本', width: 100 },
                            { field: 'Type', title: '巡查类型', width: 200 },
                            { field: 'Remark', title: '备注', width: 300 },
                            { field: 'InMan', title: '创建人', width: 90 },
                            { field: 'InDate', title: '创建时间', width: 200 },
                            { field: 'op', title: '操作', width: 180, toolbar: '#barDemo_IPDetail', fixed: 'right' }
                        ]],
                        page: true,

                        //ItemNo, ItemName, CFPoint, BuildFloor, Range, Proof, Compliant, Cause, EXEMan, EXEDate, SNo
                    });


                    //监听行工具事件
                    table.on('tool(table_InspectPlanSelectList)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                        var data = obj.data, //获得当前行数据
                            layEvent = obj.event; //获得 lay-event 对应的值
                        if (layEvent === 'IPSelect') {//选择IPQC巡查计划后，同步保存IPQC巡查执行内容清单

                            ShowInspectPlanDetailAdd(data.IPNo, data.Ver);
                            $("#txtIERIPNo").val(data.IPNo);
                            $("#txtIERVer").val(data.Ver);
                            $("#txtIERType").val(data.Type);
                            closeDialog(1);
                        }
                        else if (layEvent === 'IPDetail')//显示明细页面
                        {
                            $('#head-title3').html("IPQC巡查计划信息详情");
                            //$('#txtAEFlag').val("11");

                            $("#txtItemIPDNo").val(data.IPNo);
                            $("#txtItemIPDVer").val(data.Ver);


                            $("#div_warningDetail").html("");
                            $("#div_warningDetail").hide();
                            //弹窗显示IPQC巡查计划明细页
                            ShowInspectPlanDetailList(data.IPNo, data.Ver);

                            $('#ShowThree').css("display", "block")
                            


                            // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                        }

                    });

                    //  查询IPQC巡查计划
                    $('#InspectPlanBut_open').click(function () {

                        var sIPNo = $("#txtSIPNo").val();  //
                        var sVer = encodeURI($("#txtSVer").val());  //


                        var Data = '';
                        var Params = { No: sIPNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: sVer, B: "", C: "", D: "", E: "" };
                        var Data = JSON.stringify(Params);

                        table.reload('InspectPlanSelectListID', {
                            method: 'post',
                            url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=21&Data=' + Data,
                            where: {
                                'No': sIPNo,
                                'name': sVer
                            }, page: {
                                curr: 1
                            }
                        });
                    });


                });

                $("#div_warningSelectInspectPlanInfo").html("");
                $("#div_warningSelectInspectPlanInfo").hide();


                $('#ShowTow').css("display", "block")
            }
            else if (n = 3)//新增IPQC巡查结果，数据录入弹窗
            {

                $("#txtAEFlag").val("40");

                $('#ShowFour').css("display", "block")
            }
        }

        function closeDialog(s) {
            if (s == 1)//关闭选择工单弹层页面
            {
                $('#ShowTow').css("display", "none")
            }
            else if (s == 2)//关闭详细页面
            {
                $('#ShowOne').css("display", "none")
                $('#ShowOne-fade').css("display", "none")

                $('#ShowTow').css("display", "none")

                $('#InspectEXEBut_open').click();  // 重新查询
            }
            else if (s = 3) {
                $('#ShowThree').css("display", "none")

                $('#ShowFour').css("display", "none")
            }


        }


        //自动更新单据状态
        function GetPrdAssistStatusForNoet(sENo) {
            //var txtENo = $("#txtENo").val();
            sURL = '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistStatusForNo&CFlag=67&CNO=' + sENo;
            var Data = '';
            $.ajax({
                url: sURL,
                data: { Data: Data },
                success: function (data) {                    //数据成功读取后的处理

                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.length > 0) {
                        //sCompNo = parsedJson[0].CompanyNo;
                        //sCompName = parsedJson[0].CompName;
                        $("#txtIERStatus").val(parsedJson[0].Status);
                        //$("#txtFWName").val(parsedJson[0].CompName);
                    }
                },
                error: function (xhr, status, error) {
                    //错误处理
                    $("body").append("<div>读取数据失败：" + error + "</div>");
                }
            });
        }


        /// 显示IPQC巡查执行执行记录明细项信息
        ///daiwanshan
        function ShowInspectEXEDetail(sENo) {
            $('#txtAEFlag').val("0");
            //var sQPNo = $("#txtCPDNo").val();  //
            //var sVer = encodeURI($("#txtCPDVer").val());  //

            var Data = '';
            var Params = { No: sENo, Ver: "", Item: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#table_InspectEXEDetailList',
                    id: 'InspectEXEDetaiID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=22-1&Data=' + Data,
                    height: '360',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    //ItemNo, ItemName, CFPoint, BuildFloor, Range, Proof, Compliant, Cause, EXEMan, EXEDate, SNo
                    cols: [[
                        { type: 'numbers' },
                        { field: 'ItemNo', title: '内容编号', width: 100 },
                        { field: 'ItemName', title: '巡查项目', width: 100 },
                        { field: 'CFPoint', title: '巡查确认点', width: 100 },
                        { field: 'BuildFloor', title: '楼层', width: 100 },
                        { field: 'Range', title: '区域/线体', width: 100 },
                        { field: 'Proof', title: '证据', width: 100 },
                        { field: 'Compliant', title: '是否符合', width: 100 },
                        { field: 'Cause', title: '不符原因', width: 100 },
                        { field: 'EXEMan', title: '巡查人', width: 100 },
                        { field: 'EXEDate', title: '巡查日期', width: 100 },
                        { field: 'Result', title: '巡查结果', width: 100 },
                        { field: 'SNo', title: '序号', width: 40 },

                        //{ field: 'InMan', title: '录入人', minWidth: 90 },
                        //{ field: 'InDate2', title: '录入时间', width: 150 },
                        { field: 'op', title: '操作', width: 180, toolbar: '#bar_OrderInspectPlanRocDetail', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(table_InspectEXEDetailList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值
                    //if (data.Result == "OK") {
                    //    layer.msg('巡查结果已确认完成，不必重复操作！');
                    //    return;
                    //}
                    if (layEvent == 'QCResult') {//IPQC巡查结果录入

                        var sNo = $("#txtENo").val();
                        var sItemNo = data.ItemNo;
                        var sResult = "OK";


                        $("#txtItemCompliant").removeProp("checked");

                        if (data.Compliant == "是") {
                            $("#txtItemCompliant").prop('checked', true);
                        }


                        $("#txtItemENo").val(sNo);
                        $("#txtItemNo").val(sItemNo);
                        $("#txtItemName").val(data.ItemName);
                        $("#txtItemCFPoint").val(data.CFPoint);


                        $("#txtItemBuildFloor").val(data.BuildFloor);
                        $("#txtItemRange").val(data.Range);

                        $("#txtItemProof").val(data.Proof);
                        //$("#txtItemCompliant").val(data.Compliant);
                        $("#txtItemCause").val(data.Cause);
                        $("#txtItemCFPoint").val(data.CFPoint);

                        openDialog(3);


                    }
                    else if (layEvent == 'QCEnd')//IPQC巡查结果结果确认
                    {
                        if (data.Result == "OK") {
                            layer.msg('巡查结果已确认完成，不必重复操作！');
                            return;
                        }

                        layer.confirm('真的结束该IPQC巡查执行内容吗', function (index) {
                            //向服务端发送IPQC巡查执行确认指令 sNo: ENo,Item:ItemNo, A:sResult

                            var sNo = $("#txtENo").val();
                            var sItemNo = data.ItemNo;
                            var sResult = "OK"

                            var sFlag = "40-1";

                            var Data = '';
                            var Params = { No: sNo, Name: "", Item: sItemNo, MNo: "", MName: "", A: sResult, B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: sFlag };
                            var Data = JSON.stringify(Params);

                            $.ajax({
                                type: "POST",
                                url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
                                data: { Data: Data },
                                success: function (data) {
                                    var parsedJson = jQuery.parseJSON(data);

                                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                        layer.msg('IPQC巡查执行确认操作成功！');
                                        ShowInspectEXEDetail(sNo);//重新显示IPQC巡查执行内容清单
                                        //$('#Fin_SearchOpen').click();
                                        //obj.del(); //删除对应行（tr）的结构，并更新缓存
                                        layer.close(index);
                                    }
                                    else {
                                        layer.msg('IPQC巡查执行确认操作失败，请重试！');
                                        // $('#Fin_SearchOpen').click();
                                    }
                                },
                                error: function (data) {
                                    layer.msg('IPQC巡查执行确认操作失败2，请重试！');
                                    // $('#Fin_SearchOpen').click();
                                }
                            });

                        });
                    }


                });


            });  // layui.use('table', function () {

            document.getElementById('div_table_InspectEXEDetailList').style.display = 'block';
            document.getElementById('div_table_InspectEXEDetailAdd').style.display = 'none';

        }

        /// 显示IPQC巡查计划明细项信息
        ///daiwanshan
        function ShowInspectPlanDetailAdd(sIPNo, sVer) {

            var Data = '';
            var Params = { No: sIPNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: sVer, B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#table_InspectEXEDetailAdd',
                    id: 'InspectPlanDetailAddID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=21-1&Data=' + Data,
                    height: '420',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers' },
                        { field: 'ItemNo', title: '内容编号', width: 100 },
                        { field: 'ItemName', title: '项目名称', width: 100 },
                        { field: 'CFPoint', title: '巡查确认点', width: 100 },
                        { field: 'BuildFloor', title: '楼层', width: 100 },
                        { field: 'Range', title: '区域/线体', width: 100 },
                        { field: 'Proof', title: '巡查证据', width: 100 },
                        { field: 'Compliant', title: '是否符合', width: 100 },
                        { field: 'Cause', title: '不符合原因', width: 100 },

                        { field: 'Remark', title: '备注', width: 100 },
                        { field: 'SNo', title: '序号', width: 40 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate', title: '录入时间', width: 150 }
                        //{ field: 'op', title: '操作', width: 230, toolbar: '#barDemo_Detail', fixed: 'right' }
                    ]],
                    page: true
                });


            });  // layui.use('table', function () {

            document.getElementById('div_table_InspectEXEDetailList').style.display = 'none';
            document.getElementById('div_table_InspectEXEDetailAdd').style.display = 'block';
        }



        /// 显示IPQC巡查计划明细项信息
        ///daiwanshan
        function ShowInspectPlanDetailList(sIPNo, sVer) {

            //var sQPNo = $("#txtCPDNo").val();  //
            //var sVer = encodeURI($("#txtCPDVer").val());  //
            var Data = '';
            var Params = { No: sIPNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: sVer, B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#table_InspectPlanDetailList',
                    id: 'InspectPlanDetailID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=21-1&Data=' + Data,
                    height: '400',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers' },
                        { field: 'ItemNo', title: '内容编号', width: 100 },
                        { field: 'ItemName', title: '项目名称', width: 100 },
                        { field: 'CFPoint', title: '巡查确认点', width: 100 },
                        { field: 'BuildFloor', title: '楼层', width: 100 },
                        { field: 'Range', title: '区域/线体', width: 100 },
                        { field: 'Proof', title: '巡查证据', width: 100 },
                        { field: 'Compliant', title: '是否符合', width: 100 },
                        { field: 'Cause', title: '不符合原因', width: 100 },

                        { field: 'Remark', title: '备注', width: 100 },
                        { field: 'SNo', title: '序号', width: 40 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate', title: '录入时间', width: 150 }
                        //{ field: 'op', title: '操作', width: 230, toolbar: '#barDemo_Detail', fixed: 'right' }
                    ]],
                    page: true
                });


            });  // layui.use('table', function () {


        }

        //IPQC巡查生成PDF
        function IPQCPDF() {
            var sENo = $("#txtENo").val()
            var sIERType = $("#txtIERType").val()
            var Params = { No: sENo, Name: sIERType, Item: "", Status: "", A: "IPQC", B: "", C: "", D: "", Remark: "", Flag: "100" };
            var Data = JSON.stringify(Params);

            $("#IPQCPDF").attr("disabled", true)

            $.ajax({
                url: "../Service/PrdAssistAjax.ashx?OP=GenerateRecordFile",
                type: "POST",
                data: {
                    Data: Data
                },
                success: function (res) {
                    var parsedJson = jQuery.parseJSON(res);
                    if (parsedJson.Msg == "LoginError") {
                        ErrorMessage("登录超时，请重新登录！", 2000);
                        $("#IPQCPDF").removeAttr("disabled")
                    } else if (parsedJson.Msg == "ParamsError") {
                        ErrorMessage("请求参数出错，请重试！", 2000);
                        $("#IPQCPDF").removeAttr("disabled")
                    } else if (parsedJson.Msg == "Success") {  //如过是Success没有生成过就直接生成
                        IPQCCreatePDF()
                    } else if (parsedJson.Msg == "Y_EXIST") {  //如过是Y_EXIST就说明已经生成过了一次则提示一下是否再次生成PDF
                        layer.confirm('该编号已经生成过，确定再次生成吗？', {
                            btn: ['确定', '取消'], // 按钮
                            cancel: function () {
                                $("#IPQCPDF").removeAttr("disabled")
                            }
                        }, function () {
                            IPQCCreatePDF();
                        }, function () {
                            $("#IPQCPDF").removeAttr("disabled")
                        });
                    }
                },
                error: function () {
                    ErrorMessage("系统出错，请重试！", 2000);
                    $("#IPQCPDF").removeAttr("disabled")
                    return;
                }
            })
        }

        //IPQC巡查生成PDF
        function IPQCCreatePDF() {
            var sENo = $("#txtENo").val()
            var sIERType = $("#txtIERType").val()
            var Params = { No: sENo, Name: sIERType, Item: "", Status: "", A: "", B: "", C: "", D: "", Remark: "", Flag: "XC" };
            var Data = JSON.stringify(Params);
            if (sENo == "") {
                ErrorMessage("获取不到巡查任务编号", 2000);
                return;
            }
            if (sIERType == "") {
                ErrorMessage("获取不到巡查类型", 2000);
                return;
            }

            $("#IPQCPDF").attr("disabled", true)

            $.ajax({
                url: "../Service/PrdAssistAjax.ashx?OP=GenerateRecordFile",
                type: "POST",
                data: {
                    Data: Data
                },
                success: function (res) {
                    var parsedJson = jQuery.parseJSON(res);
                    if (parsedJson.Msg == "ImportError") {
                        ErrorMessage("模板导入失败，请重试！", 2000);
                    } else if (parsedJson.Msg == "CreateError") {
                        ErrorMessage("生成失败！", 2000);
                    } else if (parsedJson.Msg == "Error") {
                        ErrorMessage("生成失败！" + parsedJson.ExceptionMessage, 2000);
                    } else if (parsedJson.Msg == "Success") {
                        layer.msg("生成成功")
                        GetIPQCPdf();
                    }
                    $("#IPQCPDF").removeAttr("disabled")
                },
                error: function () {
                    ErrorMessage("系统出错，请重试！", 2000);
                    $("#IPQCPDF").removeAttr("disabled")
                    return;
                }
            })
        }

        function GetIPQCPdf() {
            var sENo = $("#txtENo").val()
            var Data = '';
            var Params = { No: sENo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: "IPQC", B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);
            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#IPQCPDFList',
                    id: 'IPQCPDFListID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=28&Data=' + Data,
                    height: '280',
                    cellMinWidth: 80,
                    count: 50,
                    limit: 20,
                    limits: [10, 20, 30, 40, 50],
                    cols: [[
                        { type: 'numbers' },
                        { field: 'No', title: '编号', width: 200 },
                        { field: 'Name', title: '名称' },
                        { field: 'InMan', title: '录入人', width: 100 },
                        { field: 'InDate2', title: '录入时间', width: 170 },
                        { field: 'op', title: '操作', width: 100, toolbar: '#bar_IPQCPDFList', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(IPQCPDFList)', function (obj) {
                    var data = obj.data,
                        layEvent = obj.event;
                    if (layEvent == 'view') {
                        window.open(data.FilePath)
                    }
                })
            })
        }

        function closeDelDialog() {
            $('#ShowDel').css("display", "none")
            $('#ShowDel-fade').css("display", "none")
        }
    </script>


    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })
            $("#txtItemCompliant").change(function () {
                var checkbox = $(this);
                if (checkbox.is(":checked")) {
                    $("#txtItemCause").val("/")
                } else {
                    $("#txtItemCause").val("")
                }
            })
        })



    </script>

    <style type="text/css">

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        /* .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: #bbbcc7;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=60);
        }*/

        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }

        #ShowOne .XC-Form-block-Item span, #ShowFour .XC-Form-block-Item span{
            width: 107px;
        }
    </style>

</head>
<body>
    <div class="div_find">

        <p>
            <label class="find_labela">IPQC巡查执行记录编号</label> <input type="text" id="txtSENo" class="find_input" />
            <label class="find_labela">类型</label><input type="text" id="txtSType" class="find_input" />
            <!--<label class="find_labela">描述</label><input type="text" id="txtSCPTxt" class="find_input" />-->
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="InspectEXEBut_open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="openDialog(1)">添加</button>
        </p>
    </div>

    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="InspectEXElist" lay-filter="InspectEXElist"></table>

        <script type="text/html" id="barDemo">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detail">详情</button>
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>
        </script>

    </div>

    <!--IPQC巡查执行记录详情弹窗层-->
    <div class="XC-modal XC-modal-xl" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1">IPQC巡查执行记录详情</span>
            <span class="head-close" onclick="closeDialog(2)">X</span>
        </div>
        <div class="XC-modal-body" style="padding:10px">
            <div class="XC-modal-xl-center">
                <div style="border-bottom: solid 1px #ccc;padding-bottom:12px">
                    <span style="font-weight: bold; ">IPQC巡查执行记录基本信息</span>
                </div>

                <div style="display:flex;margin-top:10px">
                    <div style="width:100%">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">巡查任务编号</span>
                                <input type="text" class="XC-Input-block" id="txtENo" name="txtENo" readonly=readonly placeholder="系统自动产生" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">IPQC巡查计划编码</span>
                                <input type="text" class="XC-Input-block" id="txtIERIPNo" name="txtIERIPNo" readonly=readonly />
                                <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="btn_CRCP_open" onclick="openDialog(2)" style="margin-left:5px;">选择</button>
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">创建人</span>
                                <input type="text" class="XC-Input-block" id="txtIERInMan" name="txtIERInMan" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">状态</span>
                                <input type="text" class="XC-Input-block" id="txtIERStatus" name="txtIERStatus" readonly=readonly />
                            </div>
                        </form>
                    </div>
                    <div style="width:100%">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">巡查类型</span>
                                <input type="text" class="XC-Input-block" id="txtIERType" name="txtIERType" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">版本</span>
                                <input type="text" class="XC-Input-block" id="txtIERVer" name="txtIERVer" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">创建日期</span>
                                <input type="text" class="XC-Input-block" id="txtIERInDate" name="txtIERInDate" readonly=readonly />
                            </div>
                            <!--<div class="XC-Form-block-Item">
                    <span class="XC-Span-Textarea-block">创建日期</span>
                    <input type="text" class="XC-Input-block" id="txtIERInDate" name="txtIERInDate" readonly=readonly />
                </div>-->
                        </form>
                    </div>
                </div>

                <div style="border-bottom: solid 1px #ccc;padding-bottom:12px">
                    <span style="font-weight: bold; ">IPQC巡查执行记录内容列表</span>
                </div>

                <div class="wangid_conbox" id="div_table_InspectEXEDetailList" style="display:none; ">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="table_InspectEXEDetailList" lay-filter="table_InspectEXEDetailList"></table>

                    <script type="text/html" id="bar_OrderInspectPlanRocDetail">
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="QCResult" style="width:70px;">巡查记录</button>
                        <!--<button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="QCEnd">结果确认</button>-->
                    </script>
                </div>
                <div class="wangid_conbox" id="div_table_InspectEXEDetailAdd" style="display:none; ">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="table_InspectEXEDetailAdd" lay-filter="table_InspectEXEDetailAdd"></table>
                </div>

                <div class="wangid_conbox" id="div_IPQCPDFList">
                    <div style="display:flex;justify-content:space-between;border-bottom: solid 1px #ccc;padding-bottom:3px;margin-top:15px">
                        <span style="font-weight: bold; ">IPQC巡查记录文件</span>
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="IPQCPDF" onclick="IPQCPDF()" style="width:70px">生成PDF</button>
                    </div>
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="IPQCPDFList" lay-filter="IPQCPDFList"></table>

                    <script type="text/html" id="bar_IPQCPDFList">
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="view">查看</button>
                    </script>

                </div>
            </div>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="btn_InspectEXEDetailSave">确定</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="btn_InspectEXEDetailClose" onclick="closeDialog(2)">取消</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>

    <!--选择IPQC巡查计划弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowTow">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title2">IPQC巡查计划搜索</span>
            <span class="head-close" onclick="closeDialog(2)">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <div>
                    <div style=" font-weight: bold; padding-bottom: 10px;margin-bottom:10px;border-bottom: solid 1px #ccc;">
                        查询
                    </div>
                    <div class="div_find">
                        <p>
                            <label class="find_labela">IPQC巡查计划编号</label> <input type="text" id="txtSIPNo" class="find_input" style="width:15%" />
                            <label class="find_labela">版本号</label><input type="text" id="txtSVer" class="find_input" style="width:15%" />
                            <!--<label class="find_labela">描述</label><input type="text" id="txtSCPTxt" class="find_input" />-->
                            <input type="button" value="搜索" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="InspectPlanBut_open">
                        </p>
                    </div>
                </div>
                <div style=" font-weight: bold;padding-bottom:10px;margin-top:10px;">
                    IPQC巡查计划列表
                </div>

                <div class="wangid_conbox">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="table_InspectPlanSelectList" lay-filter="table_InspectPlanSelectList"></table>

                    <script type="text/html" id="barDemo_IPDetail">
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="IPDetail">详情</button>
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="IPSelect">选择</button>
                    </script>

                </div>

            </div>
        </div>
        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="btn_SelectInspectPlanInfoClose" onclick='closeDialog(1)'>关闭</button>
            </div>
        </div>
    </div>



    <!--IPQC巡查计划内容清单弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowThree">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title3">IPQC巡查计划详情</span>
            <span class="head-close" onclick="closeDialog(3)">X</span>
        </div>
        <div class="XC-modal-body">
            <div style=" font-weight: bold;padding-bottom:10px;">
                IPQC巡查计划
            </div>
            <div class="XC-modal-xl-center">
                <form class="XC-Form-block" style="margin:0px;">
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">计划编号</span>
                        <input type="text" class="XC-Input-block" id="txtItemIPDNo" name="txtItemIPDNo" readonly="readonly" placeholder="系统自动产生" value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">版本</span>
                        <input type="text" class="XC-Input-block" id="txtItemIPDVer" name="txtItemCPDDVer" readonly=readonly value="" />
                    </div>
                </form>
                <div class="wangid_conbox">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="table_InspectPlanDetailList" lay-filter="table_InspectPlanDetailList"></table>
                </div>
            </div>
        </div>
    </div>


    <!--显示质量执行结果维护弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowFour">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title4">IPQC巡查执行结果</span>
            <span class="head-close" onclick="closeDialog(3)">X</span>
        </div>

        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">控制执行编号</span>
                    <input type="text" class="XC-Input-block" id="txtItemENo" name="txtItemENo" readonly=readonly placeholder="系统自动产生" value="" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">执行内容编号</span>
                    <input type="text" class="XC-Input-block" id="txtItemNo" name="txtItemNo" readonly=readonly value="" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">执行内容</span>
                    <input type="text" class="XC-Input-block" id="txtItemName" name="txtItemName" readonly=readonly value="" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">巡查确认点<span class="XC-Font-Red">*</span></span>
                    <input type="text" class="XC-Input-block" id="txtItemCFPoint" name="txtItemCFPoint" value="" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">楼层<span class="XC-Font-Red">*</span></span>
                    <input type="text" class="XC-Input-block" id="txtItemBuildFloor" name="txtItemBuildFloor" value="" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">区域/线体<span class="XC-Font-Red">*</span></span>
                    <input type="text" class="XC-Input-block" id="txtItemRange" name="txtItemRange" value="" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">证据<span class="XC-Font-Red">*</span></span>
                    <input type="text" class="XC-Input-block" id="txtItemProof" name="txtItemProof" value="" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">是否符合</span>
                    <input type="checkbox" id="txtItemCompliant" name="txtItemCompliant" value="" style="margin:0px" />
                </div>

                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">不符合原因<span class="XC-Font-Red">*</span></span>
                    <input type="text" class="XC-Input-block" id="txtItemCause" name="txtItemCause" value="" />
                </div>
            </div>
        </div>
        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="btn_InspectExtResultEditSave">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="btn_InspectExtResultEditsaveClose" onclick='closeDialog(3)'>关闭</button>
            </div>
        </div>
    </div>

    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel" style="z-index:1003">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDelDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelENo" name="txtDelENo" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="btn_InspectEXEDetailDel">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDelDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay" style="z-index:1002">
    </div>


    <!--消息提示-->
    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>


</body>
</html>