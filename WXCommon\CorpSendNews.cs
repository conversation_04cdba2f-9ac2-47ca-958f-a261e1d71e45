﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace WXCommon
{
    // <summary>
    /// 企业号发送新闻消息
    /// </summary>
    /// 
    public class CorpSendNews
    {

        public CorpSendNews()
        { }

        public CorpSendNews(string News)
        {

        }

        /// <summary>
        /// UserID列表（消息接收者，多个接收者用‘|’分隔）。特殊情况：指定为@all，则向关注该企业应用的全部成员发送
        /// </summary>
        public string touser { get; set; }

        /// <summary>
        /// PartyID列表，多个接受者用‘|’分隔。当touser为@all时忽略本参数
        /// </summary>
        public string toparty { get; set; }

        /// <summary>
        /// TagID列表，多个接受者用‘|’分隔。当touser为@all时忽略本参数
        /// </summary>
        public string totag { get; set; }

        /// <summary>
        /// 消息类型
        /// </summary>
        public string msgtype { get; set; }

        /// <summary>
        /// 企业应用的id，整型。可在应用的设置页面查看
        /// </summary>
        public string agentid { get; set; }

        public news news { get; set; }
    }

    public class news
    {
        public news() { }

        public news(articles[] _articles)
        {
            this.articles = _articles;
        }

        public articles[] articles { get; set; }
    }


    public class articles
    {
        public articles() { }

        public articles(string _title, string _description, string _url, string _picurl)
        {
            this.title = _title;
            this.description = _description;
            this.url = _url;
            //this.picurl = _picurl;  // 这个参数可以发送图片的
        }



        public string title { get; set; }
        public string description { get; set; }
        public string url { get; set; }
        //public string picurl { get; set; }
    }


    // 发料有链接的新闻信息

    //{
    //   "touser": "wudong001",
    //   "totag": " 0 ",
    //   "msgtype": "news",
    //   "agentid": 6,
    //   "news": {
    //       "articles":[
    //           {
    //               "title": "Title",
    //               "description": "Description",
    //               "url": "URL",
    //               "picurl": "PIC_URL"
    //           },
    //           {
    //               "title": "吴东你好",
    //               "description": "吴东你好，明天请你吃饭",
    //               "url": "www.baidu.com",
    //               "picurl": "PIC_URL"
    //           }    
    //       ]
    //   }
    //}




}