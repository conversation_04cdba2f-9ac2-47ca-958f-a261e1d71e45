﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <script src="../js/ajaxfileupload.js" type="text/javascript"></script>
    <link href="../css/XC.css" rel="stylesheet" />
    <!--layui css-->
    <link rel="stylesheet" href="/css/layuiM.css" />
    <!--layui js-->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <!--<script src="../js/layer/layer.js" type="text/javascript"></script>-->
    <script src="../js/Order.js"></script>

    <style>
        #loading .layui-icon {
            font-family: layui-icon !important;
            font-size: 40px;
            font-style: normal;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
            font-size: 12px
        }

        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }

        .layui-table-click-bg {
            background-color: #0c873d;
            color: white
        }

        #ShowTow .XC-Span-Input-block, #ShowTow .XC-Span-Select-block {
            width: 75px;
        }

        /* 去除移入行时的背景色 */
        .TestItemInfo .layui-table-hover {
            background-color: #0c873d !important;
            color: white;
            cursor: pointer;
        }

        #Replace .XC-Form-block-Item {
            margin-bottom: 23px
        }

        .layui-table, .layui-table-view {
            margin: 0px 0;
        }
    </style>

    <script>
        //获取当前所在的模块、菜单
        const currentTabId = sessionStorage.getItem("currentTabId");
        const ModuleName = currentTabId != null ? JSON.parse(currentTabId).ModuleName : null;

        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;

            var temp_table_list = [];
            var temp_all_list = [];

            table.render({
                elem: '#OrderInfo_SNList',
                id: 'OrderInfo_SNList',
                url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=241',
                height: 'full-80',
                cellMinWidth: 80,
                count: 50,
                limit: 20,
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    //{ type: 'checkbox' },
                    { field: 'SerielNo', title: '序列号', minWidth: 180 },
                    { field: 'MaterNo', title: '物料编码', width: 150 },
                    { field: 'MaterName', title: '物料名称', width: 280 },
                    { field: 'Model', title: '型号', width: 120 },
                    { field: 'OrderNo', title: '工单', minWidth: 130 },
                    { field: 'InMan', title: '发号人', width: 80 },
                    { field: 'InDate2', title: '发号时间', width: 150 },
                ]],
                page: true,
                done: function (res) {
                    temp_table_list = res.data;
                    temp_table_list.forEach(function (o) {
                        temp_all_list.forEach(function (selected) {
                            if (selected.SerielNo === o.SerielNo) {
                                o["LAY_CHECKED"] = true;
                                var index = o['LAY_TABLE_INDEX'];
                                $('.layui-table tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', true).next().addClass('layui-form-checked');
                            }
                        });
                    });
                }
            });

            table.on('checkbox(OrderInfo_SNList)', function (obj) {
                if (obj.checked) {
                    if (obj.type === 'one') {
                        var index = temp_all_list.findIndex(function (item) {
                            return item.SerielNo == obj.data.SerielNo;
                        });
                        if (index == -1) {
                            temp_all_list.push(obj.data);
                        }
                    } else {
                        temp_table_list.forEach(function (o) {
                            if (temp_all_list.findIndex(function (item) { return item.SerielNo == o.SerielNo; }) == -1) {
                                temp_all_list.push(o);
                            }
                        });
                    }
                } else {
                    if (obj.type === 'one') {
                        temp_all_list = temp_all_list.filter(function (item) {
                            return item.SerielNo !== obj.data.SerielNo;
                        });
                    } else {
                        for (var i = 0; i < temp_table_list.length; i++) {
                            for (var j = 0; j < temp_all_list.length; j++) {
                                if (temp_table_list[i].SerielNo == temp_all_list[j].SerielNo) {
                                    temp_all_list.splice(j, 1)
                                }
                            }
                        }
                    }
                }
            });

            $("#OrderSerialInfo_open").click(function () {
                temp_table_list = []
                var sOrderNo = $("#txtSOrder").val()
                var Data = '';
                var Params = { No: sOrderNo, Item: "", Name: "", MNo: "", MName: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "" };
                var Data = JSON.stringify(Params);

                table.reload('OrderInfo_SNList', {
                    method: 'post',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=241&Data=' + Data,
                    page: {
                        curr: 1
                    }
                });
            })


            $("#AddDevice").click(function () {
                var result = InNullJudgment(temp_table_list)
                if (result == false) return
                $("#txtAddKind").val("AddDevice")
                $("#head-title1").html("批量添加设备")
            })

            $("#AddTestItem").click(function () {
                var result = InNullJudgment(temp_table_list)
                if (result == false) return
                $("#txtAddKind").val("AddTestItem")
                $("#head-title1").html("批量添加测试项")
            })

            $("#AddFile").click(function () {
                var result = InNullJudgment(temp_table_list)
                if (result == false) return
                $("#txtAddKind").val("AddFile")
                $("#head-title1").html("批量添加工艺文件")
            })

            $("#AddMater").click(function () {
                var result = InNullJudgment(temp_table_list)
                if (result == false) return
                $("#txtAddKind").val("AddMater")
                $("#head-title1").html("批量添加物料")
                $("#txtSProcedure").hide()
                $("#txtSProcedureVer").hide()
                $(".pv").hide()
                ShowWOBOMInfo($("#txtSOrder").val())
                $(".OrderInfo_BomList").show()
                $("#openAddMater").show()

            })

            $("#openAddMater").click(function () {
                $("#ShowTow").css("display", "block")
                $("#ShowTow-fade").css("display", "block")
                $("#txtRepeatFlag").removeProp("checked");
            })

            $("#closeDialog").click(function () {
                $("#ShowOne").css("display", "none")
                $("#ShowOne-fade").css("display", "none")
                $(".FileInfo").hide()
                $(".TestItemInfo").hide()
                $(".DeviceInfo").hide()
                $("#txtSProcedureVer").empty();
                $("#txtQueryCondition").val("")
                $("#query").hide()
                $("#txtSixItem").val("")
                $("#txtSProcedure").show()
                $("#txtSProcedureVer").show()
                $(".pv").show()
                $("#openAddMater").hide()
                $(".OrderInfo_BomList").hide()
            })


            $("#txtSProcedure").change(function () {
                var stxtOrderNo = $("#txtOrderNo").val()
                var sProductNo = $("#txtProductNo").val()
                var sProcedure = $("#txtSProcedure").val()
                GetProcedureVerList(stxtOrderNo, sProcedure)
                $("#txtSixItem").val("")
                var flag = $("#txtAddKind").val()
                if (flag == "AddDevice") {
                    ShowWODeviceInfo("", "", "")
                } else if (flag == "AddTestItem") {
                    //传 - 为了清空表格
                    ShowWOTestItemInfo("-", "-", "-")
                    ChoogeGetTestItemInfo("-", "-", "-" + "-", "2")
                } else if (flag == "AddFile") {
                    ShowOrderFileInfo("", "", "")
                }
            })


            $("#txtSProcedureVer").change(function () {

                $("#query").show()

                var flag = $("#txtAddKind").val()

                var stxtOrderNo = $("#txtOrderNo").val()
                var stxtTechNo = $("#txtTechNo").val()
                var stxtProductNo = $("#txtProductNo").val()
                var stxtSProcedure = $("#txtSProcedure").val()
                var stxtSProcedureVer = $("#txtSProcedureVer").val()
                stxtSProcedureVer = stxtSProcedureVer == "" ? '-' : stxtSProcedureVer
                $("#txtSixItem").val("")
                if (flag == "AddDevice") {
                    $(".DeviceInfo").show()
                    ChoogeProcDeviceInfo()
                    ShowWODeviceInfo(stxtOrderNo, stxtSProcedure, stxtSProcedureVer)
                    $("#txtQueryCondition").attr("placeholder", "设备分类编码、名称")
                } else if (flag == "AddTestItem") {
                    $(".TestItemInfo").show()
                    ChoogeGetTestItemInfo(stxtTechNo, stxtSProcedure, stxtOrderNo + stxtSProcedureVer, "2")
                    ShowWOTestItemInfo(stxtOrderNo, stxtSProcedure, stxtSProcedureVer)
                    $("#txtQueryCondition").attr("placeholder", "检验项目")
                } else if (flag == "AddFile") {
                    $(".FileInfo").show()
                    ChoogeProcCHFileInfo()
                    ShowOrderFileInfo(stxtOrderNo, stxtSProcedure, stxtSProcedureVer)
                    $("#txtQueryCondition").attr("placeholder", "文件编码、名称")
                }

            })


            $("#BOMSaveBtn").click(function () {
                var sOrderNo = $("#txtOrderNo").val();
                var sGX = $("#txtBomProcNo option:checked").text();
                var sProcNo = $('#txtBomProcNo').val();
                var sCNo = $("#txtCMaterNo").val();
                var sCName = $("#txtCName").val();
                var sNum = $("#txtNum").val();
                var Flag = $("#txtOP").val()

                var sRFNo = $("#txtRWOFNo").val();
                var sRCNo = $("#txtRWOCMaterNo").val();
                var sRCName = $("#txtRWOCMaterName").val();
                var sRGX = $("#txtWOProcNo").val();
                var sRProcNo = sRGX.substr(1, sRGX.indexOf(")") - 1);


                var sPack = "";
                var sZSFlag = "";
                var sRepeatFlag = "";
                if ($("#txtRepeatFlag").is(":checked")) {
                    sRepeatFlag = "是";
                }
                if ($("#txtSerialC").is(':checked')) {
                    sZSFlag = "序列号";
                }
                if ($("#txtBatchC").is(':checked')) {
                    sZSFlag = "批次";
                }
                if ($("#txtMaterC").is(':checked')) {
                    sZSFlag = "物料";
                }

                if ($("#txtCPack").is(':checked')) {
                    sPack = "是";
                }


                var Data = "";
                var Params = null;

                if ($("#head-title2").html() == "BOM") {
                    if (sCNo == "") {
                        ErrorMessage("请输入子零件编码！", 2000)
                        return;
                    }
                    if (sCName == "") {
                        ErrorMessage("获取不到子零件名称，光标放在子编码内再离开！", 2000)
                        return;
                    }

                    Params = { No: sOrderNo, SN: "", MNo: sCNo, MName: sCName, A: ModuleName, B: sProcNo, C: sGX, D: "", E: sZSFlag, F: sNum, G: sPack, H: sRepeatFlag, I: "", J: "", Remark: "", Flag: Flag };
                    Data = JSON.stringify(Params);
                } else {
                    if (sRCNo == "") {
                        ErrorMessage("请填写可替代物料！", 2000)
                        return;
                    }
                    if (sRCName == "") {
                        ErrorMessage("获取不到子零件名称，光标放在子编码内再离开！", 2000)
                        return;
                    }

                    Params = { No: sOrderNo, SN: "", MNo: sRCNo, MName: sRCName, A: ModuleName, B: sRProcNo, C: sRGX, D: "", E: sRFNo, F: "", G: "", H: "", I: "", J: "", Remark: "", Flag: Flag };
                    Data = JSON.stringify(Params);
                }

                $("#loading").show()
                $("#BOMSaveBtn").attr("disabled", "disabled");


                $.ajax({
                    type: "POST",
                    url: "../Service/OrderAjax.ashx?OP=BatchManipulateData",
                    data: {
                        Data: Data
                    },
                    success: function (res) {
                        var parsedJson = jQuery.parseJSON(res);
                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg("操作成功！")
                            closeDelDialog()
                            ShowWOBOMInfo(sOrderNo)
                        } else {
                            layer.msg("操作失败！" + parsedJson.Message)
                        }

                        $("#BOMSaveBtn").removeAttr("disabled");
                        $("#loading").hide()
                    },
                    error: function (data) {
                        $("#loading").hide()
                        $("#BOMSaveBtn").removeAttr("disabled");
                        ErrorMessage("系统出错，请重试2！", 2000)
                    }
                });
            })
        })


        function InNullJudgment(temp_table_list) {
            var sOrderNo = $("#txtSOrder").val()

            if (sOrderNo == "") {
                layer.msg("请输入工单")
                return false
            } else if (temp_table_list.length == 0) {
                layer.msg(sOrderNo + " 工单没有序列号")
                return false
            }
            $("#ShowOne").css("display", "block")
            $("#ShowOne-fade").css("display", "block")
            $("#txtOrderNo").val(sOrderNo)
            GetProcedureList(temp_table_list[0].MaterNo, sOrderNo)
            return true
        }

        function GetProcedureList(MNo, OrderNo) {
            $("#txtSProcedure").empty();
            $("#txtBomProcNo").empty();
            var sSs = "工序";
            var keywords = encodeURI(sSs);
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/TechAjax.ashx?OP=GetTechInfo&CFlag=111-1-2&limit=0&page=0&Item=" + OrderNo + "&CMNO=" + MNo,
                success: function (res) {
                    if (res.data.length > 0) {
                        $("#txtProductNo").val(res.data[0].ProductNo)
                        $("#txtTechNo").val(res.data[0].TechNo)
                        $("#txtSProcedure").append("<option value=''></option>")
                        $("#txtBomProcNo").append("<option value=''></option>")
                        for (var i = 0; i < res.data.length; i++) {
                            $("#txtSProcedure").append("<option value='" + res.data[i].ProcedureNo + "'> " + res.data[i].PNName + "</option>");
                            $("#txtBomProcNo").append("<option value='" + res.data[i].ProcedureNo + "'> " + res.data[i].PNName + "</option>")
                        }
                    }
                }
            });
        }

        function GetProcedureVerList(sNo, sProc) {

            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/OrderAjax.ashx?OP=GetProcedureList&CFlag=111-12-1&CNO=" + sNo + "&Item=" + sProc,
                success: function (data) {
                    var parsedJson = eval(data).Msg;
                    var sKong = "<option value=''> </option>";
                    $("#txtSProcedureVer").empty();
                    $("#txtSProcedureVer").append(sKong + parsedJson);
                }
            });
        }


        // 选择设备相关信息
        function ChoogeProcDeviceInfo() {
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#CHDeviceList',
                    id: 'CHDeviceID',
                    url: '../Service/TechAjax.ashx?OP=GetTechInfo&CFlag=115',
                    height: '500',
                    cellMinWidth: 80,
                    count: 50,
                    limit: 20,
                    limits: [10, 20, 30, 40, 50],
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'DeviceKind', title: '类型' },
                        { field: 'MaterNo', title: '设备分类编码' },
                        { field: 'MaterName', title: '名称', },
                        { field: 'DeptName', title: '管理部门', },
                        { field: 'Status', title: '状态' },
                        { field: 'op', title: '操作', width: 80, toolbar: '#barDemoDevice', fixed: 'right' }
                    ]],
                    page: true
                });


                table.on('tool(CHDeviceList)', function (obj) {
                    var data = obj.data,
                        layEvent = obj.event;
                    if (layEvent == "chooge") {
                        if ($("#txtSProcedureVer").val() == "" || $("#txtSProcedureVer").val() == null) {
                            layer.msg("请先选择版本")
                            return
                        }

                        $("#ShowDel").css("display", "block")
                        $("#ShowDel-fade").css("display", "block")

                        $("#XC-Icon").removeClass()
                        $("#hint-value").removeClass()
                        $("#XC-Icon").addClass("XC-Icon XC-Btn-Green")
                        $("#hint-value").addClass("XC-Font-Green")

                        $("#hint-title").html("确定要批量添加该设备吗？ 分类编码：")
                        $("#hint-value").html(data.MaterNo)

                        $("#txtE").val(data.MaterNo)
                        $("#txtF").val(data.DeviceKind)
                        $("#txtG").val(data.DeptName)
                        $("#txtH").val(data.MaterName)
                        $("#txtI").val(data.Status)
                        $("#txtJ").val("")
                        $("#txtOP").val("AddDevice")
                    }

                });



                $('.QueryResult').click(function () {

                    var flag = $("#txtAddKind").val()
                    if (flag != "AddDevice") {
                        return
                    }

                    var sNNAME = encodeURI($("#txtQueryCondition").val());

                    var sBDate = "";
                    var sEDate = "";

                    var Data = '';
                    var Params = { No: "", Item: "", Name: "", MNo: "", MName: sNNAME, Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "" };
                    var Data = JSON.stringify(Params);

                    table.reload('CHDeviceID', {
                        method: 'post',
                        url: '../Service/TechAjax.ashx?OP=GetTechInfo&CFlag=115&Data=' + Data,
                        where: {
                            'No': sNNAME,
                        }, page: {
                            curr: 1
                        }
                    });
                });

            });


        }


        // 选择工艺文件相关信息
        function ChoogeProcCHFileInfo() {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#CHFileList',
                    id: 'CHFileID',
                    url: '../Service/TechAjax.ashx?OP=GetTechInfo&CFlag=122',
                    height: '500',
                    cellMinWidth: 80,
                    count: 50,
                    limit: 20,
                    limits: [10, 20, 30, 40, 50],
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'FileNo', title: '文件编号' },
                        { field: 'FileVer', title: '版本', width: 80 },
                        { field: 'FileName', title: '名称' },
                        { field: 'UserArea', title: '使用范围' },
                        { field: 'Kind', title: '类别' },
                        { field: 'op', title: '操作', width: 80, toolbar: '#barDemo5', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(CHFileList)', function (obj) {
                    var data = obj.data,
                        layEvent = obj.event;
                    if (layEvent == "chooge") {
                        if ($("#txtSProcedureVer").val() == "" || $("#txtSProcedureVer").val() == null) {
                            layer.msg("请先选择版本")
                            return
                        }

                        $("#ShowDel").css("display", "block")
                        $("#ShowDel-fade").css("display", "block")

                        $("#XC-Icon").removeClass()
                        $("#hint-value").removeClass()
                        $("#XC-Icon").addClass("XC-Icon XC-Btn-Green")
                        $("#hint-value").addClass("XC-Font-Green")

                        $("#hint-title").html("确定要批量添加该文件吗？ 文件编码：")
                        $("#hint-value").html(data.FileNo)

                        $("#txtOneItem").val($("#txtOrderNo").val())
                        $("#txtTowItem").val($("#txtSProcedure").val())
                        $("#txtThreeItem").val($("#txtSProcedureVer").val())
                        $("#txtFourItem").val(data.FileNo)
                        $("#txtFiveItem").val('启用')
                        $("#txtE").val(data.FileNo)
                        $("#txtF").val(data.FileVer)
                        $("#txtG").val(data.FileName)
                        $("#txtH").val(data.DOCName)
                        $("#txtI").val(data.Status)
                        $("#txtJ").val(data.FilePath)

                        $("#txtOP").val("AddFile")
                    }

                });


                //  查询
                $('.QueryResult').click(function () {

                    var flag = $("#txtAddKind").val()
                    if (flag != "AddFile") {
                        return
                    }

                    var sNNAME = encodeURI($("#txtQueryCondition").val());

                    var Data = '';
                    var Params = { No: "", Item: "", Name: "", MNo: "", MName: sNNAME, Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "" };
                    var Data = JSON.stringify(Params);

                    table.reload('CHFileID', {
                        method: 'post',
                        url: '../Service/TechAjax.ashx?OP=GetTechInfo&CFlag=122&Data=' + Data,
                        where: {
                            'No': sNNAME
                        }, page: {
                            curr: 1
                        }
                    });
                });

            });


        }


        // 工单，序列号选择测试项相关信息
        function ChoogeGetTestItemInfo(TechNo, ProcNo, PNo, Flag) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#CHGetTestItemList',
                    id: 'CHGetTestItemID',
                    url: '../Service/TechAjax.ashx?OP=GetTechInfo&CFlag=118&CNO=' + TechNo + '&Item=' + ProcNo + '&CMNO=' + PNo + "&CStatus=" + Flag,
                    height: '350',
                    cellMinWidth: 80,
                    count: 100,
                    limit: 100,
                    limits: [50, 100, 150, 200, 250],
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'TechNo', title: '产品代号', width: 80 },
                        { field: 'NameCH', title: '检验项目', width: 300 },
                        { field: 'DescCH', title: '检验要求', width: 350 },
                        { field: 'ValueKind', title: '数据类型', minWidth: 90 },
                        { field: 'NNo', title: '', minWidth: 30 },
                        { field: 'op', title: '操作', width: 110, toolbar: '#barDemoGetTestIte', fixed: 'right' }
                    ]],
                    page: true,
                    done: function (res, curr, count) {
                        var that = this.elem.next();
                        res.data.forEach(function (item, index) {
                            if (item.NNo != "NO") {
                                $(".layui-table-body tbody tr[data-index='" + index + "']").find('td[data-field="NNo"]').css({ 'color': "blue" });

                            }
                        });
                    }

                });

                //监听行工具事件
                table.on('tool(CHGetTestItemList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值
                    if (layEvent == "chooge") {
                        if ($("#txtSProcedureVer").val() == "" || $("#txtSProcedureVer").val() == null) {
                            layer.msg("请先选择版本")
                            return
                        }

                        $("#ShowDel").css("display", "block")
                        $("#ShowDel-fade").css("display", "block")

                        $("#XC-Icon").removeClass()
                        $("#hint-value").removeClass()
                        $("#XC-Icon").addClass("XC-Icon XC-Btn-Green")
                        $("#hint-value").addClass("XC-Font-Green")

                        $("#hint-title").html("确定要批量添加该测试项吗？ 编号：")
                        $("#hint-value").html(data.SpecNo)
                        $("#txtE").val(data.SpecNo)
                        $("#txtF").val("")
                        $("#txtG").val("")
                        $("#txtH").val("")
                        $("#txtI").val("")
                        $("#txtJ").val("")
                        $("#txtOP").val("AddTestItem")
                    }

                });


                //  查询
                $('.QueryResult').off("click").click(function () {

                    var flag = $("#txtAddKind").val()
                    if (flag != "AddTestItem") {
                        return
                    }

                    var stxtOrderNo = $("#txtOrderNo").val()
                    var stxtTechNo = $("#txtTechNo").val()
                    var stxtProductNo = $("#txtProductNo").val()
                    var stxtSProcedure = $("#txtSProcedure").val()
                    var stxtSProcedureVer = $("#txtSProcedureVer").val()
                    var sNNAME = encodeURI($("#txtQueryCondition").val());

                    var Data = '';
                    var Params = { No: stxtTechNo, Item: stxtSProcedure, Name: "", MNo: stxtOrderNo + stxtSProcedureVer, MName: "", Status: "2", BDate: "", EDate: "", A: sNNAME, B: "", C: "", D: "" };
                    var Data = JSON.stringify(Params);

                    table.reload('CHGetTestItemID', {
                        method: 'post',
                        url: '../Service/TechAjax.ashx?OP=GetTechInfo&CFlag=118&Data=' + Data,
                        where: {
                            'No': stxtTechNo
                        }, page: {
                            curr: 1
                        }
                    });
                });

            });


        }




        // 工单设备信息
        function ShowWODeviceInfo(sNo, sProcNo, sVer) {
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#WODeviceList',
                    id: 'WODeviceID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-6&CNO=' + sNo + '&Item=' + sProcNo + sVer,
                    height: '300',
                    cellMinWidth: 80,
                    count: 50,
                    limit: 50,
                    limits: [10, 20, 30, 40, 50],
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'DeviceKind', title: '类型', width: 120 },
                        { field: 'MaterNo', title: '设备分类编码', width: 200 },
                        { field: 'DeviceName', title: '名称' },
                        { field: 'InMan', title: '录入人', minWidth: 80, width: 200 },
                        { field: 'InDate2', title: '录入时间', width: 150 },
                        { field: 'op', title: '操作', width: 80, toolbar: '#barDemo_WODevice', fixed: 'right' }
                    ]],
                    page: true
                });

                table.on('tool(WODeviceList)', function (obj) {
                    var data = obj.data,
                        layEvent = obj.event;


                    if (layEvent == 'del') {

                        $("#ShowDel").css("display", "block")
                        $("#ShowDel-fade").css("display", "block")

                        $("#XC-Icon").removeClass()
                        $("#hint-value").removeClass()
                        $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                        $("#hint-value").addClass("XC-Font-Red")

                        $("#hint-title").html("确定要批量删除该设备吗？ 分类编码：")
                        $("#hint-value").html(data.MaterNo)

                        $("#txtE").val(data.MaterNo)
                        $("#txtF").val("")
                        $("#txtG").val("")
                        $("#txtH").val("")
                        $("#txtI").val("")
                        $("#txtJ").val("")
                        $("#txtOP").val("DeleteDevice")
                    }

                });

            });


        }

        //工单工艺文件信息
        function ShowOrderFileInfo(sNo, sProcNo, sVer) {
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#OrderFileList',
                    id: 'OrderFileID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-7&CNO=' + sNo + '&Item=' + sProcNo + sVer,
                    height: '300',
                    cellMinWidth: 80,
                    count: 50,
                    limit: 20,
                    limits: [10, 20, 30, 40, 50],
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'ProcedureName', title: '工序', width: 120 },
                        { field: 'ProcedureVer', title: '版本', width: 100 },
                        { field: 'FileNo', title: '文件编码', width: 150 },
                        { field: 'FileVer', title: '版本', width: 100 },
                        { field: 'FileName', title: '文件名称', minWidth: 200 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate2', title: '录入时间', width: 150 },
                        { field: 'FilePath', title: '文件路径', minWidth: 400 },
                        { field: 'op', title: '操作', width: 130, toolbar: '#barDemo_WOFile', fixed: 'right' }
                    ]],
                    page: true
                });

                table.on('tool(OrderFileList)', function (obj) {
                    var data = obj.data,
                        layEvent = obj.event;

                    if (layEvent == 'del') {

                        $("#ShowDel").css("display", "block")
                        $("#ShowDel-fade").css("display", "block")

                        $("#XC-Icon").removeClass()
                        $("#hint-value").removeClass()
                        $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                        $("#hint-value").addClass("XC-Font-Red")

                        $("#hint-title").html("确定要批量删除该文件吗？ 文件编码：")
                        $("#hint-value").html(data.FileNo)

                        $("#txtE").val(data.FileNo)
                        $("#txtF").val("")
                        $("#txtG").val("")
                        $("#txtH").val("")
                        $("#txtI").val("")
                        $("#txtJ").val("")
                        $("#txtOP").val("DeleteFile")
                    }
                });
            });
        }

        // 工单测试项信息
        function ShowWOTestItemInfo(sNo, sProcNo, sVer) {
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#WOTestItemList',
                    id: 'WOTestItemID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=234&CNO=' + sNo + '&Item=' + sProcNo + sVer,
                    height: '400',
                    cellMinWidth: 80,
                    count: 100,
                    limit: 100,
                    limits: [20, 30, 40, 50, 100, 150, 200],
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'NameCH', title: '检验项目', width: 300 },
                        { field: 'DescCH', title: '检验要求', width: 300 },
                        { field: 'ValueKind', title: '数据类型', minWidth: 80 },
                        { field: 'NotNeed', title: '是否涉及', width: 80 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate2', title: '录入时间', width: 150 },
                        { field: 'op', title: '操作', width: 80, toolbar: '#barDemo_WOTestItem', fixed: 'right' }
                    ]],
                    page: true
                });

                table.on('tool(WOTestItemList)', function (obj) {
                    var data = obj.data,
                        layEvent = obj.event;

                    if (layEvent == 'del') {

                        $("#ShowDel").css("display", "block")
                        $("#ShowDel-fade").css("display", "block")

                        $("#XC-Icon").removeClass()
                        $("#hint-value").removeClass()
                        $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                        $("#hint-value").addClass("XC-Font-Red")

                        $("#hint-title").html("确定要批量删除该测试项吗？ 编码：")
                        $("#hint-value").html(data.SpecNo)

                        $("#txtE").val(data.SpecNo)
                        $("#txtF").val("")
                        $("#txtG").val("")
                        $("#txtH").val("")
                        $("#txtI").val("")
                        $("#txtJ").val("")
                        $("#txtOP").val("DeleteTestItem")
                    }
                });

                table.on('row(WOTestItemList)', function (obj) {
                    obj.tr.siblings().removeClass(".layui-table-hover")
                    obj.tr.siblings().removeClass('layui-table-click-bg');  // 选中行颜色加深
                    obj.tr.addClass('layui-table-click-bg');
                    $("#txtF").val(obj.data.SNo)
                })

            });
        }


        // 工单：显示工单BOM信息
        function ShowWOBOMInfo(sNo) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#OrderInfo_BomList',
                    id: 'OrderInfo_BomList',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=152&CNO=' + sNo,
                    height: 'full-235',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50],
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'OrderNo', title: '生产工单', width: 120 },
                        { field: 'MaterNo', title: '物料编码', width: 150 },
                        { field: 'MaterName', title: '物料描述', width: 300 },
                        { field: 'UseNum', title: '数量', minWidth: 60 },
                        { field: 'ProcedureName', title: '工序', minWidth: 120 },
                        { field: 'ControlWay', title: '追溯属性', minWidth: 80 },
                        { field: 'Pack', title: '装箱件', minWidth: 70 },
                        { field: 'AMFlag', title: '替代料', minWidth: 70 },
                        { field: 'RepeatFlag', title: '重工对象', minWidth: 70 },
                        { field: 'AMMaterNo', title: '被替代物料', minWidth: 120 },
                        { field: 'op', title: '操作', width: 220, toolbar: '#barDemo_Bom', fixed: 'right' }
                    ]],
                    page: true
                });

                table.on('tool(OrderInfo_BomList)', function (obj) {
                    var data = obj.data,
                        layEvent = obj.event;

                    if (layEvent == 'del') {

                        $("#ShowDel").css("display", "block")
                        $("#ShowDel-fade").css("display", "block")

                        $("#XC-Icon").removeClass()
                        $("#hint-value").removeClass()
                        $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                        $("#hint-value").addClass("XC-Font-Red")

                        $("#hint-title").html("确定要批量删除该物料吗？ 编码：")
                        $("#hint-value").html(data.MaterNo)


                        $("#txtSProcedure").val(data.ProcedureNo)
                        $("#txtE").val(data.MaterNo)
                        $("#txtF").val("")
                        $("#txtG").val("")
                        $("#txtH").val("")
                        $("#txtI").val("")
                        $("#txtJ").val("")
                        $("#txtOP").val("DeleteMater")
                    }
                    else if (layEvent == 'tdl') {
                        $("#ShowTow").css("display", "block")
                        $("#ShowTow-fade").css("display", "block")

                        $("#head-title2").html("添加替代料")
                        $("#Replace").show()
                        $("#txtRWOFNo").val(data.MaterNo);
                        $("#txtRWOFName").val(data.MaterName);
                        $("#txtWOProcNo").val("(" + data.ProcedureNo + ")" + data.ProcedureName);
                        $("#txtRWOCMaterNo").val("")
                        $("#txtRWOCMaterName").val("")
                        $("#txtRWOCModel").val("")

                        $("#txtOP").val("AddReplaceMater")
                    }

                });

            });

        }


        function closeDelDialog() {
            $("#ShowDel").css("display", "none")
            $("#ShowDel-fade").css("display", "none")
            $("#ShowTow").css("display", "none")
            $("#ShowTow-fade").css("display", "none")
            $("#Replace").hide()
            $("#Mater").hide()
        }

        function BatchManipulateBtn() {

            $("#loading").show()

            var stxtOrderNo = $("#txtOrderNo").val()
            var stxtTechNo = $("#txtTechNo").val()
            var sProcNo = $("#txtSProcedure").val()
            var sProcVer = $("#txtSProcedureVer").val()
            var Procedure = $("#txtSProcedure option:selected").text()
            sProcVer = sProcVer == "" ? '-' : sProcVer

            var sE = $("#txtE").val()
            var sF = $("#txtF").val()
            var sG = $("#txtG").val()
            var sH = $("#txtH").val()
            var sI = $("#txtI").val()
            var sJ = $("#txtJ").val()
            var Flag = $("#txtOP").val()

            var Params = { No: stxtOrderNo, SN: "", MNo: "", MName: "", A: ModuleName, B: sProcNo, C: Procedure, D: sProcVer, E: sE, F: sF, G: sG, H: sH, I: sI, J: sJ, Remark: "", Flag: Flag };
            var Data = JSON.stringify(Params);


            $.ajax({
                url: "../Service/OrderAjax.ashx?OP=BatchManipulateData",
                data: {
                    Data: Data
                },
                type: "POST",
                success: function (res) {
                    var parsedJson = jQuery.parseJSON(res);
                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                        layer.msg("操作成功！")
                        closeDelDialog()
                        if (Flag == "AddDevice" || Flag == "DeleteDevice") {
                            ShowWODeviceInfo(stxtOrderNo, sProcNo, sProcVer)
                        }
                        else if (Flag == "AddFile" || Flag == "DeleteFile") {
                            ShowOrderFileInfo(stxtOrderNo, sProcNo, sProcVer)
                        }
                        else if (Flag == "AddTestItem" || Flag == "DeleteTestItem") {
                            ShowWOTestItemInfo(stxtOrderNo, sProcNo, sProcVer)
                            $("#txtF").val("")
                            ChoogeGetTestItemInfo(stxtTechNo, sProcNo, stxtOrderNo + sProcVer, "2")
                        } else if (Flag == "DeleteMater") {
                            ShowWOBOMInfo(stxtOrderNo)
                        }
                    } else {
                        layer.msg("操作失败！" + parsedJson.Message)
                    }
                    $("#loading").hide()
                },
                error: function () {
                    $("#loading").hide()
                    layer.msg("系统出错")
                }
            })
        }

        function openAddMater() {
            $("#txtONo").val($("#txtOrderNo").val())
            $("#head-title2").html("BOM")

            $("#Mater").show()

            $("#txtCMaterNo").val("")
            $("#txtCName").val("")
            $("#txtBomProcNo").val("")
            $("#txtNum").val("")

            $("#txtCPack").removeProp("checked");
            $("#txtSerialC").removeProp("checked");
            $("#txtBatchC").removeProp("checked");
            $("#txtMaterC").removeProp("checked");

            $("#txtOP").val("AddMater")
        }

    </script>
</head>
<body>
    <div class="div_find">
        <label class="find_labela">工单号</label><input type="text" id="txtSOrder" class="find_input" />

        <input type="button" value="搜索" class="XC-Btn-Green XC-Size-xs XC-Btn-md" id="OrderSerialInfo_open" />
        <input type="button" value="批量操作设备" class="XC-Btn-Green XC-Size-xs XC-Btn-md" id="AddDevice" style="width:85px" />
        <input type="button" value="批量操作测试项" class="XC-Btn-Green XC-Size-xs XC-Btn-md" id="AddTestItem" style="width:100px" />
        <input type="button" value="批量操作工艺文件" class="XC-Btn-Green XC-Size-xs XC-Btn-md" id="AddFile" style="width:110px" />
        <input type="button" value="批量操作追溯物料" class="XC-Btn-Green XC-Size-xs XC-Btn-md" id="AddMater" style="width:110px" />
    </div>

    <div class="wangid_conbox">
        <table class="layui-hide" id="OrderInfo_SNList" lay-filter="OrderInfo_SNList"></table>
    </div>

    <!--选择设备信息弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1"></span>
            <span class="head-close" id="closeDialog">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <div style="margin-bottom:15px">
                    <div style=" font-weight: bold; padding-bottom: 10px;margin-bottom:10px;border-bottom: solid 1px #ccc;">
                        基本信息
                    </div>
                    <label class="find_labela">工单号</label><input type="text" id="txtOrderNo" class="find_input" disabled="disabled" />
                    <label class="find_labela">产品代号</label><input type="text" id="txtTechNo" class="find_input" disabled="disabled" />
                    <label class="find_labela">产品编码</label><input type="text" id="txtProductNo" class="find_input" disabled="disabled" />
                    <label class="find_labela pv">工序</label><select type="text" id="txtSProcedure" class="find_input"></select>
                    <label class="find_labela pv">版本</label>
                    <select class="find_input" id="txtSProcedureVer"></select>
                    <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="openAddMater" style="display:none" onclick="openAddMater()">添加</button>
                </div>

                <!--已有设备-->
                <div class="wangid_conbox DeviceInfo" style="display:none">
                    <table class="layui-hide" id="WODeviceList" lay-filter="WODeviceList"></table>
                    <script type="text/html" id="barDemo_WODevice">
                        <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>
                    </script>
                </div>

                <!--已有工艺文件-->
                <div class="wangid_conbox FileInfo" style="display:none">
                    <table class="layui-hide" id="OrderFileList" lay-filter="OrderFileList"></table>
                    <script type="text/html" id="barDemo_WOFile">
                        <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>
                    </script>
                </div>

                <!--已有测试项-->
                <div class="wangid_conbox TestItemInfo" style="display:none">
                    <table class="layui-hide" id="WOTestItemList" lay-filter="WOTestItemList"></table>
                    <div style="color:red;text-align:right;font-size:14px;margin-top:5px">请先选择添加的位置，如未选择默认添加最前面</div>
                    <script type="text/html" id="barDemo_WOTestItem">
                        <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>
                    </script>
                </div>

                <!--已有工单BOM-->
                <div class="wangid_conbox OrderInfo_BomList" style="display:none">
                    <table class="layui-hide" id="OrderInfo_BomList" lay-filter="OrderInfo_BomList"></table>
                    <script type="text/html" id="barDemo_Bom">
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="tdl">替代料</button>
                        <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>
                    </script>
                </div>


                <div style="margin-bottom:15px;display:none" id="query">
                    <div style=" font-weight: bold; padding-bottom: 10px;margin:10px 0px;border-bottom: solid 1px #ccc;">
                        查询
                    </div>
                    <label class="find_labela">查询条件</label><input type="text" id="txtQueryCondition" class="find_input" placeholder="" style="width:25%;padding-left:5px" />
                    <button class="XC-Btn-Green XC-Size-xs XC-Btn-md QueryResult">搜索</button>
                </div>

                <!--添加设备-->
                <div class="wangid_conbox DeviceInfo" style="display:none">
                    <table class="layui-hide" id="CHDeviceList" lay-filter="CHDeviceList"></table>
                    <script type="text/html" id="barDemoDevice">
                        <button class="XC-Btn-Green XC-Size-xs XC-Btn-md" lay-event="chooge">选择</button>
                    </script>
                </div>

                <!--添加工艺文件-->
                <div class="wangid_conbox FileInfo" style="display:none">
                    <table class="layui-hide" id="CHFileList" lay-filter="CHFileList"></table>
                    <script type="text/html" id="barDemo5">
                        <button class="XC-Btn-Green XC-Size-xs XC-Btn-md" lay-event="chooge">选择</button>
                    </script>
                </div>

                <!--添加测试项-->
                <div class="wangid_conbox TestItemInfo" style="display:none">
                    <table class="layui-hide" id="CHGetTestItemList" lay-filter="CHGetTestItemList"></table>
                    <script type="text/html" id="barDemoGetTestIte">
                        <button class="XC-Btn-Green XC-Size-xs XC-Btn-md" lay-event="chooge">选择</button>
                    </script>
                </div>
            </div>
        </div>
    </div>

    <div class="input-group" style="display:none; ">
        <span class="input-group-addon">.</span>
        <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
        <span class="input-group-addon">.</span>
        <input type="text" class="form-control" id="txtEditKind" name="txtEditKind" />
        <span class="input-group-addon">.</span>
        <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
        <span class="input-group-addon">.</span>
        <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
        <span class="input-group-addon">.</span>
        <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
        <span class="input-group-addon">.</span>
        <input type="text" class="form-control" id="txtInName" name="txtInName" />
    </div>

    <div id="ShowOne-fade" class="black_overlay">
    </div>


    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel" style="z-index:1004">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDelDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>


            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtE" name="txtE" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtF" name="txtF" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtG" name="txtG" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtH" name="txtH" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtI" name="txtI" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtJ" name="txtJ" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtSevenItem" name="txtSevenItem" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtOP" name="txtOP" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="DeviceClassDetailDel_Btn" onclick="BatchManipulateBtn()">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDelDialog()">取消</button>
                </div>
            </div>
        </div>
        <div id="ShowDel-fade" class="black_overlay" style="z-index:1003">
        </div>
    </div>

    <div class="XC-modal XC-modal-md" id="ShowTow" style="min-height:230px;height:470px;z-index:1004">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title2">BOM</span>
            <span class="head-close" onclick="closeDelDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <form class="XC-Form-block" id="Mater" style="display:none">
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">单号</span>
                    <input class="XC-Input-block" type="text" id="txtONo" name="txtONo" disabled="disabled" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">子零件编码<span class="XC-Font-Red">*</span></span>
                    <input class="XC-Input-block" type="text" id="txtCMaterNo" name="txtCMaterNo" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">子零件名称</span>
                    <input class="XC-Input-block" type="text" id="txtCName" name="txtCName" disabled="disabled" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">工序</span>
                    <select class="XC-Select-block" id="txtBomProcNo"></select>
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">用量</span>
                    <input class="XC-Input-block" type="text" id="txtNum" name="txtNum" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">是否装箱件</span>
                    <input type="checkbox" id="txtCPack" name="txtCPack" />
                </div>
                <div class="XC-Form-block-Item" style="font-size:12px">
                    <span class="XC-Span-Input-block">追溯方式</span>
                    <input type="radio" id="txtMaterC" name="Mater" style="width:14px; height:14px;vertical-align:middle;margin:10px 0px">&nbsp;
                    <span style="width: 90px; font-size: 12px; color: #808080;  text-align: left; height: 30px; line-height: 30px;">按物料编码</span>
                    <input type="radio" id="txtBatchC" name="Mater" style="width: 14px; height: 14px; vertical-align: middle; margin: 10px 0px">&nbsp;
                    <span style="width: 70px; font-size: 12px; color: #808080;  text-align: left; height: 30px; line-height: 30px;">按批管</span>
                    <input type="radio" id="txtSerialC" name="Mater" style="width: 14px; height: 14px; vertical-align: middle; margin: 10px 0px">&nbsp;
                    <span style="width: 70px; font-size: 12px; color: #808080; text-align: left; height: 30px; line-height: 30px;">按序管</span>
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">重工对象</span>
                    <input type="checkbox" id="txtRepeatFlag" name="txtRepeatFlag" />
                </div>
            </form>
            <form class="XC-Form-block" id="Replace" style="display:none">
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">父零件编码</span>
                    <input class="XC-Input-block" type="text" id="txtRWOFNo" name="txtRWOFNo" disabled="disabled" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">父零件名称</span>
                    <input class="XC-Input-block" type="text" id="txtRWOFName" name="txtRWOFName" disabled="disabled" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">工序</span>
                    <input class="XC-Input-block" type="text" id="txtWOProcNo" name="txtWOProcNo" disabled="disabled" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">子零件编码<span class="XC-Font-Red">*</span></span>
                    <input class="XC-Input-block" type="text" id="txtRWOCMaterNo" name="txtRWOCMaterNo" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">子零件名称</span>
                    <input class="XC-Input-block" type="text" id="txtRWOCMaterName" name="txtRWOCMaterName" disabled="disabled" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">型号</span>
                    <input class="XC-Input-block" type="text" id="txtRWOCModel" name="txtRWOCModel" disabled="disabled" />
                </div>
            </form>
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="BOMSaveBtn">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDelDialog()">取消</button>
            </div>
        </div>
    </div>
    <div id="ShowTow-fade" class="black_overlay" style="z-index:1003">
    </div>

    <div id="loading" class="black_overlay" style="z-index: 1008; ">
        <div style="position: fixed;  top: 50%;  left: 50%;  transform: translate(-50%, -50%); ">
            <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
        </div>
        <div class="docs-icon-name" style="position: fixed;  top: 55%;  left: 50%;  transform: translate(-46.5%, -50%);font-size:12px ">正在批量处理中，请稍等！</div>
    </div>


    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>
</html>