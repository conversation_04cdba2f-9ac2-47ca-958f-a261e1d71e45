﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;


namespace Model
{
    public class BusinessData
    {

        public BusinessData()
        { }

        #region Model

        private string _BSNo;
        private string _BSName;
        private string _FlowNo;
        private string _FlowName;
        private string _F01;
        private string _F02;
        private string _F03;
        private string _F04;
        private string _F05;
        private string _F06;
        private string _F07;
        private string _F08;
        private string _F09;
        private string _F10;
        private string _Status;
        private string _CompanyNo;
        private string _InMan;
        private string _InManName;
        private DateTime? _InDate = DateTime.Now;
        private string _Remark;



        /// <summary>
        /// 业务数据编号
        /// </summary>
        public string BSNo
        {
            set { _BSNo = value; }
            get { return _BSNo; }
        }
        /// <summary>
        /// 业务数据编号
        /// </summary>
        public string BSName
        {
            set { _BSName = value; }
            get { return _BSName; }
        }

        /// <summary>
        /// 流程编号
        /// </summary>
        public string FlowNo
        {
            set { _FlowNo = value; }
            get { return _FlowNo; }
        }
        /// <summary>
        /// 流程名称
        /// </summary>
        public string FlowName
        {
            set { _FlowName = value; }
            get { return _FlowName; }
        }
        /// <summary>
        /// 字段一
        /// </summary>
        public string F01
        {
            set { _F01 = value; }
            get { return _F01; }
        }
        /// <summary>
        /// 字段二
        /// </summary>
        public string F02
        {
            set { _F02 = value; }
            get { return _F02; }
        }

        /// <summary>
        /// 字段三
        /// </summary>
        public string F03
        {
            set { _F03 = value; }
            get { return _F03; }
        }

        /// <summary>
        /// 字段
        /// </summary>
        public string F04
        {
            set { _F04 = value; }
            get { return _F04; }
        }

        /// <summary>
        /// 字段
        /// </summary>
        public string F05
        {
            set { _F05 = value; }
            get { return _F05; }
        }
        /// <summary>
        /// 字段
        /// </summary>
        public string F06
        {
            set { _F06 = value; }
            get { return _F06; }
        }
        /// <summary>
        /// 字段
        /// </summary>
        public string F07
        {
            set { _F07 = value; }
            get { return _F07; }
        }
        /// <summary>
        /// 字段
        /// </summary>
        public string F08
        {
            set { _F08 = value; }
            get { return _F08; }
        }
        /// <summary>
        /// 字段
        /// </summary>
        public string F09
        {
            set { _F09 = value; }
            get { return _F09; }
        }
        /// <summary>
        /// 字段
        /// </summary>
        public string F10
        {
            set { _F10 = value; }
            get { return _F10; }
        }
        /// <summary>
        /// 所属公司
        /// </summary>
        public string CompanyNo
        {
            set { _CompanyNo = value; }
            get { return _CompanyNo; }
        }
        /// <summary>
        /// 状态
        /// </summary>
        public string Status
        {
            set { _Status = value; }
            get { return _Status; }
        }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark
        {
            set { _Remark = value; }
            get { return _Remark; }
        }

        /// <summary>
        /// 输入人员编码
        /// </summary>
        public string InMan
        {
            set { _InMan = value; }
            get { return _InMan; }
        }
        /// <summary>
        /// 输入人员
        /// </summary>
        public string InManName
        {
            set { _InManName = value; }
            get { return _InManName; }
        }
        /// <summary>
        /// 添加时间
        /// </summary>
        public DateTime? InDate
        {
            set { _InDate = value; }
            get { return _InDate; }
        }
        #endregion Model


    }
}