﻿using Common;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text;
using System.Xml.Linq;

namespace DAL
{
    public class EchartDal
    {
        public static string TemplateBaseOP(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string Remark, string sLogin, string sComp, string Flag)
        {
            string SQL = string.Empty;
            if (Flag == "1")
            {
                No = CreateKBNo(Flag);
                //添加看板
                SQL = "insert into T_BoardLibrary(BoardNo,BoardName,BoardDesc,BoardImage,BoardPath,BoardType,Status,CompanyNo,InMan,Remark) " +
                    "values('" + No + "','" + Name + "','" + Item + "','" + B + "','" + A + "','Board','','" + sComp + "','" + sLogin + "','" + Remark + "')";
            }
            else if (Flag == "2")
            {
                //修改看板
                SQL = "update T_BoardLibrary set BoardName='" + Name + "',BoardDesc='" + Item + "',BoardImage='" + B + "',BoardPath='" + A + "',Remark='" + Remark + "' where" +
                    " BoardNo='" + No + "'";
            }
            else if (Flag == "3")
            {
                //删除
                SQL = "delete from T_BoardLibrary where BoardNo='" + No + "'";
            }
            else if (Flag == "4")
            {
                if (A != "")
                {
                    DBHelper.ExecuteCommand("delete from T_BoardLibrary where BoardType='Params' and InMan='" + sLogin + "' and Remark='" + Remark + "'");

                    No = CreateKBNo(Flag);
                    SQL = "INSERT INTO T_BoardLibrary(" + A + ", CompanyNo, InMan,BoardType,BoardNo,Remark) values(" + B + ",'" + sComp + "','" + sLogin + "','Params','" + No + "','" + Remark + "')";
                }
            }
            else if (Flag == "5")//添加Spc看板
            {
                No = CreateSpcNo(Flag);
                //添加看板
                SQL = "insert into T_SpcLibrary(SPCNo,SPCName,SPCDesc,Status,CompanyNo,InMan,Remark) " +
                    "values('" + No + "','" + Name + "','" + Item + "','','" + sComp + "','" + sLogin + "','" + Remark + "')";
            }
            else if (Flag == "6")//修改Spc看板
            {
                SQL = "update T_SpcLibrary set SPCName='" + Name + "',SPCDesc='" + Item + "',Remark='" + Remark + "' where" +
                    " SPCNo='" + No + "'";
            }
            else if (Flag == "7")//删除Spc看板
            {
                SQL = "delete from T_SpcLibrary where SPCNo='" + No + "'";
            }
            else if (Flag == "8")//添加SPC任务列表
            {
                No = CreateTNo();
                SQL = "INSERT INTO T_SpcLibraryDetails(TaskNo, SPCNo, MaterNo, ProcedureNo, SamplingObject, SampleCapacity, SampleSize, SamplingMethods,SamplingWay, StartTime,EndTime,GroupNo, Status, CompanyNo, InMan) " +
                    "VALUES ('" + No + "', '" + Item + "', '" + MNo + "', '" + A + "', '" + Name + "', '" + D + "', '" + E + "', '" + B + "','" + F + "','" + DateTime.Parse(C) + "','" + DateTime.Parse(C) + "','0', '未开始', '" + sComp + "', '" + sLogin + "');";
            }
            else if (Flag == "9")//删除SPC任务列表
            {
                SQL = "delete from T_SpcLibraryDetails where TaskNo = '" + No + "'";
            }

            try
            {
                if (SQL != "")
                {
                    DBHelper.ExecuteCommand(SQL);
                    return "Success";
                }
                else
                {
                    return "Success";
                }
            }
            catch (Exception e)
            {
                return "Error:" + e.ToString();
            }
        }

        public static DataTable GetTemplateBase(string No, string Name, string Item, string MNo, string MName, string Status, string sBDate, string sEDate, string A, string B, string C, string D, string E, string F, string G, string H, string I, string J, string K, string L, int rows, int page, string sLogin, string sComp, string Flag)
        {
            DataTable dt = new DataTable();
            string sSql = "";
            var sNo = No;

            if (Flag == "1")
            {
                sSql = "select '0' as NumCount, FieldNameEN, FieldNameCN, '' as DefaultValue from T_BoardConfig where FieldNo in (" + Name + ") ";
                dt = DBHelper.GetDataTable(sSql);

                if (dt.Rows.Count == 0)
                {
                    return dt;
                }

                // 构建查询条件
                string fieldNos = string.Join(",", dt.AsEnumerable().Select(row => row.Field<string>("FieldNameEN")).ToArray());

                var values = DBHelper.GetDataTable("select " + fieldNos + " from T_BoardLibrary where Remark = '" + sNo + "' and InMan = '" + sLogin + "' and BoardType = 'Params'");

                // 更新默认值
                foreach (DataRow item in dt.Rows)
                {
                    if (values.Rows.Count > 0)
                    {
                        item["DefaultValue"] = values.Rows[0][item["FieldNameEN"].ToString()];
                    }
                }
            }
            else if (Flag == "2")
            {
                sSql = "select  '0' as NumCount,TechNo from T_ProductFlow group by TechNo";
                dt = DBHelper.GetDataTable(sSql);
            }
            else
            {
                SqlParameter[] parameters = {
                    new SqlParameter("@lnNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnItem", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnStatus", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnE", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnF", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnG", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnH", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnI", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnJ", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnK", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnL", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,10),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
                parameters[0].Value = No;
                parameters[1].Value = Item;
                parameters[2].Value = Name;
                parameters[3].Value = MNo;
                parameters[4].Value = MName;
                parameters[5].Value = Status;
                parameters[6].Value = sBDate;
                parameters[7].Value = sEDate;
                parameters[8].Value = A;
                parameters[9].Value = B;
                parameters[10].Value = C;
                parameters[11].Value = D;
                parameters[12].Value = E;
                parameters[13].Value = F;
                parameters[14].Value = G;
                parameters[15].Value = H;
                parameters[16].Value = I;
                parameters[17].Value = J;
                parameters[18].Value = K;
                parameters[19].Value = L;
                parameters[20].Value = rows;
                parameters[21].Value = page;
                parameters[22].Value = sLogin;
                parameters[23].Value = Flag;
                parameters[24].Value = "";

                DataSet DST = DBHelper.RunProcedureForDS("P_GetEchartData", parameters);
                dt = DST.Tables[0];
            }

            return dt;
        }


        public static List<DataTable> GetEchartData(string No, string Name, string Item, string MNo, string MName, string Status, string sBDate, string sEDate, string A, string B, string C, string D, string E, string F, string G, string H, string I, string J, string K, string L, int rows, int page, string sLogin, string sComp, string Flag)
        {
            List<DataTable> dataTables = new List<DataTable>();

            SqlParameter[] parameters = {
                    new SqlParameter("@lnNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnItem", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnStatus", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnE", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnF", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnG", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnH", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnI", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnJ", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnK", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnL", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,10),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
            parameters[0].Value = No;
            parameters[1].Value = Item;
            parameters[2].Value = Name;
            parameters[3].Value = MNo;
            parameters[4].Value = MName;
            parameters[5].Value = Status;
            parameters[6].Value = sBDate;
            parameters[7].Value = sEDate;
            parameters[8].Value = A;
            parameters[9].Value = B;
            parameters[10].Value = C;
            parameters[11].Value = D;
            parameters[12].Value = E;
            parameters[13].Value = F;
            parameters[14].Value = G;
            parameters[15].Value = H;
            parameters[16].Value = I;
            parameters[17].Value = J;
            parameters[18].Value = K;
            parameters[19].Value = L;
            parameters[20].Value = rows;
            parameters[21].Value = page;
            parameters[22].Value = sLogin;
            parameters[23].Value = Flag;
            parameters[24].Value = "";

            DataSet DST = DBHelper.RunProcedureForDS("P_GetEchartData", parameters);

            foreach (DataTable item in DST.Tables)
            {
                dataTables.Add(item);
            }

            return dataTables;
        }

        public static string AddTask(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string Remark, string sLogin, string sComp, string Flag)
        {
            string SQL = string.Empty;
            if (Flag == "1")
            {
                //设置参数
                SQL = "UPDATE T_SpcLibraryDetails SET Status='进行中',OpenTime=getDate() WHERE TaskNo = '" + No + "'";
            }
            else if (Flag == "2")
            {
                //设置参数
                SQL = "UPDATE T_SpcLibraryDetails set Status='已结束',CloseTime=getDate()  WHERE TaskNo = '" + No + "'";
            }

            try
            {
                if (SQL != "")
                {
                    DBHelper.ExecuteCommand(SQL);
                    return "Success";
                }
                else
                {
                    return "Success";
                }
            }
            catch (Exception e)
            {
                return "Error:" + e.ToString();
            }
        }

        #region  生成看板编号 240514001
        public static string CreateKBNo(string Flag)
        {
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string str = "";

            if (Flag == "1")
            {
                str = "KB";
            }
            else
            {
                str = "CS";
            }

            string sMaxNo = DBHelper.GetMaxNo("T_BoardLibrary where BoardNo like '" + str + "%'", "BoardNo");
            if (sMaxNo == "")
            {
                sNo = str + CreateAllNo.GetZoreNumString(3) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(2, 4);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 4 - len;

                sNo = str + CreateAllNo.GetZoreNumString(i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion

        #region  生成看板编号 240514001
        public static string CreateSpcNo(string Flag)
        {
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string str = "SPC";

            string sMaxNo = DBHelper.GetMaxNo("T_SpcLibrary where SPCNo like '" + str + "%'", "SPCNo");
            if (sMaxNo == "")
            {
                sNo = str + CreateAllNo.GetZoreNumString(3) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(3, 4);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 4 - len;

                sNo = str + CreateAllNo.GetZoreNumString(i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion


        #region  生成任务编号 240830
        public static string CreateTNo()
        {
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string str = "TNO";

            string sMaxNo = DBHelper.GetMaxNo("T_SpcLibraryDetails where TaskNo like '" + str + "%'", "TaskNo");
            if (sMaxNo == "")
            {
                sNo = str + CreateAllNo.GetDateString(4) + CreateAllNo.GetZoreNumString(3) + "1";
            }
            else
            {//TNO2408
                string sTemp = sMaxNo.Substring(7, 4);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 4 - len;

                sNo = str + CreateAllNo.GetDateString(4) + CreateAllNo.GetZoreNumString(i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion
    }
}
