﻿using System;
using System.Collections;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml.Linq;
using BLL;
using Newtonsoft.Json;
using System.Web.SessionState;
using Common;
using System.Collections.Generic;
using WXCommon;
using System.Text;
using Newtonsoft.Json.Linq;
using System.Net;
using System.IO;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using NPOI.HSSF.UserModel;
using Alex.Kingdee.Cloud.WebAPI.Client;
using NPOI.HSSF.Model;
using NPOI.DDF;
using System.Data.OleDb;
using ExcelComentHelper;
using System.Diagnostics.Eventing.Reader;
using grsvr6Lib;
using Web.appcode;
using NPOI.SS.Formula.Functions;
using Model;
using static NPOI.HSSF.Util.HSSFColor;
using System.Runtime.Remoting.Contexts;
using System.Configuration;
using System.Drawing;
using iTextSharp.text;
using iTextSharp.text.pdf;
using static ICSharpCode.SharpZipLib.Zip.ExtendedUnixData;
using Org.BouncyCastle.Utilities;
using System.Security.Cryptography;
using System.Web.UI.MobileControls;

namespace Web.Service
{
    /// <summary>
    /// $codebehindclassname$ 的摘要说明
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class OrderAjax : IHttpHandler, IRequiresSessionState
    {

        string sAMFlag = string.Empty;
        string IP = string.Empty;
        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "text/plain";
            string Result = string.Empty;
            string Operate = context.Request.Params["OP"];
            string DataParams = context.Request.Params["Data"];
            string sItem = context.Request.Params["Item"];
            sAMFlag = context.Request.Params["CFlag"];
            string sKind = context.Request.Params["CKind"];  // 接收所有类别
            string sStatus = context.Request.Params["CStatus"];  // 状态
            string sCNo = context.Request.Params["CNO"];  // 接收所有编号  
            string sMNo = context.Request.Params["CMNO"];  // 物料编码 
            string sPNo = context.Request.Params["CPNO"];  //  
            string sSPNo = context.Request.Params["CSPNO"];  //  
            string sCName = context.Request.Params["CNAME"];  // 接收所有名称
            string sCustNo = context.Request.Params["CCustNo"];
            string sAddNum = context.Request.Params["AddNum"];  // 数据加载的次数
            IP = context.Request.ServerVariables.Get("Remote_Addr").ToString().Trim();  //获取IP 地址
            int slimit = 0;
            int spage = 0;


            switch (Operate)
            {
                case "GetOrderInfo":  // 获取工单相关信息
                    slimit = int.Parse(context.Request.Params["limit"]);
                    spage = int.Parse(context.Request.Params["page"]);
                    GetOrderInfo(DataParams, slimit, spage, sStatus, sCNo, sKind, sItem, sPNo, sAMFlag);
                    break;

                case "GetOrderInfoByNo": //根据工单号，返回工单基本信息
                    GetOrderInfoByNo(DataParams);
                    break;

                case "OPOrderInfo": //    对生产及工单相关信息进行操作
                    Result = OPOrderInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetOrderInfoFromK3": //    从K3获取工单基本信息
                    Result = GetOrderInfoFromK3(DataParams, sCNo, sItem, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetProcedureList": //获取工序下拉列表    
                    Result = GetProcedureList(sKind, sCNo, sItem, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "OPSerialInfo": //    对生产及序列号相关信息进行操作  
                    Result = OPSerialInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "GetPRDInfo":  // 获取生产相关信息
                    slimit = int.Parse(context.Request.Params["limit"]);
                    spage = int.Parse(context.Request.Params["page"]);
                    GetPRDInfo(DataParams, slimit, spage, sStatus, sCNo, sItem, sAMFlag);
                    break;

                case "GetPRDInfoTwo": //获取生产相关信息  -- BOM，流程等
                    Result = GetPRDInfoTwo(sCNo, sItem, sSPNo, sKind, sAMFlag);
                    break;
                case "GetPRDTestInfo"://获取生产测试项信息
                    GetPRDTestInfo(DataParams);
                    break;
                case "GetWorkCenter": //作业执行：获取，判断登录的作业单元
                    GetWorkCenter(DataParams, sCNo, sItem, sMNo, sPNo, sAMFlag);
                    break;

                case "GetSerialInfoByNo": //根据序列号，获取相关信息
                    GetSerialInfoByNo(DataParams, sCNo, sItem, sMNo, sPNo, sAddNum, sAMFlag);
                    break;

                case "OPPRDDeviceInfo": //    对生产相关信息进行操作--扫描设备信息  
                    Result = OPPRDDeviceInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "OPPRDChangeVerInfo": //    对生产相关信息进行操作--更新工序版本 
                    Result = OPPRDChangeVerInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "OPPRDChangeManInfo": //    对生产相关信息进行操作--更改作业人员
                    Result = OPPRDChangeManInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "OPPRDChangeUnitInfo": //    对生产相关信息进行操作--更改作业单元
                    Result = OPPRDChangeUnitInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "OPPRDTestInfo": //    对生产相关信息进行操作--操作测试项
                    Result = OPPRDTestInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "OPPRDMaterInfo": //    对生产相关信息进行操作--扫描追溯物料，装箱物料的序列号批号
                    Result = OPPRDMaterInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "OPPRDUpbatchInfo": //    对生产相关信息进行操作--绑定，完成关联上层批次
                    Result = OPPRDUpbatchInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetInspectInfo": // 对生产相关信息进行操作--显示检验方案信息
                    GetInspectInfo(DataParams, sCNo, sItem, sMNo, sPNo, sAMFlag);
                    break;

                case "OPInspectInfo": //   更改检验方案
                    Result = OPInspectInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "OPPRDOverInfo": //    对生产相关信息进行操作  -- 完工
                    Result = OPPRDOverInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "OPPRDYCCodeInfo": //    对生产相关信息进行操作-- 扫描异常代码
                    Result = OPPRDYCCodeInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "OPReworkOrder": //   重工的相关操作
                    Result = OPReworkOrder(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "OPPRDInfo": //    对生产相关信息进行操作  
                    Result = OPPRDInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "OPECodeInfo": //    不合格处理
                    Result = OPECodeInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "PrdPackToExcel":  // 生产执行，包装信息导出EXCEL  
                    Result = PrdPackToExcel(DataParams, sCNo);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "SerialToExcel":  // 工单及序列号信息导出EXCEL  
                    Result = SerialToExcel(DataParams, sCNo);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "SerialTracToExcel":  // 序列号的追溯信息导出EXCEL  
                    Result = SerialTracToExcel(DataParams, sCNo);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;
                case "SerialTracToExcel2":  // 序列号的追溯信息导出EXCEL  
                    Result = SerialTracToExcel2(DataParams, sCNo);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "PrdExceptionToExcel":  // 导出生产异常信息及分析信息到EXCEL
                    Result = PrdExceptionToExcel(DataParams, sCNo, sAMFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetWXInfo": //维修：获取维修相关信息
                    GetWXInfo(DataParams, sCNo, sItem, sMNo, sPNo, sAMFlag);
                    break;

                case "GetDefectsCauseByNo": //维修界面：根据不良原因代码返回描述  
                    GetDefectsCauseByNo(DataParams, sCNo, sAMFlag);
                    break;

                case "OPWXInfo": //   w维修模块：对系统各种操作
                    Result = OPWXInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetInStockWNo": //入库申请，扫描入库仓位，显示相关信息
                    GetInStockWNo(DataParams, sCNo, sItem, sMNo, sPNo, sAMFlag);
                    break;

                case "ScanBatchNo": //入库申请：扫描/输入序列号，检验批次，工单，获取需要入库的信息
                    ScanBatchNo(DataParams, sCNo, sItem, sMNo, sKind, sAddNum, sAMFlag);
                    break;

                case "OPInStockInfo": //   入库申请：对系统各种操作
                    Result = OPInStockInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "OPPRDQualityInfo": //    对生产相关信息进行操作--扫描质控品信息  
                    Result = OPPRDQualityInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetThirdTestItem": //    获取第三方系统的测试数据
                    Result = GetThirdTestItem(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetExportConditions":  // 作业执行：打印DHR报表，获取序列号对应的工序
                    Result = GetExportConditions(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetExportData":     // 作业执行：打印DHR报表，循环每个序列号下的工序，打印DHR报表
                    GetExportData(DataParams, context);
                    break;

                case "ToK3StockInfo": //    同步入库信息到K3系统 
                    Result = ToK3StockInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;
                case "DHRMergeOP": //    同步入库信息到K3系统 
                    DHRMergeOP(DataParams, context);
                    break;

                case "GetUpBatchInfo": // 获取包装批次及对应数量
                    GetUpBatchInfo(DataParams, sCNo, sAMFlag);
                    break;
                case "BatchManipulateData":      //批量添加、删除
                    Result = BatchManipulateData(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;
                case "OPPrdSamplingInfo":      // 对生产执行信息进行操作--抽检关联/ 抽检完工
                    Result = OPPrdSamplingInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;












            }
        }









        #region 获取生产相关的信息
        public void GetOrderInfo(string Params, int rows, int page, string sStatus, string sCQNo, string sWO, string sItmNo, string PNo, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sReturn = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;
            string sItem = string.Empty;
            string sName = string.Empty;
            string sMNo = string.Empty;
            string sMName = string.Empty;
            string sSt = string.Empty;
            string sBDate = string.Empty;
            string sEDate = string.Empty;
            string sA = string.Empty;
            string sB = string.Empty;
            string sC = string.Empty;
            string sD = string.Empty;
            int iSumCount = 0;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return;
            }

            var AnonymousUser = new
            {
                No = String.Empty,
                Item = String.Empty,
                Name = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                Status = String.Empty,
                BDate = String.Empty,
                EDate = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
            };



            if (string.IsNullOrEmpty(Params) || (Params == null) || (Params == "null"))
            {
                sNo = ""; sItem = ""; sName = ""; sMNo = ""; sMName = ""; sSt = ""; sBDate = ""; sEDate = "";
                sA = ""; sB = ""; sC = ""; sD = "";
            }
            else
            {
                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

                sNo = Item.No; sItem = Item.Item; sName = Item.Name; sMNo = Item.MNo; sMName = Item.MName; sSt = Item.Status;
                sBDate = Item.BDate; sEDate = Item.EDate; sA = Item.A; sB = Item.B; sC = Item.C; sD = Item.D;
            }
            if (sBDate == "")
            {
                sBDate = "2021-01-01";
            }
            if (sEDate == "")
            {
                sEDate = "2999-12-30";
            }


            if (string.IsNullOrEmpty(sNo))
            {  // 主要给传入工单号查询信息的，如查询产品流程
                if (string.IsNullOrEmpty(sCQNo) || (sCQNo == null) || (sCQNo == "null"))
                {
                    sNo = "";
                }
                else
                {
                    sNo = sCQNo;
                }
            }
            if (string.IsNullOrEmpty(sItem))
            {  // 主要给传入工单号查询信息的，如查询产品流程
                if (string.IsNullOrEmpty(sItmNo) || (sItmNo == null) || (sItmNo == "null"))
                {
                    sItem = "";
                }
                else
                {
                    sItem = sItmNo;
                }
            }
            if (string.IsNullOrEmpty(sA))
            {
                if (string.IsNullOrEmpty(PNo) || (PNo == null) || (PNo == "null"))
                {
                    sA = "";
                }
                else
                {
                    sA = PNo;
                }
            }
            if (string.IsNullOrEmpty(sB))
            {  // 主要是 作业执行查询 界面，插入工单号  111-21
                if (string.IsNullOrEmpty(sWO) || (sWO == null) || (sWO == "null"))
                {
                    sB = "";
                }
                else
                {
                    sB = sWO;
                }
            }
            //作业执行查询、记录在线更改
            if (Flag == "230")
            {
                if (string.IsNullOrEmpty(sC))
                {
                    sC = "2021-01-01";
                }

                if (string.IsNullOrEmpty(sD))
                {
                    sD = "2999-12-30";
                }
            }

            DataTable dt = OrderBll.GetOrderInfo(sNo, sItem, sName, sMNo, sMName, sSt, sBDate, sEDate, sA, sB, sC, sD, rows, page, sMan, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                iSumCount = int.Parse(dt.Rows[0]["NumCount"].ToString());

                string sJson = JsonConvert.SerializeObject(dt);
                sReturn = "{\"code\":0,\"msg\":\"\",\"count\":" + iSumCount + ",\"data\":" + sJson + "}";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;
            context.Response.Write(sReturn);
        }
        #endregion




        #region 根据工单号，返回工单基本信息
        public void GetOrderInfoByNo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;
            string sFlag = string.Empty;

            HttpContext context = HttpContext.Current;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }


            // No: sNo, Name: sName, Spec: sSpec,BT:sBT
            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { No = String.Empty, Flag = String.Empty, };

                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sNo = Item.No; sFlag = Item.Flag;
            }


            DataTable dt = OrderBll.GetOrderInfo(sNo, "", "", "", "", "", "", "", "", "", "", "", 200, 1, sMan, sComp, sFlag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "系统无此工单，请先到工单界面获取！";
            }

            string json = JsonConvert.SerializeObject(dt);

            Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });

            context.Response.Write(Result);

            // return Result;

        }
        #endregion



        #region 对生产执行或工单信息进行操作
        public string OPOrderInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            string sWO = string.Empty;
            string sA = string.Empty;
            string sDNo = string.Empty;
            string sSNum = string.Empty;
            string sSpecNo = string.Empty;



            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                K = String.Empty,
                L = String.Empty,
                M = String.Empty,
                N = String.Empty,
                O = String.Empty,
                P = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            sA = It.A;  // 工单绑定序列号用到这个，所以单独拿这个赋值
            sWO = It.Item;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            // 这个工单如果已暂停，不能再继续操作了  -- 231111：增加了这个，已暂停的工单无法恢复了。再考虑如何实现
            //sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "1-1");
            //if (sOKFlag == "Y")  // 判断新增的工单信息是否存在了
            //{
            //    Message = "Y_JY";
            //    return JsonConvert.SerializeObject(new { Msg = Message });
            //}

            if (It.Flag == "1") // 新增工单
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.Name, "", sComp, "1");
                if (sOKFlag == "Y")  // 判断新增的工单信息是否存在了
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "4") //删除工单
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "4");
                if (sOKFlag == "Y")  // 判断序列号是否已生产
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "7") //修改工单流程对应的工序，判断工序是否已生产
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "4");
                if (sOKFlag == "Y")  // 判断序列号是否已生产
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                //工单流程 判断当前修改工序的工序行为有没有选择 送检抽检合并
                sOKFlag = OrderBll.JudgeObjectExist(It.B, "", "", sComp, "43-2");
                if (sOKFlag == "Y")
                {
                    // 选择了送检抽检合并工序行为，检查抽样方式和抽样方案是否为空
                    if (It.L == "" || It.J == "")//It.L 抽样方式  It.J 抽样方案
                    {
                        Message = "Y_FAFSNULL";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                    if (It.L == "工序测试项抽样")//选择的是工序测试项抽样就需要判断是否有设置抽样的测试项
                    {
                        sOKFlag = OrderBll.JudgeObjectExist(It.Item, It.No, "", sComp, "47-1");
                        if (sOKFlag == "N")
                        {
                            Message = "Y_NOTEST";
                            return JsonConvert.SerializeObject(new { Msg = Message });
                        }
                    }
                    //else
                    //{
                    //    //选择的是工序抽样就需要判断是否有设置抽样的测试项,如果有就不能保存
                    //    sOKFlag = OrderBll.JudgeObjectExist(It.Item, It.No, "", sComp, "47-2");
                    //    if (sOKFlag == "Y")
                    //    {
                    //        Message = "Y_ISTEST";
                    //        return JsonConvert.SerializeObject(new { Msg = Message });
                    //    }
                    //}
                }
                else
                {
                    // 没有选择送检抽检合并工序行为，检查抽样方式和抽样方案是否不为空
                    if (It.L != "" || It.J != "")//It.L 抽样方式  It.J 抽样方案
                    {
                        Message = "Y_FAFSNOTNULL";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                    //sOKFlag = OrderBll.JudgeObjectExist(It.Item, It.No, "", sComp, "47-2");
                    //if (sOKFlag == "Y")
                    //{
                    //    Message = "Y_NOTISTEST";
                    //    return JsonConvert.SerializeObject(new { Msg = Message });
                    //}
                }
            }
            else if (It.Flag == "8") //添加工单流程对应的工序，判断工序是否已存在
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, It.No, "", sComp, "8");
                if (sOKFlag == "Y")  // 判断是否用于产品流程
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "4");
                if (sOKFlag == "Y")  // 判断序列号是否已生产
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                //工单流程 判断当前修改工序的工序行为有没有选择 送检抽检合并
                sOKFlag = OrderBll.JudgeObjectExist(It.B, "", "", sComp, "43-2");
                if (sOKFlag == "Y")
                {
                    // 选择了送检抽检合并工序行为，检查抽样方式和抽样方案是否为空
                    if (It.L == "" || It.J == "")//It.L 抽样方式  It.J 抽样方案
                    {
                        Message = "Y_FAFSNULL";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                    if (It.L == "工序测试项抽样")//选择的是工序测试项抽样就需要判断是否有设置抽样的测试项
                    {
                        sOKFlag = OrderBll.JudgeObjectExist(It.Item, It.No, "", sComp, "47-1");
                        if (sOKFlag == "N")
                        {
                            Message = "Y_NOTEST";
                            return JsonConvert.SerializeObject(new { Msg = Message });
                        }
                    }
                    //else
                    //{
                    //    //选择的是工序抽样就需要判断是否有设置抽样的测试项,如果有就不能保存
                    //    sOKFlag = OrderBll.JudgeObjectExist(It.Item, It.No, "", sComp, "47-2");
                    //    if (sOKFlag == "Y")
                    //    {
                    //        Message = "Y_ISTEST";
                    //        return JsonConvert.SerializeObject(new { Msg = Message });
                    //    }
                    //}
                }
                else
                {
                    // 没有选择送检抽检合并工序行为，检查抽样方式和抽样方案是否不为空
                    if (It.L != "" || It.J != "")//It.L 抽样方式  It.J 抽样方案
                    {
                        Message = "Y_FAFSNOTNULL";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                    //sOKFlag = OrderBll.JudgeObjectExist(It.Item, It.No, "", sComp, "47-2");
                    //if (sOKFlag == "Y")
                    //{
                    //    Message = "Y_ISTEST";
                    //    return JsonConvert.SerializeObject(new { Msg = Message });
                    //}
                }
            }
            else if ((It.Flag == "6") || (It.Flag == "7") || (It.Flag == "9")) //删除工单流程，
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "4");
                if (sOKFlag == "Y")  // 判断序列号是否已生产
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "13")  //添加工单BOM
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, It.No, It.MNo, sComp, "13");
                if (sOKFlag == "Y")  // 判断该工单和工序是否存在该物料 
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "4");
                if (sOKFlag == "Y")  // 判断序列号是否已生产
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "12") || (It.Flag == "14")) // 12 修改， 14 删除
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "4");
                if (sOKFlag == "Y")  // 判断序列号是否已生产
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "14-1")  //工单：添加替代料
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "18");
                if (sOKFlag == "Y")
                {// 判断序列号是否已生产
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sDNo = "(" + It.C + ")" + It.D;
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, sDNo, It.A, sComp, "13");
                if (sOKFlag == "Y")
                {// 判断是否已添加过
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, It.MNo, It.C, sComp, "14-1");
                if (sOKFlag == "Y")
                {// 不能给本身是替代料的物料，再增加替代料
                    Message = "Y_EXISTTD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "14-3")  //判断工单工序是否存在该设备了
            {
                string sGX = It.No + It.A;
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, sGX, It.MNo, sComp, "14-3-0");
                if (sOKFlag == "Y")  // 工单工序工序是否存在该设备
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "14-3-1")  //判断工单工序是否存在该质控品了
            {
                string sGX = It.No + It.A;
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, sGX, It.MNo, sComp, "14-3-1");
                if (sOKFlag == "Y")  // 工单工序工序是否存在该质控品
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "14-5") || (It.Flag == "14-5-3")) //判断工单工序文件是否存在; 14-5:新增工艺文件  14-5-3：选择工艺文件
            {
                string sGX = It.No + It.A;
                string sFile = It.D + It.E;
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, sGX, sFile, sComp, "14-5");
                if (sOKFlag == "Y")  // 工单工序工艺文件已存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "14-5-1") || (It.Flag == "14-5-2"))  //判断序列号工序文件是否存在
            {
                string sGX = It.No + It.A;
                string sFile = It.D + It.E;
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, sGX, sFile, sComp, "14-5-1");
                if (sOKFlag == "Y")  // 序列号工序工艺文件已存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "14-8") || (It.Flag == "14-11"))  //工单，工序增加测试项  14-8:新增，增加在后面；14-11：当前行插入新测试项；
            {
                sSpecNo = CreateSpecNo();
                string sPNVer = It.Item + It.No;  // 工单+工序+版本
                string sHB = It.Name + It.MNo;  // 检验项目+要求不能重复
                sOKFlag = OrderBll.JudgeObjectExist(sSpecNo, sPNVer, sHB, sComp, "14-8");
                if (sOKFlag == "Y")  //   判断工单工序对应测试项是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "14-9")  //工单，14-9 修改测试项
            {
                sSpecNo = It.E;
                string sPNVer = It.Item + It.No;  // 工单+工序+版本
                sOKFlag = OrderBll.JudgeObjectExist(sSpecNo, sPNVer, It.Name, sComp, "14-8");
                if (sOKFlag == "Y")  //   判断工单工序对应测试项是否存在 
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "14-14")
            {
                string sGX = "(" + It.K + ")" + It.A;
                sOKFlag = OrderBll.JudgeObjectExist(It.L + It.Item, sGX, "", sComp, "13-2");  // It.Item+It.B  序列号+工单
                if (sOKFlag == "Y")  // 判断该序列号工序是否已完成如果已完成则不给操作
                {
                    Message = "Y_FlOWOVER";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                if (It.Flag == "14-14" && It.J == "ZDWZ") //序列号工序选择测试判断是否已测试
                {
                    sOKFlag = OrderBll.JudgeObjectExist(It.Item + It.L, It.K + It.B, It.F, sComp, "36-1");  // It.Item+It.B  序列号+工单
                    if (sOKFlag == "Y")  // 序列号工序选择测试判断是否已测试
                    {
                        Message = "Y_EXISTPRD";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }
            }
            //else if (It.Flag == "16")  //工单判断工单BOM是否存在
            //{
            //    sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "16");
            //    if (sOKFlag == "Y")  // 说明工单BOM已存在
            //    {
            //        Message = "Y_EXIST";
            //        return JsonConvert.SerializeObject(new { Msg = Message });
            //    }
            //}
            else if (It.Flag == "17")
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "17-1");
                if (sOKFlag == "Y")  //判断产品流程有没有被禁用
                {
                    Message = "Y_FlowDisable";
                    return JsonConvert.SerializeObject(new { Msg = Message, RMgs = "该工单对应流程中存在状态为禁用。" });
                }
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "17-2");
                if (sOKFlag == "Y")  // 判断产品流程当前使用版本是否被禁用
                {
                    Message = "Y_VerDisable";
                    return JsonConvert.SerializeObject(new { Msg = Message, RMgs = "该工单对应流程中版本存在状态为禁用。" });
                }
            }
            else if (It.Flag == "18")  //工单-删除对应序列号，判断是否已投产，
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.Item, "", sComp, "39-1");
                if (sOKFlag == "Y")  // 说明已生产，不能删除
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "18-2")  //工单-删除对应序列号，判断是否已投产，
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.Item, "", sComp, "39-2");
                if (sOKFlag == "Y")  // 说明已生产，不能删除
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "18-3")  //工单-启动序列号
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.MNo, "", sComp, "39-3");
                if (sOKFlag == "Y")  // 判断序列号有没有被使用，如果使用了不能启动
                {
                    Message = "Y_EXISTUSE";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.MNo, "", sComp, "39-4");
                if (sOKFlag == "Y")  // 判断序列号是不是被关闭的，如果不是这不能启动
                {
                    Message = "Y_EXISTCLOSE";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

            }
            else if (It.Flag == "20")  //判断序列号是否存在
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "20-0");
                if (sOKFlag == "N")  // 说明序列号不存在
                {
                    Message = "Y_NOTEXISTSN";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sA = sOKFlag;  // 原序列号的工单号

                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.Item, "", sComp, "20-1");
                if (sOKFlag == "Y")  // 判断工单+序列号是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.Item, "", sComp, "20-2");
                if (sOKFlag == "N")  // 判断该序列号是否已烧录完成，只有烧录完成的，才可以绑定;=N 标识没有烧录或还在烧录中
                {
                    Message = "N_NOTSHAOLU";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

            }
            else if (It.Flag == "21")  // 删除工单绑定的序列号
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "21-1");
                if (sOKFlag == "Y")  // 说明工单序列号已投产，不能删除了。
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

            }
            else if (It.Flag == "22")  // 手工添加一个工单
            {
                // 如果工单号空，就自己产生一个,规则：60-yymm-4位流水号
                if (string.IsNullOrEmpty(It.Item))
                {
                    sWO = CreateOrderNo();
                }

                sOKFlag = OrderBll.JudgeObjectExist(sWO, "", "", sComp, "22");
                if (sOKFlag == "Y")  // 说明工单已存在了
                {
                    Message = "Y_EXISTWO";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "23")  // 修改工单基本信息
            {
                if (It.G != "ZDY")
                {  //主要给自定义发放序列号时，第一次可修改工单的基本信息
                    sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "23");
                    if (sOKFlag == "Y")  // = Y 说明工单的序列号已全部获取过来了
                    {
                        Message = "Y_EXISTSN";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }

                // 如果工单没有流程，不给下达
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "23-1");
                if (sOKFlag == "N")  // = N 说明工单没有流程
                {
                    Message = "Y_NotWOFlow";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                if (It.E == "直接入库-返工生产")//只有工单类型等于 直接入库-返工生产 才需要进行一下判断
                {
                    //如果工单的第一条工艺路线，有没有设置重工工序行为，则不能下达
                    sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "23-3");
                    if (sOKFlag == "N")  // = N 说明该工单第一条工艺路线没有设置重工工序行为
                    {
                        Message = "Y_NotWOAction";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }

                    //如果工单没有设置重工对象，则不能下达
                    sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "23-4");
                    if (sOKFlag == "N")  // = N 说明该重工单没有设置重工对象
                    {
                        Message = "Y_NotWOMater";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }

                    //此时已经设置重工对象在判断是否设置在第一道工序上，如过没有不能下达
                    sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "23-5");
                    if (sOKFlag != "Y")
                    {
                        Message = "Y_NotWOProc";
                        return JsonConvert.SerializeObject(new { Msg = Message, RMsg = sOKFlag });
                    }
                }

            }
            else if (It.Flag == "31")//继承工单信息
            {
                // 如果工单没有流程，不给下达
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "23-1");
                if (sOKFlag == "N")  // = N 说明工单没有流程
                {
                    Message = "Y_NotWOFlow";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                if (It.E == "直接入库-返工生产")//只有工单类型等于 直接入库-返工生产 才需要进行一下判断
                {
                    //如果工单的第一条工艺路线，有没有设置重工工序行为，则不能下达
                    sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "23-3");
                    if (sOKFlag == "N")  // = N 说明该工单第一条工艺路线没有设置重工工序行为
                    {
                        Message = "Y_NotWOAction";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }

                    //如果工单没有设置重工对象，则不能下达
                    sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "23-4");
                    if (sOKFlag == "N")  // = N 说明该重工单没有设置重工对象
                    {
                        Message = "Y_NotWOMater";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }

                    //此时已经设置重工对象在判断是否设置在第一道工序上，如过没有不能下达
                    sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "23-5");
                    if (sOKFlag != "Y")
                    {
                        Message = "Y_NotWOProc";
                        return JsonConvert.SerializeObject(new { Msg = Message, RMsg = sOKFlag });
                    }
                }
            }





            sOKFlag = OrderBll.OPOrderInfo(It.No, It.Name, sWO, It.MNo, It.MName, sA, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, It.K, It.L, It.M, It.N, It.O, It.P, sSpecNo, sComp, sLogin, It.Remark, It.Flag);
            if (It.Flag == "1")
            {
                Message = sOKFlag;
            }
            else if (sOKFlag.IndexOf("Success") > 0)
            {  // 6 后台返回插入记录的总数， 6 表示能插入十万级别的记录
                Message = "Success";

                if ((It.Flag == "23") && (It.G != "ZDY"))
                {   // 表示修改工单，这个时候同步工单的序列号及发料记录
                    sA = GetOrderInfoFromK3("", It.Item, It.F, "4"); // 同步发料信息

                    sA = GetOrderInfoFromK3("", It.Item, It.F, "3"); // 同步序列号信息
                    if (sA.IndexOf("Y_EXISTSN") >= 0 || sA.IndexOf("Y_EXISTZJSN") >= 0)
                    {
                        //sOKFlag = sOKFlag + " " + sA.Substring(sA.IndexOf("序列号同步失败："), sA.Length - sA.IndexOf("序列号同步失败：") - 2);
                        sOKFlag += sOKFlag + " " + " 序列号同步失败：" + (sA.IndexOf("Y_EXISTSN") >= 0 ? "新获取的序列号在其他工单未生产完成，不能变更" : "该主机工单对应的序列号已在整机工单上");

                        JObject jsonObject = JObject.Parse(sA);

                        sA = jsonObject.ContainsKey("RMgs") ? jsonObject["RMgs"].ToString() : "";

                        var dt = OrderBll.GetOrderInfo(sA, "", "", "", "", "", "", "", "", "", "", "", 0, 0, "", "", "111-24");
                        Result = JsonConvert.SerializeObject(dt);
                        Message = "NOTCHANGE";

                    }

                }

                sOKFlag = sOKFlag.Replace("Success ", "");
                sOKFlag = sOKFlag.Replace("Success：", "");
                sOKFlag = sOKFlag.Replace("1Success", "操作成功");  // 19 保存信息
                sOKFlag = sOKFlag.Replace("Success", "操作成功");
            }
            else
            {
                Message = sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo, RMgs = sOKFlag, Data = Result });

            return Result;
        }
        #endregion



        #region 对生产执行或序列号信息进行操作
        public string OPSerialInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            string sDNo = string.Empty;
            string sSpecNo = string.Empty;
            string sSNum = string.Empty;


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                K = String.Empty,
                L = String.Empty,
                M = String.Empty,
                N = String.Empty,
                O = String.Empty,
                P = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }



            if (It.Flag == "1") // 新增序列号  -- 没有用
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.Name, "", sComp, "1");
                if (sOKFlag == "Y")  // 判断新增的工单信息是否存在了
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            if (It.Flag == "3") // 工单界面：发放序列号
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, It.A, "", sComp, "3-1");
                if (sOKFlag == "Y")  // 判断是否已发放序列号
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "8") //添加序列号流程对应的工序，判断工序是否已存在
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, It.No, It.G, sComp, "8-1");
                if (sOKFlag == "Y")  // 判断是否用于产品流程
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, It.No, It.G, sComp, "8-2");
                if (sOKFlag == "Y")  // 说明序列号在生产中
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

            }
            else if (It.Flag == "6")  // 上下移动
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, It.No, It.E, sComp, "8-2-1");
                if (sOKFlag == "Y")  // 说明序列号生产中
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "7") //  7：修改   
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, It.No, It.G, sComp, "8-2");
                if (sOKFlag == "Y")  // 说明序列号+工序在生产中
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "9") // 9 ：删除
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, It.No, It.E, sComp, "8-2");
                if (sOKFlag == "Y")  // 说明序列号+工序在生产中
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "13")  //添加序列号BOM
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item + It.D, It.No, It.MNo, sComp, "13-1");  // It.Item+It.D  序列号+工单
                if (sOKFlag == "Y")  // 判断该序列号和工序是否存在该物料 
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sOKFlag = OrderBll.JudgeObjectExist(It.Item + It.D, It.No, It.MNo, sComp, "13-2");  // It.Item+It.D  序列号+工单
                if (sOKFlag == "Y")  //序列号： 判断该序列号工序是否已完成如果已完成则不给添加 
                {
                    Message = "Y_FlOWOVER";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "12") || (It.Flag == "14") || (It.Flag == "14-2")) // 12 修改， 14 删除  , 14-2 ：删除替代料
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item + It.D, It.No, It.MNo, sComp, "8-3-1");
                if (sOKFlag == "Y")  //序列号 ：判断操作的物料是否已扫描，扫了就不给操作
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "14-1")  //序列号：添加替代料
            {
                sDNo = "(" + It.C + ")" + It.D;
                sOKFlag = OrderBll.JudgeObjectExist(It.Item + It.No, sDNo, It.MNo, sComp, "8-3-1");
                if (sOKFlag == "Y")
                {//序列号 ：判断操作的物料是否已扫描，扫了就不给操作
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sOKFlag = OrderBll.JudgeObjectExist(It.Item + It.No, sDNo, It.A, sComp, "13-1");  // It.Item+It.No  序号+工单
                if (sOKFlag == "Y")
                {// 判断是否已添加过
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sOKFlag = OrderBll.JudgeObjectExist(It.Item + It.No, It.MNo, It.C, sComp, "14-2"); // It.Item+It.No  序号+工单
                if (sOKFlag == "Y")
                {// 不能给本身是替代料的物料，再增加替代料
                    Message = "Y_EXISTTD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "14-3")
            {
                string sGX = "(" + It.No + ")" + It.Name;
                sOKFlag = OrderBll.JudgeObjectExist(It.Item + It.B, sGX, "", sComp, "13-2");  // It.Item+It.B  序列号+工单
                if (sOKFlag == "Y")  // 判断该序列号工序是否已完成如果已完成则不给添加 
                {
                    Message = "Y_FlOWOVER";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sGX = It.No + It.A;
                sOKFlag = OrderBll.JudgeObjectExist(It.Item + It.No, sGX, It.MNo, sComp, "14-3-2");  // It.Item+It.No  序号+工单
                if (sOKFlag == "Y")  // 判断该序列号工序是否存在该设备
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "14-3-1")  //判断工单工序是否存在该质控品了
            {
                string sGX = It.No + It.A;
                sOKFlag = OrderBll.JudgeObjectExist(It.Item + It.No, sGX, It.MNo, sComp, "14-3-3");
                if (sOKFlag == "Y")  // 工单工序工序是否存在该质控品
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "14-4") //判断序列号工序设备有没有扫描
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.D + It.Item, It.No + It.A, It.MNo, sComp, "35-1");
                if (sOKFlag == "Y")  // 判断序列号工序设备有没有扫描
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "14-5-1") || (It.Flag == "14-5-2"))  //判断序列号工序文件是否存在
            {
                string sGX = It.No + It.A;
                string sFile = It.D + It.E;
                sOKFlag = OrderBll.JudgeObjectExist(It.Item + It.MNo, sGX, sFile, sComp, "14-5-1");  // It.Item+It.MNo  序列号+工单号
                if (sOKFlag == "Y")  // 序列号工序工艺文件已存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "14-8-1") || (It.Flag == "14-11-1"))  //序列号，工序增加测试项  14-8-1:新增，增加在后面；14-11-1：当前行插入新测试项 
            {
                sSpecNo = CreateSpecNo();
                string sPNVer = It.Item + It.L + It.No;  // 序列号+It.L工单+ 工序+版本
                string sHB = It.Name + It.MNo;  //检验项目+要求不能重复
                sOKFlag = OrderBll.JudgeObjectExist(It.Item + It.L, It.N, "", sComp, "13-2");  // It.Item+It.B  序列号+工单
                if (sOKFlag == "Y")  // 判断该序列号工序是否已完成如果已完成则不给操作
                {
                    Message = "Y_FlOWOVER";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                if (It.Flag == "14-11-1") //序列号工序选择测试判断是否已测试
                {
                    sOKFlag = OrderBll.JudgeObjectExist(It.L + It.Item, It.No, It.F, sComp, "36-1");  // It.Item+It.B  序列号+工单
                    if (sOKFlag == "Y")  // 序列号工序选择测试判断是否已测试
                    {
                        Message = "Y_EXISTPRD";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }
                sOKFlag = OrderBll.JudgeObjectExist(sSpecNo, sPNVer, sHB, sComp, "14-8-1");
                if (sOKFlag == "Y")  //   判断序列号工序对应测试项是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "14-9-1")  //序列号 14-9-1：修改测试项
            {
                string sPNVer = It.Item + It.No;  // 工序+版本
                sSpecNo = It.E;
                sOKFlag = OrderBll.JudgeObjectExist(It.L + It.Item, It.No, It.E, sComp, "36-3");
                if (sOKFlag == "Y")// 判断序列号工序测试项是否已测试
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sOKFlag = OrderBll.JudgeObjectExist(sSpecNo, sPNVer, It.Name, sComp, "14-8-1");
                if (sOKFlag == "Y")  //   判断工单工序对应测试项是否存在
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if ((It.Flag == "14-10") || (It.Flag == "14-15"))  // 10修改测试项,删除测试项  15 :设置是否涉及
            {
                sSpecNo = It.E;
                string sNoVer = It.Flag == "14-10" ? It.No : It.No + It.G;//两个操作传参方式不同，使用三元运算方式区分
                sOKFlag = OrderBll.JudgeObjectExist(It.L + It.Item, sNoVer, It.E, sComp, "36-3");
                if (sOKFlag == "Y")// 判断序列号工序测试项是否已测试
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "14-10-1")  // 删除多个测试项判断是否勾选了已测试的，有就不给删除
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.L + It.Item, It.No, It.E, sComp, "36-3-1");
                if (sOKFlag == "Y")// 判断序列号工序多个测试项是否已测试
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "14-12")//序列号测试项
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.L + It.Item, It.No + It.G, It.H, sComp, "36-3"); // It.L+It.Item 工单+序列号
                if (sOKFlag == "Y")// 判断序列号工序测试项是否已测试
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                if (It.F == "up")
                {
                    sOKFlag = OrderBll.JudgeObjectExist(It.L + It.Item, It.No + It.G, It.A, sComp, "36-2");  // It.L+It.Item 工单+序列号
                    if (sOKFlag == "Y")  // 序列号工序测试项的上一项是否已测试
                    {
                        Message = "Y_FRONTPRD";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }
            }
            else if (It.Flag == "15") // 15 :在工单界面，删除序列号。判断序列号是否已投产
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, It.No, It.MNo, sComp, "8-3");
                if (sOKFlag == "Y")  // 说明序列号+工序+物料+在生产中
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }


            sOKFlag = OrderBll.OPSerialInfo(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, It.K, It.L, It.M, It.N, It.O, It.P, sSpecNo, sComp, sLogin, It.Remark, It.Flag);
            //if (sOKFlag.Length <= 2)
            if (sOKFlag.IndexOf("Success") > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            sOKFlag = sOKFlag.Replace("Success ", "");
            sOKFlag = sOKFlag.Replace("Success：", "");
            sOKFlag = sOKFlag.Replace("Success", "操作成功：");

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo, RMsg = sOKFlag });

            return Result;
        }
        #endregion


        #region 从K3获取工单基本信息
        public string GetOrderInfoFromK3(string Params, string WO, string Item, string Flag)
        {
            string Result = string.Empty;
            string sResult = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRMsg = string.Empty;
            string K3_GetUrl1 = string.Empty;
            string K3_dbId = string.Empty;
            string K3_userName = string.Empty;
            string K3_password = string.Empty;
            int K3_lcid = 0;
            string sFlag = string.Empty;
            string sNo = string.Empty;
            string sName = string.Empty;
            string sDept = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sLogin });
                return Result;
            }

            if (string.IsNullOrEmpty(Params) || (Params == null) || (Params == "null"))
            {
                sFlag = Flag; sNo = WO; sName = ""; sDept = Item;
            }
            else
            {
                var AnonymousUser = new
                {
                    No = String.Empty,
                    Name = String.Empty,
                    Item = String.Empty,
                    MNo = String.Empty,
                    MName = String.Empty,
                    A = String.Empty,
                    B = String.Empty,
                    C = String.Empty,
                    D = String.Empty,
                    E = String.Empty,
                    F = String.Empty,
                    Remark = String.Empty,
                    Flag = String.Empty,
                };

                var Item2 = JsonConvert.DeserializeObject(Params);
                var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sFlag = It.Flag; sNo = It.No; sName = It.Name; sDept = It.F;
            }



            if (sFlag == "2") // 同步K3工单
            {
                sOKFlag = OrderBll.JudgeObjectExist(sNo, sName, "", sComp, "1");
                if (sOKFlag == "Y")  // 判断新增的工单信息是否存在了
                {
                    Message = "Y_EXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }

            // 获取K3数据的链接地址，账号密码 label1
            DataTable dtK3 = OrderBll.GetK3UrlInfo("获取K3Url相关信息", sComp);
            if (dtK3.Rows.Count > 0)
            {
                K3_GetUrl1 = dtK3.Rows[0]["C1"].ToString();
                K3_dbId = dtK3.Rows[0]["C2"].ToString();
                K3_userName = dtK3.Rows[0]["C3"].ToString();
                K3_password = dtK3.Rows[0]["C4"].ToString();
                K3_lcid = int.Parse(dtK3.Rows[0]["C5"].ToString());
            }
            else
            {
                sRMsg = "获取不到同步K3的相关地址链接！";
                Message = "Y_NotEXIST";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }

            sResult = GetK3Info(sNo, Item, sName, K3_GetUrl1, K3_dbId, K3_userName, K3_password, K3_lcid, sFlag); // 获取工单信息
            JObject iObject = JObject.Parse(sResult);  // 获取JSON字符串的值
            Message = iObject["Msg"].ToString();
            sRMsg = iObject["RMgs"].ToString();

            if (sFlag == "2")
            { // 获取工单成功，再获取工单BOM
                if (Message == "Success")
                {  // 说明获取工单成功，再获取工单BOM

                    sResult = GetK3Info(sNo, Item, sName, K3_GetUrl1, K3_dbId, K3_userName, K3_password, K3_lcid, "5");  // 获取工单BOM信息

                    iObject = JObject.Parse(sResult);  // 获取JSON字符串的值
                    string ssg = iObject["RMgs"].ToString();
                    string Mes = iObject["Msg"].ToString();

                    if (Mes != "Success")
                    {
                        sRMsg = sRMsg + "；无工单BOM，请检查追溯清单";
                    }
                }
            }


            Result = JsonConvert.SerializeObject(new { Msg = Message, RMgs = sRMsg });

            return Result;
        }
        #endregion



        #region 从K3获取基本信息 -- 执行获取的动作
        public string GetK3Info(string WO, string Item, string Name, string K3_GetUrl1, string K3_dbId, string K3_userName, string K3_password, int K3_lcid, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sColumn = string.Empty;
            string sK3Column = string.Empty;
            string sFilter = string.Empty;
            string sInTable = string.Empty;
            string sLogTable = string.Empty;
            string sRMsg = string.Empty;
            string sResult = string.Empty;
            string sFlag = string.Empty;
            string sFlagSN = string.Empty;
            string sNo = string.Empty;
            string sName = string.Empty;
            string sDept = string.Empty;
            string sSyncNo = DateTime.Now.ToString("yyyyMMddHHmmss"); // 产生一个同步编号，表示的数据是一起的


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sFlag = Flag; sNo = WO; sName = Name; sDept = Item;

            if (sFlag == "2")  // 同步工单
            {
                sColumn = "OrderNo,MaterID,MaterName,MaterSpec,DeptNo,OrderNum,OrderKind,FCreateDate,ReleaseDate,PlanStartDate,PlanEndDate,BomNo,BomName,PrdctStatus,Remark,FID,FTreeEntity_FEntryId,FTreeEntity_FSeq,FCONVEYDATE";
                sK3Column = "FBillNo,FMaterialId,FMaterialName,FSpecification,FWorkShopID,FQty,FBillType,FCreateDate,FCreateDate,FPlanStartDate,FPlanFinishDate,FBOMID.FNumber,FBOMID.FName,FSTATUS,FDescription,FID,FTreeEntity_FEntryId,FTreeEntity_FSeq,FCONVEYDATE";
                sFilter = " FBillNo = '" + sNo + "' "; // 按
                //sFilter = " FMaterialId =112606";  // 按物料ID获取，不过获取BOM时有错误，因为没有获取到产品编码
                //sFilter = " FCreateDate >= '2023-11-12' and FCreateDate <= '2023-11-14' ";
                sInTable = "PRD_MO";  // K3的数据表
                sLogTable = "T_OrderInfoSynLog";
            }
            else if (sFlag == "3")  // 插入序列号信息，注意，如果这个序列号已存在，而且不是当前工单，则更新序列号对应工单；同时插入序列号变更表
            {
                sColumn = "SerielNo,SerielID,OrderNo,DeptNo,MaterID,MaterName,FBillNo,Status,OrderType";
                sK3Column = "FSerialNo,FSerialId,FBillNo,FWorkShopID,FMaterialID,FmaterialName,FBillNo,FSTATUS,FBillType";
                sFilter = " FBillNo = '" + sNo + "' "; // "Fnumber in ('1101-00004-01','1101-00006-01')";
                sInTable = "PRD_MO";  // K3的数据表   231116： 这个之前定义是序列号表，现在不用了 BD_SerialMainFile，使用工单表
                sLogTable = "T_SerielInfoSynLog";
            }
            else if (sFlag == "4")  // 插入发料信息
            {
                sColumn = "FID,BillNo,OrderNo,MaterID,MaterName,ReqNum,SendNum,Unit,WorkShop,SerialNo";
                sK3Column = "FID,FBillNo,FMoBillNo,FMaterialId,FMaterialName,FAppQty,FActualQty,FUnitID,FPrdOrgId,F_BHR_ZDXLH";
                sFilter = " FMoBillNo = '" + sNo + "' "; // "Fnumber in ('1101-00004-01','1101-00006-01')";
                sInTable = "PRD_PickMtrl";  // K3的数据表
                sLogTable = "T_OrderMBillSynLog";
            }
            else if (sFlag == "5")  // 插入工单BOM
            {
                sColumn = "FBillNo,OrderNo,MaterNo,MaterName,Num";
                sK3Column = "FROWID,FMOBILLNO,FMaterialID2,FMaterialName1,FMATERIALID";
                sFilter = " FMOBILLNO = '" + sNo + "' "; // "Fnumber in ('1101-00004-01','1101-00006-01')";
                sInTable = "prd_ppbom";  // K3的数据表
                sLogTable = "T_OrderBOMSynLog";
            }

            K3CloudApiClient client = new K3CloudApiClient(K3_GetUrl1);
            var loginResult = client.ValidateLogin(K3_dbId, K3_userName, K3_password, K3_lcid);
            var result = JObject.Parse(loginResult)["LoginResultType"].Value<int>();

            if (result == 1)
            {
                //接口的列可以跟数据表的列不一致， 但是顺序必须保持一致，要不然到时候会出错，并且接口的列不能少于数据表的列,过滤条件： \"FilterString\":\"Fnumber='1101-00004-01'\" ； \"Fnumber in ('1101-00004-01','1101-00006-01')\"
                // List<List<Object>> list = client.ExecuteBillQuery("{\"FormId\":\"BD_MATERIAL\",\"FieldKeys\":\"" + sK3 + "\",\"FilterString\":\"Fnumber='1101-00004-01'\",\"OrderString\":\"\",\"TopRowCount\":0,\"StartRow\":0,\"Limit\":2000,\"SubSystemId\":\"\"}");
                List<List<Object>> list = client.ExecuteBillQuery("{\"FormId\":\"" + sInTable + "\",\"FieldKeys\":\"" + sK3Column + "\",\"FilterString\":\"" + sFilter + "\",\"OrderString\":\"\",\"TopRowCount\":0,\"StartRow\":0,\"Limit\":2000,\"SubSystemId\":\"\"}");
                if (list.Count != 0)
                {
                    Message = "Success";

                    sResult = OrderBll.SavaData(sColumn, sLogTable, list, "SyncNo", sSyncNo);  // sColumn 是追溯平台的表字段，不是K3的;SyncNo 这个是新增一个列名，存放本次同步的编号，后台数据库也必须是这个字段

                    if (string.IsNullOrEmpty(sResult)) // 说明正常，则更新系统的物料信息
                    {
                        if (sFlag == "2")  // 同步工单时，判定工单对应产品是否有流程
                        {
                            sOKFlag = OrderBll.JudgeObjectExist(list[0][1].ToString(), "", "", sComp, "8-5");
                            if (sOKFlag == "N")  // 说明没有流程
                            {
                                sRMsg = "同步工单失败：工单对应产品编码无流程";
                                Message = "Y_NOFLOW";
                                sFlagSN = "Y";  // =Y 标识不需要执行插入工单操作了
                            }
                        }
                        else if (sFlag == "3")  // 判断当前同步过来的序列号，是否已存在某个工单上，而且这个工单没有完工的，如果是，不给把这些序列号变更到本工单上（虽然K3是已建立了关系）。
                        {
                            sOKFlag = OrderBll.JudgeObjectExist(sNo, sSyncNo, "", sComp, "8-4");
                            if (sOKFlag == "Y")  // 说明这些序列号在其他工单未生产完成，不能变更
                            {
                                sRMsg = sSyncNo;
                                Message = "Y_EXISTSN";
                                sFlagSN = "Y";
                                //sRMsg = "序列号同步失败：新获取的序列号在其他工单未生产完成，不能变更";
                                //Message = "Y_EXISTSN";
                                //sFlagSN = "Y";
                            }
                            // 如果整机工单已存在序列号，现在获取的是主机工单序列号，这些序列号相同，则主机工单不给下达。理论来说，先获取主机工单序列号，再到整机工单序列号
                            sOKFlag = OrderBll.JudgeObjectExist(sNo, sSyncNo, "", sComp, "8-6");
                            if (sOKFlag == "Y")  //  
                            {
                                sRMsg = sSyncNo;
                                Message = "Y_EXISTZJSN";  // ZJ 整机 序列号
                                sFlagSN = "Y";
                                //sRMsg = "序列号同步失败：该主机工单对应的序列号已在整机工单上";
                                //Message = "Y_EXISTZJSN";  // ZJ 整机 序列号
                                //sFlagSN = "Y";
                            }

                        }

                        // 同步工单，序列号，工单BOM等信息
                        if (sFlagSN != "Y")
                        {  // =Y 说明这个工单对应的序列号未完工，不能同步到该平台；其他类型的同步也是使用下面的方面
                            sRMsg = OrderBll.InsertK3Date(sNo, "手工同步K3", sSyncNo, sLogin, sDept, sComp, sFlag);
                            if (sRMsg.IndexOf("TIP") >= 0)
                            {
                                Message = "Error002";
                            }
                        }
                    }
                    else // 说明有异常，把异常存起来
                    {
                        sRMsg = sResult;
                    }
                }
                else
                {  // 没有数据
                    sRMsg = "获取不到K3工单数据";

                    Message = "Y_NoOrder";
                }
            }
            else
            {
                Message = "Error001";
                sRMsg = "获取不到数据，请检查同步K3账号密码是否正确";
            }


            Result = JsonConvert.SerializeObject(new { Msg = Message, RMgs = sRMsg });

            return Result;
        }
        #endregion




        #region 获取工序的下拉列表
        public string GetProcedureList(string Kind, string No, string Item, string sFlag)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sComp = string.Empty;
            string sTNo = string.Empty;
            string sTName = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            DataTable dt = OrderBll.GetOrderInfo(No, Item, "", "", "", "", "", "", "", "", "", "", 5000, 1, "", sComp, sFlag);
            if (dt.Rows.Count > 0)
            {
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    sTNo = dt.Rows[i]["PNNo"].ToString();
                    sTName = dt.Rows[i]["PNName"].ToString();

                    Message += "<option value='" + sTNo + "'>" + sTName + "</option>";
                }
            }
            else
            {
                Message = "Error";
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;

        }
        #endregion



        #region 获取生产相关的信息
        public void GetPRDInfo(string Params, int rows, int page, string sStatus, string sCQNo, string sItmNo, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sReturn = string.Empty;
            string sComp = string.Empty;
            string sNo = string.Empty;
            string sItem = string.Empty;
            string sName = string.Empty;
            string sMNo = string.Empty;
            string sMName = string.Empty;
            string sSt = string.Empty;
            string sBDate = string.Empty;
            string sEDate = string.Empty;
            string sA = string.Empty;
            string sB = string.Empty;
            string sC = string.Empty;
            string sD = string.Empty;
            int iSumCount = 0;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return;
            }

            var AnonymousUser = new
            {
                No = String.Empty,
                Item = String.Empty,
                Name = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                Status = String.Empty,
                BDate = String.Empty,
                EDate = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
            };



            if (string.IsNullOrEmpty(Params) || (Params == null) || (Params == "null"))
            {
                sNo = ""; sItem = ""; sName = ""; sMNo = ""; sMName = ""; sSt = ""; sBDate = ""; sEDate = "";
                sA = ""; sB = ""; sC = ""; sD = "";
            }
            else
            {
                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

                sNo = Item.No; sItem = Item.Item; sName = Item.Name; sMNo = Item.MNo; sMName = Item.MName; sSt = Item.Status;
                sBDate = Item.BDate; sEDate = Item.EDate; sA = Item.A; sB = Item.B; sC = Item.C; sD = Item.D;
            }
            if (sBDate == "")
            {
                sBDate = "2021-01-01";
            }
            if (sEDate == "")
            {
                sEDate = "2999-12-30";
            }




            DataTable dt = OrderBll.GetPRDInfo(sNo, sItem, sName, sMNo, sMName, sSt, sBDate, sEDate, sA, sB, sC, sD, rows, page, sMan, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                iSumCount = int.Parse(dt.Rows[0]["NumCount"].ToString());

                string sJson = JsonConvert.SerializeObject(dt);
                sReturn = "{\"code\":0,\"msg\":\"\",\"count\":" + iSumCount + ",\"data\":" + sJson + "}";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;
            context.Response.Write(sReturn);
        }
        #endregion




        #region 作业执行获取作业单元信息
        public void GetWorkCenter(string Params, string sCNo, string sCItem, string sCMNo, string sCWO, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string json = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            string sNo = string.Empty;
            string sEXEMan = string.Empty;
            string sFlag = string.Empty;
            bool sExistsResult = false;

            DataTable dt = new DataTable();

            HttpContext context = HttpContext.Current;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }

            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { CNo = String.Empty, InMan = String.Empty, Flag = String.Empty, };

                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sNo = Item.CNo; sEXEMan = Item.InMan; sFlag = Item.Flag;
            }


            if (Flag == "28-2") // 作业执行-扫描作业单元时
            {
                sOKFlag = OrderBll.JudgeObjectExist(sCNo, "", "", sComp, "28-2-1");
                if (sOKFlag == "N")  // // 判断作业单元是否存在 
                {
                    Message = "N_EXIST";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                    context.Response.Write(Result);
                    return;
                }

                sOKFlag = OrderBll.JudgeObjectExist(sCNo, sEXEMan, "", sComp, "28-2-2");
                if (sOKFlag == "N")   // 该用户是否有该作业单元权限
                {
                    Message = "N_USEREXIST";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                    context.Response.Write(Result);
                    return;
                }

                // 判断作业单元是否被其他作业人员使用了，主要判断进行中的序列号即可
                //sOKFlag = OrderBll.JudgeObjectExist(sEXEMan, sCNo, "", sComp, "28-1-4-1");
                //if (sOKFlag == "Y")  // 已被使用 
                //{
                //    Message = "Y_UNITUSE";
                //    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                //    context.Response.Write(Result);
                //    return;
                //}

                sOKFlag = OrderBll.JudgeObjectExist(sEXEMan, "", "", sComp, "49-1");// 判断该用户有没有签名图
                if (sOKFlag == "N")
                {
                    Message = "N_USER";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                    context.Response.Write(Result);
                    return;
                }
                else
                {
                    try
                    {
                        sExistsResult = File.Exists(AppDomain.CurrentDomain.BaseDirectory + sOKFlag.Substring(1));
                    }
                    catch (Exception)
                    {
                        sExistsResult = false;
                    }

                    if (!sExistsResult)
                    {
                        Message = "N_SIGN";
                        Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                        context.Response.Write(Result);
                        return;
                    }
                }
            }

            dt = OrderBll.GetPRDInfo(sCNo, "", "", "", "", "", "", "", "", "", "", "", 50000, 1, sEXEMan, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                sRNo = dt.Rows[0]["CenterDesc"].ToString();  // 作业单元描述 
            }
            else
            {
                Message = "Error";
            }

            json = JsonConvert.SerializeObject(dt);

            Result = JsonConvert.SerializeObject(new { Msg = Message, json = json, BZBatch = sRNo });
            context.Response.Write(Result);

            // return Result;

        }
        #endregion


        #region 根据序列号获取相关信息（插入序列号当前生产工序，显示该工序相关信息）
        public void GetSerialInfoByNo(string Params, string sCNo, string sCItem, string sCMNo, string sCWO, string sActFlag, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sEXEMan = string.Empty;  // 作业人员账号
            string sManName = string.Empty;
            string sNo = string.Empty;
            string sWO = string.Empty;
            string sPNum = string.Empty;
            string sOKFlag = string.Empty;
            string sComp = string.Empty;
            string sFlag = string.Empty;
            string json = string.Empty;
            string sRNo = string.Empty;
            string sTNo = string.Empty;
            string sRWo = string.Empty;
            string sEXENo = string.Empty;
            string sProc = string.Empty;
            string sProcAct = string.Empty;
            string sPNo = string.Empty;
            string sDoFlag = string.Empty;
            string sData = string.Empty;


            DataTable dt = new DataTable();

            HttpContext context = HttpContext.Current;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }

            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { No = String.Empty, WO = String.Empty, PNum = String.Empty, InMan = String.Empty, Flag = String.Empty, };

                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
                sNo = Item.No; sWO = Item.WO; sPNum = Item.PNum; sEXEMan = Item.InMan; sFlag = Item.Flag;
            }


            sDoFlag = Flag;

            if (sActFlag == "28-JY")  // 扫描的是检验批号，找到第一个序列号
            {
                sTNo = sCNo;  // 检验批次
                sOKFlag = OrderBll.JudgeObjectExist(sTNo, "", "", sComp, "28-1-1-0");
                if (sOKFlag == "N")
                {   // 判断这个送检批次的所有序列号所以工序是否有作业完成了。
                    Message = "Y_PHOVER";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                    context.Response.Write(Result);
                    return;
                }

                // 如果 工序行为是“整机放行检验”，则判断序列号是否完工，没有完工的话，把这个批次的所有序列号都设置为：生产中
                sOKFlag = OrderBll.JudgeObjectExist(sTNo, "", "", sComp, "28-1-1-2");
                if (sOKFlag.IndexOf("Error") > 0)
                {   // 说明找不到想要的信息
                    Message = "Y_Error";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                    context.Response.Write(Result);
                    return;
                }
                else
                {
                    sProcAct = sOKFlag.Substring(0, sOKFlag.IndexOf("_"));  //  获取工序行为下拉的内容， 整机放行检验_SN0001:IPQC
                    sCNo = sOKFlag.Substring(sOKFlag.IndexOf("_") + 1, sOKFlag.IndexOf(":") - sOKFlag.IndexOf("_") - 1);  //  获取序列号
                    sProc = sOKFlag.Substring(sOKFlag.IndexOf(":") + 1, sOKFlag.Length - sOKFlag.IndexOf(":") - 1);  //  获取工序 
                }

                if (sProcAct == "整机放行检验")  // 如果是DHR放行检验，把这个送检批次的所有序列号都置生产中
                {
                    sDoFlag = "28-1-1";
                }
                else
                {  // 整机包装送检 ,批量作业工序行为(批量生产)
                    sOKFlag = OrderBll.JudgeObjectExist(sTNo, "", "", sComp, "28-1-1-1");

                    if (sOKFlag == "N")
                    {   // 这个送检批次没有可用的序列号
                        Message = "N_PHEXIST";
                        Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                        context.Response.Write(Result);
                        return;
                    }
                    else if (sOKFlag == "OVER")
                    {  // 送检批次对应的序列号已完工
                        Message = "Y_PHOVER";
                        Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                        context.Response.Write(Result);
                        return;
                    }
                    else
                    {
                        sCNo = sOKFlag;
                    }
                }
            }

            // 判断工单是否暂停了，如果是不能操作了 
            sOKFlag = OrderBll.JudgeObjectExist(sCNo, "", "", sComp, "28-WO");
            if (sOKFlag == "Y")  // 说明工单已暂停
            {
                Message = "Y_ORDERZT";
                Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                context.Response.Write(Result);
                return;
            }

            // 判断该序列号是否存在  
            sOKFlag = OrderBll.JudgeObjectExist(sCNo, "", "", sComp, "28-1-1");
            if (sOKFlag == "N")  // 不存在
            {
                // 序列号不存在，看看是不是扫描了重工单，如果是，把重工单号记录下来,表示马上要生产这个重工单
                sOKFlag = OrderBll.JudgeObjectExist(sCNo, "", "", sComp, "28-1-1-3");
                if (sOKFlag == "Y")  //这个是重工单，记录在前端
                {
                    Message = "Y_REODERNO";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                    context.Response.Write(Result);
                    return;
                }
                else
                { // 告诉前端，需要扫描一个序列号
                    Message = "N_EXIST";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                    context.Response.Write(Result);
                    return;
                }
            }
            else
            {  // 存在这个序列号，则返回工单号

                if (string.IsNullOrEmpty(sCWO))
                {
                    sCWO = sOKFlag;
                }

                // 如果是重工单，扫描进来的是一个被重工的序列号，不需要判断是否有流程
                sOKFlag = OrderBll.JudgeObjectExist(sCNo, sCWO, "", sComp, "28-1-2-2");
                if (sOKFlag != "Y")
                {   // =Y 说明这个被重工的序列号对应工单，和重工单绑定了关系。这里不需要判断序列号是否有流程。因为重工单和该序列号还没有建立关系。接下去执行完了才建立关系

                    sOKFlag = OrderBll.JudgeObjectExist(sCNo, sCWO, "", sComp, "28-1-2");  // sOKFlag  是工单号
                    if (sOKFlag == "N")  // = N 不存在   // 判断该序列号是否有生产流程
                    {
                        Message = "N_EXISTFLOW";
                        Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                        context.Response.Write(Result);
                        return;
                    }
                }
            }

            sOKFlag = OrderBll.JudgeObjectExist(sCNo, "", "", sComp, "28-2-3");
            if (sOKFlag == "Y")  // 判断序列号是否已完成；=Y 说明已完工了
            {
                // 看看是否需要重工，如果是，提醒用户先扫描重工工单
                if (string.IsNullOrEmpty(sWO))
                {
                    Message = "No_EIXSTWO";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                    context.Response.Write(Result);
                    return;
                }

                // 看看这个序列号的工单，是否和重工单建立了对应关系
                sOKFlag = OrderBll.JudgeObjectExist(sCNo, sWO, "", sComp, "28-1-1-4");
                if (sOKFlag == "N")  //=N 表示没有建立关系
                {
                    Message = "N_Relationship";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                    context.Response.Write(Result);
                    return;
                }

                // 看看这个序列号对应的工单是否绑定在重工单上，如果是，这个序列号可以用来重工
                sOKFlag = OrderBll.JudgeObjectExist(sCNo, sWO, "", sComp, "28-2-3-1");
                sRWo = sOKFlag;
                if (sOKFlag != "N")
                { // 说明这个序列号需要重工,该序列号继承重工单所有信息， sOKFlag 这里是返回一个重工单号

                    sOKFlag = OrderBll.OPReworkOrder(sRWo, "", sCNo, "", "", "", "", "", "", "", "", sComp, sEXEMan, "", "34-3");
                    if (sOKFlag == "操作成功")
                    {
                        Message = "Success";
                    }
                    else
                    { // 有异常
                        Message = "Error";
                        Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                        context.Response.Write(Result);
                        return;
                    }
                }
                else
                {  //= N ，说明不需要这个序列号
                    Message = "Y_EXISTSOVER";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                    context.Response.Write(Result);
                    return;
                }
            }

            // 判断该序列号是否所有流程已完成
            sOKFlag = OrderBll.JudgeObjectExist(sCNo, sCWO, "", sComp, "28-1-3");
            if (sOKFlag == "Y")
            {
                Message = "Y_OVER";
                Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                context.Response.Write(Result);
                return;
            }

            // 判断该序列号和当前作业单元是否匹配   
            sOKFlag = OrderBll.JudgeObjectExist(sCNo, sCItem, sCWO, sComp, "28-1-4");
            if (sOKFlag != "Y")  // 不匹配  
            {
                Message = "N_EXISTCMP";
                Result = JsonConvert.SerializeObject(new { Msg = Message, json = sOKFlag });
                context.Response.Write(Result);
                return;
            }

            // 判断该序列号和当前批次状态
            sOKFlag = OrderBll.JudgeObjectExist(sCWO, sCNo, sEXEMan, sComp, "28-1-5");
            if (sOKFlag != "Y")  // 说明扫描的序列号已经关联了  
            {
                Message = "N_NOTGL";
                Result = JsonConvert.SerializeObject(new { Msg = Message, json = sOKFlag });
                context.Response.Write(Result);
                return;
            }

            //// 判断作业单元是否被其他作业人员使用了，主要判断进行中的序列号即可  20240516:用户说不需要切换了
            //sOKFlag = OrderBll.JudgeObjectExist(sEXEMan, sCItem, "", sComp, "28-1-4-1");
            //if (sOKFlag == "Y")  // 已被使用 
            //{
            //    Message = "Y_UNITUSE";
            //    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
            //    context.Response.Write(Result);
            //    return;
            //}

            //// 判断序列号是否被其他作业人员占用了，主要判断进行中的序列号即可  20240516:用户说不需要切换了
            //sOKFlag = OrderBll.JudgeObjectExist(sCNo, sEXEMan, "", sComp, "28-1-4-2");
            //if (sOKFlag == "Y")  // 已被使用  
            //{
            //    Message = "Y_SNUSE";
            //    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
            //    context.Response.Write(Result);
            //    return;
            //}

            if (Flag == "28-1")
            {
                // 判断批序号有没有未使用的
                sData = OrderBll.JudgeObjectExist(sCNo, "", "", sComp, "48-1");
            }

            // 插入作业执行信息
            // 28-1:扫描单个序列，或工序行为是“包装检验”扫描送检批次时，按序列号插入生产信息；28-1-1：DHR整机批量放行，这个批次的所有序列号都插入到生产中
            sOKFlag = OrderBll.OPPRDInfo(sCNo, "", sTNo, "", "", sProc, sCItem, sPNum, "", "", "", sCWO, "", "", sData, sComp, sEXEMan, "", sDoFlag);
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else if (sOKFlag.IndexOf("OVER") > 0)
            { // 说明该工序直接完工的
                Message = "N_INSEROVER";
                Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                context.Response.Write(Result);
                return;
            }
            else
            {
                Message = "N_INSERPRD";
                Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                context.Response.Write(Result);
                return;
            }

            dt = OrderBll.GetPRDInfo(sCNo, sTNo, "", "", "", "", "", "", sProc, "", "", "", 50000, 1, sEXEMan, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }

            json = JsonConvert.SerializeObject(dt);

            Result = JsonConvert.SerializeObject(new { Msg = Message, json = json, BZBatch = sRNo, ProcAct = sProcAct, Data = sData });
            context.Response.Write(Result);

            // return Result;

        }
        #endregion



        #region 获取生产相关信息  -- BOM，流程等
        public string GetPRDInfoTwo(string sSSNo, string sItem, string sPNO, string Kind, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sKFlag = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            DataTable dt = OrderBll.GetPRDInfo(sSSNo, sItem, "", "", "", "", "", "", sPNO, Kind, "", "", 50000, 1, sMan, sComp, Flag); // 工序版本sPNO
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }


            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion

        #region 获取生产测试项信息
        public void GetPRDTestInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sKFlag = string.Empty;
            string sEnough = "N";
            DataTable dt = new DataTable();
            HttpContext context = HttpContext.Current;
            if (context.Session["LoginName"] != null)
            {
                sMan = context.Session["LoginName"].ToString();
                sManName = context.Session["FullName"].ToString();
                sComp = context.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }
            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                InMan = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };
            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
            if (It.Flag == "28-13" && It.Name == "送检抽检合并")//判断生产工序测试项是否达到了样本量
            {
                //判断设置的抽样方式是不是测试项抽样
                sKFlag = OrderBll.JudgeObjectExist(It.G, It.No, It.E, sComp, "41-1");
                if (sKFlag == "Y")  //sKFlag = Y 说明设置的是测试项抽样
                {
                    //判断有没有达到样本量
                    sEnough = OrderBll.JudgeObjectExist(It.A, It.G, "", sComp, "28-3-5-9");
                    if (sEnough == "Y")//说明是测试项抽样此时已经达到了样本量
                    {
                        //把那些带有抽样标记的测试项给不涉及掉
                        OrderBll.OPPRDInfo(It.No, It.Name, It.Item, "", "", It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, "", sComp, sMan, "", "30-7");
                    }
                }
                //把状态改为已检验
                OrderBll.OPPRDInfo(It.No, "", "", "", "", It.A, It.B, "", It.D, "", "", It.G, "", "", "", sComp, sMan, "", "30-7-1");
            }
            if (sKFlag.Length <= 2)
            {
                dt = OrderBll.GetPRDInfo(It.No, It.B, "", "", "", "", "", "", It.D, It.G, It.F, "", 50000, 1, sMan, sComp, It.Flag);
                Message = "Success";
            }
            else
            {
                Message = "Error" + sKFlag;
            }
            context.Response.Write(JsonConvert.SerializeObject(new { Msg = Message, Data = dt }));
        }
        #endregion

        #region 根据包装批次，获取数量
        public string GetUpBatchInfo(string Params, string sCNo, string sFlag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sComp = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sMan });
                return Result;
            }

            DataTable dt = OrderBll.GetPRDInfo(sCNo, "", "", "", "", "", "", "", "", "", "", "", 5000, 1, sMan, sComp, sFlag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }

            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 对生产执行信息进行操作-- 操作设备信息
        public string OPPRDDeviceInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            string sCPNo = string.Empty;
            string sEXEMan = string.Empty;
            int i = 0;

            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                InMan = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sEXEMan = It.InMan;   //20240315: 使用前端传入的作业人员

            if (It.Flag == "29-3") // 扫描设备，扣减设备时
            {
                //作业执行，判断当前工序的工序行为是不是送检抽检合并 
                sOKFlag = OrderBll.JudgeObjectExist(It.G, "", "", sComp, "43-2");
                if (sOKFlag == "Y")
                {
                    //作业执行，如果设置抽检工序行为则需要判断是否关联完成，只有关联完成后才可操作 测试项、设备
                    sOKFlag = OrderBll.JudgeObjectExist(It.H, "", "", sComp, "42-3");
                    if (sOKFlag == "Y")  // 未关联
                    {
                        Message = "N_NORELEVANCE";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }

                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.B + It.Item, It.C + It.E, sComp, "29-3-0");  // It.B+It.Item  序列号+工单
                if (sOKFlag == "N")  // 判断这个序列号是否需要这个设备
                {
                    Message = "N_NOTNEED";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sOKFlag = OrderBll.JudgeObjectExist(It.No, "", "", sComp, "29-3");
                if (sOKFlag == "N")  // 判断该设备是否存在
                {
                    Message = "N_EXISTsbCODE";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                //sOKFlag = OrderBll.JudgeObjectExist(It.No, It.A, "", sComp, "29-3-1");
                //if (sOKFlag == "Y")  // 判断是否已扫描过了这个设备编号
                //{
                //    Message = "Y_EXISTsbCODE";
                //    return JsonConvert.SerializeObject(new { Msg = Message });
                //}
                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.A, "", sComp, "29-3-3");
                if (sOKFlag == "N")  //  判断是设备校准日期是否过期了  =N:表示过期
                {
                    Message = "N_GUOQI";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            if (It.Flag == "29-3-1") // 扫描设备后，没有点检任务，可以刷新
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.A, "", sComp, "29-3-2");
                if (sOKFlag == "Y")  // 判断该设备是否存在点检任务了。
                {
                    Message = "Y_Check";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            if (It.Flag == "29-3-2") // 设备清单，点击 不涉及
            {
                //作业执行，判断当前工序的工序行为是不是送检抽检合并 
                sOKFlag = OrderBll.JudgeObjectExist(It.G, "", "", sComp, "43-2");
                if (sOKFlag == "Y")
                {
                    //作业执行，如果设置抽检工序行为则需要判断是否关联完成，只有关联完成后才可操作 测试项、设备
                    sOKFlag = OrderBll.JudgeObjectExist(It.H, "", "", sComp, "42-3");
                    if (sOKFlag == "Y")  // 未关联
                    {
                        Message = "N_NORELEVANCE";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }

                sOKFlag = OrderBll.JudgeObjectExist(It.MNo, It.A, "", sComp, "29-3-4");
                if (sOKFlag == "Y")  // 判断该设备是否已执行过一次
                {
                    Message = "Y_EXISTSB";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }

            sOKFlag = OrderBll.OPPRDInfo(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, sComp, sEXEMan, It.Remark, It.Flag);

            i = sOKFlag.IndexOf("Success:");
            if (i > 0)
            {
                Message = "Success";
                if (It.Flag == "29-3-1")
                {
                    sRNo = sOKFlag.Substring(sOKFlag.IndexOf("DJRW"), 15);  // Success:DJRW20231002001  
                    sCPNo = sOKFlag.Substring(sOKFlag.IndexOf("LY:") + 3, sOKFlag.Length - sOKFlag.IndexOf("LY:") - 3);  // Success:DJRW20231002001 LY:DJJH20231006001
                }
            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo, CPNo = sCPNo });

            return Result;
        }
        #endregion

        #region 对生产执行信息进行操作-- 操作质控品信息
        public string OPPRDQualityInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            string sMNo = string.Empty;
            string sPROCNo = string.Empty;
            string sEXEMan = string.Empty;

            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                InMan = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sEXEMan = It.InMan;   //20240315: 使用前端传入的作业人员
            sPROCNo = It.C + It.E;  // 工序+版本

            sOKFlag = OrderBll.JudgeObjectExist(It.No, "", "", sComp, "29-9-1");
            if (sOKFlag == "N")  // 判断扫描的质控品批次是否存在
            {
                Message = "N_EXISTzkp";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }
            sOKFlag = OrderBll.JudgeObjectExist(It.No, sPROCNo, It.B + It.Item, sComp, "29-9-2");  // It.B+It.Item  序列号 + 工单号
            if (sOKFlag == "N")  //  判断是否需要这个质控品，如果需要，返回质控品的物料编码; =N 表示不需要
            {
                Message = "Y_IsNOTNeed";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }
            else
            {
                sMNo = sOKFlag;
            }
            sOKFlag = OrderBll.JudgeObjectExist(It.B + It.Item, sPROCNo, sMNo, sComp, "29-9-3");   // It.B+It.Item  序列号 + 工单号
            if (sOKFlag == "Y")  // 判断这个序列号，这个工序是否已消耗了这个质控品
            {
                Message = "Y_IsSCan";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }
            //sOKFlag = OrderBll.JudgeObjectExist(It.No, It.A, "", sComp, "29-16-4");
            //if (sOKFlag == "N999999")  //  判断质控品是否过期了  =N:表示过期
            //{
            //    Message = "N_GUOQI";
            //    return JsonConvert.SerializeObject(new { Msg = Message });
            //}

            sOKFlag = OrderBll.OPPRDInfo(It.No, It.Name, It.Item, sMNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, sComp, sEXEMan, It.Remark, It.Flag);
            if (sOKFlag.Length <= 3) // > 3 表示执行语句打印999 
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });

            return Result;
        }
        #endregion


        #region 对生产执行信息进行操作-- 更新工序版本
        public string OPPRDChangeVerInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            string sEXEMan = string.Empty;


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                InMan = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sEXEMan = It.InMan;

            // 判断更改前的 版本 是否已扫描追溯物料，装箱物料，扣减设备，填写测试项等，如果已有操作，则不能更换了。
            sOKFlag = OrderBll.JudgeObjectExist(It.B, It.No + It.D, It.Item, sComp, "29-5");
            if (sOKFlag == "Y")
            {
                Message = "Y_EXIST";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }
            // 判断更改前的 版本 是否填写测试项等，如果已有操作，则不能更换了。
            sOKFlag = OrderBll.JudgeObjectExist(It.B, It.No + It.D, It.Item, sComp, "29-5-1");
            if (sOKFlag == "Y")
            {
                Message = "Y_EXIST";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }
            // 判断更改前的 版本 是否已扫描追溯物料，装箱物料，则不能更换了。
            sOKFlag = OrderBll.JudgeObjectExist(It.B, It.No + It.D, It.Item, sComp, "29-5-2");
            if (sOKFlag == "Y")
            {
                Message = "Y_EXIST";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }


            sOKFlag = OrderBll.OPPRDInfo(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, sComp, sEXEMan, It.Remark, It.Flag);
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });

            return Result;
        }
        #endregion

        #region 对生产执行信息进行操作-- 更改作业人员
        public string OPPRDChangeManInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            string sEXEMan = string.Empty;

            //  { No: sEXENo, Name: "", Item: sSerial, MNo: "", MName: "", A: sOldMan, B: sNewManNo, C: sNewMan, D: sEXEMan, E: sUnit, F: "", Remark: "", Flag: sFlag };
            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sEXEMan = It.D;

            // 如果作业单元不为空，判断新的作业人员是否有权限
            if (!string.IsNullOrEmpty(It.E))
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.E, It.B, "", sComp, "28-2-2");
                if (sOKFlag == "N")   // 该用户是否有该作业单元权限
                {
                    Message = "N_USEREXIST";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                    return Result;
                }
            }




            sOKFlag = OrderBll.OPPRDInfo(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, sComp, sEXEMan, It.Remark, It.Flag);
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });

            return Result;
        }
        #endregion

        #region 对生产执行信息进行操作-- 更改作业单元
        public string OPPRDChangeUnitInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            string sEXEMan = string.Empty;


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sEXEMan = It.D;

            // 判断作业单元是否被其他作业人员使用了，主要判断进行中的序列号即可
            sOKFlag = OrderBll.JudgeObjectExist(It.D, It.B, "", sComp, "28-1-4-1");
            if (sOKFlag == "Y")  // 已被使用 
            {
                Message = "Y_UNITUSE";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            // 判断这个序列号，这个工序是否能在这个作业单元操作


            sOKFlag = OrderBll.OPPRDInfo(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, sComp, sEXEMan, It.Remark, It.Flag);
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });

            return Result;
        }
        #endregion


        #region 对生产执行信息进行操作-- 操作测试项
        public string OPPRDTestInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            string sSNWO = string.Empty;
            string sEXEMan = string.Empty;


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                InMan = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sEXEMan = It.InMan;   //20240315: 使用前端传入的作业人员

            if ((It.Flag == "29-6") || (It.Flag == "29-6-1"))
            {  // = 29-6 是作业执行测试项，点击 下一项；29-6-1：点击 不涉及
                sSNWO = It.No + "^@" + It.G; //It.No+It.G 序列号+工单。这样拼接，如果数据量大，查找是很慢的；所以传一个标识符进去，把工单和序列号拆开，再查询，这样就快了，其他地方可参考
                sOKFlag = OrderBll.JudgeObjectExist(sSNWO, It.Item + It.MNo, It.MName, sComp, "28-3-5");
                if (sOKFlag == "N")  // 判断测试项是否填写完成，N :表示没有测试项了，测试完成
                {
                    Message = "N_TestItem";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                //作业执行，判断当前工序的工序行为是不是送检抽检合并 
                sOKFlag = OrderBll.JudgeObjectExist(It.MName, "", "", sComp, "43-2");
                if (sOKFlag == "Y")
                {
                    sRNo = sOKFlag;
                    //作业执行，如果设置抽检工序行为则需要判断是否关联完成，只有关联完成后才可操作 测试项、设备
                    sOKFlag = OrderBll.JudgeObjectExist(It.J, "", "", sComp, "42-3");
                    if (sOKFlag == "Y")  // 未关联
                    {
                        Message = "N_NORELEVANCE";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }

            }
            else if (It.Flag == "29-6-2")
            {  // 作业执行变更，修改测试项
                sOKFlag = OrderBll.JudgeObjectExist(It.MName, "", "", sComp, "28-3-5-8");
                if (sOKFlag == "N")  // 判断输入的账号密码是否正确
                {
                    Message = "N_NOUSER";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }

            if (It.Flag == "29-6-2" || It.Flag == "29-6-3")
            {
                sEXEMan = sLogin;
            }


            sOKFlag = OrderBll.OPPRDInfo(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, sComp, sEXEMan, It.Remark, It.Flag);
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });

            return Result;
        }
        #endregion


        #region 对生产执行信息进行操作-- 扣减追溯物料，装箱物料  28-2
        public string OPPRDMaterInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sFlag = string.Empty;
            string sRNo = string.Empty;
            string sD = string.Empty;
            string sMNo = string.Empty;
            string sCG = string.Empty;
            string sEXEMan = string.Empty;


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                InMan = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sEXEMan = It.InMan;
            sMNo = It.MNo;
            sFlag = It.Flag;
            sD = It.D;

            // 判断是否重工单，如果是，而且当前扫描的这个物料是已扣减在机器上的，那么接下来就是做卸载操作（重工不换序列号场景）
            //sOKFlag = OrderBll.JudgeObjectExist(It.No + It.G, sD, It.B, sComp, "28-2-7-0");   // It.No+It.G  序列号+工单
            //if ((It.Name == "直接入库-返工生产") && (sOKFlag.Length > 2))
            //{  // sOKFlag.Length > 2 说明返回的是物料编码，证明这个扫描进来的物料，是消耗在机器上了，卸载掉
            //    if (sOKFlag.IndexOf("SN_") >= 0)
            //    { // 先看看这个扫描进来的这个编号，是不是一个被重工扣减的序列号，如果是，卸载这个物料序列号后，还要删除其他BOM信息
            //        sFlag = "28-2-3";
            //    }
            //    else if (sOKFlag == "NOTNEED")
            //    {  // 扫描进来的物料序列号，这个序列号BOM不需要
            //        Message = "N_ReqMater";
            //        return JsonConvert.SerializeObject(new { Msg = Message });
            //    }
            //    else
            //    {  // 卸载这个物料
            //        sFlag = "28-2-1";
            //        sMNo = sOKFlag;
            //    }
            //}
            //else if ((It.Name == "直接入库-返工生产") && (sOKFlag == "DOWN"))
            //{ // 重工工序，维护了负数的物料，表示 需要卸载这个物料
            //    sFlag = "28-2-4";
            //}
            sOKFlag = OrderBll.JudgeObjectExist(It.No + It.G, sD, It.B, sComp, "28-2-7-0");   // It.No+It.G  序列号+工单
            if (It.Name == "直接入库-返工生产" && sOKFlag == "DOWN")
            {
                sFlag = "28-2-4";
            }
            else
            {  // 正常扣减物料
                // 扫描进来的物料，不是消耗在这个机器上的，那么看看是不是一个完工的序列号，如果是，则把该序列号的生产信息复制到新的序列号上（重工换序号场景）
                sOKFlag = OrderBll.JudgeObjectExist(It.No, sD, "", sComp, "28-2-7-1");
                if (sOKFlag == "Y")
                { // 说明这个扫描进来的序列号，是一个完工的序列号，则把该序列号的生产信息复制到新的序列号上（重工换序号场景）
                    sCG = "Y";
                }
                else if (sOKFlag == "N")
                {   // = N,说明这个编号是产品序列号； 说明不需要这个序列号 这个序列号不可用 
                    Message = "N_SNERROR";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                if (It.D.IndexOf(";") > 0)  // 说明扫描进来的是一个发料标贴的信息，如： 1404-00042-01;20240104;把物料和批次拆出来
                {
                    sMNo = It.D.Substring(0, It.D.IndexOf(";"));  //  获取到物料编码
                    sD = It.D.Substring(It.D.IndexOf(";") + 1, It.D.Length - It.D.IndexOf(";") - 1);  //  获取分号的批次号,如：20240104
                    if (sD == "")//如果等于空 ，就说明分号后面没有批号
                    {
                        Message = "N_EXISTOVER";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }
                else
                {
                    sOKFlag = OrderBll.JudgeObjectExist(sD, "", "", sComp, "28-2-7");
                    if (sOKFlag == "N")  // 判断扫描的序列号，批号是否在批序号关系表存在; N 表示批号或序列号不存在，或者已使用完了
                    {
                        Message = "N_EXISTOVER";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                    else
                    {  // 获取物料编码
                        sMNo = sOKFlag;
                    }
                }

                // { No: sSNo, Name: "", Item: sWUnit, MNo: "", MName: "", A: sEXENo, B: sProc, C: sVer, D: sScan, 
                sOKFlag = OrderBll.JudgeObjectExist(It.No + It.B + It.G, sMNo, sD, sComp, "28-2-8");  // It.No + It.B +It.G  序列号+工序+工单
                if (sOKFlag == "N")  // 判断扫描的编码是否这个BOM 想要的，主要判断：BOM里面需要扫描批次或序列号，结果扫描了一个物料编码进去，是不给扣减的
                {
                    Message = "N_ReqMater";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                sOKFlag = OrderBll.JudgeObjectExist(It.No, "", "", sComp, "28-2-3");
                if (sOKFlag == "Y")  // 判断序列号是否已完成
                {
                    Message = "Y_EXISTSOVER";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                if (It.F == "ZS")  // 表示扫描物料
                {
                    sOKFlag = OrderBll.JudgeObjectExist(It.No + It.G, It.B, sMNo, sComp, "28-2-4");  // It.No+It.G  序列号+工单
                    if (sOKFlag == "Y")  // 判断该物料是否已扫描完成
                    {
                        Message = "Y_EXISTMATER";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }

                if (It.F == "ZX")  // 表示扫描装箱物料
                {
                    sOKFlag = OrderBll.JudgeObjectExist(It.No + It.G, It.B, sMNo, sComp, "28-2-5"); // It.No+It.G  序列号+工单
                    if (sOKFlag == "Y")  // 判断该物料是否已装箱完成
                    {
                        Message = "Y_EXISTMATER";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }
            }

            if ((It.Name == "直接入库-返工生产") && (sCG == "Y") && (It.Remark == "在库重工换序列号"))
            {   // 重工换序列号场景，说明前面条件验证通过了，先把旧序列号的生产扣减物料全部拷贝到新的序列号
                sOKFlag = OrderBll.JudgeObjectExist(It.No + It.G, It.B, sMNo, sComp, "46-1"); // It.No+It.G  序列号+工单
                if (sOKFlag == "Y")//只有标记了是重工对象的才拷贝流程
                {
                    sFlag = "28-2-2";
                }
            }

            if (It.Name == "直接入库-返工生产" && It.Remark.IndexOf("在库重工不换序列号") != -1)//重工不换序列号 扫描的序列号是否和当前生产的序列号是否一致
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.No + It.G, It.B, sMNo, sComp, "46-1"); // It.No+It.G  序列号+工单
                if (sOKFlag == "Y")
                {
                    if (It.No.Trim() != sD.Trim())
                    {
                        return JsonConvert.SerializeObject(new { Msg = "N_RepeatObject" });
                    }
                }
            }





            sOKFlag = OrderBll.OPPRDInfo(It.No, It.Name, It.Item, sMNo, It.MName, It.A, It.B, It.C, sD, It.E, It.F, It.G, It.H, It.I, It.J, sComp, sEXEMan, It.Remark, sFlag); //  28-2
            if (sOKFlag.Length <= 2)  //  sOKFlag.IndexOf("操作成功") > 0
            {
                Message = "Success";

                // 扫描完每个物料，装箱物料完成后，判断一下接下来进入那个操作 
                sRNo = OrderBll.JudgeObjectExist(It.No + It.G, It.B, It.F, sComp, "28-2-6");  // It.No+It.G  序列号+工单
            }
            else
            {
                Message = "Error" + sOKFlag;
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });

            return Result;
        }
        #endregion


        #region 对生产执行信息进行操作--绑定/ 完成管理批次和序列号关系
        public string OPPRDUpbatchInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            string sBackStr = string.Empty;
            string sEXEMan = string.Empty;
            string sProc = string.Empty;
            string sSNWO = string.Empty;



            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                InMan = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sEXEMan = It.InMan;   //20240315: 使用前端传入的作业人员

            if (It.Flag == "29-8")  // 关联完成
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.A, "", "", sComp, "29-8-1");
                if (sOKFlag == "N")  // 判断是否有关联的序列号，如果没有提示； N 表示没有关联的序列号，不需要完成关联
                {
                    Message = "N_GLSN";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                if (string.IsNullOrEmpty(It.J))
                {
                    DataTable dt = DHRBll.CheckDHR(It.No, "", It.G, "", "", It.A, "", "", "", "PH", "", "", "CompleteCheckDHR", 10, 1, sEXEMan, sComp, IP);
                    foreach (DataRow row in dt.Rows)
                    {
                        try
                        {
                            string filePath = HttpContext.Current.Server.MapPath(row["FilePath"].ToString());//服务器路径
                            if (!File.Exists(filePath))
                            {
                                Message = "";
                                return JsonConvert.SerializeObject(new { Msg = "NOTDHR" });
                            }
                        }
                        catch (Exception ex)
                        {
                            return JsonConvert.SerializeObject(new { Msg = "NOTDHR" });
                        }
                    }

                }
            }
            else if (It.Flag == "29-9")
            {  // 用于判断是否能正常完成关联序列号
                sSNWO = It.No + "^@" + It.G; //It.No+It.G 序列号+工单。这样拼接，如果数据量大，查找是很慢的；所以传一个标识符进去，把工单和序列号拆开，再查询，这样就快了，其他地方可参考
                sProc = It.B + It.D;
                sOKFlag = OrderBll.JudgeObjectExist(sSNWO, sProc, It.C, sComp, "28-3-5-1");  // It.No+It.G 序列号+工单
                if (sOKFlag == "Y")
                {  // 判断测试项是否填写完成，Y表示没有填写完成
                    Message = "N_TestItem";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sOKFlag = OrderBll.JudgeObjectExist(It.No + It.G, sProc, It.C, sComp, "28-3-6");
                if (sOKFlag == "Y")
                { // 判断设备是否扫描完成，Y 表示没有扫描完成
                    Message = "N_Device";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sOKFlag = OrderBll.JudgeObjectExist(It.F, "", "", sComp, "28-3-7");
                if (sOKFlag == "Y")
                { // 判断设备是否点检完成； Y 表示还有需要点击的记录
                    Message = "CheckExist";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                sOKFlag = OrderBll.JudgeObjectExist(It.No + It.G, sProc, It.C, sComp, "28-3-9");
                if (sOKFlag == "Y")
                { // 判断质控品是否扫描完成，Y 表示没有扫描完成
                    Message = "N_ZKP";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                Message = "Success";
                return JsonConvert.SerializeObject(new { Msg = Message });

                //-- 20240601:在此之前，包装送检是必须所有序列号测试完成，才可以往下走。6月1号修改为：只要当前序列号所有测试项，物料，设备等扣减完成，就可以往下走，其他序列号测试项拷贝本序列号的
                //sOKFlag = OrderBll.JudgeObjectExist(It.No, It.D, "", sComp, "29-8-2");  // It.D 工单号
                //if (sOKFlag == "Y")  // 判断追溯物料是否已完成 Y:标识物料没有扣减完成  
                //{
                //    sBackStr = "1.关联的序列号中，有些追溯料或装箱料未扣减！";
                //}

                //sOKFlag = OrderBll.JudgeObjectExist(It.No, It.D, "", sComp, "29-8-3");    // It.D 工单号
                //if (sOKFlag == "Y")  // 判断测试项是否填写完成，Y表示没有填写完成
                //{
                //    sBackStr = sBackStr + "\n" + "2.关联的序列号中，有些测试项未完成！";
                //}
                //sOKFlag = OrderBll.JudgeObjectExist(It.No, It.D, "", sComp, "29-8-4");  // It.D 工单号
                //if (sOKFlag == "Y")  // 判断设备是否扫描完成，Y 表示没有扫描完成
                //{
                //    sBackStr = sBackStr + "\n" + "3.关联的序列号中，有些设备未扫描！";
                //}
                //sOKFlag = OrderBll.JudgeObjectExist(It.No, It.D, "", sComp, "29-8-5");  // It.D 工单号
                //if (sOKFlag == "Y")  // 判断设备是否点检完成； Y 表示还有需要点击的记录
                //{
                //    sBackStr = sBackStr + "\n" + "4.关联的序列号中，有些设备未点检！";
                //}

                //Message = "Success";
                //return JsonConvert.SerializeObject(new { Msg = Message, BStr = sBackStr });
            }


            sOKFlag = OrderBll.OPPRDInfo(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, sComp, sEXEMan, It.Remark, It.Flag);
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";

                // 扫描完每个物料，装箱物料完成后，判断一下接下来进入那个操作  
                sRNo = OrderBll.JudgeObjectExist(It.No + It.D, It.B, It.F, sComp, "28-2-6");  // It.No+It.D  序列号+工单
            }
            else if (sOKFlag.IndexOf("BZ_") > 0) // 说明返回的是包装上层批次
            {
                sRNo = sOKFlag.Substring(sOKFlag.IndexOf("_") + 1, sOKFlag.Length - sOKFlag.IndexOf("_") - 1);  // 2 + "BZ_jf2208200001"
            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });

            return Result;
        }
        #endregion



        #region 作业执行，获取检验方案信息
        public void GetInspectInfo(string Params, string sCNo, string sCItem, string sCMNo, string sCWO, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string json = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            DataTable dt = new DataTable();


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            HttpContext context = HttpContext.Current;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }

            dt = OrderBll.GetPRDInfo(sCNo, "", "", "", "", "", "", "", "", "", "", "", 50000, 1, sMan, sComp, It.Flag);  // 28-15 获取检验批次的检验方案
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
            }

            json = JsonConvert.SerializeObject(dt);

            Result = JsonConvert.SerializeObject(new { Msg = Message, json = json, BZBatch = sRNo });
            context.Response.Write(Result);
        }
        #endregion


        #region 更改检验方案信息
        public string OPInspectInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sPNo = string.Empty;


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            //if (It.Flag == "29-2")
            //{  // 新增不良现象，返回不合格单据
            //    sPNo = CreatePECode();

            //    sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "29-2-4");
            //    if (sOKFlag == "Y")  // 如果这个序列号还有未完成的维修单，不给继续添加，先维修完这个
            //    {
            //        Message = "Y_EXSIT";
            //        return JsonConvert.SerializeObject(new { Msg = Message });
            //    }
            //}



            // No: sNo, Name: "", Item: sSN, MNo: "", MName: "", A: sGXNo, B: sGXName, C: sFDate, D: sFMan, E: "", F: "", Remark: "", 
            sOKFlag = OrderBll.OPPRDInfo(sPNo, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, sComp, sLogin, It.Remark, It.Flag);  // 29-10
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sPNo });

            return Result;
        }
        #endregion



        #region 对生产执行信息进行操作-- 扫描异常代码，提交
        public string OPPRDYCCodeInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            string sEXEMan = string.Empty;


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                InMan = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sEXEMan = It.InMan;

            sOKFlag = OrderBll.JudgeObjectExist(It.No, It.A, "", sComp, "29-1-2");
            if (sOKFlag == "Y")  // 判断这个作业任务下的不合格单，是否已提交或已维修了。
            {
                Message = "Y_EXISTPRD";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }

            if (It.Flag == "29-1")
            {  // 扫描不良现象代码执行
                sOKFlag = OrderBll.JudgeObjectExist(It.No, "", "", sComp, "29-1");
                if (sOKFlag == "N")  // 判断异常代码是否存在
                {
                    Message = "N_EXISTYCCODE";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.A, "", sComp, "29-2");
                if (sOKFlag == "Y")  // 判断是否已扫描过了这个异常编号
                {
                    Message = "Y_EXISTYCCODE";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.A, "", sComp, "29-1-2");
                if (sOKFlag == "Y")  // 如果这个不合格单据已不是 创建 状态，表示已提交过，或者已维修，不能再继续扫描了
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

            }
            else if (It.Flag == "29-1-1")
            {  // 点击提交按钮执行
                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.A, "", sComp, "29-1-1");
                if (sOKFlag == "N")  // 判断这个作业任务是否有不合格处理单 = N,,表示没有，不能执行
                {
                    Message = "Y_NOTEXIST";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                if (string.IsNullOrEmpty(It.J))
                {
                    DataTable dt = DHRBll.CheckDHR(It.B, "", It.G, "", "", "", "", "", "", "SN", "", "", "CompleteCheckDHR", 10, 1, sEXEMan, sComp, IP);
                    foreach (DataRow row in dt.Rows)
                    {
                        try
                        {
                            if (row["DHRTpye"].ToString() != "HZ")
                            {
                                string filePath = HttpContext.Current.Server.MapPath(row["FilePath"].ToString());//服务器路径
                                if (!File.Exists(filePath))
                                {
                                    return JsonConvert.SerializeObject(new { Msg = "NOTDHR" });
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            return JsonConvert.SerializeObject(new { Msg = "NOTDHR" });
                        }
                    }
                }

                if (It.J == "BadSubmit")//到这，就说明前面条件满足提交不良，先返回生成DHR，在提交
                {
                    return JsonConvert.SerializeObject(new { Msg = "Success" });
                }
            }


            sOKFlag = OrderBll.OPPRDInfo(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, sComp, sEXEMan, It.Remark, It.Flag);
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });

            return Result;
        }
        #endregion


        #region 对生产执行信息进行操作-- 完工 28-3
        public string OPPRDOverInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            string sProc = string.Empty;
            string sYCFlag = string.Empty;
            string sSN = string.Empty;
            string sEXEMan = string.Empty;
            string sFilePath = string.Empty;
            string sSNWO = string.Empty;



            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                InMan = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sEXEMan = It.InMan;   //20240315: 使用前端传入的作业人员
            sSN = It.No;

            sOKFlag = OrderBll.JudgeObjectExist(It.No, "", "", sComp, "28-2-3");
            if (sOKFlag == "Y")
            {  // 判断序列号是否已完成
                Message = "Y_EXISTSOVER";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }

            sOKFlag = OrderBll.JudgeObjectExist(It.No + It.G, It.B, It.E, sComp, "28-3-1"); // It.No+It.G 序列号+工单
            if (sOKFlag == "Y")
            { // 判断序列号+工序+顺序编号（考虑重工） 是否已完成
                Message = "Y_EXISTSPROCOVER";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }


            // 如果是有异常的单，则下面的判断不需要了，直接完工了。
            sYCFlag = OrderBll.JudgeObjectExist(It.F, "", "", sComp, "28-3-8");    //判断是否有异常的单据
            if (sYCFlag == "N")  // = N  说明 没有 异常单据
            {
                if ((It.Flag == "28-3-1") || (It.Flag == "28-3-1-1"))
                {   // 28-3-1:说明有上层批次--包装检验批次,按包装送检批执行；28-3-1-1：说明是按批量生产的批次号执行，比如：老化，都是一批一批推进老化房的
                    sProc = It.B + It.D;

                    sOKFlag = OrderBll.JudgeObjectExist(It.A + It.G, It.B, It.Remark, sComp, "28-3-5-7");
                    if (sOKFlag == "N")
                    {  //判断检验中的数量是否等于样本量  = N ,表示还不够，
                        Message = "N_Enough";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }



                    //if (It.Remark == "整机包装检验"){   // 如果是增加包装检验，只要有一个序列号全部测试完成，设备扣减完成即可，另外一些序列号的测试项，设备，拷贝该测试完成的信息
                    //    sOKFlag = OrderBll.JudgeObjectExist(It.A, sProc, It.C, sComp, "28-3-5-0");
                    //}
                    //else {
                    //    sOKFlag = OrderBll.JudgeObjectExist(It.A, sProc, It.C, sComp, "28-3-5-2");
                    //}

                    sOKFlag = OrderBll.JudgeObjectExist(It.A, sProc, It.C, sComp, "28-3-5-0");
                    if (sOKFlag.IndexOf("NOTEST") >= 0)  //  sOKFlag == "NOTEST"
                    {  // 判断测试项是否填写完成，NOTEST 表示没有测试完成
                        Message = "N_PHTestItem";
                        sRNo = sOKFlag.Substring(sOKFlag.IndexOf("_") + 1, sOKFlag.Length - sOKFlag.IndexOf("_") - 1);  // 2 + "BZ_jf2208200001"
                        return JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });
                    }
                    else if (sOKFlag.IndexOf("NOTsb") >= 0)
                    {  // 有设备，而且没有扫描完成
                        Message = "N_PHSCANSB";
                        sRNo = sOKFlag.Substring(sOKFlag.IndexOf("_") + 1, sOKFlag.Length - sOKFlag.IndexOf("_") - 1);  // 2 + "BZ_jf2208200001"
                        return JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });
                    }
                    else if (sOKFlag.IndexOf("NOTzk") >= 0)
                    {  // 有质控品，而且没有扫描完成
                        Message = "N_PHSCANZK";
                        sRNo = sOKFlag.Substring(sOKFlag.IndexOf("_") + 1, sOKFlag.Length - sOKFlag.IndexOf("_") - 1);  // 2 + "BZ_jf2208200001"
                        return JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });
                    }
                    // 执行到这里： 如果 sOKFlag 返回的是 Y ,N 说明，说明没有测试项，序列号使用原来的序列号 
                    if ((sOKFlag == "Y") || (sOKFlag == "N"))
                    {
                        sSN = It.No;
                    }
                    else
                    {
                        sSN = sOKFlag;  // 如果上面的语句验证通过，返回一个有测试项的序列号，用这个序列号作为后面的信息查询，插入数据
                    }

                    sOKFlag = OrderBll.JudgeObjectExist(It.F, "", "", sComp, "28-3-7");
                    if (sOKFlag == "Y")
                    { // 判断设备是否点检完成； Y 表示还有需要点击的记录
                        Message = "CheckExist";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }
                else if ((It.Flag == "28-3") || (It.Flag == "28-3-2"))
                {   // 常规操作，扫描序列号完工作业。  28-3:工序完工；28-3-2 DHR放行工序

                    if (It.Remark != "重工拆解工序行为")
                    {   // 如果是重工单下的序列号，不需要判断是否物料扣减完成，可直接完工的  (It.Item != "直接入库-返工生产")
                        sOKFlag = OrderBll.JudgeObjectExist(It.No + It.G, It.B, It.C, sComp, "28-3-2"); // It.No+It.G 序列号+工单
                        if (sOKFlag == "Y")
                        {  // 判断追溯物料是否已完成 Y:标识物料没有扣减完成
                            Message = "N_MATER";
                            return JsonConvert.SerializeObject(new { Msg = Message });
                        }
                    }

                    sSNWO = It.No + "^@" + It.G; //It.No+It.G 序列号+工单。这样拼接，如果数据量大，查找是很慢的；所以传一个标识符进去，把工单和序列号拆开，再查询，这样就快了，其他地方可参考
                    sProc = It.B + It.D;
                    sOKFlag = OrderBll.JudgeObjectExist(sSNWO, sProc, It.C, sComp, "28-3-5-1");  // It.No+It.G 序列号+工单
                    if (sOKFlag == "Y")
                    {  // 判断测试项是否填写完成，Y表示没有填写完成
                        Message = "N_TestItem";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                    sOKFlag = OrderBll.JudgeObjectExist(It.No + It.G, sProc, It.C, sComp, "28-3-6");
                    if (sOKFlag == "Y")
                    { // 判断设备是否扫描完成，Y 表示没有扫描完成
                        Message = "N_Device";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                    sOKFlag = OrderBll.JudgeObjectExist(It.F, "", "", sComp, "28-3-7");
                    if (sOKFlag == "Y")
                    { // 判断设备是否点检完成； Y 表示还有需要点击的记录
                        Message = "CheckExist";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }

                    sOKFlag = OrderBll.JudgeObjectExist(It.No + It.G, sProc, It.C, sComp, "28-3-9");
                    if (sOKFlag == "Y")
                    { // 判断质控品是否扫描完成，Y 表示没有扫描完成
                        Message = "N_ZKP";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }

                    if (It.Flag == "28-3")// 判断是否有获取测试项
                    {
                        sOKFlag = OrderBll.JudgeObjectExist(It.No + It.G, It.F, It.C, sComp, "38-1"); // It.No+It.G 序列号+工单
                        if (sOKFlag == "Y")
                        { // 判断是否有获取测试项
                            Message = "N_NOTEST";
                            return JsonConvert.SerializeObject(new { Msg = Message });
                        }
                    }

                    //if (It.Flag == "28-3-2")
                    //{  //DHR单个放行时特别判断
                    //    sOKFlag = OrderBll.JudgeObjectExist(It.No, "", "", sComp, "28-3-5-6");
                    //    if (sOKFlag == "N")
                    //    { // 判断这个准备放行的序列号，是否生产中的。
                    //        Message = "N_SNNOTPRDING";
                    //        return JsonConvert.SerializeObject(new { Msg = Message });
                    //    }

                    //    // 判断是否已产生了DHR文件
                    //    sFilePath = System.AppDomain.CurrentDomain.BaseDirectory + "DHRFile\\" + It.G + "\\" + It.No;  // // 服务器根目录下的文件夹
                    //    if (Directory.Exists(sFilePath))//存在文件夹
                    //    {
                    //        string[] fileList = Directory.GetFiles(sFilePath);//文件夹下的所有文件 个数
                    //        if (fileList.Length == 0)
                    //        { // 表示这个文件夹没有文件
                    //            Message = "N_NOTFILE";
                    //            return JsonConvert.SerializeObject(new { Msg = Message });
                    //        }

                    //    }
                    //    else
                    //    {  // 没有这个文件夹
                    //        Message = "N_NOTFILE";
                    //        return JsonConvert.SerializeObject(new { Msg = Message });
                    //    }

                    //}

                }
                else if (It.Flag == "28-3-3")
                {   // DHR放行，批量放行判断

                    sProc = It.B + It.D;
                    // 判断这个批次下的所有序列号是否都在生产中
                    sOKFlag = OrderBll.JudgeObjectExist(It.A, sProc, It.C, sComp, "28-3-5-5");
                    if (sOKFlag == "Y")
                    {  // 说明还有些序列号不在生产中，要先扫描序列号，让其再生产中，才可以批量放行
                        Message = "N_NOTPRDING";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }

                    // 判定当前作业的序列号是否测试完成，设备是否扫描完成，质控品是否完成
                    sSNWO = It.No + "^@" + It.G; //It.No+It.G 序列号+工单。这样拼接，如果数据量大，查找是很慢的；所以传一个标识符进去，把工单和序列号拆开，再查询，这样就快了，其他地方可参考
                    sOKFlag = OrderBll.JudgeObjectExist(sSNWO, sProc, It.C, sComp, "28-3-5-1");  // It.No+It.G 序列号+工单
                    if (sOKFlag == "Y")
                    {  // 判断测试项是否填写完成，Y表示没有填写完成
                        Message = "N_TestItem";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                    sOKFlag = OrderBll.JudgeObjectExist(It.No + It.G, sProc, It.C, sComp, "28-3-6");
                    if (sOKFlag == "Y")
                    { // 判断设备是否扫描完成，Y 表示没有扫描完成
                        Message = "N_Device";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                    sOKFlag = OrderBll.JudgeObjectExist(It.F, "", "", sComp, "28-3-7");
                    if (sOKFlag == "Y")
                    { // 判断设备是否点检完成； Y 表示还有需要点击的记录
                        Message = "CheckExist";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }

                    sOKFlag = OrderBll.JudgeObjectExist(It.No + It.G, sProc, It.C, sComp, "28-3-9");
                    if (sOKFlag == "Y")
                    { // 判断质控品是否扫描完成，Y 表示没有扫描完成
                        Message = "N_ZKP";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }

                    // 判断是否都产生了DHR报表
                    //sFilePath = System.AppDomain.CurrentDomain.BaseDirectory + "DHRFile\\" + It.G + "\\" + It.No;  // // 服务器根目录下的文件夹
                    //if (Directory.Exists(sFilePath))//存在文件夹
                    //{
                    //    string[] fileList = Directory.GetFiles(sFilePath);//文件夹下的所有文件 个数
                    //    if (fileList.Length == 0)
                    //    { // 表示这个文件夹没有文件
                    //        Message = "N_NOTFILE";
                    //        return JsonConvert.SerializeObject(new { Msg = Message });
                    //    }
                    //}
                    //else
                    //{  // 没有这个文件夹
                    //    Message = "N_NOTFILE";
                    //    return JsonConvert.SerializeObject(new { Msg = Message });
                    //}
                }


            }
            else
            {  //有异常单号，判断异常单号是否已提交

                // 判断是否有异常单号，是否已提交，如果没有提交，不给完工
                sOKFlag = OrderBll.JudgeObjectExist(It.F, "", "", sComp, "28-3-8-1");
                if (sOKFlag == "Y")
                {  // = Y 说明这个异常单未提交
                    Message = "NOTCOMMITYC";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

            }

            if ((It.Flag == "28-3" || It.Flag == "28-3-2") && It.J == "Over")//针对单个工序完工,到这，就说明前面条件满足完工，先返回生成DHR，在完工
            {
                return JsonConvert.SerializeObject(new { Msg = "Success" });
            }

            if (string.IsNullOrEmpty(It.J))
            {
                string sE = It.Flag == "28-3" || It.Flag == "28-3-2" ? "SN" : "PH";
                DataTable dt = DHRBll.CheckDHR(sSN, "", It.G, "", "", It.A, "", "", "", sE, "", "", "CompleteCheckDHR", 10, 1, sEXEMan, sComp, IP);
                foreach (DataRow row in dt.Rows)
                {
                    try
                    {
                        string filePath = HttpContext.Current.Server.MapPath(row["FilePath"].ToString());//服务器路径
                        if (!File.Exists(filePath))
                        {
                            return JsonConvert.SerializeObject(new { Msg = "NOTDHR" });
                        }
                    }
                    catch (Exception ex)
                    {
                        return JsonConvert.SerializeObject(new { Msg = "NOTDHR"});
                    }
                }
            }


            //28-3:单个序列号工序完工；28-3-1：工序行为选择了“整机包装检验”需要按照整机送检批号完工；28-3-2：单个序列号整机放行；28-3-3：批量序列号整机放行
            sOKFlag = OrderBll.OPPRDInfo(sSN, It.Name, "", "", "", It.A, It.B, It.C, It.D, sYCFlag, It.F, It.G, It.H, It.I, It.J, sComp, sEXEMan, It.Remark, It.Flag);
            if (sOKFlag.Length <= 4)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });

            return Result;
        }
        #endregion


        #region 不合格流程处理界面执行的
        public string OPECodeInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sPNo = string.Empty;


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sPNo = It.No;

            if (It.Flag == "29-2")
            {  // 新增不良现象，返回不合格单据
                sPNo = CreatePECode();

                sOKFlag = OrderBll.JudgeObjectExist(It.Item, "", "", sComp, "29-2-4");
                if (sOKFlag == "Y")  // 如果这个序列号还有未完成的维修单，不给继续添加，先维修完这个
                {
                    Message = "Y_EXSIT";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }

            sOKFlag = OrderBll.JudgeObjectExist(It.No, It.A, It.E, sComp, "29-2-1");
            if (sOKFlag == "Y") // 如果这个不合格单已在维修中，维修完成，不可操作
            {
                Message = "N_EXSITCode";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }

            if (It.Flag == "29-2-1")   // 扫描不良现象代码时
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.E, "", "", sComp, "29-1");
                if (sOKFlag == "N")  // 判断异常代码是否存在
                {
                    Message = "N_NOCODE";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.A, It.E, sComp, "29-2-3");
                if (sOKFlag == "Y")  // 判断异常代码在这个不合格单据是否已扫描
                {
                    Message = "N_EXSITCode";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }


            }


            // No: sNo, Name: "", Item: sSN, MNo: "", MName: "", A: sGXNo, B: sGXName, C: sFDate, D: sFMan, E: "", F: "", Remark: "", 
            sOKFlag = OrderBll.OPPRDInfo(sPNo, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, sComp, sLogin, It.Remark, It.Flag);
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sPNo });

            return Result;
        }
        #endregion


        #region 对重工单信息进行操作
        public string OPReworkOrder(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            // No: sRWO, Name: "", Item: "", MNo: sRMNo, MName: sRMName, A: sSpec, B: sRNum, C: sOWO, D: sNum,
            if (It.Flag == "34-1")
            {  // 重工单绑定原工单
                sOKFlag = OrderBll.JudgeObjectExist(It.No, "", "", sComp, "34-1-1");
                if (sOKFlag == "Y")  // 判断这个重工单是否重新发放序列号，如果有，不给绑定 
                {
                    Message = "Y_EXISTSN";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sOKFlag = OrderBll.JudgeObjectExist(It.C, It.D, "", sComp, "34-1-2");
                if (sOKFlag == "N")  // 判断工单是否存在
                {
                    Message = "N_EXISTWO";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sOKFlag = OrderBll.JudgeObjectExist(It.C, It.D, "", sComp, "34-1-3");
                if (sOKFlag == "Y")  // 判断绑定在这个重工单的数量，不能大于原工单数量, 
                {
                    Message = "Y_EXISTNUM";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

            }
            else if (It.Flag == "34-2")
            {  // 删除 
                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.A, "", sComp, "34-2-1");
                if (sOKFlag == "Y")  //如果存在工单绑定序列号关系，则表示已进入生产，不能删除
                {
                    Message = "Y_EXISTPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

            }




            sOKFlag = OrderBll.OPReworkOrder(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, sComp, sLogin, It.Remark, It.Flag);
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });

            return Result;
        }
        #endregion




        #region 对生产执行信息进行操作 -- 这个方法后面删除     28-4  有用，看看是不是拆出来
        public string OPPRDInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            string sDNo = string.Empty;
            string sIQCNo = string.Empty;
            string sSNum = string.Empty;
            string sRMsg = string.Empty;
            string K3_GetUrl1 = string.Empty;
            string K3_dbId = string.Empty;
            string K3_userName = string.Empty;
            string K3_password = string.Empty;
            int K3_lcid = 0;
            string sResult = string.Empty;
            string sFlag = string.Empty;
            string sNo = string.Empty;
            string sName = string.Empty;
            string sDept = string.Empty;
            string sSyncNo = DateTime.Now.ToString("yyyyMMddHHmmss"); // 产生一个同步编号，表示的数据是一起的


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            //接口的列可以跟数据表的列不一致， 但是顺序必须保持一致，要不然到时候会出错，并且接口的列不能少于数据表的列,过滤条件： \"FilterString\":\"Fnumber='1101-00004-01'\" ； \"Fnumber in ('1101-00004-01','1101-00006-01')\"
            // List<List<Object>> list = client.ExecuteBillQuery("{\"FormId\":\"BD_MATERIAL\",\"FieldKeys\":\"" + sK3 + "\",\"FilterString\":\"Fnumber='1101-00004-01'\",\"OrderString\":\"\",\"TopRowCount\":0,\"StartRow\":0,\"Limit\":2000,\"SubSystemId\":\"\"}");
            //List<List<Object>> list = client.ExecuteBillQuery("{\"FormId\":\"" + sInTable + "\",\"FieldKeys\":\"" + sK3Column + "\",\"FilterString\":\"" + sFilter + "\",\"OrderString\":\"\",\"TopRowCount\":0,\"StartRow\":0,\"Limit\":2000,\"SubSystemId\":\"\"}");
            //string sJson = GetK3InStockInfo(sNo, "1");
            //string ss = client.Save("PRD_INSTOCK", sJson);

            // Message = GetInStockDataInfo(Params);

            //Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });

            // return Result;



            sOKFlag = OrderBll.OPPRDInfo(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, sComp, sLogin, It.Remark, "buyongle");
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";

                // 扫描完每个物料，装箱物料完成后，判断一下接下来进入那个操作  
                sRNo = OrderBll.JudgeObjectExist(It.No, It.B, It.F, sComp, "28-2-6");
            }
            else if (sOKFlag.IndexOf("BZ_") > 0) // 说明返回的是包装上层批次
            {
                sRNo = sOKFlag.Substring(sOKFlag.IndexOf("_") + 1, sOKFlag.Length - sOKFlag.IndexOf("_") - 1);  // 2 + "BZ_jf2208200001"
            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });

            return Result;
        }
        #endregion



        public static string GetInStockDataInfo(string DataParams)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string No = string.Empty;
            string Flag = string.Empty;
            string ProductType = string.Empty;    //产品类型
            string StorageType = string.Empty;  //入库类型
            string StoreHouseNo = string.Empty; //仓库
            string ProduceDate = string.Empty; //生产日期
            string ExpiryDate = string.Empty; //过期时间
            bool IsAccomplish = false;   //是否完工
            bool IsBackWms = false;    //是否已回传WMS
            bool IsReverseMaterial = false; //是否倒冲领料
            bool IsAutoSubmitAndAudit = false;  //是否自动提交审核
            string K3_GetUrl1 = string.Empty;
            string K3_dbId = string.Empty;
            string K3_userName = string.Empty;
            string K3_password = string.Empty;
            int K3_lcid = 0;
            int ealityNum = 0;
            string HttpResult = string.Empty;

            HttpContext context = HttpContext.Current;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            var AnonymousUser = new
            {
                No = String.Empty,//序列号
                ProductType = String.Empty,
                StorageType = String.Empty,
                StoreHouseNo = String.Empty,
                ProduceDate = String.Empty,
                ExpiryDate = String.Empty,
                IsAccomplish = false,
                IsBackWms = false,
                IsReverseMaterial = false,
                RKFS = String.Empty, //如果RKFS=="1" No就是传序列号, RKFS=="2"  No就是传工单编号
                IsAutoSubmitAndAudit = String.Empty
            };

            var Item2 = JsonConvert.DeserializeObject(DataParams);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            No = Item.No;
            Flag = Item.RKFS;
            ProductType = Item.ProductType;
            StorageType = Item.StorageType;
            StoreHouseNo = Item.StoreHouseNo;
            ProduceDate = Item.ProduceDate;
            ExpiryDate = Item.ExpiryDate;
            IsAccomplish = Item.IsAccomplish;
            IsBackWms = Item.IsBackWms;
            IsReverseMaterial = Item.IsReverseMaterial;
            IsAutoSubmitAndAudit = Item.IsAutoSubmitAndAudit == "false" ? true : false;

            DataTable dtK3 = OrderBll.GetK3UrlInfo("获取K3Url相关信息", sComp);
            if (dtK3.Rows.Count > 0)
            {
                K3_GetUrl1 = dtK3.Rows[0]["C1"].ToString();
                K3_dbId = dtK3.Rows[0]["C2"].ToString();
                K3_userName = dtK3.Rows[0]["C3"].ToString();
                K3_password = dtK3.Rows[0]["C4"].ToString();
                K3_lcid = int.Parse(dtK3.Rows[0]["C5"].ToString());
            }
            else
            {
                Message = "Y_NotEXIST" + "，获取不到同步K3的相关地址链接！";
                HttpResult = "Y_NotEXIST" + "，获取不到同步K3的相关地址链接！";
                return HttpResult;
            }

            K3CloudApiClient client = new K3CloudApiClient(K3_GetUrl1);
            var loginResult = client.ValidateLogin(K3_dbId, K3_userName, K3_password, K3_lcid);
            var result = JObject.Parse(loginResult)["LoginResultType"].Value<int>();

            if (result == 1)
            {
                //入库数据
                DataTable dt = OrderBll.GetInStockDataInfo(No, sMan, sComp, Flag);

                if (dt.Rows.Count == 0)
                {
                    HttpResult = "没有需要入库的数据";
                    return HttpResult;
                }
                ealityNum = dt.Rows.Count;  // 实际入库数量

                string materNo = dt.Rows[0]["MaterNo"].ToString();
                //判断入库的产品编码中是否包含CTO如果包含就需要传BOMNo，没有就不用
                string bomNo = materNo.Contains("CTO") ? dt.Rows[0]["BOMNo"].ToString() : string.Empty;

                JsonObj obj = new JsonObj();
                obj.FormId = "PRD_INSTOCK";
                JsonData data = new JsonData();
                //List<JsonFlowId> l = new List<JsonFlowId>();//注意这个对象不要赋值，使用空对象

                //注意下面这两个不能取消注释
                //data.NeedUpDateFields = "[]";
                //data.NeedReturnFields = "[]";

                data.IsDeleteEntry = "true";
                data.SubSystemId = "";
                data.IsVerifyBaseDataField = "true";
                data.IsEntryBatchFill = "true";
                data.ValidateFlag = "true";
                data.NumberSearch = "true";
                data.IsAutoAdjustField = "false";
                data.InterationFlags = "";
                data.IgnoreInterationFlag = "";
                data.IsControlPrecision = "false";
                data.ValidateRepeatJson = "false";
                data.IsAutoSubmitAndAudit = IsAutoSubmitAndAudit;
                JsonModel model = new JsonModel();
                model.FID = 0;  // 固定
                model.FDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");  // 当前日期  -- 当前时间

                JsonFNumber fNumber_FBillType = new JsonFNumber();
                fNumber_FBillType.FNumber = "SCRKD02_SYS";  // 单据类型（生产入库单：SCRKD02_SYS）-- 固定
                model.FBillType = fNumber_FBillType;

                JsonFNumber fNumber_FStockOrgId = new JsonFNumber();
                fNumber_FStockOrgId.FNumber = "100";   // 入库组织（麦科田生物医疗：100）-- 固定
                model.FStockOrgId = fNumber_FStockOrgId;

                //JsonFNumber fNumber_FStockId0 = new JsonFNumber();
                //fNumber_FStockId0.FNumber = "1";   
                //model.FStockId0 = fNumber_FStockId0;

                JsonFNumber fNumber_FPrdOrgId = new JsonFNumber();
                fNumber_FPrdOrgId.FNumber = "100";   // 生产组织（麦科田生物医疗：100）-- 固定
                model.FPrdOrgId = fNumber_FPrdOrgId;

                //JsonFNumber fNumber_FWorkShopId = new JsonFNumber();
                //fNumber_FWorkShopId.FNumber = "1";  
                //model.FWorkShopId = fNumber_FWorkShopId;

                model.FOwnerTypeId0 = "BD_OwnerOrg";  // 固定

                JsonFNumber fNumber_FOwnerId0 = new JsonFNumber();
                fNumber_FOwnerId0.FNumber = "100";  // 货主（麦科田生物医疗：100）-- 固定
                model.FOwnerId0 = fNumber_FOwnerId0;

                //JsonFNumber fNumber_FSTOCKERID = new JsonFNumber();
                //fNumber_FSTOCKERID.FNumber = "1";   
                //model.FSTOCKERID = fNumber_FSTOCKERID;

                model.FDescription = "";
                model.F_BHR_WMSBack = IsBackWms;  // WMS已回传  -- 固定          //
                model.F_BHR_NoWMS = false;    // 不同步WMS-- 固定
                model.F_BHR_WMS = false;      // 已同步WMS-- 固定
                model.F_BHR_ZDZF = false;     // 固定

                JsonEntity entity = new JsonEntity();
                entity.FSrcEntryId = int.Parse(dt.Rows[0]["FTreeEntity_FEntryId"].ToString());  // 源单明细ID（工单上的FTreeEntity_FEntryId字段，同步工单时获取）  -- 变量
                entity.FIsNew = false;        // 固定

                JsonFNumber fNumber_FMaterialId = new JsonFNumber();
                fNumber_FMaterialId.FNumber = materNo;//"6010B-CTO-02";   // 变量
                entity.FMaterialId = fNumber_FMaterialId;

                entity.FProductType = ProductType;  // 产品类型（主产品：1；联产品：2；副产品：3）  -- 变量
                entity.FInStockType = StorageType;  // 入库类型（合格品入库：1；不合格入库：2；报废品入库：3；返工品入库）  -- 变量

                JsonFNumber fNumber_FUnitID = new JsonFNumber();
                fNumber_FUnitID.FNumber = dt.Rows[0]["Unit"].ToString();  // 单位  -- 变量
                entity.FUnitID = fNumber_FUnitID;


                entity.FMustQty = double.Parse(dt.Rows[0]["OrderNum"].ToString());  // 应收数量  -- 变量
                entity.FRealQty = ealityNum;//int.Parse(dt.Rows[0]["ealityNum"].ToString());  // 实收数量  -- 变量

                JsonFNumber fNumber_FBaseUnitId = new JsonFNumber();
                fNumber_FBaseUnitId.FNumber = dt.Rows[0]["Unit"].ToString();  // 基本单位  -- 变量
                entity.FBaseUnitId = fNumber_FBaseUnitId;

                entity.FOwnerTypeId = "BD_OwnerOrg";  // 货主类型    -- 固定

                JsonFNumber fNumber_FOwnerId = new JsonFNumber();
                fNumber_FOwnerId.FNumber = "100";  // 麦科田生物医疗：100  -- 固定
                entity.FOwnerId = fNumber_FOwnerId;

                JsonFNumber fNumber_FStockId = new JsonFNumber();
                fNumber_FStockId.FNumber = StoreHouseNo;  // 仓库    -- 变量
                entity.FStockId = fNumber_FStockId;

                JsonFNumber fNumber_FBomId = new JsonFNumber();
                fNumber_FBomId.FNumber = bomNo;// "6010B-CTO-02_V4.3"; //BOM版本
                entity.FBomId = fNumber_FBomId;

                entity.FISBACKFLUSH = IsReverseMaterial; // 是否倒冲领料    -- 固定

                JsonFdeptid Fdeptid_FDEPTID = new JsonFdeptid();
                Fdeptid_FDEPTID.FDEPTID = 100946;
                entity.FWorkShopId1 = Fdeptid_FDEPTID; // 生产车间  -- 变量

                entity.FSecRealQty = 0;  // 变量  辅助单位实收数量

                entity.FMoBillNo = dt.Rows[0]["OrderNo"].ToString();  // 生产订单编号   -- 变量
                entity.FMoId = int.Parse(dt.Rows[0]["FID"].ToString());  // 生产订单ID（生产订单FID字段，同步工单时获取）   -- 变量
                entity.FMoEntryId = int.Parse(dt.Rows[0]["FTreeEntity_FEntryId"].ToString());  // 生产订单明细ID（生产订单FTreeEntity_FEntryId字段，同步工单时获取）   -- 变量

                entity.FMoEntrySeq = int.Parse(dt.Rows[0]["FTreeEntity_FSeq"].ToString()); //生产订单行号（生产订单FTreeEntity_FSeq字段，同步工单时获取）   -- 变量

                JsonFNumber fNumber_FStockUnitId = new JsonFNumber();
                fNumber_FStockUnitId.FNumber = dt.Rows[0]["Unit"].ToString();  // 库存单位   -- 变量
                entity.FStockUnitId = fNumber_FStockUnitId;

                entity.FStockRealQty = ealityNum;//int.Parse(dt.Rows[0]["ealityNum"].ToString()); //  -- 变量   库存单位实收数量

                entity.FIsFinished = IsAccomplish;  // 是否完工   -- 变量

                JsonFNumber fNumber_FStockStatusId = new JsonFNumber();
                fNumber_FStockStatusId.FNumber = "KCZT01_SYS";  // 库存状态（可用：KCZT01_SYS）  -- 固定
                entity.FStockStatusId = fNumber_FStockStatusId;

                entity.FSrcEntrySeq = int.Parse(dt.Rows[0]["FTreeEntity_FSeq"].ToString());   // 源单明细行号（生产订单FTreeEntity_FSeq字段，同步工单时获取）  -- 变量

                entity.FMOMAINENTRYID = int.Parse(dt.Rows[0]["FTreeEntity_FEntryId"].ToString()); // 生产订单明细ID（生产订单FTreeEntity_FEntryId字段，同步工单时获取）  -- 变量
                entity.FSrcBillType = "PRD_MO"; // 源单类型（生产订单：PRD_MO）    -- 固定
                entity.FSrcBillNo = dt.Rows[0]["OrderNo"].ToString(); // "11-2311-0021";  // 源单编码    -- 变量
                entity.FKeeperTypeId = "BD_KeeperOrg";  // 固定
                entity.FBasePrdRealQty = ealityNum;  // 变量  基本单位生产实收数量

                JsonFNumber fNumber_FKeeperId = new JsonFNumber();
                fNumber_FKeeperId.FNumber = "100"; //变量   保管者
                entity.FKeeperId = fNumber_FKeeperId;

                entity.FProduceDate = ProduceDate;  // 生产日期   -- 变量

                entity.FExpiryDate = ExpiryDate;   // 有效期至   -- 变量

                List<JsonSerial> serialList = new List<JsonSerial>();//这个是参数需要传进来,支持循环
                foreach (DataRow item in dt.Rows)
                {
                    //循环添加
                    JsonSerial serial = new JsonSerial();
                    serial.FSerialNo = item["BatchNo"].ToString();  // 序列号   -- 变量
                    serial.FQty = Convert.ToInt32(item["InNum"]);        // 序列号数量    -- 变量
                    serial.FSerialNote = "";  // 序列号备注   -- 变量
                    serialList.Add(serial);
                }

                entity.FSerialSubEntity = serialList;

                JsonLink link = new JsonLink();
                link.FEntity_Link_FRuleId = "PRD_MO2INSTOCK";  // 生产订单到生产入库单单据转换标识      -- 固定 
                link.FEntity_Link_FSTableName = "T_PRD_MOENTRY"; // 生产订单表体表名    -- 固定
                link.FEntity_Link_FSBillId = dt.Rows[0]["FID"].ToString();// "101094";  // 生产订单ID（生产订单FID字段，同步工单时获取）      -- 变量
                link.FEntity_Link_FSId = dt.Rows[0]["FTreeEntity_FEntryId"].ToString();    // 产订单明细ID（生产订单FTreeEntity_FEntryId字段，同步工单时获取）      -- 变量
                link.FEntity_Link_FFlowId = "f11b462a-8733-40bd-8f29-0906afc6a201";  //业务流程Id（生产直接入库流程【f11b462a-8733-40bd-8f29-0906afc6a201】） -- 固定 
                link.FEntity_Link_FFlowLineId = "5"; // 生产订单到生产入库单，默认等于5     -- 固定 
                link.FEntity_Link_FBasePrdRealQtyOld = ealityNum.ToString();   //生产订单基本数量，等于生产入库单基本单位实收数量也可以     -- 变量

                link.FEntity_Link_FBasePrdRealQty = ealityNum.ToString();  // 生产入库单基本单位实收数量     -- 变量

                JsonLink[] links = new JsonLink[] { link };

                entity.FEntity_Link = links;

                JsonFlowId flowId = new JsonFlowId();
                flowId.FID = "f11b462a-8733-40bd-8f29-0906afc6a201";  //  固定 
                entity.FBFLowId = flowId;

                JsonEntity[] flow = new JsonEntity[] { entity };

                model.FEntity = flow;
                data.Model = model;
                obj.data = data;

                string jo2 = JsonConvert.SerializeObject(obj.data);

                //获取请求的结果
                HttpResult = client.Save("PRD_INSTOCK", jo2);

                //K3HttpResultEntity K3请求结果的实体类
                K3HttpResultEntity k3HttpResultEntity = JsonConvert.DeserializeObject<K3HttpResultEntity>(HttpResult);
                //k3HttpResultEntity.Result.Responsestatus.Issuccess 如果入库成功就把入库单号\ID保存
                if (k3HttpResultEntity.Result.Responsestatus.Issuccess)
                {
                    Result = OrderBll.OPInStockInfo(k3HttpResultEntity.Result.Responsestatus.SuccessEntitys[0].Number, "", "", "", "", No, k3HttpResultEntity.Result.Responsestatus.SuccessEntitys[0].Id, "", "", "", "", "", "", "", sComp, sMan, "", "32-7");
                }

                // context.Response.Write(HttpResult);
            }

            return HttpResult;
        }




        #region 维修：获取相关信息  
        public void GetWXInfo(string Params, string sCNo, string sCItem, string sCMNo, string sCWO, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string json = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            DataTable dt = new DataTable();


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            HttpContext context = HttpContext.Current;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }

            if (It.Flag == "28-2")
            {   // 维修扫描作业单元
                sOKFlag = OrderBll.JudgeObjectExist(sCNo, "", "", sComp, "28-2-1");
                if (sOKFlag == "N")  // // 判断作业单元是否存在 
                {
                    Message = "N_EXIST";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                    context.Response.Write(Result);
                    return;
                }

                sOKFlag = OrderBll.JudgeObjectExist(sCNo, sMan, "", sComp, "28-2-2");
                if (sOKFlag == "N")   // 该用户是否有该作业单元权限
                {
                    Message = "N_USEREXIST";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                    context.Response.Write(Result);
                    return;
                }

                //// 判断作业单元是否被其他作业人员使用了，主要判断进行中的序列号即可
                //sOKFlag = OrderBll.JudgeObjectExist(sEXEMan, sCNo, "", sComp, "28-1-4-1");
                //if (sOKFlag == "Y")  // 已被使用 
                //{
                //    Message = "Y_UNITUSE";
                //    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                //    context.Response.Write(Result);
                //    return;
                //}
            }
            else if (It.Flag == "30-2")
            { // 维修扫描序列号 231020:暂时不做判断，因为已维修完成的也能看到，只是不给操作

                //sOKFlag = OrderBll.JudgeObjectExist(It.No, sMan, "", sComp, "30-2-0");
                //if (sOKFlag == "N")   // 判断这个序列号是否有要维修的记录，或者已维修完成
                //{
                //    Message = "N_NOTRepair";
                //    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                //    context.Response.Write(Result);
                //    return;
                //}

                sOKFlag = OrderBll.JudgeObjectExist(It.No, sMan, "", sComp, "30-2-4");
                if (sOKFlag == "Y")   // 判断是否创建状态，如果是，需要在作业执行提交异常单，这样才能插入  维修  工序
                {
                    Message = "Y_CREATE";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                    context.Response.Write(Result);
                    return;
                }
            }
            else if (It.Flag == "31-1")
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.No, "", "", sComp, "42-1");
                if (sOKFlag == "Y")   // 判断批量异常处理单状态是不是已处理
                {
                    Message = "Y_ISOK";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                    context.Response.Write(Result);
                    return;
                }

                // 判断抽检的不良的序列号有没有维修完成以及返回工序是否完工
                //sOKFlag = OrderBll.JudgeObjectExist(sOKFlag, "", "", sComp, "42-4");
                //if (sOKFlag == "Y")   
                //{
                //    Message = "Y_WXISOK";
                //    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                //    context.Response.Write(Result);
                //    return;
                //}

                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.E, "", sComp, "42-2");
                if (sOKFlag == "Y")   // 判断输入的作业单元是否匹配
                {
                    Message = "Y_UNITNOT";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                    context.Response.Write(Result);
                    return;
                }
            }
            dt = OrderBll.GetPRDInfo(It.No, It.Item, "", "", "", "", "", "", It.A, It.B, "", "", 50000, 1, sMan, sComp, It.Flag);  // 
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                if (Flag == "28-2")
                {
                    sRNo = dt.Rows[0]["CenterDesc"].ToString();  // 作业单元描述 
                }
            }
            else
            {
                Message = "Error";
            }

            json = JsonConvert.SerializeObject(dt);

            Result = JsonConvert.SerializeObject(new { Msg = Message, json = json, BZBatch = sRNo });
            context.Response.Write(Result);

            // return Result;

        }
        #endregion


        #region 维修：各种维修操作
        public string OPWXInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            string sMNo = string.Empty;
            string sD = string.Empty;

            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);



            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sMNo = It.MNo;
            sD = It.D;

            if (It.Flag == "30-2-1") // 扫描不良原因后点击 提交,保存不良现象对应的不良原因
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.E, It.Item, sComp, "30-2-1");
                if (sOKFlag == "Y")  // 判断是否已扫描过了这个原因代码
                {
                    Message = "Y_EXISTYY";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                sOKFlag = OrderBll.JudgeObjectExist(It.No, "", "", sComp, "30-2-2");
                if (sOKFlag == "Y")  // 如果这个不合格单据已不是完成，不能再扫描了
                {
                    Message = "Y_ISOVER";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

            }
            else if (It.Flag == "30-2-4")
            {  // 维修：拆解物料
                sOKFlag = OrderBll.JudgeObjectExist(It.No, sLogin, "", sComp, "30-2-0");
                if (sOKFlag == "N")   // 判断这个序列号是否有要维修的记录，或者已维修完成
                {
                    Message = "N_NOTRepair";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "30-2-5")//维修： 扫描安装物料
            {
                if (It.D.IndexOf(";") > 0)
                {
                    sMNo = It.D.Substring(0, It.D.IndexOf(";"));
                    sD = It.D.Substring(It.D.IndexOf(";") + 1, It.D.Length - It.D.IndexOf(";") - 1);
                    if (sD == "")//如果等于空 ，就说明分号后面没有批号
                    {
                        Message = "N_EXISTOVER";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }
                else
                {
                    sOKFlag = OrderBll.JudgeObjectExist(It.D, "", "", sComp, "28-2-7");
                    if (sOKFlag == "N")  // 判断扫描的序列号，批号是否在批序号关系表存在; N 表示批号或序列号不存在，或者已使用完了
                    {
                        Message = "N_EXISTOVER";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                    else
                    {  // 获取物料编码
                        sMNo = sOKFlag;
                    }
                }

                sOKFlag = OrderBll.JudgeObjectExist(It.Item, It.F, "", sComp, "28-2-3");
                if (sOKFlag == "Y")  // 判断序列号是否已完成
                {
                    Message = "Y_EXISTSOVER";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                sOKFlag = OrderBll.JudgeObjectExist(It.Item + It.F, It.B, sMNo, sComp, "30-2-3");  //It.Item+ It.F 序列号+工单 
                if (sOKFlag == "Y")  // 判断该物料是否已扫描完成
                {
                    Message = "Y_EXISTMATER";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sOKFlag = OrderBll.JudgeObjectExist(It.No, sLogin, "", sComp, "30-2-0");
                if (sOKFlag == "N")   // 判断这个序列号是否有要维修的记录，或者已维修完成
                {
                    Message = "N_NOTRepair";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "30-2-6" || It.Flag == "30-2-8")
            {  // 维修：提交完成
                sOKFlag = OrderBll.JudgeObjectExist(It.No, "", "", sComp, "30-2-2");
                if (sOKFlag == "Y")  // 判断这个维修单是否完成了
                {
                    Message = "Y_ISOVER";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                sOKFlag = OrderBll.JudgeObjectExist(It.No, "", "", sComp, "30-2-5");
                if (sOKFlag != "Y")  // 判断这个维修单有没有填写不良原因
                {
                    Message = "N_BLYY";
                    return JsonConvert.SerializeObject(new { Msg = Message, RNo = sOKFlag });
                }
            }
            else if (It.Flag == "30-2-7")//维修： 维修方式记录物料
            {
                // 判断输入的物理是否存在; N 表示物料不存在，存在就把物料描述返回
                sOKFlag = OrderBll.JudgeObjectExist(sMNo, "", "", sComp, "40-1");
                if (sOKFlag == "N")
                {
                    Message = "NO_Mater";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                else
                {
                    sD = sOKFlag;//获取物料描述
                    sRNo = sOKFlag;//将物料描述返回前端
                }
            }




            sOKFlag = OrderBll.OPWXInfo(It.No, It.Name, It.Item, sMNo, It.MName, It.A, It.B, It.C, sD, It.E, It.F, It.G, It.H, It.I, It.J, sComp, sLogin, It.Remark, It.Flag);
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });

            return Result;
        }
        #endregion

        #region 维修界面：根据不良原因代码返回描述
        public void GetDefectsCauseByNo(string Params, string No, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            DataTable dt = new DataTable();

            HttpContext context = HttpContext.Current;
            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }

            dt = OrderBll.GetOrderInfo(No, "", "", "", "", "", "", "", "", "", "", "", 200, 1, sMan, sComp, Flag);   //111-16-1：现象； 111-18：原因；
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "获取不到不良信息";
            }

            string json = JsonConvert.SerializeObject(dt);

            Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });

            context.Response.Write(Result);
        }
        #endregion


        #region 入库申请，扫描入库仓位，显示相关信息
        public void GetInStockWNo(string Params, string sCNo, string sCItem, string sCMNo, string sCWO, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string json = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            DataTable dt = new DataTable();

            HttpContext context = HttpContext.Current;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }

            sOKFlag = OrderBll.JudgeObjectExist(sCNo, "", "", sComp, "32-1");
            if (sOKFlag == "N")  // // 判断入库仓位是否存在 
            {
                Message = "N_EXIST";
                Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                context.Response.Write(Result);
                return;
            }

            dt = OrderBll.GetInStockInfo(sCNo, "", "", "", "", "", "", "", "", "", "", "", 50000, 1, sMan, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                sRNo = dt.Rows[0]["WName"].ToString();  // 作业单元描述 
            }
            else
            {
                Message = "Error";
            }

            json = JsonConvert.SerializeObject(dt);

            Result = JsonConvert.SerializeObject(new { Msg = Message, json = json, Name = sRNo });
            context.Response.Write(Result);

            // return Result;

        }
        #endregion


        #region 入库申请：扫描/输入序列号，检验批次，工单，获取需要入库的信息
        public void ScanBatchNo(string Params, string sCNo, string sCItem, string sCMNo, string sKind, string sActFlag, string Flag)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sNo = string.Empty;
            string sOKFlag = string.Empty;
            string sComp = string.Empty;
            string sFlag = string.Empty;
            string json = string.Empty;
            string sRNo = string.Empty;
            string sInNo = string.Empty;
            string sSt = string.Empty;


            DataTable dt = new DataTable();

            HttpContext context = HttpContext.Current;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }


            var AnonymousUser = new
            {
                No = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            sInNo = It.A; // 入库单号
            if (string.IsNullOrEmpty(sInNo))
            {   // 说明没有入库批次

                // 看看传入的扫描批次，是否有未提交的入库批次，如果有，使用这个入库批次继续添加其他的入库序列号
                sOKFlag = OrderBll.JudgeObjectExist(sCNo, sMan, It.B, sComp, "32-1-1");
                if (string.IsNullOrEmpty(sOKFlag))
                {
                    sInNo = CreateInStockNo();
                }
                else
                {
                    sInNo = sOKFlag;
                }
            }

            // 判断工单是否暂停了，如果是不能操作了 
            sOKFlag = OrderBll.JudgeObjectExist(sCNo, "", It.B, sComp, "32-WO");
            if (sOKFlag == "Y")  // 说明工单已暂停
            {
                Message = "Y_ORDERZT";
                Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                context.Response.Write(Result);
                return;
            }

            sOKFlag = OrderBll.JudgeObjectExist(sCNo, "", It.B, sComp, "32-2-1");
            if (sOKFlag == "N")  // 不存在
            {
                Message = "N_EXISTNO";
                Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
                context.Response.Write(Result);
                return;
            }
            //sOKFlag = OrderBll.JudgeObjectExist(sCNo, "", It.B, sComp, "32-2-2");
            //if (sOKFlag == "N")  // 扫描单号没有可入库的序列号
            //{
            //    Message = "N_EXISTSN";
            //    Result = JsonConvert.SerializeObject(new { Msg = Message, json = json });
            //    context.Response.Write(Result);
            //    return;
            //}



            // { CNo: sSNo, Item: "", MNo: "", A: sInNo, B: sWay, C: sWNo, D: sWName, E: sType, F: sDate, G: "", H: "", Flag: Flag };
            // 如果还没有入库信息，先插入一笔入库信息 
            sOKFlag = OrderBll.OPInStockInfo(sCNo, sManName, "", "", "", sInNo, It.B, It.C, It.D, It.E, It.F, "", "", "", sComp, sMan, "", Flag);
            if (sOKFlag.Length <= 3)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error";
                Result = JsonConvert.SerializeObject(new { Msg = Message, json = json, BNo = sInNo });
                context.Response.Write(Result);
                return;
            }

            // 获取入库信息
            dt = OrderBll.GetInStockInfo(sCNo, sInNo, "", "", "", "", "", "", "", "", "", "", 50000, 1, sMan, sComp, Flag);
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                sSt = dt.Rows[0]["Status"].ToString();
            }
            else
            {
                Message = "Error";
            }

            json = JsonConvert.SerializeObject(dt);

            Result = JsonConvert.SerializeObject(new { Msg = Message, json = json, BZBatch = sRNo, Status = sSt });
            context.Response.Write(Result);

            // return Result;

        }
        #endregion


        #region 入库管理，把数据推送给K3系统
        public string ToK3StockInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            Message = GetInStockDataInfo(Params);

            Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });

            return Result;
        }
        #endregion


        #region 维修：各种维修操作
        public string OPInStockInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;

            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            if (It.Flag == "32-5") //删除入库的序列号：创建状态的
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.Item, It.No, It.A, sComp, "32-5-1");
                if (sOKFlag == "N")  // =N 表示没有创建状态下序列号了
                {
                    Message = "N_EXISTSTA";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "32-6")
            {  // 提交申请单时，判断这个申请单是否已入库了
                sOKFlag = OrderBll.JudgeObjectExist(It.A, "", "", sComp, "32-6-1");
                if (sOKFlag == "Y")  // 表示已入库
                {
                    Message = "Y_ISInStock";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }


            // { CNo: sSNo, Item: "", MNo: "", A: sInNo, B: sWay, C: sWNo, D: sWName, E: sType, F: sDate, G: "", H: "", Flag: Flag };
            sOKFlag = OrderBll.OPInStockInfo(It.No, "", It.Item, "", "", It.A, "", "", "", "", "", "", "", "", sComp, sLogin, "", It.Flag);
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });

            return Result;
        }
        #endregion



        // 导出标贴信息到EXCEL
        public string PrdPackToExcel(string Params, string sWO)
        {
            string Message = string.Empty;
            string Result = string.Empty;
            string sUserNo = string.Empty;
            string sName = string.Empty;
            string sComp = string.Empty;


            string sReturnFile = "\\ExcelFile\\UDI系统软件包装打印记录表 " + DateTime.Now.ToString("yyyymmddss") + ".xls";
            string sFile = System.AppDomain.CurrentDomain.BaseDirectory + "\\Template\\UDI系统软件包装打印记录表.xls";
            string sExcelFilePath = System.AppDomain.CurrentDomain.BaseDirectory + sReturnFile;
            System.IO.File.Copy(sFile, sExcelFilePath, true);
            Microsoft.Office.Interop.Excel.Application app = new Microsoft.Office.Interop.Excel.Application();
            object missing = Type.Missing;
            Microsoft.Office.Interop.Excel.Workbook workbooks = null;



            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sUserNo = HttpContext.Current.Session["LoginName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            DataTable dt = OrderBll.GetOrderInfo(sWO, "", "", "", "", "", "", "", "", "", "", "", 50000, 1, sUserNo, sComp, "173");

            try
            {
                workbooks = app.Workbooks.Open(sExcelFilePath, missing, missing, missing, "", "", missing, missing, missing, missing, missing, missing, missing, missing, missing);
                //Microsoft.Office.Interop.Excel.Worksheet xlsSheet = (Microsoft.Office.Interop.Excel.Worksheet)workbooks.Worksheets[1];
                //Microsoft.Office.Interop.Excel.Range xlsColumns = (Microsoft.Office.Interop.Excel.Range)xlsSheet.Rows[5, missing];

                app.Cells[3, 2] = dt.Rows[0]["MaterName"].ToString();//序号  // 3,2  :第3行，第 2 列    
                app.Cells[3, 5] = dt.Rows[0]["MaterSpec"].ToString();
                app.Cells[3, 7] = dt.Rows[0]["OrderNo"].ToString();
                app.Cells[3, 9] = dt.Rows[0]["OrderNum"].ToString();

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    app.Cells[6 + i, 1] = i + 1;//序号  // 5,1  :第5行，第 1 列 
                    app.Cells[6 + i, 2] = dt.Rows[i]["BatchNo"].ToString();
                    app.Cells[6 + i, 3] = dt.Rows[i]["BZDOne"].ToString();
                    app.Cells[6 + i, 4] = dt.Rows[i]["BZMOne"].ToString();
                    app.Cells[6 + i, 5] = dt.Rows[i]["BZDTwo"].ToString();
                    app.Cells[6 + i, 6] = dt.Rows[i]["BZMTwo"].ToString();
                    app.Cells[6 + i, 7] = dt.Rows[i]["ZXDOne"].ToString();
                    app.Cells[6 + i, 8] = dt.Rows[i]["ZXMOne"].ToString();
                }

                app.Visible = true;
                app.ActiveWorkbook.Save();
            }
            catch (Exception ex)
            {
            }
            finally
            {
                workbooks.Close(false, missing, missing);
                workbooks = null;
                app.Quit();
                app = null;

                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }


            Result = JsonConvert.SerializeObject(new { sFilePath = ".." + sReturnFile });
            return Result;
        }



        // 导出工单及序列号信息到EXCEL
        public string SerialToExcel(string Params, string sWO)
        {
            string Message = string.Empty;
            string Result = string.Empty;
            string sUserNo = string.Empty;
            string sName = string.Empty;
            string sComp = string.Empty;


            string sReturnFile = "\\ExcelFile\\JF-G-023-R2 生产工单、序列号、灭菌批号发放记录表 " + DateTime.Now.ToString("yyyymmddss") + ".xls";
            string sFile = System.AppDomain.CurrentDomain.BaseDirectory + "\\Template\\JF-G-023-R2 生产工单、序列号、灭菌批号发放记录表.xls";
            string sExcelFilePath = System.AppDomain.CurrentDomain.BaseDirectory + sReturnFile;
            System.IO.File.Copy(sFile, sExcelFilePath, true);
            Microsoft.Office.Interop.Excel.Application app = new Microsoft.Office.Interop.Excel.Application();
            object missing = Type.Missing;
            Microsoft.Office.Interop.Excel.Workbook workbooks = null;



            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sUserNo = HttpContext.Current.Session["LoginName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }

            var AnonymousUser = new
            {
                No = String.Empty,
                Item = String.Empty,
                Name = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                Status = String.Empty,
                BDate = String.Empty,
                EDate = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
            };


            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            DataTable dt = OrderBll.GetOrderInfo(Item.No, Item.Item, "", Item.MNo, "", "", "", "", "", "", "", "", 50000, 1, sUserNo, sComp, "174");

            try
            {
                workbooks = app.Workbooks.Open(sExcelFilePath, missing, missing, missing, "", "", missing, missing, missing, missing, missing, missing, missing, missing, missing);
                //Microsoft.Office.Interop.Excel.Worksheet xlsSheet = (Microsoft.Office.Interop.Excel.Worksheet)workbooks.Worksheets[1];
                //Microsoft.Office.Interop.Excel.Range xlsColumns = (Microsoft.Office.Interop.Excel.Range)xlsSheet.Rows[5, missing];

                app.Cells[3, 3] = dt.Rows[0]["MaterNo"].ToString();//序号  // 3,2  :第3行，第 2 列    
                app.Cells[3, 8] = dt.Rows[0]["MaterName"].ToString();
                app.Cells[3, 13] = dt.Rows[0]["Model"].ToString();

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    //app.Cells[6 + i, 2] = i + 1;//序号  // 5,1  :第5行，第 1 列 
                    app.Cells[6 + i, 2] = dt.Rows[i]["InDate"].ToString();
                    app.Cells[6 + i, 3] = dt.Rows[i]["BOMVer"].ToString();
                    app.Cells[6 + i, 4] = dt.Rows[i]["OrderNo"].ToString();
                    app.Cells[6 + i, 5] = dt.Rows[i]["BegNum"].ToString();
                    app.Cells[6 + i, 7] = dt.Rows[i]["EndNum"].ToString();
                    app.Cells[6 + i, 8] = dt.Rows[i]["MJBatch"].ToString();
                    app.Cells[6 + i, 9] = dt.Rows[i]["OrderNum"].ToString();
                    app.Cells[6 + i, 11] = dt.Rows[i]["FullName"].ToString();

                }

                app.Visible = true;
                app.ActiveWorkbook.Save();
            }
            catch (Exception ex)
            {
            }
            finally
            {
                workbooks.Close(false, missing, missing);
                workbooks = null;
                app.Quit();
                app = null;

                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }


            Result = JsonConvert.SerializeObject(new { sFilePath = ".." + sReturnFile });
            return Result;
        }




        // 导出序列号追溯信息到EXCE：L这个方法在2008 开发环境发布到服务器没有问题，2022发布到服务器有问题，本地没有问题。 240304暂时不用
        public string SerialTracToExcel22(string Params, string sSN)
        {
            string Message = string.Empty;
            string Result = string.Empty;
            string sUserNo = string.Empty;
            string sName = string.Empty;
            string sComp = string.Empty;
            int j = 0;


            var AnonymousUser = new
            {
                sSN = String.Empty,//序列号
                OrderNo = String.Empty,//工单编号
                ProductNo = String.Empty,//产品编码
                ProductName = String.Empty,//产品名称
                ProdcssNo = String.Empty,//工序编号
                ProdcssName = String.Empty,//工序名称
                ProdcssVer = String.Empty,//工序名称
                ActionName = String.Empty,//工序行为
                SNo = String.Empty,//顺序号
                WorkUnit = String.Empty,//作业单元
                Status = String.Empty,//生产状态
                Starter = String.Empty,//开始作业员
                EndOperator = String.Empty,//结束作业员
                startDate = String.Empty,//开始时间
                endDate = String.Empty,//结束时间
            };


            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            string sReturnFile = "\\ExcelFile\\序列号导出模板 " + DateTime.Now.ToString("yyyymmddss") + ".xls";
            string sFile = System.AppDomain.CurrentDomain.BaseDirectory + "\\Template\\序列号导出模板.xls";
            string sExcelFilePath = System.AppDomain.CurrentDomain.BaseDirectory + sReturnFile;
            System.IO.File.Copy(sFile, sExcelFilePath, true);
            Microsoft.Office.Interop.Excel.Application app = new Microsoft.Office.Interop.Excel.Application();
            object missing = Type.Missing;
            Microsoft.Office.Interop.Excel.Workbook workbooks = null;



            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sUserNo = HttpContext.Current.Session["LoginName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }


            DataTable dt1 = OrderBll.GetPRDInfo(Item.sSN, Item.ProdcssNo, "", "", "", "", "", "", "", "", "", "", 50000, 1, "", sComp, "28-6"); // 追溯物料
            DataTable dt2 = OrderBll.GetPRDInfo(Item.sSN, Item.ProdcssNo, "", "", "", "", "", "", "", "", "", "", 50000, 1, "", sComp, "28-7"); // 装箱物料
            DataTable dt3 = OrderBll.GetPRDInfo(Item.sSN, Item.ProdcssNo, "", "", "", "", "", "", "", "", "", "", 50000, 1, "", sComp, "28-8-1"); // 检验批次
            DataTable dt4 = OrderBll.GetOrderInfo(Item.sSN, Item.ProdcssNo, "", "", "", "", "", "", "", "", "", "", 50000, 1, "", sComp, "111-21"); // 测试项
            DataTable dt5 = OrderBll.GetOrderInfo(Item.sSN, Item.ProdcssNo + Item.ProdcssVer, "", "", "", "", "", "", "", "", "", "", 50000, 1, "", sComp, "111-14"); // 设备
            DataTable dt6 = OrderBll.GetPRDInfo(Item.sSN, Item.ProdcssNo, "", "", "", "", "", "", "", "", "", "", 50000, 1, "", sComp, "28-16"); // 质控品
            DataTable dt7 = OrderBll.GetOrderInfo(Item.sSN, Item.ProdcssNo, "", "", "", "", "", "", "", "", "", "", 50000, 1, "", sComp, "111-22"); // 不良信息



            try
            {
                workbooks = app.Workbooks.Open(sExcelFilePath, missing, missing, missing, "", "", missing, missing, missing, missing, missing, missing, missing, missing, missing);
                Microsoft.Office.Interop.Excel.Worksheet xlsSheet = (Microsoft.Office.Interop.Excel.Worksheet)workbooks.Worksheets[1];  // 基本信息
                Microsoft.Office.Interop.Excel.Worksheet xlsSheet2 = (Microsoft.Office.Interop.Excel.Worksheet)workbooks.Worksheets[2]; // 追溯物料
                Microsoft.Office.Interop.Excel.Worksheet xlsSheet3 = (Microsoft.Office.Interop.Excel.Worksheet)workbooks.Worksheets[3]; // 装箱物料
                Microsoft.Office.Interop.Excel.Worksheet xlsSheet4 = (Microsoft.Office.Interop.Excel.Worksheet)workbooks.Worksheets[4]; // 检验批次
                Microsoft.Office.Interop.Excel.Worksheet xlsSheet5 = (Microsoft.Office.Interop.Excel.Worksheet)workbooks.Worksheets[5]; // 不良信息
                Microsoft.Office.Interop.Excel.Worksheet xlsSheet6 = (Microsoft.Office.Interop.Excel.Worksheet)workbooks.Worksheets[6]; // 设备
                Microsoft.Office.Interop.Excel.Worksheet xlsSheet7 = (Microsoft.Office.Interop.Excel.Worksheet)workbooks.Worksheets[7]; // 质控品
                Microsoft.Office.Interop.Excel.Worksheet xlsSheet8 = (Microsoft.Office.Interop.Excel.Worksheet)workbooks.Worksheets[8]; // 测试项
                // Microsoft.Office.Interop.Excel.Range xlsColumns = (Microsoft.Office.Interop.Excel.Range)xlsSheet.Rows[5, missing];

                xlsSheet.Cells[3, 3] = Item.sSN;//序列号  // 3,3  :第3行，第 3 列    导出多个sheet   
                xlsSheet.Cells[3, 5] = Item.OrderNo;


                // 序列号基本信息
                xlsSheet.Cells[3, 3] = Item.sSN;//序列号  // 3,3  :第3行，第 3 列    
                xlsSheet.Cells[3, 5] = Item.OrderNo;
                xlsSheet.Cells[3, 7] = Item.ProductNo;
                xlsSheet.Cells[3, 9] = Item.ProductName;
                xlsSheet.Cells[3, 11] = Item.ProdcssName;
                xlsSheet.Cells[4, 3] = Item.WorkUnit;
                xlsSheet.Cells[4, 5] = Item.ActionName;
                xlsSheet.Cells[4, 7] = Item.SNo;
                xlsSheet.Cells[4, 9] = Item.Starter;
                xlsSheet.Cells[4, 11] = Item.startDate;
                xlsSheet.Cells[5, 3] = Item.Status;
                xlsSheet.Cells[5, 9] = Item.EndOperator;
                xlsSheet.Cells[5, 11] = Item.endDate;

                // 追溯物料 
                for (int i = 0; i < dt1.Rows.Count; i++)
                {
                    xlsSheet2.Cells[3 + i, 1] = i + 1;//序号  // 5,1  :第5行，第 1 列 
                    xlsSheet2.Cells[3 + i, 2] = dt1.Rows[i]["MaterNo"].ToString();
                    xlsSheet2.Cells[3 + i, 3] = dt1.Rows[i]["MaterName"].ToString();
                    xlsSheet2.Cells[3 + i, 4] = dt1.Rows[i]["MBatch"].ToString();
                    xlsSheet2.Cells[3 + i, 5] = dt1.Rows[i]["UseNum"].ToString();
                    xlsSheet2.Cells[3 + i, 6] = dt1.Rows[i]["OverNum"].ToString();
                    xlsSheet2.Cells[3 + i, 7] = dt1.Rows[i]["AMFlag"].ToString();

                    j = i;
                }

                //app.Cells[8+j + 1, 6] = "以下空白";
                //j = 0;

                // 装箱物料
                for (int i = 0; i < dt2.Rows.Count; i++)
                {
                    xlsSheet3.Cells[3 + i, 1] = i + 1;//序号  // 5,1  :第5行，第 1 列 
                    xlsSheet3.Cells[3 + i, 2] = dt2.Rows[i]["MaterNo"].ToString();
                    xlsSheet3.Cells[3 + i, 3] = dt2.Rows[i]["MaterName"].ToString();
                    xlsSheet3.Cells[3 + i, 4] = dt2.Rows[i]["UseNum"].ToString();
                    xlsSheet3.Cells[3 + i, 5] = dt2.Rows[i]["OverNum"].ToString();

                    j = i;
                }
                // app.Cells[22 + j + 1, 6] = "以下空白";
                // j = 0;

                // 检验批次
                for (int i = 0; i < dt3.Rows.Count; i++)
                {
                    xlsSheet4.Cells[3 + i, 1] = i + 1;//序号  // 5,1  :第5行，第 1 列 
                    xlsSheet4.Cells[3 + i, 2] = dt3.Rows[i]["BatchNo"].ToString();
                    xlsSheet4.Cells[3 + i, 3] = dt3.Rows[i]["MaterBatchNo"].ToString();
                    xlsSheet4.Cells[3 + i, 4] = dt3.Rows[i]["PDate"].ToString();
                    xlsSheet4.Cells[3 + i, 5] = dt3.Rows[i]["Status"].ToString();

                    j = i;
                }
                // app.Cells[34 + j + 1, 6] = "以下空白";
                // j = 0;

                // 不良信息
                for (int i = 0; i < dt7.Rows.Count; i++)
                {
                    xlsSheet5.Cells[3 + i, 1] = i + 1;//序号  // 5,1  :第5行，第 1 列 
                    xlsSheet5.Cells[3 + i, 2] = dt7.Rows[i]["PECode"].ToString();
                    xlsSheet5.Cells[3 + i, 3] = dt7.Rows[i]["BatchNo"].ToString();
                    xlsSheet5.Cells[3 + i, 4] = dt7.Rows[i]["OrderNo"].ToString();
                    xlsSheet5.Cells[3 + i, 5] = dt7.Rows[i]["ProcedureNo"].ToString();
                    xlsSheet5.Cells[3 + i, 6] = dt7.Rows[i]["ProcedureName"].ToString();
                    xlsSheet5.Cells[3 + i, 7] = dt7.Rows[i]["InDate"].ToString();

                    j = i;
                }
                //app.Cells[46 + j + 1, 6] = "以下空白";
                // j = 0;


                // 设备
                for (int i = 0; i < dt5.Rows.Count; i++)
                {
                    xlsSheet6.Cells[3 + i, 1] = i + 1;//序号  // 5,1  :第5行，第 1 列 
                    xlsSheet6.Cells[3 + i, 2] = dt5.Rows[i]["DeviceName"].ToString();
                    xlsSheet6.Cells[3 + i, 3] = dt5.Rows[i]["MaterNo"].ToString();
                    xlsSheet6.Cells[3 + i, 4] = dt5.Rows[i]["DeviceNo"].ToString();
                    xlsSheet6.Cells[3 + i, 5] = dt5.Rows[i]["TNo"].ToString();
                    xlsSheet6.Cells[3 + i, 6] = dt5.Rows[i]["UseDate"].ToString();

                    j = i;
                }
                //app.Cells[46 + j + 1, 6] = "以下空白";
                // j = 0;

                // 质控品
                for (int i = 0; i < dt6.Rows.Count; i++)
                {
                    xlsSheet7.Cells[3 + i, 1] = i + 1;//序号  // 5,1  :第5行，第 1 列 
                    xlsSheet7.Cells[3 + i, 2] = dt6.Rows[i]["MaterNo"].ToString();
                    xlsSheet7.Cells[3 + i, 3] = dt6.Rows[i]["QualityName"].ToString();
                    xlsSheet7.Cells[3 + i, 4] = dt6.Rows[i]["LotNo"].ToString();

                    j = i;
                }
                //app.Cells[46 + j + 1, 6] = "以下空白";
                // j = 0;

                // 测试项
                for (int i = 0; i < dt4.Rows.Count; i++)
                {
                    xlsSheet8.Cells[3 + i, 1] = i + 1;//序号  // 5,1  :第5行，第 1 列 
                    xlsSheet8.Cells[3 + i, 2] = dt4.Rows[i]["NameCH"].ToString();
                    xlsSheet8.Cells[3 + i, 3] = dt4.Rows[i]["DescCH"].ToString();
                    xlsSheet8.Cells[3 + i, 4] = dt4.Rows[i]["TestValue"].ToString();
                    xlsSheet8.Cells[3 + i, 5] = dt4.Rows[i]["TestResult"].ToString();
                    xlsSheet8.Cells[3 + i, 6] = dt4.Rows[i]["InDate2"].ToString();

                    j = i;
                }
                //app.Cells[46 + j + 1, 6] = "以下空白";
                // j = 0;

                app.Visible = true;
                app.ActiveWorkbook.Save();
            }
            catch (Exception ex)
            {
            }
            finally
            {
                workbooks.Close(false, missing, missing);
                workbooks = null;
                app.Quit();
                app = null;

                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }


            Result = JsonConvert.SerializeObject(new { sFilePath = ".." + sReturnFile });
            return Result;
        }



        // 导出序列号追溯信息到EXCEL：这个方法在2008 开发环境发布到服务器没有问题，2022发布到服务器有问题，本地没有问题。 240304暂时不用
        public string SerialTracToExcel2(string Params, string sSN)
        {
            string Message = string.Empty;
            string Result = string.Empty;
            string sUserNo = string.Empty;
            string sName = string.Empty;
            string sComp = string.Empty;
            int j = 0;


            string sReturnFile = "\\ExcelFile\\序列号导出模板2 " + DateTime.Now.ToString("yyyymmddss") + ".xls";
            string sFile = System.AppDomain.CurrentDomain.BaseDirectory + "\\Template\\序列号导出模板2.xls";
            string sExcelFilePath = System.AppDomain.CurrentDomain.BaseDirectory + sReturnFile;
            System.IO.File.Copy(sFile, sExcelFilePath, true);
            Microsoft.Office.Interop.Excel.Application app = new Microsoft.Office.Interop.Excel.Application();
            object missing = Type.Missing;
            Microsoft.Office.Interop.Excel.Workbook workbooks = null;



            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sUserNo = HttpContext.Current.Session["LoginName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }


            var AnonymousUser = new
            {
                sSN = String.Empty,//序列号
                OrderNo = String.Empty,//工单编号
                ProductNo = String.Empty,//产品编码
                ProductName = String.Empty,//产品名称
                ProdcssNo = String.Empty,//工序编号
                ProdcssName = String.Empty,//工序名称
                ProdcssVer = String.Empty,//工序名称
                ActionName = String.Empty,//工序行为
                SNo = String.Empty,//顺序号
                WorkUnit = String.Empty,//作业单元
                Status = String.Empty,//生产状态
                Starter = String.Empty,//开始作业员
                EndOperator = String.Empty,//结束作业员
                startDate = String.Empty,//开始时间
                endDate = String.Empty,//结束时间
            };


            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            DataSet ds = OrderBll.GetSerialTracInfo(sSN, "", "", "", "", "", sUserNo, sComp, "175");
            DataTable dtSN = ds.Tables[0]; // 序列号基本信息
            DataTable dtPRD = ds.Tables[1]; // 生产信息
            DataTable dtPint = ds.Tables[2]; // 标贴打印记录
            DataTable dtScan = OrderBll.GetPRDInfo(Item.sSN, Item.ProdcssNo, "", "", "", "", "", "", "", "", "", "", 50000, 1, "", sComp, "28-6"); // 追溯物料
            DataTable dtZX = ds.Tables[4]; // 装箱信息

            try
            {
                workbooks = app.Workbooks.Open(sExcelFilePath, missing, missing, missing, "", "", missing, missing, missing, missing, missing, missing, missing, missing, missing);
                //Microsoft.Office.Interop.Excel.Worksheet xlsSheet = (Microsoft.Office.Interop.Excel.Worksheet)workbooks.Worksheets[1];
                //Microsoft.Office.Interop.Excel.Range xlsColumns = (Microsoft.Office.Interop.Excel.Range)xlsSheet.Rows[5, missing];


                // 序列号基本信息
                app.Cells[3, 3] = Item.sSN;//序列号  // 3,3  :第3行，第 3 列    
                app.Cells[3, 5] = Item.OrderNo;
                app.Cells[3, 7] = Item.ProductNo;
                app.Cells[3, 9] = Item.ProductName;
                app.Cells[3, 11] = Item.ProdcssName;
                app.Cells[4, 3] = Item.WorkUnit;
                app.Cells[4, 5] = Item.ActionName;
                app.Cells[4, 7] = Item.SNo;
                app.Cells[4, 9] = Item.Starter;
                app.Cells[4, 11] = Item.startDate;
                app.Cells[5, 3] = Item.Status;
                app.Cells[5, 9] = Item.EndOperator;
                app.Cells[5, 11] = Item.endDate;


                // 生产信息 
                for (int i = 0; i < dtPRD.Rows.Count; i++)
                {
                    app.Cells[8 + i, 2] = i + 1;//序号  // 5,1  :第5行，第 1 列 
                    app.Cells[8 + i, 3] = dtPRD.Rows[i]["ProcedureName"].ToString();
                    app.Cells[8 + i, 4] = dtPRD.Rows[i]["OrderNo"].ToString();
                    app.Cells[8 + i, 5] = dtPRD.Rows[i]["ProductNo"].ToString();
                    app.Cells[8 + i, 6] = dtPRD.Rows[i]["ProductName"].ToString();
                    app.Cells[8 + i, 7] = dtPRD.Rows[i]["FullName"].ToString();
                    app.Cells[8 + i, 8] = dtPRD.Rows[i]["UnitNo"].ToString();
                    app.Cells[8 + i, 9] = dtPRD.Rows[i]["WName"].ToString();
                    app.Cells[8 + i, 10] = dtPRD.Rows[i]["StartDate"].ToString();
                    app.Cells[8 + i, 11] = dtPRD.Rows[i]["EndDate"].ToString();

                    j = i;
                }

                //app.Cells[8+j + 1, 6] = "以下空白";
                //j = 0;

                // 标贴打印记录
                for (int i = 0; i < dtPint.Rows.Count; i++)
                {
                    app.Cells[22 + i, 2] = i + 1;//序号  // 5,1  :第5行，第 1 列 
                    app.Cells[22 + i, 3] = dtPint.Rows[i]["ProcedureName"].ToString();
                    app.Cells[22 + i, 4] = dtPint.Rows[i]["LabelNo"].ToString();
                    app.Cells[22 + i, 5] = dtPint.Rows[i]["LabelName"].ToString();
                    app.Cells[22 + i, 6] = dtPint.Rows[i]["LabelVer"].ToString();
                    app.Cells[22 + i, 7] = dtPint.Rows[i]["TPNo"].ToString();
                    app.Cells[22 + i, 8] = dtPint.Rows[i]["TemplateName"].ToString();
                    app.Cells[22 + i, 9] = dtPint.Rows[i]["TPVer"].ToString();
                    app.Cells[22 + i, 10] = dtPint.Rows[i]["PrintNum"].ToString();
                    app.Cells[22 + i, 11] = dtPint.Rows[i]["InDate"].ToString();

                    j = i;
                }
                // app.Cells[22 + j + 1, 6] = "以下空白";
                // j = 0;

                // 物料扫描信息
                for (int i = 0; i < dtScan.Rows.Count; i++)
                {
                    app.Cells[34 + i, 2] = i + 1;//序号  // 5,1  :第5行，第 1 列 
                    app.Cells[34 + i, 3] = dtScan.Rows[i]["ProcedureName"].ToString();
                    app.Cells[34 + i, 6] = dtScan.Rows[i]["MaterNo"].ToString();
                    app.Cells[34 + i, 7] = dtScan.Rows[i]["MaterName"].ToString();
                    //app.Cells[34 + i, 11] = dtScan.Rows[i]["InDate"].ToString();

                    j = i;
                }
                // app.Cells[34 + j + 1, 6] = "以下空白";
                // j = 0;

                // 装箱信息
                for (int i = 0; i < dtZX.Rows.Count; i++)
                {
                    app.Cells[46 + i, 2] = i + 1;//序号  // 5,1  :第5行，第 1 列 
                    app.Cells[46 + i, 3] = dtZX.Rows[i]["ProcedureName"].ToString();
                    app.Cells[46 + i, 4] = dtZX.Rows[i]["BatchNo"].ToString();
                    app.Cells[46 + i, 5] = dtZX.Rows[i]["MaterNo"].ToString();
                    app.Cells[46 + i, 10] = dtZX.Rows[i]["InDate"].ToString();

                    j = i;
                }
                //app.Cells[46 + j + 1, 6] = "以下空白";
                // j = 0;

                app.Visible = true;
                app.ActiveWorkbook.Save();
            }
            catch (Exception ex)
            {
            }
            finally
            {
                workbooks.Close(false, missing, missing);
                workbooks = null;
                app.Quit();
                app = null;

                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }


            Result = JsonConvert.SerializeObject(new { sFilePath = ".." + sReturnFile });
            return Result;
        }


        // 导出EXCEL，多个sheet
        public string SerialTracToExcel(string Params, string sSN)
        {
            string Message = string.Empty;
            string Result = string.Empty;
            string sUserNo = string.Empty;
            string sName = string.Empty;
            string sComp = string.Empty;
            int j = 0;


            var AnonymousUser = new
            {
                sSN = String.Empty,//序列号
                OrderNo = String.Empty,//工单编号
                ProductNo = String.Empty,//产品编码
                ProductName = String.Empty,//产品名称
                ProdcssNo = String.Empty,//工序编号
                ProdcssName = String.Empty,//工序名称
                ProdcssVer = String.Empty,//工序名称
                ActionName = String.Empty,//工序行为
                SNo = String.Empty,//顺序号
                WorkUnit = String.Empty,//作业单元
                Status = String.Empty,//生产状态
                Starter = String.Empty,//开始作业员
                EndOperator = String.Empty,//结束作业员
                startDate = String.Empty,//开始时间
                endDate = String.Empty,//结束时间
            };


            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sUserNo = HttpContext.Current.Session["LoginName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }


            string path = "\\ExcelFile";
            string fileName = "\\序列号导出模板 " + DateTime.Now.ToString("yyyymmddss") + ".xls";
            string filePath = System.AppDomain.CurrentDomain.BaseDirectory + path;
            if (!Directory.Exists(filePath))
            {
                Directory.CreateDirectory(filePath);
            }

            filePath = filePath + fileName;


            DataTable dt1 = OrderBll.GetPRDInfo(Item.sSN, Item.ProdcssNo, "", "", "", "", "", "", "", Item.OrderNo, "", "", 50000, 1, "", sComp, "28-6"); // 追溯物料
            DataTable dt2 = OrderBll.GetPRDInfo(Item.sSN, Item.ProdcssNo, "", "", "", "", "", "", "", Item.OrderNo, "", "", 50000, 1, "", sComp, "28-7"); // 装箱物料
            DataTable dt3 = OrderBll.GetPRDInfo(Item.sSN, Item.ProdcssNo, "", "", "", "", "", "", "", "", "", "", 50000, 1, "", sComp, "28-8-1"); // 检验批次
            DataTable dt4 = OrderBll.GetOrderInfo(Item.sSN, Item.ProdcssNo, "", "", "", "", "", "", "", Item.OrderNo, "", "", 50000, 1, "", sComp, "111-21"); // 测试项
            DataTable dt5 = OrderBll.GetOrderInfo(Item.sSN, Item.ProdcssNo + Item.ProdcssVer, "", "", "", "", "", "", "", Item.OrderNo, "", "", 50000, 1, "", sComp, "111-14"); // 设备
            DataTable dt6 = OrderBll.GetPRDInfo(Item.sSN, Item.ProdcssNo, "", "", "", "", "", "", "", Item.OrderNo, "", "", 50000, 1, "", sComp, "28-16"); // 质控品
            DataTable dt7 = OrderBll.GetOrderInfo(Item.sSN, Item.ProdcssNo, "", "", "", "", "", "", "", Item.OrderNo, "", "", 50000, 1, "", sComp, "111-22");//不良


            // 加载现有模板
            using (FileStream file = new FileStream(filePath, FileMode.Create, FileAccess.Write))
            {
                IWorkbook workbook = new HSSFWorkbook();
                // 获取模板中的某个 sheet，例如第一个 sheet

                ISheet sheet1 = workbook.CreateSheet("基础信息");// 基本信息
                ISheet sheet2 = workbook.CreateSheet("追溯物料");// 追溯物料
                ISheet sheet3 = workbook.CreateSheet("装箱物料");// 装箱物料
                ISheet sheet4 = workbook.CreateSheet("检验批次");// 检验批次
                ISheet sheet5 = workbook.CreateSheet("不良信息");// 不良信息
                ISheet sheet6 = workbook.CreateSheet("设备");// 设备
                ISheet sheet7 = workbook.CreateSheet("质控品");// 质控品
                ISheet sheet8 = workbook.CreateSheet("测试项");// 测试项

                // 创建一个单元格样式
                ICellStyle cellStyle = workbook.CreateCellStyle();
                // 设置水平居中对齐
                cellStyle.VerticalAlignment = VerticalAlignment.Center;

                cellStyle.BorderTop = BorderStyle.Medium;
                cellStyle.BorderRight = BorderStyle.Medium;
                cellStyle.BorderBottom = BorderStyle.Medium;
                cellStyle.BorderLeft = BorderStyle.Medium;


                //设置列宽
                sheet1.SetColumnWidth(1, 30 * 256);
                sheet1.SetColumnWidth(3, 30 * 256);
                sheet1.SetColumnWidth(5, 30 * 256);
                sheet1.SetColumnWidth(7, 30 * 256);
                sheet1.SetColumnWidth(9, 30 * 256);

                sheet1.SetColumnWidth(2, 15 * 256);
                sheet1.SetColumnWidth(4, 15 * 256);
                sheet1.SetColumnWidth(6, 15 * 256);
                sheet1.SetColumnWidth(8, 15 * 256);
                sheet1.SetColumnWidth(10, 15 * 256);


                IRow row = sheet1.CreateRow(0);
                ICell cell = null;

                cell = row.CreateCell(0);
                cell.SetCellValue("序列号");
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(1);
                cell.SetCellValue(Item.sSN);
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(2);
                cell.SetCellValue("工单号");
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(3);
                cell.SetCellValue(Item.OrderNo);
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(4);
                cell.SetCellValue("产品编码");
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(5);
                cell.SetCellValue(Item.ProductNo);
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(6);
                cell.SetCellValue("产品名称");
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(7);
                cell.SetCellValue(Item.ProductName);
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(8);
                cell.SetCellValue("工序名称");
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(9);
                cell.SetCellValue(Item.ProdcssName);
                cell.CellStyle = cellStyle;
                row.HeightInPoints = 25;


                row = sheet1.CreateRow(1);
                cell = row.CreateCell(0);
                cell.SetCellValue("作业单元");
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(1);
                cell.SetCellValue(Item.WorkUnit);
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(2);
                cell.SetCellValue("行为名称");
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(3);
                cell.SetCellValue(Item.ActionName);
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(4);
                cell.SetCellValue("顺序号");
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(5);
                cell.SetCellValue(Item.SNo);
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(6);
                cell.SetCellValue("开始作业员");
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(7);
                cell.SetCellValue(Item.Starter);
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(8);
                cell.SetCellValue("开始时间");
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(9);
                cell.SetCellValue(Item.startDate);
                cell.CellStyle = cellStyle;
                row.HeightInPoints = 25;

                row = sheet1.CreateRow(2);
                cell = row.CreateCell(0);
                cell.SetCellValue("生产状态");
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(1);
                cell.SetCellValue(Item.Status);
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(2);
                cell.SetCellValue("");
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(3);
                cell.SetCellValue("");
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(4);
                cell.SetCellValue("");
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(5);
                cell.SetCellValue("");
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(6);
                cell.SetCellValue("结束作业员");
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(7);
                cell.SetCellValue(Item.EndOperator);
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(8);
                cell.SetCellValue("完工时间");
                cell.CellStyle = cellStyle;

                cell = row.CreateCell(9);
                cell.SetCellValue(Item.endDate);
                cell.CellStyle = cellStyle;

                row.HeightInPoints = 25;



                //追溯物料
                row = sheet2.CreateRow(0);

                cell = row.CreateCell(0);
                cell.SetCellValue("序号");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(1);
                cell.SetCellValue("物料编码");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(2);
                cell.SetCellValue("物料描述");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(3);
                cell.SetCellValue("批/序号");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(4);
                cell.SetCellValue("需求数");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(5);
                cell.SetCellValue("已用数");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(6);
                cell.SetCellValue("替代料");
                cell.CellStyle = cellStyle;
                row.HeightInPoints = 25;

                for (int i = 0; i < dt1.Rows.Count; i++)
                {
                    row = sheet2.CreateRow(i + 1);

                    cell = row.CreateCell(0);
                    cell.SetCellValue(i + 1);
                    cell.CellStyle = cellStyle;

                    cell = row.CreateCell(1);
                    cell.SetCellValue(dt1.Rows[i]["MaterNo"].ToString());
                    cell.CellStyle = cellStyle;

                    cell = row.CreateCell(2);
                    cell.SetCellValue(dt1.Rows[i]["MaterName"].ToString());
                    cell.CellStyle = cellStyle;

                    cell = row.CreateCell(3);
                    cell.SetCellValue(dt1.Rows[i]["MBatch"].ToString());
                    cell.CellStyle = cellStyle;

                    cell = row.CreateCell(4);
                    cell.SetCellValue(dt1.Rows[i]["UseNum"].ToString());
                    cell.CellStyle = cellStyle;

                    cell = row.CreateCell(5);
                    cell.SetCellValue(dt1.Rows[i]["OverNum"].ToString());
                    cell.CellStyle = cellStyle;

                    cell = row.CreateCell(6);
                    cell.SetCellValue(dt1.Rows[i]["AMFlag"].ToString());
                    cell.CellStyle = cellStyle;
                    row.HeightInPoints = 25;
                }


                sheet2.SetColumnWidth(1, 15 * 256);
                sheet2.SetColumnWidth(2, 30 * 256);
                sheet2.SetColumnWidth(3, 30 * 256);
                sheet2.SetColumnWidth(4, 30 * 256);
                sheet2.SetColumnWidth(5, 30 * 256);
                sheet2.SetColumnWidth(6, 30 * 256);
                sheet2.SetColumnWidth(7, 30 * 256);



                //装箱物料
                row = sheet3.CreateRow(0);

                cell = row.CreateCell(0);
                cell.SetCellValue("序号");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(1);
                cell.SetCellValue("物料编码");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(2);
                cell.SetCellValue("物料描述");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(3);
                cell.SetCellValue("装箱数");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(4);
                cell.SetCellValue("已装数");
                cell.CellStyle = cellStyle;
                row.HeightInPoints = 25;

                for (int i = 0; i < dt2.Rows.Count; i++)
                {
                    row = sheet3.CreateRow(i + 1);

                    cell = row.CreateCell(0);
                    cell.SetCellValue(i + 1);
                    cell.CellStyle = cellStyle;

                    cell = row.CreateCell(1);
                    cell.SetCellValue(dt2.Rows[i]["MaterNo"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(2);
                    cell.SetCellValue(dt2.Rows[i]["MaterName"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(3);
                    cell.SetCellValue(dt2.Rows[i]["UseNum"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(4);
                    cell.SetCellValue(dt2.Rows[i]["OverNum"].ToString());
                    cell.CellStyle = cellStyle;
                    row.HeightInPoints = 25;

                }

                sheet3.SetColumnWidth(1, 15 * 256);
                sheet3.SetColumnWidth(2, 30 * 256);
                sheet3.SetColumnWidth(3, 30 * 256);
                sheet3.SetColumnWidth(4, 30 * 256);
                sheet3.SetColumnWidth(5, 30 * 256);


                //检验批次
                row = sheet4.CreateRow(0);

                cell = row.CreateCell(0);
                cell.SetCellValue("序号");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(1);
                cell.SetCellValue("序列号");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(2);
                cell.SetCellValue("上及包装批次");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(3);
                cell.SetCellValue("时间");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(4);
                cell.SetCellValue("是否关联完毕");
                cell.CellStyle = cellStyle;
                row.HeightInPoints = 25;

                for (int i = 0; i < dt3.Rows.Count; i++)
                {
                    row = sheet4.CreateRow(i + 1);

                    cell = row.CreateCell(0);
                    cell.SetCellValue(i + 1);
                    cell.CellStyle = cellStyle;

                    cell = row.CreateCell(1);
                    cell.SetCellValue(dt3.Rows[i]["BatchNo"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(2);
                    cell.SetCellValue(dt3.Rows[i]["MaterBatchNo"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(3);
                    cell.SetCellValue(dt3.Rows[i]["PDate"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(4);
                    cell.SetCellValue(dt3.Rows[i]["Status"].ToString());
                    cell.CellStyle = cellStyle;
                    row.HeightInPoints = 25;
                }


                sheet4.SetColumnWidth(1, 15 * 256);
                sheet4.SetColumnWidth(2, 30 * 256);
                sheet4.SetColumnWidth(3, 30 * 256);
                sheet4.SetColumnWidth(4, 30 * 256);
                sheet4.SetColumnWidth(5, 30 * 256);


                //不良信息
                row = sheet5.CreateRow(0);

                cell = row.CreateCell(0);
                cell.SetCellValue("序号");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(1);
                cell.SetCellValue("异常编号");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(2);
                cell.SetCellValue("序列号");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(3);
                cell.SetCellValue("工单");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(4);
                cell.SetCellValue("工序编号");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(5);
                cell.SetCellValue("工序");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(6);
                cell.SetCellValue("时间");
                cell.CellStyle = cellStyle;
                row.HeightInPoints = 25;

                for (int i = 0; i < dt7.Rows.Count; i++)
                {
                    row = sheet5.CreateRow(i + 1);

                    cell = row.CreateCell(0);
                    cell.SetCellValue(i + 1);
                    cell.CellStyle = cellStyle;

                    cell = row.CreateCell(1);
                    cell.SetCellValue(dt7.Rows[i]["PECode"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(2);
                    cell.SetCellValue(dt7.Rows[i]["BatchNo"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(3);
                    cell.SetCellValue(dt7.Rows[i]["OrderNo"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(4);
                    cell.SetCellValue(dt7.Rows[i]["ProcedureNo"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(5);
                    cell.SetCellValue(dt7.Rows[i]["ProcedureName"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(6);
                    cell.SetCellValue(dt7.Rows[i]["InDate"].ToString());
                    cell.CellStyle = cellStyle;
                    row.HeightInPoints = 25;
                }

                sheet5.SetColumnWidth(1, 15 * 256);
                sheet5.SetColumnWidth(2, 30 * 256);
                sheet5.SetColumnWidth(3, 30 * 256);
                sheet5.SetColumnWidth(4, 30 * 256);
                sheet5.SetColumnWidth(5, 30 * 256);
                sheet5.SetColumnWidth(6, 30 * 256);
                sheet5.SetColumnWidth(7, 30 * 256);



                //设备信息
                row = sheet6.CreateRow(0);

                cell = row.CreateCell(0);
                cell.SetCellValue("序号");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(1);
                cell.SetCellValue("设备名称");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(2);
                cell.SetCellValue("设备分类编码");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(3);
                cell.SetCellValue("设备编码");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(4);
                cell.SetCellValue("点检任务");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(5);
                cell.SetCellValue("校准日期");
                cell.CellStyle = cellStyle;
                row.HeightInPoints = 25;

                for (int i = 0; i < dt5.Rows.Count; i++)
                {
                    row = sheet6.CreateRow(i + 1);

                    cell = row.CreateCell(0);
                    cell.SetCellValue(i + 1);
                    cell.CellStyle = cellStyle;

                    cell = row.CreateCell(1);
                    cell.SetCellValue(dt5.Rows[i]["DeviceName"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(2);
                    cell.SetCellValue(dt5.Rows[i]["MaterNo"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(3);
                    cell.SetCellValue(dt5.Rows[i]["DeviceNo"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(4);
                    cell.SetCellValue(dt5.Rows[i]["TNo"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(5);
                    cell.SetCellValue(dt5.Rows[i]["UseDate"].ToString());
                    cell.CellStyle = cellStyle;
                    row.HeightInPoints = 25;
                }

                sheet6.SetColumnWidth(1, 15 * 256);
                sheet6.SetColumnWidth(2, 30 * 256);
                sheet6.SetColumnWidth(3, 30 * 256);
                sheet6.SetColumnWidth(4, 30 * 256);
                sheet6.SetColumnWidth(5, 30 * 256);
                sheet6.SetColumnWidth(6, 30 * 256);


                //质控品
                row = sheet7.CreateRow(0);

                cell = row.CreateCell(0);
                cell.SetCellValue("序号");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(1);
                cell.SetCellValue("编码");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(2);
                cell.SetCellValue("描述");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(3);
                cell.SetCellValue("批次");
                cell.CellStyle = cellStyle;
                row.HeightInPoints = 25;

                for (int i = 0; i < dt6.Rows.Count; i++)
                {
                    row = sheet7.CreateRow(i + 1);

                    cell = row.CreateCell(0);
                    cell.SetCellValue(i + 1);
                    cell.CellStyle = cellStyle;

                    cell = row.CreateCell(1);
                    cell.SetCellValue(dt6.Rows[i]["MaterNo"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(2);
                    cell.SetCellValue(dt6.Rows[i]["QualityName"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(3);
                    cell.SetCellValue(dt6.Rows[i]["LotNo"].ToString());
                    cell.CellStyle = cellStyle;
                    row.HeightInPoints = 25;
                }

                sheet7.SetColumnWidth(1, 15 * 256);
                sheet7.SetColumnWidth(2, 30 * 256);
                sheet7.SetColumnWidth(3, 30 * 256);
                sheet7.SetColumnWidth(4, 30 * 256);


                //测试信息
                row = sheet8.CreateRow(0);

                cell = row.CreateCell(0);
                cell.SetCellValue("序号");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(1);
                cell.SetCellValue("检验项目");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(2);
                cell.SetCellValue("检验要求");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(3);
                cell.SetCellValue("测试项");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(4);
                cell.SetCellValue("结论");
                cell.CellStyle = cellStyle;
                cell = row.CreateCell(5);
                cell.SetCellValue("测试时间");
                cell.CellStyle = cellStyle;
                row.HeightInPoints = 25;

                for (int i = 0; i < dt4.Rows.Count; i++)
                {
                    row = sheet8.CreateRow(i + 1);

                    cell = row.CreateCell(0);
                    cell.SetCellValue(i + 1);
                    cell.CellStyle = cellStyle;

                    cell = row.CreateCell(1);
                    cell.SetCellValue(dt4.Rows[i]["NameCH"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(2);
                    cell.SetCellValue(dt4.Rows[i]["DescCH"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(3);
                    cell.SetCellValue(dt4.Rows[i]["TestValue"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(4);
                    cell.SetCellValue(dt4.Rows[i]["TestResult"].ToString());
                    cell.CellStyle = cellStyle;
                    cell = row.CreateCell(5);
                    cell.SetCellValue(dt4.Rows[i]["InDate2"].ToString());
                    cell.CellStyle = cellStyle;
                    row.HeightInPoints = 25;
                }

                sheet8.SetColumnWidth(1, 15 * 256);
                sheet8.SetColumnWidth(2, 30 * 256);
                sheet8.SetColumnWidth(3, 30 * 256);
                sheet8.SetColumnWidth(4, 30 * 256);
                sheet8.SetColumnWidth(5, 30 * 256);
                sheet8.SetColumnWidth(6, 30 * 256);

                workbook.Write(file);
            }

            Result = JsonConvert.SerializeObject(new { sFilePath = ".." + path + fileName });
            return Result;
        }


        // 导出异常信息
        public string PrdExceptionToExcel(string Params, string sCNo, string Flag)
        {
            string Message = string.Empty;
            string Result = string.Empty;
            string sUserNo = string.Empty;
            string sComp = string.Empty;
            string sReturnFile = "\\ExcelFile\\ 异常信息 " + sCNo + " " + DateTime.Now.ToString("yyyymmddss") + ".xls";
            string sFile = string.Empty;
            string sOldCustPN = string.Empty;
            string sCustPN = string.Empty;
            string sPF = string.Empty;
            string sBDate = string.Empty;
            string sEDate = string.Empty;
            string sOKFlag = string.Empty;


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
                sUserNo = HttpContext.Current.Session["LoginName"].ToString();
            }
            else
            {
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sComp });
                return Result;
            }


            var AnonymousUser = new
            {
                No = String.Empty,
                Item = String.Empty,
                Name = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                Status = String.Empty,
                BDate = String.Empty,
                EDate = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            string sExcelFilePath = System.AppDomain.CurrentDomain.BaseDirectory + sReturnFile;

            sBDate = Item.BDate;
            sEDate = Item.EDate;

            if (sBDate == "")
            {
                sBDate = "2018-01-01";
            }
            if (sEDate == "")
            {
                sEDate = "2999-12-30";
            }

            DataTable dt = OrderBll.GetOrderInfo(Item.No, Item.Item, Item.Name, Item.MNo, Item.MName, Item.Status, sBDate, sEDate, Item.A, Item.B, Item.C, Item.D, 50000, 1, sUserNo, sComp, Flag);

            try
            {
                //if (dt.Rows.Count > 0)
                //{
                var j = 0;

                HttpResponse response = HttpContext.Current.Response;
                object dataSource = dt;
                int dataCount = dt.Rows.Count;
                IWorkbook workbook = null;
                ISheet sheet = null;
                //DataTable dt = (DataTable)ctrl.DataSource;
                //根据不同路径的文件名，生成不同版本的Excel工作簿
                if (sExcelFilePath.IndexOf(".xlsx") > 0)
                {
                    workbook = new XSSFWorkbook();//新版Excel工作簿
                }
                else if (sExcelFilePath.IndexOf(".xls") > 0)
                {
                    workbook = new HSSFWorkbook();//旧版Excel工作簿
                }

                //为工作簿创建sheet表，并对其进行命名
                sheet = workbook.CreateSheet("sheet1");

                // 数据行样式（带边框）
                ICellStyle dataStyle = workbook.CreateCellStyle();
                IFont dataFont = workbook.CreateFont();
                dataFont.FontHeightInPoints = 9;
                dataStyle.SetFont(dataFont);
                dataStyle.BorderTop = BorderStyle.Thin;
                dataStyle.BorderBottom = BorderStyle.Thin;
                dataStyle.BorderLeft = BorderStyle.Thin;
                dataStyle.BorderRight = BorderStyle.Thin;

                //标题
                string[] excelHeaders = { "不合格处理编号", "型号", "工单号", "序列号", "产品编码", "产品名称", "状态", "部门", "发生环节", "批量数", "不良数量", "不良现象代码", "不良现象名称", "不良现象描述", "不良原因代码", "不良原因名称", "不良原因描述", "不良原因类型", "位号", "产品代号", "作业类型", "物料编码", "物料描述", "录入人", "维修人", "维修时间", "录入时间" };

                // 定义列名数组
                string[] columnNames = {"PECode", "Model", "OrderNo", "BatchNo", "MaterNo","MaterName", "Status", "DeptName", "ProcedureName", "BatchNum","BadNum", "ECode", "EName", "EDesc", "CNo","CName", "CDesc", "CType", "Location", "TechNo","OrderKind", "RepMaterNo", "RepMaterName", "InMan", "WInMan","DealDate", "InDate2"};

                IRow headerRow = sheet.CreateRow(0);  // 创建第一行，标题

                // 使用循环创建表头单元格
                for (int i = 0; i < excelHeaders.Length; i++)
                {
                    ICell cell = headerRow.CreateCell(i);
                    cell.SetCellValue(excelHeaders[i]);
                    cell.CellStyle = dataStyle;
                    sheet.AutoSizeColumn(i);
                }

                // 填充数据行
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    IRow dataRow = sheet.CreateRow(i + 1);

                    // 使用循环为每列设置值
                    for (int col = 0; col < columnNames.Length; col++)
                    {
                        ICell cell = dataRow.CreateCell(col);
                        cell.SetCellValue(dt.Rows[i][columnNames[col]].ToString()); 
                        cell.CellStyle = dataStyle; // 设置单元格样式
                    }
                }
                
                //把数据写入内存流
                FileStream fileStream = new FileStream(sExcelFilePath, FileMode.Create);
                workbook.Write(fileStream);
                fileStream.Close();
                fileStream.Dispose();

                //}  // end of  (dt.Rows.Count > 0)
            }
            catch (Exception ex)
            {
            }
            finally
            {
                // workbooks.Close(false, missing, missing);
            }


            Result = JsonConvert.SerializeObject(new { sFilePath = ".." + sReturnFile });
            return Result;
        }


        #region 对生产执行信息进行操作-- 获取第三方系统的测试数据
        public string GetThirdTestItem(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            string sEPath = string.Empty;
            string sInOK = string.Empty;
            string sIP = string.Empty;
            string sTUser = string.Empty;
            string sPwd = string.Empty;
            string sReturnFile = string.Empty;
            string sEXEMan = string.Empty;
            string sTValue = string.Empty;
            string sIsPass = string.Empty;
            string sExcelFilePath = string.Empty;
            int iCount = 0;
            string sSyncNo = DateTime.Now.ToString("yyyyMMddHHmmss"); // 产生一个同步编号，表示的数据是一起的
            DataTable dt = new DataTable();


            // 前端数据 { No: sSerial, Name: sPath, Item: "", MNo: "", MName: "", A: sType, B:"", C: sProc, D: sVer, E: "", F: "", Remark: "", Flag: sFlag }
            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                InMan = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sEXEMan = It.InMan;   //20240315: 使用前端传入的作业人员
            sInOK = "Y";  // 初始化能正常插入数据
            // 如果是从EXCEL获取测试值的，则先得到EXCEL的地址 ,,
            if (It.A == "EXCEL文件")
            {
                //作业执行，判断当前工序的工序行为是不是送检抽检合并 
                sOKFlag = OrderBll.JudgeObjectExist(It.MName, "", "", sComp, "43-2");
                if (sOKFlag == "Y")
                {
                    //作业执行，如果设置抽检工序行为则需要判断是否关联完成，只有关联完成后才可操作 测试项、设备
                    sOKFlag = OrderBll.JudgeObjectExist(It.F, "", "", sComp, "42-3");
                    if (sOKFlag == "Y")  // 未关联
                    {
                        Message = "N_NORELEVANCE";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }

                // 判断这个序列号，这个工序是否有测试项  作业执行编号 It.MNo 因为没有多余参数可用就用了It.MNo
                sOKFlag = OrderBll.JudgeObjectExist(It.No, It.MNo, It.Item, sComp, "33-2");
                if (sOKFlag == "Y")  // 表示这个序列号已获取过第三方的测试记录了
                {
                    Message = "Y_TestItem";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }

                //DataTable dtK = OrderBll.GetSysConfigInfo("测试记录EXCEL文档路径", sComp, "1");  // 20240303 不从配置表获取测试EXCEL文件路径，从工序行为获取
                DataTable dtK = OrderBll.GetSysConfigInfo(It.B, sComp, "3");
                if (dtK.Rows.Count > 0)
                {
                    sEPath = dtK.Rows[0]["SysValue"].ToString() + "\\" + It.Item + "\\" + It.Name;  //测试工装的文件路径：   It.Item:工单   It.Name: YG-3B000811_PASS.xls
                    sIP = dtK.Rows[0]["TestIP"].ToString();
                    sTUser = dtK.Rows[0]["TestUser"].ToString();
                    sPwd = dtK.Rows[0]["TestPwd"].ToString();

                    // 从测试工装把测试文件拷贝到服务器，用这个方法，标识访问共享文件夹时，需要输入账号密码
                    using (IdentityScope c = new IdentityScope(sTUser, sIP, sPwd))
                    {
                        sReturnFile = "ExcelTestFile\\" + It.No + DateTime.Now.ToString("yyyymmddss") + ".xlsx";  // 服务器根目录下，拷贝过来的测试文件
                        sExcelFilePath = System.AppDomain.CurrentDomain.BaseDirectory + sReturnFile;
                        System.IO.File.Copy(sEPath, sExcelFilePath, true);
                    }

                    //sEPath = @"E:\wudong\系统开发\02 系统方案\纤草科技\01 客户资料\mkt\30 数据\IP17\MO-2105-0738\9236012100001_PASS_print.xls";
                    // sEPath = @"\\**********\Share\WD\WO1\9236012100001_PASS_print.xls";

                    try
                    {
                        // dt = ExcelToDataTable(sEPath, true, sSyncNo);  // 把EXCEL数据放到DT
                        //  iCount = OrderBll.GetExcelTestInfo(dt, "T_ExcelTestInfo"); // 把dt数据放到正式的数据表

                        dt = ExcelToDataTable(sExcelFilePath, true, sSyncNo);  //  sExcelFilePath   把EXCEL数据放到DT
                        iCount = OrderBll.GetExcelTestInfo(dt, "T_ExcelTestInfo"); // 把dt数据放到正式的数据表


                    }
                    catch (Exception ex)
                    {
                        sInOK = "N";
                        Message = "Not_Error";
                        Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName, BStr = ex.Message });
                        return Result;
                    }
                }
                else
                {
                    sInOK = "N";
                    Message = "Not_Patch";
                    Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                    return Result;
                }

            }


            // OPThirdTestItemInfo(No, Name, Item, MNo, MName, A, B, C, D, E, F,sComp, InMan, Remark, sFlag);
            if (sInOK == "Y")
            { // 表示正确读取数据，则插入对应的测试记录表
                sOKFlag = OrderBll.OPThirdTestItemInfo(It.No, It.Name, "", "", "", It.A, It.B, It.C, It.D, "", sSyncNo, sComp, sEXEMan, It.Remark, It.Flag);
                if (sOKFlag.IndexOf("Success") > 0)
                {
                    Message = "Success";

                    if (It.A == "SQL")  // 如果是从数据库返回的一个测试值，则按要求拼接一下
                    {
                        sTValue = sOKFlag.Substring(sOKFlag.IndexOf("_") + 1, sOKFlag.IndexOf("WD") - sOKFlag.IndexOf("_") - 1);  //   Success_W1(g):34.6  W2(g):56.9  实际注射精度误差ActualAccuracyError:980 + WD + PASS
                        sIsPass = sOKFlag.Substring(sOKFlag.IndexOf("WD") + 2, sOKFlag.Length - sOKFlag.IndexOf("WD") - 2).Trim();  // 获得结论：PASS/NG
                    }

                }
                else
                {
                    Message = "Error";
                }
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, BStr = sOKFlag, TValue = sTValue, IsPass = sIsPass });

            return Result;
        }
        #endregion

        /// <summary>  
        /// 将excel导入到datatable  
        /// </summary>  
        /// <param name="filePath">excel路径</param>  
        /// <param name="isColumnName">第一行是否是列名</param>  
        /// <returns>返回datatable</returns>  
        public static DataTable ExcelToDataTable(string filePath, bool isColumnName, string sSyncNo)
        {
            string sSN = string.Empty;
            string sF = string.Empty;
            string sOItem = string.Empty;
            string sItem = string.Empty;
            DataTable dt = new DataTable();
            dt.Columns.Add("SN");
            dt.Columns.Add("F1");
            dt.Columns.Add("F2");
            dt.Columns.Add("F3");
            dt.Columns.Add("F4");
            dt.Columns.Add("F5");
            dt.Columns.Add("SyncNo");
            dt.Columns.Add("Type");



            ExcelComentsHelperClass IExcel = new ExcelComentsHelperClass();
            DataTable Tdt = IExcel.ExcelGetTable(filePath);
            //string value = IExcel.ExcelGetValue("D://123.xlsx", 1, 5);//第一种读取方法：支持xls和xlsx 第1行 第5列



            //ExcelComentsHelperClass IExcel = new ExcelComentsHelperClass();
            ////string value = IExcel.ExcelGetValue("D://123.xlsx", 1, 5);//第一种读取方法：支持xls和xlsx 第1行 第5列

            //FileStream fs = new FileStream(filePath, FileMode.Open);  // 第二种读取方法，使用流的方式，只支持xls
            //DataTable Tdt = IExcel.ExcelToTableForXLS(fs);//




            //把不标准的数据转为标准的，存放在DT，用一个循环读取，用EXCEL里面的这些字符判断是设备，还是测试项，如：包含“设备名称”，下面就是设备名称；
            // 包含“检验项目”下面就是检验项目，有多个检验项目的，注意读取。包含“检验结论”说明“检验项目”结束了，取检验结论即可。
            sSN = Tdt.Rows[1][5].ToString();
            for (int i = 0; i < Tdt.Rows.Count; i++)
            {
                sF = Tdt.Rows[i][0].ToString();
                if (sF.IndexOf("设备名称") >= 0)
                {
                    DataRow dr = dt.NewRow();
                    dr["SN"] = sSN;
                    dr["F1"] = Tdt.Rows[i + 1][0].ToString();
                    dr["F2"] = Tdt.Rows[i + 1][1].ToString();
                    dr["F3"] = Tdt.Rows[i + 1][2].ToString();
                    dr["SyncNo"] = sSyncNo;
                    dr["Type"] = "设备";
                    dt.Rows.Add(dr);
                    DataRow dr1 = dt.NewRow();
                    dr1["SN"] = sSN;
                    dr1["F1"] = Tdt.Rows[i + 1][3].ToString();
                    dr1["F2"] = Tdt.Rows[i + 1][4].ToString();
                    dr1["F3"] = Tdt.Rows[i + 1][5].ToString();
                    dr1["SyncNo"] = sSyncNo;
                    dr1["Type"] = "设备";
                    dt.Rows.Add(dr1);

                    for (int j = i + 2; j < 200; j++)
                    {  // 循环插入设备，默认200个，应该没有200个设备的吧
                        sF = Tdt.Rows[j][0].ToString();
                        if (sF.IndexOf("检验项目") < 0)
                        {   // 说明还是设备信息
                            DataRow dr2 = dt.NewRow();
                            dr2["SN"] = sSN;
                            dr2["F1"] = Tdt.Rows[j][0].ToString();
                            dr2["F2"] = Tdt.Rows[j][1].ToString();
                            dr2["F3"] = Tdt.Rows[j][2].ToString();
                            dr2["SyncNo"] = sSyncNo;
                            dr2["Type"] = "设备";
                            dt.Rows.Add(dr2);
                            DataRow dr3 = dt.NewRow();
                            dr3["SN"] = sSN;
                            dr3["F1"] = Tdt.Rows[j][3].ToString();
                            dr3["F2"] = Tdt.Rows[j][4].ToString();
                            dr3["F3"] = Tdt.Rows[j][5].ToString();
                            dr3["SyncNo"] = sSyncNo;
                            dr3["Type"] = "设备";
                            dt.Rows.Add(dr3);
                        }
                        else
                        {
                            break;
                        }
                        i = j;
                    }
                }
                else if (sF.IndexOf("检验项目") >= 0)
                {
                    DataRow dr = dt.NewRow();
                    dr["SN"] = sSN;
                    sOItem = Tdt.Rows[i + 1][0].ToString();
                    sItem = Tdt.Rows[i + 1][0].ToString();
                    dr["F1"] = sItem;
                    dr["F2"] = Tdt.Rows[i + 1][1].ToString();
                    dr["F3"] = Tdt.Rows[i + 1][2].ToString();
                    dr["F4"] = Tdt.Rows[i + 1][3].ToString();
                    dr["F5"] = Tdt.Rows[i + 1][4].ToString();
                    dr["SyncNo"] = sSyncNo;
                    dr["Type"] = "测试项";
                    dt.Rows.Add(dr);

                    for (int j = i + 2; j < 2000; j++)
                    {  // 循环插入测试项，默认2000个，应该没有2000个测试项的吧
                        sF = Tdt.Rows[j][0].ToString();
                        if (sF.IndexOf("检验结论") < 0)
                        {   // 说明还是设备信息
                            DataRow dr2 = dt.NewRow();
                            dr2["SN"] = sSN;
                            sItem = Tdt.Rows[j][0].ToString();
                            if (string.IsNullOrEmpty(sItem))
                            {  // 说明测试项目合并了单元格
                                dr2["F1"] = sOItem;
                            }
                            else
                            {
                                dr2["F1"] = sItem;
                            }
                            dr2["F2"] = Tdt.Rows[j][1].ToString();
                            dr2["F3"] = Tdt.Rows[j][2].ToString();
                            dr2["F4"] = Tdt.Rows[j][3].ToString();
                            dr2["F5"] = Tdt.Rows[j][4].ToString();
                            dr2["SyncNo"] = sSyncNo;
                            dr2["Type"] = "测试项";
                            dt.Rows.Add(dr2);

                            sOItem = dr2["F1"].ToString();
                        }
                        else
                        {
                            break;
                        }
                        i = j;
                    }
                }
                else if (sF.IndexOf("检验结论") >= 0)
                {
                    DataRow dr = dt.NewRow();
                    dr["SN"] = sSN;
                    dr["F1"] = sF;
                    dr["F2"] = Tdt.Rows[i][2].ToString();
                    dr["SyncNo"] = sSyncNo;
                    dr["Type"] = "检验结论";
                    dt.Rows.Add(dr);
                }


            }

            ////string Path = @"E:\旧电脑资料\接口文档\PCBA物料信息导入模板.xls"; 
            //string strConn = "Provider=Microsoft.Jet.OLEDB.4.0;" + "Data Source=" + filePath + ";" + "Extended Properties=Excel 8.0;";
            ////string strConn = "Provider=Microsoft.Ace.OleDb.12.0;" + "Data Source=" + filePath + ";" + "Extended Properties=Excel 12.0;";
            //OleDbConnection conn = new OleDbConnection(strConn);
            //conn.Open();
            //string strExcel = "";
            //OleDbDataAdapter myCommand = null;
            //DataSet ds = null;
            //strExcel = "select * from [sheet1$]";
            //myCommand = new OleDbDataAdapter(strExcel, strConn);
            //ds = new DataSet();
            //myCommand.Fill(ds, "table1");
            //return ds;

            return dt;
        }


        // 作业执行：打印DHR报表，获取序列号对应的工序
        public static string GetExportConditions(string DataParams)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sEXEMan = string.Empty;

            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                InMan = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(DataParams);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            sEXEMan = It.InMan;   //20240315: 使用前端传入的作业人员

            // GetExportConditions(sNo, A, B, C, sInMan, Comp, Flag);
            var dt = OrderBll.GetExportConditions(It.No, It.A, It.F, It.Item, It.E, sEXEMan, sComp, It.Flag);  // F 工单号

            Result = JsonConvert.SerializeObject(dt);


            return Result;
        }


        // 作业执行：打印DHR报表，循环每个序列号下的工序，打印DHR报表

        public static void GetExportData(string DataParams, HttpContext context)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sIsPass = string.Empty;
            string sComp = string.Empty;
            var finalConclusion = "通过";  // 默认DHR报表通过
            string formattedDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            var sData1 = new DataTable();
            var sData2 = new DataTable();
            var sData3 = new DataTable();
            var sData4 = new DataTable();
            var sData5 = new DataTable();
            var sData6 = new DataTable();
            var sData7 = new DataTable();
            var sData8 = new DataTable();
            var sData9 = new DataTable();
            var sData10 = new DataTable();
            var sData11 = new DataTable();
            var sData12 = new DataTable();
            var sData13 = new DataTable();
            var sData14 = new DataTable();
            var sData15 = new DataTable();

            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(DataParams);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                context.Response.Write(Result);
            }

            if (It.E == "DHR")
            {
                // { No: sSN, Name: "", Item: "", MNo: "", MName: "", A: sProcName, B: sProcNo, C: sProcVer, D: sFlowOrder,
                sData1 = OrderBll.GetOrderInfo(It.No, "1", It.F, It.Item, "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//设备及工装
                sData2 = OrderBll.GetOrderInfo(It.No, "2", It.F, "", "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//试剂/质控品信息
                sData3 = OrderBll.GetOrderInfo(It.No, "3", It.F, "", "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//追溯物料信息
                sData4 = OrderBll.GetOrderInfo(It.No, "4", It.F, It.Item, "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//测试信息
                sData5 = OrderBll.GetOrderInfo(It.No, "5", It.F, "", "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//基本信息   表头
                sData6 = OrderBll.GetOrderInfo(It.No, "6", It.F, "", "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//检验方式    表头
                sData7 = OrderBll.GetOrderInfo(It.No, "7", It.F, "", "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//检验方式
                sData15 = OrderBll.GetOrderInfo(It.No, "15", It.F, It.Item, "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//测试信息
            }
            else if (It.E == "HZ")
            {
                //汇总页
                sData8 = OrderBll.GetOrderInfo(It.No, "8", It.F, "", "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//追溯物料
                sData9 = OrderBll.GetOrderInfo(It.No, "9", It.F, "", "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//生产工序    
                sData10 = OrderBll.GetOrderInfo(It.No, "10", It.F, "", "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//基本信息 表头

            }
            else if (It.E == "WX")
            {
                sData11 = OrderBll.GetOrderInfo(It.No, "11", It.F, It.Item, "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//维修表头
                sData12 = OrderBll.GetOrderInfo(It.No, "12", It.F, It.Item, "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//不良现象    
                sData13 = OrderBll.GetOrderInfo(It.No, "13", It.F, It.Item, "", "", "", "", It.A, It.B, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//不良原因 
                sData14 = OrderBll.GetOrderInfo(It.No, "14", It.F, It.Item, "", "", "", "", It.A, It.Name, It.C, It.D, 50, 1, sMan, sComp, It.Flag);//物料
            }



            foreach (DataRow item in sData4.Rows)
            {
                //循环判断每一条记录只要有一条为PASS就不通过
                sIsPass = item["TestResult"].ToString().Trim();
                if (sIsPass == "NG" || sIsPass == "FAIL" || sIsPass == "NO")
                {
                    finalConclusion = "不通过";
                    break;
                }

                if (sData4.Rows.Count == 1 && item["NameCH"].ToString() == "/")
                {
                    finalConclusion = "/";
                }
            }

            // 格式化，符合锐浪报表的数据格式
            Result = JsonConvert.SerializeObject(new
            {
                Table1 = sData1,
                Table2 = sData2,
                Table3 = sData3,
                Table4 = sData4,
                Table5 = sData5,
                Table6 = sData6,
                Table7 = sData7,
                Table8 = sData8,
                Table9 = sData9,
                Table10 = sData10,
                Table11 = sData11,
                Table12 = sData12,
                Table13 = sData13,
                Table14 = sData14,
                Table15 = sData15,

                //结论
                FinalConclusion = finalConclusion,
                //维修确认   暂时用不上
                RepairConfirmation = "/",
                //处理人/日期
                InManAndInDate = sManName + " / " + formattedDate,
                //返还工序
                BackProcedureName = It.MName   //用It.MNane 应为没有多余的参数了
            });

            //It.E 操作标记
            //It.F 工单编号
            //It.No 序号
            //It.A 工序名称
            //It.B 工序编号
            GeneratePDF(context, Result, It.E, It.F, It.No, It.A, It.B, It.D, It.MNo, It.MName, sComp, sMan);
        }

        public static void GeneratePDF(HttpContext context, string data, string OPFlag, string OrderNo, string SNo, string ProcName, string sProcNo, string sFlowOrder, string sMNo, string sMName, string Comp, string sMan)
        {
            GridppReportServer Report = new GridppReportServer();

            //首先载入报表模板文件
            string ReportPathFile = string.Empty;

            //文件名称
            string fileName = string.Empty;

            string time = DateTime.Now.ToString("yyMMddhhmmss");

            //如果是等于DHR就是DHR报表，否则就是汇总报表
            if (OPFlag == "DHR")
            {
                //使用服务器的相对路径
                ReportPathFile = context.Server.MapPath("/Template/" + Comp + "/LabelPrint/DHR.grf");//根据WEB服务器根目录寻址

                //DHR文件名称 工序编号 - 工序名称
                fileName = sFlowOrder + "-" + ProcName + "(" + time + ")" + ".pdf";
            }
            else if (OPFlag == "HZ")
            {
                //使用服务器的相对路径
                ReportPathFile = context.Server.MapPath("/Template/" + Comp + "/LabelPrint/DHR(HZ).grf");//根据WEB服务器根目录寻址

                //汇总页文件名称 序列号
                fileName = SNo + "(" + time + ")" + ".pdf";
            }
            else
            {
                //使用服务器的相对路径
                ReportPathFile = context.Server.MapPath("/Template/" + Comp + "/LabelPrint/WX.grf");//根据WEB服务器根目录寻址

                //维修dhr 工序编号 - 工序名称
                fileName = sFlowOrder + "-" + ProcName + "(" + time + ")" + ".pdf";
            }


            bool Success = Report.LoadFromFile(ReportPathFile);


            if (!Success)
            {
                ServerUtility.ResponseException(context, "载入报表模板失败。");
                return;
            }

            //载入报表数据，为约定格式的 XML 或 JSON 文本数据
            string reportDataText = data;

            Report.LoadDataFromXML(reportDataText);

            //生成PDF文档数据
            IGRBinaryObject PDFDataObject = Report.ExportDirectToBinaryObject(GRExportType.gretPDF);

            //将生成的数据响应给客户端
            if (PDFDataObject.DataSize > 0)
                ServerUtility.ResponseBinary(context, PDFDataObject, fileName, OrderNo, SNo, sProcNo, ProcName, sFlowOrder, time, OPFlag, sMNo, sMName, "", Comp, sMan, "application/pdf", "attachment", "ZY"); //attachment ： inline
            else
                ServerUtility.ResponseException(context, "报表生成失败");
        }


        public static void DHRMergeOP(string DataParams, HttpContext context)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;

            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(DataParams);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
            }


            var list = OrderBll.GetExportConditions(It.No, It.A, "", It.Item, "", sMan, sComp, It.Flag);

            List<string> paths = new List<string>();

            int rand = new Random().Next(1000, 10000);

            string fileName = "(" + DateTime.Now.ToString("yyMMddhhmmss") + ")" + rand + ".pdf";

            string xuPath = "/DHRFile/DHRMerge/";

            if (!Directory.Exists(context.Server.MapPath(xuPath)))
            {
                Directory.CreateDirectory(context.Server.MapPath(xuPath));
            }

            string path = context.Server.MapPath(xuPath) + fileName;

            string str = "";

            if (list[0].Rows.Count == 0)
            {
                Result = JsonConvert.SerializeObject(new { Msg = "errorNullFile", Path = "" });
                context.Response.Write(Result);
            }

            foreach (DataRow item in list[0].Rows)
            {
                str = context.Server.MapPath(item["FilePath"].ToString());
                paths.Add(str);
            }

            Message = MergePdfFiles(paths, path);

            Result = JsonConvert.SerializeObject(new { Msg = Message, Path = xuPath + fileName });

            context.Response.Write(Result);
        }


        public static string MergePdfFiles(List<string> filesToMerge, string outputFile)
        {
            // 创建一个新的PDF文档对象
            Document document = null;
            PdfCopy writer = null;
            FileStream outputStream = null;

            try
            {
                document = new Document();
                outputStream = new FileStream(outputFile, FileMode.Create);
                writer = new PdfCopy(document, outputStream);
                document.Open();

                // 遍历所有要合并的PDF文件
                foreach (string file in filesToMerge)
                {
                    // 使用using语句确保PdfReader在使用后被正确关闭
                    using (PdfReader reader = new PdfReader(file))
                    {
                        // 将源文件的每一页复制到目标文档中
                        for (int i = 1; i <= reader.NumberOfPages; i++)
                        {
                            PdfImportedPage page = writer.GetImportedPage(reader, i);
                            writer.AddPage(page);
                        }
                    }
                }

                return "success";
            }
            catch (FileNotFoundException ex)
            {
                // 处理文件未找到异常
                return "errorNULL" + "文件未找到异常: " + ex.Message;
            }
            catch (IOException ex)
            {
                // 处理IO异常，例如写入文件时发生错误
                return "errorIO" + "IO异常: " + ex.Message;
            }
            catch (DocumentException ex)
            {
                // 处理PDF文档相关的异常
                return "errorDOC" + "文档异常: " + ex.Message;
            }
            catch (Exception ex)
            {
                // 处理其他未预期的异常
                return "errorSystem" + "未知异常: " + ex.Message;
            }
            finally
            {
                // 使用finally块确保资源被正确关闭
                if (document != null)
                {
                    document.Close();
                }
                if (writer != null)
                {
                    writer.Close();
                }
                if (outputStream != null)
                {
                    outputStream.Close();
                }
            }
        }



        public string BatchManipulateData(string DataParams)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;

            var AnonymousUser = new
            {
                No = String.Empty,
                SN = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(DataParams);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }

            if (It.Flag == "SN") //序列号选择添加工艺路线
            {
                Result = OrderBll.JudgeObjectExist(It.No, It.SN, "", sComp, "37-1");//判断添加的序列号是否已完工
                if (Result == "Y")
                {
                    return JsonConvert.SerializeObject(new { Msg = "Y_FLOWOVER" });
                }

                Result = OrderBll.JudgeObjectExist(It.No, It.SN, It.B, sComp, "37-3");//判断选择的工序是否存在，如存在则不能选择
                if (Result == "Y")
                {
                    return JsonConvert.SerializeObject(new { Msg = "repeat" });
                }

                if (It.E != "0")
                {
                    Result = OrderBll.JudgeObjectExist(It.No, It.SN, It.E, sComp, "37-2"); //判断工艺路线是否已完成  
                    if (Result == "Y")
                    {
                        return JsonConvert.SerializeObject(new { Msg = "Y_EXISTPRD" });
                    }
                }
            }
            else if (It.Flag == "WO") //工单选择添加工艺路线
            {
                Result = OrderBll.JudgeObjectExist(It.No, It.B, "", sComp, "37-4");//判断选择的工序是否存在，如存在则不能选择
                if (Result == "Y")// 等于 Y 说明已经存在了
                {
                    return JsonConvert.SerializeObject(new { Msg = "repeat" });
                }
            }


            DataTable dt = OrderBll.BatchManipulateData(It.No, It.SN, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, It.Remark, sMan, It.Flag, sComp, IP);
            if (dt.Rows.Count > 0)
            {
                Result = JsonConvert.SerializeObject(new { Msg = dt.Rows[0]["loMsg"], Message = dt.Rows[0]["loErrgs"].ToString() });
            }
            else
            {
                Result = JsonConvert.SerializeObject(new { Msg = "Error", Message = "" });
            }

            return Result;
        }



        #region 对生产执行信息进行操作--抽检关联/ 抽检完工/ 生成送检批号 抽检相关操作
        public string OPPrdSamplingInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sRNo = string.Empty;
            string sBZFlag = string.Empty;
            string sProcAndOrder = string.Empty;
            string sSN = string.Empty;
            string sEXEMan = string.Empty;
            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                G = String.Empty,
                H = String.Empty,
                I = String.Empty,
                J = String.Empty,
                InMan = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };
            var Item2 = JsonConvert.DeserializeObject(Params);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);
            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLogin = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }
            sEXEMan = It.InMan;   //20240315: 使用前端传入的作业人员
            sSN = It.No;
            if (It.Flag == "30-1")  // 关联完成
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.A, "", "", sComp, "29-8-1");
                if (sOKFlag == "N")  // 判断是否有关联的序列号，如果没有提示； N 表示没有关联的序列号，不需要完成关联
                {
                    Message = "N_GLSN";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "30-3")//完工
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.No, "", "", sComp, "28-2-3");
                if (sOKFlag == "Y")
                {  // 判断序列号是否已完成
                    Message = "Y_EXISTSOVER";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                sOKFlag = OrderBll.JudgeObjectExist(It.No + It.G, It.B, It.E, sComp, "28-3-1"); // It.No+It.G 序列号+工单
                if (sOKFlag == "Y")
                { // 判断序列号+工序+顺序编号（考虑重工） 是否已完成
                    Message = "Y_EXISTSPROCOVER";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
                // 判断是否有异常单号，是否已提交，如果没有提交，不给完工
                sOKFlag = OrderBll.JudgeObjectExist(It.A, "", "", sComp, "28-3-8-2");
                if (sOKFlag != "N")// = Y 说明有异常单未提交
                {
                    Message = "NOTCOMMITYC";
                    return JsonConvert.SerializeObject(new { Msg = Message, RNo = sOKFlag });
                }

                sOKFlag = OrderBll.JudgeObjectExist(It.A, It.I, "", sComp, "28-3-5-12");
                if (sOKFlag == "N")//判断有没有达到拒收数   sOKFlag == "N" 没有达到拒收数
                {
                    //It.B 工序编号 It.D 工序版本  It.G 工单 参数不够用使用&拼接起来传，用的时候再拆开
                    sProcAndOrder = It.B + "&" + It.D + "&" + It.G;
                    //获取当前操作的序列号的抽样方式 sOKFlag = "Y"  工序测试项抽样  sOKFlag = "N"  工序抽样
                    sOKFlag = OrderBll.JudgeObjectExist(It.G, It.No, It.E, sComp, "41-1");
                    //判断检验中的数量是否等于样本量  = N ,表示还不够，
                    sOKFlag = OrderBll.JudgeObjectExist(It.A, It.G, sOKFlag, sComp, "28-3-5-9");
                    if (sOKFlag != "Y")
                    {
                        return JsonConvert.SerializeObject(new { Msg = sOKFlag });
                    }

                    //此时已经达到了样本量，再判断批次是否存在检验中的，如果有必须走完才行
                    sOKFlag = OrderBll.JudgeObjectExist(It.A, "", "", sComp, "28-3-5-11");
                    if (sOKFlag == "Y")
                    {
                        return JsonConvert.SerializeObject(new { Msg = "NOTTESTED" });
                    }

                    sOKFlag = OrderBll.JudgeObjectExist(It.A, sProcAndOrder, It.C, sComp, "28-3-5-10");
                    if (sOKFlag.IndexOf("NOTEST") >= 0)  //  sOKFlag == "NOTEST"
                    {  // 判断测试项是否填写完成，NOTEST 表示没有测试完成
                        Message = "N_PHTestItem";
                        sRNo = sOKFlag.Substring(sOKFlag.IndexOf("_") + 1, sOKFlag.Length - sOKFlag.IndexOf("_") - 1);
                        return JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });
                    }
                    else if (sOKFlag.IndexOf("NOTsb") >= 0)
                    {  // 有设备，而且没有扫描完成
                        Message = "N_PHSCANSB";
                        sRNo = sOKFlag.Substring(sOKFlag.IndexOf("_") + 1, sOKFlag.Length - sOKFlag.IndexOf("_") - 1);
                        return JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });
                    }
                    else if (sOKFlag.IndexOf("NOTzk") >= 0)
                    {  // 有质控品，而且没有扫描完成
                        Message = "N_PHSCANZK";
                        sRNo = sOKFlag.Substring(sOKFlag.IndexOf("_") + 1, sOKFlag.Length - sOKFlag.IndexOf("_") - 1);
                        return JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo });
                    }
                    sOKFlag = OrderBll.JudgeObjectExist(It.F, "", "", sComp, "28-3-7-1");
                    if (sOKFlag == "Y")
                    { // 判断设备是否点检完成； Y 表示还有需要点击的记录
                        Message = "CheckExist";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }

                if (string.IsNullOrEmpty(It.J))
                {
                    DataTable dt = DHRBll.CheckDHR(sSN, "", It.G, "", "", It.A, "", "", "", "PH", "", "", "CompleteCheckDHR", 10, 1, sEXEMan, sComp, IP);
                    foreach (DataRow row in dt.Rows)
                    {
                        try
                        {
                            string filePath = HttpContext.Current.Server.MapPath(row["FilePath"].ToString());//服务器路径
                            if (!File.Exists(filePath))
                            {
                                return JsonConvert.SerializeObject(new { Msg = "NOTDHR" });
                            }
                        }
                        catch (Exception ex)
                        {
                            return JsonConvert.SerializeObject(new { Msg = "NOTDHR" });
                        }
                    }
                }
            }
            else if (It.Flag == "30-4")
            {
                if (It.Item == "调整检验方式")
                {
                    //判断批量异常单有设置检验方式，如果没有就不能再设置
                    sOKFlag = OrderBll.JudgeObjectExist(It.G, It.No, "", sComp, "44-1");//It.G 工单  It.No 序列号
                    if (sOKFlag == "Y")
                    {
                        Message = "N_REPEATSET";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                    // 产品流程 判断当前工序的工序行为有没有选择 送检抽检合并 
                    sOKFlag = OrderBll.JudgeObjectExist(It.E, "", "", sComp, "43-2");
                    if (sOKFlag == "Y")
                    {
                        // 选择了送检抽检合并工序行为，检查抽样方式和抽样方案是否为空
                        if (It.H == "" || It.C == "")//It.C 抽样方式  It.H 抽样方案
                        {
                            Message = "Y_FAFSNULL";
                            return JsonConvert.SerializeObject(new { Msg = Message });
                        }
                    }
                    else
                    {
                        // 没有选择送检抽检合并工序行为，检查抽样方式和抽样方案是否不为空
                        if (It.H != "" || It.C != "")//It.C 抽样方式  It.H 抽样方案
                        {
                            Message = "Y_FAFSNOTNULL";
                            return JsonConvert.SerializeObject(new { Msg = Message });
                        }
                    }
                }
            }
            else if (It.Flag == "30-5")
            {
                if (It.E == "调整检验方式")
                {
                    //判断批量异常单有没有设置检验方式，如果没有就不能提交
                    sOKFlag = OrderBll.JudgeObjectExist(It.G, It.No, "", sComp, "44-1");//It.G 工单  It.No 序列号
                    if (sOKFlag == "N")
                    {
                        Message = "N_SETCHECKWAY";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                    //处理批量异常单据判断选择的是工序测试项抽样就需要判断是否有设置抽样的测试项
                    sOKFlag = OrderBll.JudgeObjectExist(It.G, It.No, It.B + It.D, sComp, "43-1");
                    if (sOKFlag != "Y")
                    {
                        Message = sOKFlag;
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }
                else if (It.E == "返工")
                {
                    //判断批量异常单有没有添加返工工序，如果没有就不能提交
                    sOKFlag = OrderBll.JudgeObjectExist(It.G, It.No, "", sComp, "44-1");//It.G 工单  It.No 序列号
                    if (sOKFlag == "N")
                    {
                        Message = "N_BACKPROC";
                        return JsonConvert.SerializeObject(new { Msg = Message });
                    }
                }
            }
            else if (It.Flag == "30-6")
            {
                //判断批量异常单有没有添加返工工序，如果有就不能修改处理方式
                sOKFlag = OrderBll.JudgeObjectExist(It.B, It.No, "", sComp, "44-1");//It.B 工单  It.No 序列号
                if (sOKFlag == "Y")
                {
                    Message = "NOTUPDATE";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            else if (It.Flag == "30-8" || It.Flag == "30-9")//批量异常处理：30-8 修改工序测试项 \ 30-9 设置是否抽样
            {
                sOKFlag = OrderBll.JudgeObjectExist(It.G, It.No, It.Remark, sComp, "45-1");
                if (sOKFlag == "Y")  //批量异常处理： 修改工序测试项、设置是否抽样 判断操作的工序有没有生产,如果有则不给操作 
                {
                    Message = "Y_FlOWPRD";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }


                sOKFlag = OrderBll.JudgeObjectExist(It.G, It.No, It.Remark, sComp, "45-2");
                if (sOKFlag == "N")//批量异常处理：修改工序测试项、设置是否抽样 判断操作的工序是不是批量异常返回的，如果不是则不给操作 
                {
                    Message = "Y_YCGX";
                    return JsonConvert.SerializeObject(new { Msg = Message });
                }
            }
            sOKFlag = OrderBll.OPPRDInfo(sSN, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.G, It.H, It.I, It.J, sComp, sEXEMan, It.Remark, It.Flag);
            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
                sBZFlag = sOKFlag;
            }
            else if (sOKFlag.IndexOf("BZ_") > 0) // 说明返回的是包装上层批次
            {
                Message = "Success";
                sRNo = sOKFlag.Substring(sOKFlag.IndexOf("_") + 1, sOKFlag.Length - sOKFlag.IndexOf("_") - 1);  // 2 + "BZ_jf2208200001"
                sBZFlag = sOKFlag.Substring(0, 2);
            }
            else
            {
                Message = "Error" + sOKFlag;
            }
            Result = JsonConvert.SerializeObject(new { Msg = Message, RNo = sRNo, BZFlag = sBZFlag });
            return Result;
        }
        #endregion
























        #region  生成测试项属性编号 S23090900001
        public static string CreateSpecNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_TestItemBaseInfo where convert(char(10),InDate,120)='" + sDate + "' ", "SpecNo");// S230909 00001
            if (sMaxNo == "")
            {
                sNo = "S" + CreateAllNo.CreateBillNo(2, 4) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(7, 5);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 5 - len;

                sNo = "S" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion




        #region  生产不合格单号   F2104230001  
        public static string CreatePECode()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_PrdExceptionInfo where convert(char(10),InDate,120)=convert(char(10),getdate(),120) ", "PECode");//
            if (sMaxNo == "")
            {
                sNo = "BHG" + CreateAllNo.CreateBillNo(1, 2) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(sMaxNo.Length - 3, 3);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 3 - len;
                sNo = "BHG" + CreateAllNo.CreateBillNo(1, i) + iMax.ToString();
            }

            return sNo;

        }
        #endregion


        #region  生产入库申请单号     
        public static string CreateInStockNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_InStockHead where convert(char(10),InDate,120)=convert(char(10),getdate(),120) ", "InNo");//
            if (sMaxNo == "")
            {
                sNo = "RK" + CreateAllNo.CreateBillNo(1, 3) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(sMaxNo.Length - 4, 4);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 4 - len;
                sNo = "RK" + CreateAllNo.CreateBillNo(1, i) + iMax.ToString();
            }

            return sNo;

        }
        #endregion


        #region  自动产生工单编号    60-yymm-4位流水号
        public static string CreateOrderNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_OrderInfo where convert(char(7),InDate,120)='" + sDate + "' and Left(OrderNo,3)='60-' ", "OrderNo");//    60-2404-0002
            if (sMaxNo == "")
            {
                sNo = "60-" + CreateAllNo.GetDateString(4) + "-" + CreateAllNo.GetZoreNumString(3) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(8, 4);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 4 - len;

                sNo = "60-" + CreateAllNo.GetDateString(4) + "-" + CreateAllNo.GetZoreNumString(i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion







        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}
