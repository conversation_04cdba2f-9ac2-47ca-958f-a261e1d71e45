﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>作业执行信息</title>
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <script src="../js/bootstrap.min.js" type="text/javascript"></script>
    <script src="../js/bootstrap-table.min.js" type="text/javascript"></script>
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <script src="../js/ajaxfileupload.js" type="text/javascript"></script>

    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/BaseModule.js" type="text/javascript"></script>
    <script src="../js/UDI.js" type="text/javascript"></script>
    <script src="../js/grhtml5-6.8-min.js" type="text/javascript"></script>
    <script src="../js/grwebapp.js" type="text/javascript"></script>


    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function () {
            var h = 150;
            $('#dgDataList1').bootstrapTable('resetView', { height: h });
            $('#dgDataList2').bootstrapTable('resetView', { height: h });
            $('#dgDataList3').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=28&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=28&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        //$('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=28-1' });
                    }
                }
            })
        });



    </script>


    <script type="text/javascript">


        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#PrdSeriallist',
                id: 'PrdSeriallistID',
                url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=230',
                height: 'full-260',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'SerialNo', title: '序列号', width: 180, sort: true },
                    { field: 'OrderNo', title: '工单号', width: 130, sort: true },
                    { field: 'ProductNo', title: '产品编码', width: 160, sort: true },
                    { field: 'ProductName', title: '产品名称', minWidth: 280 },
                    { field: 'ProcedureName', title: '工序名称', minWidth: 100 },
                    { field: 'ProcedureNo', title: '工序', width: 60 },
                    { field: 'WorkUnit', title: '作业单元', minWidth: 100 },
                    { field: 'ConditionNo', title: '工序行为', minWidth: 100 },
                    { field: 'ConditionName', title: '行为名称', minWidth: 160 },
                    { field: 'FlowOrder', title: '顺序号', minWidth: 50 },
                    { field: 'FlowDate', title: '流程创建时间', minWidth: 150 },
                    { field: 'UserNo', title: '开始作业员', minWidth: 90 },
                    { field: 'StartDate', title: '开始时间', minWidth: 150, sort: true },
                    { field: 'EndUserNo', title: '结束作业员', minWidth: 90 },
                    { field: 'EndDate', title: '完工时间', minWidth: 150, sort: true },
                    { field: 'PStatus', title: '工序状态', minWidth: 80 },
                    //{ field: 'SNStatus', title: '序列号状态', minWidth: 80 },
                    { field: 'op', title: '操作', width: 80, toolbar: '#barDemo', fixed: 'right' }
                ]],
                page: true
                //  done: function(res, curr, count) {
                //     var that = this.elem.next();
                //     res.data.forEach(function(item, index) {
                //         if (item.Flag == "0") { // 说明评估日期已到期
                //             $(".layui-table-body tbody tr[data-index='" + index + "']").css({ 'backgroundColor': "Pink" });
                //             $(".layui-table-body tbody tr[data-index='" + index + "']").find('td[data-field="LastContactTime"]').css({ 'color': "red" });
                //
                //         }
                //     });
                // }


            });



            //监听是否选中操作
            table.on('checkbox(PrdSeriallist)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);


            });


            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(PrdSeriallist)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值

                $("#txtEXENo").val(data.EXENo);
                $("#txtSOrderNo").val(data.OrderNo);  // 工单号
                $("#txtSerialNo").val(data.SerialNo);
                $("#txtProcNo").val(data.ProcedureNo);
                $("#txtProcVer").val(data.ProcedureVer);
                $("#txtFlowNum").val(data.FlowOrder);
                var sWO = data.OrderNo;

                var sSerial = "88888";
                var sProcNo = "88";
                var sVer = "";

                $('#dgDataListZS').bootstrapTable('refresh', { url: '../Service/OrderAjax.ashx?OP=GetPRDInfoTwo&CFlag=28-6&CNO=' + sSerial + "&Item=" + sProcNo + "&CSPNO=" + sVer });
                $('#WXChangeMaterList').bootstrapTable('refresh', { url: '../Service/OrderAjax.ashx?OP=GetPRDInfoTwo&CFlag=32-1&CNO=' + sSerial + "&Item=" + sProcNo + "&CSPNO=" + data.EXENo + "&CKind=" + sWO });
                $('#dgDataListZX').bootstrapTable('refresh', { url: '../Service/OrderAjax.ashx?OP=GetPRDInfoTwo&CFlag=28-7&CNO=' + sSerial + "&Item=" + sProcNo + "&CSPNO=" + sVer });
                $('#dgDataListBZP').bootstrapTable('refresh', { url: '../Service/OrderAjax.ashx?OP=GetPRDInfoTwo&CFlag=28-8-1&CNO=' + sSerial + "&Item=" + sProcNo + "&CSPNO=" + sVer });
                ShowTestItemInfo(sSerial, sWO, sProcNo, sVer, data.EXENo)
                ShowSNDeviceInfo(sProcNo, sSerial, sVer, sWO);
                ShowTestItemLogInfo(sSerial, sWO, sProcNo, sVer)
                $('#dgDataListZK').bootstrapTable('refresh', { url: '../Service/OrderAjax.ashx?OP=GetPRDInfoTwo&CFlag=28-16&CNO=' + sSerial + "&Item=" + sProcNo + "&CSPNO=" + sVer + "&CKind=" + sWO });
                $('#dgDataListYC').bootstrapTable('refresh', { url: '../Service/OrderAjax.ashx?OP=GetPRDInfoTwo&CFlag=29-1-1&CNO=' + sSerial + "&Item=" + sProcNo + "&CSPNO=" + sVer + "&CKind=" + sWO });

            });

            //监听单元格编辑
            table.on('edit(Labellist)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });


            //监听行工具事件
            table.on('tool(PrdSeriallist)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值

                if (layEvent === 'del') {

                } else if (layEvent === 'edit') {

                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录

                } else if (layEvent === 'view') {  // 按序列号查询追溯表

                    var Data = '';
                    var Params = { sSN: data.SerialNo, OrderNo: data.OrderNo, ProductNo: data.ProductNo, ProductName: data.ProductName, ProdcssNo: data.ProcedureNo, ProdcssName: data.ProcedureName, ProdcssVer: data.ProcedureVer, ActionName: data.ConditionName, SNo: data.FlowOrder, WorkUnit: data.WorkUnit, Status: data.PStatus, Starter: data.UserNo, EndOperator: data.EndUserNo, startDate: data.StartDate, endDate: data.EndDate };
                    var Data = JSON.stringify(Params);

                    $("#Loading").show();
                    $.ajax({
                        type: "POST",
                        url: '../Service/OrderAjax.ashx?OP=SerialTracToExcel&CFlag=175',
                        data: { Data: Data },
                        success: function (data) {

                            var parsedJson = jQuery.parseJSON(data);

                            window.open(parsedJson.sFilePath);
                            $("#Loading").hide();
                        }
                    });

                } else if (layEvent === 'view2') {  // 按序列号查询追溯表

                    var Data = '';
                    var Params = { sSN: data.SerialNo, OrderNo: data.OrderNo, ProductNo: data.ProductNo, ProductName: data.ProductName, ProdcssNo: data.ProcedureNo, ProdcssName: data.ProcedureName, ProdcssVer: data.ProcedureVer, ActionName: data.ConditionName, SNo: data.FlowOrder, WorkUnit: data.WorkUnit, Status: data.PStatus, Starter: data.UserNo, EndOperator: data.EndUserNo, startDate: data.StartDate, endDate: data.EndDate };
                    var Data = JSON.stringify(Params);

                    $("#Loading").show();
                    $.ajax({
                        type: "POST",
                        url: '../Service/OrderAjax.ashx?OP=SerialTracToExcel2&CFlag=175',
                        data: { Data: Data },
                        success: function (data) {

                            var parsedJson = jQuery.parseJSON(data);

                            window.open(parsedJson.sFilePath);
                            $("#Loading").hide();
                        }
                    });

                }
                else if (layEvent === 'export') {  // 导出PDF
                    //浏览器通常不允许直接打开本地文件，因为这可能会带来安全风险。浏览器的安全机制限制了JavaScript的访问权限，防止恶意脚本在用户的计算机上执行恶意操作。
                    //但是，你可以通过上传文件到服务器，然后通过服务器的URL来访问该文件。这样可以避免直接访问本地文件系统带来的安全限制。你可以使用以下代码来在新窗口中打开该PDF文件：javascript
                    //var pdfURL = "http://yourserver.com/path/to/your/file.pdf";
                    //window.open(pdfURL, "_blank");
                    //在这个例子中，`pdfURL`变量包含了PDF文件在服务器上的URL。然后，`window.open()`函数将在新窗口中打开该URL对应的PDF文件。

                    var orderNo = data.OrderNo;
                    var ProceNo = data.ProcedureNo;
                    var ProceName = data.ProcedureName
                    var serialNo = data.SerialNo;

                    var path = "/DHRFile/" + orderNo + "/" + serialNo + "/" + ProceNo + "-" + ProceName + ".pdf"

                    window.open(path)
                }




            });


            //  查询 -- 序列号生产信息
            $('#PrdSerialBut_open').click(function () {
                var sSNo = $("#txtSSerial").val();  //序列号
                var sSONo = $("#txtSOrder").val();  // 工单号
                var sPMNo = $("#txtSPMNo").val();  // 产品编码
                //var sUPB = $("#txtSUpBatch").val();  // 上层批次

                var txtPrdMan = $("#txtPrdMan").val();  // 作业人员编号
                var txtPrdStatus = $("#txtPrdStatus").val();  // 生产状态
                var txtGX = $("#txtGX").val();  // 工序
                txtGX = txtGX.substring(1, txtGX.indexOf(")"))
                //var sName = encodeURI($("#txtSMName").val());  // 名称
                var SBDate = $("#txtStartBDate").val(); //  开始时间  开始
                var SEDate = $("#txtStartEDate").val(); //  开始时间  结束
                var EBDate = $("#txtEndBDate").val();   //  结束时间  开始
                var EEDate = $("#txtEndEDate").val();   //  结束时间  结束

                var Data = '';
                var Params = { No: sSNo, Item: sSONo, Name: txtGX, MNo: sPMNo, MName: "", Status: txtPrdStatus, BDate: SBDate, EDate: SEDate, A: "", B: txtPrdMan, C: EBDate, D: EEDate };
                var Data = JSON.stringify(Params);

                table.reload('PrdSeriallistID', {
                    method: 'post',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=230&Data=' + encodeURIComponent(Data),
                    where: {
                        'No': sSNo,
                        'name': sSONo
                    }, page: {
                        curr: 1
                    }
                });
            });
        });


        function ExcelDialog(n) {  // 导出作业执行包装信息

            var sWO = $("#txtSOrder").val();  //工单号

            if (sWO == '') {
                layer.msg('请输入工单号，查询序列号再导出！');
                return;
            }

            //         var Data = '';
            //         var Params = { No: sWO, Item: "", Name: "", MNo: "", MName: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "" };
            //         var Data = JSON.stringify(Params);

            $("#Loading").show();
            $.ajax({
                type: "POST",
                url: '../Service/OrderAjax.ashx?OP=PrdPackToExcel&CFlag=173' + '&CNO=' + sWO,
                // data: { Data: Data },
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    window.open(parsedJson.sFilePath);
                    $("#Loading").hide();
                }
            });

        }





        function Sheet_onclick(num) {


            var sEXENo = $("#txtEXENo").val(); // 作业编号
            var sWO = $("#txtSOrderNo").val();
            var sSerial = $("#txtSerialNo").val();
            var sProcNo = $("#txtProcNo").val();
            var sVer = $("#txtProcVer").val();
            // $("#txtFlowNum").val(data.FlowOrder);

            document.getElementById("DivSheet" + num).style.display = "block";
            document.getElementById("Sheet" + num).setAttribute("style", "width: 80px; background-color:#56dcaf; color: white; text-align:center; font-size:12px; cursor:pointer;");
            for (var i = 1; i <= 7; i++) {
                if (i != num) {
                    document.getElementById("DivSheet" + i).style.display = "none";
                    document.getElementById("Sheet" + i).setAttribute("style", "width: 80px; background: #ccc; text-align: center; font-size: 12px; cursor:pointer;");
                }
            }

            if (num == 1) {  // 追溯物料
                $('#dgDataListZS').bootstrapTable('refresh', { url: '../Service/OrderAjax.ashx?OP=GetPRDInfoTwo&CFlag=28-6&CNO=' + sSerial + "&Item=" + sProcNo + "&CSPNO=" + sVer + "&CKind=" + sWO });
                $('#WXChangeMaterList').bootstrapTable('refresh', { url: '../Service/OrderAjax.ashx?OP=GetPRDInfoTwo&CFlag=32-1&CNO=' + sSerial + "&Item=" + sProcNo + "&CSPNO=" + sEXENo + "&CKind=" + sWO });
            }
            else if (num == 2) {  // 装箱物料
                $('#dgDataListZX').bootstrapTable('refresh', { url: '../Service/OrderAjax.ashx?OP=GetPRDInfoTwo&CFlag=28-7&CNO=' + sSerial + "&Item=" + sProcNo + "&CSPNO=" + sVer + "&CKind=" + sWO });
            }
            else if (num == 3) {  // 包装批次
                $('#dgDataListBZP').bootstrapTable('refresh', { url: '../Service/OrderAjax.ashx?OP=GetPRDInfoTwo&CFlag=28-8-1&CNO=' + sSerial + "&Item=" + sProcNo + "&CSPNO=" + sVer });
            }
            else if (num == 4) {  // 测试项
                ShowTestItemInfo(sSerial, sWO, sProcNo, sVer, sEXENo)
                ShowTestItemLogInfo(sSerial, sWO, sProcNo, sVer)
            }
            else if (num == 5) {  // 设备
                ShowSNDeviceInfo(sProcNo, sSerial, sVer, sWO);
            }
            else if (num == 6) {  // 质控品
                $('#dgDataListZK').bootstrapTable('refresh', { url: '../Service/OrderAjax.ashx?OP=GetPRDInfoTwo&CFlag=28-16&CNO=' + sSerial + "&Item=" + sProcNo + "&CSPNO=" + sVer + "&CKind=" + sWO });
            }
            else if (num == 7) {  // 不良
                ShowYCInfo(sSerial, sProcNo, sVer);
            }

        }




        $(function () {
            $(".fixed-table-header").attr("hidden", "hidden");  // 隐藏表头

            var $result = $('#events-result');
            $('#dgDataList1').bootstrapTable({
                search: true,
                pagination: false,
                pageSize: 5,
                pageList: [5, 10, 15, 20],
                showColumns: true,
                showRefresh: false,
                showToggle: true,
                locale: "zh-CN",
                striped: true

            }).on('all.bs.table', function (e, name, args) {
                console.log('Event:', name, ', data:', args);
            }).on('click-row.bs.table', function (e, row, $element) {
                var sKind = encodeURI(row.BZCPBS);


                // 选中显示颜色
                $('.info').removeClass('info'); //移除class
                $($element).addClass('info'); //添加class
                $("#warningCH").html("")
                $("#warningCH").hide();

                $result.text('Event: click-row.bs.table, data: ' + sKind);
            }).on('dbl-click-row.bs.table', function (e, row, $element) {
                $result.text('Event: dbl-click-row.bs.table, data: ' + JSON.stringify(row));
            }).on('search.bs.table', function (e, text) {
                $result.text('Event: search.bs.table, data: ' + text);
            });


        });



        // 展示设备信息
        function ShowSNDeviceInfo(sPNo, sNo, sVer, sWO) {

            var sEXENo = $("#txtEXENo").val(); // 作业编号

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#SNDeviceList',
                    id: 'SNDeviceID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-14&CNO=' + sNo + "&Item=" + sPNo + sVer + "&CKind=" + sWO + "&CPNO=" + sEXENo,
                    height: '230',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 50, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        //{ type: 'numbers', title: '序号', width: 30 },
                        { field: 'DeviceName', title: '设备名称', width: 110 },
                        { field: 'MaterNo', title: '设备分类编码', width: 120 },
                        { field: 'DeviceNo', title: '设备编码', width: 130 },
                        { field: 'TNo', title: '点检任务', minWidth: 130 },
                        { field: 'UseDate', title: '校准日期', minWidth: 90 },
                        { field: 'op', title: '操作', width: 75, toolbar: '#barDemo_SNDevice', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(SNDeviceList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值

                    if (layEvent == 'detail') {

                        $('#txtDJDevice').val(data.DeviceNo);  //设备编码
                        $('#txtDJProc').val(data.ProcedureName);  //工序名称
                        $('#txtDJType').val(data.DeviceKind);  //设备类型
                        $('#txtDJReq').val(data.CheckReq);  //点检要求
                        $('#txtDJDept').val(data.DeptName);  //部门
                        $('#txtDJStatus').val(data.Status);  //状态
                        $('#txtDJMan').val($('#txtEUserNo').val());  //操作人
                        $('#txtDJMaterNo').val(data.MaterNo);  //设备分类编码
                        $('#txtDJRWNo').val(data.TNo);  //点检任务编号
                        $('#txtDJCPNo').val(data.CPNo);  //来源

                        var time = new Date();
                        var sM = time.getMonth() + 1;
                        if (sM < 10) {
                            sM = "0" + sM
                        }
                        var sD = time.getDate()
                        if (sD < 10) {
                            sD = "0" + sD
                        }
                        var s1 = time.getFullYear() + "-" + sM + "-" + sD;
                        $('#txtDJDate').val(s1);//点检日期

                        ShowSNDeviceCheckInfo(data.TNo, "", "");

                        $("#div_warningDJ").html("")
                        $("#div_warningDJ").hide();

                        document.getElementById('Div_DeviceDJ').style.display = 'block';
                        document.getElementById('fade').style.display = 'block'
                    }

                });
            });

        }

        // 展示设备的点击任务信息
        function ShowSNDeviceCheckInfo(sSN, sNo, sVer) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#CheckDeviceList',
                    id: 'CheckDeviceID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-15&CNO=' + sSN + "&Item=" + sNo + "&CSPNO=" + sVer,
                    height: '270',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 50, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers', title: '序号', width: 30 },
                        { field: 'CheckTxt', title: '点检内容', width: 400 },
                        { field: 'CResult', title: '结果', width: 80, },
                        { field: 'CRemark', title: '备注', width: 150, }
                        //{ field: 'op', title: '操作', width: 80, toolbar: '#barDemo_CheckDevice', fixed: 'right' }
                    ]],
                    page: true
                });


                //监听单元格编辑
                table.on('edit(CheckDeviceList)', function (obj) {
                    var value = obj.value //得到修改后的值
                        , data = obj.data //得到所在行所有键值
                        , field = obj.field; //得到字段


                });

                //监听行工具事件
                table.on('tool(CheckDeviceList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值

                    if (layEvent == 'detail') {
                        $('#txtAddKind').val("");
                        $("#L_K").html("修改设备信息");
                    }

                });
            });
        }


        // 展示测试项信息日志记录
        function ShowTestItemLogInfo(sSN, sWO, sNo, sVer) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#TestItemListLog',
                    id: 'TestItemListLogID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-25&CNO=' + sSN + "&Item=" + sNo + "&CSPNO=" + sVer + "&CKind=" + sWO,
                    height: '400',
                    cellMinWidth: 80,
                    count: 400, //数据总数 服务端获得
                    limit: 400, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [50, 100, 150, 200, 250, 300, 400], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers', title: '序号', width: 30 },
                        { field: 'NameCH', title: '检验项目' },
                        { field: 'DescCH', title: '检验要求' },
                        { field: 'TestValue', title: '测试值', },
                        { field: 'TestResult', title: '结论', },
                        { field: 'OldTestValue', title: '原测试值', },
                        { field: 'Remark', title: '修改原因' },
                        { field: 'InMan', title: '操作人' },
                        { field: 'InDate2', title: '测试时间' },
                    ]],
                    page: true
                });

            });
        }

        // 展示测试项信息
        function ShowTestItemInfo(sSN, sWO, sNo, sVer, EXENo) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#TestItemList',
                    id: 'TestItemID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-21&CNO=' + sSN + "&Item=" + sNo + sVer + "&CPNO=" + EXENo + "&CKind=" + sWO,
                    height: '400',
                    cellMinWidth: 80,
                    count: 400, //数据总数 服务端获得
                    limit: 400, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [50, 100, 150, 200, 250, 300, 400], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers', title: '序号', width: 30 },
                        { field: 'NameCH', title: '检验项目', width: 300 },
                        { field: 'DescCH', title: '检验要求', width: 500 },
                        { field: 'TestValue', title: '测试值', },
                        { field: 'InDate2', title: '测试时间', },
                        //{ field: 'op', title: '操作', width: 80, toolbar: '#barDemo_TestItem', fixed: 'right' }
                    ]],
                    page: true
                });


                //监听单元格编辑
                table.on('edit(TestItemList)', function (obj) {
                    var value = obj.value //得到修改后的值
                        , data = obj.data //得到所在行所有键值
                        , field = obj.field; //得到字段


                });

                //监听行工具事件
                table.on('tool(TestItemList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值

                    if (layEvent == 'detail') {
                        $('#txtAddKind').val("");
                        $("#L_K").html("修改设备信息");
                    }

                });
            });
        }


        // 展示异常信息
        function ShowYCInfo(sSN, sNo, sVer) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#SNYCList',
                    id: 'YCID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-22&CNO=' + sSN + "&Item=" + sNo + "&CSPNO=" + sVer,
                    height: '180',
                    cellMinWidth: 80,
                    count: 400, //数据总数 服务端获得
                    limit: 400, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [50, 100, 150, 200, 250, 300, 400], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers', title: '序号', width: 30 },
                        { field: 'PECode', title: '异常编号', width: 150 },
                        { field: 'BatchNo', title: '序列号', width: 180 },
                        { field: 'OrderNo', title: '工单', width: 150 },
                        { field: 'ProcedureNo', title: '工序编号', width: 100 },
                        { field: 'ProcedureName', title: '工序', width: 100 },
                        { field: 'InDate', title: '发生时间', width: 150 },
                        { field: 'op', title: '操作', width: 80, toolbar: '#barDemo_SNYC', fixed: 'right' }
                    ]],
                    page: true
                });


                //监听单元格编辑
                table.on('edit(SNYCList)', function (obj) {
                    var value = obj.value //得到修改后的值
                        , data = obj.data //得到所在行所有键值
                        , field = obj.field; //得到字段


                });

                //监听行工具事件
                table.on('tool(SNYCList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值

                    if (layEvent == 'detail') {

                        $('#txtK').html("查看不合格处理单");
                        $('#txtAEFlag').val("29-View");

                        $("#txtPECode").val(data.PECode);
                        $("#txtOrderNo").val(data.OrderNo);
                        $("#txtSerialNo").val(data.BatchNo);
                        $("#txtFNo").val(data.MaterNo);
                        $("#txtFName").val(data.MaterName);
                        $("#txtModel").val(data.Model);
                        $("#txtGX").val("(" + data.ProcedureNo + ")" + data.ProcedureName);
                        $("#txtProcNoR").val("(" + data.ProcedureNo + ")" + data.ProcedureName);
                        $("#txtFDate").val(data.InDate2);
                        $("#txtStatus").val(data.Status);
                        $("#txtFMan").val(data.FMan);
                        $("#txtOrderKind").val(data.OrderKind);
                        $("#txtDeptName").val(data.DeptName);

                        $("#txtFMan").attr({ "disabled": "disabled" });
                        $("#txtFDate").attr({ "disabled": "disabled" });
                        $("#txtDeptName").attr({ "disabled": "disabled" });

                        $("#div_warning").html("");
                        $("#div_warning").hide();
                        $("#ChoogeSN_Btn").hide();  // 隐藏选择的序列号
                        $("#ECauseSaveBtn").hide();


                        GetProcedureList(data.BatchNo);
                        ShowBLXXInfo(data.PECode);  // 显示这个不合格单的不良现象
                        ShowWXInfo(data.PECode);  // 显示维修信息

                        $("#Div_BLYY").show();
                        $("#Div_WXFS").show();
                        $("#Div_MaterInfo").show();
                        $("#div_SBLYY").show();

                        document.getElementById('light').style.display = 'block';
                        //document.getElementById('fade').style.display = 'block';
                    }

                });
            });
        }


        function GetProcedureList(sNo) {  // 加载工序

            var sSs = "工序";

            var keywords = encodeURI(sSs);  // 解决中文乱码的问题。
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/TechAjax.ashx?OP=GetProcedureList&CFlag=111-1-1&Item=" + sNo,
                success: function (data) {
                    var parsedJson = eval(data).Msg;

                    var sKong = "<option value=''> </option>";
                    $("#txtGX").empty();
                    $("#txtGX").append(sKong + parsedJson);

                    $('#txtGX').val($('#txtProcNoR').val());
                }
            });
        }


        //显示不良现象
        function ShowBLXXInfo(sNo) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#BLXXList',
                    id: 'BLXXID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-16&CNO=' + sNo,
                    height: '250',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'ECode', title: '不良现象代码', width: 150 },
                        { field: 'EName', title: '名称', width: 300 },
                        //{ field: 'EDesc', title: '描述', width: 150 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate2', title: '录入时间', width: 120 }
                        //{ field: 'op', title: '操作', width: 80, toolbar: '#barDemo_BLXX', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行单击事件（双击事件为：rowDouble）
                table.on('row(BLXXList)', function (obj) {
                    obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                    obj.tr.addClass('layui-table-click');

                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值

                    $("#txtAECode").val(data.ECode);
                    $("#txtAEName").val(data.EName);

                    ShowBLYYInfo(data.PECode, data.ECode);

                });


                //监听行工具事件
                table.on('tool(BLXXList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值


                    if (layEvent == 'edit') {
                        $('#txtAddKind').val("88888");
                        $("#L_WOFile").html("修改不良信息");
                        return;
                    }
                    else if (layEvent == 'del') {

                        if ($('#txtAEFlag').val() == "29-View") {
                            $("#div_warning").html("单据查看状态，不可操作");
                            $("#div_warning").show();
                            return;
                        }

                        layer.confirm('真的删除该记录么', function (index) {
                            //向服务端发送删除指令 A: sPVer, B: sPath, C: sFile, D: sFNo, E: sFVer, F: sFName,

                            var sPECode = data.PECode;
                            var sECode = data.ECode;
                            var sEXENo = data.EXENo;  // 在这里的不良处理界面，这个EXENo = PECode
                            var sFlag = "29-2-2";

                            var Data = '';
                            var Params = { No: sPECode, Name: "", Item: "", MNo: "", MName: "", A: "", B: "", C: "", D: sEXENo, E: sECode, F: "", Remark: "", Flag: sFlag };
                            var Data = JSON.stringify(Params);

                            $.ajax({
                                type: "POST",
                                url: "../Service/OrderAjax.ashx?OP=OPOrderInfo&CFlag=" + sFlag,
                                data: { Data: Data },
                                success: function (data) {
                                    var parsedJson = jQuery.parseJSON(data);

                                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                        layer.msg('删除成功！');
                                        obj.del();
                                        layer.close(index);
                                    }
                                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                                        layer.msg('已维修或维修中，不可删除！');
                                    }
                                    else {
                                        layer.msg('删除失败，请重试！');
                                    }
                                },
                                error: function (data) {
                                    layer.msg('删除失败2，请重试！');
                                }
                            });

                        }); // 删除
                    }

                });

            });  // layui.use('table', function () {


        } // function ShowProcFileInfo(sPNo, sNo, sVer) {


        // 显示维修信息
        function ShowWXInfo(sPECODE) {

            $("#L_Mgs").html("");
            var sFlag = "30-2-1"

            var Data = '';
            var Params = { No: sPECODE, Name: "", Item: "", MNo: "", MName: "", A: "", B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: sFlag };
            var Data = JSON.stringify(Params);

            $.ajax({
                type: "POST",
                url: "../Service/OrderAjax.ashx?OP=GetWXInfo&CFlag=" + sFlag,
                data: { Data: Data },
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);

                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                        var Str = eval(parsedJson.json)[0];
                        $("#txtPECode").val(Str.PECode);  // eval(parsedJson.json)[0].PECode
                        $("#txtEXENo").val(Str.EXENo);
                        $("#txtOrderNo").val(Str.OrderNo);
                        $("#txtSerialNo").val(Str.BatchNo);
                        $("#txtFNo").val(Str.MaterNo);
                        $("#txtFName").val(Str.MaterName);
                        $("#txtModel").val(Str.Model);
                        $("#txtGX").val("(" + Str.ProcedureNo + ")" + Str.ProcedureName);
                        $("#L_NowGX").html("(" + Str.ProcedureNo + ")" + Str.ProcedureName);
                        $("#txtFDate").val(Str.FDate);
                        $("#txtStatus").val(Str.Status);
                        $("#txtFMan").val(Str.FMan);
                        $("#txtOrderKind").val(Str.OrderKind);
                        $("#txtDeptName").val(Str.DeptName);
                        $("#txtDealType").val(Str.DealWay);
                        $("#txtYUNNo").val(Str.YUNNo);
                        $("#txtMaintainType").val(Str.MaintainType);
                        $("#txtMaintainDesc").val(Str.MaintainDesc);

                        ShowMaterInfo(Str.BatchNo, Str.EXENo); // 已安装的物料
                        GetProcedureList(Str.BatchNo); // 加载当前序列号已生产的工序
                        ShowBLYYInfo("888888", "888888");

                    } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                        $("#L_Mgs").html("您未登陆系统，请先登录！")
                        //location.href = "Login.htm";
                    }
                    else {
                        $("#L_Mgs").html("");
                    }
                },
                error: function (data) {
                    $("#div_warningAddBLXX").html("系统出错，请重试2！");
                    $('#div_warningAddBLXX').show();
                }
            });

        }


        //显示不良原因
        function ShowBLYYInfo(sNo, sENo) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#BLYYList',
                    id: 'BLYYID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-17&CNO=' + sNo + "&Item=" + sENo,
                    height: '250',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'CNo', title: '不良原因代码', width: 150 },
                        { field: 'CName', title: '名称', width: 150 },
                        { field: 'CDesc', title: '描述', width: 200 },
                        { field: 'Location', title: '描述', width: 200 },
                        { field: 'CType', title: '不良原因类型', width: 120 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate2', title: '录入时间', width: 120 }
                        //{ field: 'op', title: '操作', width: 130, toolbar: '#barDemo_BLYY', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(BLYYList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值


                    if (layEvent == 'edit') {

                    }


                });

            });  // layui.use('table', function () {


        }

        //显示这个序列号已安装的物料
        function ShowMaterInfo(sNo, sEXENo) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#MaterList',
                    id: 'MaterID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-19&CNO=' + sNo + "&Item=" + sEXENo,
                    height: '350',
                    cellMinWidth: 80,
                    count: 200, //数据总数 服务端获得
                    limit: 200, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [50, 100, 150, 200], //分页显示每页条目下拉选择
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'ProcedureName', title: '工序', width: 80 },
                        { field: 'MaterNo', title: '物料编码', width: 150 },
                        { field: 'MaterName', title: '物料名称', width: 180 },
                        // { field: 'OldSN', title: '旧序列号', width: 120 },
                        { field: 'MaterBatch', title: '物料序列号', width: 120 },
                        { field: 'ConsumeNum', title: '数量', width: 50 },
                        { field: 'Kind', title: '类别', width: 50 },
                        { field: 'DealType', title: '旧料处理意见', minWidth: 100 }
                        // { field: 'op', title: '操作', width: 130, toolbar: '#barDemo_Mater', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(MaterList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值


                    if (layEvent == 'edit') {

                    }

                });

            });  // layui.use('table', function () {

        }









        function closeDialog() {
            document.getElementById('light').style.display = 'none';
            document.getElementById('fade').style.display = 'none';
            document.getElementById('Div_DeviceDJ').style.display = 'none';
        }

        function closeDeviceDJDialog() {
            document.getElementById('fade').style.display = 'none';
            document.getElementById('Div_DeviceDJ').style.display = 'none';
        }




    </script>


    <script type="text/javascript">


        //在网页初始加载时向报表提供数据
        var reportViewer;


        //应用URL协议启动WEB报表客户端程序，根据参数 args 调用对应的功能
        function webapp(args, sTp, sHttpUrl) {
            var sss = "";
            var sSerial = "8888888,";  // 后续做一个固定的数据给预览的

            if (sHttpUrl == "") {
                alert('请选择要打印的标贴模板 ');
                return;
            }

            if (sTp == "") {
                alert('请选择要打印的标贴模板 ');
                return;
            }
            var sPath = ".." + sTp;   //"../Template/LabelPrint/PrdSerial.grf";

            reportViewer = rubylong.grhtml5.insertReportViewer("report_holder", sPath);

            // --  不知为啥，如果屏蔽了下面 4 行代码，调用 "Service/UDIAjax.ashx?OP=GetPrintInfo&Flag=1&CNO=" + sSerial;  返回的序列号多了一些字符串，如：U8A2021A000035V1.0,?QuerySQL=data.txt
            webapp_urlprotocol_startup(); //启动WEB报表客户端程序，以便侦听接受 WebSocket 数据

            reportViewer.stop();
            reportViewer.dataURL = "../Service/UDIAjax.ashx?OP=GetPrintInfo&CNO=wu2222222222";  //根据当前查询参数设置报表的取数URL
            reportViewer.start();   //启动报表运行，生成报表



            args.baseurl = window.rootURL;
            args.report = sHttpUrl + reportViewer.reportURL; //去掉../../
            args.data = sHttpUrl + "Service/UDIAjax.ashx?OP=GetPrintInfo&CFlag=1&CNO=" + sSerial + "&SS=" + sss;
            if (!reportViewer.dataURL) {
                args.selfsql = true;
            }
            webapp_ws_run(args);
        }



        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

            $("#txtStartBDate,#txtStartEDate,#txtEndBDate,#txtEndEDate").change(function () {
                var status = $("#txtPrdStatus").val()
                if (status == "" || status == null) {
                    layer.msg("请先选择工序状态！")
                    $(this).val("")
                    return
                }
            })

            //$("#txtPrdStatus").change(function () {
            //    var status = $("#txtPrdStatus").val()
            //    if (status == "未开始") {
            //        $("#txtStartBDate").prop("disabled", true).val("")
            //        $("#txtStartEDate").prop("disabled", true).val("")
            //        $("#txtEndBDate").prop("disabled", true).val("")
            //        $("#txtEndEDate").prop("disabled", true).val("")
            //    }
            //    else if (status == "生产中") {
            //        $("#txtStartBDate").prop("disabled", false)
            //        $("#txtStartEDate").prop("disabled", false)
            //        $("#txtEndBDate").prop("disabled", true).val("")
            //        $("#txtEndEDate").prop("disabled", true).val("")
            //    }
            //    else if (status == "已完成") {
            //        $("#txtStartBDate").prop("disabled", false)
            //        $("#txtStartEDate").prop("disabled", false)
            //        $("#txtEndBDate").prop("disabled", false)
            //        $("#txtEndEDate").prop("disabled", false)
            //    } else {
            //        $("#txtStartBDate").prop("disabled", false).val("")
            //        $("#txtStartEDate").prop("disabled", false).val("")
            //        $("#txtEndBDate").prop("disabled", false).val("")
            //        $("#txtEndEDate").prop("disabled", false).val("")
            //    }
            //})

            $("#txtPrdStatus").change(function () {
                var status = $("#txtPrdStatus").val();
                var map = {
                    "未开始": [true, true, true, true],
                    "生产中": [false, false, true, true],
                    "已完成": [false, false, false, false],
                };
                var fields = ["#txtStartBDate", "#txtStartEDate", "#txtEndBDate", "#txtEndEDate"];

                var val = map[status]
                if (val == undefined || val == "" || val == null) {
                    val = [false, false, false, false];
                }

                fields.forEach(function (item, index) {
                    $(item).prop("disabled", val[index]).val("");
                });
            });

            GetProcedureList()
        })


        function GetProcedureList() {  // 加载工序

            var sSs = "工序";

            var keywords = encodeURI(sSs);  // 解决中文乱码的问题。
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/TechAjax.ashx?OP=GetProcedureList&CFlag=111-1&CKind=" + keywords,
                success: function (data) {
                    var parsedJson = eval(data).Msg;

                    var sKong = "<option value=''> </option>";
                    $("#txtGX").empty();
                    $("#txtGX").append(sKong + parsedJson);

                    $('#txtGX').val($('#txtProcNoR').val());
                }
            });
        }

    </script>


    <style type="text/css">


        #light table {
            margin: 0px;
            padding: 0px;
        }
        /* 主要防止点击 追溯物料， 包装时，显示内容的网格自动变大*/

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: white;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=60);
        }

        .LabelDelBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #da542e;
        }

        .LabelAddBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #579fd8;
        }


        .table-responsive {
            overflow-x: hidden;
        }
    </style>




</head>
<body>
    <div class="div_find">

        <p>
            <label class="find_labela">工单号：</label><input type="text" id="txtSOrder" class="find_input" />
            <label class="find_labela">序列号：</label> <input type="text" id="txtSSerial" class="find_input" />
            <label class="find_labela">产品编码：</label><input type="text" id="txtSPMNo" class="find_input" />
            <!--<label class="find_labela">上层批次：</label><input type="text" id="txtSUpBatch" class="find_input" />-->
            <label class="find_labela">工序：</label><select type="text" id="txtGX" class="find_input"></select>
            <label class="find_labela">作业员编号：</label> <input type="text" id="txtPrdMan" class="find_input" />
            <input type="button" value="搜索" class="find_but" id="PrdSerialBut_open" />
            <span class="find_span"><i class="i_open01"></i>展开</span>
            <span class="find_span1"><i class="i_close01"></i>收起</span>
        </p>
        <p id="open" style="display:none;margin-top:5px">
            <label class="find_labela">工序状态：</label><select type="text" id="txtPrdStatus" class="find_input">
                <option value=""></option>
                <option value="未开始">未开始</option>
                <option value="生产中">生产中</option>
                <option value="已完成">已完成</option>
            </select>
           
            <label class="find_labela">开始时间：</label><input type="date" id="txtStartBDate" class="find_input" style=" width:120px;" />-<input type="date" id="txtStartEDate" class="find_input" style=" width:120px;" />
            <label class="find_labela">结束时间：</label><input type="date" id="txtEndBDate" class="find_input" style=" width:120px;" />-<input type="date" id="txtEndEDate" class="find_input" style=" width:120px;" />
        </p>
    </div>
    <div style="text-align:right">
        <!--   <i class="add2_i" ></i><a href="JavaScript:void(0)" onclick="openDialog(1)" style="color: Blue">添加</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-->
        <!--<i class="down_i"></i><a href="JavaScript:void(0)" onclick="ExcelDialog(1)" class="add_a">包装作业数据</a> &nbsp;&nbsp;&nbsp;-->
    </div>

    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="PrdSeriallist" lay-filter="PrdSeriallist"></table>

        <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-xs" lay-event="view">追溯表</a>
            <!--<a class="layui-btn layui-btn-xs" lay-event="export">DHR</a>-->
        </script>

    </div>

    <div id="SheetN" style=" height: 25px; margin-top: 5px; ">
        <table id="yh_header2" cellspacing="0" cellpadding="0" border='0' style="width:100%;background:white; height:25px;color: #222222; ">
            <tr>
                <td id="Sheet1" onclick="Sheet_onclick(1)" style="width: 80px; background-color:#56dcaf; color: white; text-align:center; font-size:12px; cursor:pointer;">
                    追溯物料
                </td>
                <td style="width:5px;background:white; text-align:center;">
                </td>
                <td id="Sheet2" onclick="Sheet_onclick(2)" style="width: 80px; background: #ccc; text-align: center; font-size: 12px; cursor:pointer;">
                    装箱物料
                </td>
                <td style="width:5px;background:white; text-align:center;">
                </td>
                <td id="Sheet3" onclick="Sheet_onclick(3)" style="width: 80px; background: #ccc; text-align: center; font-size: 12px; cursor:pointer;">
                    检验批次
                </td>
                <td style="width:5px;background:white; text-align:center;">
                </td>
                <td id="Sheet4" onclick="Sheet_onclick(4)" style="width: 80px; background: #ccc; text-align: center; font-size: 12px; cursor:pointer;">
                    测试项
                </td>
                <td style="width:5px;background:white; text-align:center;">
                </td>
                <td id="Sheet5" onclick="Sheet_onclick(5)" style="width: 80px; background: #ccc; text-align: center; font-size: 12px; cursor:pointer;">
                    设备
                </td>
                <td style="width:5px;background:white; text-align:center;">
                </td>
                <td id="Sheet6" onclick="Sheet_onclick(6)" style="width: 80px; background: #ccc; text-align: center; font-size: 12px; cursor:pointer;">
                    质控品
                </td>
                <td style="width:5px;background:white; text-align:center;">
                </td>
                <td id="Sheet7" onclick="Sheet_onclick(7)" style="width: 80px; background: #ccc; text-align: center; font-size: 12px; cursor:pointer;">
                    不良信息
                </td>
                <td style="width:30%;background:white;">
                    <input type="text" class="form-control" id="txtEXENo" name="txtEXENo" readonly=readonly style=" display:none;" />
                    <input type="text" class="form-control" id="txtSerialNo" name="txtSerialNo" readonly=readonly style=" display:none;" />
                    <input type="text" class="form-control" id="txtProcNo" name="txtProcNo" readonly=readonly style=" display:none;" />
                    <input type="text" class="form-control" id="txtProcVer" name="txtProcVer" readonly=readonly style=" display:none;" />
                    <input type="text" class="form-control" id="txtFlowNum" name="txtFlowNum" readonly=readonly style=" display:none;" />
                </td>
            </tr>
        </table>
    </div>

    <div id="DivSheet1" style="background-color: White; float: left; width: 100%; display: none; ">
        <div class="table-responsive">
            <table id="dgDataListZS" data-toggle="table" data-cache="false" data-pagination="false" class="table-striped" data-search="false" data-show-toggle="false" data-sort-order="desc">
                <thead>
                    <tr style="background-color: White; text-align: center; border: 0px; ">
                        <th data-field="MaterNo" data-align="left" style="width: 100px;">
                            物料编码
                        </th>
                        <th data-field="MaterName" data-align="left" style="width: 25%;">
                            物料描述
                        </th>
                        <th data-field="MBatch" data-align="left" style="width: 100px;">
                            批/序号
                        </th>
                        <th data-field="UseNum" data-align="left" style="width: 50px;">
                            需求数
                        </th>
                        <th data-field="OverNum" data-align="left" style="width: 50px;">
                            已用数
                        </th>
                        <th data-field="AMFlag" data-align="left" style="width: 50px;">
                            替代料
                        </th>
                    </tr>
                </thead>
            </table>
            <div style="margin:10px 0px;font-weight:bold;color:black">物料拆解记录</div>
            <table id="WXChangeMaterList" data-toggle="table" data-cache="false" data-pagination="false" class="table-striped" data-search="false" data-show-toggle="false" data-sort-order="desc">
                <thead>
                    <tr style="background-color: White; text-align: center; border: 0px; ">
                        <th data-field="MaterNo" data-align="left" style="width: 100px;">
                            物料编码
                        </th>
                        <th data-field="MaterName" data-align="left" style="width: 25%;">
                            物料描述
                        </th>
                        <th data-field="MaterBatch" data-align="left" style="width: 100px;">
                            批/序号
                        </th>
                        <th data-field="Num" data-align="left" style="width: 50px;">
                            拆解数
                        </th>
                        <th data-field="Kind" data-align="left" style="width: 50px;">
                            类型
                        </th>
                        <th data-field="DealType" data-align="left" style="width: 50px;">
                            旧料处理意见
                        </th>
                        <th data-field="InMan" data-align="left" style="width: 50px;">
                            操作人
                        </th>
                        <th data-field="InDate" data-align="left" style="width: 50px;">
                            操作时间
                        </th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>

    <div id="DivSheet2" style="background-color: White; float: left; width: 100%; display: none; ">
        <div class="table-responsive">
            <table id="dgDataListZX" data-toggle="table" data-cache="false" data-pagination="false" class="table-striped" data-search="false" data-show-toggle="false" data-sort-order="desc">
                <thead>
                    <tr style="background-color: White; text-align: center; border: 0px;">
                        <th data-field="MaterNo" data-align="left" style="width: 100px;">
                            物料编码
                        </th>
                        <th data-field="MaterName" data-align="left" style="width: 25%;">
                            物料描述
                        </th>
                        <th data-field="UseNum" data-align="left" style="width: 50px;">
                            装箱数
                        </th>
                        <th data-field="OverNum" data-align="left" style="width: 50px;">
                            已装数
                        </th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>

    <div id="DivSheet3" style="background-color: White; float: left; width: 100%; display: none; ">
        <div class="table-responsive">
            <table id="dgDataListBZP" data-toggle="table" data-cache="false" data-pagination="false" class="table-striped" data-search="false" data-show-toggle="false" data-sort-order="desc">
                <thead>
                    <tr style="background-color: White; text-align: center; border: 0px;">
                        <th data-field="BatchNo" data-align="left" style="width: 150px;">
                            序列号
                        </th>
                        <th data-field="MaterBatchNo" data-align="left" style="width: 20%;">
                            上层批次
                        </th>
                        <th data-field="PDate" data-align="left" style="width: 50px;">
                            时间
                        </th>
                        <th data-field="Status" data-align="left" style="width: 50px;">
                            是否关联完毕
                        </th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>

    <div id="DivSheet4" style="height: auto; display: none; border-top: solid 1px #56dcaf; padding-top: 5px; color: #4b545d ">
        <div class="wangid_conbox">
            <!-- 下面写内容   -->
            <table class="layui-hide" id="TestItemList" lay-filter="TestItemList"></table>
            <script type="text/html" id="barDemo_TestItem">
                <a class="layui-btn layui-btn-xs" lay-event="edit">修改</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del888888" style="background-color:white;">.</a>
            </script>
            <div style="margin:5px 0px;font-weight:bold;color:black">修改记录</div>
            <table class="layui-hide" id="TestItemListLog" lay-filter="TestItemListLog"></table>
        </div>
    </div>

    <div id="DivSheet5" style="height: auto; display: none; border-top: solid 1px #56dcaf; padding-top: 5px; color: #4b545d ">
        <div class="wangid_conbox">
            <!-- 下面写内容   -->
            <table class="layui-hide" id="SNDeviceList" lay-filter="SNDeviceList"></table>
            <script type="text/html" id="barDemo_SNDevice">
                <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del888888" style="background-color:white;">.</a>
            </script>

        </div>
    </div>

    <div id="DivSheet6" style="background-color: White; float: left; width: 100%; display: none;">
        <div class="table-responsive" style="height:230px;padding-top:0px">
            <table id="dgDataListZK" data-toggle="table" data-cache="false" data-pagination="false"
                   class="table-striped" data-search="false" data-show-toggle="false"
                   data-sort-order="desc">
                <thead>
                    <tr style="background-color: White; text-align: center; border: 0px;">
                        <th data-field="MaterNo" data-align="left" style="width: 130px;">
                            编码
                        </th>
                        <th data-field="QualityName" data-align="left" style="width: 200px;">
                            描述
                        </th>
                        <th data-field="LotNo" data-align="left" style="width: 100px;">
                            批次
                        </th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>

    <div id="DivSheet7" style="background-color: White; float: left; width: 100%; display: none;">
        <div class="wangid_conbox">
            <!-- 下面写内容   -->
            <table class="layui-hide" id="SNYCList" lay-filter="SNYCList"></table>
            <script type="text/html" id="barDemo_SNYC">
                <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del888888" style="background-color:white;">.</a>
            </script>

        </div>
    </div>


    <div id="Div_DeviceDJ" style="display: none;position: absolute;top: 1%;left: 2%;right: 2%;width: 96%;height:96%;border: 2px solid orange; background-color: white;z-index: 1002;overflow: auto;">
        <div style="background-color: #26d0a1; height: 30px;">
            <label style="font-size: 14px; color:White; padding:5px;">设备点检详情</label>
            <label onclick="closeDeviceDJDialog()" style="float:right; margin:5px 5px 0 0; cursor:pointer;">X</label>
        </div>
        <div id="div_warningDJ" role="alert" style="text-align: center; display:none;color: Red ">
            <strong id="divsuccessDJ" style="color: Red"></strong>
        </div>
        <table cellspacing="0" cellpadding="0" border='0' style="width:98%; ">
            <tr style=" height:35px;">
                <td style=" width:120px; text-align:right;">
                    设备编码
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDJDevice" name="txtDJDevice" style=" height:30px; width:99%;" readonly="readonly" />
                </td>
                <td style=" width: 120px; text-align: right;">
                    工序名称
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDJProc" name="txtDJProc" style=" height:30px; width:99%;" readonly="readonly" />
                </td>
                <td style=" width: 120px; text-align: right;">
                    设备类型
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDJType" name="txtDJType" style=" height:30px; width:99%;" readonly="readonly" />
                </td>
                <td style=" width: 120px; text-align: right;">
                    点检日期
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDJDate" name="txtDJDate" style=" height:30px; width:99%;" readonly="readonly" />
                </td>
            </tr>
            <tr style=" height:35px;">
                <td style=" width: 120px; text-align: right;">
                    点检要求
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDJReq" name="txtDJReq" style=" height:30px; width:99%;" readonly="readonly" />
                </td>
                <td style=" width: 120px; text-align: right;">
                    部门
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDJDept" name="txtDJDept" style=" height:30px; width:99%;" readonly="readonly" />
                </td>
                <td style=" width: 120px; text-align: right;">
                    状态
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDJStatus" name="txtDJStatus" style=" height:30px; width:99%;" readonly="readonly" />
                </td>
                <td style=" width: 120px; text-align: right;">
                    操作人
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDJMan" name="txtDJMan" style=" height:30px; width:99%;" readonly="readonly" />
                </td>
            </tr>
            <tr style=" height:35px;">
                <td style=" width: 120px; text-align: right;">
                    设备分类编码
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDJMaterNo" name="txtDJMaterNo" style=" height:30px; width:99%;" readonly="readonly" />
                </td>
                <td style=" width: 120px; text-align: right;">
                    点击任务编号
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDJRWNo" name="txtDJRWNo" style=" height:30px; width:99%;" readonly="readonly" />
                </td>
                <td style=" width: 120px; text-align: right;">
                    来源
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDJCPNo" name="txtDJCPNo" style=" height:30px; width:99%;" readonly="readonly" />
                </td>
                <td style=" width: 120px; text-align: right;">
                </td>
                <td>
                </td>
            </tr>
            <tr style=" height:20px;">
                <td colspan="8">
                    点检要求  &nbsp; &nbsp; &nbsp;
                </td>
            </tr>
            <tr style=" height:40px;">
                <td colspan="6">
                    <div class="wangid_conbox">
                        <!-- 下面写内容   -->
                        <table class="layui-hide" id="CheckDeviceList" lay-filter="CheckDeviceList"></table>

                        <script type="text/html" id="barDemo_CheckDevice">
                            <a class="layui-btn layui-btn-xs" lay-event="chooge">选择</a>
                            <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del666" style="background-color:white;">.</a>
                        </script>

                    </div>
                </td>
                <td>
                </td>
                <td>
                </td>
            </tr>
        </table>
        <br />
        <div align="center">
            <input type='button' id="DeviceSave_Close" value='关闭' onclick='closeDeviceDJDialog()' style="height: 30px;width: 65px;background-color: #56dcaf;color: white;border: 0px;border-radius: 5px;" />
        </div>
    </div>



    <div id="light" style="display: none;position: absolute;top: 0%;left: 1%;right: 1%;width: 99%;height: 540px;border: 2px solid #21b6b4; background-color: white;z-index: 1002;overflow: auto;">
        <div id="lightHead" style=" height:35px;">
            <label id="txtK" style=" padding:5px;font-size: 14px; color:White; "></label>
            <label onclick="closeDialog()" style="float:right; margin:5px 5px 0 0; cursor:pointer;">X</label>
        </div>
        <div id="div_warning" role="alert" style="text-align: center;color: Red ">
            <strong id="divsuccess" style="color: Red"></strong>
        </div>
        <table cellspacing="0" cellpadding="0" border='0' style="width: 98%;  ">
            <tr style=" height:30px;">
                <td style=" width:100px;font-weight:bold;color:blue;">
                    基本信息
                </td>
                <td>
                </td>
                <td colspan="3">
                    <label id="L_Mgs" style="font-weight:bold;color:red;"></label>
                </td>
                <td>
                    <input type='button' id="ECauseSaveBtn" value='提交' class="find_but" style="width: 50px; height: 28px;" />
                </td>
            </tr>
            <tr style=" height:30px;">
                <td style=" width:100px; text-align:right;">
                    不合格处理单:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtPECode" name="txtPECode" style="height: 28px;" readonly=readonly />
                </td>
                <td style=" width:100px; text-align:right;">
                    序列号:
                </td>
                <td>
                </td>
                <td style=" width:100px; text-align:right;">
                    工单号:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtOrderNo" name="txtOrderNo" style="height: 28px;" readonly=readonly />
                </td>
            </tr>
            <tr style=" height: 30px;">
                <td style=" width:100px; text-align:right;">
                    产品编码:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtFNo" name="txtFNo" style="height: 28px;" readonly=readonly />
                </td>
                <td style=" width:100px; text-align:right;">
                    产品描述:
                </td>
                <td colspan="3">
                    <input type="text" class="form-control" id="txtFName" name="txtFName" style="height: 28px; width: 96%;" readonly=readonly />
                </td>
            </tr>
            <tr style=" height: 30px;">
                <td style=" width:100px; text-align:right;">
                    型号:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtModel" name="txtModel" style="height: 28px;" readonly=readonly />
                </td>
                <td style=" width:100px; text-align:right;">
                    发生环节:
                </td>
                <td>
                    <select class="form-control" id="txtGX" style="height: 28px;" disabled="disabled">
                        <option></option>
                    </select>
                    <input type="text" class="form-control" id="txtProcNoR" name="txtProcNoR" style="height: 28px; display: none;" readonly=readonly />
                </td>
                <td style=" width:100px; text-align:right;">
                    日期:
                </td>
                <td>
                    <input type="date" class="form-control" id="txtFDate" name="txtFDate" style="height: 28px;" />
                </td>
            </tr>
            <tr style=" height: 30px;">
                <td style=" width:100px; text-align:right;">
                    状态:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtStatus" name="txtStatus" style="height: 28px;" readonly=readonly />
                </td>
                <td style=" width:100px; text-align:right;">
                    发现人:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtFMan" name="txtFMan" style="height: 28px;" />
                </td>
                <td style=" width:100px; text-align:right;">
                    作业类型:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtOrderKind" name="txtOrderKind" style="height: 28px;" readonly=readonly />
                </td>
            </tr>
            <tr style=" height: 30px;">
                <td style=" width:100px; text-align:right;">
                    部门:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDeptName" name="txtDeptName" style="height:28px;" readonly=readonly />
                </td>
                <td style=" width:100px; text-align:right;">
                </td>
                <td>
                </td>
                <td style=" width:100px; text-align:right;">
                </td>
                <td>
                    <input type="text" class="form-control" id="txtSOrderNo" name="txtSOrderNo" style="height:28px; display:none;" readonly=readonly />
                </td>
            </tr>
            <tr>
                <!--  下面是各个执行明细了-->
                <td colspan="6">
                    <table cellspacing="0" cellpadding="0" border='0' style="width:100%; " id="blxxClass">
                        <tr style=" height: 30px;">
                            <td style="font-weight: bold; color: blue;">
                                不良现象
                            </td>
                        </tr>
                        <tr style=" height:220px;vertical-align:top;">
                            <td>
                                <div class="wangid_conbox">
                                    <!-- 下面写内容   -->
                                    <table class="layui-hide" id="BLXXList" lay-filter="BLXXList"></table>
                                    <script type="text/html" id="barDemo_BLXX">
                                        <a class="layui-btn layui-btn-xs" lay-event="edit5555" style="background-color:white;">.</a>
                                        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                                    </script>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

        </table>

        <div id="div_SBLYY" style="font-weight: bold; color: blue;display:none;">
            不良原因
        </div>
        <!--  下面是各个执行明细了:不良原因-->
        <div class="wangid_conbox" id="Div_BLYY" style="display:none;">
            <!-- 下面写内容   -->
            <table class="layui-hide" id="BLYYList" lay-filter="BLYYList"></table>
            <script type="text/html" id="barDemo_BLYY">
                <a class="layui-btn layui-btn-xs" lay-event="edit">修改</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
            </script>
        </div>
        <!--  处理方式，维修方式-->
        <div id="Div_WXFS" style="width:100%; display:none;">
            <table cellspacing="0" cellpadding="0" border='0' style="width:100%; ">
                <tr style=" height: 30px;">
                    <td style="font-weight: bold; color: blue;">
                        处理方式
                    </td>
                    <td>
                    </td>
                </tr>
                <tr style=" height: 30px;">
                    <td style="width:200px;">
                        <input type="text" class="form-control" id="txtDealType" name="txtDealType" style="height:28px;" readonly="readonly" />
                    </td>
                    <td>
                        <input type="text" class="form-control" id="txtYUNNo" name="txtYUNNo" style="height:28px;" readonly="readonly" />
                    </td>
                </tr>
                <tr style=" height: 30px;">
                    <td style="font-weight: bold; color: blue;">
                        维修方式
                    </td>
                    <td>
                    </td>
                </tr>
                <tr style=" height: 30px;">
                    <td style="width:200px;">
                        <input type="text" class="form-control" id="txtMaintainType" name="txtMaintainType" style="height:28px;" readonly="readonly" />
                    </td>
                    <td>
                        <input type="text" class="form-control" id="txtMaintainDesc" name="txtMaintainDesc" style="height:28px;" readonly="readonly" />
                    </td>
                </tr>
            </table>
        </div>
        <!--  展示这个序列号装配包装的所有物料-->
        <div class="wangid_conbox" id="Div_MaterInfo" style="display:none;">
            <!-- 下面写内容   -->
            <table class="layui-hide" id="MaterList" lay-filter="MaterList"></table>
            <script type="text/html" id="barDemo_Mater">
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">拆解</a>
                <a class="layui-btn layui-btn-xs" lay-event="edit">绑定</a>
            </script>
        </div>
        <div style="text-align:center; height:40px;">
            <input type="button" value="关闭" id="AddBLXX_Close_Btn" class="find_but" style="width: 50px; height: 28px;" onclick='closeDialog()' />
        </div>
    </div>



    <div id="fade" class="black_overlay">
    </div>

    <div id="Loading" style="display: none; z-index: 1000; width:100%; text-align:right;">
        导出中...  <img src="../fonts/loading.gif" width="60px" height="12px" />
    </div>


</body>
</html>