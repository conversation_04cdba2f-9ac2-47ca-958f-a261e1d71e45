﻿<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{EE1F325D-A77B-4D43-807A-4F5508E918FE}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Web</RootNamespace>
    <AssemblyName>Web</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <UseIISExpress>false</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Alex.Kingdee.Cloud.WebAPI.Client, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Alex.Kingdee.Cloud.WebAPI.Client.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=*******, Culture=neutral, PublicKeyToken=0e99375e54769942">
      <HintPath>..\packages\BouncyCastle.1.8.9\lib\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="ExcelComentHelper, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\ExcelComentHelper.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=0.86.0.518, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="Interop.grsvr6Lib, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <EmbedInteropTypes>True</EmbedInteropTypes>
      <HintPath>bin\Interop.grsvr6Lib.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp, Version=********, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\iTextSharp.********\lib\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="microsoft.office.interop.excel, Version=********, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Lib\microsoft.office.interop.excel.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=2.3.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL" />
    <Reference Include="NPOI.OOXML, Version=2.3.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL" />
    <Reference Include="NPOI.OpenXml4Net, Version=2.3.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL" />
    <Reference Include="NPOI.OpenXmlFormats, Version=2.3.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL" />
    <Reference Include="OFFICE, Version=********, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Lib\OFFICE.DLL</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web.Mobile" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="BaseModule\BatchSerial.htm" />
    <Content Include="BaseModule\LogManage.html" />
    <Content Include="BaseModule\SoftwareVersion.html" />
    <Content Include="css\bootstrap-select.min.css" />
    <Content Include="css\bootstrap.min.css" />
    <Content Include="css\XC.css" />
    <Content Include="Echart\css\layui.css" />
    <Content Include="Echart\css\reset.css" />
    <Content Include="Echart\css\style.css" />
    <Content Include="Echart\css\XC.css" />
    <Content Include="Echart\font\iconfont.svg" />
    <Content Include="Echart\html\HtmlPage1.html" />
    <Content Include="Echart\html\HtmlPage10.html" />
    <Content Include="Echart\html\HtmlPage11.html" />
    <Content Include="Echart\html\HtmlPage12.html" />
    <Content Include="Echart\html\HtmlPage13.html" />
    <Content Include="Echart\html\HtmlPage2.html" />
    <Content Include="Echart\html\HtmlPage3.html" />
    <Content Include="Echart\html\HtmlPage4.html" />
    <Content Include="Echart\html\HtmlPage6.html" />
    <Content Include="Echart\html\HtmlPage5.html" />
    <Content Include="Echart\html\HtmlPage7.html" />
    <Content Include="Echart\html\HtmlPage8.html" />
    <Content Include="Echart\html\HtmlPage9.html" />
    <Content Include="Echart\image\HtmlPage1.png" />
    <Content Include="Echart\image\HtmlPage2.png" />
    <Content Include="Echart\image\HtmlPage3.png" />
    <Content Include="Echart\image\HtmlPage4.png" />
    <Content Include="Echart\js\echarts.js" />
    <Content Include="Echart\js\jquery-3.3.1.min.js" />
    <Content Include="Echart\js\layui.js" />
    <Content Include="Echart\JXC_Main.htm" />
    <Content Include="Echart\Spc.html" />
    <Content Include="Echart\SPC_Main.htm" />
    <Content Include="Echart\TemplateBase.html" />
    <Content Include="Flow\Test.html" />
    <Content Include="images\Login1.png" />
    <Content Include="js\ajaxfileupload.js" />
    <Content Include="js\bootstrap-select.min.js" />
    <Content Include="js\bootstrap.js" />
    <Content Include="js\bootstrap.min.js" />
    <Content Include="js\jquery-1.4.1-vsdoc.js" />
    <Content Include="js\jquery-1.4.1.js" />
    <Content Include="js\jquery-1.4.1.min.js" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="appcode\ReportGenerator.cs" />
    <Compile Include="appcode\ServerUtility.cs" />
    <Compile Include="BaseModule\ModuleInfo.aspx.cs">
      <DependentUpon>ModuleInfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BaseModule\ModuleInfo.aspx.designer.cs">
      <DependentUpon>ModuleInfo.aspx</DependentUpon>
    </Compile>
    <Compile Include="Echart\Common\TimerTaskManager.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Service\ActivateService.ashx.cs">
      <DependentUpon>ActivateService.ashx</DependentUpon>
    </Compile>
    <Compile Include="Service\ApplyUpPicFile.aspx.cs">
      <DependentUpon>ApplyUpPicFile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Service\ApplyUpPicFile.aspx.designer.cs">
      <DependentUpon>ApplyUpPicFile.aspx</DependentUpon>
    </Compile>
    <Compile Include="Service\BaseModuleAjax.ashx.cs">
      <DependentUpon>BaseModuleAjax.ashx</DependentUpon>
    </Compile>
    <Compile Include="Service\Check.ashx.cs">
      <DependentUpon>Check.ashx</DependentUpon>
    </Compile>
    <Compile Include="Service\CompanyCombo.ashx.cs">
      <DependentUpon>CompanyCombo.ashx</DependentUpon>
    </Compile>
    <Compile Include="Service\CompanyPricing.ashx.cs">
      <DependentUpon>CompanyPricing.ashx</DependentUpon>
    </Compile>
    <Compile Include="Service\DHRAjax.ashx.cs">
      <DependentUpon>DHRAjax.ashx</DependentUpon>
    </Compile>
    <Compile Include="Service\EchartAjax.ashx.cs">
      <DependentUpon>EchartAjax.ashx</DependentUpon>
    </Compile>
    <Compile Include="Service\FlowConfigAjax.ashx.cs">
      <DependentUpon>FlowConfigAjax.ashx</DependentUpon>
    </Compile>
    <Compile Include="Service\GetWXOpenID.aspx.cs">
      <DependentUpon>GetWXOpenID.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Service\LoginAjax.ashx.cs">
      <DependentUpon>LoginAjax.ashx</DependentUpon>
    </Compile>
    <Compile Include="Service\MaterAjax.ashx.cs">
      <DependentUpon>MaterAjax.ashx</DependentUpon>
    </Compile>
    <Compile Include="Service\OrderAjax.ashx.cs">
      <DependentUpon>OrderAjax.ashx</DependentUpon>
    </Compile>
    <Compile Include="Service\PrdAssistAjax.ashx.cs">
      <DependentUpon>PrdAssistAjax.ashx</DependentUpon>
    </Compile>
    <Compile Include="Service\ProductPricing.ashx.cs">
      <DependentUpon>ProductPricing.ashx</DependentUpon>
    </Compile>
    <Compile Include="Service\TechAjax.ashx.cs">
      <DependentUpon>TechAjax.ashx</DependentUpon>
    </Compile>
    <Compile Include="Service\TimerAjax.ashx.cs">
      <DependentUpon>TimerAjax.ashx</DependentUpon>
    </Compile>
    <Compile Include="Service\UDIAjax.ashx.cs">
      <DependentUpon>UDIAjax.ashx</DependentUpon>
    </Compile>
    <Compile Include="UpdateFile.aspx.cs">
      <DependentUpon>UpdateFile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UpdateFile.aspx.designer.cs">
      <DependentUpon>UpdateFile.aspx</DependentUpon>
    </Compile>
    <Compile Include="WebService\GetSerielInfo.asmx.cs">
      <DependentUpon>GetSerielInfo.asmx</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="WebService\OperateSerielInfo.asmx.cs">
      <DependentUpon>OperateSerielInfo.asmx</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="js\jquery-1.8.3.min.js" />
    <Content Include="js\jquery-3.6.0.min.js" />
    <Content Include="js\json.js" />
    <Content Include="js\json2.js" />
    <Content Include="js\PrdAssist.js" />
    <Content Include="js\qrcode.js" />
    <Content Include="Lib\Newtonsoft.Json.dll" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Echart\font\iconfont.eot" />
    <Content Include="Echart\font\iconfont.ttf" />
    <Content Include="Echart\font\iconfont.woff" />
    <Content Include="Echart\font\iconfont.woff2" />
    <None Include="fonts\fontawesome-webfont.eot" />
    <None Include="fonts\fontawesome-webfont.ttf" />
    <None Include="fonts\fontawesome-webfont.woff" />
    <None Include="fonts\fontawesome-webfont.woff2" />
    <None Include="fonts\FontAwesome.otf" />
    <None Include="fonts\glyphicons-halflings-regular.eot" />
    <None Include="fonts\glyphicons-halflings-regular.svg" />
    <None Include="fonts\glyphicons-halflings-regular.ttf" />
    <None Include="fonts\glyphicons-halflings-regular.woff" />
    <None Include="fonts\glyphicons-halflings-regular.woff2" />
    <None Include="fonts\glyphicons-halflings-regulard41d.eot" />
    <None Include="fonts\iconfont.ttf" />
    <None Include="fonts\iconfont.woff" />
    <None Include="fonts\weathericons-regular-webfont.eot" />
    <None Include="fonts\weathericons-regular-webfont.ttf" />
    <None Include="fonts\weathericons-regular-webfont.woff" />
    <None Include="fonts\weathericons-regular-webfontd41d.eot" />
    <None Include="json\Dept.json" />
    <None Include="layui\font\iconfont.eot" />
    <None Include="layui\font\iconfont.ttf" />
    <None Include="layui\font\iconfont.woff" />
    <Content Include="BaseModule\LabelTemplate.htm" />
    <Content Include="BaseModule\Personal.htm" />
    <Content Include="css\orderinfo.css" />
    <Content Include="js\Order.js" />
    <Content Include="MES_Main.htm" />
    <Content Include="PrdAssist\ClearPlan.html" />
    <Content Include="PrdAssist\DefectsCause.html" />
    <Content Include="PrdAssist\DefectsDesc.html" />
    <Content Include="PrdAssist\DeptInfo.html" />
    <Content Include="PrdAssist\DeviceCheckTask.html" />
    <Content Include="PrdAssist\DeviceClass.html" />
    <Content Include="PrdAssist\DeviceInfo.html" />
    <Content Include="PrdAssist\DeviceMaintainTask.html" />
    <Content Include="PrdAssist\InspectEXE.html" />
    <Content Include="PrdAssist\InspectPlan.html" />
    <Content Include="PrdAssist\module\treetable-lay\treetable.css" />
    <Content Include="PrdAssist\module\treetable-lay\treetable.js" />
    <Content Include="PrdAssist\module\treetable.js" />
    <Content Include="PrdAssist\OrderClearRec.html" />
    <Content Include="PrdAssist\OrderInStoreQuery.html" />
    <Content Include="PrdAssist\ProcessFile.html" />
    <Content Include="PrdAssist\QAControlEXE.html" />
    <Content Include="PrdAssist\QAControlPlan.html" />
    <Content Include="PrdAssist\QCWarehouse.html" />
    <Content Include="PrdAssist\Warehouse.html" />
    <Content Include="PrdExe\CreateDHR.html" />
    <Content Include="PrdExe\DHRQuery.htm" />
    <Content Include="PrdExe\InStock.html" />
    <Content Include="PrdExe\OrderInfo.htm" />
    <Content Include="PrdExe\OrderSerial.htm" />
    <Content Include="PrdExe\PrdBatchECode.html" />
    <Content Include="PrdExe\PrdECode.htm" />
    <Content Include="PrdExe\PrdExe.htm" />
    <Content Include="PrdExe\PrdExeBatchOP.html" />
    <Content Include="PrdExe\PrdExeInfo.htm" />
    <Content Include="PrdExe\PrdExeOP.htm" />
    <Content Include="PrdExe\Repair.html" />
    <Content Include="PrdExe\RetrospectQuery.html" />
    <Content Include="PrdExe\WorkStatusQuery.html" />
    <Content Include="ProductPricing\Activate.html" />
    <Content Include="ProductPricing\CompanyPricing.html" />
    <Content Include="ProductPricing\CompanyCombo.html" />
    <Content Include="ProductPricing\CustSettlement.html" />
    <Content Include="ProductPricing\Pricing.html" />
    <Content Include="ProductPricing\test.html" />
    <Content Include="Service\OrderAjax.ashx" />
    <Content Include="Service\TechAjax.ashx" />
    <Content Include="BaseModule\CompanyInfo.htm" />
    <Content Include="BaseModule\ProductLabel.htm" />
    <Content Include="images\loginleft.jpg" />
    <Content Include="js\Tech.js" />
    <Content Include="Service\UDIAjax.ashx" />
    <Content Include="js\CreateControl.js" />
    <Content Include="js\grhtml5-6.8-min.js" />
    <Content Include="js\grwebapp.js" />
    <Content Include="Service\Check.ashx" />
    <Content Include="BaseModule\MCode.htm" />
    <Content Include="BaseModule\SerielMater.htm" />
    <Content Include="js\UDI.js" />
    <Content Include="JXC_Main.htm" />
    <Content Include="Mater\IQC.htm" />
    <Content Include="Mater\MaterFaulty.htm" />
    <Content Include="Mater\MaterJXC.htm" />
    <Content Include="Mater\MaterOut.htm" />
    <Content Include="Mater\MaterStock.htm" />
    <Content Include="Mater\MOutSearch.htm" />
    <Content Include="Mater\MReturnSearch.htm" />
    <Content Include="Mater\PdOutSearch.htm" />
    <Content Include="Mater\ProductStock.htm" />
    <Content Include="Mater\ProdutOut.htm" />
    <Content Include="Mater\ReturnMater.htm" />
    <Content Include="Mater\SHInfo.htm" />
    <Content Include="Service\MaterAjax.ashx" />
    <Content Include="css\styleLogin.css" />
    <Content Include="images\background01.jpg" />
    <Content Include="images\icon_01.png" />
    <Content Include="images\icon_02.png" />
    <Content Include="js\Mater.js" />
    <Content Include="Login2.htm" />
    <Content Include="BaseModule\AddUser.htm" />
    <Content Include="BaseModule\BOM.htm" />
    <Content Include="BaseModule\CustInfo.htm" />
    <Content Include="BaseModule\CustMater.htm" />
    <Content Include="BaseModule\Mater.htm" />
    <Content Include="BaseModule\MaterType.htm" />
    <Content Include="BaseModule\ModuleInfo.aspx" />
    <Content Include="BaseModule\ModuleInfo.htm" />
    <Content Include="BaseModule\module\treetable-lay\treetable.css" />
    <Content Include="BaseModule\module\treetable-lay\treetable.js" />
    <Content Include="BaseModule\module\treetable.js" />
    <Content Include="BaseModule\Role.htm" />
    <Content Include="BaseModule\Supplier.htm" />
    <Content Include="BaseModule\SupplierYC.htm" />
    <Content Include="BaseModule\UseLayui.htm" />
    <Content Include="BaseModule\User.htm" />
    <Content Include="css\all.css" />
    <Content Include="css\children.css" />
    <Content Include="css\demo.css" />
    <Content Include="css\font-awesome.min.css" />
    <Content Include="css\indexSRM.css" />
    <Content Include="css\indexUser.css" />
    <Content Include="css\layui.css" />
    <Content Include="css\layuiM.css" />
    <Content Include="css\skins\_all-skins.css" />
    <Content Include="css\style.css" />
    <Content Include="css\styleLucky.css" />
    <Content Include="css\zTreeStyle\img\cc.png" />
    <Content Include="css\zTreeStyle\img\diy\1_close.png" />
    <Content Include="css\zTreeStyle\img\diy\1_open.png" />
    <Content Include="css\zTreeStyle\img\diy\2.png" />
    <Content Include="css\zTreeStyle\img\diy\3.png" />
    <Content Include="css\zTreeStyle\img\diy\4.png" />
    <Content Include="css\zTreeStyle\img\diy\5.png" />
    <Content Include="css\zTreeStyle\img\diy\6.png" />
    <Content Include="css\zTreeStyle\img\diy\7.png" />
    <Content Include="css\zTreeStyle\img\diy\8.png" />
    <Content Include="css\zTreeStyle\img\diy\9.png" />
    <Content Include="css\zTreeStyle\img\diy\cc.png" />
    <Content Include="css\zTreeStyle\img\diy\zclose.png" />
    <Content Include="css\zTreeStyle\img\diy\zopen.png" />
    <Content Include="css\zTreeStyle\img\diy\ztop.png" />
    <Content Include="css\zTreeStyle\img\line_conn.gif" />
    <Content Include="css\zTreeStyle\img\loading.gif" />
    <Content Include="css\zTreeStyle\img\zTreeStandard.gif" />
    <Content Include="css\zTreeStyle\img\zTreeStandard.png" />
    <Content Include="css\zTreeStyle\zTreeStyle.css" />
    <Content Include="fonts\fontawesome-webfont.svg" />
    <Content Include="fonts\weathericons-regular-webfont.svg" />
    <Content Include="images\list_top.gif" />
    <Content Include="images\menu_bg.jpg" />
    <Content Include="images\menu_left.jpg" />
    <Content Include="images\Search.gif" />
    <Content Include="images\top_bg.jpg" />
    <Content Include="images\top_lable.jpg" />
    <Content Include="images\top_login1.jpg" />
    <Content Include="img\9.png" />
    <Content Include="img\add01.png" />
    <Content Include="img\ajax-loader.gif" />
    <Content Include="img\alam_map.png" />
    <Content Include="img\colse.png" />
    <Content Include="img\del01.png" />
    <Content Include="img\house.png" />
    <Content Include="img\look.png" />
    <Content Include="img\loy_ph.png" />
    <Content Include="img\opne.png" />
    <Content Include="img\pr01.png" />
    <Content Include="img\print.png" />
    <Content Include="img\res01.png" />
    <Content Include="img\start.png" />
    <Content Include="img\table_add.png" />
    <Content Include="img\table_del.png" />
    <Content Include="img\table_down.png" />
    <Content Include="img\table_print.png" />
    <Content Include="img\tclose.png" />
    <Content Include="img\timg %281%29.jpg" />
    <Content Include="img\timg %282%29.jpg" />
    <Content Include="img\timg.jpg" />
    <Content Include="img\tjico.png" />
    <Content Include="img\top_lf.jpg" />
    <Content Include="img\top_logo.png" />
    <Content Include="img\up.png" />
    <Content Include="img\user2-160x160.jpg" />
    <Content Include="js\BaseModule.js" />
    <Content Include="js\bstable\js\bootstrap-table-zh-CN.min.js" />
    <Content Include="js\bstable\js\bootstrap-table.js" />
    <Content Include="js\bstable\js\bootstrap.min.js" />
    <Content Include="js\bstable\js\jquery-2.2.0.min.js" />
    <Content Include="js\bstable\js\js-2.1.4.min.js" />
    <Content Include="js\bstable\table_t.js" />
    <Content Include="js\date\index.html" />
    <Content Include="js\date\js\laydate.js" />
    <Content Include="js\date\js\need\laydate.css" />
    <Content Include="js\date\js\skins\dahong\icon.png" />
    <Content Include="js\date\js\skins\dahong\laydate.css" />
    <Content Include="js\date\js\skins\danlan\icon.png" />
    <Content Include="js\date\js\skins\danlan\laydate.css" />
    <Content Include="js\date\js\skins\default\icon.png" />
    <Content Include="js\date\js\skins\default\laydate.css" />
    <Content Include="js\date\js\skins\molv\icon.png" />
    <Content Include="js\date\js\skins\molv\laydate.css" />
    <Content Include="js\date\js\skins\qianhuang\icon.png" />
    <Content Include="js\date\js\skins\qianhuang\laydate.css" />
    <Content Include="js\date\js\skins\yahui\icon.png" />
    <Content Include="js\date\js\skins\yahui\laydate.css" />
    <Content Include="js\date\js\skins\yalan\icon.png" />
    <Content Include="js\date\js\skins\yalan\laydate.css" />
    <Content Include="js\index.js" />
    <Content Include="js\jquery-1.11.0.min.js" />
    <Content Include="js\jQuery-2.2.0.min.js" />
    <Content Include="js\jquery.ztree.all-3.5.js" />
    <Content Include="js\jquery.ztree.all-3.5.min.js" />
    <Content Include="js\jquery.ztree.core-3.5.js" />
    <Content Include="js\jquery.ztree.core-3.5.min.js" />
    <Content Include="js\jquery.ztree.excheck-3.5.js" />
    <Content Include="js\jquery.ztree.excheck-3.5.min.js" />
    <Content Include="js\jquery.ztree.exedit-3.5.js" />
    <Content Include="js\jquery.ztree.exedit-3.5.min.js" />
    <Content Include="js\jquery.ztree.exhide-3.5.js" />
    <Content Include="js\jquery.ztree.exhide-3.5.min.js" />
    <Content Include="js\laydate.js" />
    <Content Include="js\layer.js" />
    <Content Include="js\layer\extend\layer.ext.js" />
    <Content Include="js\layer\layer.js" />
    <Content Include="js\layer\layui.js" />
    <Content Include="js\layer\skin\default\icon-ext.png" />
    <Content Include="js\layer\skin\default\icon.png" />
    <Content Include="js\layer\skin\default\loading-0.gif" />
    <Content Include="js\layer\skin\default\loading-1.gif" />
    <Content Include="js\layer\skin\default\loading-2.gif" />
    <Content Include="js\layer\skin\layer.css" />
    <Content Include="js\layer\skin\layer.ext.css" />
    <Content Include="js\skin\layer.css" />
    <Content Include="js\treetable.js" />
    <Content Include="layui\css\layui.css" />
    <Content Include="layui\css\layui.mobile.css" />
    <Content Include="layui\css\modules\code.css" />
    <Content Include="layui\css\modules\laydate\default\laydate.css" />
    <Content Include="layui\css\modules\layer\default\icon-ext.png" />
    <Content Include="layui\css\modules\layer\default\icon.png" />
    <Content Include="layui\css\modules\layer\default\icon1.png" />
    <Content Include="layui\css\modules\layer\default\layer.css" />
    <Content Include="layui\css\modules\layer\default\loading-0.gif" />
    <Content Include="layui\css\modules\layer\default\loading-1.gif" />
    <Content Include="layui\css\modules\layer\default\loading-2.gif" />
    <Content Include="layui\font\iconfont.svg" />
    <Content Include="layui\images\face\0.gif" />
    <Content Include="layui\images\face\1.gif" />
    <Content Include="layui\images\face\10.gif" />
    <Content Include="layui\images\face\11.gif" />
    <Content Include="layui\images\face\12.gif" />
    <Content Include="layui\images\face\13.gif" />
    <Content Include="layui\images\face\14.gif" />
    <Content Include="layui\images\face\15.gif" />
    <Content Include="layui\images\face\16.gif" />
    <Content Include="layui\images\face\17.gif" />
    <Content Include="layui\images\face\18.gif" />
    <Content Include="layui\images\face\19.gif" />
    <Content Include="layui\images\face\2.gif" />
    <Content Include="layui\images\face\20.gif" />
    <Content Include="layui\images\face\21.gif" />
    <Content Include="layui\images\face\22.gif" />
    <Content Include="layui\images\face\23.gif" />
    <Content Include="layui\images\face\24.gif" />
    <Content Include="layui\images\face\25.gif" />
    <Content Include="layui\images\face\26.gif" />
    <Content Include="layui\images\face\27.gif" />
    <Content Include="layui\images\face\28.gif" />
    <Content Include="layui\images\face\29.gif" />
    <Content Include="layui\images\face\3.gif" />
    <Content Include="layui\images\face\30.gif" />
    <Content Include="layui\images\face\31.gif" />
    <Content Include="layui\images\face\32.gif" />
    <Content Include="layui\images\face\33.gif" />
    <Content Include="layui\images\face\34.gif" />
    <Content Include="layui\images\face\35.gif" />
    <Content Include="layui\images\face\36.gif" />
    <Content Include="layui\images\face\37.gif" />
    <Content Include="layui\images\face\38.gif" />
    <Content Include="layui\images\face\39.gif" />
    <Content Include="layui\images\face\4.gif" />
    <Content Include="layui\images\face\40.gif" />
    <Content Include="layui\images\face\41.gif" />
    <Content Include="layui\images\face\42.gif" />
    <Content Include="layui\images\face\43.gif" />
    <Content Include="layui\images\face\44.gif" />
    <Content Include="layui\images\face\45.gif" />
    <Content Include="layui\images\face\46.gif" />
    <Content Include="layui\images\face\47.gif" />
    <Content Include="layui\images\face\48.gif" />
    <Content Include="layui\images\face\49.gif" />
    <Content Include="layui\images\face\5.gif" />
    <Content Include="layui\images\face\50.gif" />
    <Content Include="layui\images\face\51.gif" />
    <Content Include="layui\images\face\52.gif" />
    <Content Include="layui\images\face\53.gif" />
    <Content Include="layui\images\face\54.gif" />
    <Content Include="layui\images\face\55.gif" />
    <Content Include="layui\images\face\56.gif" />
    <Content Include="layui\images\face\57.gif" />
    <Content Include="layui\images\face\58.gif" />
    <Content Include="layui\images\face\59.gif" />
    <Content Include="layui\images\face\6.gif" />
    <Content Include="layui\images\face\60.gif" />
    <Content Include="layui\images\face\61.gif" />
    <Content Include="layui\images\face\62.gif" />
    <Content Include="layui\images\face\63.gif" />
    <Content Include="layui\images\face\64.gif" />
    <Content Include="layui\images\face\65.gif" />
    <Content Include="layui\images\face\66.gif" />
    <Content Include="layui\images\face\67.gif" />
    <Content Include="layui\images\face\68.gif" />
    <Content Include="layui\images\face\69.gif" />
    <Content Include="layui\images\face\7.gif" />
    <Content Include="layui\images\face\70.gif" />
    <Content Include="layui\images\face\71.gif" />
    <Content Include="layui\images\face\8.gif" />
    <Content Include="layui\images\face\9.gif" />
    <Content Include="layui\layui.all.js" />
    <Content Include="layui\layui.all.min.js" />
    <Content Include="layui\layui.js" />
    <Content Include="layui\lay\modules\carousel.js" />
    <Content Include="layui\lay\modules\code.js" />
    <Content Include="layui\lay\modules\colorpicker.js" />
    <Content Include="layui\lay\modules\element.js" />
    <Content Include="layui\lay\modules\flow.js" />
    <Content Include="layui\lay\modules\form.js" />
    <Content Include="layui\lay\modules\jquery.js" />
    <Content Include="layui\lay\modules\laydate.js" />
    <Content Include="layui\lay\modules\layedit.js" />
    <Content Include="layui\lay\modules\layer.js" />
    <Content Include="layui\lay\modules\laypage.js" />
    <Content Include="layui\lay\modules\laytpl.js" />
    <Content Include="layui\lay\modules\mobile.js" />
    <Content Include="layui\lay\modules\rate.js" />
    <Content Include="layui\lay\modules\slider.js" />
    <Content Include="layui\lay\modules\table.js" />
    <Content Include="layui\lay\modules\tree.js" />
    <Content Include="layui\lay\modules\upload.js" />
    <Content Include="layui\lay\modules\util.js" />
    <Content Include="OA_Main.htm" />
    <Content Include="Tech\ExCode.htm" />
    <Content Include="Tech\module\treetable-lay\treetable.css" />
    <Content Include="Tech\module\treetable-lay\treetable.js" />
    <Content Include="Tech\module\treetable.js" />
    <Content Include="Tech\Proc.htm" />
    <Content Include="Tech\ProcAction.htm" />
    <Content Include="Tech\ProductFlow.htm" />
    <Content Include="Tech\SampleSizeCode.htm" />
    <Content Include="Tech\SamplingPlan.html" />
    <Content Include="Tech\SamplingStd.htm" />
    <Content Include="Tech\TestItem.html" />
    <Content Include="Tech\UserWorkCenter.htm" />
    <Content Include="Tech\WorkCenter.htm" />
    <Content Include="UDI\DI.htm" />
    <Content Include="UDI\DIMng.htm" />
    <Content Include="UDI\Serial.htm" />
    <Content Include="UDI\UDIPrint.htm" />
    <Content Include="UDI\UDIPrint_GG.htm" />
    <Content Include="UDI\UDIPrint_IE.htm" />
    <Content Include="UDI\UDIReport.htm" />
    <Content Include="UDI_Main.htm" />
    <Content Include="UpdateFile.aspx" />
    <Content Include="Web.config" />
    <Content Include="Default.html" />
    <Content Include="Login.htm" />
    <Content Include="LoginAuto.htm" />
    <Content Include="LoginGetUser.htm" />
    <Content Include="Service\BaseModuleAjax.ashx" />
    <Content Include="Service\FlowConfigAjax.ashx" />
    <Content Include="Service\LoginAjax.ashx" />
    <Content Include="BaseModule\CodeRule.htm" />
    <Content Include="BaseModule\SysKind.htm" />
    <Content Include="BaseModule\UserRelation.htm" />
    <Content Include="css\bootstrap-theme.css" />
    <Content Include="css\bootstrap-theme.min.css" />
    <Content Include="css\bootstrap.css" />
    <Content Include="css\demos.css" />
    <Content Include="css\index.css" />
    <Content Include="Flow\AudList.htm" />
    <Content Include="Flow\AudMX.htm" />
    <Content Include="Flow\BSDataApply.htm" />
    <Content Include="Flow\BSDataList.htm" />
    <Content Include="Flow\FConfigList.htm" />
    <Content Include="Flow\FConfigMX.htm" />
    <Content Include="fonts\down2.ico" />
    <Content Include="fonts\JS1.png" />
    <Content Include="fonts\JS2.png" />
    <Content Include="fonts\JS3.png" />
    <Content Include="fonts\loading.gif" />
    <Content Include="fonts\MS01.jpg" />
    <Content Include="fonts\MS02.jpg" />
    <Content Include="fonts\MS03.jpg" />
    <Content Include="fonts\MS04.jpg" />
    <Content Include="fonts\MS05.jpg" />
    <Content Include="fonts\MS06.jpg" />
    <Content Include="fonts\MS07.jpg" />
    <Content Include="fonts\MS08.jpg" />
    <Content Include="fonts\MTop.png" />
    <Content Include="fonts\SJ01.gif" />
    <Content Include="fonts\StartBlank.gif" />
    <Content Include="fonts\StartRed.gif" />
    <Content Include="fonts\SX01.gif" />
    <Content Include="fonts\SX02.gif" />
    <Content Include="fonts\TB01.gif" />
    <Content Include="fonts\TB02.gif" />
    <Content Include="fonts\TB03.gif" />
    <Content Include="fonts\TB04.gif" />
    <Content Include="fonts\TB05.gif" />
    <Content Include="images\CodeRule.png" />
    <Content Include="images\Cust.png" />
    <Content Include="images\DateCofirm.png" />
    <Content Include="images\Delivery.png" />
    <Content Include="images\ForeverLogo.png" />
    <Content Include="images\icon_1.png" />
    <Content Include="images\icon_10.png" />
    <Content Include="images\icon_2.png" />
    <Content Include="images\icon_6.png" />
    <Content Include="images\icon_7.png" />
    <Content Include="images\icon_8.png" />
    <Content Include="images\Inquiry.png" />
    <Content Include="images\InquiryA.png" />
    <Content Include="images\machine_bg_b.png" />
    <Content Include="images\MaterNo.png" />
    <Content Include="images\more.jpg" />
    <Content Include="images\pointer.png" />
    <Content Include="images\PReturn.png" />
    <Content Include="images\Purch.png" />
    <Content Include="images\PurchAud.png" />
    <Content Include="images\ReplyDate.png" />
    <Content Include="images\roll.png" />
    <Content Include="images\SHCofirm.png" />
    <Content Include="images\Supplier.png" />
    <Content Include="images\SysKind.png" />
    <Content Include="images\Take.png" />
    <Content Include="images\turnplate-bg.png" />
    <Content Include="images\turnplate-pointer.png" />
    <Content Include="images\turntable-bg.jpg" />
    <Content Include="images\turntable.png" />
    <Content Include="images\yfk.png" />
    <Content Include="images\ysk.png" />
    <Content Include="js\FConfig.js" />
    <Content Include="css\bootstrap-table.css" />
    <Content Include="js\bootstrap-table.min.js" />
    <Content Include="js\jquery-1.9.1.min.js" />
    <Content Include="Service\ApplyUpPicFile.aspx" />
    <Content Include="Service\GetWXOpenID.aspx" />
    <Content Include="UserRegister.htm" />
    <Content Include="Verifier.htm" />
    <Content Include="WebService\GetSerielInfo.asmx" />
    <Content Include="WebService\OperateSerielInfo.asmx" />
    <None Include="packages.config" />
    <Content Include="Service\PrdAssistAjax.ashx" />
    <Content Include="PrdAssist\PrdAssist.zip" />
    <Content Include="Service\EchartAjax.ashx" />
    <Content Include="Service\TimerAjax.ashx" />
    <Content Include="Service\DHRAjax.ashx" />
    <Content Include="Service\ProductPricing.ashx" />
    <Content Include="Service\CompanyPricing.ashx" />
    <Content Include="Service\CompanyCombo.ashx" />
    <Content Include="Service\ActivateService.ashx" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BLL\BLL.csproj">
      <Project>{058230BA-2887-4E74-AAE4-86B827DCC93F}</Project>
      <Name>BLL</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{C19DCC7F-0313-4FF8-A75E-A3B0F8CE4B4E}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Model\Model.csproj">
      <Project>{1A364520-EDAB-4927-BE62-C0D8B2E2487E}</Project>
      <Name>Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\WXCommon\WXCommon.csproj">
      <Project>{45A320F5-D037-465F-B0DD-2A803BF281D8}</Project>
      <Name>WXCommon</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>False</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>24979</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>
          </IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>