﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>入库申请</title>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <script src="../js/jquery-1.9.1.min.js" type="text/javascript"></script>
    <script src="../js/jQuery-2.2.0.min.js" type="text/javascript"></script>
    <script type="text/javascript" src="../js/bstable/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="../js/bstable/js/bootstrap-table.js"></script>


    <!-- layui css -->
    <link href="../js/skin/layer.css" rel="stylesheet" />
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/Order.js" type="text/javascript"></script>
    <script src="../js/grhtml5-6.8-min.js" type="text/javascript"></script>
    <script src="../js/grwebapp.js" type="text/javascript"></script>



    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function () {
            var h = 250;
            //var h1 = 170;
            //$('#dgDataListZS').bootstrapTable('resetView', { height: h });  // 追溯物料
            // $('#dgDataListZX').bootstrapTable('resetView', { height: h1 }); // 装箱物料
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        $('#txtEUserNo').val(parsedJson.Man + "(" + parsedJson.InName + ")");
                        //$('#txtEUserName').val(parsedJson.InName);
                        //$('#txtInStockKind').val("正常入库");
                    }
                }
            })
        });



    </script>


    <script type="text/javascript">
        //导入提示声音
        var errorAudio = new Audio('../images/error.mp3');
        var successAudio = new Audio('../images/success.mp3');

        //  入库申请：扫描仓库信息
        function GetWNo_keydown(event) {

            if (event.keyCode == 13) {
                $('#LMgs').html("");

                var Flag = "32-1";

                var sWNo = $('#txtWNo').val();
                if (sWNo == "") {
                    $('#LMgs').html("请输入入库仓位");
                    playAudio(errorAudio)
                    return;
                }

                var Data = '';
                var Params = { CNo: sWNo, Flag: Flag };
                var Data = JSON.stringify(Params);

                $.ajax({
                    type: "POST",
                    url: "../Service/OrderAjax.ashx?OP=GetInStockWNo&CFlag=" + Flag + "&CNO=" + sWNo,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            var sStr = eval(parsedJson.json)[0];
                            $('#txtOPRec').val("入库仓位描成功，请扫描检验批次。");
                            $('#txtWName').val(parsedJson.Name); // 作业单元名称

                            $("#IsAutoSubmitAndAudit").val(sStr.IsAutoSubmitAndAudit)

                            ResetInputText(1);  // 初始化输入框
                            ShowSNInfo("88888", "2");
                            ShowInStockInfo("88888");

                            var time = new Date();
                            var sM = time.getMonth() + 1;
                            if (sM < 10) {
                                sM = "0" + sM
                            }
                            var sD = time.getDate()
                            if (sD < 10) {
                                sD = "0" + sD
                            }
                            var s1 = time.getFullYear() + "-" + sM + "-" + sD;
                            $('#txtInStockDate').val(s1);
                            playAudio(successAudio)
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_EXIST') {
                            $('#LMgs').html("该库位不存在，请确认！");
                            $("#txtScanNo").attr({ "disabled": "disabled" });
                            playAudio(errorAudio)
                        }
                        else {
                            $("#txtWCUserName").val("");
                            $("#LMgs").html("系统出错，请联系管理员1！")
                            playAudio(errorAudio)
                        }
                    },
                    error: function (data) {
                        $("#LMgs").html("系统出错，请联系管理员2！")
                        playAudio(errorAudio)
                    }
                });
            }  // if (event.keyCode == 13)

        }


        //  入库申请：扫描入库的序列号，工单号检验批次号回车
        function GetScanNo_keydown(event) {

            if (event.keyCode == 13) {
                $('#LMgs').html("");

                

                var sOldNo = $('#txtOPNo').val();   // 当前系统记录的，扫描进来的编号
                var sSNo = $('#txtScanNo').val();  // 扫描的单号：检验批次，序列号，工单
                if (sOldNo != sSNo) {  // 现在扫描进来的号码和当前系统存储的不一致
                    $("#txtInStockNo").val("");
                    $("#txtMaterBatchNo").val("");
                    $("#txtOrderNo").val("");
                    $("#txtOrderKind").val("");
                    $("#txtPMaterNo").val("");
                    $("#txtPMaterName").val("");
                    $("#txtMaterSpec").val("");
                    //$('#txtInStockKind').val("");
                }
                var sInNo = $('#txtInStockNo').val();   // 入库单号
                var sWNo = $('#txtWNo').val();   // 库位编号
                var sWName = $('#txtWName').val();   // 库位
                var sKind = $('#txtInStockKind').val();   // 入库类型
                var sDate = $('#txtInStockDate').val();   // 入库日期
                var sWay = $('#txtInStockWay').val();
                var Flag = "32-2";  // 默认按送检批次入库


                if (sSNo == "") {
                    $('#LMgs').html("请输入或扫描入库单号");
                    playAudio(errorAudio)
                    return;
                }

                if (sWay == "") {
                    $('#LMgs').html("请选择入库方式");
                    playAudio(errorAudio)
                    return;
                }

                if (sWay == "按序列号入库") {
                    Flag = "32-3";
                }
                else if (sWay == "按工单入库") {
                    Flag = "32-4";
                }



                var Data = '';
                var Params = { CNo: sSNo, Item: "", MNo: "", A: sInNo, B: sWay, C: sWNo, D: sWName, E: sKind, F: sDate, G: "", H: "", Flag: Flag };
                var Data = JSON.stringify(Params);

                $.ajax({
                    type: "POST",
                    url: "../Service/OrderAjax.ashx?OP=ScanBatchNo&CFlag=" + Flag + "&CNO=" + sSNo,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            var sStr = eval(parsedJson.json)[0];
                            var stxtOPRec = $('#txtOPRec').val()
                            $('#txtOPRec').val(stxtOPRec + '\n' + sSNo + " 单号扫描成功。");
                            $('#txtScanNo').val("");
                            $('#txtOPNo').val(sSNo);

                            $("#txtInStockNo").val(sStr.InNo);
                            $("#txtMaterBatchNo").val(sStr.MaterBatchNo);
                            $("#txtOrderNo").val(sStr.OrderNo);
                            $("#txtOrderKind").val(sStr.OrderKind);
                            $("#txtPMaterNo").val(sStr.MaterNo);
                            $("#txtPMaterName").val(sStr.MaterName);
                            $("#txtMaterSpec").val(sStr.MaterSpec);
                            //$('#txtInStockKind').val(sStr.InKind);
                            $('#txtInStockDate').val(sStr.ApplyDate);
                            if (sStr.Status == "已入库") {
                                $('#ToK3_Btn').show();
                            }
                            else {
                                $('#ToK3_Btn').hide();
                            }

                            if (sStr.MJDate != null && sStr.MJDate != "") {
                                $('#txtSCRQ').val(sStr.MJDate.split('T')[0]);
                            }

                            if (sStr.FExpPeriod != null && sStr.FExpPeriod != "") {
                                $('#txtGQSJ').val(sStr.FExpPeriod.split('T')[0]);
                            }

                            InSheet_onclick(2)

                            ShowSNInfo(sStr.InNo, "");
                            ShowInStockInfo(sStr.InNo);
                            playAudio(successAudio)
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_ORDERZT') {
                            $('#txtScanNo').val("");
                            $('#LMgs').html("序列号对应的工单已暂停，不可操作");
                            playAudio(errorAudio)
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_EXISTNO') {
                            $('#txtScanNo').val("");
                            $('#LMgs').html("扫描的单号不存在,请检查入库方式是否正确");
                            playAudio(errorAudio)
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_EXISTSN') {
                            $('#txtScanNo').val("");
                            $('#LMgs').html("该扫描单号没有可入库的序列号，请确认");
                            playAudio(errorAudio)
                        }
                        else {
                            $('#txtScanNo').val("");
                            $("#LMgs").html("无入库的序列号信息")
                            playAudio(errorAudio)
                        }
                    },
                    error: function (data) {
                        $('#txtScanNo').val("");
                        $("#LMgs").html("系统出错，请联系管理员2")
                        playAudio(errorAudio)
                    }
                });
            }  // if (event.keyCode == 13)

        }

        // 播放音频的函数
        function playAudio(audio) {
            // 重置音频播放位置
            audio.currentTime = 0;
            // 播放音频
            audio.play();
        }

        //按申请单号，显示序列号入库信息
        function ShowInStockInfo(sInNo) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#InStockList',
                    id: 'InStockID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=236&CNO=' + sInNo,
                    height: '270',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 50, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers', title: '序号', width: 30 },
                        { field: 'MaterNo', title: '物料编码', width: 130 },
                        { field: 'BOMVer', title: 'BOM版本', width: 120 },
                        { field: 'MaterName', title: '物料描述', width: 200 },
                        { field: 'MaterSpec', title: '规格', minWidth: 120 },
                        { field: 'MaterBatchNo', title: '工单/批号', minWidth: 150 },
                        { field: 'InNum', title: '入库数量', minWidth: 90 },
                        { field: 'RegNo', title: '注册证号或备案凭证', minWidth: 150 },
                        { field: 'InMan', title: '入库人', minWidth: 90 },
                        { field: 'Status', title: '状态', minWidth: 70 }
                        //{ field: 'op', title: '操作', width: 80, toolbar: '#barDemo_InStock', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(InStockList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值

                    if (layEvent == 'detail') {

                        document.getElementById('fade').style.display = 'block'
                    }

                });

                //  查询 --入库信息
                $('#InStock_SearchOpen').click(function () {

                    var sInNo = $("#txtInStockNo").val();

                    if (sInNo == "") {
                        return;
                    }

                    var Data = '';
                    var Params = { No: sInNo, Item: "", Name: "", MNo: "", MName: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "" };
                    var Data = JSON.stringify(Params);

                    table.reload('InStockID', {
                        method: 'post',
                        url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=236&Data=' + Data,
                        where: {
                            'No': sInNo
                        }, page: {
                            curr: 1
                        }
                    });
                });



            });

        }


        //扫描批号，工单或序列号后，显示序列号信息
        function ShowSNInfo(sNo, Flag) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#SNList',
                    id: 'SNID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=237&CNO=' + sNo + "&Item=" + Flag,
                    height: '270',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 50, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers', title: '序号', width: 30 },
                        { field: 'BatchNo', title: '序列号', width: 120 },
                        { field: 'MaterNo', title: '物料编码', width: 130 },
                        { field: 'MaterName', title: '物料描述', minWidth: 260 },
                        { field: 'InNum', title: '数量', minWidth: 60 },
                        { field: 'PrdDate', title: '生产日期', minWidth: 90 },
                        { field: 'op', title: '操作', width: 80, toolbar: '#barDemo_SN', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(SNList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值

                    if (layEvent == 'del') {

                        layer.confirm('您确实要删除该记录么？', function (index) {

                            //向服务端发送禁用指令
                            var sInNo = data.InNo;
                            var sMBatch = data.MaterBatchNo;
                            var sSN = data.BatchNo;
                            var sFlag = "32-5";

                            var Data = '';
                            var Params = { No: sMBatch, Name: "", Item: sSN, MNo: "", MName: "", A: sInNo, B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: sFlag };
                            var Data = JSON.stringify(Params);
                            $.ajax({
                                type: "POST",
                                url: "../Service/OrderAjax.ashx?OP=OPInStockInfo&CFlag=" + sFlag,
                                data: { Data: Data },
                                success: function (data) {
                                    var parsedJson = jQuery.parseJSON(data);

                                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                        layer.msg('删除成功！');

                                        $('#InStock_SearchOpen').click();  // 重新查询
                                        $('#InStockSN_SearchOpen').click();  // 重新查询

                                    } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_EXISTSTA') {
                                        layer.msg('已提交入库申请，不能删除！');
                                    }
                                    else {
                                        layer.msg('删除失败，请重试！');
                                    }
                                },
                                error: function (data) {
                                    layer.msg('删除失败2，请重试！');
                                }
                            });

                        }); // 删除

                    }

                });

                //  查询 --入库信息
                $('#InStockSN_SearchOpen').click(function () {

                    var sInNo = $("#txtInStockNo").val();

                    if (sInNo == "") {
                        return;
                    }

                    var Data = '';
                    var Params = { No: sInNo, Item: "", Name: "", MNo: "", MName: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "" };
                    var Data = JSON.stringify(Params);

                    table.reload('SNID', {
                        method: 'post',
                        url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=237&Data=' + Data,
                        where: {
                            'No': sInNo
                        }, page: {
                            curr: 1
                        }
                    });
                });


            });

        }


        //  扫描作业单元，工序完工，重置一下输入框
        function ResetInputText(n) {

            $('#L_Scan').html("请扫描需要入库的单号");
            $("#TRInStock").show();   // 1 入库信息sheet
            $("#TRSN").hide();   // 2 显示序列号sheet
            $("#txtScanNo").removeAttr("disabled");
            $('#txtScanNo').show(); // 显示入库单号的扫描框
            $('#txtScanNo').focus();

            $("#txtScanNo").val("");
            $("#txtInStockNo").val("");
            $("#txtMaterBatchNo").val("");
            $("#txtOrderNo").val("");
            $("#txtOrderKind").val("");
            $("#txtPMaterNo").val("");
            $("#txtPMaterName").val("");
            $("#txtMaterSpec").val("");
            $('#LMgs').html("");
            $('#txtInStockWay').val("按批号入库");
           
            $("#txtSCRQ").val("")
            $("#txtGQSJ").val("")
        }


        function InSheet_onclick(num) {

            if (num == 1) {   // 显示入库信息
                $('#txtAddKind').val("SN");
                $("#TRInStock").show();   // 1 显示库存
                $("#TRSN").hide();   // 1 显示序列号
                $("#InStock_SearchOpen").show();
                $("#InStockSN_SearchOpen").hide();

                document.getElementById("Sheet1").setAttribute("style", "width:50px; background-color:#56dcaf; color: white; text-align:center; font-size:12px;cursor:pointer;");
                document.getElementById("Sheet2").setAttribute("style", "width:50px; background-color:#ccc; color: white; text-align:center; font-size:12px;cursor:pointer; ");
            }
            else if (num == 2)   // 显示序列号信息
            {
                $('#txtAddKind').val("KC");
                $("#TRInStock").hide();   // 1 显示库存
                $("#TRSN").show();   // 2 显示序列号
                $("#InStock_SearchOpen").hide();
                $("#InStockSN_SearchOpen").show();

                document.getElementById("Sheet1").setAttribute("style", "width:50px; background-color:#ccc; color: white; text-align:center; font-size:12px;cursor:pointer;");
                document.getElementById("Sheet2").setAttribute("style", "width:50px; background-color:#56dcaf; color: white; text-align: center; font-size: 12px; cursor:pointer;");
            }

        }


        function ShowInStockDialog() {  // 提交入库申请:
            var sOr = $("#txtInStockNo").val();

            if (sOr == "") {
                $('#LMgs').html("获取不到需要入库申请的单号！");
                return;
            }
            $("#txtOInStockNo").val(sOr);
            $("#div_warningInStock").html("");

            document.getElementById('Div_InStockOver').style.display = 'block';
            // document.getElementById('fade').style.display = 'block';
        }


        function closeDialog() {
            document.getElementById('fade').style.display = 'none';
            document.getElementById('Div_InStockOver').style.display = 'none';

        }




    </script>




    <style type="text/css">


        #light table {
            margin: 0px;
            padding: 0px;
        }

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        /*     调整网格的宽度
                .layui-table-view {
                    width: 89%;
                }
        */

        .table > tbody > tr > td {
            border: 0px;
        }

        .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: #bbbcc7;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=60);
        }

        .LabelDelBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #da542e;
        }

        .LabelAddBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #579fd8;
        }


        .table-responsive {
            overflow-x: hidden;
        }

        .LabelAddBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #56dcaf;
        }
    </style>




</head>
<body>

    <div id="light" style="position: absolute;top: 1px;left:1px;right: 0%;width: 99%;height:98%;border: none; background-color: white;z-index: 1002;overflow: auto;">
        <table cellspacing="0" cellpadding="0" border='0' style="width: 99%; height: 98%; ">
            <tr>
                <td style=" width:30%;" valign=top>
                    <table cellspacing="0" cellpadding="0" border='0' style="width: 100%; ">
                        <tr style=" height:26px;">
                            <td style=" width:100px;" align=right>
                                入库人员：
                            </td>
                            <td>
                                <input type="text" class="form-control" id="txtEUserNo" name="txtEUserNo" readonly=readonly style="height: 25px;" />
                            </td>
                        </tr>
                        <tr style=" height: 27px;">
                            <td style=" width:100px;" align=right>
                                扫描仓位：
                            </td>
                            <td>
                                <input type="text" class="form-control" id="txtWNo" name="txtWNo" onkeydown="GetWNo_keydown(event)" style=" color: Blue; font-weight: bold; font-size: 14px; height: 26px;" />
                            </td>
                        </tr>
                        <tr style=" height: 26px;">
                            <td style=" width:100px;" align=right>
                                仓位名称：
                            </td>
                            <td>
                                <input type="text" class="form-control" id="txtWName" name="txtWName" readonly=readonly style=" color: Blue; font-weight: bold; font-size: 14px; height: 26px;" />
                            </td>
                        </tr>
                        <tr style="height: 26px;">
                            <td style=" width:100px;" align=right>
                                入库方式：
                            </td>
                            <td>
                                <select class="form-control" style="height:25px;" id="txtInStockWay">
                                    <option value="按批号入库">按批号入库</option>
                                    <option value="按序列号入库">按序列号入库</option>
                                    <option value="按工单入库">按工单入库</option>
                                </select>
                            </td>
                        </tr>
                        <tr style=" height: 26px;">
                            <td style=" width:100px;" align=right>
                                扫描信息：
                            </td>
                            <td>
                                <input type="text" class="form-control" id="txtScanNo" name="txtScanNo" onkeydown="GetScanNo_keydown(event)" style=" color: Blue; font-weight: bold; font-size: 14px; height: 26px;" disabled=disabled />
                                <input type="text" class="form-control" id="txtOPNo" name="txtOPNo" readonly=readonly style="height: 25px; display: none;" />
                            </td>
                        </tr>
                        <tr style=" height:20px;">
                            <td style=" width:100px;" align=right>
                            </td>
                            <td>
                                <label id="L_Scan" style=" color:#987165;"></label>
                            </td>
                        </tr>
                        <tr style=" height: 26px;">
                            <td style=" width:100px;" align=right>
                                入库单号：
                            </td>
                            <td>
                                <input type="text" class="form-control" id="txtInStockNo" name="txtInStockNo" style="color: Red; font-weight: bold; height: 26px;" readonly=readonly />
                                <input type="text" class="form-control" id="txtMaterBatchNo" name="txtMaterBatchNo" style="color: Red; font-weight: bold; height: 26px;display:none;" readonly=readonly />
                            </td>
                        </tr>
                        <tr style=" height: 26px;">
                            <td style=" width:100px;" align=right>
                                工单号：
                            </td>
                            <td>
                                <input type="text" class="form-control" id="txtOrderNo" name="txtOrderNo" readonly=readonly style="height: 25px;" />
                            </td>
                        </tr>
                        <tr style=" height: 26px;">
                            <td style=" width:100px;" align=right>
                                工单类型：
                            </td>
                            <td>
                                <input type="text" class="form-control" id="txtOrderKind" name="txtOrderKind" readonly=readonly style="height: 25px;" />
                            </td>
                        </tr>
                        <tr style=" height: 26px;">
                            <td style=" width:100px;" align=right>
                                产品编码：
                            </td>
                            <td>
                                <input type="text" class="form-control" id="txtPMaterNo" name="txtPMaterNo" readonly=readonly style="height: 25px;" />
                            </td>
                        </tr>
                        <tr style=" height: 26px;">
                            <td style=" width:100px;" align=right>
                                产品描述：
                            </td>
                            <td>
                                <input type="text" class="form-control" id="txtPMaterName" name="txtPMaterName" readonly=readonly style="height: 25px;" />
                            </td>
                        </tr>
                        <tr style=" height: 26px;">
                            <td style=" width:100px;" align=right>
                                型号：
                            </td>
                            <td>
                                <input type="text" class="form-control" id="txtMaterSpec" name="txtMaterSpec" readonly=readonly style="height: 25px;" />
                            </td>
                        </tr>
                        <tr style=" height: 26px;">
                            <td style=" width:100px;" align=right>
                                生产类型：
                            </td>
                            <td>
                                <select class="form-control" id="txtSCLX" style="height: 25px;">
                                    <option></option>
                                    <option value="1" selected>主产品</option>
                                    <option value="2">联产品</option>
                                    <option value="3">副产品</option>
                                </select>
                            </td>
                        </tr>
                        <tr style=" height: 26px;">
                            <td style=" width:100px;" align=right>
                                入库类型：
                            </td>
                            <td>
                                <select class="form-control" id="txtInStockKind" style="height: 25px;">
                                    <option></option>
                                    <option value="1" selected>合格品入库</option>
                                    <option value="2">不合格入库</option>
                                    <option value="3">报废品入库</option>
                                </select>
                            </td>
                        </tr>
                        <tr style=" height: 26px;">
                            <td style=" width:100px;" align=right>
                                生产日期：
                            </td>
                            <td>
                                <input type="date" class="form-control" id="txtSCRQ" name="txtSCRQ" style="height:25px;" />
                            </td>
                        </tr>
                        <tr style=" height: 26px;">
                            <td style=" width:100px;" align=right>
                                过期日期：
                            </td>
                            <td>
                                <input type="date" class="form-control" id="txtGQSJ" name="txtGQSJ" style="height:25px;" />
                            </td>
                        </tr>
                        <tr style=" height: 26px;">
                            <td style=" width:100px;" align=right>
                                入库日期：
                            </td>
                            <td>
                                <input type="date" class="form-control" id="txtInStockDate" name="txtInStockDate" style="height:25px;" />
                            </td>
                        </tr>
                        <tr style=" height: 26px;">
                            <td colspan=2 align=center>
                                <table cellspacing="0" cellpadding="0" border='0' style="width: 99%;">
                                    <tr>
                                        <td>
                                            <input type="checkbox" value="true" id="txtIsReverseMaterial" name="txtIsReverseMaterial" style="height:26px;" />
                                        </td>
                                        <td>
                                            是否倒冲领料
                                        </td>
                                        <td>
                                            <input type="checkbox" value="true" id="txtIsAccomplish" name="txtIsAccomplish" style="height:26px;" />
                                        </td>
                                        <td>
                                            是否完工
                                        </td>
                                        <td>
                                            <input type="checkbox" value="true" id="txtIsBackWms" name="txtIsBackWms" style="height:26px;" />
                                        </td>
                                        <td>
                                            WMS已回传
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr style=" height: 28px;">
                            <td colspan=2 align=center>
                                <input type="button" value="入库申请" class="find_but" id="InStock_Btn" onclick="ShowInStockDialog()" style=" width:75px; height:28px;" />

                                <input type="button" value="同步K3" class="find_but" id="ToK3_Btn" style=" width:75px; height:28px; display:none;" />
                            </td>
                        </tr>
                    </table>
                </td>
                <td style=" width:70%;" valign=top align=left>
                    <table cellspacing="0" cellpadding="0" border='1' style="width:100%; ">
                        <tr style=" height:25px;">
                            <td>
                                操作记录：<label id="LMgs" style=" color:Red;"></label>
                            </td>
                        </tr>
                        <tr style=" height:190px;">
                            <td>
                                <textarea class="form-control" id="txtOPRec" name="txtOPRec" style="height:190px; background-color:White;width:99%;" readonly=readonly> </textarea>
                            </td>
                        </tr>
                        <tr style=" height:5px;">
                            <td>
                            </td>
                        </tr>
                        <tr style=" height:35px;">
                            <td>
                                <table cellspacing="0" cellpadding="0" border='0' style="width: 100%; border: 1px solid #c7c2c2; ">
                                    <tr style=" height:30px;">
                                        <td id="Sheet1" onclick="InSheet_onclick(1)" style="width: 50px; background: #56dcaf; color: white; text-align: center; font-size: 12px;cursor:pointer;">
                                            入库信息
                                        </td>
                                        <td id="Sheet2" onclick="InSheet_onclick(2)" style=" width: 50px; background: #ccc; color:White; text-align: center; font-size: 12px;cursor:pointer; ">
                                            序列号详情
                                        </td>
                                        <td style=" width:80%;">
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                            <input type="button" value="刷新" class="find_but" id="InStock_SearchOpen" style=" width:55px; height:25px;" />
                                            <input type="button" value="刷新" class="find_but" id="InStockSN_SearchOpen" style=" width:55px; height:25px;display:none;" />
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <!-- 1 入库信息-->
                        <tr id="TRInStock" style=" height:270px; ">
                            <td style="width:100%; ">
                                <div class="wangid_conbox" style="width:720px;">
                                    <!-- 下面写内容   -->
                                    <table class="layui-hide" id="InStockList" lay-filter="InStockList"></table>
                                    <script type="text/html" id="barDemo_InStock">
                                        <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
                                        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del888888" style="background-color:white;">.</a>
                                    </script>

                                </div>
                            </td>
                        </tr>
                        <tr id="TRSN" style=" height:270px;display:none; ">
                            <td style="width:98%; ">
                                <div class="wangid_conbox">
                                    <!-- 下面写内容   -->
                                    <table class="layui-hide" id="SNList" lay-filter="SNList"></table>
                                    <script type="text/html" id="barDemo_SN">
                                        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                                        <a class="layui-btn layui-btn-xs" lay-event="detail" style="background-color:white;">.</a>
                                    </script>

                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>

    </div>



    <div id="Div_InStockOver" style="display: none;position: absolute;top: 20%;left: 25%;right: 25%;width: 50%;height: 200px;border: 2px solid orange; background-color: white;z-index: 1002;overflow: auto;">
        <div style="background-color: #26d0a1; height: 40px; ">
            <label id="L_InStock" style="font-size: 14px; color:White; padding:10px;">*&nbsp;您确定需要入库码？</label>
        </div>
        <div id="div_warningInStock" role="alert" style="text-align: center;color: Red ">
            <strong id="divsuccessInStock" style="color: Red"></strong>
        </div>
        <div class="input-group" style=" width:98%;">
            <span class="input-group-addon">入库单号：</span>
            <input type="text" class="form-control" id="txtOInStockNo" name="txtOInStockNo" style="color: Red; font-weight: bold; height: 26px;" readonly=readonly />
        </div>
        <br />
        <div align="center">
            <input type='button' id="InStockOver_Btn" value='提交' class="find_but" />
            <input type='button' id="Button2" value='取消' onclick='closeDialog()' class="find_but" />
        </div>
    </div>




    <div class="input-group" style="display:none; ">
        <span class="input-group-addon">.</span>
        <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
        <span class="input-group-addon">.</span>
        <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
        <span class="input-group-addon">.</span>
        <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
        <span class="input-group-addon">.</span>
        <input type="text" class="form-control" id="txtInName" name="txtInName" />
        <span class="input-group-addon">.</span>
        <input type="text" class="form-control" id="IsAutoSubmitAndAudit" name="IsAutoSubmitAndAudit" />
    </div>


    <div id="fade" class="black_overlay">
    </div>

</body>
</html>