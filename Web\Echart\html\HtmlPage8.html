﻿<!DOCTYPE html
          PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title></title>
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/echarts.js"></script>
    <script>
        $(function () {
            init()
        })

        // 初始化函数
        function init() {
            const data = sessionStorage.getItem("KB0006");
            let lineBodyData = []; // 已完成数据
            let deviceUseData = []; // 未完成数据
            let deviceValidityData = []; // 未完成数据
            let lineBodyUseData = []; // 未完成数据

            // 校验并解析数据
            if (data) {
                try {
                    const JsonData = JSON.parse(data);
                    lineBodyData = JsonData[0] || [];
                    deviceUseData = JsonData[1] || [];
                    deviceValidityData = JsonData[2] || [];
                    lineBodyUseData = JsonData[3] || [];
                } catch (error) {
                    console.error("数据解析失败:", error);
                }
            }

            // 渲染已完成和未完成的图表
            RenderChart(lineBodyData, 'ChartOne', '线体利用占比', true);
            RenderChart(deviceUseData, 'ChartTow', '设备利用占比', true);
            RenderChart(deviceValidityData, 'ChartThree', '设备有效期', true);
            RenderList(lineBodyUseData);
        }

        /**
         * 通用图表渲染函数
         * @param {Array} data - 图表数据
         * @param {string} chartId - 图表容器的ID
         * @param {boolean} isColor - 是否需要设置颜色（默认为false）
         */
        function RenderChart(data, chartId, title, isColor = false) {
            if (!data || data.length === 0) {
                console.warn(`没有数据提供给图表 ${chartId}`);
                return;
            }

            if (isColor) {
                data = addItemStyle(data)
            }

            // 初始化图表
            const chart = echarts.init(document.getElementById(chartId));
            chart.setOption({
                tooltip: {
                    trigger: 'item'
                },
                series: [
                    {
                        name: title,
                        type: 'pie',
                        radius: ['40%', '65%'],
                        data: data,
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        label: {
                            show: true,
                            //position: "outside",
                            formatter(param) {
                                return (
                                    param.name + " " +
                                    param.value + "个(" +
                                    param.percent + "%)"
                                );
                            },
                            fontSize: "12px",
                            backgroundColor: "transparent",
                            color: "white",
                            fontWeight: "bold",
                        },
                    }
                ]
            });

            // 窗口调整时重新渲染图表
            $(window).resize(() => chart.resize());
        }

        function RenderList(data) {
            let str = "";
            data.forEach(item => {
                let val = (item.UseCount / item.SumCount) * 100;
                str += "<div class='" + (item.UseCount == 0 ? "red" : "green") + "'>" + item.LineBodyName + " " + "(" + item.SumCount + "/" + item.UseCount + ") 利用率:" + val.toFixed(2) + "%</div>";
            })
            $("#div_any_body").append(str)
        }

        // 处理函数：动态添加 itemStyle
        function addItemStyle(data) {

            const colors = [
                '#5bc0de', '#0b3c51', "#ffffff", "#FF7F50", "#98FF98", "#00BFFF", "#FFA500", "#B2FFCC", "#87CEEB", "#7CCD7C",
                "#FFD700", "#FF99CC", "#E6E6FF", "#C7B8EA", "#A9A9A9", "#B8860B", "#FFE4B3", "#6495ED", "#8B4513",
                "#FF669D", "#4D4D4D", "#B5C59D", "#FF6347", "#008B8B", "#adff2f", "#ee82ee",
            ];

            // 使用 map 返回新数组
            return data.map((item, index) => ({
                ...item, // 保留原数据
                itemStyle: { color: colors[index] } // 动态添加 itemStyle，循环使用颜色
            }));
        }


    </script>

    <style>
        * {
            margin: 0;
            padding: 0;
            list-style: none;
            text-decoration: none;
        }

        .container-div {
            height: 100vh;
            background: #15181d;
            overflow: hidden;
            position: relative;
            color: #ffffff;
        }

        .container-top {
            height: 40%;
            display: flex;
        }

        .container-bottom {
            margin-top: 1.5%;
            height: 60%;
        }


        .container-top-left {
            width: 33%;
            height: 100%;
            margin-right: 0.5%
        }

        .container-top-center {
            width: 33%;
            height: 100%;
            margin-right: 0.5%
        }

        .container-top-right {
            width: 33%;
            height: 100%
        }

        .left {
            float: left;
        }

        .div_any01 {
            width: 23%;
            margin-right: 2%;
        }

        .div_any_child {
            width: 100%;
            height: 330px;
            box-shadow: -5px 0px 10px #034c6a inset, 0px -10px 10px #034c6a inset, 5px 0px 10px #034c6a inset, 0px 10px 10px #034c6a inset;
            border: 1px solid #034c6a;
            box-sizing: border-box;
            position: relative;
            margin-top: 15px;
        }

        .div_any_title {
            background-color: #034c6a;
            border-radius: 18px;
            position: absolute;
            height: 25px;
            width: 60%;
            top: -15px;
            color: #ffffff;
            font-weight: bold;
            font-size: 16px;
            left: 20%;
            line-height: 25px;
            text-align: center;
        }

        .div_any_body {
            padding: 1% 0 1% 1%;
            width: 99%;
            height: 100%;
        }

        #div_any_body {
            display: flex; /* 使用 Flexbox 布局 */
            flex-wrap: wrap; /*允许换行 */
            height: 93%;
            width: 100%;
            overflow: auto;
        }

            #div_any_body div {
                margin-right: 1%;
                padding: 0.5%;
                width: 23%;
                margin-top: 0.5%;
                display: flex;
                align-items: center; /* 垂直居中 */
                justify-content: center; /* 水平居中 */
            }


            /* 自定义滚动条样式 */
            #div_any_body::-webkit-scrollbar {
                width: 4px;
                height: 10px;
            }

            /* 滚动条滑块样式 */
            #div_any_body::-webkit-scrollbar-thumb {
                background-color: #edf2fa;
                border-radius: 5px;
            }

        .red {
            background-color: #0b3c51;
        }

        .green {
            background-color: #5ec8e7
        }
    </style>
</head>


<body>
    <div class="container-div">

        <div class="container-top">
            <div class="container-top-left">
                <div class="left div_any01" style="width:100%;height:100%">
                    <div class="div_any_child" style="height: 100%;">
                        <div class="div_any_title">线体利用占比</div>
                        <div id="ChartOne" style="width:100%;height:100%"></div>
                    </div>
                </div>
            </div>
            <div class="container-top-center">
                <div class="left div_any01" style="width:100%;height:100%">
                    <div class="div_any_child" style="height: 100%;">
                        <div class="div_any_title">设备利用占比</div>
                        <div id="ChartTow" style="width:100%;height:100%"></div>
                    </div>
                </div>
            </div>
            <div class="container-top-right">
                <div class="left div_any01" style="width:100%;height:100%">
                    <div class="div_any_child" style="height: 100%;">
                        <div class="div_any_title">设备有效期</div>
                        <div id="ChartThree" style="width:100%;height:100%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-bottom">
            <div class="left div_any01" style="width:100%;height:96%">
                <div class="div_any_child" style="height: 96%;">
                    <div class="div_any_title">线体使用情况</div>
                    <div class="div_any_body">
                        <div id="div_any_body"> </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>