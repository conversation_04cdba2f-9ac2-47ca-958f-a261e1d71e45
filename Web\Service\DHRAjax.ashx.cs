﻿using BLL;
using grsvr6Lib;
using iTextSharp.text.pdf;
using iTextSharp.text;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.SessionState;
using Web.appcode;
using Common;
using System.Web.Services.Description;
using System.Threading.Tasks;
using NPOI.SS.UserModel;
using NPOI.HSSF.UserModel;
using NPOI.SS.Util;
using NPOI.SS.Formula.Functions;
using Model;

namespace Web.Service
{
    /// <summary>
    /// DHRAjax 主要用于DHR相关操作、生成、合并......
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class DHRAjax : IHttpHandler, IRequiresSessionState
    {

        string sAMFlag = string.Empty;
        string IP = string.Empty;

        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "text/plain";
            string Result = string.Empty;
            string Operate = context.Request.Params["OP"];
            string DataParams = context.Request.Params["Data"];
            sAMFlag = context.Request.Params["CFlag"];
            IP = context.Request.ServerVariables.Get("Remote_Addr").ToString().Trim();  //获取IP 地址

            switch (Operate)
            {
                case "GetExportConditions":  // 作业执行：打印DHR报表，获取序列号对应的工序
                    GetExportConditions(DataParams, context);
                    break;

                case "GetExportData":     // 作业执行：打印DHR报表，循环每个序列号下的工序，打印DHR报表
                    GetExportData(DataParams, context);
                    break;

                case "DHRMergeOP": //    合并DHR 
                    DHRMergeOP(DataParams, context);
                    break;
                case "CheckDHR": //    检查DHR 
                    CheckDHR(DataParams, context);
                    break;
                case "ExportDHRCreateInfo": //    检查DHR 
                    ExportDHRCreateInfo(DataParams, context);
                    break;
                case "CreateDHRPDF":
                    CreateDHRPDF(DataParams, context);
                    break;


            }
        }

        #region 找出需要生成DHR
        public void GetExportConditions(string DataParams, HttpContext context)
        {
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string Result = string.Empty;
            DataTable dt = new DataTable();

            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(DataParams);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                context.Response.Write(JsonConvert.SerializeObject(new { Msg = "LoginError", Login = sManName }));
                return;
            }

            if (It.Flag == "3")//关闭工单
            {
                Result = DHRBll.JudgeObjectExist(It.Item, "", "", sComp, "1");//It.Item 工单
                if (Result == "Y")  // 判断工单的状态是否已关闭、已完成
                {
                    Result = JsonConvert.SerializeObject(new { Msg = "Y_WOCLOSE" });
                    context.Response.Write(Result);
                    return;
                }
            }
            else if (It.Flag == "18-1")//关闭序列号
            {
                Result = DHRBll.JudgeObjectExist(It.Item, It.No, "", sComp, "2");//It.Item 工单 It.No 序列号
                if (Result == "Y")  // 判断序列号的状态是否已完成
                {
                    Result = JsonConvert.SerializeObject(new { Msg = "Y_SNCLOSE" });
                    context.Response.Write(Result);
                    return;
                }
            }
            else if (It.F == "JLZX")
            {
                Result = DHRBll.JudgeObjectExist(It.Item, It.No, It.E, sComp, "3");//It.Item 工单 It.No 序列号
                if (Result == "N")  // 判断序列号的状态是否已完成
                {
                    Result = JsonConvert.SerializeObject(new { Msg = "Y_EXIST" });
                    context.Response.Write(Result);
                    return;
                }
            }

            try
            {
                dt = DHRBll.GetExportConditions(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.Remark, It.Flag, sMan, sComp, IP);
                context.Response.Write(JsonConvert.SerializeObject(new { Msg = "Success", Data = dt, ExceptionMessage = "" }));
            }
            catch (Exception ex)
            {
                context.Response.Write(JsonConvert.SerializeObject(new { Msg = "Error", Data = dt, ExceptionMessage = ex.Message.ToString() }));
            }
        }
        #endregion

        #region 生成文档
        public void GetExportData(string DataParams, HttpContext context)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;

            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(DataParams);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Result = JsonConvert.SerializeObject(new { Path = "", Msg = "LoginError", Filename = "", SerialNo = It.No, ProcedureName = It.A, DHRType = It.E, ExceptionMessage = "登录超时，请重新登录" });
                context.Response.Write(Result);
                return;
            }

            Dictionary<string, object> list = DHRBll.GetExportData(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.Remark, It.Flag, sMan, sComp, IP);
            // 如果数量为0就说明获取生成数据发生错误
            if (list.Count > 0)
            {
                // 格式化，符合锐浪报表的数据格式
                Result = JsonConvert.SerializeObject(list);

                GridppReportServer Report = new GridppReportServer();
                IGRBinaryObject PDFDataObject = null;

                //首先载入报表模板文件
                string ReportPathFile = string.Empty;
                //文件名称
                string fileName = string.Empty;
                //时间戳
                string time = DateTime.Now.ToString("yyMMddhhmmssfff");

                //如果是等于DHR就是DHR报表，否则就是汇总报表
                if (It.E == "DHR")
                {
                    //使用服务器的相对路径
                    ReportPathFile = context.Server.MapPath("/Template/" + sComp + "/LabelPrint/DHR.grf");//根据WEB服务器根目录寻址

                    //DHR文件名称 顺序号 - 工序名称 时间戳
                    fileName = It.D + "-" + It.A + "(" + time + ")" + ".pdf";
                }
                else if (It.E == "HZ")
                {
                    //使用服务器的相对路径
                    ReportPathFile = context.Server.MapPath("/Template/" + sComp + "/LabelPrint/DHR(HZ).grf");//根据WEB服务器根目录寻址

                    //汇总页文件名称 序列号 时间戳
                    fileName = It.No + "(" + time + ")" + ".pdf";
                }
                else if (It.E == "WX")
                {
                    //使用服务器的相对路径
                    ReportPathFile = context.Server.MapPath("/Template/" + sComp + "/LabelPrint/WX.grf");//根据WEB服务器根目录寻址

                    //维修dhr 顺序号 - 工序名称 时间戳
                    fileName = It.D + "-" + It.A + "(" + time + ")" + ".pdf";
                }

                //获取报表导入到结果判断是否成功
                bool Success = Report.LoadFromFile(ReportPathFile);
                if (!Success)
                {
                    ServerUtility.ResponseException(context, "", "Error", "", It.No, It.A, It.E, "报表模板导入失败");
                    return;
                }

                try
                {
                    //载入报表数据，为约定格式的 XML 或 JSON 文本数据
                    Report.LoadDataFromXML(Result);

                    //生成PDF文档数据
                    PDFDataObject = Report.ExportDirectToBinaryObject(GRExportType.gretPDF);
                }
                catch (Exception)
                {
                    ServerUtility.ResponseException(context, "", "Error", "", It.No, It.A, It.E, "生成报表发生错误");
                    return;
                }

                //将生成的数据响应给客户端
                if (PDFDataObject.DataSize > 0)
                {
                    ServerUtility.ResponseBinary(context, PDFDataObject, fileName, It.Item, It.No, It.B, It.A, It.D, IP, It.E, It.MNo, It.Name, It.Remark, sComp, sMan, "application/pdf", "attachment", "ZY"); //attachment ： inline
                }
                else
                {
                    ServerUtility.ResponseException(context, "", "Error", "", It.No, It.A, It.E, "生成报表发生错误");
                }
            }
            else
            {
                ServerUtility.ResponseException(context, "", "Error", "", It.No, It.A, It.E, "获取生成数据发生错误");
            }
        }
        #endregion

        #region 多份DHR合并
        public void DHRMergeOP(string DataParams, HttpContext context)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            DataTable dt = new DataTable();

            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(DataParams);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                context.Response.Write(JsonConvert.SerializeObject(new { Msg = "LoginError", Login = sManName }));
                return;
            }

            try
            {
                dt = DHRBll.GetExportConditions(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.Remark, It.Flag, sMan, sComp, IP);
                if (dt.Rows.Count == 0)
                {
                    context.Response.Write(JsonConvert.SerializeObject(new { Msg = "NullFile", Path = sManName }));
                    return;
                }

                string fileName = DateTime.Now.ToString("yyMMddhhmmssfff") + ".pdf";

                string xuPath = "/DHRFile/DHRMerge/";

                if (!Directory.Exists(context.Server.MapPath(xuPath)))
                {
                    Directory.CreateDirectory(context.Server.MapPath(xuPath));
                }

                string path = context.Server.MapPath(xuPath) + fileName;

                MergePdfFiles(context, dt, path, sComp, sMan, It.Name);

                context.Response.Write(JsonConvert.SerializeObject(new { Msg = "Success", Path = xuPath + fileName, ExceptionMessage = "" }));
            }
            catch (Exception ex)
            {
                context.Response.Write(JsonConvert.SerializeObject(new { Msg = "Error", Path = "", ExceptionMessage = ex.Message.ToString() }));
            }
        }

        public void MergePdfFiles(HttpContext context, DataTable dt, string outputFile, string sComp, string sMan, string Name)
        {
            // 创建一个新的PDF文档对象
            Document document = null;
            PdfCopy writer = null;
            FileStream outputStream = null;
            string path = string.Empty;
            try
            {
                document = new Document();
                outputStream = new FileStream(outputFile, FileMode.Create);
                writer = new PdfCopy(document, outputStream);
                document.Open();

                // 遍历所有要合并的PDF文件
                foreach (DataRow file in dt.Rows)
                {
                    path = context.Server.MapPath(file["FilePath"].ToString());
                    // 使用using语句确保PdfReader在使用后被正确关闭
                    using (PdfReader reader = new PdfReader(path))
                    {
                        // 将源文件的每一页复制到目标文档中
                        for (int i = 1; i <= reader.NumberOfPages; i++)
                        {
                            PdfImportedPage page = writer.GetImportedPage(reader, i);
                            writer.AddPage(page);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.LogError("获取", "DHR合并发生错误！原因：" + ex.Message.ToString(), IP, sComp, sMan, Name);
                throw ex;
            }
            finally
            {
                // 使用finally块确保资源被正确关闭
                if (document != null)
                {
                    document.Close();
                }
                if (writer != null)
                {
                    writer.Close();
                }
                if (outputStream != null)
                {
                    outputStream.Close();
                }
            }
        }
        #endregion

        #region 检查DHR生成情况
        public void CheckDHR(string DataParams, HttpContext context)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            int SumCount = 0;
            int limit = Convert.ToInt32(context.Request["limit"]);
            int page = Convert.ToInt32(context.Request["page"]);
            DataTable dt = new DataTable();


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(DataParams);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                context.Response.Write(JsonConvert.SerializeObject(new { Msg = "LoginError", Login = sManName }));
                return;
            }

            dt = DHRBll.CheckDHR(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.Remark, It.Flag, limit, page, sMan, sComp, IP);

            foreach (DataRow row in dt.Rows)
            {
                bool exists = false;
                try
                {
                    string filePath = context.Server.MapPath(row["FilePath"].ToString());//服务器路径
                    exists = File.Exists(filePath);
                }
                catch (Exception ex)
                {
                    exists = false;
                }

                row["IsExist"] = exists ? "是" : "否";
            }

            if (dt.Rows.Count > 0)
            {
                Message = "Success";
                SumCount = Convert.ToInt32(dt.Rows[0]["NumCount"]);
                Result = JsonConvert.SerializeObject(dt);
            }
            else
            {
                Message = "Error";
            }

            Result = "{\"code\":0,\"msg\":\"\",\"count\":" + SumCount + ",\"data\":" + Result + "}";

            context.Response.Write(Result);
            return;
        }

        /// <summary>
        /// 导出DHR生成情况
        /// </summary>
        /// <param name="DataParams"></param>
        /// <param name="context"></param>
        public void ExportDHRCreateInfo(string DataParams, HttpContext context)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            DataTable dt = new DataTable();


            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(DataParams);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                context.Response.Write(JsonConvert.SerializeObject(new { Msg = "LoginError", Login = sManName }));
                return;
            }

            dt = DHRBll.CheckDHR(It.No, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, It.E, It.F, It.Remark, It.Flag, 50000, 1, sMan, sComp, IP);

            foreach (DataRow row in dt.Rows)
            {
                bool exists = false;
                try
                {
                    string filePath = context.Server.MapPath(row["FilePath"].ToString());//服务器路径
                    exists = File.Exists(filePath);
                }
                catch (Exception ex)
                {
                    exists = false;
                }

                row["IsExist"] = exists ? "是" : "否";
            }


            IWorkbook workbook = new HSSFWorkbook();
            // 文件保存优化
            const string SAVE_PATH = "/RecordFile/CheckDHR/";
            string saveDir = context.Server.MapPath(SAVE_PATH);
            string fileName = It.Item + "_" + DateTime.Now.ToString("yyMMddhhmmssfff") + ".xls";

            try
            {
                ISheet sheet = workbook.CreateSheet("检测结果");
                IRow iRow = sheet.CreateRow(0);  // 创建第一行，标题
                iRow.CreateCell(0).SetCellValue("工单");
                iRow.CreateCell(1).SetCellValue("序列号");
                iRow.CreateCell(2).SetCellValue("工序");
                iRow.CreateCell(3).SetCellValue("文件类型");
                iRow.CreateCell(4).SetCellValue("是否生成");

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    // 创建其他行
                    iRow = sheet.CreateRow(i + 1);
                    iRow.CreateCell(0).SetCellValue(dt.Rows[i]["OrderNo"].ToString());
                    iRow.CreateCell(1).SetCellValue(dt.Rows[i]["SerielNo"].ToString());
                    iRow.CreateCell(2).SetCellValue(dt.Rows[i]["ProcedureName"].ToString());
                    iRow.CreateCell(3).SetCellValue(dt.Rows[i]["DHRType"].ToString());
                    iRow.CreateCell(4).SetCellValue(dt.Rows[i]["IsExist"].ToString());
                }

                Directory.CreateDirectory(saveDir);  // 自动创建不存在的目录

                string fullPath = Path.Combine(saveDir, fileName);

                using (FileStream fs = new FileStream(fullPath, FileMode.Create))
                {
                    workbook.Write(fs);
                }

                context.Response.Write(JsonConvert.SerializeObject(new
                {
                    Msg = "Success",
                    Data = dt,
                    FileInfo = new
                    {
                        VirtualPath = SAVE_PATH,
                        FileName = fileName
                    },
                }));

            }
            catch (Exception ex)
            {
                context.Response.Write(JsonConvert.SerializeObject(new
                {
                    Msg = "Error",
                    ExceptionMsg = "生成检查记录发错误。错误原因" + ex.Message.ToString()
                })); ;
            }
            finally
            {
                if (workbook != null)
                {
                    workbook.Close();
                }
            }

            return;
        }

        #endregion

        #region 生成DHR文档
        public void CreateDHRPDF(string DataParams, HttpContext context)
        {
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sComp = string.Empty;
            string Result = string.Empty;
            string Message = "Success";
            string exceptionMessage = string.Empty;
            string SNo = string.Empty;
            string sE = string.Empty;
            string sF = string.Empty;
            string sFlag = string.Empty;

            var AnonymousUser = new
            {
                No = String.Empty,
                Name = String.Empty,
                Item = String.Empty,
                MNo = String.Empty,
                MName = String.Empty,
                A = String.Empty,
                B = String.Empty,
                C = String.Empty,
                D = String.Empty,
                E = String.Empty,
                F = String.Empty,
                Remark = String.Empty,
                Flag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(DataParams);
            var It = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                context.Response.Write(JsonConvert.SerializeObject(new { Msg = "LoginError", Login = sManName }));
                return;
            }

            SNo = It.No;
            sE = It.E;
            sF = It.F;
            sFlag = It.Flag;

            if (It.Flag == "GBWO")//关闭工单
            {
                Result = DHRBll.JudgeObjectExist(It.Item, "", "", sComp, "1");//It.Item 工单
                if (Result == "Y")  // 判断工单的状态是否已关闭、已完成
                {
                    Result = JsonConvert.SerializeObject(new { Msg = "Y_WOCLOSE" });
                    context.Response.Write(Result);
                    return;
                }
            }
            else if (It.Flag == "GBSN")//关闭序列号
            {
                SNo = SNo.Replace("'", "");
                Result = DHRBll.JudgeObjectExist(It.Item, It.No, "", sComp, "2");//It.Item 工单 It.No 序列号
                if (Result == "Y")  // 判断序列号的状态是否已完成
                {
                    Result = JsonConvert.SerializeObject(new { Msg = "Y_SNCLOSE" });
                    context.Response.Write(Result);
                    return;
                }
            }
            else if (It.D == "JLZX" && It.Flag == "GXWG")
            {
                Result = DHRBll.JudgeObjectExist(It.Item, It.No, It.E, sComp, "3");//It.Item 工单 It.No 序列号 It.E 顺序号
                if (Result == "N")  // 判断序列号的状态是否已完成
                {
                    Result = JsonConvert.SerializeObject(new { Msg = "Y_EXIST" });
                    context.Response.Write(Result);
                    return;
                }

                // 记录在线更改判断是不是维修工序
                Result = DHRBll.JudgeObjectExist(It.Item, It.No, It.E, sComp, "4");//It.Item 工单 It.No 序列号 It.E 顺序号
                if (Result == "Y")  
                {
                    sFlag = "WX";
                    //找到维修工序对应得不合格处理单
                    sF = DHRBll.JudgeObjectExist(It.Item, It.No, It.E, sComp, "5");//It.Item 工单 It.No 序列号 It.B 顺序号
                    if (string.IsNullOrEmpty(sF))
                    {
                        Result = JsonConvert.SerializeObject(new { Msg = "N_BHGCLD" });
                        context.Response.Write(Result);
                        return;
                    }
                }
            }
            else if (It.Flag == "WX")
            {
                //找到维修工序得顺序号
                sE = DHRBll.JudgeObjectExist(It.Item, It.No, It.B, sComp, "6");//It.Item 工单 It.No 序列号 It.B 工序编号
                if (string.IsNullOrEmpty(sE))
                {
                    Result = JsonConvert.SerializeObject(new { Msg = "N_FLOWNO" });
                    context.Response.Write(Result);
                    return;
                }
            }

            Dictionary<string, DataTable> list = DHRBll.CreateDHRPDF(SNo, It.Name, It.Item, It.MNo, It.MName, It.A, It.B, It.C, It.D, sE, sF, It.Remark, sFlag, sMan, sComp, IP);

            if (!list.ContainsKey("Table"))
            {
                Result = JsonConvert.SerializeObject(new { Msg = "N_DHR" });
                context.Response.Write(Result);
                return;
            }


            //首先载入报表模板文件
            string ReportPathFile = string.Empty;
            //文件名称
            string fileName = string.Empty;

            string defaultPath = context.Server.MapPath("/UpPicFile/Default/empty.png");

            Random random = new Random();
            GridppReportServer Report = new GridppReportServer();
            IGRBinaryObject PDFDataObject = null;

            List<string> SQLStringList = new List<string>();
            List<OperationResult> result = new List<OperationResult>();

            foreach (DataRow item in list["Table"].Rows)
            {
                string opFlag = Convert.ToString(item["OPFlag"]);
                string orderNo = Convert.ToString(item["OrderNo"]);
                string serialNo = Convert.ToString(item["SerialNo"]);
                string flowOrder = Convert.ToString(item["FlowOrder"]);
                string procedureName = Convert.ToString(item["ProcedureName"]);
                string procedureNo = Convert.ToString(item["procedureNo"]);
                string procedureVer = Convert.ToString(item["ProcedureVer"]);
                string productNo = Convert.ToString(item["ProductNo"]);
                string exeNo = Convert.ToString(item["EXENo"]);
                string time = DateTime.Now.ToString("yyMMddhhmmssfff");
                string FileNo = time + random.Next(100, 1000);
                try
                {

                    switch (opFlag)
                    {
                        case "DHR":
                            //使用服务器的相对路径
                            ReportPathFile = context.Server.MapPath("/Template/" + sComp + "/LabelPrint/DHR.grf");
                            //DHR文件名称 顺序号 - 工序名称 时间戳
                            fileName = flowOrder + "-" + procedureName + "(" + time + ")" + ".pdf";

                            var baseInfo = GetDataList<BaseInfo>(list["Table1"], $"SerielNo='{serialNo}' and FlowOrder='{flowOrder}'", "");
                            var deviceInfo = GetDataList<DeviceInfo>(list["Table2"], $"BatchNo='{serialNo}'  and EXENo='{exeNo}'", "");
                            var materInfo = GetDataList<MaterialInfo>(list["Table3"], $"BatchNo='{serialNo}' and EXENo='{exeNo}'", defaultPath);
                            var testItemInfo = GetDataList<TestItemInfo>(list["Table4"], $"BatchNo='{serialNo}' and EXENo='{exeNo}'", defaultPath);
                            var testItemEditInfo = GetDataList<TestItemEditLog>(list["Table5"], $"SerialNo='{serialNo}' and EXENo='{exeNo}'", defaultPath);
                            var inspectionInfo = GetDataList<Inspection>(list["Table6"], $"SerialNo='{serialNo}'", "");
                            var FinalConclusion = testItemInfo.Count == 0 ? "/" : testItemInfo.Any(x => x.TestResult == "NG" || x.TestResult == "FAIL" || x.TestResult == "NO") ? "不通过" : "通过";

                            // 格式化，符合锐浪报表的数据格式
                            Result = JsonConvert.SerializeObject(new { baseInfo, deviceInfo, materInfo, testItemInfo, testItemEditInfo, inspectionInfo, FinalConclusion });
                            break;
                        case "HZ":
                            //使用服务器的相对路径
                            ReportPathFile = context.Server.MapPath("/Template/" + sComp + "/LabelPrint/DHR(HZ).grf");
                            //汇总页文件名称 序列号 时间戳
                            fileName = serialNo + "(" + time + ")" + ".pdf";

                            var materProductInfo = GetDataList<MaterialInfo>(list["Table7"], $"BatchNo='{serialNo}'", defaultPath);
                            var procedureInfo = GetDataList<ProductionProcessInfo>(list["Table8"], $"BatchNo='{serialNo}'", defaultPath);
                            var productbaseInfo = GetDataList<BaseInfo>(list["Table9"], $"SerielNo='{serialNo}'", "");

                            // 格式化，符合锐浪报表的数据格式
                            Result = JsonConvert.SerializeObject(new { materProductInfo, procedureInfo, productbaseInfo });
                            break;
                        case "WX":
                            //使用服务器的相对路径
                            ReportPathFile = context.Server.MapPath("/Template/" + sComp + "/LabelPrint/WX.grf");
                            //维修dhr 顺序号 - 工序名称 时间戳
                            fileName = flowOrder + "-" + procedureName + "(" + time + ")" + ".pdf";

                            var repairHeaderInfo = GetDataList<RepairHeader>(list["Table1"], "", "");
                            var badPhenomenonInfo = GetDataList<BadPhenomenon>(list["Table2"], "", "");
                            var badCauseInfo = GetDataList<BadCause>(list["Table3"], "", "");
                            var materialInfo = GetDataList<Material>(list["Table4"], "", "");

                            // 格式化，符合锐浪报表的数据格式
                            Result = JsonConvert.SerializeObject(new { repairHeaderInfo, badPhenomenonInfo, badCauseInfo, materialInfo });
                            break;
                        default:
                            break;
                    }

                    //获取报表导入到结果判断是否成功
                    if (!Report.LoadFromFile(ReportPathFile))
                    {
                        throw new Exception("报表模板导入失败！");
                    }

                    //载入报表数据，为约定格式的 XML 或 JSON 文本数据
                    Report.LoadDataFromXML(Result);
                    //生成PDF文档数据
                    PDFDataObject = Report.ExportDirectToBinaryObject(GRExportType.gretPDF);

                    //虚拟路径
                    string xuPath = "/DHRFile/" + orderNo + "/" + serialNo + "/";
                    //服务器路径
                    string path = context.Server.MapPath(xuPath);

                    var filePath = path + fileName;

                    //验证当前服务器是否有这个路径
                    if (!Directory.Exists(path))
                    {
                        Directory.CreateDirectory(path);
                    }

                    object Data = PDFDataObject.SaveToVariant();
                    // 将二进制数据写入文件
                    File.WriteAllBytes(filePath, (byte[])Data);

                    SQLStringList.Add(" Insert into T_DHRFile(FileNo,FileName,OrderNo,SerialNo,MaterNo,ProcedureNo,ProcedureName,FlowOrder,RPath,APath,CreateWay,Status,CompanyNo,InMan,Remark) values " +
                        "('" + FileNo + "','" + fileName + "','" + orderNo + "','" + serialNo + "','" + productNo + "','" + procedureNo + "','" + procedureName + "','" + flowOrder + "','" + xuPath + "','','" + It.Remark + "','启用','" + sComp + "','" + sMan + "','" + opFlag + "')");

                    result.Add(new OperationResult { SerialNo = serialNo, ProcedureName = procedureName, DHRType = opFlag, Path = xuPath+ fileName , Msg = "Success", ExceptionMessage = exceptionMessage });
                }
                catch (Exception ex)
                {
                    Message = "CreateError";
                    result.Add(new OperationResult { SerialNo = serialNo, ProcedureName = procedureName, DHRType = opFlag, Path = "", Msg = "CreateError", ExceptionMessage = "错误原因："+ ex.Message.ToString() });
                }
            }

            if (SQLStringList.Count > 0)
            {
                string str = DBHelper.ExecuteSqlTranStr(SQLStringList);
                if (str.Contains("Err"))
                {
                    foreach (var item in result)
                    {
                        string servicePath = context.Server.MapPath(item.Path);
                        try
                        {
                            if (File.Exists(servicePath) && item.Msg == "Success")
                            {
                                File.Delete(servicePath);
                            }
                        }
                        catch (Exception) {}
                    }
                    result.Clear();
                    Message = "InsertError";
                }
            }
            context.Response.Write(JsonConvert.SerializeObject(new { Msg = Message, Data = result, SumCount = list["Table"].Rows.Count }));
            return;
        }


        private List<T> GetDataList<T>(DataTable table, string condition, string defaultPath) where T : new()
        {
            var data = table.Select(condition).Select((row, index) => MapDataRow<T>(row, index)).ToList();
            if (data.Count == 0)
            {
                data.Add(CreateDefaultObject<T>(defaultPath));
            }
            return data;
        }
        // 通用方法：将 DataRow 映射到指定类型对象，并处理空值
        private T MapDataRow<T>(DataRow row, int index) where T : new()
        {
            var instance = new T();
            var type = typeof(T);

            foreach (var prop in type.GetProperties())
            {
                if (prop.PropertyType == typeof(string) && row.Table.Columns.Contains(prop.Name))
                {
                    if (prop.Name == "Id")
                    {
                        prop.SetValue(instance, (index + 1).ToString());
                    }
                    else
                    {
                        prop.SetValue(instance, GetValueOrDefault(row[prop.Name]));
                    }
                }
            }
            return instance;
        }
        // 创建默认对象
        private T CreateDefaultObject<T>(string defaultPath) where T : new()
        {
            var instance = new T();
            var type = typeof(T);
            foreach (var prop in type.GetProperties())
            {
                if (prop.PropertyType == typeof(string))
                {
                    if (prop.Name == "ImgPath")
                    {
                        prop.SetValue(instance, defaultPath);
                    }
                    else
                    {
                        prop.SetValue(instance, "/");
                    }
                }
            }
            return instance;
        }
        //设置默认值
        private string GetValueOrDefault(object value)
        {
            return value == DBNull.Value || string.IsNullOrEmpty(value?.ToString()) ? "/" : value.ToString();
        }
        #endregion

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}