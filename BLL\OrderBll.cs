﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using DAL;

namespace BLL
{
    public class OrderBll
    {




        // 判断信息是否存在
        public static string JudgeObjectExist(string Kind, string KindList, string QT, string sComp, string sFlag)
        {
            return OrderDal.JudgeObjectExist(Kind, KindList, QT, sComp, sFlag);
        }

        // 获取工单相关的信息
        public static DataTable GetOrderInfo(string No, string Item, string Name, string MNo, string MName, string Status, string BDate, string EDate, string A, string B, string C, string D, int Row, int num, string sInMan, string sComp, string sFlag)
        {
            return OrderDal.GetOrderInfo(No, Item, Name, MNo, MName, Status, BDate, EDate, A, B, C, D, Row, num, sInMan, sComp, sFlag);
        }


        // 操作工艺相关信息 -- 工单的
        public static string OPOrderInfo(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string G, string H, string I, string J, string K, string L, string M, string N, string O, string P, string SeNo, string sComp, string InMan, string Remark, string sFlag)
        {
            return OrderDal.OPOrderInfo(No, Name, Item, MNo, MName, A, B, C, D, E, F, G, H, I, J, K, L, M, N, O, P, SeNo, sComp, InMan, Remark, sFlag);
        }

        // 操作工艺相关信息  -- 序列号的
        public static string OPSerialInfo(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string G, string H, string I, string J, string K, string L, string M, string N, string O, string P, string SeNo,string sComp, string InMan, string Remark, string sFlag)
        {
            return OrderDal.OPSerialInfo(No, Name, Item, MNo, MName, A, B, C, D, E, F, G, H, I, J, K, L, M, N, O,P, SeNo, sComp, InMan, Remark, sFlag);
        }


        // 获取生产相关的信息
        public static DataTable GetPRDInfo(string No, string Item, string Name, string MNo, string MName, string Status, string BDate, string EDate, string A, string B, string C, string D, int Row, int num, string sInMan, string sComp, string sFlag)
        {
            return OrderDal.GetPRDInfo(No, Item, Name, MNo, MName, Status, BDate, EDate, A, B, C, D, Row, num, sInMan, sComp, sFlag);
        }


        // 操作生产相关信息
        public static string OPPRDInfo(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string G, string H, string I, string J, string sComp, string InMan, string Remark, string sFlag)
        {
            return OrderDal.OPPRDInfo(No, Name, Item, MNo, MName, A, B, C, D, E, F, G, H, I, J, sComp, InMan, Remark, sFlag);
        }

        // 操作维修相关信息
        public static string OPWXInfo(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string G, string H, string I, string J, string sComp, string InMan, string Remark, string sFlag)
        {
            return OrderDal.OPWXInfo(No, Name, Item, MNo, MName, A, B, C, D, E, F, G, H, I, J, sComp, InMan, Remark, sFlag);
        }

        //  重工相关操作
        public static string OPReworkOrder(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string sComp, string InMan, string Remark, string sFlag)
        {
            return OrderDal.OPReworkOrder(No, Name, Item, MNo, MName, A, B, C, D, E, F, sComp, InMan, Remark, sFlag);
        }

        // 获取系统配置信息
        public static DataTable GetSysConfigInfo(string sKind, string sComp, string sFlag)
        {
            return OrderDal.GetSysConfigInfo(sKind,sComp, sFlag);
        }

        // 获取序列号的追溯信息
        public static DataSet GetSerialTracInfo(string No, string MNo, string A, string B, string C, string D, string sInMan, string sComp, string sFlag)
        {
            return OrderDal.GetSerialTracInfo(No, MNo, A, B, C, D, sInMan, sComp, sFlag);
        }


        // 获取K3的相关连接
        public static DataTable GetK3UrlInfo(string sKind, string sComp)
        {
            return OrderDal.GetK3UrlInfo(sKind, sComp);
        }


        /// <summary>
        /// 把接口的数据保存到数据库中
        /// </summary>
        /// <param name="sTablColumnName">数据库表字段</param>
        /// <param name="sTableName">数据库对应的数据表</param>
        /// <param name="list">接口返回的数据</param>
        /// <returns></returns>
        public static string SavaData(string sTablColumnName, string sTableName, List<List<Object>> list, string sSyncColumnName, string sSyncColumnValue)
        {
            return OrderDal.SavaData(sTablColumnName, sTableName, list, sSyncColumnName, sSyncColumnValue);
        }

        // 插入K3各类数据
        public static string InsertK3Date(string sKind, string sInType, string sSyncNo, string InMan, string Dept, string sComp, string sFlag)
        {
            return OrderDal.InsertK3Date(sKind, sInType, sSyncNo, InMan, Dept, sComp, sFlag);
        }

        // 获取入库相关信息
        public static DataTable GetInStockInfo(string No, string Item, string Name, string MNo, string MName, string Status, string BDate, string EDate, string A, string B, string C, string D, int Row, int num, string sInMan, string sComp, string sFlag)
        {
            return OrderDal.GetInStockInfo(No, Item, Name, MNo, MName, Status, BDate, EDate, A, B, C, D, Row, num, sInMan, sComp, sFlag);
        }

        // 操作入库相关信息
        public static string OPInStockInfo(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string G, string H, string I, string sComp, string InMan, string Remark, string sFlag)
        {
            return OrderDal.OPInStockInfo(No, Name, Item, MNo, MName, A, B, C, D, E, F,G,H,I, sComp, InMan, Remark, sFlag);
        }

        public static int GetExcelTestInfo(DataTable dt, string TableName)
        {
            return OrderDal.GetExcelTestInfo(dt, TableName);
        }


        // 操作第三方测试项信息，插入数据表
        public static string OPThirdTestItemInfo(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string sComp, string InMan, string Remark, string sFlag)
        {
            return OrderDal.OPThirdTestItemInfo(No, Name, Item, MNo, MName, A, B, C, D, E, F,sComp, InMan, Remark, sFlag);
        }

        public static List<DataTable> GetExportConditions(string sNo, string A, string B, string Item, string E, string sInMan, string Comp, string Flag)
        {
            return OrderDal.GetExportConditions(sNo, A, B, Item, E, sInMan, Comp, Flag);
        }

        public static DataTable GetInStockDataInfo(string No, string sInMan, string Comp, string Flag)
        {
            return OrderDal.GetInStockDataInfo(No,sInMan, Comp, Flag);
        }

        public static DataTable BatchManipulateData(string No, string SN, string MNo, string MName, string A, string B, string C, string D, string E, string F, string G, string H, string I, string J, string Remark, string sMan, string Flag, string sComp, string IP)
        {
            return OrderDal.BatchManipulateData(No, SN, MNo, MName, A, B, C, D, E, F, G, H, I, J, Remark, sMan, Flag, sComp, IP);
        }
















    }
}
