﻿

//选中的作业单元 
var selectedCheckboxes = []

$(function () {

    function EntityMsg(item, Mesage) {
        $("#div_warning").show();
        item.focus();
        $("#sMessage").html(Mesage);

    }
    // 重新登录
    $('#BtnRelationLogin').click(function () {
        var Num = parseInt(Math.random() * 1000);
        location.href = "../Login.htm?CFlag=30&RA" + Num;

    });









    //  根据客户编码获取简称
    $('#txtBCustNo').blur(function () {
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();
        var sCNo = $('#txtBCustNo').val();
        var Flag = "41-2";
        var time = new Date();
        var sM = time.getMonth() + 1;
        if (sM < 10) {
            sM = "0" + sM
        }
        var sD = time.getDate()
        if (sD < 10) {
            sD = "0" + sD
        }

        var s1 = time.getFullYear() + "-" + sM + "-" + sD;
        $('#txtPPDate').val(s1);

        if (sCNo == "") {
            $("#div_warning").html("请输入客户编码！")
            $("#divsuccess").show();
            return;
        }


        var Data = '';
        var Params = { CNo: sCNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetCustInfoByNo&CFlag=41-2",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson.length > 0) {
                    // var sStr = eval(parsedJson.json)[0];
                    //$('#txtRNo').val(row.ReceiveNo);
                    $('#txtNameEn').val(parsedJson[0].CustEn);
                    $('#txtPPNo').val(parsedJson[0].CustNo);
                    $('#txtPPNo').focus();


                } else {
                    $("#txtNameEn").html("系统不存在该客户信息");
                }
            },
            error: function (data) {
                $("#div_warning2").html("系统出错，请重试2！")
                $("#div_warning2").show();
            }
        });

    });









    //  保存:工单流程信息
    $('#OrderFlowSave_Btn').click(function () {
        $("#OrderFlowSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();


        var sOrderNo = $("#txtSOrderNo").val();
        var OKind = $("#txtOKind").val();  // 工单类型
        var sPNo = $("#txtProductNo").val();
        var sPName = $("#txtProductName").val();
        var sTNo = $("#txtProcNo").val();  //工序  (05)总装 
        var sNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
        var sName = sTNo.substr(sTNo.indexOf(")") + 1, sTNo.length);
        var sProcVer = $("#txtInProcVer").val();  // 工序版本
        var sPOldVer = $("#txtOldInProcVer").val();  // 工序版本 -- 旧版本
        var sOrder = $("#txtFlowOrder").val(); // 现在系统自动产生
        var sUnit = $("#txtCWUnit").val();  // 作业单元
        var sTCNo = $("#txtConditionNo").val();   // 工序行为  (GXXW002)通用包装行为  
        var sCNo = ""; // 工序行为编号
        var sCName = ""; // 工序行为名称
        var sKind = "正常"; // 正常工序
        var sVerDesc = $("#txtVerDesc").val(); //  版本说明
        var sFlag = $("#txtAEFlag").val();  // 8 标识新增，7 标识修改
        var sPlanName = $('#txtWOSamplPlan option:selected').text(); //抽样方案名称
        var sPlanNo = $('#txtWOSamplPlan').val();                  //抽样方案编号
        var sSamplWay = $('#txtSamplWay').val();//抽样方式

        if (sPNo == "") {
            $("#div_warning").html("请输入产品编号！")
            $("#div_warning").show();
            $("#ProductFlowSave_Btn").removeAttr("disabled");
            return;
        }
        if (sPName == "") {
            $("#div_warning").html("请输入产品名称！")
            $("#div_warning").show();
            $("#OrderFlowSave_Btn").removeAttr("disabled");
            return;
        }

        if (sNo == "") {
            $("#div_warning").html("请输入工序编号！")
            $("#div_warning").show();
            $("#OrderFlowSave_Btn").removeAttr("disabled");
            return;
        }
        if (sName == "") {
            $("#div_warning").html("请输入工序名称！")
            $("#div_warning").show();
            $("#OrderFlowSave_Btn").removeAttr("disabled");
            return;
        }
        if (sUnit == "") {
            $("#div_warning").html("请选择作业单元！")
            $("#div_warning").show();
            $("#OrderFlowSave_Btn").removeAttr("disabled");
            return;
        }
        if ((sTCNo == "") || (sTCNo == "null") || (sTCNo == null)) {
            $("#div_warning").html("请选择工序行为！")
            $("#div_warning").show();
            $("#OrderFlowSave_Btn").removeAttr("disabled");
            return;
        }
        sCNo = sTCNo.substr(1, sTCNo.indexOf(")") - 1);  // (GXXW002)通用包装行为  
        sCName = sTCNo.substr(sTCNo.indexOf(")") + 1, sTCNo.length);

        if (sProcVer == "") {
            $("#div_warning").html("请填写工序版本！")
            $("#div_warning").show();
            $("#OrderFlowSave_Btn").removeAttr("disabled");
            return;
        }



        var Data = '';
        var Params = { No: sNo, Name: sName, Item: sOrderNo, MNo: sPNo, MName: sPName, A: sOrder, B: sCNo, C: sCName, D: sKind, E: sUnit, F: sProcVer, G: sPOldVer, H: OKind, I: "", J: sPlanNo, K: sPlanName, L: sSamplWay, Remark: sVerDesc, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPOrderInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#div_warning").html("提交成功！")
                    $("#div_warning").show();
                    $("#OrderFlowSave_Btn").removeAttr("disabled");

                    $('#Search_OrderFowBtn').click();
                    document.getElementById('light').style.display = 'none';

                    $("#LB_Flow").html(parsedJson.RMgs);

                    ShowWODeviceInfo(sOrderNo, sNo + sProcVer, "");  // 设备信息
                    ShowOrderFileInfo(sOrderNo, sNo + sProcVer, "");  // 显示工艺文件
                    ShowWOTestItemInfo(sOrderNo, sNo + sProcVer, ""); // 显示测试

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#OrderFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                    $("#OrderFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("工单对应序列号生产中，不能操作！")
                    $("#div_warning").show();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#OrderFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("该产品流程已存在该工序，请确认！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_NOTEST') {
                    $("#OrderFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("工序测试项抽样必须先设置抽样的测试项，请切换工序版本确认所有工序版本都已设置！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_FAFSNULL') {
                    $("#OrderFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("抽样工序行为必须设置抽样方案和检验方式，请确认！")
                    $("#div_warning").show();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_FAFSNOTNULL') {
                    $("#OrderFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("该工序没有设置的是非抽样工序行为，不能设置抽样方案和检验方式，请确认！")
                    $("#div_warning").show();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_ISTEST') {
                    $("#OrderFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("工序抽样的检验方式下无需设置测试项，请确认并取消所有工序版本的测试项！")
                    $("#div_warning").show();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_NOTISTEST') {
                    $("#OrderFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("该工序没有设置抽样工序行为，请取消该工序所有版本的抽样测试项！")
                    $("#div_warning").show();
                } else {
                    $("#OrderFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html(parsedJson.Msg);
                    $("#div_warning").show();
                }
            },
            error: function (data) {
                $("#OrderFlowSave_Btn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });




    //  根据物料编码获取物料信息
    $('#txtProductNo').blur(function () {
        $("#div_warning").hide();
        $("#divsuccess").hide();

        var sNo = $("#txtProductNo").val();
        var Flag = "33-99";

        if (sNo == "") {
            $("#div_warning").html("请输入物料编码！")
            $("#divsuccess").show();
            return;
        }

        var Data = '';
        var Params = { No: sNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetMaterInfoByNo&CFlag=" + Flag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $("#txtProductName").val(sStr.MaterName);
                    //$("#txtModel").val(sStr.MaterSpec);
                    //$("#txtPModel").val(sStr.MaterSpec);

                }
                else {
                    $("#txtProductNo").val("");
                    $("#div_warning").html("系统无此产品编码，请确认！")
                    $("#div_warning").show();
                }
            },
            error: function (data) {
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });


    //  根据物料编码获取物料信息
    $('#txtFNo').blur(function () {
        $("#div_warning").hide();
        $("#divsuccess").hide();

        var sNo = $("#txtFNo").val();
        var Flag = "33-99";

        if (sNo == "") {
            $("#div_warning").html("请输入物料编码！")
            $("#divsuccess").show();
            return;
        }

        var Data = '';
        var Params = { No: sNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetMaterInfoByNo&CFlag=" + Flag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                $("#txtFName").val("");
                $("#txtModel").val("");
                $("#txtPModel").val("");
                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $("#txtFName").val(sStr.MaterName);
                    $("#txtModel").val(sStr.MaterSpec);
                    $("#txtPModel").val(sStr.MaterSpec);
                }
            },
            error: function (data) {
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });


    //  根据物料编码获取物料信息
    $('#txtCMaterNo').blur(function () {
        $("#div_warning3").hide();
        $("#divsuccess3").hide();

        var sNo = $("#txtCMaterNo").val();
        var Flag = "33-99";

        if (sNo == "") {
            $("#div_warning3").html("请输入物料编码！")
            $("#div_warning3").show();
            return;
        }

        var Data = '';
        var Params = { No: sNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetMaterInfoByNo&CFlag=" + Flag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                $("#txtCName").val("");
                $("#txtModel").val("");
                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $("#txtCName").val(sStr.MaterName);
                    $("#txtModel").val(sStr.MaterSpec);
                }
                else {
                    $("#div_warning3").html("无此物料信息！")
                    $("#div_warning3").show();
                }
            },
            error: function (data) {
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });



    //  保存:保存工单BOM信息
    $('#OrderBOMSaveBtn').click(function () {
        $("#OrderBOMSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning3").hide();

        var sOrderNo = $("#txtSOrderNo").val();
        var sFNo = $("#txtFNo").val();
        var sCNo = $("#txtCMaterNo").val();
        var sCName = $("#txtCName").val();
        var sNum = $("#txtNum").val();
        var sGX = $('#txtBomProcNo option:selected').text(); //选中的文本 获取下拉值     // 这个只能获取工序编号 $("#txtBomProcNo").val();  //工序  (05)总装 
        var sFlag = $("#txtAEFlag").val();  //   12 修改   13新增 
        var sRepeatFlag = "";
        var sPack = "";
        var sZSFlag = "";

        if ($("#txtRepeatFlag").is(":checked")) {
            sRepeatFlag = "是";
        }

        if ($("#txtSerialC").is(':checked')) {
            sZSFlag = "序列号";
        }
        if ($("#txtBatchC").is(':checked')) {
            sZSFlag = "批次";
        }
        if ($("#txtMaterC").is(':checked')) {
            sZSFlag = "物料";
        }
        if ($("#txtCPack").is(':checked')) {
            sPack = "是";
        }

        if (sCNo == "") {
            $("#div_warning3").html("请输入子零件编码！")
            $("#div_warning3").show();
            $("#OrderBOMSaveBtn").removeAttr("disabled");
            return;
        }
        if (sCName == "") {
            $("#div_warning3").html("获取不到子零件名称，光标放在子编码内再离开！")
            $("#div_warning3").show();
            $("#OrderBOMSaveBtn").removeAttr("disabled");
            return;
        }

        if ((sPack == "是") && (sZSFlag != "不管控")) {  // 如果勾选是装箱件，则不能勾选追溯属性了
            $("#div_warning").html("已选择为装箱件，不需要再勾选批序管控，可勾选不管控！")
            $("#div_warning").show();
            $("#BOMSaveBtn").removeAttr("disabled");
            return;
        }

        if (sZSFlag == "") {
            $("#div_warning3").html("请选择追溯方式，请确认！")
            $("#div_warning3").show();
            $("#OrderBOMSaveBtn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { No: sGX, Name: sFNo, Item: sOrderNo, MNo: sCNo, MName: sCName, A: sNum, B: sPack, C: sZSFlag, D: sRepeatFlag, E: "", F: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPOrderInfo&CFlag=152",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#OrderBOMSaveBtn").removeAttr("disabled");
                    $("#divsuccess").show();
                    document.getElementById('light2').style.display = 'none';
                    $('#Search_OrderBomBtn').click();


                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#OrderBOMSaveBtn").removeAttr("disabled");
                    $("#loginError").html("您未登陆系统，请先登录！")
                    $("#loginError").show();
                    //location.href = "Login.htm";
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                    $("#OrderBOMSaveBtn").removeAttr("disabled");
                    $("#div_warning3").html("工单对应序列号生产中，不能操作！")
                    $("#div_warning3").show();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#OrderBOMSaveBtn").removeAttr("disabled");
                    $("#div_warning3").html("该工单BOM下已有该子零件，请确认，请确认");
                    $("#txtCNo").val("");
                    $("#txtCName").val("");
                    $("#div_warning").show();
                }
                else {
                    $("#OrderBOMSaveBtn").removeAttr("disabled");
                    $("#div_warning3").html("系统出错，请重试1！")
                    $("#div_warning3").show();
                }
            },
            error: function (data) {
                $("#OrderBOMSaveBtn").removeAttr("disabled");
                $("#div_warning3").html("系统出错，请重试2！")
                $("#div_warning3").show();
            }
        });
    });


    // 工单：替代料使用： 根据物料编码获取物料信息
    $('#txtRWOCMaterNo').blur(function () {
        $("#div_warningWOTD").hide();
        var sNo = $("#txtRWOCMaterNo").val();
        var Flag = "33-99";
        if (sNo == "") {
            $("#div_warningWOTD").html("请输入物料编码！")
            $("#divsuccess").show();
            return;
        }

        var Data = '';
        var Params = { No: sNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetMaterInfoByNo&CFlag=" + Flag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $("#txtRWOCMaterName").val(sStr.MaterName);
                    $("#txtRWOCModel").val(sStr.MaterSpec);
                }
                else {
                    $("#txtRWOCMaterNo").val("");
                    $("#div_warning").html("系统无此产品编码，请确认！")
                    $("#div_warning").show();
                }
            },
            error: function (data) {
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });


    //  工单：添加替代物料
    $('#WOReplaceSaveBtn').click(function () {
        $("#WOReplaceSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warningWOTD").hide();

        var sWO = $("#txtROrderNo").val();
        var sFNo = $("#txtRWOFNo").val();  // 被替换的物料编码
        var sFName = $("#txtRWOFName").val();
        var sCNo = $("#txtRWOCMaterNo").val();
        var sCName = $("#txtRWOCMaterName").val();
        var sTNo = $("#txtWOProcNo").val();  //工序  (05)总装
        var sNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
        var sName = sTNo.substr(sTNo.indexOf(")") + 1, sTNo.length);
        var sNum = $("#txtWONum").val(); // 被替代物料的数量
        var sPX = $("#txtWOCWay").val(); // 被替代物料的管控方式
        var sFlag = "14-1";

        if (sCName == "") {
            $("#div_warningWOTD").html("请填写可替代物料！");
            $("#div_warningWOTD").show();
            $("#WOReplaceSaveBtn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { No: "", Name: "", Item: sWO, MNo: sFNo, MName: sFName, A: sCNo, B: sCName, C: sNo, D: sName, E: sNum, F: sPX, Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPOrderInfo&CFlag=14-1",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#WOReplaceSaveBtn").removeAttr("disabled");

                    layer.msg('操作成功！');

                    $('#Search_OrderBomBtn').click();

                    document.getElementById('Div_Replace').style.display = 'none';
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#WOReplaceSaveBtn").removeAttr("disabled");
                    $("#div_warningWOTD").html("该替代物料已存在，请确认！")
                    $("#div_warningWOTD").show();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTTD') {
                    $("#WOReplaceSaveBtn").removeAttr("disabled");
                    $("#div_warningWOTD").html("不能给本身是替代料的物料添加替代料！")
                    $("#div_warningWOTD").show();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                    $("#SNReplaceSaveBtn").removeAttr("disabled");
                    $("#div_warningWOTD").html("生产中的序列号不可再添加替代料！")
                    $("#div_warningWOTD").show();
                }
                else {
                    $("#OP_ztqyOrderBtn").removeAttr("disabled");
                    $("#div_warningWOTD").html("系统出错，请重试1！")
                    $("#div_warningWOTD").show();
                }
            },
            error: function (data) {
                $("#WOReplaceSaveBtn").removeAttr("disabled");
                $("#div_warningWOTD").html("系统出错，请重试2！")
                $("#div_warningWOTD").show();
            }
        });
    });



    //  保存:序列号 流程信息
    $('#SerialFlowSave_Btn').click(function () {
        $("#SerialFlowSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();

        var sWO = $("#txtSOrderNo").val();
        var sSerialNo = $("#txtSSerialNo").val();
        var sPNo = $("#txtProductNo").val();
        var sPName = $("#txtProductName").val();
        var sTNo = $("#txtProcNo").val();  //工序  (05)总装
        var sNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
        var sName = sTNo.substr(sTNo.indexOf(")") + 1, sTNo.length);
        var sProcVer = $("#txtInProcVer").val();  // 工序版本
        var sOrder = $("#txtFlowOrder").val();
        var sUnit = $("#txtCWUnit").val();  // 作业单元
        var sTCNo = $("#txtConditionNo").val();   // 工序行为  (GXXW002)通用包装行为  
        var sCNo = ""; // 工序行为编号
        var sCName = ""; // 工序行为名称
        var sKind = "正常"; // 正常工序
        var sVerDesc = $("#txtVerDesc").val(); //  版本说明
        var sFlag = $("#txtAEFlag").val();  // 8 标识新增，7 标识修改


        if (sPNo == "") {
            $("#div_warning").html("请输入产品编号！")
            $("#div_warning").show();
            $("#SerialFlowSave_Btn").removeAttr("disabled");
            return;
        }
        if (sPName == "") {
            $("#div_warning").html("请输入产品名称！")
            $("#div_warning").show();
            $("#SerialFlowSave_Btn").removeAttr("disabled");
            return;
        }

        if (sNo == "") {
            $("#div_warning").html("请输入工序编号！")
            $("#div_warning").show();
            $("#SerialFlowSave_Btn").removeAttr("disabled");
            return;
        }
        if (sUnit == "") {
            $("#div_warning").html("请选择作业单元！")
            $("#div_warning").show();
            $("#SerialFlowSave_Btn").removeAttr("disabled");
            return;
        }
        if (sName == "") {
            $("#div_warning").html("请输入工序名称！")
            $("#div_warning").show();
            $("#SerialFlowSave_Btn").removeAttr("disabled");
            return;
        }
        if ((sTCNo == "") || (sTCNo == "null") || (sTCNo == null)) {
            $("#div_warning").html("请选择工序行为！")
            $("#div_warning").show();
            $("#SerialFlowSave_Btn").removeAttr("disabled");
            return;
        }
        sCNo = sTCNo.substr(1, sTCNo.indexOf(")") - 1);  // (GXXW002)通用包装行为  
        sCName = sTCNo.substr(sTCNo.indexOf(")") + 1, sTCNo.length);


        var Data = '';
        var Params = { No: sNo, Name: sName, Item: sSerialNo, MNo: sPNo, MName: sPName, A: sOrder, B: sCNo, C: sCName, D: sKind, E: sUnit, F: sProcVer, G: sWO, H: "", Remark: sVerDesc, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPSerialInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#div_warning").html("提交成功！")
                    $("#div_warning").show();
                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';
                    $("#SerialFlowSave_Btn").removeAttr("disabled");

                    $('#Search_SerialFowBtn').click();
                    document.getElementById('light').style.display = 'none';

                    $("#txtProcNo").val("");
                    $("#txtFlowOrder").val("");
                    $("#txtConditionNo").val("");
                    $("#txtRemark").val("");

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#SerialFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#SerialFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("该产品流程已存在该工序，请确认！")
                    $("#div_warning").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                    $("#SerialFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("序列号生产中，不能操作！")
                    $("#div_warning").show();
                }
                else {
                    $("#SerialFlowSave_Btn").removeAttr("disabled");
                    $("#div_warning").html(parsedJson.Msg);
                    $("#div_warning").show();
                }
            },
            error: function (data) {
                $("#SerialFlowSave_Btn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });


    //  保存:保存序列号BOM信息
    $('#SerialBOMSaveBtn').click(function () {
        $("#SerialBOMSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning3").hide();

        var sWO = $("#txtSOrderNo").val();
        var sSerialNo = $("#txtSSerialNo").val();
        var sFNo = $("#txtFNo").val();
        var sCNo = $("#txtCMaterNo").val();
        var sCName = $("#txtCName").val();
        var sNum = $("#txtNum").val();
        var sGX = $('#txtBomProcNo option:selected').text(); //选中的文本 获取下拉值     // 这个只能获取工序编号 $("#txtBomProcNo").val(); 
        var sFlag = $("#txtAEFlag").val();  //   12 修改   13新增 
        var sPack = "";
        var sZSFlag = "";
        var sRepeatFlag = "";

        if ($("#txtRepeatFlag").is(":checked")) {
            sRepeatFlag = "是";
        }

        if (sCNo == "") {
            $("#div_warning3").html("请输入子零件编码！")
            $("#div_warning3").show();
            $("#SerialBOMSaveBtn").removeAttr("disabled");
            return;
        }
        if (sCName == "") {
            $("#div_warning3").html("获取不到子零件名称，光标放在子编码内再离开！")
            $("#div_warning3").show();
            $("#SerialBOMSaveBtn").removeAttr("disabled");
            return;
        }

        if (sGX == "") {
            $("#div_warning3").html("请选择工序！")
            $("#div_warning3").show();
            $("#SerialBOMSaveBtn").removeAttr("disabled");
            return;
        }

        if ($("#txtSerialC").is(':checked')) {
            sZSFlag = "序列号";
        }
        if ($("#txtBatchC").is(':checked')) {
            sZSFlag = "批次";
        }
        if ($("#txtMaterC").is(':checked')) {
            sZSFlag = "物料";
        }
        if ($("#txtCPack").is(':checked')) {
            sPack = "是";
        }

        if ((sPack == "是") && (sZSFlag != "不管控")) {  // 如果勾选是装箱件，则不能勾选追溯属性了
            $("#div_warning").html("已选择为装箱件，不需要再勾选批序管控，可勾选不管控！")
            $("#div_warning").show();
            $("#BOMSaveBtn").removeAttr("disabled");
            return;
        }

        if (sZSFlag == "") {
            $("#div_warning3").html("请选择追溯方式，请确认！")
            $("#div_warning3").show();
            $("#OrderBOMSaveBtn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { No: sGX, Name: sFNo, Item: sSerialNo, MNo: sCNo, MName: sCName, A: sNum, B: sPack, C: sZSFlag, D: sWO, E: sRepeatFlag, F: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPSerialInfo&CFlag=152",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#SerialBOMSaveBtn").removeAttr("disabled");
                    $("#divsuccess").show();
                    document.getElementById('light2').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';
                    $('#Search_SerialBomBtn').click();


                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#SerialBOMSaveBtn").removeAttr("disabled");
                    $("#loginError").html("您未登陆系统，请先登录！")
                    $("#loginError").show();
                    //location.href = "Login.htm";
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                    $("#SerialBOMSaveBtn").removeAttr("disabled");
                    $("#div_warning3").html("该物料已扫描不能修改！")
                    $("#div_warning3").show();
                    $('#Search_SerialBomBtn').click();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#SerialBOMSaveBtn").removeAttr("disabled");
                    $("#div_warning3").html("该序列号BOM下已有该子零件，请确认，请确认");
                    $("#txtCNo").val("");
                    $("#txtCName").val("");
                    $("#div_warning3").show();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_FlOWOVER') {
                    $("#SerialBOMSaveBtn").removeAttr("disabled");
                    $("#div_warning3").html("该工序已完工，不能添加！");
                    $("#div_warning3").show();
                }
                else {
                    $("#SerialBOMSaveBtn").removeAttr("disabled");
                    $("#div_warning3").html("系统出错，请重试1！")
                    $("#div_warning3").show();
                }
            },
            error: function (data) {
                $("#SerialBOMSaveBtn").removeAttr("disabled");
                $("#div_warning3").html("系统出错，请重试2！")
                $("#div_warning3").show();
            }
        });
    });



    //  序列号：添加替代物料
    $('#SNReplaceSaveBtn').click(function () {
        $("#SNReplaceSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warningWOTD").hide();

        var sWO = $("#txtSOrderNo").val();  // 工单号
        var sSN = $("#txtROrderNo").val();  // 序列号
        var sFNo = $("#txtRWOFNo").val();  // 被替代的物料
        var sCNo = $("#txtRWOCMaterNo").val();
        var sCName = $("#txtRWOCMaterName").val();
        var sTNo = $("#txtWOProcNo").val();  //工序  (05)总装
        var sNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
        var sName = sTNo.substr(sTNo.indexOf(")") + 1, sTNo.length);
        var sFlag = "14-1";

        if (sCName == "") {
            $("#div_warningWOTD").html("请填写可替代物料！");
            $("#div_warningWOTD").show();
            $("#SNReplaceSaveBtn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { No: sWO, Name: "", Item: sSN, MNo: sFNo, MName: "", A: sCNo, B: sCName, C: sNo, D: sName, E: "", F: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPSerialInfo&CFlag=14-1",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#SNReplaceSaveBtn").removeAttr("disabled");

                    layer.msg('操作成功！');

                    $('#Search_SerialBomBtn').click();

                    document.getElementById('Div_Replace').style.display = 'none';
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#SNReplaceSaveBtn").removeAttr("disabled");
                    $("#div_warningWOTD").html("该替代物料已存在，请确认！")
                    $("#div_warningWOTD").show();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTTD') {
                    $("#SNReplaceSaveBtn").removeAttr("disabled");
                    $("#div_warningWOTD").html("不能给本身是替代料的物料添加替代料！")
                    $("#div_warningWOTD").show();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                    $("#SNReplaceSaveBtn").removeAttr("disabled");
                    $("#div_warningWOTD").html(sFNo + "已扫描不可再添加替代料！")
                    $("#div_warningWOTD").show();
                    $('#Search_SerialBomBtn').click();
                }
                else {
                    $("#OP_ztqyOrderBtn").removeAttr("disabled");
                    $("#div_warningWOTD").html("系统出错，请重试1！")
                    $("#div_warningWOTD").show();
                }
            },
            error: function (data) {
                $("#SNReplaceSaveBtn").removeAttr("disabled");
                $("#div_warningWOTD").html("系统出错，请重试2！")
                $("#div_warningWOTD").show();
            }
        });
    });

    $("#InTable").on("change", "input[type=checkbox]", function () {
        var checkbox = $(this);
        if (checkbox.is(":checked")) {
            // 如果复选框被选中，则将其值添加到数组中
            selectedCheckboxes.push(checkbox.val());
        } else {
            // 如果复选框被取消选中，则从数组中移除其值
            var index = selectedCheckboxes.indexOf(checkbox.val());
            if (index !== -1) {
                selectedCheckboxes.splice(index, 1);
            }
        }
    })

    //  工单：、员工对应作业单元 ：作业单元，选择工厂后
    $('#txtGC').change(function () {

        var sFNo = encodeURI($("#txtGC").val());
        $("#txtCJ").empty();
        $("#txtXT").empty();
        ChoogeUnit("", sFNo, "工厂", selectedCheckboxes);  // 显示这个工厂对应的作业单元
        GetWorkUnitConList("车间", sFNo);  // 显示这个工厂下的车间
    });

    //  工单：作业单元，选择车间后 encodeURI(Kind);
    $('#txtCJ').change(function () {

        var sFNo = encodeURI($("#txtGC").val());
        var sWNo = encodeURI($("#txtCJ").val());
        $("#txtXT").empty();

        ChoogeUnit(sFNo, sWNo, "车间", selectedCheckboxes);  // 显示这个车间对应的作业单元
        GetWorkUnitConList("线体", sWNo);  // 显示这个车间下的线体
    });

    //  工单：作业单元，选择线体后，直接显示作业单元
    $('#txtXT').change(function () {
        var sFNo = encodeURI($("#txtCJ").val());
        var sWNo = encodeURI($("#txtXT").val());

        ChoogeUnit(sFNo, sWNo, "线体", selectedCheckboxes);  // 显示这个线体对应的作业单元
    });

    // 选择对应的作业单元
    $('#OUnit_SaveBtn').click(function () {
        $("#OUnit_SaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning2").hide();
        $("#divsuccess2").hide();
        $("#txtCWUnit").val("");

        var sUnitStr = selectedCheckboxes.join(";");  //

        console.log(sUnitStr)

        if (sUnitStr == "") {
            $("#div_warning2").html("请选择对应的作业单元！")
            $("#div_warning2").show();
            $("#OUnit_SaveBtn").removeAttr("disabled");
            return;
        }

        $("#txtCWUnit").val(sUnitStr);
        $("#InTable").empty();
        document.getElementById('Div_WUnit').style.display = 'none';
        selectedCheckboxes = []
        $("#OUnit_SaveBtn").removeAttr("disabled");
        $("#txtGC").val("")
        $("#txtCJ").val("")
        $("#txtXT").val("")
    });

    //  作业执行，更改工序对应版本，相对应的设备，测试项等都变更 
    $('#txtPRDProcVer').change(function () {
        var sWO = $("#txtOrderNo").val();  // 工单号
        var sOVer = $('#txtProcVerR').val();
        var sNVer = $('#txtPRDProcVer').val();  //切换后的工序版本
        var sEXEMan = $('#txtInMan').val();  //作业执行人员账号
        var sFlag = "29-5";

        if ($('#txtNowSerialNo').val() == "") {  // 说明没有生产的序列号
            return;
        }

        if (sOVer == sNVer) {  // 没有
            return;
        }

        var bln = window.confirm("您确定要切换工序版本么?");  // 提示信息
        if (!bln) { // 不往下执行
            $('#txtPRDProcVer').val(sOVer);
            return;
        }

        var sEXENo = $("#txtEXENo").val(); // 作业编号
        var sSerial = $('#txtNowSerialNo').val();  // 序列号
        var sProcNo = $('#txtNowProcNo').val();  // 工序编码 

        var Data = '';
        var Params = { No: sProcNo, Name: "", Item: sWO, MNo: "", MName: "", A: sEXENo, B: sSerial, C: sNVer, D: sOVer, E: "", F: "", InMan: sEXEMan, Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPPRDChangeVerInfo&CFlag=29-5",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $('#txtOPRec').val(sNVer + " 工序版本切换成功！" + "\n" + $('#txtOPRec').val());

                    $('#txtNowProcVer').val(sNVer);  // 工序版本
                    $("#txtProcVerR").val(sNVer);

                    if ($('#txtAddKind').val() == "SB") {
                        PRDSheet_onclick(7);
                    }
                    if ($('#txtAddKind').val() == "CS") {
                        PRDSheet_onclick(3);
                    }
                    if ($('#txtAddKind').val() == "ZK") {
                        PRDSheet_onclick(8);
                    }

                    ShowPRDSNFileInfo(sSerial, sProcNo, sNVer); // 重新加载工艺文件
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST888888888') {
                    $("#LMgs").html("该工单已关闭或完工，无需操作！");
                    $('#txtPRDProcVer').val(sOVer);
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#LMgs").html("该工序版本已有作业记录，不可更换！");
                    $('#txtPRDProcVer').val(sOVer);
                }
                else {
                    $("#LMgs").html("系统出错，请重试1！");
                    $('#txtPRDProcVer').val(sOVer);
                }
            },
            error: function (data) {
                $("#LMgs").html("系统出错，请重试2！");
                $('#txtPRDProcVer').val(sOVer);
            }
        });

    })








    //  作业执行:填写测试项，下一项 按钮  -- 这个代码和作业执行信息变更点击 提交 和相似
    $('#TestNext_Btn').click(function () {
        $("#L_TestMgs").html("");
        var sEXEMan = $('#txtInMan').val();  //作业执行人员账号
        var sEXENo = $("#txtEXENo").val(); // 作业编号
        var sUnit = $("#txtEWorkCenter").val(); // 作业单元
        var sWO = $("#txtOrderNo").val();  // 工单号
        var sSerial = $('#txtNowSerialNo').val();  // 序列号
        var sProcNo = $('#txtNowProcNo').val();  // 工序编码 
        var sVer = $('#txtNowProcVer').val();  // 工序版本
        var sXW = $("#txtConditionNo").val(); //工序行为
        var sBatch = $("#txtBZBatch").val(); // 上层批次：包装上层批次--包装送检批次
        var sFlag = "29-6";

        var sSpecNo = $('#txtSpecNo').val();  // 测试项编号
        var sDStdV = $('#txtDStdV').val();// 标准值：下限，如200
        var sDStdB = $('#txtDStdB').val();// 是否包含标准值 -- 下限
        var sUStdV = $('#txtUStdV').val();// 标准值：上限，如600
        var sUStdB = $('#txtUStdB').val();// 是否包含标准值 -- 上限
        var sRKind = $('#txtRKind').val();// 上下限标识
        var sVKind = $('#txtVKind').val();// 数据类型
        var sVUnit = $('#txtNUnit').val();// 单位

        // 测试值
        var sV1 = "";  // 布尔值的
        var sV2 = $('#txtDValue').val();// 介于的测试值-下限  200   20231127：暂时不使用这个输入框，都是使用 txtNValue，记录测试值
        var sV3 = $('#txtUValue').val();// 介于的测试值-上限  600   20231127：暂时不使用这个输入框，都是使用 txtNValue，记录测试值
        var sV4 = "";  // 测试总体结论

        if (sSerial == "") {
            $("#L_TestMgs").html("获取不到序列号！")
            return;
        }
        if (sEXENo == "") {
            $("#L_TestMgs").html("获取不到工序！");
            return;
        }

        if ($("#txtPass").is(':checked')) { // 总体结论-PASS
            sV4 = "PASS"
        }
        if ($('#txtFail').is(':checked')) { // 总体结论-PASS
            sV4 = "FAIL"
        }

        if (sVKind == "布尔类型") {
            if ($('#txtOK').is(':checked')) { // 布尔测试值
                sV1 = "OK"
            }
            if ($('#txtNG').is(':checked')) { // 布尔测试值NG
                sV1 = "NG"
            }
            if (sV1 == "") {
                $("#L_TestMgs").html("请选择测试值！");
                return;
            }
        }
        else if (sVKind == "日期类型") {
            sV3 = $('#txtDateValue').val().replace("T", " ");   // 原值： 2023-12-16T15:45
            if (sV3 == "") {
                $("#L_TestMgs").html("请填写测试值！");
                return;
            }
        }
        else if (sVKind == "数值类型") {  // 填写数值，数字
            if (sRKind == "介于") {
                sV3 = $('#txtNValue').val();

                if (sV3 == "") {
                    $("#L_TestMgs").html("请填写测试值！");
                    return;
                }
                if (isNaN(sV3)) {  // $('#txtUValue').val("");// 介于的测试值-上限  600
                    $("#L_TestMgs").html("请填写数值！");
                    return;
                }
            }
            else if (sRKind == "大于") {
                sV3 = $('#txtNValue').val();// 其他测试值
                if (sV3 == "") {
                    $("#L_TestMgs").html("请填写测试值！");
                    return;
                }
                if (isNaN(sV3)) {  // $('#txtUValue').val("");// 介于的测试值-上限  600
                    $("#L_TestMgs").html("请填写数值！");
                    return;
                }
            }
            else if (sRKind == "小于") {
                sV3 = $('#txtNValue').val();// 其他测试值
                if (sV3 == "") {
                    $("#L_TestMgs").html("请填写测试值！");
                    return;
                }
                if (isNaN(sV3)) {  // $('#txtUValue').val("");// 介于的测试值-上限  600
                    $("#L_TestMgs").html("请填写数值！");
                    return;
                }
            }
            else if (sRKind == "等于") {
                sV3 = $('#txtNValue').val();// 其他测试值
                if (sV3 == "") {
                    $("#L_TestMgs").html("请填写测试值！");
                    return;
                }
                if (isNaN(sV3)) {  // $('#txtUValue').val("");// 介于的测试值-上限  600
                    $("#L_TestMgs").html("请填写数值！");
                    return;
                }
            }
            else {
                sV3 = $('#txtNValue').val();// 其他测试值
            }
        }
        else {  // 其他类型，填写啥，就保存啥
            sV3 = $('#txtNValue').val();// 其他测试值
            if (sV3 == "") {
                $("#L_TestMgs").html("请填写测试值！");
                return;
            }
        }

        if (sV4 == "") {
            $("#L_TestMgs").html("请选择测试结论！");
            return;
        }
        if ((sV1 == "OK") && (sV4 == "FAIL")) {
            $("#L_TestMgs").html("测试值和测试结论有冲突！");
            return;
        }
        if ((sV1 == "NG") && (sV4 == "PASS")) {
            $("#L_TestMgs").html("测试值和测试结论有冲突！");
            return;
        }


        var Data = '';
        var Params = { No: sSerial, Name: sVKind, Item: sProcNo, MNo: sVer, MName: sXW, A: sEXENo, B: sSpecNo, C: sV1, D: sV2, E: sV3, F: sV4, G: sWO, H: sVUnit, I: "", J: sBatch, InMan: sEXEMan, Remark: sUnit, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPPRDTestInfo&CFlag=29-6",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    ShowPRDSNTestInfo(sSerial, sProcNo, sVer);
                    // $("#txtPass").removeProp("checked");  // 这种方式不行
                    document.getElementById("txtPass").checked = false;  // 这种可以 删除选择项，清空复选框
                    // $("#txtFail").removeProp("checked");
                    document.getElementById("txtFail").checked = false;
                    //  $("#txtOK").removeProp("checked");
                    document.getElementById("txtOK").checked = false;
                    // $("#txtNG").removeProp("checked");
                    document.getElementById("txtNG").checked = false;
                    $('#txtNValue').val("");
                    $('#txtNUnit').val("");// 单位
                    $('#txtDateValue').val("");

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#LMgs").html("您未登陆系统，请先登录！")
                    //location.href = "Login.htm";
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_TestItem') {
                    $("#LMgs").html("已测试完成")
                    //location.href = "Login.htm";
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_NORELEVANCE') {
                    $("#LMgs").html("请先完成序列号关联到抽检批号上")
                    layer.msg("请先完成序列号关联到抽检批号上", { icon: 0 })
                    PRDSheet_onclick(4)
                    //location.href = "Login.htm";
                }
                else {
                    $("#LMgs").html("系统出错，请重试1！")
                }
            },
            error: function (data) {
                $("#LMgs").html("系统出错，请重试2！")
            }
        });
    });



    //  作业执行:测试项时，点击 不涉及 按钮
    $('#NotTest_Btn').click(function () {
        $("#NotTest_Btn").attr({ "disabled": "disabled" });
        $("#L_TestMgs").html("");
        var sEXEMan = $('#txtInMan').val();  //作业执行人员账号
        var sEXENo = $("#txtEXENo").val(); // 作业编号
        var sUnit = $("#txtEWorkCenter").val(); // 作业单元
        var sWO = $("#txtOrderNo").val();  // 工单号
        var sSerial = $('#txtNowSerialNo').val();  // 序列号
        var sProcNo = $('#txtNowProcNo').val();  // 工序编码 
        var sVer = $('#txtNowProcVer').val();  // 工序版本
        var sXW = $("#txtConditionNo").val(); //工序行为
        var sSpecNo = $('#txtSpecNo').val();  // 测试项编号
        var sBatch = $("#txtBZBatch").val(); // 上层批次：包装上层批次--包装送检批次
        var sFlag = "29-6-1";

        if ($('#txtNotNeed').val() != "Y") {  // $('#txtNotNeed').val("Y");  // =Y 如果是这里自动点击不涉及的，就不需要弹出对话框了。
            var bln = window.confirm("您确定该测试项不涉及本次测试吗?");  // 提示信息
            if (!bln) { // 不往下执行
                $("#NotTest_Btn").removeAttr("disabled");
                return;
            }
        }


        var Data = '';
        var Params = { No: sSerial, Name: "", Item: sProcNo, MNo: sVer, MName: sXW, A: sEXENo, B: sSpecNo, C: "", D: "", E: "", F: "", G: sWO, H: "", I: "", J: sBatch, InMan: sEXEMan, Remark: sUnit, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPPRDTestInfo&CFlag=29-6",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    ShowPRDSNTestInfo(sSerial, sProcNo, sVer);
                    $("#NotTest_Btn").removeAttr("disabled");

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#LMgs").html("您未登陆系统，请先登录！")
                    $("#NotTest_Btn").removeAttr("disabled");
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_TestItem') {
                    $("#LMgs").html("已测试完成")
                    $("#NotTest_Btn").removeAttr("disabled");
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_NORELEVANCE') {
                    $("#LMgs").html("请先完成序列号关联到抽检批号上")
                    layer.msg("请先完成序列号关联到抽检批号上", { icon: 0 })
                    PRDSheet_onclick(4)
                    $("#NotTest_Btn").removeAttr("disabled");
                }
                else {
                    $("#LMgs").html("系统出错，请重试1！");
                    $("#NotTest_Btn").removeAttr("disabled");
                }
            },
            error: function (data) {
                $("#LMgs").html("系统出错，请重试2！");
                $("#NotTest_Btn").removeAttr("disabled");
            }
        });
    });



    //  作业执行信息变更:填写测试项，提交 按钮  -- 这个代码和作业执行点击 下一项 和相似
    $('#EditTestItem_Btn').click(function () {
        $("#L_TestMgs").html("");
        $("#EditTestItem_Btn").attr({ "disabled": "disabled" });
        var sEXEMan = $('#txtInMan').val();  //作业执行人员账号
        var sEXENo = $("#txtEditEXENo").val(); // 作业编号
        var sWO = $("#txtSOrderNo").val();  // 工单号
        var sSerial = $('#txtEditSN').val();  // 序列号
        var sProcNo = $('#txtEditGXNo').val();  // 工序编码 
        var sVer = $('#txtEditGXVer').val();  // 工序版本
        var sFlag = "29-6-2";
        var stxtFlowNum = $("#txtFlowNum").val()
        var sSpecNo = $('#txtSpecNo').val();  // 测试项编号
        var sDStdV = $('#txtDStdV').val();// 标准值：下限，如200
        var sDStdB = $('#txtDStdB').val();// 是否包含标准值 -- 下限
        var sUStdV = $('#txtUStdV').val();// 标准值：上限，如600
        var sUStdB = $('#txtUStdB').val();// 是否包含标准值 -- 上限
        var sRKind = $('#txtRKind').val();// 上下限标识
        var sVKind = $('#txtVKind').val();// 数据类型
        var sUser = "";// 账号密码
        var sVUnit = $('#txtNUnit').val();// 单位

        var sCause = $("#txtcause").val()
        var soldTestValue = $("#txtoldTestValue").val()//原测试值

        // 测试值
        var sV1 = "";  // 布尔值的
        var sV2 = $('#txtDValue').val();// 介于的测试值-下限  200   20231127：暂时不使用这个输入框，都是使用 txtNValue，记录测试值
        var sV3 = $('#txtUValue').val();// 介于的测试值-上限  600   20231127：暂时不使用这个输入框，都是使用 txtNValue，记录测试值
        var sV4 = "";  // 测试总体结论


        if (sSerial == "") {
            $("#L_TestMgs").html("获取不到序列号！");
            $("#EditTestItem_Btn").removeAttr("disabled");
            return;
        }
        //if (sEXENo == "") {
        //    $("#L_TestMgs").html("获取不到工序！");
        //    $("#EditTestItem_Btn").removeAttr("disabled");
        //    return;
        //}

        if ($("#txtPass").is(':checked')) { // 总体结论-PASS
            sV4 = "PASS"
        }
        if ($('#txtFail').is(':checked')) { // 总体结论-PASS
            sV4 = "FAIL"
        }

        if (sVKind == "布尔类型") {
            if ($('#txtOK').is(':checked')) { // 布尔测试值
                sV1 = "OK"
            }
            if ($('#txtNG').is(':checked')) { // 布尔测试值NG
                sV1 = "NG"
            }
            if (sV1 == "") {
                $("#L_TestMgs").html("请选择测试值！");
                $("#EditTestItem_Btn").removeAttr("disabled");
                return;
            }
        }
        else if (sVKind == "日期类型") {
            sV3 = $('#txtDateValue').val();
            if (sV3 == "") {
                $("#L_TestMgs").html("请填写测试值！");
                $("#EditTestItem_Btn").removeAttr("disabled");
                return;
            }
        }
        else if (sVKind == "数值类型") {  // 填写数值，数字
            if (sRKind == "介于") {
                sV3 = $('#txtNValue').val();

                if (sV3 == "") {
                    $("#L_TestMgs").html("请填写测试值！");
                    $("#EditTestItem_Btn").removeAttr("disabled");
                    return;
                }
                if (isNaN(sV3)) {  // $('#txtUValue').val("");// 介于的测试值-上限  600
                    $("#L_TestMgs").html("请填写数值！");
                    $("#EditTestItem_Btn").removeAttr("disabled");
                    return;
                }
            }
            else if (sRKind == "大于") {
                sV3 = $('#txtNValue').val();// 其他测试值
                if (sV3 == "") {
                    $("#L_TestMgs").html("请填写测试值！");
                    $("#EditTestItem_Btn").removeAttr("disabled");
                    return;
                }
                if (isNaN(sV3)) {  // $('#txtUValue').val("");// 介于的测试值-上限  600
                    $("#L_TestMgs").html("请填写数值！");
                    $("#EditTestItem_Btn").removeAttr("disabled");
                    return;
                }
            }
            else if (sRKind == "小于") {
                sV3 = $('#txtNValue').val();// 其他测试值
                if (sV3 == "") {
                    $("#L_TestMgs").html("请填写测试值！");
                    $("#EditTestItem_Btn").removeAttr("disabled");
                    return;
                }
                if (isNaN(sV3)) {  // $('#txtUValue').val("");// 介于的测试值-上限  600
                    $("#L_TestMgs").html("请填写数值！");
                    $("#EditTestItem_Btn").removeAttr("disabled");
                    return;
                }
            }
            else if (sRKind == "等于") {
                sV3 = $('#txtNValue').val();// 其他测试值
                if (sV3 == "") {
                    $("#L_TestMgs").html("请填写测试值！");
                    $("#EditTestItem_Btn").removeAttr("disabled");
                    return;
                }
                if (isNaN(sV3)) {  // $('#txtUValue').val("");// 介于的测试值-上限  600
                    $("#L_TestMgs").html("请填写数值！");
                    $("#EditTestItem_Btn").removeAttr("disabled");
                    return;
                }
            }
            else {
                sV3 = $('#txtNValue').val();// 其他测试值
            }
        }
        else {  // 其他类型，填写啥，就保存啥
            sV3 = $('#txtNValue').val();// 其他测试值
            if (sV3 == "") {
                $("#L_TestMgs").html("请填写测试值！");
                $("#EditTestItem_Btn").removeAttr("disabled");
                return;
            }
        }

        if (sV4 == "") {
            $("#L_TestMgs").html("请选择测试结论！");
            $("#EditTestItem_Btn").removeAttr("disabled");
            return;
        }
        if ((sV1 == "OK") && (sV4 == "FAIL")) {
            $("#L_TestMgs").html("测试值和测试结论有冲突！");
            $("#EditTestItem_Btn").removeAttr("disabled");
            return;
        }
        if ((sV1 == "NG") && (sV4 == "PASS")) {
            $("#L_TestMgs").html("测试值和测试结论有冲突！");
            $("#EditTestItem_Btn").removeAttr("disabled");
            return;
        }

        if (($('#txtCHUserNo').val() == "") || ($('#txtCHUserName').val() == "")) {
            $("#L_TestMgs").html("请输入账号密码验证修改操作！");
            $("#EditTestItem_Btn").removeAttr("disabled");
            return;
        }
        sUser = $('#txtCHUserNo').val() + $('#txtCHUserName').val();

        if (sCause == "") {
            $("#L_TestMgs").html("请输入修改原因！");
            $("#EditTestItem_Btn").removeAttr("disabled");
            return;
        }

        var bln = window.confirm("您确定要修改该测试值吗?");  // 提示信息
        if (!bln) { // 不往下执行
            $("#EditTestItem_Btn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { No: sSerial, Name: sVKind, Item: sProcNo, MNo: sVer, MName: sUser, A: sEXENo, B: sSpecNo, C: sV1, D: sV2, E: sV3, F: sV4, G: sWO, H: sVUnit, I: "", J: soldTestValue, InMan: sEXEMan, Remark: sCause, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPPRDTestInfo&CFlag=29-6",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#txtCHUserNo").val("")
                    $("#txtCHUserName").val("")
                    $("#txtcause").val("")

                    ShowTestItemInfo(sSerial, sProcNo, sVer);
                    ShowTestItemLogInfo(sSerial, sProcNo, sVer);
                    $("#EditTestItem_Btn").removeAttr("disabled");
                    document.getElementById('Div_EditTestItem').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                    export_pdf_custom(Params, stxtFlowNum)
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#L_TestMgs").html("您未登陆系统，请先登录！")
                    $("#EditTestItem_Btn").removeAttr("disabled");
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_TestItem') {
                    $("#L_TestMgs").html("已测试完成");
                    $("#EditTestItem_Btn").removeAttr("disabled");
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_NOUSER') {
                    $("#L_TestMgs").html("输入的账号密码不正确");
                    $("#EditTestItem_Btn").removeAttr("disabled");
                }
                else {
                    $("#L_TestMgs").html("系统出错，请重试1！");
                    $("#EditTestItem_Btn").removeAttr("disabled");
                }
            },
            error: function (data) {
                $("#L_TestMgs").html("系统出错，请重试2！");
                $("#EditTestItem_Btn").removeAttr("disabled");
            }
        });
    });


    //  作业执行信息变更:测试项时，点击 不涉及 按钮
    $('#NotTest_EditBtn').click(function () {
        $("#NotTest_EditBtn").attr({ "disabled": "disabled" });
        $("#L_TestMgs").html("");
        var sEXEMan = $('#txtInMan').val();  //作业执行人员账号
        var sEXENo = $("#txtEditEXENo").val(); // 作业编号
        var sWO = $("#txtSOrderNo").val();  // 工单号
        var sSerial = $('#txtEditSN').val();  // 序列号
        var sProcNo = $('#txtEditGXNo').val();  // 工序编码 
        var sVer = $('#txtEditGXVer').val();  // 工序版本
        var sSpecNo = $('#txtSpecNo').val();  // 测试项编号
        var sFlag = "29-6-3";
        var stxtFlowNum = $("#txtFlowNum").val()
        var sCause = $("#txtcause").val()
        var soldTestValue = $("#txtoldTestValue").val()

        if (sCause == "") {
            $("#L_TestMgs").html("请输入修改原因！");
            $("#NotTest_EditBtn").removeAttr("disabled");
            return;
        }

        var bln = window.confirm("您确定该测试项不涉及本次测试吗?");  // 提示信息
        if (!bln) { // 不往下执行
            $("#NotTest_EditBtn").removeAttr("disabled");
            return;
        }



        var Data = '';
        var Params = { No: sSerial, Name: "", Item: sProcNo, MNo: sVer, MName: "", A: sEXENo, B: sSpecNo, C: "", D: "", E: "", F: "", G: sWO, H: "", I: "", J: soldTestValue, InMan: sEXEMan, Remark: sCause, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPPRDTestInfo&CFlag=29-6",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    ShowTestItemInfo(sSerial, sProcNo, sVer);
                    ShowTestItemLogInfo(sSerial, sProcNo, sVer);
                    $("#NotTest_EditBtn").removeAttr("disabled");
                    document.getElementById('Div_EditTestItem').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';
                    export_pdf_custom(Params, stxtFlowNum)
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#L_TestMgs").html("您未登陆系统，请先登录！")
                    $("#NotTest_EditBtn").removeAttr("disabled");
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_TestItem') {
                    $("#L_TestMgs").html("已测试完成")
                    $("#NotTest_Btn").removeAttr("disabled");
                }
                else {
                    $("#L_TestMgs").html("系统出错，请重试1！");
                    $("#NotTest_EditBtn").removeAttr("disabled");
                }
            },
            error: function (data) {
                $("#L_TestMgs").html("系统出错，请重试2！");
                $("#NotTest_EditBtn").removeAttr("disabled");
            }
        });
    });



    // 作业执行-设备刷新点检记录：扫描设备时，没有点检记录，点击详细进入界面后，可以刷新
    $('#DeviceCheck_Btn').click(function () {
        $("#DeviceCheck_Btn").attr({ "disabled": "disabled" });
        var sEXEMan = $('#txtInMan').val();  //作业执行人员账号
        var sCode = $('#txtDJDevice').val();
        var sEXENo = $("#txtEXENo").val();  // 作业执行编码
        var sSerialNo = $("#txtNowSerialNo").val();  //  当前执行的序列号
        var sWO = $('#txtOrderNo').val();
        var sProc = $("#txtNowProcNo").val();  // 工序
        var sProcName = $('#txtNowProc').val();  // 工序名称
        var sVer = $('#txtNowProcVer').val();  // 工序版本
        $("#div_warningDJ").val("");
        $("#div_warningDJ").hide();
        var sFlag = "29-3-1";


        var Data = '';
        var Params = { No: sCode, Name: "", Item: sWO, MNo: "", MName: "", A: sEXENo, B: sSerialNo, C: sProc, D: sProcName, E: sVer, F: "", InMan: sEXEMan, Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPPRDDeviceInfo&CFlag=29-3-1",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $('#txtDJRWNo').val(parsedJson.RNo);  //点检任务编号
                    $('#txtDJCPNo').val(parsedJson.CPNo);  //来源
                    ShowSNDeviceCheckInfo(parsedJson.RNo, "", "");
                    $("#DeviceCheck_Btn").removeAttr("disabled");

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                    $("#DeviceCheck_Btn").removeAttr("disabled");
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Check') {
                    ErrorMessage("该设备已有点检任务，无需刷新！", 2000)
                    $("#DeviceCheck_Btn").removeAttr("disabled");
                }
                else {
                    ErrorMessage("系统出错1！", 2000)
                    $("#DeviceCheck_Btn").removeAttr("disabled");
                }
            },
            error: function (data) {
                ErrorMessage("系统出错2！", 2000)
                $("#DeviceCheck_Btn").removeAttr("disabled");
            }
        });


    });


    // 作业执行-关联完成：包装批次关联序列号，点击关联完成
    //$('#GLOver_Btn').click(function () {
    //    $("#GLOver_Btn").attr({ "disabled": "disabled" });
    //    var sEXEMan = $('#txtInMan').val();  //作业执行人员账号
    //    var sUpBatch = $('#txtBZBatch').val();  // 上层批次号
    //    var sEXENo = $("#txtEXENo").val();  // 作业执行编码
    //    var sWO = $('#txtOrderNo').val();  // 工单号
    //    var sSerial = $('#txtNowSerialNo').val();  // 序列号
    //    var sProcNo = $('#txtNowProcNo').val();  // 工序编码
    //    var sVer = $('#txtNowProcVer').val();  // 工序版本
    //    var sUnit = $("#txtEWorkCenter").val();  // 作业单元
    //    var sFlowOrder = $('#txtFlowOrder').val();  // 工序顺序编号
    //    $("#div_warningGL").val("");
    //    var sFlag = "29-8";


    //    var Data = '';
    //    var Params = { No: sSerial, Name: sUnit, Item: "", MNo: "", MName: "", A: sUpBatch, B: sProcNo, C: "", D: sVer, E: sFlowOrder, F: sEXENo, G: sWO, H: "", I: "", J: "", InMan: sEXEMan, Remark: "", Flag: sFlag };
    //    var Data = JSON.stringify(Params);

    //    //11单个工序生成，12单个工序生成12
    //    var sN = sUpBatch == "" ? 11 : 12;

    //    $.ajax({
    //        type: "POST",
    //        url: "../Service/OrderAjax.ashx?OP=OPPRDUpbatchInfo&CFlag=29-8",
    //        data: { Data: Data },
    //        success: function (data) {
    //            var parsedJson = jQuery.parseJSON(data);

    //            if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

    //                PRDSheet_onclick(4);  //

    //                document.getElementById('Div_GLOver').style.display = 'none';
    //                document.getElementById('fade').style.display = 'none';

    //                $('#txtOPRec').val(sUpBatch + " 关联完成！" + "\n" + $('#txtOPRec').val());
    //                GetResetInputText(1); // 初始化界面，初始化输入框

    //                CreateDHR(sN, Params)

    //                $("#GLOver_Btn").removeAttr("disabled");

    //            } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
    //                ErrorMessage("您未登陆系统，请先登录！",2000)
    //                $("#GLOver_Btn").removeAttr("disabled");
    //                //location.href = "Login.htm";
    //            } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_GLSN') {
    //                ErrorMessage("无需要关联的序列号！", 2000)
    //                $("#GLOver_Btn").removeAttr("disabled");
    //            }
    //            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_NOTTEST') {
    //                ErrorMessage("有些序列号尚未测试完成！", 2000)
    //                $("#GLOver_Btn").removeAttr("disabled");
    //            }
    //            else {
    //                ErrorMessage("系统出错1！", 2000)
    //                $("#GLOver_Btn").removeAttr("disabled");
    //            }
    //        },
    //        error: function (data) {
    //            ErrorMessage("系统出错2！", 2000)
    //            $("#GLOver_Btn").removeAttr("disabled");
    //        }
    //    });
    //});



    // 作业执行：扫描不良现象，填写现象描述，点击提交  
    $('#BLXX_Btn').click(function () {
        $("#BLXX_Btn").attr({ "disabled": "disabled" });

        var sEXEMan = $('#txtInMan').val();  //作业执行人员账号
        var sCode = $('#txtECode').val();
        var sEDesc = $('#txtEDesc').val();
        var sEXENo = $("#txtEXENo").val(); // 作业编号
        var sSerial = $('#txtNowSerialNo').val();  // 序列号
        var sWO = $('#txtOrderNo').val();  // 工单号
        var sProcNo = $('#txtNowProcNo').val();  // 工序编码
        var sProcName = $('#txtNowProc').val();  // 工序名称
        var sVer = $('#txtNowProcVer').val();  // 工序版本
        var sPrdKind = $("#PrdKind").val() //作业类型
        $('#div_warningBLXX').html("");

        var sFlag = "29-1";  //  29-1 标识是扫描不良现象代码

        if (sEXENo == "") {
            ErrorMessage("获取不到作业编号，请检查是否开始作业", 2000)
            $("#BLXX_Btn").removeAttr("disabled");
            return;
        }
        if (sSerial == "") {
            ErrorMessage("获取不到生产中的序列号，请检查是否开始作业", 2000)
            $("#BLXX_Btn").removeAttr("disabled");
            return;
        }
        if (sCode == "") {
            ErrorMessage("请输入异常代码", 2000)
            $("#BLXX_Btn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { No: sCode, Name: "", Item: sWO, MNo: "", MName: "", A: sEXENo, B: sSerial, C: sProcNo, D: sProcName, E: sVer, F: sEDesc, G: sPrdKind, InMan: sEXEMan, Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPPRDYCCodeInfo&CFlag=29-1",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $('#txtOPRec').val(sCode + " 扫描成功！" + "\n" + $('#txtOPRec').val());
                    $("#BLXX_Btn").removeAttr("disabled");
                    $('#txtECode').val("");
                    $('#txtEName').val("");
                    $('#txtEDesc').val("");
                    $('#PrdKind').val("");
                    $('#txtECode').focus();
                    $('#dgDataListYC').bootstrapTable('refresh', { url: '../Service/OrderAjax.ashx?OP=GetPRDInfoTwo&CFlag=29-1&CNO=' + sEXENo });
                    layer.msg("添加成功")

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_EXISTYCCODE') {
                    ErrorMessage("异常代码不存在，请确认！", 2000)
                    $("#BLXX_Btn").removeAttr("disabled");
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTYCCODE') {
                    ErrorMessage("该异常编号已扫描，请确认！", 2000)
                    $("#BLXX_Btn").removeAttr("disabled");
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                    ErrorMessage("该作业的不合格单据已提交或已维修，不可再扫描！", 2000)
                    $("#BLXX_Btn").removeAttr("disabled");
                }
                else {
                    ErrorMessage("系统出错，请重试1！", 2000)
                    $("#BLXX_Btn").removeAttr("disabled");
                }
            },
            error: function (data) {
                ErrorMessage("系统出错，请重试2！", 2000)
                $("#BLXX_Btn").removeAttr("disabled");
            }
        });



    });





    // 作业执行 保存:工序完工提交信息
    //$('#ProcOver_Btn').click(function () {
    //    $("#ProcOver_Btn").attr({ "disabled": "disabled" });
    //    $("#div_warning2").html("");
    //    $("#div_warning2").hide();

    //    var sEXEMan = $('#txtInMan').val();  //作业执行人员账号
    //    var sUnit = $("#txtEWorkCenter").val();  // 作业单元
    //    var sEXENo = $("#txtEXENo").val(); // 作业编号 
    //    var sOKind = $("#txtOrderKind").val(); // 工单类别，正常工单，重工单
    //    var sWO = $("#txtOrderNo").val();  // 工单号
    //    var sSerialNo = $("#txtOSerialNo").val(); //  作业完工弹层的序列号
    //    var sSerial = $('#txtNowSerialNo').val();  // 作业执行带出的序列号
    //    var sProc = $("#txtOProcNo").val();
    //    var sVer = $('#txtNowProcVer').val();  // 工序版本
    //    var sFlowOrder = $('#txtFlowOrder').val();  // 工序顺序编号
    //    var sProcName = $("#txtOProcName").val();
    //    var sXW = $("#txtConditionNo").val(); //工序行为
    //    var sAct = $("#txtProcAct").val(); // 工序行为下拉选择的内容
    //    var sUpBatch = $("#txtBZBatch").val();  // 上层批次：包装上层批次--包装送检批次
    //    var sFlag = $('#txtAEFlag').val();  // 28-3:单个序列号工序完工；28-3-1：工序行为选择了“整机包装检验”需要按照整机送检批号完工；28-3-2：单个序列号整机放行；28-3-3：批量序列号整机放行

    //    if (sUnit == "") {
    //        ErrorMessage("获取不到作业单元！", 2000)
    //        $("#ProcOver_Btn").removeAttr("disabled");
    //        return;
    //    }
    //    if (sSerialNo == "") {
    //        ErrorMessage("无完工的序列号！", 2000)
    //        $("#ProcOver_Btn").removeAttr("disabled");
    //        return;
    //    }
    //    if (sProc == "") {
    //        ErrorMessage("无完工的工序！", 2000)
    //        $("#ProcOver_Btn").removeAttr("disabled");
    //        return;
    //    }

    //    if (sFlag == "28-3-3") {  // 批量DHR工序放行（IPQC工序），后台还需要一个序列号，因此转换一下，传给后台SQL
    //        sSerialNo = sSerial;
    //    }


    //    var Data = '';
    //    var Params = { No: sSerialNo, Name: sUnit, Item: sOKind, MNo: "", MName: "", A: sUpBatch, B: sProc, C: sXW, D: sVer, E: sFlowOrder, F: sEXENo, G: sWO, H: "", I: "", J: "", InMan: sEXEMan, Remark: sAct, Flag: sFlag };
    //    var Data = JSON.stringify(Params);

    //    //11单个工序生成，12单个工序生成12
    //    var sN = sUpBatch == "" ? 11 : 12;

    //    $.ajax({
    //        type: "POST",
    //        url: "../Service/OrderAjax.ashx?OP=OPPRDOverInfo&CFlag=" + sFlag,
    //        data: { Data: Data },
    //        success: function (data) {
    //            var parsedJson = jQuery.parseJSON(data);

    //            if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                $("#div_warning2").html("");

    //                $('#Div_ProcOver').css("display","none")
    //                $('#fade').css("display", "none")

    //                $('#txtOPRec').val(sSerialNo + "  " + sProc + " " + sProcName + " 工序完工！" + "\n" + $('#txtOPRec').val());

    //                GetResetInputText(1); // 初始化界面，初始化输入框

    //                CreateDHR(sN, Params)

    //            } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage("您未登陆系统，请先登录！", 2000)
    //                //location.href = "Login.htm";
    //            } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTSOVER') {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage("该序列号生产已完成，无需再生产！", 2000)
    //            }
    //            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTSPROCOVER') {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage("该工序生产已完成，无需再提交！", 2000)
    //            }
    //            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_MATER') {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage("还有未扣减完成的物料，请检查是追溯物料还是装箱物料！", 2000)
    //                PRDSheet_onclick(1);
    //            }
    //            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_Pack') {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage("该工序需要包装，目前未包装或包装关联中！", 2000)
    //            }
    //            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_TestItem') {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage("该工序测试项未填写完成，请确认！", 2000)
    //                PRDSheet_onclick(3);
    //            }
    //            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_Device') {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage("该工序设备未扫描完成，请确认！", 2000)
    //                PRDSheet_onclick(7);
    //            }
    //            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_ZKP') {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage("该工序质控品未扫描完成，请确认！", 2000)
    //                PRDSheet_onclick(8);
    //            }
    //            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'CheckExist') {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage("有设备未点检完成，请点检！", 2000)
    //                PRDSheet_onclick(7);
    //            }
    //            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'NOTCOMMITYC') {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage("有异常单未提交，请提交异常单完工", 2000)
    //                PRDSheet_onclick(6);
    //                $('#Div_BLXX').hide(); // 屏蔽不良的输入框
    //                $('#fade').hide(); // 屏蔽不良的输入框
    //            }
    //            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'UP_Batch') {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage("需把该序列号关联到上层批次，请先扫描序列号关联！", 2000)
    //            }
    //            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_PHTestItem') {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage(parsedJson.RNo + " 序列号下有序列号测试记录，但没有测试完成！", 2000);
    //                $('#LMgs').html(parsedJson.RNo + " 序列号下有序列号测试记录，但没有测试完成！");
    //            }
    //            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_PHSCANSB') {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage(parsedJson.RNo + " 序列号下有设备信息，但没有扫描记录！", 2000);
    //                $('#LMgs').html(parsedJson.RNo + " 序列号下有设备信息，但没有扫描记录！");
    //                PRDSheet_onclick(7);
    //            }
    //            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_PHSCANZK') {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage(parsedJson.RNo + " 序列号有质控品信息，但没有扫描记录！", 2000);
    //                $('#LMgs').html(parsedJson.RNo + " 序列号下有质控品信息，但没有扫描记录！");
    //                PRDSheet_onclick(8);
    //            }
    //            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_Enough') {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage("该检验批，检验中的序列号未达到样本量，请确认！", 2000)
    //            }
    //            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_NOTPRDING') {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage("按检验批放行的序列号，有些不在生产中，先扫描序列号进行放行检验！", 2000)
    //            }
    //            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_SNNOTPRDING') {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage("该序列号不在生产中或已完成，无法放行，请确认。", 2000)
    //            }
    //            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_NOTFILE') {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage(" 未产生DHR文件，请确认！", 2000);
    //                $('#LMgs').html(" 未产生DHR文件，请确认！");
    //            }
    //            else {
    //                $("#ProcOver_Btn").removeAttr("disabled");
    //                ErrorMessage("系统出错，请重试1！", 2000)
    //            }
    //        },
    //        error: function (data) {
    //            $("#ProcOver_Btn").removeAttr("disabled");
    //            ErrorMessage("系统出错，请重试2！", 2000)
    //        }
    //    });
    //});



    //  作业执行：在包装检验工序，修改检验方案
    $('#EditFA_Btn').click(function () {
        $("#txtSamplPlan").removeAttr("disabled");
    });


    //作业执行：在包装检验工序，修改检验方案
    $('#txtSamplPlan').change(function () {

        var sOName = $('#txtSamplPlanNameR').val();  // 原来的检验方案名称
        var sONO = $('#txtSamplPlanR').val();  // 原来的检验方案编码
        var sNNO = $('#txtSamplPlan').val();  //切换后的检验方案编号
        var sNName = $('#txtSamplPlan option:selected').text(); //选中的文本 获取下拉值    切换后的检验方案
        var sFlag = "29-10";

        if (sNNO == "") {  // 没有编号
            return;
        }

        if (sOName == sNName) {  // 没有编号
            return;
        }

        var bln = window.confirm("您确定要修改检验方案么?");  // 提示信息
        if (!bln) { // 不往下执行
            $('#txtSamplPlan').val(sONO);
            return;
        }

        ChangeInspect();
    });


    //  根据工单号，带出相关性信息
    $('#txtBaseOrderNo').blur(function () {
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $('#sFMsg').html("");

        var sNo = $("#txtBaseOrderNo").val();
        var Flag = "111-3";


        var Data = '';
        var Params = { No: sNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=GetOrderInfoByNo&CFlag=" + Flag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $("#txtPMaterNo").val(sStr.MaterNo);
                    $("#txtPMaterName").val(sStr.MaterName);
                    $("#txtFNum").val(sStr.OrderNum);
                    $("#txtModel").val(sStr.MaterSpec);
                    $("#txtOverNum").val(sStr.OverNum);

                }
                else if (parsedJson != undefined && parsedJson != '') {
                    $("#div_warning").html(parsedJson.Msg);
                    $("#div_warning").show();
                }
            },
            error: function (data) {
                $("#div_warning").html("系统出错，请重试2！");
                $("#div_warning").show();
            }
        });
    });


    //  添加工单时，根据物料编码获取相关物料信息
    $('#txtOrderMaterNo').blur(function () {
        $("#div_warning").hide();
        $("#divsuccess").hide();

        var sNo = $("#txtOrderMaterNo").val();
        var Flag = "33-99";

        if (sNo == "") {
            $("#div_warning").html("请输入物料编码！")
            $("#divsuccess").show();
            return;
        }

        var Data = '';
        var Params = { No: sNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetMaterInfoByNo&CFlag=" + Flag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                $("#txtOrderMaterName").val("");
                // $("#txtModel").val("");
                $("#txtOrderUnit").val("");
                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $("#txtOrderMaterName").val(sStr.MaterName);
                    // $("#txtModel").val(sStr.MaterSpec);
                    $("#txtOrderUnit").val(sStr.MaterUnit);
                }
            },
            error: function (data) {
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });



    //  保存:手工添加一个工单
    $('#OP_OrderBtn').click(function () {
        $("#OP_OrderBtn").attr({ "disabled": "disabled" });
        $("#Div_AddOrderMgs").hide();
        $("#AddDoing").show();

        var sOrderNo = $("#txtAddOrderNo").val();
        var sMNo = $("#txtOrderMaterNo").val();
        var sMName = $("#txtOrderMaterName").val();
        var sRDate = $("#txtReqDate").val();   // 需求日期
        var sSDate = $("#txtPSDate").val();   // 计划开始时间
        var sEDate = $("#txtPEDate").val();   // 计划结束时间
        var sNum = $("#txtAddOrderNum").val();
        var sKind = $("#txtCTKind").val();
        var sDept = $('#txtLDept option:selected').text().trim(); //选中的文本 获取下拉值  var sDept = $("#txtLDept").val(); // 部门 得到 编号
        var sRemark = $("#txtAddOrderRemark").val();
        var sK = $("#txtCTKindR").val();// 一开始的工单类别，主要给自定义发放序列号时，第一次可修改工单的基本信息
        var sFlag = $("#txtOAEFlag").val();  //   22新增  23 修改


        if (sMNo == "") {
            $("#Div_AddOrderMgs").html("请输入产品编码！");
            $("#Div_AddOrderMgs").show();
            $("#OP_OrderBtn").removeAttr("disabled");
            $("#AddDoing").hide();
            return;
        }
        if (sMName == "") {
            $("#Div_AddOrderMgs").html("获取不到产品描述，请确认编码输入是否正确");
            $("#Div_AddOrderMgs").show();
            $("#OP_OrderBtn").removeAttr("disabled");
            $("#AddDoing").hide();
            return;
        }
        if (sDept == "") {
            $("#Div_AddOrderMgs").html("请选择部门");
            $("#Div_AddOrderMgs").show();
            $("#OP_OrderBtn").removeAttr("disabled");
            $("#AddDoing").hide();
            return;
        }



        var Data = '';
        var Params = { No: "", Name: "", Item: sOrderNo, MNo: sMNo, MName: "", A: sNum, B: sRDate, C: sSDate, D: sEDate, E: sKind, F: sDept, G: sK, Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPOrderInfo&CFlag=22",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#OP_OrderBtn").removeAttr("disabled");
                    $("#Div_AddOrderMgs").html("");
                    $("#Div_AddOrderMgs").show();

                    $("#txtSNo").val(sOrderNo);
                    $('#Order_open').click();
                    $("#txtSNo").val("");

                    document.getElementById('Div_AddOrder').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';
                    $("#AddDoing").hide();

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#OP_OrderBtn").removeAttr("disabled");
                    $("#Div_AddOrderMgs").html("您未登陆系统，请先登录！");
                    $("#Div_AddOrderMgs").show();
                    //location.href = "Login.htm";
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTWO') {
                    $("#OP_OrderBtn").removeAttr("disabled");
                    $("#Div_AddOrderMgs").html("该工单已存在！");
                    $("#Div_AddOrderMgs").show();
                    $("#AddDoing").hide();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_NotWOFlow') {
                    $("#OP_OrderBtn").removeAttr("disabled");
                    $("#Div_AddOrderMgs").html("该工单没有流程，不能修改和下达！");
                    $("#Div_AddOrderMgs").show();
                    $("#AddDoing").hide();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_NotWOAction') {
                    $("#OP_OrderBtn").removeAttr("disabled");
                    $("#Div_AddOrderMgs").html("重工单的第一道工序必需要设置重工相关的工序行为，请确认！");
                    $("#Div_AddOrderMgs").show();
                    $("#AddDoing").hide();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_NotWOMater') {
                    $("#OP_OrderBtn").removeAttr("disabled");
                    $("#Div_AddOrderMgs").html("该工单BOM没有设置重工的对象，不能下达！");
                    $("#Div_AddOrderMgs").show();
                    $("#AddDoing").hide();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_NotWOProc') {
                    $("#OP_OrderBtn").removeAttr("disabled");
                    $("#Div_AddOrderMgs").html("重工对象必需设置在第一道工序上！" + sOrderNo + " 工单第一道工序是 " + parsedJson.RMsg + " ，请确认！");
                    $("#Div_AddOrderMgs").show();
                    $("#AddDoing").hide();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTSN') {
                    $("#OP_OrderBtn").removeAttr("disabled");
                    $("#Div_AddOrderMgs").html("该工单序列号已发放完成，不允许修改！");
                    $("#Div_AddOrderMgs").show();
                    $("#AddDoing").hide();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'NOTCHANGE') {
                    $("#OP_OrderBtn").removeAttr("disabled");
                    $("#Div_AddOrderMgs").html(parsedJson.RMgs);
                    $("#Div_AddOrderMgs").show();
                    $("#AddDoing").hide();
                    ShowSerialInfoList(parsedJson.Data)
                }
                else {
                    $("#OP_OrderBtn").removeAttr("disabled");
                    $("#Div_AddOrderMgs").html("系统出错，请重试1！")
                    $("#Div_AddOrderMgs").show();
                    $("#AddDoing").hide();
                }
            },
            error: function (data) {
                $("#OP_OrderBtn").removeAttr("disabled");
                $("#Div_AddOrderMgs").html("系统出错，请重试2！")
                $("#Div_AddOrderMgs").show();
                $("#AddDoing").hide();
            }
        });
    });



    //  工单：暂停，启用工单
    $('#OP_ztqyOrderBtn').click(function () {
        $("#OP_ztqyOrderBtn").attr({ "disabled": "disabled" });
        $("#Div_ztqyOrderMgs").hide();

        var sOrderNo = $("#txtztqyOrderNo").val();
        var sKind = $("#txtOPStatus").val();
        var sRemark = $("#txtztqyRemark").val().trim();
        var sFlag = "5";  //   5 


        if (sRemark == "") {
            $("#Div_ztqyOrderMgs").html("请填写原因！");
            $("#Div_ztqyOrderMgs").show();
            $("#OP_ztqyOrderBtn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { No: "", Name: "", Item: sOrderNo, MNo: "", MName: "", A: "", B: "", C: "", D: "", E: sKind, F: "", Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPOrderInfo&CFlag=152",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#OP_ztqyOrderBtn").removeAttr("disabled");

                    layer.msg('操作成功！');

                    $("#txtSNo").val(sOrderNo);
                    $('#Order_open').click();
                    $("#txtSNo").val("");

                    document.getElementById('Div_ztqyOrder').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST888888888') {
                    $("#OP_ztqyOrderBtn").removeAttr("disabled");
                    $("#Div_ztqyOrderMgs").html("该工单已关闭或完工，无需操作！")
                    $("#Div_ztqyOrderMgs").show();
                }
                else {
                    $("#OP_ztqyOrderBtn").removeAttr("disabled");
                    $("#Div_ztqyOrderMgs").html("系统出错，请重试1！")
                    $("#Div_ztqyOrderMgs").show();
                }
            },
            error: function (data) {
                $("#OP_ztqyOrderBtn").removeAttr("disabled");
                $("#Div_ztqyOrderMgs").html("系统出错，请重试2！")
                $("#Div_ztqyOrderMgs").show();
            }
        });
    });


    // 工单：-上传工艺文件
    $('#btnWOFileload').click(function () {
        $("#div_warningFile").val("");

        var p = $("#txtWOFPNo");
        var sSSF = $('#txtAddKind').val();  //  14-5 工单上传工艺文件；  14-5-1: 序列号上传工艺文件
        var sMsg = "";

        if (sSSF == "14-5") {
            sMsg = "无法获取工单号！";
        }
        else {
            sMsg = "无法获取序列号！";
        }
        if (p.val() == "") {
            $("#div_warningFile").html(sMsg);
            $("#div_warningFile").show();
            return;
        }

        var sNo = $("#txtWOFProNo").val();  //工序 
        var sName = $("#txtWOFProName").val();
        var sPVer = $("#txtWOFProVer").val();  //工序版本
        var sStr = p.val() + " " + sNo + " " + sPVer + " ";

        var arr = new Array();
        var sPath = $("#txtPath").val();
        var sUpFile = $("#UpFile").val();  // ,用下面这种方式，解决中文乱码问题。
        arr = sPath.split(';/'); //注split可以用字符或字符串分割
        if (arr.length > 1) {
            $("#div_warningFile").html("每次只能上传一份文件！")
            $("#div_warningFile").show();
            return;
        }
        else {
            $("#Loading").show();
            $.ajaxFileUpload({
                url: '../Service/ApplyUpPicFile.aspx?NNo=' + sStr + '&sFlag=21&sUpFile=' + encodeURI(sUpFile),
                fileElementId: 'UpFile',
                dataType: 'json',
                success: function (data) {
                    //                 var parsedJson = jQuery.parseJSON(data);
                    data = eval(data);
                    var sPath = $("#txtPath").val();
                    $("#txtPath").val(sPath + ";" + data.Path);
                    var sFile = $("#txtFile").val();
                    $("#txtFile").val(sFile + ";" + data.FileName);

                    $("#Loading").hide();

                    $("#div_warningFile").html("上传成功,请填写文件编号，版本，名称，再点击提交。");
                    $("#div_warningFile").show();
                    //alert("上传成功");

                }
            });
        }
    });


    //  工单：保存，添加工艺文件
    $('#WOFileSave_Btn').click(function () {
        $("#WOFileSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warningFile").hide();

        var sWONo = $("#txtWOFPNo").val(); // 工单号
        var sNo = $("#txtWOFProNo").val();  //工序 
        var sName = $("#txtWOFProName").val();
        var sPVer = $("#txtWOFProVer").val();  //工序版本
        var sPath = $("#txtPath").val();  //  文件路径
        var sFile = $("#txtFile").val();  // 文件名称-带扩展名
        var sFNo = $("#txtWOFileNo").val();   // 文件编码
        var sFVer = $("#txtWOFileVer").val();   // 文件版本
        var sFName = $("#txtWOFileName").val();   // 文件名称-显示报表上的
        var sFlag = $('#txtAddKind').val(); //  新增： "14-5";   修改 14-6 

        if (sPath == "") {
            $("#div_warningFile").html("请选择工艺文件！")
            $("#div_warningFile").show();
            $("#WOFileSave_Btn").removeAttr("disabled");
            return;
        }
        if (sFNo == "") {
            $("#div_warningFile").html("请填写文件编号！")
            $("#div_warningFile").show();
            $("#WOFileSave_Btn").removeAttr("disabled");
            return;
        }

        if (sFVer == "") {
            $("#div_warningFile").html("请填写文件版本！")
            $("#div_warningFile").show();
            $("#WOFileSave_Btn").removeAttr("disabled");
            return;
        }
        if (sFName == "") {
            $("#div_warningFile").html("请填写文件名称！")
            $("#div_warningFile").show();
            $("#WOFileSave_Btn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { No: sNo, Name: sName, Item: sWONo, MNo: "", MName: "", A: sPVer, B: sPath, C: sFile, D: sFNo, E: sFVer, F: sFName, Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPOrderInfo&CFlag=14-5",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#WOFileSave_Btn").removeAttr("disabled");

                    ShowOrderFileInfo(sWONo, sNo + sPVer, "");  // 显示工艺文件

                    document.getElementById('Div_WOFile').style.display = 'none';
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#WOFileSave_Btn").removeAttr("disabled");
                    $("#div_warningFile").html("该工艺文件已存在！")
                    $("#div_warningFile").show();
                }
                else {
                    $("#WOFileSave_Btn").removeAttr("disabled");
                    $("#div_warningFile").html("系统出错，请重试1！")
                    $("#div_warningFile").show();
                }
            },
            error: function (data) {
                $("#WOFileSave_Btn").removeAttr("disabled");
                $("#div_warningFile").html("系统出错，请重试2！")
                $("#div_warningFile").show();
            }
        });
    });



    //  序列号：保存，添加工艺文件
    $('#SNFileSave_Btn').click(function () {
        $("#SNFileSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warningFile").hide();

        var sOrderNo = $('#txtSOrderNo').val();
        var sWONo = $("#txtWOFPNo").val(); // 序列号
        var sNo = $("#txtWOFProNo").val();  //工序 
        var sName = $("#txtWOFProName").val();
        var sPVer = $("#txtWOFProVer").val();  //工序版本
        var sPath = $("#txtPath").val();  //  文件路径
        var sFile = $("#txtFile").val();  // 文件名称-带扩展名
        var sFNo = $("#txtWOFileNo").val();   // 文件编码
        var sFVer = $("#txtWOFileVer").val();   // 文件版本
        var sFName = $("#txtWOFileName").val();   // 文件名称-显示报表上的
        var sFlag = $('#txtAddKind').val(); //  新增： "14-5-1";   修改 14-6 

        if (sPath == "") {
            $("#div_warningFile").html("请选择工艺文件！")
            $("#div_warningFile").show();
            $("#SNFileSave_Btn").removeAttr("disabled");
            return;
        }
        if (sFNo == "") {
            $("#div_warningFile").html("请填写文件编号！")
            $("#div_warningFile").show();
            $("#SNFileSave_Btn").removeAttr("disabled");
            return;
        }

        if (sFVer == "") {
            $("#div_warningFile").html("请填写文件版本！")
            $("#div_warningFile").show();
            $("#SNFileSave_Btn").removeAttr("disabled");
            return;
        }
        if (sFName == "") {
            $("#div_warningFile").html("请填写文件名称！")
            $("#div_warningFile").show();
            $("#SNFileSave_Btn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { No: sNo, Name: sName, Item: sWONo, MNo: sOrderNo, MName: "", A: sPVer, B: sPath, C: sFile, D: sFNo, E: sFVer, F: sFName, Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPSerialInfo&CFlag=14-5-1",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#SNFileSave_Btn").removeAttr("disabled");

                    ShowSNFileInfo(sWONo, sNo + sPVer, "");  // 显示工艺文件

                    document.getElementById('Div_WOFile').style.display = 'none';
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#SNFileSave_Btn").removeAttr("disabled");
                    $("#div_warningFile").html("该工艺文件已存在！")
                    $("#div_warningFile").show();
                }
                else {
                    $("#SNFileSave_Btn").removeAttr("disabled");
                    $("#div_warningFile").html("系统出错，请重试1！")
                    $("#div_warningFile").show();
                }
            },
            error: function (data) {
                $("#SNFileSave_Btn").removeAttr("disabled");
                $("#div_warningFile").html("系统出错，请重试2！")
                $("#div_warningFile").show();
            }
        });
    });


    //  工单工艺流程：测试项，如果是“数字类型”时，显示可设置上下限 
    $('#txtValueKind').change(function () {
        $("#div_warningTestItem").hide();

        var sKind = $("#txtValueKind").val();

        if (sKind == "数值类型") {
            $("#div_QJ").show();
        }
        else {
            $("#div_QJ").hide();
        }
        $("#txtRangeKind").val("");
        $("#txtDownV").val("");
        $("#txtUpV").val("");
        $("#CH_Down").removeProp("checked"); //设置为不选中状态
        $("#CH_Up").removeProp("checked"); //设置为不选中状态
    });


    //  产品工艺流程：测试项，如果是“数字类型”时，设置上下限
    $('#txtRangeKind').change(function () {
        $("#div_warningTestItem").hide();

        var sKind = $("#txtRangeKind").val();

        if (sKind == "") {
            $("#div_warningTestItem").html("请选择区间类别！")
            $("#div_warningTestItem").show();
            return;
        }

        $("#txtDownV").val("");
        $("#txtUpV").val("");
        $("#CH_Down").removeProp("checked"); //设置为不选中状态
        $("#CH_Up").removeProp("checked"); //设置为不选中状态

        $("#CH_Down").hide();
        $("#L_BHD").hide();
        $("#txtDownV").hide();
        $("#L_Down").hide();
        $("#L_Deng").hide();
        $("#L_Up").hide();
        $("#txtUpV").hide();
        $("#CH_Up").hide();
        $("#L_BHU").hide();
        if (sKind == "大于") {
            $("#CH_Down").show();
            $("#L_BHD").show();
            $("#txtDownV").show();
            $("#L_Down").show();
        }
        else if (sKind == "小于") {
            $("#L_Up").show();
            $("#txtUpV").show();
            $("#CH_Up").show();
            $("#L_BHU").show();
        }
        else if (sKind == "介于") {
            $("#CH_Down").show();
            $("#L_BHD").show();
            $("#txtDownV").show();
            $("#L_Down").show();
            $("#L_Up").show();
            $("#txtUpV").show();
            $("#CH_Up").show();
            $("#L_BHU").show();
        }
        else if (sKind == "等于") {
            $("#L_Deng").show();
            $("#txtUpV").show();
        }

    });



    //  保存:工单工序版本对应的测试项信息
    $('#WOTestItemSave_Btn').click(function () {
        $("#WOTestItemSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warningTestItem").hide();

        var sTechNo = $("#txtTechNo").val();
        var sMSpec = $('#txtOSpec').val();  // 型号
        var sWONo = $("#txtTIPNo").val();
        var sTNo = $("#txtTIProNo").val();  //工序  (05)总
        var sNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
        var sName = sTNo.substr(sTNo.indexOf(")") + 1, sTNo.length);
        var sPVer = $("#txtTIProVer").val();  //工序版本
        var sNVer = sNo + sPVer;  // 工序和版本
        var sNCH = $("#txtNameCH").val();  //  检验项目
        var sDCH = $("#txtDescCH").val();  //检验要求  之前： 检验条件说明
        var sUnit = $("#txtUnit").val();  //单位
        var sSCH = $("#txtStandardCH").val();   // 检验要求666  231101：不使用这个了
        var sVKind = $("#txtValueKind").val();   // 数据类型
        var sRKind = $("#txtRangeKind").val();   // 请填写区间值
        var sDV = $("#txtDownV").val();   // 下限
        var sUV = $("#txtUpV").val();   // 上限值
        var sSpecNo = $("#txtSpecNo").val(); // 测试项编号
        var sSeqNo = $("#txtSeqNo").val();   // 当前测试项的顺序编号，点击网格某个记录的“增加”时使用
        var sFlag = $("#txtAddKind").val();  //  工单的新增14-8，修改14-9；14-11 在网格行新增测试项； 序列号的新增：，修改：
        var sDF = "";
        var sUF = "";

        if ($("#CH_Down").is(':checked')) {  //  包含下限值
            sDF = "1";
        }
        if ($("#CH_Up").is(':checked')) {  // 包含上限值
            sUF = "1";
        }


        if (sRKind == "大于") {
            if (sDV == "") {
                $("#div_warningTestItem").html("请填写大于值！")
                $("#div_warningTestItem").show();
                $("#WOTestItemSave_Btn").removeAttr("disabled");
                return;
            }
        }
        if (sRKind == "小于") {
            if (sUV == "") {
                $("#div_warningTestItem").html("请填写小于值！")
                $("#div_warningTestItem").show();
                $("#WOTestItemSave_Btn").removeAttr("disabled");
                return;
            }
        }
        if (sRKind == "介于") {
            if ((sDV == "") || (sUV == "")) {
                $("#div_warningTestItem").html("请填写区间值！")
                $("#div_warningTestItem").show();
                $("#WOTestItemSave_Btn").removeAttr("disabled");
                return;
            }
        }
        if (sRKind == "等于") {
            if (sUV == "") {
                $("#div_warningTestItem").html("请填写等于值！")
                $("#div_warningTestItem").show();
                $("#WOTestItemSave_Btn").removeAttr("disabled");
                return;
            }
        }

        if (sNCH == "") {
            $("#div_warningTestItem").html("请输入检验项目！")
            $("#div_warningTestItem").show();
            $("#WOTestItemSave_Btn").removeAttr("disabled");
            return;
        }
        if (sDCH == "") {
            $("#div_warningTestItem").html("请输入检验条件说明！")
            $("#div_warningTestItem").show();
            $("#WOTestItemSave_Btn").removeAttr("disabled");
            return;
        }
        if ((sVKind == "") || (sVKind == "null") || (sVKind == null)) {
            $("#div_warningTestItem").html("请选择数据类型！")
            $("#div_warningTestItem").show();
            $("#WOTestItemSave_Btn").removeAttr("disabled");
            return;
        }

        if (sDV != "") {
            if (isNaN(sDV)) {
                $("#div_warningTestItem").html("请填写数值！")
                $("#div_warningTestItem").show();
                $("#WOTestItemSave_Btn").removeAttr("disabled");
                return;
            }
        }
        if (sUV != "") {
            if (isNaN(sUV)) {
                $("#div_warningTestItem").html("请填写数值！")
                $("#div_warningTestItem").show();
                $("#WOTestItemSave_Btn").removeAttr("disabled");
                return;
            }
        }


        var Data = '';
        var Params = { No: sNVer, Name: sNCH, Item: sWONo, MNo: sDCH, MName: sSCH, A: sVKind, B: sRKind, C: sDV, D: sUV, E: sSpecNo, F: sSeqNo, H: sDF, I: sTechNo, J: sMSpec, K: sNo, L: "", M: sUnit, N: "", O: "", P: "", Remark: sUF, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPOrderInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#div_warningTestItem").html("提交成功！")
                    $("#div_warningTestItem").show();
                    $("#WOTestItemSave_Btn").removeAttr("disabled");

                    ShowWOTestItemInfo(sWONo, sNVer, ""); // 显示测试

                    document.getElementById('Div_WOTestItem').style.display = 'none';

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#WOTestItemSave_Btn").removeAttr("disabled");
                    $("#div_warningTestItem").html("您未登陆系统，请先登录！")
                    $("#div_warningTestItem").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#WOTestItemSave_Btn").removeAttr("disabled");
                    $("#div_warningTestItem").html("该测试记录已存在，请确认！")
                    $("#div_warningTestItem").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                    layer.msg('该工序版本已禁用，不能操作！');
                }
                else {
                    $("#WOTestItemSave_Btn").removeAttr("disabled");
                    $("#div_warningTestItem").html(parsedJson.Msg);
                    $("#div_warningTestItem").show();
                }
            },
            error: function (data) {
                $("#WOTestItemSave_Btn").removeAttr("disabled");
                $("#div_warningTestItem").html("系统出错，请重试2！")
                $("#div_warningTestItem").show();
            }
        });
    });


    //  保存:序列号工序版本对应的测试项信息
    $('#SNTestItemSave_Btn').click(function () {
        $("#SNTestItemSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warningTestItem").hide();

        var sOrderNo = $('#txtSOrderNo').val();
        var sTechNo = $("#txtTechNo").val();
        var sMSpec = $('#txtOSpec').val();  // 型号
        var sWONo = $("#txtTIPNo").val();  // 序列号
        var sTNo = $("#txtTIProNo").val();  //工序  (05)总
        var sNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
        var sName = sTNo.substr(sTNo.indexOf(")") + 1, sTNo.length);
        var sPVer = $("#txtTIProVer").val();  //工序版本
        var sNVer = sNo + sPVer;  // 工序和版本
        var sNCH = $("#txtNameCH").val();  //  检验项目
        var sDCH = $("#txtDescCH").val();  // 231101：修改为：检验要求       这个不用： 检验条件说明
        var sUnit = $("#txtUnit").val();  // 单位
        var sSCH = $("#txtStandardCH").val();   // 检验要求   231101 ：不使用了
        var sVKind = $("#txtValueKind").val();   // 数据类型
        var sRKind = $("#txtRangeKind").val();   // 请填写区间值
        var sDV = $("#txtDownV").val();   // 下限
        var sUV = $("#txtUpV").val();   // 上限值
        var sSpecNo = $("#txtSpecNo").val(); // 测试项编号
        var sSeqNo = $("#txtSeqNo").val();   // 当前测试项的顺序编号，点击网格某个记录的“增加”时使用
        var sFlag = $("#txtAddKind").val();  //  工单的新增14-8，修改14-9；14-11 在网格行新增测试项； 序列号的新增：，修改：
        var sDF = "";
        var sUF = "";

        if ($("#CH_Down").is(':checked')) {  //  包含下限值
            sDF = "1";
        }
        if ($("#CH_Up").is(':checked')) {  // 包含上限值
            sUF = "1";
        }


        if (sRKind == "大于") {
            if (sDV == "") {
                $("#div_warningTestItem").html("请填写大于值！")
                $("#div_warningTestItem").show();
                $("#SNTestItemSave_Btn").removeAttr("disabled");
                return;
            }
        }
        if (sRKind == "小于") {
            if (sUV == "") {
                $("#div_warningTestItem").html("请填写小于值！")
                $("#div_warningTestItem").show();
                $("#SNTestItemSave_Btn").removeAttr("disabled");
                return;
            }
        }
        if (sRKind == "介于") {
            if ((sDV == "") || (sUV == "")) {
                $("#div_warningTestItem").html("请填写区间值！")
                $("#div_warningTestItem").show();
                $("#SNTestItemSave_Btn").removeAttr("disabled");
                return;
            }
        }
        if (sRKind == "等于") {
            if (sUV == "") {
                $("#div_warningTestItem").html("请填写等于值！")
                $("#div_warningTestItem").show();
                $("#SNTestItemSave_Btn").removeAttr("disabled");
                return;
            }
        }

        if (sNCH == "") {
            $("#div_warningTestItem").html("请输入检验项目！")
            $("#div_warningTestItem").show();
            $("#SNTestItemSave_Btn").removeAttr("disabled");
            return;
        }
        if (sDCH == "") {
            $("#div_warningTestItem").html("请输入检验条件说明！")
            $("#div_warningTestItem").show();
            $("#SNTestItemSave_Btn").removeAttr("disabled");
            return;
        }
        if ((sVKind == "") || (sVKind == "null") || (sVKind == null)) {
            $("#div_warningTestItem").html("请选择数据类型！")
            $("#div_warningTestItem").show();
            $("#SNTestItemSave_Btn").removeAttr("disabled");
            return;
        }

        if (sDV != "") {
            if (isNaN(sDV)) {
                $("#div_warningTestItem").html("请填写数值！")
                $("#div_warningTestItem").show();
                $("#SNTestItemSave_Btn").removeAttr("disabled");
                return;
            }
        }
        if (sUV != "") {
            if (isNaN(sUV)) {
                $("#div_warningTestItem").html("请填写数值！")
                $("#div_warningTestItem").show();
                $("#SNTestItemSave_Btn").removeAttr("disabled");
                return;
            }
        }

        var Data = '';
        var Params = { No: sNVer, Name: sNCH, Item: sWONo, MNo: sDCH, MName: sSCH, A: sVKind, B: sRKind, C: sDV, D: sUV, E: sSpecNo, F: sSeqNo, H: sDF, I: sTechNo, J: sMSpec, K: sNo, L: sOrderNo, M: sUnit, N: sTNo, O: "", P: "", Remark: sUF, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPSerialInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#div_warningTestItem").html("提交成功！")
                    $("#div_warningTestItem").show();
                    $("#SNTestItemSave_Btn").removeAttr("disabled");

                    ShowSNTestItemInfo(sWONo + sOrderNo, sNVer, ""); // 显示测试

                    document.getElementById('Div_WOTestItem').style.display = 'none';

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#SNTestItemSave_Btn").removeAttr("disabled");
                    $("#div_warningTestItem").html("您未登陆系统，请先登录！")
                    $("#div_warningTestItem").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#SNTestItemSave_Btn").removeAttr("disabled");
                    $("#div_warningTestItem").html("该测试记录已存在，请确认！")
                    $("#div_warningTestItem").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                    layer.msg('该工序版本已禁用，不能操作！');
                    $("#SNTestItemSave_Btn").removeAttr("disabled");
                    $('#SNTestItem_SearchOpen').click();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_FlOWOVER') {
                    layer.msg('该工序已完工，不能添加！');
                    $("#SNTestItemSave_Btn").removeAttr("disabled");
                    $('#SNTestItem_SearchOpen').click();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                    layer.msg('测试项已测试，不可操作！');
                    $("#SNTestItemSave_Btn").removeAttr("disabled");
                    $('#SNTestItem_SearchOpen').click();
                }
                else {
                    $("#SNTestItemSave_Btn").removeAttr("disabled");
                    $("#div_warningTestItem").html(parsedJson.Msg);
                    $("#div_warningTestItem").show();
                }
            },
            error: function (data) {
                $("#SNTestItemSave_Btn").removeAttr("disabled");
                $("#div_warningTestItem").html("系统出错，请重试2！")
                $("#div_warningTestItem").show();
            }
        });
    });




    //  工单界面，发放序列号
    $('#SendSerial_Btn').click(function () {
        $("#SendSerial_Btn").attr({ "disabled": "disabled" });
        $("#div_warning2").hide();

        var sOrderNo = $("#txtSOrderNo").val();  //工单号
        var sMNo = $("#txtOMaterNo").val();  //产品编码
        var sNum = $("#txtONum").val();  //发号数量
        var sFlag = "3";


        if (sOrderNo == "") {
            $("#div_warning2").html("获取不到工单号！");
            $("#div_warning2").show();
            $("#SendSerial_Btn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { No: "", Name: "", Item: sOrderNo, MNo: sMNo, MName: "", A: sNum, B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPSerialInfo&CFlag=3",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#SendSerial_Btn").removeAttr("disabled");

                    $("#Search_SerialBtn").click();

                    layer.msg(parsedJson.RMsg);
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#SendSerial_Btn").removeAttr("disabled");
                    $("#div_warning2").html("该工单已发放序列号！")
                    $("#div_warning2").show();
                }
                else {
                    $("#SendSerial_Btn").removeAttr("disabled");
                    $("#div_warning2").html("系统出错，请重试1！")
                    $("#div_warning2").show();
                }
            },
            error: function (data) {
                $("#SendSerial_Btn").removeAttr("disabled");
                $("#div_warning2").html("系统出错，请重试2！")
                $("#div_warning2").show();
            }
        });
    });



    //  工单界面，序列号继承工单信息：设备，测试项目，工艺文件/ 已在生产中的序列号，不会再刷新
    $('#Send_WOInfoToSN').click(function () {
        $("#Send_WOInfoToSN").attr({ "disabled": "disabled" });
        $("#div_warning2").hide();


        var bln = window.confirm("确定重新获取序列号生产信息吗?");  // 提示信息
        if (!bln) { // 不往下执行
            $("#Send_WOInfoToSN").removeAttr("disabled");
            return;
        }

        var sWONo = $("#txtSOrderNo").val();  //工单号
        var sSN = $("#txtSSerialNo").val();  //序列号
        var sOrderKind = $("#txtOKind").val()//工单类型
        var sFlag = "31";


        if (sWONo == "") {
            $("#div_warning2").html("获取不到工单号！");
            $("#div_warning2").show();
            $("#Send_WOInfoToSN").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { No: sSN, Name: "", Item: sWONo, MNo: "", MName: "", A: "", B: "", C: "", D: "", E: sOrderKind, F: "", H: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);


        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPOrderInfo&CFlag=31",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#Send_WOInfoToSN").removeAttr("disabled");

                    $("#Search_SerialBtn").click();

                    layer.msg("同步成功"); // LB_Serial
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST8888') {
                    $("#Send_WOInfoToSN").removeAttr("disabled");
                    $("#div_warning2").html("该工单已发放序列号！")
                    $("#div_warning2").show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_NotWOAction') {
                    $("#Send_WOInfoToSN").removeAttr("disabled");
                    $("#div_warning2").html("重工单的第一道工序必需要设置重工相关的工序行为，请确认！");
                    $("#div_warning2").show();
                    $("#AddDoing").hide();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_NotWOMater') {
                    $("#Send_WOInfoToSN").removeAttr("disabled");
                    $("#div_warning2").html("该工单BOM没有设置重工的对象，不能下达！");
                    $("#div_warning2").show();
                    $("#AddDoing").hide();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_NotWOProc') {
                    $("#Send_WOInfoToSN").removeAttr("disabled");
                    $("#div_warning2").html("重工对象必需设置在第一道工序上！" + sWONo + " 工单第一道工序是 " + parsedJson.RMsg + " ，请确认！");
                    $("#div_warning2").show();
                    $("#AddDoing").hide();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_NotWOFlow') {
                    $("#Send_WOInfoToSN").removeAttr("disabled");
                    $("#div_warning2").html("该工单没有流程，不能继承工单信息！");
                    $("#div_warning2").show();
                    $("#AddDoing").hide();
                } else {
                    $("#Send_WOInfoToSN").removeAttr("disabled");
                    $("#div_warning2").html("系统出错，请重试1！")
                    $("#div_warning2").show();
                }
            },
            error: function (data) {
                $("#Send_WOInfoToSN").removeAttr("disabled");
                $("#div_warning2").html("系统出错，请重试2！")
                $("#div_warning2").show();
            }
        });
    });



    //  作业执行，对不合格单据进行提交
    //$('#EndBHG_Btn').click(function () {
    //    $("#EndBHG_Btn").attr({ "disabled": "disabled" });
    //    $("#LMgs").html("");

    //    var sEXEMan = $('#txtInMan').val();  //作业执行人员账号
    //    var sEXENo = $("#txtEXENo").val(); // 作业编号
    //    var UpBatch = $("#txtBZBatch").val(); // 上层批次：包装上层批次--包装送检批次
    //    var sSerial = $('#txtNowSerialNo').val();  // 序列号
    //    var sProcNo = $('#txtNowProcNo').val();  // 工序编码
    //    var sVer = $('#txtNowProcVer').val();  // 工序版本
    //    var sWO = $('#txtOrderNo').val();  // 工单号
    //    var sFlowOrder = $('#txtFlowOrder').val();  // 工序顺序编号
    //    var sFlag = "29-1-1";


    //    if (sEXENo == "") {
    //        $("#LMgs").html("获取作业执行编号，请退出重新登录。");
    //        $("#EndBHG_Btn").removeAttr("disabled");
    //        return;
    //    }
    //    if (sSerial == "") {
    //        $("#LMgs").html("获取不到作业的序列号，请确认")
    //        $("#EndBHG_Btn").removeAttr("disabled");
    //        return;
    //    }
    //    var Data = '';
    //    var Params = { No: "", Name: "", Item: "", MNo: "", MName: "", A: sEXENo, B: sSerial, C: sProcNo, D: UpBatch, E: sVer, F: "", InMan: sEXEMan, Remark: "", Flag: sFlag };
    //    var Data = JSON.stringify(Params);

    //    //11单个工序生成，12单个工序生成12
    //    var sN = 11;
    //    var val = { No: sSerial, Name: "", Item: "", MNo: "", MName: "", A: UpBatch, B: sProcNo, C: "", D: "", E: sFlowOrder, F: sEXENo, G: sWO, H: "", I: "", J: "", InMan: sEXEMan, Remark: "", Flag: sFlag };


    //    $.ajax({
    //        type: "POST",
    //        url: "../Service/OrderAjax.ashx?OP=OPPRDYCCodeInfo&CFlag=29-1-1",
    //        data: { Data: Data },
    //        success: function (data) {
    //            var parsedJson = jQuery.parseJSON(data);

    //            if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

    //                $("#EndBHG_Btn").removeAttr("disabled");
    //                $('#txtOPRec').val(" 不良流程提交成功，请转到维修界面处理！" + "\n" + $('#txtOPRec').val());
    //                $("#LMgs").html("");
    //                GetResetInputText(1); // 初始化界面，初始化输入框
    //                //$('#dgDataListYC').bootstrapTable('refresh', { url: '../Service/OrderAjax.ashx?OP=GetPRDInfoTwo&CFlag=29-1&CNO=' + sEXENo });
    //                CreateDHR(sN, val)

    //            } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
    //                $("#EndBHG_Btn").removeAttr("disabled");
    //                $("#LMgs").html("您未登陆系统，请先登录！")
    //                //location.href = "Login.htm";
    //            } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_NOTEXIST') {
    //                $("#EndBHG_Btn").removeAttr("disabled");
    //                $("#LMgs").html("该作业没有不合格处理单！")
    //            }
    //            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
    //                $("#EndBHG_Btn").removeAttr("disabled");
    //                $("#LMgs").html("已提交，无需重复提交！")
    //            }
    //            else {
    //                $("#EndBHG_Btn").removeAttr("disabled");
    //                $("#LMgs").html("系统出错，请重试1！")
    //            }
    //        },
    //        error: function (data) {
    //            $("#EndBHG_Btn").removeAttr("disabled");
    //            $("#LMgs").html("系统出错，请重试2！")
    //        }
    //    });
    //});



    // 不合格处理： 提交:对不良现象进行新增
    $('#ECauseSaveBtn').click(function () {
        $("#ECauseSaveBtn").attr({ "disabled": "disabled" });
        $("#L_Mgs").html("");

        var sNo = $("#txtPECode").val();  // 不合格单号 
        var sWO = $("#txtOrderNo").val();  // 工单
        var sSN = $("#txtSerialNo").val();  // 序列号
        var sTNo = $("#txtGX").val();  // 工序
        var sFDate = $("#txtFDate").val(); // 发现时间
        var sFMan = $("#txtFMan").val(); //发现人
        var stxtOrderKind = $("#txtOrderKind").val()

        var sFlag = "29-2";


        if (sSN == "") {
            $("#L_Mgs").html("请选择序列号");
            $("#ECauseSaveBtn").removeAttr("disabled");
            return;
        }
        if (sTNo == "") {
            $("#L_Mgs").html("请选择工序")
            $("#ECauseSaveBtn").removeAttr("disabled");
            return;
        }
        var sGXNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
        var sGXName = sTNo.substr(sTNo.indexOf(")") + 1, sTNo.length);

        if (sFDate == "") {
            $("#L_Mgs").html("请选择发现时间");
            $("#ECauseSaveBtn").removeAttr("disabled");
            return;
        }
        if (sFMan == "") {
            $("#L_Mgs").html("请输入发现人");
            $("#ECauseSaveBtn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { No: sNo, Name: "", Item: sSN, MNo: "", MName: "", A: sGXNo, B: sGXName, C: sFDate, D: sFMan, E: sWO, F: "", G: stxtOrderKind, Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPECodeInfo&CFlag=29-2",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#L_Mgs").html("单据创建成功，请添加不良现象");
                    $("#txtPECode").val(parsedJson.RNo);
                    $('#PrdECode_open').click();

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#ECauseSaveBtn").removeAttr("disabled");
                    $("#L_Mgs").html("您未登陆系统，请先登录！")
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTSOVER') {
                    $("#ECauseSaveBtn").removeAttr("disabled");
                    $("#L_Mgs").html("该序列号生产已完成，无需再生产！")
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXSIT') {
                    $("#ECauseSaveBtn").removeAttr("disabled");
                    $("#L_Mgs").html("该序列号还有未完成的不合格单，请先完成！")
                }
                else {
                    $("#ECauseSaveBtn").removeAttr("disabled");
                    $("#L_Mgs").html("系统出错，请重试1！")
                }
            },
            error: function (data) {
                $("#ECauseSaveBtn").removeAttr("disabled");
                $("#L_Mgs").html("系统出错，请重试2！")
            }
        });
    });



    //  作业执行，更换作业人员
    $('#SaveCHMan_Btn').click(function () {
        $("#SaveCHMan_Btn").attr({ "disabled": "disabled" });
        $("#div_warningCHMan").html("");


        var sEXENo = $("#txtEXENo").val(); // 作业编号 
        var sSerial = $('#txtNowSerialNo').val();  // 序列号
        var sOldMan = $('#txtOldManNo').val();  // 原作业人员
        var sNewManNo = $('#txtNewManNo').val();  // 新作业人员
        var sNewMan = $('#txtNewMan').val();  // 新作业人员
        var sUnit = $("#txtEWorkCenter").val(); // 作业单元
        var sEXEMan = $('#txtInMan').val();  //作业执行人员账号
        var sFlag = "29-11";


        if (sEXEMan == sNewManNo) {
            ErrorMessage("新旧作业人员一样，无需更换", 2000)
            $("#SaveCHMan_Btn").removeAttr("disabled");
            return;
        }

        if (sNewManNo == "") {
            ErrorMessage("请输入新的作业人员！", 2000)
            $("#SaveCHMan_Btn").removeAttr("disabled");
            return;
        }
        if (sNewMan == "") {
            ErrorMessage("获取不到新作业人员姓名，在新作业人员输入框按回车！", 2000)
            $("#SaveCHMan_Btn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { No: sEXENo, Name: "", Item: sSerial, MNo: "", MName: "", A: sOldMan, B: sNewManNo, C: sNewMan, D: sEXEMan, E: sUnit, F: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPPRDChangeManInfo&CFlag=29-11",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#SaveCHMan_Btn").removeAttr("disabled");
                    $("#div_warningCHMan").html("");

                    $('#txtEUserNo').val(sNewManNo + "(" + sNewMan + ")");
                    $('#txtNewManNo').val("");  // 新作业人员 
                    $('#txtNewMan').val("");  // 新作业人员
                    $('#txtInMan').val(sNewManNo);  //当前作业执行人员账号

                    $('#Div_CHMan').css("display", "none")
                    $('#fade').css("display", "none")

                    $("#LMgs").html("更换成功！")

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#SaveCHMan_Btn").removeAttr("disabled");
                    ErrorMessage("更换的人员不存在！", 2000)
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_USEREXIST') {
                    $("#SaveCHMan_Btn").removeAttr("disabled");
                    ErrorMessage("更换的人员无该作业单元操作权限！", 2000)
                }
                else {
                    $("#SaveCHMan_Btn").removeAttr("disabled");
                    ErrorMessage("系统出错，请重试1！", 2000)
                }
            },
            error: function (data) {
                $("#SaveCHMan_Btn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });


    //  作业执行，更换作业单元
    $('#SaveCHUnit_Btn').click(function () {
        $("#SaveCHUnit_Btn").attr({ "disabled": "disabled" });
        $("#div_warningCHUnit").html("");

        var sEXENo = $("#txtEXENo").val(); // 作业编号
        var sOldUnit = $('#txtOldUnitNo').val();  // 原作业单元
        var sNewUnitNo = $('#txtNewUnitNo').val();  // 新作业单元
        var sNewUnit = $('#txtNewUnit').val();  // 新作业单元
        var sEXEMan = $('#txtInMan').val();  //作业执行人员账号
        var sFlag = "29-12";

        if (sNewUnitNo == "") {
            ErrorMessage("请输入新的作业单元！", 2000)
            $("#SaveCHUnit_Btn").removeAttr("disabled");
            return;
        }
        if (sNewUnit == "") {
            ErrorMessage("新作业单元不存在！", 2000)
            $("#SaveCHUnit_Btn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { No: sEXENo, Name: "", Item: "", MNo: "", MName: "", A: sOldUnit, B: sNewUnitNo, C: sNewUnit, D: sEXEMan, E: "", F: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);


        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPPRDChangeUnitInfo&CFlag=29-12",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#SaveCHUnit_Btn").removeAttr("disabled");
                    $("#div_warningCHUnit").html("");

                    $("#txtNewUnitNo").val("");
                    $("#txtNewUnit").val("");
                    $("#txtEWorkCenter").val(sNewUnitNo);
                    $("#LEWCName").html(sNewUnit);

                    $("#LMgs").html("更换成功！")

                    $('#Div_CHUnit').css("display", "none")
                    $('#fade').css("display", "none")


                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTSOVER') {
                    $("#SaveCHUnit_Btn").removeAttr("disabled");
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTSOVER') {
                    $("#SaveCHUnit_Btn").removeAttr("disabled");
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_UNITUSE') {
                    ErrorMessage("该作业单元已被其他作业人员占用，不可更换！", 2000)
                    $("#SaveCHUnit_Btn").removeAttr("disabled");
                }
                else {
                    ErrorMessage("系统出错，请重试1！", 2000)
                    $("#SaveCHUnit_Btn").removeAttr("disabled");
                }
            },
            error: function (data) {
                ErrorMessage("系统出错，请重试2！", 2000)
                $("#SaveCHUnit_Btn").removeAttr("disabled");
            }
        });
    });



    // 维修： 扫描不良原因后点击 提交 ；这个按钮两个地方有：维修，作业执行信息变更
    $('#AddBLYYSave_Btn').click(function () {
        $("#AddBLYYSave_Btn").attr({ "disabled": "disabled" });
        $('#div_warningAddBLYY').html("");
        $('#div_warningAddBLYY').hide();

        var sPECode = $("#txtPECode").val();  // 不良流程编号
        var sCNo = $('#txtAYYCode').val();
        var sCKind = $('#txtCKind').val();  // 不良原因名称
        var sCDesc = $('#txtCDesc').val();
        var sLocation = $('#txtLocation').val();
        var sCType = $('#txtCType').val();
        var sECode = $('#txtAECode').val();  // 不良现象编码
        var sEName = $('#txtAEName').val();  // 不良现象名称
        var sUnitNo = $('#txtWXUnit').val()
        var sFlag = $('#txtAEFlag').val(); // 30-2-1 新增，   30-2-2  修改


        if (sPECode == "") {
            $('#div_warningAddBLYY').html("获取不到不合格单据");
            $('#div_warningAddBLYY').show();
            $("#AddBLYYSave_Btn").removeAttr("disabled");
            return;
        }
        if (sCNo == "") {
            $('#div_warningAddBLYY').html("请扫描不良原因编号");
            $('#div_warningAddBLYY').show();
            $("#AddBLYYSave_Btn").removeAttr("disabled");
            return;
        }
        if (sCKind == "") {
            $('#div_warningAddBLYY').html("请扫描正确的不良原因编号");
            $('#div_warningAddBLYY').show();
            $("#AddBLYYSave_Btn").removeAttr("disabled");
            return;
        }



        var Data = '';
        var Params = { No: sPECode, Name: sUnitNo, Item: sCNo, MNo: "", MName: "", A: sCKind, B: sCDesc, C: sLocation, D: sCType, E: sECode, F: sEName, Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPWXInfo&CFlag=30-2",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#AddBLYYSave_Btn").removeAttr("disabled");
                    $("#div_warningAddBLYY").html("不良原因扫描成功，请继续扫描");
                    $('#div_warningAddBLYY').show();
                    $('#txtAYYCode').val("");
                    $('#txtCKind').val("");
                    $('#txtCDesc').val("");
                    $('#txtLocation').val("");
                    $('#txtCType').val("");

                    ShowBLYYInfo(sPECode, sECode);

                    if (sFlag == "30-2-2") {
                        document.getElementById('Div_AddBLYY').style.display = 'none';
                    }
                    $('#txtAYYCode').focus();

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#AddBLYYSave_Btn").removeAttr("disabled");
                    $("#div_warningAddBLYY").html("您未登陆系统，请先登录！");
                    $('#div_warningAddBLYY').show();
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTYY') {
                    $("#AddBLYYSave_Btn").removeAttr("disabled");
                    $("#div_warningAddBLYY").html("该不良原因编码已扫描，无需再扫描");
                    $('#div_warningAddBLYY').show();
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_ISOVER') {
                    $("#AddBLYYSave_Btn").removeAttr("disabled");
                    $("#div_warningAddBLYY").html("该不合格处理单已完成，不能再扫描");
                    $('#div_warningAddBLYY').show();
                }
                else {
                    $("#AddBLYYSave_Btn").removeAttr("disabled");
                    $("#div_warningAddBLYY").html("系统出错，请重试1！");
                    $('#div_warningAddBLYY').show();
                }
            },
            error: function (data) {
                $("#AddBLYYSave_Btn").removeAttr("disabled");
                $("#div_warningAddBLYY").html("系统出错，请重试2！");
                $('#div_warningAddBLYY').show();
            }
        });
    });


    // 维修： 维修完成后，提交数据
    $('#WXSave_Btn').click(function () {
        $("#WXSave_Btn").attr({ "disabled": "disabled" });
        $('#L_SaveMgs').html("");

        var sWO = $("#txtOrderNo").val();  // 工单
        var sUnit = $("#txtWXUnit").val();  // 作业单元
        var sPECode = $("#txtPECode").val();  // 不良流程编号 
        var sEXENo = $('#txtEXENo').val();
        var sSN = $('#txtSerialNo').val();
        var sDType = $('#txtDealType').val();  // 处理方式
        var sYUNNo = $('#txtYUNNo').val();  // 云之家编号
        var sMType = $('#txtMaintainType').val(); //维修方式
        var sMDesc = $('#txtMaintainDesc').val(); // 维修内容
        var sTempGX = $('#txtGX').val();  // 当前工序-发生异常的工序
        var sModel = $("#txtModel").val().trim(); // 产品型号
        var sYCGX = sTempGX.substr(1, sTempGX.indexOf(")") - 1);  // 当前工序-发生异常的工序
        var sTNo = $('#txtBackGX option:selected').text().trim(); //选中的文本 获取下拉值   // var sTNo = $('#txtBackGX').val();  // 返回工序 
        var sFlag = "30-2-6";

        if (sDType == "维修拆解" ) {
            sFlag = "30-2-8";
        }

        if (sUnit == "") {
            $('#L_SaveMgs').html("获取不到维修作业单元");
            $("#WXSave_Btn").removeAttr("disabled");
            return;
        }
        if (sPECode == "") {
            $('#L_SaveMgs').html("获取不到不合格单据");
            $("#WXSave_Btn").removeAttr("disabled");
            return;
        }
        if (sDType == "") {
            $('#L_SaveMgs').html("请选择处理方式");
            $("#WXSave_Btn").removeAttr("disabled");
            return;
        }
        if (sMType == "") {
            $('#L_SaveMgs').html("请选择维修方式");
            $("#WXSave_Btn").removeAttr("disabled");
            return;
        }

        // 返回工序
        var sSeqNo = sTNo.substring(0, sTNo.indexOf(" "));  //工序：  1  (05)总装 截取字符串，字符位置   1：是顺序编号;这里得到  1
        var sGXNo = sTNo.substring(sTNo.indexOf('(') + 1, sTNo.indexOf(')'));  //工序：  1  (05)总装 截取字符串，字符位置 ；这里得到 05 
        var sGXName = sTNo.substring(sTNo.indexOf(")") + 1, sTNo.length);

        sSeqNo = sSeqNo.trim();
        sGXNo = sGXNo.trim();
        sGXName = sGXName.trim();

        if (sGXNo == "" || sGXName == "") {
            $("#L_SaveMgs").html("请选择返回工序")
            $("#WXSave_Btn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { No: sPECode, Name: "", Item: sYCGX, MNo: sSN, MName: sUnit, A: sEXENo, B: sDType, C: sMType, D: sMDesc, E: sGXNo, F: sGXName, G: sWO, H: sSeqNo, I: sYUNNo, J: sModel, Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPWXInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#WXSave_Btn").removeAttr("disabled");
                    $("#L_SaveMgs").html("维修完成");

                    ShowBLXXInfo("8888888");// 不良现象 只是打开，不显示数据
                    ShowBLYYInfo("8888888", "8888888");  // 不良原因 只是打开，不显示数据
                    ShowMaterInfo("8888888", "8888888"); // 已安装的物料 只是打开，不显示数据

                    $("#txtPECode").val("");
                    $("#txtGX").val("");
                    $("#txtOrderKind").val("");
                    $("#txtSerialNo").val("");
                    $("#txtOrderNo").val("");
                    $("#txtFNo").val("");
                    $("#txtFName").val("");
                    $("#txtModel").val("");
                    $("#txtFDate").val("");
                    $("#txtStatus").val("");
                    $("#txtFMan").val("");
                    $("#txtDeptName").val("");
                    $("#txtYUNNo").val("");
                    $("#txtMaintainDesc").val("");
                    $("#txtSNCode").val("");
                    $("#txtBackGX").val("");
                    $("#txtRepMaterNo").val("")
                    $("#txtRepMaterName").val("")
                    $("#txtSNCode").focus();
                    export_pdf_custom(Params)

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#WXSave_Btn").removeAttr("disabled");
                    $("#L_SaveMgs").html("您未登陆系统，请先登录！");
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_ISOVER') {
                    $("#WXSave_Btn").removeAttr("disabled");
                    $("#L_SaveMgs").html("该不合格处理单已完成，不能再扫描");
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_BLYY') {
                    $("#WXSave_Btn").removeAttr("disabled");
                    $("#L_SaveMgs").html(parsedJson.RNo + "现象未填写不良原因，不能提交，请确认！");
                }
                else {
                    $("#WXSave_Btn").removeAttr("disabled");
                    $("#L_SaveMgs").html("系统出错，请重试1！");
                }
            },
            error: function (data) {
                $("#WXSave_Btn").removeAttr("disabled");
                $("#L_SaveMgs").html("系统出错，请重试2！");
            }
        });
    });



    //  入库申请：输入框内容变化时
    $('#txtInStockWay').change(function () {
        var sWay = $('#txtInStockWay').val();
        ResetInputText(1);  // 初始化输入框
        $('#txtInStockWay').val(sWay);
    });

    // 入库申请： 提交入库申请
    $('#InStockOver_Btn').click(function () {
        $("#InStockOver_Btn").attr({ "disabled": "disabled" });
        $('#div_warningInStock').html("");

        var sInNo = $("#txtOInStockNo").val();  // 入库单号
        var sMBatch = $("#txtMaterBatchNo").val();  // 扫描的检验批次或工单
        var sFlag = "32-6";


        if (sInNo == "") {
            $('#L_SaveMgs').html("获取不到入库单号");
            $("#InStockOver_Btn").removeAttr("disabled");
            return;
        }


        var Data = '';
        var Params = { No: sMBatch, Name: "", Item: "", MNo: "", MName: "", A: sInNo, B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPInStockInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#InStockOver_Btn").removeAttr("disabled");

                    $('#txtOPRec').val(sInNo + " 入库成功！准备入库K3系统.." + "\n" + $('#txtOPRec').val());

                    document.getElementById('Div_InStockOver').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                    // 出发入库K3的方法。这个地方后续可以修改为：写在入库的方法做
                    $('#ToK3_Btn').click();

                    $("#div_warningInStock").html("");
                    $("#txtOInStockNo").val("");
                    ResetInputText(1);  // 初始化输入框
                    ShowSNInfo("88888", "2");
                    ShowInStockInfo("88888");

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#InStockOver_Btn").removeAttr("disabled");
                    $("#div_warningInStock").html("您未登陆系统，请先登录！");
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_ISInStock') {
                    $("#InStockOver_Btn").removeAttr("disabled");
                    $("#div_warningInStock").html("该申请单已入库，无需再入库");
                }
                else {
                    $("#InStockOver_Btn").removeAttr("disabled");
                    $("#div_warningInStock").html("系统出错，请重试1！");
                }
            },
            error: function (data) {
                $("#InStockOver_Btn").removeAttr("disabled");
                $("#div_warningInStock").html("系统出错，请重试2！");
            }
        });
    });



    // 入库申请，点击推送K3的接口  -- 在入库完成按钮也会调用这个按钮执行，入库按钮：InStockOver_Btn
    $('#ToK3_Btn').click(function () {
        $("#ToK3_Btn").attr({ "disabled": "disabled" });
        var txtSNoAndWNO = $('#txtInStockNo').val();  // 入库单号
        var txtSCLX = $("#txtSCLX").val()//生产类型
        var txtRKLX = $('#txtInStockKind').val();//入库类型
        var txtCWNo = $('#txtWNo').val();//仓位
        var txtSCRQ = $("#txtSCRQ").val()//生产日期
        var txtGQSJ = $("#txtGQSJ").val()//过期日期
        var txtRKFS = $("#txtInStockWay").val()//入库方式
        var txtIsBackWms = $('#txtIsBackWms').is(':checked');//是否WMS已回传
        var txtIsAccomplish = $("#txtIsAccomplish").is(':checked')//是否完工
        var txtIsReverseMaterial = $("#txtIsReverseMaterial").is(':checked')//是否倒冲领料
        var txtIsAutoSubmitAndAudit = $("#IsAutoSubmitAndAudit").val()

        if (txtSCLX == "" || txtSCLX == null) {
            $("#LMgs").html("请选择生产类型")
            $("#ToK3_Btn").removeAttr("disabled");
            return
        }
        if (txtRKLX == "" || txtRKLX == null) {
            $("#LMgs").html("请选择入库类型")
            $("#ToK3_Btn").removeAttr("disabled");
            return
        }

        //if (txtSCRQ == "" || txtSCRQ == null) {
        //    $("#LMgs").html("请选择生产时间")
        //    $("#ToK3_Btn").removeAttr("disabled");
        //    return
        //}
        //if (txtGQSJ == "" || txtGQSJ == null) {
        //    $("#LMgs").html("请选择过期时间")
        //    $("#ToK3_Btn").removeAttr("disabled");
        //    return
        //}

        //if (txtGQSJ < txtSCRQ) {
        //    $("#LMgs").html("过期时间必须要大于生产时间")
        //    $("#ToK3_Btn").removeAttr("disabled");
        //    return
        //}

        var sFlag = '32-7'

        var Data = '';
        var Params = { No: txtSNoAndWNO, ProductType: txtSCLX, StorageType: txtRKLX, StoreHouseNo: txtCWNo, ProduceDate: txtSCRQ, ExpiryDate: txtGQSJ, IsAccomplish: txtIsAccomplish, IsBackWms: txtIsBackWms, IsReverseMaterial: txtIsReverseMaterial, Flag: sFlag, RKFS: txtRKFS, IsAutoSubmitAndAudit: txtIsAutoSubmitAndAudit };
        var Data = JSON.stringify(Params);

        //var Data = '';
        //var Params = { No: sCode, Name: "", Item: "", MNo: "", MName: "", A: "", B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: sFlag };
        //var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=ToK3StockInfo&CFlag=32-7",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                var res = jQuery.parseJSON(parsedJson.Msg)

                console.log(res)
                if (res.Result.ResponseStatus.IsSuccess) {
                    $("#LMgs").html("入库成功！")
                    $("#ToK3_Btn").removeAttr("disabled");
                } else {
                    //入库失败的原因直接把K3报错信息返回
                    $("#LMgs").html("入库失败！原因：" + res.Result.ResponseStatus.Errors[0].Message)
                    $("#ToK3_Btn").removeAttr("disabled");
                }

                //if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                //    $("#LMgs").html("入库成功")
                //    $("#ToK3_Btn").removeAttr("disabled");
                //    $('#txtDJRWNo').val(parsedJson.RNo);  //点检任务编号


                //} else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                //    $("#div_warningDJ").html("您未登陆系统，请先登录！")
                //    $("#div_warningDJ").show();
                //    $("#ToK3_Btn").removeAttr("disabled");
                //    //location.href = "Login.htm";
                //} else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Check') {
                //    $("#div_warningDJ").html("该设备已有点检任务，无需刷新！")
                //    $("#div_warningDJ").show();
                //    $("#ToK3_Btn").removeAttr("disabled");
                //}
                //else {
                //    $("#div_warningDJ").html("系统出错1！")
                //    $("#div_warningDJ").show();
                //    $("#ToK3_Btn").removeAttr("disabled");
                //}
            },
            error: function (data) {
                $("#div_warningDJ").html("该系统出错2！")
                $("#div_warningDJ").show();
                $("#ToK3_Btn").removeAttr("disabled");
            }
        });


    });


    // 生产工单界面：建立重工单和原工单对应关系
    $('#SaveROrder_Btn').click(function () {
        $("#SaveROrder_Btn").attr({ "disabled": "disabled" });
        $("#LB_ROrder").html("");
        var sRWO = $('#txtSOrderNo').val();  // 重工单
        var sRMNo = $('#txtOMaterNo').val();  // 重工单产品编码
        var sRMName = $('#txtOMaterName').val();  // 重工单产品描述
        var sSpec = $('#txtOSpec').val();  // 重工单型号
        var sRNum = $('#txtONum').val();  // 重工单数量
        var sOWO = $('#txtROOrderNo').val();  // 原工单
        var sNum = $('#txtRONum').val();  // 原工单绑定数量
        var sFlag = "34-1";

        if (txtROOrderNo == "") {
            $('#LB_ROrder').html("原工单号不能为空");
            $("#SaveROrder_Btn").removeAttr("disabled");
            return;
        }
        if (sNum == "") {
            $('#LB_ROrder').html("绑定数量不能为空");
            $("#SaveROrder_Btn").removeAttr("disabled");
            return;
        }
        if (isNaN(sNum)) {  // $('#txtUValue').val("");// 介于的测试值-上限  600
            $("#LB_ROrder").html("数量输入框请填写数值！");
            $("#SaveROrder_Btn").removeAttr("disabled");
            return;
        }
        if (parseFloat(sNum) > parseFloat(sRNum)) {
            $("#LB_ROrder").html("绑定数量超过重工单数量");
            $("#SaveROrder_Btn").removeAttr("disabled");
            return;
        }




        var Data = '';
        var Params = { No: sRWO, Name: "", Item: "", MNo: sRMNo, MName: sRMName, A: sSpec, B: sRNum, C: sOWO, D: sNum, E: "", F: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPReworkOrder&CFlag=34-1",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $('#txtROOrderNo').val("");
                    $('#txtRONum').val("");
                    $("#txtROOrderNo").attr({ "disabled": "disabled" });
                    $("#txtRONum").attr({ "disabled": "disabled" });
                    $("#SaveROrder_Btn").removeAttr("disabled");
                    $("#LB_ROrder").html("");
                    ShowReworkOrderRel(sRWO);

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#LB_ROrder").html("您未登陆系统，请先登录！");
                    $("#SaveROrder_Btn").removeAttr("disabled");
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTSN') {
                    $("#LB_ROrder").html("该重工单已有序列号，无需绑定原工单");
                    $("#SaveROrder_Btn").removeAttr("disabled");
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_EXISTWO') {
                    $("#LB_ROrder").html("原工单不存在");
                    $("#SaveROrder_Btn").removeAttr("disabled");
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTNUM') {
                    $("#LB_ROrder").html("绑定数量大于原工单数量");
                    $("#SaveROrder_Btn").removeAttr("disabled");
                }
                else {
                    $("#LB_ROrder").html("系统出错1！")
                    $("#SaveROrder_Btn").removeAttr("disabled");
                }
            },
            error: function (data) {
                $("#LB_ROrder").html("该系统出错2！");
                $("#SaveROrder_Btn").removeAttr("disabled");
            }
        });


    });


    // 生产工单界面：拆解物料
    $('#Unload_Btn').click(function () {
        $("#Unload_Btn").attr({ "disabled": "disabled" });
        var sEXENo = $("#txtEXENo").val(); // 作业编号
        var sRWO = $('#txtSOrderNo').val();  // 重工单
        var sSerial = $('#txtNowSerialNo').val();  // 序列号
        var sOWO = $('#txtROOrderNo').val();  // 原工单
        var sProcNo = $('#txtNowProcNo').val();  // 工序编码 
        var sProcName = $('#txtNowProc').val();  // 工序名称
        var sFlag = "34-4";

        if (sEXENo == "") {
            $('#LMgs').html("获取不到作业执行编号");
            $("#Unload_Btn").removeAttr("disabled");
            return;
        }
        if (sRWO == "") {
            $('#LMgs').html("获取不到重工单号");
            $("#Unload_Btn").removeAttr("disabled");
            return;
        }
        if (sSerial == "") {
            $('#LMgs').html("获取不到需要拆解的序列号");
            $("#Unload_Btn").removeAttr("disabled");
            return;
        }


        var bln = window.confirm("您确定要拆解物料么?");  // 提示信息
        if (!bln) { // 不往下执行
            $('#Unload_Btn').removeAttr("disabled");
            return;
        }



        var Data = '';
        var Params = { No: sRWO, Name: "", Item: sSerial, MNo: "", MName: "", A: sEXENo, B: sProcNo, C: sOWO, D: "", E: "", F: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPReworkOrder&CFlag=34-4",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {


                    $("#Unload_Btn").removeAttr("disabled");
                    $('#txtOPRec').val(sSerial + "  " + sProcNo + " " + sProcName + " 拆解完成！" + "\n" + $('#txtOPRec').val());

                    GetResetInputText(1); // 初始化界面，初始化输入框


                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#LMgs").html("您未登陆系统，请先登录！");
                    $("#Unload_Btn").removeAttr("disabled");
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTSN') {
                    $("#LMgs").html("该重工单已有序列号，无需绑定原工单");
                    $("#Unload_Btn").removeAttr("disabled");
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_EXISTWO') {
                    $("#LMgs").html("原工单不存在");
                    $("#Unload_Btn").removeAttr("disabled");
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTNUM') {
                    $("#LMgs").html("绑定数量大于原工单数量");
                    $("#Unload_Btn").removeAttr("disabled");
                }
                else {
                    $("#LMgs").html("系统出错1！")
                    $("#SaveROrder_Btn").removeAttr("disabled");
                }
            },
            error: function (data) {
                $("#LMgs").html("该系统出错2！");
                $("#Unload_Btn").removeAttr("disabled");
            }
        });


    });

















    // 这里增加其他按钮，空间事件方法











});







// 用这个方法，主要是保证勾选不需要人勾，不设置disabled=disabled  ，这样用户看见的勾选比较清晰点。  2019  
function CheckBox_OnClick(ob) {
    var id = $(ob).attr("id");
    var sCD = id.substr(2, id.length);  // CB0  CB1  CB2


    if ($('#CB' + sCD).is(':checked')) {
        // $('#CB' + sCD).prop("checked", "checked");
        $('#CB' + sCD).removeProp("checked"); //设置为选中状态
    }
    else {
        $('#CB' + sCD).prop("checked", "checked");
    }


}



//  作业执行:扫描作业单元
function GetWorkCent_keydown(event) {

    if (event.keyCode == 13) {
        $("#div_warning").hide();
        $('#txtProcAct').val("");
        $('#LMgs').html("");
        var sEXEMan = $('#txtInMan').val();  //作业执行人员账号

        var Flag = "28-2";

        var sWUnit = $('#txtEWorkCenter').val();
        if (sWUnit == "") {
            $('#LMgs').html("请输入作业单元");
            return;
        }
        if (sWUnit.substr(0, 2) == "WX") {
            $('#LMgs').html("维修工序不在此操作，请到维修界面执行");
            return;
        }

        var Data = '';
        var Params = { CNo: sWUnit, InMan: sEXEMan, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=GetWorkCenter&CFlag=" + Flag + "&CNO=" + encodeURIComponent(sWUnit),
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $('#txtOPRec').val("作业单元扫描成功，请扫描序列号。");
                    $('#LEWCName').html(parsedJson.BZBatch); // 作业单元名称

                    GetResetInputText(1);  // 初始化输入框
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_EXIST') {
                    $('#LMgs').html(sWUnit + "该作业单元不存在，请确认！");
                    $("#txtScanNo").attr({ "disabled": "disabled" });
                    $('#txtEWorkCenter').val("");
                    $('#txtEWorkCenter').focus();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_USEREXIST') {
                    $('#LMgs').html("该用户不能操作该作业单元，请确认！");
                    $("#txtScanNo").attr({ "disabled": "disabled" });
                    $('#txtEWorkCenter').val("");
                    $('#txtEWorkCenter').focus();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_USEEXIST') {
                    $('#LMgs').html(sWUnit + "该作业单元已在生产中，请确认！");
                    $("#txtScanNo").attr({ "disabled": "disabled" });
                    $('#txtEWorkCenter').val("");
                    $('#txtEWorkCenter').focus();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_UNITUSE') {
                    $('#LMgs').html(sWUnit + "该作业单元已被其他作业人员占用，请确认！");
                    $("#txtScanNo").attr({ "disabled": "disabled" });
                    $('#txtEWorkCenter').val("");
                    $('#txtEWorkCenter').focus();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_USER') {
                    $('#LMgs').html(sEXEMan + "该用户不存在，请确认！");
                    $("#txtScanNo").attr({ "disabled": "disabled" });
                    $('#txtEWorkCenter').val("");
                    $('#txtEWorkCenter').focus();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_SIGN') {
                    $('#LMgs').html(sEXEMan + "该用户未上传签名图片，请确认！");
                    $("#txtScanNo").attr({ "disabled": "disabled" });
                    $('#txtEWorkCenter').val("");
                    $('#txtEWorkCenter').focus();
                }
                else {
                    $("#txtWCUserName").val("");
                    $("#LMgs").html("系统错误，请确认该员工是否有该作业单元权限！")
                }
            },
            error: function (data) {
                $("#LMgs").html("系统出错，请联系管理员2！")
            }
        });
    }  // if (event.keyCode == 13)

}


//  扫描作业单元，工序完工，重置一下输入框
function GetResetInputText(n) {

    $('#txtAddKind').val("SN");  //标识接下来执行扫描的是序列号；SN：扫描序列号；ZS：扫描追溯物料；ZX：扫描装箱物料；BZ：扫描序列号包装；JJ扫描序列号或送检批次；YC：扫描异常代码；SB：扫描设备
    $('#L_Scan').html("请扫描需要生产作业的序列号");
    $("#TRZS").show();   // 1 显示追溯物料扫描sheet  
    $("#TRZX").hide();   // 2 装箱物料扫描sheet
    $("#TRCSX").hide();  // 3 测试项
    $("#TRBZ").hide();   // 4 包装上层批次扫描sheet
    $("#TRBZJJ").hide(); // 5 包装检验sheet
    $("#TRYC").hide();   // 6 生产异常，扫描异常代码
    $("#TRSB").hide();   // 7 设备
    $("#TRZK").hide();   // 8 质控品sheet
    $("#TRMB").hide();  // 显示标贴打印模板sheet
    //$("#tb_BZS").hide(); // 显示包装数量
    $("#TR_Over").show(); // 工序完工按钮
    $("#TR_GLOver").hide(); // 包装送检-关联完成
    $("#TR_BOver").hide(); // 批量放行按钮
    $("#TR_CYOver").hide(); // 抽样完工
    $('#LMgs').html("");
    $("#txtScanNo").removeAttr("disabled");
    $('#txtScanNo').show(); // 显示序列号的扫描框
    $('#txtScanNo').focus();
    $('#txtScanInfoNo').hide(); // 隐藏追溯物料，装箱物料的扫描框
    $('#txtYCCode').hide(); // 异常代码输入框
    $('#txtDeviceCode').hide(); // 设备的输入框
    $('#txtQualityCode').hide(); // 设备的输入框
    $('#TRBatch').hide(); // 
    $('#CheckDHRFile').hide(); // 

    $("#txtEXENo").val("");
    $("#txtStatus").val("");
    $("#txtBZBatch").val(""); // 包装批次
    $("#txtOrderNo").val("");
    $("#txtOrderKind").val("");
    $("#txtPMaterNo").val("");
    $("#txtPMaterName").val("");
    $("#txtMaterSpec").val("");
    $("#txtNowSerialNo").val("");
    $("#txtOSerialNo").val("");
    $("#txtPrintSerialNo").val(""); // 打印的序列号 
    $("#txtNowProcNo").val("");
    $("#txtNowProc").val("");
    $("#txtPRDProcVer").empty();
    $('#txtPRDProcVer').val("<option value=''> </option>");  // 工序版本 -- 下拉选择的
    $('#txtNowProcVer').val("");  // 工序版本 
    $("#txtProcVerR").val("");
    $("#txtConditionNo").val(""); //工序行为
    $("#txtPrintLable").val(""); // 是否打印
    $("#txtScanMater").val(""); // 是否扫描物料
    $("#txtUpPack").val(""); // 是否生成上级物料
    $("#txtTestItem").val(""); // 是否需要填写测试项
    $("#txtScanDevice").val(""); // 是否需要扫描设备
    $('#txtPackNum').val("");  // 包装批次数量
    $("#txtBOMVer").val("");  // BOM版本
    $("#txtBOMEN").val("");  // BOM简称
    $("#txtTPName").val(""); // 标贴模板名称
    $("#txtTPPath").val(""); // 标贴模板路径
    $("#txtLabel").val(""); // 标贴编码及版本
    $("#txtCTPName").empty();  // 下拉选择的标贴模板
    $("#txtTSN").val("");     // 从扫描的信息解读出来的序列号
    $("#txtTWO").val("");    // 从扫描的信息解读出来的工单号
    $("#txtShowOver").val("");   // 按空格键，弹窗
    $("#txtBZPNo").val("");   //包装批次
    $("#txtBZSL").val("");   // 包装批次对应数量

    $("#txtSamplPlan").val("");//抽样方案
    $("#txtOrderNum").val("");//检验批数
    $("#txtSampleSize").val("");//样本量
    $("#txtRNum").val("");//接收数
    $("#txtBNum").val("");//退收数
    $("#txtInspectNo").val("");//抽样方案编号
    $("#txtInspectLevel").val("");//检验水平
    $("#txtStringency").val("");//严格度
    $("#txtAql").val("");//质量限

    $("#txtBZPNoW").val("");//上层批次
    $("#txtBZSLW").val("");//上层批次数量
    $("#txtBZStatus").val("");//上层批次状态

    $('#dgDataListZS').bootstrapTable('refresh', { url: '../Service/OrderAjax.ashx?OP=GetPRDInfoTwo&CFlag=28-6&CNO=88888' });   // 刷新用-不需要显示数据
    document.getElementById("Sheet1").setAttribute("style", "background: #0c873d;color:white");
    document.getElementById("Sheet2").setAttribute("style", "background: #f8f9f9; color: black ");
    document.getElementById("Sheet3").setAttribute("style", "background: #f8f9f9; color: black ");
    document.getElementById("Sheet4").setAttribute("style", "background: #f8f9f9; color: black ");
    document.getElementById("Sheet5").setAttribute("style", "background: #f8f9f9; color: black ");
    document.getElementById("Sheet6").setAttribute("style", "background: #f8f9f9; color: black ");
    document.getElementById("Sheet7").setAttribute("style", "background: #f8f9f9; color: black ");
    document.getElementById("Sheet8").setAttribute("style", "background: #f8f9f9; color: black ");
}



//  作业执行:更换作业作业人员
function ChangeMan_keydown(event) {

    if (event.keyCode == 13) {
        $('#LMgs').html("");
        $("#div_warningCHMan").html("");
        var sEXEMan = $('#txtInMan').val();  //作业执行人员账号
        var Flag = "10-99";

        var sMan = $('#txtNewManNo').val();
        if (sMan == "") {
            ErrorMessage("请输入作业人员", 2000)
            return;
        }

        var Data = '';
        var Params = { No: sMan, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetUserInfoByNo&CFlag=" + Flag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $("#txtNewMan").val(sStr.FullName);
                }
                else {
                    $("#txtNewMan").val("");
                    ErrorMessage("请确认用户是否存在或被禁用！", 2000)
                }
            },
            error: function (data) {
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    }  // if (event.keyCode == 13)

}


//  作业执行:更换作业单元
function ChangeWorkUnit_keydown(event) {

    if (event.keyCode == 13) {
        $('#LMgs').html("");
        $("#div_warningCHUnit").html("");
        var sEXEMan = $('#txtInMan').val();  //作业执行人员账号
        var Flag = "28-2";

        var sWUnit = $('#txtNewUnitNo').val();
        if (sWUnit == "") {
            ErrorMessage("请输入作业单元", 2000)
            return;
        }
        if (sWUnit.substr(0, 2) == "WX") {
            ErrorMessage("维修工序不在此操作，请到维修界面执行", 2000)
            return;
        }

        var Data = '';
        var Params = { CNo: sWUnit, InMan: sEXEMan, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=GetWorkCenter&CFlag=" + Flag + "&CNO=" + sWUnit,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];


                    $('#txtNewUnit').val(parsedJson.BZBatch); // 作业单元名称
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_EXIST') {
                    ErrorMessage("该作业单元不存在，请确认！", 2000)
                    $('#txtNewUnitNo').val("");
                    $('#txtNewUnitNo').focus();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_USEREXIST') {
                    ErrorMessage("该用户不能操作该作业单元，请确认！", 2000)
                    $('#txtNewUnitNo').val("");
                    $('#txtNewUnitNo').focus();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_USEEXIST') {
                    ErrorMessage("该作业单元已在生产中，请确认！", 2000)
                    $('#txtNewUnitNo').val("");
                    $('#txtNewUnitNo').focus();
                }
                else {
                    $('#txtNewUnitNo').val("");
                    $('#txtNewUnitNo').focus();
                    ErrorMessage("系统错误，请确认该员工是否有该作业单元权限！", 2000)
                }
            },
            error: function (data) {
                $('#txtNewUnitNo').val("");
                $('#txtNewUnitNo').focus();
                ErrorMessage("系统错误2！", 2000)
            }
        });
    }  // if (event.keyCode == 13)

}

//  作业执行扫描 物料编码，包装用的序列号，原材料批次，序列号等
function GetScanInfoNo_keydown(event) {

    if (event.keyCode == 13) {
        var sScan = $('#txtScanInfoNo').val().trim(); // 扫描的信息
        if (sScan == "EC") {  // 发现不良了
            PRDSheet_onclick(6);
        }
        else {
            GetScanInfo_oninput(sScan + "[CR]");
        }
    }

}


//  作业执行扫描 物料编码，包装用的序列号，原材料批次，序列号等
function GetScanInfo_oninput(sNo) {

    $('#LMgs').html("");
    var sFlag = "28-2";

    var sScan = $('#txtScanInfoNo').val().trim(); // 扫描的信息

    if (sNo != "@_@") {  // 如果输入框是按回车的，则优先使用按回车的内容; @_@ 是固定一个值，标识是输入框内容变化的
        sScan = sNo;
    }

    if (sScan.indexOf("[CR]") > 0) {

        // 删除后面的字符 [CR]
        sScan = sScan.substr(0, sScan.indexOf("[CR]"))// 获取物料：DS50-20-01117[CR]

        var sWO = $("#txtOrderNo").val();  // 工单号
        var sEXEMan = $('#txtInMan').val();  //作业执行人员账号
        var sEXENo = $("#txtEXENo").val();  // 生产记录唯一变化
        var OFlag = $('#txtAddKind').val();  // 当前操作的类别：ZS 扫描追溯物料，ZX 装箱检查
        var sWUnit = $('#txtEWorkCenter').val(); // 工作中心
        var sSNo = $('#txtNowSerialNo').val(); // 当前执行的序列号
        var sProc = $("#txtNowProcNo").val();  // 工序
        var sVer = $('#txtNowProcVer').val();  // 工序版本
        var sProcName = $('#txtNowProc').val();  // 工序名称
        var sPNo = $("#txtPMaterNo").val(); // 产品编码
        var sOKind = $("#txtOrderKind").val();  // 工单类型
        var sAct = $("#txtProcAct").val(); // 工序行为下拉选择的内容

        if (sWUnit == "") {
            $('#LMgs').html("请输入作业单元");
            return;
        }
        if (sSNo == "") {
            $('#LMgs').html("请输入序列号");
            return;
        }
        if (sScan == "") {
            $('#LMgs').html("请输入扫描的信息");
            return;
        }
        if (sProc == "") {
            $('#LMgs').html("获取不到作业工序，请确认");
            return;
        }


        var Data = '';
        var Params = { No: sSNo, Name: sOKind, Item: sWUnit, MNo: "", MName: sProcName, A: sEXENo, B: sProc, C: sVer, D: sScan, E: sPNo, F: OFlag, G: sWO, H: "", I: "", J: "", InMan: sEXEMan, Remark: sAct, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=OPPRDMaterInfo&CFlag=28-2",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $('#txtOPRec').val(sScan + " 扫描成功！" + "\n" + $('#txtOPRec').val());
                    $('#txtScanInfoNo').val("");

                    PRDSheet_onclick(parsedJson.RNo);

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#LMgs").html("您未登陆系统，请先登录！");
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTSOVER') {
                    $("#LMgs").html("序列号已完工或原工单绑定数量已满足重工单，无需再扫描！");
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_EXISTOVER') {
                    $("#LMgs").html("该批号/序列号批序号关系不存在或已使用！");
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_ReqMater') {
                    $("#LMgs").html("扫描编号不正确，确认是物料，批号还是序列号");
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTMATER') {
                    $("#LMgs").html("该物料已扫描完成，无需再扫描！");
                    $('#txtScanInfoNo').val("");
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_RepeatObject') {
                    $("#LMgs").html("在库重工不换序列号，扫描重工对象必须与当前生产的序列号一致！");
                    $('#txtScanInfoNo').val("");
                }
                else {
                    $("#SerialBOMSaveBtn").removeAttr("disabled");
                    $("#div_warning").html("系统出错，请重试1！")
                    $("#div_warning").show();
                }
            },
            error: function (data) {
                $("#SerialBOMSaveBtn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    }  // if (sScan.indexOf("[CR]") > 0)
}



//  维修：扫描作业单元
function ScanWXUnit_keydown(event) {

    if (event.keyCode == 13) {
        $("#L_Mgs").html("");
        $('#txtWXUnitName').val(""); // 作业单元名称
        var sFlag = "28-2";

        var sWUnit = $('#txtWXUnit').val();
        if (sWUnit == "") {
            $('#L_Mgs').html("请输入作业单元");
            return;
        }


        var Data = '';
        var Params = { No: sWUnit, Name: "", Item: "", MNo: "", MName: "", A: "", B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);;

        $.ajax({
            type: "POST",
            url: "../Service/OrderAjax.ashx?OP=GetWXInfo&CFlag=" + sFlag + "&CNO=" + sWUnit,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    var sStr = eval(parsedJson.json)[0];
                    $('#txtWXUnitName').val(parsedJson.BZBatch); // 作业单元名称
                    $("#txtSNCode").removeAttr("disabled");
                    $("#txtSNCode").focus();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_EXIST') {
                    $('#L_Mgs').html("该作业单元不存在，请确认！");
                    $("#txtScanNo").attr({ "disabled": "disabled" });
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_USEREXIST') {
                    $('#L_Mgs').html("该用户不能操作该作业单元，请确认！");
                    $("#txtScanNo").attr({ "disabled": "disabled" });
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_USEEXIST') {
                    $('#L_Mgs').html("该作业单元已在生产中，请确认！");
                    $("#txtScanNo").attr({ "disabled": "disabled" });
                }
                else {
                    $("#txtWCUserName").val("");
                    $("#L_Mgs").html("系统错误，请确认该员工是否有该作业单元权限！")
                }
            },
            error: function (data) {
                $("#L_Mgs").html("系统出错，请联系管理员2！")
            }
        });
    }  // if (event.keyCode == 13)

}


// 作业执行：包装检验，更改检验方案
function ChangeInspect() {

    var sEXEMan = $('#txtInMan').val();  //作业执行人员账号
    var sOName = $('#txtSamplPlanNameR').val();  // 原来的检验方案名称
    var sONO = $('#txtSamplPlanR').val();  // 原来的检验方案编码
    var sNNO = $('#txtSamplPlan').val();  //切换后的检验方案编号
    var sNName = $('#txtSamplPlan option:selected').text(); //选中的文本 获取下拉值    切换后的检验方案
    var sFlag = "29-10";

    var UpBatch = $("#txtBZBatch").val(); // 上层批次：包装上层批次--包装送检批次
    var sEXENo = $("#txtEXENo").val(); // 作业编号
    var sWO = $('#txtOrderNo').val();  // 工单 
    var sProcNo = $('#txtNowProcNo').val();  // 工序编码
    var sNum = $('#txtOrderNum').val();  // 检验批数

    var Data = '';
    var Params = { No: sProcNo, Name: "", Item: UpBatch, MNo: "", MName: "", A: sEXENo, B: sWO, C: sNName, D: sOName, E: sNum, F: sNNO, InMan: sEXEMan, Remark: "", Flag: sFlag };
    var Data = JSON.stringify(Params);

    $.ajax({
        type: "POST",
        url: "../Service/OrderAjax.ashx?OP=OPInspectInfo&CFlag=" + sFlag + "&CNO=" + UpBatch,
        data: { Data: Data },
        success: function (data) {
            var parsedJson = jQuery.parseJSON(data);

            if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                $('#txtOPRec').val(" 检验方案成功！" + "\n" + $('#txtOPRec').val());

                GetInspectInfo(UpBatch, sWO);
                $("#txtSamplPlan").attr({ "disabled": "disabled" });
            }
            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST888888888') {
                $("#LMgs").html("该工单已关闭或完工，无需操作！");
                $('#txtSamplPlan').val(sONO);
            }
            else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                $("#LMgs").html("该工序版本已有作业记录，不可更换！");
                $('#txtSamplPlan').val(sONO);
            }
            else {
                $("#LMgs").html("系统出错，请重试1！");
                $('#txtSamplPlan').val(sONO);
            }
        },
        error: function (data) {
            $("#LMgs").html("系统出错，请重试2！");
            $('#txtSamplPlan').val(sONO);
        }
    });

}



//消息提示
function SuccessMessage(text, time) {
    var Message = $('#Success');
    Message.html(text)
    Message.show()
    setTimeout(function () {
        Message.hide()
    }, time);
}
function ErrorMessage(text, time) {
    var Message = $('#Error');
    Message.html(text)
    Message.show()
    setTimeout(function () {
        Message.hide()
    }, time);
}
function WarningMessage(text, time) {
    var Message = $('#Warning');
    Message.html(text)
    Message.show()
    setTimeout(function () {
        Message.hide()
    }, time);
}
