﻿using System;
using System.Data;
using DAL;
using Model;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace BLL
{
    /// <summary>
    /// 客户套餐管理业务逻辑层
    /// </summary>
    public class CompanyComboBll
    {
        private static CompanyComboDal dal = new CompanyComboDal();

        /// <summary>
        /// 生成申请编号
        /// </summary>
        public static string CreateApplyNo()
        {
            return dal.CreateApplyNo();
        }

        /// <summary>
        /// 获取客户套餐信息列表
        /// </summary>
        public static DataTable GetCustMenuInfo(string applyNo, string custNo, string custName, string status, int rows, int page, string loginUser, string companyNo, string menuNo = "", string nowVer = "")
        {
            return dal.GetCustMenuInfo(applyNo, custNo, custName, status, rows, page, loginUser, companyNo, menuNo, nowVer);
        }

        /// <summary>
        /// 添加客户套餐信息
        /// </summary>
        public static string AddCustMenuInfo(string id, string applyNo, string custNo, string custName, string accountType, 
            DateTime settlementStartTime, DateTime settlementEndTime, string regType, string code, 
            DateTime effectiveDate, DateTime expiringDate, int settlementCycle, 
            string menuNo, string menuName, decimal basePrice, int bup, int tva, 
            decimal aUserPrice, string awoPrice, string dFunction, 
            bool sla, bool depthTrain, bool imServices, bool customDev, bool interfaceDev, bool onsiteSV, 
            bool nowVer, string status, string companyNo, string inMan, DateTime inDate, string remark)
        {
            CustMenuInfo menu = new CustMenuInfo
            {
                Id = id,
                ApplyNo = applyNo,
                CustNo = custNo,
                CustName = custName,
                AccountType = accountType,
                SettlementStartTime = settlementStartTime,
                SettlementEndTime = settlementEndTime,
                RegType = regType,
                Code = code,
                EffectiveDate = effectiveDate,
                ExpiringDate = expiringDate,
                SettlementCycle = settlementCycle,
                MenuNo = menuNo,
                MenuName = menuName,
                BasePrice = basePrice,
                BUP = bup,
                TVA = tva,
                AUserPrice = aUserPrice,
                AWOPrice = awoPrice,
                DFunction = dFunction,
                SLA = sla,
                DepthTrain = depthTrain,
                IMServices = imServices,
                CustomDev = customDev,
                InterfaceDev = interfaceDev,
                OnsiteSV = onsiteSV,
                NowVer = nowVer,
                Status = status,
                CompanyNo = companyNo,
                InMan = inMan,
                InDate = inDate,
                Remark = remark
            };

            string result = dal.AddCustMenuInfo(menu);
            return result;
        }

        /// <summary>
        /// 添加客户套餐信息（带事务）
        /// </summary>
        public static string AddCustMenuInfoWithTransaction(string id, string applyNo, string custNo, string custName, string accountType, 
            DateTime settlementStartTime, DateTime settlementEndTime, string regType, string code, 
            DateTime effectiveDate, DateTime expiringDate, int settlementCycle, 
            string menuNo, string menuName, decimal basePrice, int bup, int tva, 
            decimal aUserPrice, string awoPrice, string dFunction, 
            bool sla, bool depthTrain, bool imServices, bool customDev, bool interfaceDev, bool onsiteSV, 
            bool nowVer, string status, string companyNo, string inMan, DateTime inDate, string remark,
            List<KeyValuePair<string, SqlParameter[]>> additionalSqlParams)
        {
            CustMenuInfo menu = new CustMenuInfo
            {
                Id = id,
                ApplyNo = applyNo,
                CustNo = custNo,
                CustName = custName,
                AccountType = accountType,
                SettlementStartTime = settlementStartTime,
                SettlementEndTime = settlementEndTime,
                RegType = regType,
                Code = code,
                EffectiveDate = effectiveDate,
                ExpiringDate = expiringDate,
                SettlementCycle = settlementCycle,
                MenuNo = menuNo,
                MenuName = menuName,
                BasePrice = basePrice,
                BUP = bup,
                TVA = tva,
                AUserPrice = aUserPrice,
                AWOPrice = awoPrice,
                DFunction = dFunction,
                SLA = sla,
                DepthTrain = depthTrain,
                IMServices = imServices,
                CustomDev = customDev,
                InterfaceDev = interfaceDev,
                OnsiteSV = onsiteSV,
                NowVer = nowVer,
                Status = status,
                CompanyNo = companyNo,
                InMan = inMan,
                InDate = inDate,
                Remark = remark
            };

            string result = dal.AddCustMenuInfoWithTransaction(menu, additionalSqlParams);
            return result;
        }

        /// <summary>
        /// 更新客户套餐信息
        /// </summary>
        public static string UpdateCustMenuInfo(
            string id, string applyNo, string custNo, string custName, string accountType,
            DateTime settlementStartTime, DateTime settlementEndTime, string regType, string code,
            DateTime effectiveDate, DateTime expiringDate, int settlementCycle,
            string menuNo, string menuName, decimal basePrice, int bup, int tva,
            decimal aUserPrice, string aWOPrice, string dFunction,
            bool sla, bool depthTrain, bool imServices, bool customDev, bool interfaceDev, bool onsiteSV,
            bool nowVer, string status, string companyNo, string loginUser, DateTime inDate, string remark)
        {
            // 创建CustMenuInfo对象
            var menu = new CustMenuInfo
            {
                Id = id,
                ApplyNo = applyNo,
                CustNo = custNo,
                CustName = custName,
                AccountType = accountType,
                SettlementStartTime = settlementStartTime,
                SettlementEndTime = settlementEndTime,
                RegType = regType,
                Code = code,
                EffectiveDate = effectiveDate,
                ExpiringDate = expiringDate,
                SettlementCycle = settlementCycle,
                MenuNo = menuNo,
                MenuName = menuName,
                BasePrice = basePrice,
                BUP = bup,
                TVA = tva,
                AUserPrice = aUserPrice,
                AWOPrice = aWOPrice,
                DFunction = dFunction,
                SLA = sla,
                DepthTrain = depthTrain,
                IMServices = imServices,
                CustomDev = customDev,
                InterfaceDev = interfaceDev,
                OnsiteSV = onsiteSV,
                NowVer = nowVer,
                Status = status,
                CompanyNo = companyNo,
                InMan = loginUser,
                InDate = inDate,
                CHMan = loginUser,
                CHDate = DateTime.Now,
                Remark = remark
            };

            return dal.UpdateCustMenuInfo(menu);
        }

        /// <summary>
        /// 更新客户套餐状态
        /// </summary>
        public static string UpdateCustMenuStatus(string applyNo, string status, string companyNo, string loginUser)
        {
            return dal.UpdateCustMenuStatus(applyNo, status, companyNo, loginUser);
        }

        /// <summary>
        /// 删除客户套餐信息
        /// </summary>
        public static string DeleteCustMenuInfo(string applyNo, string companyNo, string loginUser)
        {
            return dal.DeleteCustMenuInfo(applyNo, companyNo, loginUser);
        }

        /// <summary>
        /// 检查客户套餐是否已存在
        /// </summary>
        /// <param name="custNo">客户编号</param>
        /// <param name="menuNo">套餐编号</param>
        /// <param name="excludeApplyNo">排除的申请编号（用于更新时排除自身）</param>
        /// <param name="companyNo">公司编号</param>
        /// <returns>true: 已存在; false: 不存在</returns>
        public static bool CheckCustMenuExists(string custNo, string menuNo, string excludeApplyNo, string companyNo)
        {
            return dal.CheckCustMenuExists(custNo, menuNo, excludeApplyNo, companyNo);
        }

        /// <summary>
        /// 续费客户套餐信息
        /// </summary>
        public static string RenewCustMenuInfo(string id, string applyNo, string custNo, string custName, string accountType, 
            DateTime settlementStartTime, DateTime settlementEndTime, string regType, string code, 
            DateTime effectiveDate, DateTime expiringDate, int settlementCycle, 
            string menuNo, string menuName, decimal basePrice, int bup, int tva, 
            decimal aUserPrice, string awoPrice, string dFunction, 
            bool sla, bool depthTrain, bool imServices, bool customDev, bool interfaceDev, bool onsiteSV, 
            bool nowVer, string status, string companyNo, string inMan, DateTime inDate, string remark,
            string originalApplyNo)
        {
            // 调用DAL层处理续费逻辑
            return dal.RenewCustMenuInfo(
                id, applyNo, custNo, custName, accountType, 
                settlementStartTime, settlementEndTime, regType, code, 
                effectiveDate, expiringDate, settlementCycle, 
                menuNo, menuName, basePrice, bup, tva, 
                aUserPrice, awoPrice, dFunction, 
                sla, depthTrain, imServices, customDev, interfaceDev, onsiteSV, 
                nowVer, status, companyNo, inMan, inDate, remark,
                originalApplyNo
            );
        }
    }
}
