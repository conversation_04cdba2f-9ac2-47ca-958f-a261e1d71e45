﻿<!DOCTYPE html
          PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>质量看板</title>
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/echarts.js"></script>

    <script>
        $(function () {
            init()
        })

        // 初始化函数
        function init() {
            const data = sessionStorage.getItem("KB0002");
            let passRateData = []; //直通率
            let procedurePassRateData = 0;  //工序直通率
            var badProblemData = [] //不良问题
            var repairRateData = [] //维修率

            // 校验并解析数据
            if (data) {
                try {
                    const jsonData = JSON.parse(data);
                    passRateData = jsonData[0] || [];
                    procedurePassRateData = jsonData[1] || [];
                    badProblemData = jsonData[2] || [];
                    repairRateData = jsonData[3] || [];
                } catch (error) {
                    console.error("数据解析失败:", error);
                }
            }

            // 渲染已完成和未完成的图表
            PassRateDataEcharts(passRateData, 'passRateDataEcharts', '生产部门直通率');
            ProcedurePassRateDataEcharts(procedurePassRateData, 'procedurePassRateDataEcharts', '各工序直通率');
            BadProblemDataEcharts(badProblemData, 'badProblemDataEcharts', '日维修率TOP不良问题');
            RepairRateDataEcharts(repairRateData, 'repairRateDataEcharts', '日维修率折线图_泵');
        }


        function PassRateDataEcharts(data, chartId, title) {
            if (!data || data.length == 0) {
                console.warn(`没有数据提供给图表 ${chartId}`);
                return;
            }
            const badCount = data[0]?.SNBadCount ?? 0
            const overCount = data[0]?.SNOverCount ?? 0
            const passRate = calcPassPercent(badCount, overCount)
            const chart = echarts.init(document.getElementById(chartId));
            chart.setOption({
                title: [
                    {
                        text: passRate + "%",
                        x: "center",
                        y: "center",
                        textStyle: {
                            fontSize: "35",
                            color: "white",
                            foontWeight: "bold",
                        },
                    },
                    {
                        text: title,
                        x: "center",
                        top: "2%",
                        textStyle: {
                            color: "#FFFFFF",
                            fontSize: "15px",
                            fontWeight: "500",
                        },
                    },
                ],
                backgroundColor: "#1b1e25",
                polar: {
                    radius: ["57%", "67%"],
                    center: ["50%", "53%"],
                },
                angleAxis: {
                    max: 100,
                    show: false,
                },
                radiusAxis: {
                    type: "category",
                    show: true,
                    axisLabel: {
                        show: false,
                    },
                    axisLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false,
                    },
                },
                series: [
                    {
                        name: "生产部门直通率",
                        type: "bar",
                        roundCap: true,
                        barWidth: 30,
                        showBackground: true,
                        backgroundStyle: {
                            color: "rgba(66, 66, 66, .3)",
                        },
                        data: [passRate],
                        coordinateSystem: "polar",
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                                    {
                                        offset: 0,
                                        color: "#16CEB9",
                                    },
                                    {
                                        offset: 1,
                                        color: "#6648FF",
                                    },
                                ]),
                            },
                        },
                    },
                    {
                        name: "",
                        type: "pie",
                        startAngle: 80,
                        radius: ["71%"],
                        hoverAnimation: false,
                        center: ["50%", "53%"],
                        itemStyle: {
                            color: "rgba(66, 66, 66, .1)",
                            borderWidth: 1,
                            borderColor: "#5269EE",
                        },
                        data: [100],
                    },
                    {
                        name: "",
                        type: "pie",
                        startAngle: 80,
                        radius: ["53%"],
                        hoverAnimation: false,
                        center: ["50%", "53%"],
                        itemStyle: {
                            color: "rgba(66, 66, 66, .1)",
                            borderWidth: 1,
                            borderColor: "#5269EE",
                        },
                        data: [100],
                    },
                ],
            });

            // 窗口调整时重新渲染图表
            $(window).resize(() => chart.resize());
        }

        function ProcedurePassRateDataEcharts(data, chartId, title) {
            if (!data || data.length == 0) {
                console.warn(`没有数据提供给图表 ${chartId}`);
                return;
            }

            const procedureName = data.map(item => item.ProcedureName)

            const procedurePassRate = data.map(item => calcPassPercent(item.ProcedureBadCount, item.ProcedureOverCount))

            const chart = echarts.init(document.getElementById(chartId));

            chart.setOption({
                backgroundColor: '#1b1e25',
                title: [
                    {
                        text: title,
                        x: "center",
                        top: "2%",
                        textStyle: {
                            color: "#FFFFFF",
                            fontSize: "15px",
                            fontWeight: "500",
                        },
                    },
                ],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    top: '15%',
                    right: '4%',
                    left: '6%',
                    bottom: '12%'
                },
                xAxis: [{
                    type: 'category',
                    data: procedureName,
                    axisLine: {
                        lineStyle: {
                            color: 'white'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: "#FFFFFF",
                        fontWeight: "bold",
                    }
                }],
                yAxis: [{
                    min: 0, // 设置最小刻度为 0
                    max: 100, // 设置最大刻度为 100
                    axisLabel: {
                        color: "#FFFFFF",
                        fontWeight: "bold",
                        formatter: '{value}%' // 可根据需要添加单位
                    },
                    axisLine: {
                        show: false
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(255,255,255,0.12)'
                        }
                    }
                }],
                series: [{
                    type: 'bar',
                    data: procedurePassRate,
                    barWidth: '20px',
                    itemStyle: {
                        normal: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                offset: 0,
                                color: 'rgba(0,244,255,1)' // 0% 处的颜色
                            }, {
                                offset: 1,
                                color: 'rgba(0,77,167,1)' // 100% 处的颜色
                            }], false),
                            barBorderRadius: [30, 30, 30, 30],
                            shadowColor: 'rgba(0,160,221,1)',
                            shadowBlur: 4,
                        }
                    },
                    label: {
                        show: true,
                        color: "white",
                        fontWeight: "bold",
                        position: 'top',
                        formatter: (params) => params.value == 0 ? '' : params.value + '%'
                    }
                }]
            })

            // 窗口调整时重新渲染图表
            $(window).resize(() => chart.resize());
        }

        function BadProblemDataEcharts(data, chartId, title) {
            if (!data || data.length == 0) {
                console.warn(`没有数据提供给图表 ${chartId}`);
                return;
            }

            const name = data.map(item => item.name)

            const chart = echarts.init(document.getElementById(chartId));
            chart.setOption({
                backgroundColor: "#1b1e25",
                title: [
                    {
                        text: title,
                        x: "center",
                        top: "2%",
                        textStyle: {
                            color: "#FFFFFF",
                            fontSize: "15px",
                            fontWeight: "500",
                        },
                    },
                ],
                tooltip: {
                    trigger: "item",
                    formatter: "{a} <br/>{b}: {c} ({d}%)",
                },
                legend: {
                    itemWidth: 13,
                    itemHeight: 13,
                    icon: 'circle',
                    data: name,
                    bottom: "3%",
                    x: 'center',
                    textStyle: {
                        color: "white",
                        fontWeight: "bold"
                    },
                },
                color: ["#fa4b46", "#ffc502", "#ae9fff", "#6ecdfa"],
                series: [
                    {
                        name: title,
                        type: "pie",
                        center: ["50%", "50%"],
                        radius: ["35%", "53%"],
                        startAngle: 360,
                        avoidLabelOverlap: false,

                        labelLine: {
                            normal: {
                                show: true,
                            },
                        },
                        data: data,
                        label: {
                            show: true,
                            position: "outside",
                            formatter(param) {
                                return (
                                    param.value + "个(" +
                                    param.percent + "%)"
                                );
                            },
                            color: "white",
                            fontWeight: "bold",
                        },
                    },
                ],
            });

            // 窗口调整时重新渲染图表
            $(window).resize(() => chart.resize());

        }

        function RepairRateDataEcharts(data, chartId, title) {
            if (!data || data.length == 0) {
                console.warn(`没有数据提供给图表 ${chartId}`);
                return;
            }

            const dayNum = data.map(item => item.DayNum)

            const repairRate = data.map(item => calcPercent(item.SNBadCount, item.SNOverCount))

            const chart = echarts.init(document.getElementById(chartId));

            chart.setOption({
                backgroundColor: "#1b1e25",
                title: [
                    {
                        text: title,
                        x: "center",
                        top: "2%",
                        textStyle: {
                            color: "#FFFFFF",
                            fontSize: "15px",
                            fontWeight: "500",
                        },
                    },
                ],
                tooltip: {
                    trigger: 'axis',
                },
                toolbox: {
                    show: true,
                    right: "1%",
                    feature: {
                        dataView: { readOnly: true },
                        magicType: { type: ['line', 'bar'] },
                        restore: { show: true }, // 隐藏还原按钮
                        saveAsImage: { show: true } // 隐藏保存图片按钮
                    },
                },
                grid: {

                    left: "4%", // 增加左侧边距
                    right: "2%",
                    bottom: "7%", // 增加底部边距
                    top: "12%",
                    containLabel: false, // 确保标签完全包含在网格内
                },
                legend: {
                    data: ['维修率'],
                    textStyle: {
                        color: "white",
                        fontWeight: "bold"
                    },
                    left: "1%",
                    top: "1%"
                },
                xAxis: {
                    type: 'category',
                    data: dayNum,
                    boundaryGap: false,
                    splitLine: {
                        show: false,
                        interval: 'auto',
                        lineStyle: {
                            color: ['#D4DFF5']
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'white'
                        }
                    },
                    axisLabel: {
                        margin: 10,
                        color: "#FFFFFF",
                        fontWeight: "bold",
                        formatter: '{value}日' // 可根据需要添加单位
                    }
                },
                yAxis: {
                    type: 'value',
                    min: 0, // 设置最小刻度为 0
                    max: 100, // 设置最大刻度为 100
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(255,255,255,0.12)'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#609ee9'
                        }
                    },
                    axisLabel: {
                        margin: 20,
                        color: "#FFFFFF",
                        fontWeight: "bold",
                        formatter: '{value}%', // 可根据需要添加单位,
                    },
                },
                series: [{
                    name: '维修率',
                    type: 'line',
                    showSymbol: true,
                    symbol: 'circle',
                    symbolSize: 10,
                    data: repairRate,
                    itemStyle: {
                        normal: {
                            color: '#fa4b46',
                        },
                    },
                    lineStyle: {
                        normal: {
                            width: 2
                        }
                    },
                    barWidth: '20',
                    label: {
                        show: true,
                        color: "white",
                        fontWeight: "bold",
                        position: 'top',
                        formatter: (params) => params.value == 0 ? '' : params.value + '%'
                    }
                }]
            });

            // 窗口调整时重新渲染图表
            $(window).resize(() => chart.resize());
        }

        // 计算直通率
        function calcPassPercent(bad, total) {
            return total ? (100 - (bad / total * 100)).toFixed(2) : "0.00";
        }

        // 统一百分比计算
        function calcPercent(bad, total) {
            return total ? (bad / total * 100).toFixed(2) : "0.00";
        }
    </script>

    <style>
        * {
            padding: 0;
            margin: 0
        }

        #container {
            height: 100vh;
            background: #15181d;
            overflow: hidden;
            position: relative;
            color: #ffffff;
        }

        .container-header {
            height: 43%;
            display: flex;
            margin-bottom: 10px;
        }

        .container-body {
            background-color: #1b1e25;
            height: 54.5%;
            margin: 0px 10px 10px 10px;
        }

        .header-left {
            /*flex: 1;*/
            width: 24%;
            background-color: #1b1e25;
            margin-left: 10px;
            margin-right: 5px;
        }

        .header-center {
            flex: 1;
            background-color: #1b1e25;
            margin-right: 5px;
            margin-left: 5px;
        }

        .header-right {
            /*flex: 1;*/
            width: 24%;
            background-color: #1b1e25;
            margin-right: 10px;
            margin-left: 5px;
        }
    </style>

</head>


<body>
    <div id="container">
        <!--顶部-->
        <div class="container-header">
            <div class="header-left">
                <div id="passRateDataEcharts" style="width: 100%; height: 100%;"></div>
            </div>
            <div class="header-center">
                <div id="procedurePassRateDataEcharts" style="width: 100%; height: 100%;"></div>
            </div>
            <div class="header-right">
                <div id="badProblemDataEcharts" style="width: 100%; height: 100%;"></div>
            </div>
        </div>
        <div class="container-body">
            <div id="repairRateDataEcharts" style="width: 100%; height: 100%;"></div>
        </div>
    </div>
</body>

</html>