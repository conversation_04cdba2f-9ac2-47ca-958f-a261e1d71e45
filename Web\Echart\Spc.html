﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="./css/layui.css" rel="stylesheet" />
    <script src="./js/layui.js"></script>
    <script src="./js/jquery-3.3.1.min.js"></script>
    <link href="css/XC.css" rel="stylesheet" />
    <link href="../css/all.css" rel="stylesheet" />

    <script>
        layui.use(function () {
            var table = layui.table;
            var form = layui.form;
            var upload = layui.upload;
            var element = layui.element;
            var $ = layui.$;

            var Data = GetRequestParams("", "", "101")

            // 创建渲染实例
            table.render({
                elem: '#ID-table-demo-css',
                url: '../Service/EchartAjax.ashx?OP=GetTemplateBase&Data=' + Data, 
                id: "templatelatebaseID",
                page: true,
                count: 50,
                limit: 20,
                limits: [10, 20, 30, 40, 50],
                height: 'full-80',
                cols: [[
                    { field: 'SPCNo', title: '编号', sort: true },
                    { field: 'SPCName', title: '名称', sort: true },
                    { field: 'SPCDesc', title: '描述' },
                    { field: 'InMan', title: '操作人' },
                    { field: 'InDate2', title: '添加时间' },
                    { field: 'Remark', title: '备注' },
                    { field: 'op', title: '操作', width: 180, toolbar: '#ID-table-demo-css-user', fixed: 'right' }
                ]]
            });

            $("#TemplateQuery_open").click(function () {
                var txtsBoardNo = $("#txtsBoardNo").val()
                var txtsBoardName = $("#txtsBoardName").val()
                var Data = GetRequestParams(txtsBoardNo, txtsBoardName, "101")
                table.reload('templatelatebaseID', {
                    method: 'post',
                    url: '../Service/EchartAjax.ashx?OP=GetTemplateBase&Data=' + Data,
                    page: {
                        curr: 1
                    }
                });
            })

            table.on('tool(ID-table-demo-css)', function (obj) {
                var data = obj.data, layEvent = obj.event;

                if (layEvent == "edit") {
                    $("#txtAEFlag").val("6")
                    $("#SPCNo-item").show()
                    $("#ShowOne").css("display", "block")
                    $("#ShowOne-fade").css("display", "block")
                    $("#SPCNo").val(data.SPCNo);
                    $("#SPCDesc").val(data.SPCDesc);
                    $("#SPCName").val(data.SPCName);
                    $("#SPCRemark").val(data.Remark);
                    $("#head-title1").html("修改看板")
                } else if (layEvent == "delete") {
                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("确定要删除该看板吗？ 看板编号：")
                    //设置删除的对象
                    $("#hint-value").html(data.SPCNo)

                    $("#txtdelBoardNo").val(data.SPCNo)
                } else if (layEvent == "params") {
                    $("#ShowTow").css("display", "block")
                    $("#ShowTow-fade").css("display", "block")
                    $("#startDate").val(formattedDate())
                    $("#sampleCapacity").append("<option></option>")
                    $("#sampleSize").append("<option></option>")
                    for (var i = 2; i <= 25; i++) {
                        if (i <= 10) {
                            $("#sampleCapacity").append("<option value='" + i + "'>" + i + "</option>")
                        }
                        $("#sampleSize").append("<option value='" + i + "'>" + i + "</option>")
                    }
                    $("#SpcNo").val(data.SPCNo)

                    GetTaskList(data.SPCNo)
                }


            })


            $("#TemplateBaseSave_Btn").click(function () {
                var SPCNo = $("#SPCNo").val();
                var SPCName = $("#SPCName").val();
                var SPCDesc = $("#SPCDesc").val();
                var SPCRemark = $("#SPCRemark").val()
                var sFlag = $("#txtAEFlag").val()

                if (SPCName == "") {
                    layer.msg("请输入看板名称")
                    return
                }


                var Data = '';
                var Params = { No: SPCNo, Name: SPCName, Item: SPCDesc, MNo: "", MName: "", A: "", B: "", C: "", D: "", E: "", F: "", Remark: SPCRemark, Flag: sFlag };
                var Data = JSON.stringify(Params);

                $.ajax({
                    url: "../Service/EchartAjax.ashx?OP=TemplateBaseOP",
                    data: { Data: Data },
                    type: "POST",
                    success: function (res) {
                        var data = jQuery.parseJSON(res)
                        if (data.Msg == "Success") {
                            if (sFlag == "5" || sFlag == "8")
                                layer.msg('添加成功');
                            else
                                layer.msg('修改成功');

                            closeDialog()
                            $("#TemplateQuery_open").click()
                        } else {
                            layer.msg("系统出错！")
                        }
                    },
                    error: function (err) {
                        layer.msg("系统出错！")
                    }
                })
            })


            $("#addTemplate").click(function () {
                $("#SPCNo-item").hide()
                $("#txtAEFlag").val("5")
                $("#ShowOne").css("display", "block")
                $("#ShowOne-fade").css("display", "block")
                $("#head-title1").html("新增看板")
            })

            $("#TemplateBaseDel_Btn").click(function () {

                var SPCNo = $("#txtdelBoardNo").val();

                var Data = '';
                var Params = { No: SPCNo, Name: "", Item: "", MNo: "", MName: "", A: "", B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: "7" };
                var Data = JSON.stringify(Params);

                $.ajax({
                    url: "../Service/EchartAjax.ashx?OP=TemplateBaseOP",
                    data: { Data: Data },
                    type: "POST",
                    success: function (res) {
                        $("#TemplateQuery_open").click()
                        layer.msg('删除成功');
                        closeDelDialog()
                    },
                    error: function (err) {
                        layer.Msg("系统出错！")
                    }
                })

            })

            $("#tempParams_Btn").click(function () {

                var SpcNo = $("#SpcNo").val()
                var productNo = $("#productNo").val();
                var processes = $("#processes").val();
                var specNo = $("#specNo").val();
                var sampleWay = $("#sampleWay").val()
                var SampleMethod = $("#SampleMethod").val()
                var sampleCapacity = $("#sampleCapacity").val()
                var sampleSize = $("#sampleSize").val()
                const startDate = new Date($("#startDate").val())
                const NowDate = new Date()
                const timeDiff = NowDate.getTime() - startDate.getTime()
                const day = (timeDiff / (1000 * 3600 * 24)) > 180

                if (productNo == "") {
                    layer.msg("请输选产品编码")
                    return
                } else if (processes == "") {
                    layer.msg("请输选工序")
                    return
                } else if (specNo == "") {
                    layer.msg("请输选择管制对象")
                    return
                } else if (SampleMethod == "") {
                    layer.msg("请输选择抽样方式")
                    return
                } else if (sampleCapacity == "") {
                    layer.msg("请输入样本容量")
                    return
                } else if (sampleSize == "") {
                    layer.msg("请输入样本量")
                    return
                } else if (startDate == "" || startDate == null) {
                    layer.msg("请选择开始日期")
                    return
                } else if (day) {
                    layer.msg("选择的开始时间超过180天")
                    return
                }

                //判断选择的抽样方式，如果不是间隔抽样就清空抽样方法
                sampleWay = SampleMethod == "SpaceSample" ? sampleWay : "3";

                var Data = '';
                var Params = { No: "", Name: specNo, Item: SpcNo, MNo: productNo, MName: "", A: processes, B: sampleWay, C: startDate, D: sampleCapacity, E: sampleSize, F: SampleMethod, Remark: "", Flag: "8" };
                var Data = JSON.stringify(Params);

                $.ajax({
                    url: "../Service/EchartAjax.ashx?OP=TemplateBaseOP",
                    data: { Data: Data },
                    type: "POST",
                    success: function (res) {
                        var data = jQuery.parseJSON(res)
                        if (data.Msg == "Success") {
                            layer.msg('设置成功');
                            closeParamsDialog()
                            GetTaskList(SpcNo)
                        } else {
                            layer.msg("系统出错！")
                        }
                    },
                    error: function (err) {
                        layer.Msg("系统出错！")
                    }
                })
            })

            $("#SampleMethod").change(function () {
                var SampleMethod = $("#SampleMethod").val()
                if (SampleMethod == "SpaceSample") {
                    $("#SampleTime").show()
                } else {
                    $("#SampleTime").hide()
                }
            })
        });

        function GetRequestParams(txtsBoardNo, txtsBoardName, flag) {
            var Data = '';
            var Params = { No: txtsBoardNo, Name: txtsBoardName, Item: "", MNo: "", MName: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "", E: "", F: "", G: "", H: "", I: "", J: "", K: "", L: "", Remark: "", Flag: flag };
            var Data = JSON.stringify(Params);
            return Data
        }

        function openDialog() {
            GetProcedureList()
            $("#ShowThree").css("display", "block")
            $("#ShowThree-fade").css("display", "block")

            var Data = '';
            var Params = { No: "", Name: "", Item: "", MNo: "", MName: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "", E: "", F: "", G: "", H: "", I: "", J: "", K: "", L: "", Remark: "", Flag: "102" };
            var Data = JSON.stringify(Params);

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#TestItemlist',
                    id: 'TestItemID',
                    url: '../Service/EchartAjax.ashx?OP=GetTemplateBase&Data=' + Data,
                    height: 'full-200',
                    cellMinWidth: 80,
                    count: 100, //数据总数 服务端获得
                    limit: 100, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [20, 50, 100, 150, 200, 250, 300], //分页显示每页条目下拉选择
                    cols: [[
                        { type: 'numbers', title: '序号', width: 60 },
                        { field: 'SpecNo', title: '编号', width: 200 },
                        { field: 'ProductNo', title: '产品编码', width: 200 },
                        { field: 'ProcedureNo', title: '工序编号', width: 100 },
                        { field: 'ProcedureName', title: '工序名称', width: 120 },
                        { field: 'NameCH', title: '检验项目', },
                        { field: 'DescCH', title: '检验要求', },
                        //{ field: 'Model', title: '适用型号', width: 180 },
                        { field: 'ValueKind', title: '数据类型', width: 100 },
                        //{ field: 'RangeKind', title: '区间', width: 70 },
                        //{ field: 'ThisValue', title: '下限', width: 70 },
                        //{ field: 'ToValue', title: '上限', width: 70 },
                        //{ field: 'SpecUnit', title: '单位', width: 70 },
                        //{ field: 'Remark', title: '备注', width: 150 },
                        //{ field: 'InMan', title: '录入人', width: 90 },
                        //{ field: 'InDate2', title: '录入时间', width: 180 },
                        { field: 'op', title: '操作', width: 120, toolbar: '#barDemo', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(TestItemlist)', function (obj) {
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值

                    if (layEvent === 'select') {
                        $("#productNo").val(data.ProductNo)
                        $("#processes").val("(" + data.ProcedureNo + ")" + data.ProcedureName)
                        $("#specNo").val("(" + data.SpecNo + ")" + data.NameCH)
                        closeTestDialog()
                    }
                })

                //  查询 --
                $('#BaseTestItemBut_open').click(function () {

                    var sMNo = $("#txtSProductNo").val();  //产品编码
                    var sTNo = $("#txtSProcNo").val();  //工序  (05)总
                    var sGXNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
                    var sNameCH = $("#txtNameCH").val();  //型号

                    Params.MNo = sMNo
                    Params.No = sGXNo
                    Params.Name = sNameCH
                    Data = JSON.stringify(Params)

                    table.reload('TestItemID', {
                        method: 'post',
                        url: '../Service/EchartAjax.ashx?OP=GetTemplateBase&Data=' + Data,
                        where: {
                            'No': sMNo,
                            'name': sNameCH
                        }, page: {
                            curr: 1
                        }
                    });
                });
            })
        }

        function GetTaskList(No) {
            layui.use('table', function () {
                var table = layui.table;

                var Data = GetRequestParams(No, "", "104")

                // 创建渲染实例
                table.render({
                    elem: '#TaskList',
                    id: 'TaskListID',
                    url: '../Service/EchartAjax.ashx?OP=GetTemplateBase&Data=' + Data, // 此处为静态模拟数据，实际使用时需换成真实接口
                    page: true,
                    height: '350px',
                    cols: [[
                        { type: 'numbers', title: '序号', width: 60 },
                        { field: 'TaskNo', title: '任务编号', width: 115 },
                        { field: 'MaterNo', title: '产品编码', width: 120 },
                        { field: 'ProcedureNo', title: '工序', width: 170 },
                        { field: 'SamplingObject', title: '管制对象', width: 280 },
                        { field: 'SampleCapacity', title: '样本容量(子组大小)', width: 140, align: 'center' },
                        { field: 'SampleSize', title: '样本量(组数)', width: 100, align: 'center' },
                        {
                            field: 'SamplingMethods', title: '抽样方法', width: 85, align: 'center', templet: function (d) {
                                if (d.SamplingMethods >= 60) {
                                    return (d.SamplingMethods / 60) + '小时'
                                } else {
                                    return d.SamplingMethods + '分钟'
                                }

                            }
                        },
                        {
                            field: 'Status', width: 60, title: '状态', width: 75, align: 'center', templet: function (d) {
                                if (d.Status == '未开始') {
                                    return '<span style="background-color: #b4afb1; color: white; font-size: 12px; padding: 4px">' + d.Status + '</span>';
                                } else if (d.Status == '进行中') {
                                    return '<span style="background-color: #0c873d; color: white; font-size: 12px; padding: 4px">' + d.Status + '</span>';
                                } else {
                                    return '<span style="background-color: #d42e2e; color: white; font-size: 12px; padding: 4px">' + d.Status + '</span>';
                                }
                            }
                        },
                        {
                            field: 'SamplingWay', title: '抽样方式', width: 100, align: 'center', templet: function (d) {
                                return d.SamplingWay == "ContinueSample" ? "连续抽样" : "间隔抽样"
                            }  
                        },
                        { field: 'InMan', width: 70, title: '操作者', align: 'center' },
                        { field: 'EndTime2', width: 80, title: '开始时间', width: 140 },
                        { field: 'InDate2', width: 80, title: '添加时间', width: 140 },
                        { field: 'op', title: '操作', width: 240, toolbar: '#TaskListbarDemo', fixed: 'right' }

                    ]]
                });

                //监听行工具事件
                table.on('tool(TaskList)', function (obj) {
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值
                    var No = data.SPCNo
                    if (layEvent == 'delete') {
                        if (data.Status != "未开始") {
                            layer.msg("只能删除还未开始的")
                            return
                        } else {

                            layer.confirm('确定要删除该任务吗？', function (index) {
                                var Data = '';
                                var Params = { No: data.TaskNo, Name: "", Item: "", MNo: "", MName: "", A: "", B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: "9" };
                                var Data = JSON.stringify(Params);
                                $.ajax({
                                    url: "../Service/EchartAjax.ashx?OP=TemplateBaseOP",
                                    data: { Data: Data },
                                    type: "POST",
                                    success: function (res) {
                                        var data = jQuery.parseJSON(res)
                                        if (data.Msg == "Success") {
                                            layer.msg('删除成功');
                                            GetTaskList(No)
                                        } else {
                                            layer.msg("系统出错！")
                                        }
                                    },
                                    error: function (err) {
                                        layer.Msg("系统出错！")
                                    }
                                })
                            })
                        }
                    } else if (layEvent == "start") {
                        if (data.Status == "进行中") {
                            layer.msg("编号：" + data.TaskNo + " 正在进行中")
                            return
                        } else if (data.Status == "已结束") {
                            layer.msg("编号：" + data.TaskNo + " 已结束，不能启动")
                            return
                        } else {
                            layer.confirm('确定要启动该任务吗？', function (index) {
                                var Data = '';
                                var Params = { No: data.TaskNo, Name: "", Item: "", MNo: "", MName: "", A: data.EndTime2, B: "", C: data.SamplingMethods, D: "", E: "", F: "", Remark: "", Flag: "1" };
                                var Data = JSON.stringify(Params);
                                $.ajax({
                                    url: "../Service/TimerAjax.ashx?OP=AddTask",
                                    data: { Data: Data },
                                    type: "POST",
                                    success: function (res) {
                                        var data = jQuery.parseJSON(res)
                                        if (data.Msg == "Success") {
                                            layer.msg('设置成功');
                                            GetTaskList(No)
                                        } else {
                                            layer.msg("系统出错！")
                                        }
                                    },
                                    error: function (err) {
                                        layer.Msg("系统出错！")
                                    }
                                })
                            })
                        }
                    } else if (layEvent == "end") {
                        if (data.Status == "未开始") {
                            layer.msg("编号：" + data.TaskNo + " 还未未开始，不能关闭")
                            return
                        } else if (data.Status == "已结束") {
                            layer.msg("编号：" + data.TaskNo + " 已结束，不能关闭")
                            return
                        } else {
                            layer.confirm('确定要关闭吗？', function (index) {
                                var Data = '';
                                var Params = { No: data.TaskNo, Name: "", Item: "", MNo: "", MName: "", A: "", B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: "2" };
                                var Data = JSON.stringify(Params);
                                $.ajax({
                                    url: "../Service/TimerAjax.ashx?OP=RemoveTask",
                                    data: { Data: Data },
                                    type: "POST",
                                    success: function (res) {
                                        var data = jQuery.parseJSON(res)
                                        if (data.Msg == "Success") {
                                            layer.msg('设置成功');
                                            GetTaskList(No)
                                        } else {
                                            layer.msg("系统出错！")
                                        }
                                    },
                                    error: function (err) {
                                        layer.Msg("系统出错！")
                                    }
                                })
                            })
                        }

                    }
                    else if (layEvent == "view") {
                        if (data.Status == "未开始") {
                            layer.msg("编号：" + data.TaskNo + " 还未未开始，不能查看")
                            return
                        }

                        window.open("/Echart/SPC_Main.htm?No=" + data.TaskNo + "&SC=" + data.SampleCapacity)
                    }
                })
            });
        }


        function GetProcedureList() {  // 加载工序
            var sSs = "工序";
            var keywords = encodeURI(sSs);  // 解决中文乱码的问题。 txtSProcNo
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/TechAjax.ashx?OP=GetProcedureList&CFlag=111-1&CKind=" + keywords,
                success: function (data) {
                    var parsedJson = eval(data).Msg;
                    var sKong = "<option value=''> </option>";
                    $("#txtSProcNo").empty();
                    $("#txtSProcNo").append(sKong + parsedJson);
                }
            });
        }


        function closeDialog() {
            $("#ShowOne").css("display", "none")
            $("#ShowOne-fade").css("display", "none")
            $("#SPCNo").val("");
            $("#SPCName").val("");
            $("#SPCDesc").val("");
            $("#SPCRemark").val("");
        }


        function closeDelDialog() {
            $("#ShowDel").css("display", "none")
            $("#ShowDel-fade").css("display", "none")
        }


        function closeParamsDialog(c) {
            if (c == "1") {
                $("#ShowTow").css("display", "none")
                $("#ShowTow-fade").css("display", "none")
            }
            $("#productNo").val("");
            $("#processes").val("");
            $("#specNo").val("");
            $("#sampleWay").val("30")
            $("#startDate").val(formattedDate());
            $("#sampleCapacity").val("")
            $("#sampleSize").val("")
        }

        function closeTestDialog() {
            $("#ShowThree").css("display", "none")
            $("#ShowThree-fade").css("display", "none")
        }

        function formattedDate() {
            let now = new Date();
            let year = now.getFullYear();
            let month = (now.getMonth() + 1).toString().padStart(2, '0');
            let day = now.getDate().toString().padStart(2, '0');
            let hours = now.getHours().toString().padStart(2, '0');
            let minutes = now.getMinutes().toString().padStart(2, '0');
            let seconds = now.getSeconds().toString().padStart(2,'0')
            return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
        }
    </script>

    <style>
        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }

        #ShowThree .div_find {
            padding: 5px 0px 15px 5px;
        }

        .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: white;
            z-index: 1001;
            opacity: 0.95;
        }

        #ShowTow .XC-Span-Input-block {
            width: 110px;
        }

        .layui-table-header td, .layui-table-header th, .layui-table-body td, .layui-table-body th {
            font-size: 12px
        }
    </style>
</head>
<body>
    <div class="div_find" style="width:99%">
        <label class="find_labela">编号</label> <input type="text" id="txtsBoardNo" class="find_input" />
        <label class="find_labela">名称</label> <input type="text" id="txtsBoardName" class="find_input" />
        <input type="button" value="搜索" class="XC-Btn-Green XC-Size-xs XC-Btn-md" id="TemplateQuery_open" />
        <input type="button" value="添加" class="XC-Btn-Green XC-Size-xs XC-Btn-md" id="addTemplate" />
    </div>

    <div style="padding: 5px;">
        <table class="layui-hide" id="ID-table-demo-css" lay-filter="ID-table-demo-css"></table>
    </div>

    <script type="text/html" id="ID-table-demo-css-user">
        <div style="text-align:right">
            <button class="XC-Btn-Green XC-Size-xs XC-Btn-md" style="margin-left:0px" lay-event="edit">修改</button>
            <button class="XC-Btn-Red XC-Size-xs XC-Btn-md" style="margin-left:0px" lay-event="delete">删除</button>
            <button class="XC-Btn-Green XC-Size-xs XC-Btn-md" style="margin-left:0px" lay-event="params">设置</button>
        </div>
    </script>


    <div class="XC-modal XC-modal-md" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title1" id="head-title1">标题</span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <form class="XC-Form-block">
                <div class="XC-Form-block-Item" id="SPCNo-item">
                    <span class="XC-Span-Input-block">编号</span>
                    <input type="text" class="XC-Input-block" id="SPCNo" name="SPCNo" disabled="disabled" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Input-block">名称</span>
                    <input type="text" class="XC-Input-block" id="SPCName" name="SPCName" />
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Textarea-block">描述</span>
                    <textarea class="XC-Textarea-block" id="SPCDesc" name="SPCDesc" style="height: 100px; width: 99%;"> </textarea>
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Textarea-block">备注</span>
                    <textarea class="XC-Textarea-block" id="SPCRemark" name="SPCRemark" style="height: 100px; width: 99%;"> </textarea>
                </div>
            </form>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtEditKind" name="txtEditKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="TemplateBaseSave_Btn">提交</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick='closeDialog()'>关闭</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>

    <div class="XC-modal XC-modal-xl" id="ShowTow" style="background-color: #fafafa">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1">设置</span>
            <span class="head-close" onclick="closeParamsDialog('1')">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center" style="height: calc(100vh - 95px);">
                <div class="layui-card" style="border: 1px solid #edf2fa">
                    <div class="layui-card-header" style="border-bottom: 1px solid #edf2fa">设置参数</div>
                    <div class="layui-card-body">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item" id="boardNo-item" style="display:none">
                                <span class="XC-Span-Input-block">SpcNo</span>
                                <input type="text" class="XC-Input-block" id="SpcNo" name="SpcNo" disabled="disabled" />
                            </div>
                            <div class="XC-Form-block-Item" id="boardNo-item">
                                <span class="XC-Span-Input-block">产品编码</span>
                                <input type="text" class="XC-Input-block" id="productNo" name="productNo" disabled="disabled" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">工序</span>
                                <input type="text" class="XC-Input-block" id="processes" name="processes" disabled="disabled" />

                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">管制对象</span>
                                <input class="XC-Input-block" id="specNo" name="specNo" disabled="disabled" />
                                <input type="button" value="选择" class="XC-Btn-Green XC-Size-xs XC-Btn-md" onclick="openDialog()" style="margin-left:15px" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block" style="line-height:30px">抽样方式</span>
                                <select class="XC-Input-block" id="SampleMethod" name="SampleMethod">
                                    <option value=""></option>
                                    <option value="ContinueSample">连续抽样</option>
                                    <option value="SpaceSample">间隔抽样</option>
                                </select>
                            </div>
                            <div class="XC-Form-block-Item" id="SampleTime" style="display:none">
                                <span class="XC-Span-Input-block" style="line-height:30px">抽样方法</span>
                                <select class="XC-Input-block" id="sampleWay" name="sampleWay">
                                    <!--<option value="3">3分钟</option>-->
                                    <option value="30" selected>30分钟</option>
                                    <option value="60">1小时</option>
                                    <option value="120">2小时</option>
                                    <option value="180">3小时</option>
                                    <option value="360">6小时</option>
                                    <option value="1440">24小时</option>
                                </select>
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">样本容量(子组大小)</span>
                                <select class="XC-Input-block" id="sampleCapacity" name="sampleCapacity">
                                </select>
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">样本量(组数)</span>
                                <select class="XC-Input-block" id="sampleSize" name="sampleSize">
                                </select>
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">开始日期</span>
                                <input type="datetime-local" class="XC-Input-block" id="startDate" />
                            </div>
                            <!--<div class="XC-Form-block-Item">
        <span class="XC-Span-Select-block">告警规则</span>
    </div>-->
                        </form>
                        <div style="text-align:right">
                            <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="tempParams_Btn">保存</button>
                            <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="ProcSaveClose" onclick="closeParamsDialog('1')">取消</button>
                        </div>
                    </div>
                </div>
                <div class="layui-card" style="border: 1px solid #edf2fa">
                    <div class="layui-card-header" style="border-bottom: 1px solid #edf2fa">任务列表</div>
                    <div class="layui-card-body">
                        <table class="layui-hide" id="TaskList" lay-filter="TaskList"></table>
                        <script type="text/html" id="TaskListbarDemo">
                            <div style="text-align:right">
                                <button class="XC-Btn-Green XC-Size-xs XC-Btn-md" style="margin-left:0px" lay-event="start">启动</button>
                                <button class="XC-Btn-Green XC-Size-xs XC-Btn-md" style="margin-left:0px" lay-event="view">查看</button>
                                <button class="XC-Btn-Red XC-Size-xs XC-Btn-md" style="margin-left:0px" lay-event="end">关闭</button>
                                <button class="XC-Btn-Red XC-Size-xs XC-Btn-md" style="margin-left:0px" lay-event="delete">删除</button>
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowTow-fade" class="black_overlay">
    </div>

    <div class="XC-modal XC-modal-xl" id="ShowThree" style="z-index:1200">
        <div class="XC-modal-head">
            <span class="head-title3">选择测试项</span>
            <span class="head-close" onclick="closeTestDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <div class="div_find">

                    <p>
                        <label class="find_labela">产品编码</label> <input type="text" id="txtSProductNo" class="find_input" />
                        <label class="find_labela">工序</label>
                        <select class="find_input" id="txtSProcNo">
                            <option></option>
                        </select>
                        <label class="find_labela">检验项目</label><input type="text" id="txtNameCH" class="find_input" />
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="BaseTestItemBut_open">搜索</button>
                    </p>
                </div>
                <table class="layui-hide" id="TestItemlist" lay-filter="TestItemlist"></table>
                <script type="text/html" id="barDemo">
                    <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="select">选择</button>
                </script>
            </div>
        </div>
    </div>
    <div id="ShowThree-fade" class="black_overlay" style="z-index:1119">
    </div>


    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDelDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtdelBoardNo" name="txtdelBoardNo" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="TemplateBaseDel_Btn">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDelDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>

    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>
</html>