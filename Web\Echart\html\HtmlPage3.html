﻿<!DOCTYPE html
          PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title></title>
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/echarts.js"></script>

    <script>
        $(function () {

            var data = sessionStorage.getItem("KB0003")
            var JsonData = null
            var EchartData1 = []
            if (data != "" && data != null) { // 确保data存在且不为空字符串
                JsonData = JSON.parse(data);
                if (JsonData.length > 0) {
                    EchartData1 = JsonData[0]
                }
            }

            var serie = []



            var Procedure = [...new Set(EchartData1.map(item => item.ProcedureName))];

            var order = [...new Set(EchartData1.map(item => item.OrderNo))];


            Procedure.forEach((item1, index1) => {
                var ObjData = []
                order.forEach((item2, index2) => {
                    var f = EchartData1.filter(f => f.OrderNo == item2 && f.ProcedureName == item1)
                    if (f.length > 0) {
                        ObjData.push(f[0].Num)
                    } else {
                        ObjData.push(0)
                    }
                })

                var obj = {
                    name: item1,
                    type: 'bar',
                    stack: 'total',
                    label: {
                        show: true,
                        color: 'white', // 设置 Y 轴标签的字体颜色为红色
                        fontWeight: "bold",
                        formatter: function (params) {
                            if (params.value == 0) { //为0时不显示
                                return ''
                            } else {
                                return params.value
                            }
                        }
                    },
                    emphasis: {
                        focus: 'series'
                    },
                    data: ObjData
                }

                serie.push(obj)
            })




            var myChart4 = echarts.init(document.getElementById('myChart4'));

            var option4 = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    textStyle: {
                        color: "white",
                        fontWeight: "bold"
                    },
                },
                grid: {
                    // 定义画布的位置，这里设为居中
                    left: 'center',
                    right: 'center',
                    top: '5%',
                    bottom: 'middle',
                    // 定义画布的大小，这里将其宽和高都设置为 50%
                    width: '88%',
                    height: '90%'
                },
                toolbox: {
                    show: true,
                    feature: {
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ['line', 'bar'] },
                        restore: { show: true },
                        saveAsImage: { show: true }
                    }
                },
                xAxis: {
                    type: 'value'
                },
                yAxis: {
                    type: 'category',
                    data: order,
                    axisLabel: {
                        color: 'white', // 设置 Y 轴标签的字体颜色为红色
                        fontWeight: "bold"
                    },
                },
                series: serie
            };

            myChart4.setOption(option4);

            $(window).resize(function () {
                myChart4.resize();
            })
        })


    </script>

    <style>
        * {
            padding: 0;
            margin: 0
        }

        #container {
            height: 100vh;
            background: #15181d;
            overflow: hidden;
            position: relative;
            color: #ffffff;
        }

        .container-footer {
            height: 96.5%;
            background: Green;
            margin: 10px;
            background-color: #1b1e25;
            padding: 5px;
        }

        .status {
            width: 100px;
            padding: 5px;
            float: left;
            position: relative;
            z-index: 9999;
            /* 设置一个较高的值 */
            margin: 5px 0px 0px 5px;
            font-size: 12px;
        }

            .status:focus {
                outline: none;
            }
    </style>

</head>


<body>
    <div id="container">
        <div class="container-footer">
            <div id="myChart4" style="width: 100%; height: 100%;"></div>
        </div>
    </div>
</body>

</html>