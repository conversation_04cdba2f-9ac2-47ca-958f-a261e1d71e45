﻿using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml.Linq;
using BLL;
using Newtonsoft.Json;
using System.Web.SessionState;
using Common;
using System.Collections.Generic;
using WXCommon;



namespace Web.WebService
{
    /// <summary>
    /// GetSerielInfo 的摘要说明
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    [ToolboxItem(false)]
    // 若要允许使用 ASP.NET AJAX 从脚本中调用此 Web 服务，请取消对下行的注释。
    // [System.Web.Script.Services.ScriptService]
    public class GetSerielInfo : System.Web.Services.WebService
    {


        [WebMethod]
        public string GetSerielInfobyCon(string sOrderNo, int sNum,string sComp)
        {
            string sJson = string.Empty;
            string sReturn = string.Empty;
            string Message = string.Empty;
            string sOKFlag = string.Empty;


            //  先看看有没有产品流程
            sOKFlag = OrderBll.JudgeObjectExist(sOrderNo, "", "", sComp, "28-1-2-1");
            if (sOKFlag == "Y")  // 说明已有流程，而且有烧录这个工序
            {
                DataTable dt = OrderBll.GetOrderInfo(sOrderNo, "", "", "", "", "", "", "", "", "", "", "", 2000, sNum, "", sComp, "111-4");
                if (dt.Rows.Count > 0)
                {
                    sJson = JsonConvert.SerializeObject(dt);
                }
                else
                {
                    Message = "Error：无序列号信息。";
                }
            }
            else
            {
                Message = "Error:该工单对应序列号无工艺流程（无烧录工序），请先建立流程。";
                sJson = JsonConvert.SerializeObject(new { Msg = Message });
            }


            return sJson;
        }






    }
}
