﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>清场记录</title>

    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <link href="../css/orderinfo.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <script src="../layui/layui.js" type="text/javascript"></script>
    <link href="../js/skin/layer.css" rel="stylesheet" />
    <link href="../css/XC.css" rel="stylesheet" />
    <script src="../js/layer/layer.js" type="text/javascript"></script>
    <script src="../js/PrdAssist.js" type="text/javascript"></script>



    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }
        //
        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        $('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=28-1' });
                    }
                }
            })
        });

    </script>


    <script type="text/javascript">
        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#OrderClearReclist',
                id: 'OrderClearReclistID',
                url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=14',
                height: 'full-80',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'CRNo', title: '清场记录编号', width: 150, sort: true },
                    { field: 'MaterSpec', title: '型号', width: 100 },
                    { field: 'OrderNo', title: '工单号', width: 100, sort: true },
                    { field: 'Status', title: '状态', width: 80 },
                    { field: 'CPNo', title: '清场计划', width: 150 },
                    { field: 'CPVer', title: '版本', width: 50 },
                    { field: 'MaterNo', title: '物料编码', width: 150 },
                    { field: 'DeptName', title: '部门', width: 100 },
                    { field: 'GroupName', title: '组别', width: 100 },
                    { field: 'InMan', title: '创建人', width: 90 },
                    { field: 'InDate2', title: '创建时间', width: 150, sort: true },
                    { field: 'op', title: '操作', width: 180, toolbar: '#barDemo', fixed: 'right' }
                ]],
                page: true,


            });
            //监听是否选中操作
            table.on('checkbox(OrderClearReclist)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);


            });


            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(OrderClearReclist)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

            });

            //监听单元格编辑
            table.on('edit(OrderClearReclist)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });


            //监听行工具事件
            table.on('tool(OrderClearReclist)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值
                if (layEvent === 'del') {

                    if (data.Status != "未执行") {
                        layer.msg('已执行清场工作，禁止删除！');
                        $('#OrderClearRecBut_open').click();  // 重新查询
                        return;
                    }

                    layer.confirm('您确定要删除该清场记录么？', function (index) {


                        //向服务端发送禁用指令
                        var sCRNo = data.CRNo;
                        //var sCPVer = data.CPVer;
                        var sFlag = "18";

                        var Data = '';
                        var Params = { No: sCRNo, Name: "", A: "", B: "", C: "", D: "", E: "", F: "", I: "", J: "", K: "", L: "", Kind: "", Remark: "", Flag: sFlag };
                        var Data = JSON.stringify(Params);
                        $.ajax({
                            type: "POST",
                            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
                            data: { Data: Data },
                            success: function (data) {
                                var parsedJson = jQuery.parseJSON(data);

                                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                    layer.msg('删除成功！');

                                    $('#OrderClearRecBut_open').click();  // 重新查询


                                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                                    layer.msg('工序已用于工艺流程，不能删除！');

                                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST1') {
                                    layer.msg('工序已用于BOM设计，不能删除！');

                                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST2') {
                                    layer.msg('工序已用于产品对应标贴，不能删除！');

                                }
                                else {
                                    layer.msg('删除失败，请重试！');
                                    $('#OrderClearRecBut_open').click();  // 重新查询
                                }
                            },
                            error: function (data) {
                                layer.msg('删除失败2，请重试！');
                                $('#OrderClearRecBut_open').click();  // 重新查询
                            }
                        });

                    }); // 删除

                }
                else if (layEvent === 'detail')//显示明细页面
                {
                    $('#L_ShowOrderClearRec').html("清场记录详情");
                    $('#txtAEFlag').val("16-1");

                    $("#txtCRNo").val(data.CRNo);
                    $("#txtCROrderNo").val(data.OrderNo);
                    $("#txtCRMaterNo").val(data.MaterNo);
                    $("#txtCRMaterSpec").val(data.MaterSpec);
                    $("#txtCRStatus").val(data.Status);

                    $("#txtCRCPNo").val(data.CPNo);
                    $("#txtCRCPVer").val(data.CPVer);
                    $("#txtCRDeptName").val(data.DeptName);
                    $("#txtCRGroupName").val(data.GroupName);
                    $("#txtCRInMan").val(data.InMan);

                    $("#txtCRInDate").val(data.InDate2);


                    $("#div_warning").html("");
                    $("#div_warning").hide();

                    //弹窗显示清场记录明细页
                    ShowOrderClearRecDetail(data.CRNo);

                    $("barDemo").hide();

                    document.getElementById('OrderBut_open').style.display = 'none';
                    document.getElementById('btn_CRCP_open').style.display = 'none';
                    document.getElementById('btn_DeptInfo_open').style.display = 'none';
                    document.getElementById('btn_OrderClearRecDetailSave').style.display = 'none';
                    /*document.getElementById('div_ClearPlanInfoSelect').style.display = 'block';*/
                    document.getElementById('div_OrderClearRecDetail').style.display = 'block';
                    document.getElementById('fade').style.display = 'block';
                    $("#div_ClearRecPDFList").show()
                    GetClearRecPDF()
                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }
                else if (layEvent === 'exe')//显示明细页面
                {
                    $('#L_ShowOrderClearRec').html("清场记录执行");
                    $('#txtAEFlag').val("16");

                    $("#txtCRNo").val(data.CRNo);
                    $("#txtCROrderNo").val(data.OrderNo);
                    $("#txtCRMaterNo").val(data.MaterNo);
                    $("#txtCRMaterSpec").val(data.MaterSpec);
                    $("#txtCRStatus").val(data.Status);

                    $("#txtCRCPNo").val(data.CPNo);
                    $("#txtCRCPVer").val(data.CPVer);
                    $("#txtCRDeptName").val(data.DeptName);
                    $("#txtCRGroupName").val(data.GroupName);
                    $("#txtCRInMan").val(data.InMan);

                    $("#txtCRInDate").val(data.InDate2);

                    $("barDemo").show();

                    $("#div_warning").html("");
                    $("#div_warning").hide();

                    //弹窗显示清场记录明细页
                    ShowOrderClearRecExe(data.CRNo);

                    document.getElementById('OrderBut_open').style.display = 'none';
                    document.getElementById('btn_CRCP_open').style.display = 'none';
                    document.getElementById('btn_DeptInfo_open').style.display = 'none';

                    document.getElementById('btn_OrderClearRecDetailSave').style.display = 'none';
                    /*document.getElementById('div_ClearPlanInfoSelect').style.display = 'block';*/
                    document.getElementById('div_OrderClearRecDetail').style.display = 'block';
                    document.getElementById('fade').style.display = 'block';

                    $("#div_ClearRecPDFList").show()
                    GetClearRecPDF()
                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }

            });

            //  查询表头 --
            $('#OrderClearRecBut_open').click(function () {

                var sCPNo = $("#txtSCRNo").val();  //清场记录编号
                var sCPVer = encodeURI($("#txtSCROrderNo").val());  //工单号

                var Data = '';
                var Params = { No: sCPNo, CPVer: sCPVer, Item: "", Status: "", BDate: "", EDate: "", A: sCPVer, B: "", C: "", D: "", E: "" };
                var Data = JSON.stringify(Params);

                table.reload('OrderClearReclistID', {
                    method: 'post',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=14&Data=' + Data,
                    where: {
                        'No': sCPNo,
                        'name': sCPVer
                    }, page: {
                        curr: 1
                    }
                });
            });

        });
        function openDialog(n) {
            if (n == 1) { // 新增清场记录弹窗
                $('#L_ShowOrderClearRec').html("清场记录新增");
                $('#txtAEFlag').val("16");

                $("#txtCRNo").val("系统自动产生");
                $("#txtCROrderNo").val("");
                $("#txtCRMaterNo").val("");
                $("#txtCRMaterSpec").val("");
                $("#txtCRStatus").val("");

                $("#txtCRCPNo").val("");
                $("#txtCRCPVer").val("");
                $("#txtCRDeptName").val("");
                $("#txtCRGroupName").val("");
                $("#txtCRInMan").val($('#txtInMan').val());

                var date = new Date();
                var datetime = date.toLocaleString(); // 获取本地时间
                $("#txtCRInDate").val(datetime);

                //$("#txtCRInDate").val("");

                $("#div_warning").html("");
                $("#div_warning").hide();

                //ShowOrderClearRecDetail("");
                ShowClearPlanDetailAdd("", "");

                document.getElementById('OrderBut_open').style.display = 'block';
                document.getElementById('btn_CRCP_open').style.display = 'block';
                document.getElementById('btn_DeptInfo_open').style.display = 'block';
                document.getElementById('div_OrderClearRecDetail').style.display = 'block';
                document.getElementById('btn_OrderClearRecDetailSave').style.display = 'block';
                document.getElementById('div_OrderClearRecDetailListExe').style.display = 'none';
                document.getElementById('div_OrderClearRecDetailListDetail').style.display = 'block';
                $("#div_ClearRecPDFList").hide()
                document.getElementById('fade').style.display = 'block';
            }
            else if (n == 2)//选择工单信息弹窗层
            {
                $('#L_ShowSelectOrderInfo').html("选择工单信息");
                $('#txtAEFlag').val("0");

                layui.use('table', function () {
                    var table = layui.table,
                        form = layui.form;
                    table.render({
                        elem: '#table__OrderInfoSelectList',
                        id: 'MaterID',
                        url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=150',
                        height: '400',
                        cellMinWidth: 80,
                        count: 50, //数据总数 服务端获得
                        limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                        limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                        cols: [[
                            { type: 'numbers' },
                            { field: 'OrderNo', title: '工单编号', width: 150 },
                            { field: 'MaterNo', title: '物料编码', width: 150 },
                            { field: 'MaterName', title: '物料描述', width: 200 },
                            { field: 'MaterSpec', title: '型号', width: 90 },
                            { field: 'DeptNo', title: '部门', width: 90 },
                            { field: 'BOMNo', title: 'BOM版本', width: 90 },
                            { field: 'RequireDate', title: '需求时间', width: 200 },
                            { field: 'PlanStartDate', title: '计划开始时间', width: 200 },
                            { field: 'PlanEndDate', title: '计划结束时间', width: 200 },
                            { field: 'OrderNum', title: '工单数量', minWidth: 80 },
                            { field: 'Unit', title: '单位', width: 50 },
                            { field: 'OrderKind', title: '工单类型', width: 80 },
                            { field: 'OPStatus', title: '启用状态', width: 90 },
                            { field: 'PrdctStatus', title: '执行状态', width: 90 },
                            /* { field: 'OrderType', title: '工单属性', width: 80 },*/
                            //{ field: 'Remark', title: '备注', width: 150 },
                            //{ field: 'InMan', title: '录入人', width: 110 },
                            //{ field: 'InDate', title: '录入时间', width: 150 },
                            { field: 'op', title: '操作', width: 60, toolbar: '#barDemo_OrderDetail', fixed: 'right' }
                        ]],
                        page: true,
                        even: true
                    });

                    //监听行工具事件
                    table.on('tool(table__OrderInfoSelectList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                        var data = obj.data, //获得当前行数据
                            layEvent = obj.event; //获得 lay-event 对应的值

                        if (layEvent == 'OrderSelect') {

                            $('#txtCROrderNo').val(data.OrderNo);
                            $('#txtCRMaterNo').val(data.MaterNo);
                            $('#txtCRMaterSpec').val(data.MaterSpec);
                            $('#txtCRMaterName').val(data.MaterName);

                            // ShowOrderRelevInfo(data.OrderNo, "", data.MaterNo);
                            closeDialog(1);

                        }

                    });

                    //  查询工单选择弹窗界面- 工单信息
                    $('#OrderInfBut_open').click(function () {

                        var sSNo = $("#txtSOrderNo").val();  //工单或产品编码
                        var sBDate = "";  //
                        var sEDate = ""; //
                        var sMNoe = $("#txtSMaterNo").val();;  //
                        var sMName = $("#txtSMaterSpec").val();; //

                        var Data = '';
                        var Params = { No: "", Item: "", Name: "", MNo: sMNoe, MName: sMName, Status: "", BDate: "", EDate: "", A: sSNo, B: "", C: "", D: "" };
                        var Data = JSON.stringify(Params);

                        table.reload('MaterID', {
                            method: 'post',
                            url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=150&Data=' + Data,
                            where: {
                                'No': sSNo,
                                'name': sMNoe
                            }, page: {
                                curr: 1
                            }
                        });
                    });


                });


                /*table__OrderInfoSelectList*/

                $("#div_warningSelectOrderInfo").html("");
                $("#div_warningSelectOrderInfo").hide();

                document.getElementById('div_OrderInfoSelect').style.display = 'block';
                document.getElementById('fade_OrderClearRecDetail').style.display = 'block';
            }
            else if (n == 3)//选择清场计划弹窗层
            {
                $('#txtK').html("选择清场计划");
                $('#txtAEFlag').val("0");

                layui.use('table', function () {
                    var table = layui.table,
                        form = layui.form;
                    table.render({
                        elem: '#table_ClearPlanSelectList',
                        id: 'ClearPlanSelectListID',
                        url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=13',
                        height: '400',
                        cellMinWidth: 80,
                        //toolbar: 'default',//工具栏
                        //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                        count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                        limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                        limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                        cols: [[
                            { type: 'numbers' },
                            { field: 'CPNo', title: '清场计划编号', width: 200 },
                            { field: 'CPVer', title: '版本', width: 100 },
                            //{ field: 'ItemNo', title: '清场项目编号', width: 200 },
                            //{ field: 'CPTxt', title: '清场项目描述', width: 200 },
                            //{ field: 'Type', title: '类型', width: 200 },
                            { field: 'Remark', title: '备注', width: 300 },
                            { field: 'InMan', title: '创建人', width: 90 },
                            { field: 'InDate2', title: '创建时间', width: 200 },
                            { field: 'op', title: '操作', width: 100, toolbar: '#barDemo_CPDetail', fixed: 'right' }
                        ]],
                        page: true,


                    });


                    //监听行工具事件
                    table.on('tool(table_ClearPlanSelectList)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                        var data = obj.data, //获得当前行数据
                            layEvent = obj.event; //获得 lay-event 对应的值
                        if (layEvent === 'CPSelect') {//选择清场计划后，同步保存清场内容清单

                            ShowClearPlanDetailAdd(data.CPNo, data.CPVer);
                            $("#txtCRCPNo").val(data.CPNo);
                            $("#txtCRCPVer").val(data.CPVer);
                            document.getElementById('div_ClearPlanInfoSelect').style.display = 'none';
                            document.getElementById('fade_OrderClearRecDetail').style.display = 'none';

                        }
                        else if (layEvent === 'CPDetail')//显示明细页面
                        {
                            $('#L_ShowClearPlanInfo').html("清场计划信息详情");
                            //$('#txtAEFlag').val("11");

                            $("#txtItemCPDNo").val(data.CPNo);
                            $("#txtItemCPDVer").val(data.CPVer);


                            $("#").html("");
                            $("#div_warningDetail").hide();
                            //弹窗显示清场计划明细页
                            ShowClearPlanDetailList(data.CPNo, data.CPVer);

                            document.getElementById('div_ClearPlanDetail').style.display = 'block';
                            document.getElementById('fade').style.display = 'block';

                            // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                        }

                    });
                    //  查询清场计划
                    $('#ClearPlanBut_open').click(function () {

                        var sCPNo = $("#txtSCPNo").val();  //
                        var sCPVer = encodeURI($("#txtSCPVer").val());  //


                        var Data = '';
                        var Params = { No: sCPNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: sCPVer, B: "", C: "", D: "", E: "" };
                        var Data = JSON.stringify(Params);

                        table.reload('ClearPlanSelectListID', {
                            method: 'post',
                            url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=13&Data=' + Data,
                            where: {
                                'No': sCPNo,
                                'name': sCPVer
                            }, page: {
                                curr: 1
                            }
                        });
                    });


                });

                $("#div_warningClearPlan").html("");
                $("#div_warningClearPlan").hide();

                document.getElementById('div_ClearPlanInfoSelect').style.display = 'block';
                document.getElementById('fade_OrderClearRecDetail').style.display = 'block';
            }
            else if (n == 4)//选择部门弹窗层
            {
                layui.use('table', function () {
                    var table = layui.table,
                        form = layui.form;
                    table.render({
                        elem: '#table_SelectDeptInfo',
                        id: 'SelectDeptInfoID',
                        url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=12',//初始化界面查询对应的存过程标记信息，获取部门信息
                        height: '300',
                        cellMinWidth: 80,
                        //toolbar: 'default',//工具栏
                        //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                        count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                        limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                        limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                        cols: [[
                            { type: 'numbers' },
                            { field: 'DeptNo', title: '部门编号', width: 120 },
                            { field: 'DeptName', title: '部门名称', width: 200 },
                            { field: 'DeptAddr', title: '部门地址', width: 200 },
                            { field: 'Remark', title: '备注', width: 300 },
                            { field: 'op', title: '操作', width: 120, toolbar: '#barDemo_SelectDeptInfo', fixed: 'right' }
                        ]],
                        page: true,


                    });

                    //监听行工具事件
                    table.on('tool(table_SelectDeptInfo)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                        var data = obj.data, //获得当前行数据
                            layEvent = obj.event; //获得 lay-event 对应的值
                        if (layEvent === 'slc') {

                            //$("#txtDeptNo").val(data.DeptNo);
                            $("#txtCRDeptName").val(data.DeptName);

                            /* $("#txtWNo").attr({ "disabled": "disabled" });*/

                            $("#div_warningDetail").html("");
                            $("#div_warningDetail").hide();

                            closeDialog(1);
                        }

                    });


                    //  查询 --
                    $('#btn_DeptInfo_open').click(function () {

                        var sWNo = $("#txtSWNo").val();  //
                        var sName = encodeURI($("#txtSWDesc").val());  //

                        var Data = '';
                        var Params = { No: sWNo, Name: sName, Item: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "", E: "" };
                        var Data = JSON.stringify(Params);

                        table.reload('SelectDeptInfoID', {
                            method: 'post',
                            url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=12&Data=' + Data,
                            where: {
                                'No': sWNo,
                                'name': sName
                            }, page: {
                                curr: 1
                            }
                        });
                    });


                });   // table 方法体

                //监听工具条
                document.getElementById('div_selectDeptInfo').style.display = 'block';
                document.getElementById('fade_OrderClearRecDetail').style.display = 'block';
            }

        }


        function closeDialog(s) {
            if (s == 1)//关闭选择工单弹层页面
            {
                document.getElementById('div_OrderInfoSelect').style.display = 'none';
                document.getElementById('div_ClearPlanInfoSelect').style.display = 'none';
                document.getElementById('fade_OrderClearRecDetail').style.display = 'none';
                document.getElementById('div_selectDeptInfo').style.display = 'none';
            }
            else if (s == 2)//关闭详细页面
            {
                document.getElementById('div_OrderClearRecDetail').style.display = 'none';
                document.getElementById('div_ClearPlanInfoSelect').style.display = 'none';
                document.getElementById('div_OrderInfoSelect').style.display = 'none';
                document.getElementById('fade').style.display = 'none';
                document.getElementById('fade_OrderClearRecDetail').style.display = 'none';

                $('#OrderClearRecBut_open').click();  // 重新查询
            }
            else {
                document.getElementById('div_ClearPlanDetail').style.display = 'none';
            }
        }

        //自动更新单据状态
        function GetPrdAssistStatusForNoet(sENo) {
            //var txtENo = $("#txtENo").val();
            sURL = '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistStatusForNo&CFlag=65&CNO=' + sENo;
            var Data = '';
            $.ajax({
                url: sURL,
                data: { Data: Data },
                success: function (data) {                    //数据成功读取后的处理

                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.length > 0) {
                        //sCompNo = parsedJson[0].CompanyNo;
                        //sCompName = parsedJson[0].CompName;
                        $("#txtCRStatus").val(parsedJson[0].Status);
                        //$("#txtFWName").val(parsedJson[0].CompName);
                    }
                },
                error: function (xhr, status, error) {
                    //错误处理
                    $("body").append("<div>读取数据失败：" + error + "</div>");
                }
            });
        }

        /// 执行清场记录
        ///daiwanshan
        function ShowOrderClearRecExe(sCRNo) {

            var Data = '';
            var Params = { No: sCRNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#table_OrderClearRecDetailListExe',
                    id: 'OrderClearRecDetailExeID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=14-1&Data=' + Data,
                    height: '260',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers' },
                        { field: 'ItemNo', title: '序号', width: 40 },
                        { field: 'CPTxt', title: '清场内容', width: 230 },
                        { field: 'Result', title: '结果', width: 100 },
                        //{ field: 'InMan', title: '录入人', minWidth: 90 },
                        //{ field: 'InDate2', title: '录入时间', width: 150 },
                        { field: 'op', title: '操作', width: 80, toolbar: '#bar_OrderClearPlanRocExe', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(table_OrderClearRecDetailListExe)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值

                    if (layEvent == 'ClearCheck') {
                        if (data.Result == 'OK') {
                            layer.msg('已清场确认完成，不必重复操作！');
                            return;
                        }
                        layer.confirm('真的要完成该清场内容了吗', function (index) {
                            //向服务端发送清场确认指令 sNo: CRNo,Item:ItemNo, A:sResult

                            var sNo = $("#txtCRNo").val();
                            var sItemNo = data.ItemNo;
                            var sResult = "OK"


                            var sFlag = "17";

                            var Data = '';
                            var Params = { No: sNo, Name: "", Item: sItemNo, MNo: "", MName: "", A: sResult, B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: sFlag };
                            var Data = JSON.stringify(Params);

                            $.ajax({
                                type: "POST",
                                url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
                                data: { Data: Data },
                                success: function (data) {
                                    var parsedJson = jQuery.parseJSON(data);

                                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                        layer.msg('清场确认操作成功！');
                                        GetPrdAssistStatusForNoet(sNo);
                                        ShowOrderClearRecExe(sNo);//重新显示清场内容清单
                                        //$('#Fin_SearchOpen').click();
                                        //obj.del(); //删除对应行（tr）的结构，并更新缓存
                                        layer.close(index);
                                    }
                                    else {
                                        layer.msg('清场确认操作失败，请重试！');
                                        // $('#Fin_SearchOpen').click();
                                    }
                                },
                                error: function (data) {
                                    layer.msg('清场确认操作失败2，请重试！');
                                    // $('#Fin_SearchOpen').click();
                                }
                            });

                        }); // 删除
                    }
                });

            });  // layui.use('table', function () {
            document.getElementById('div_OrderClearRecDetailListExe').style.display = 'block';
            document.getElementById('div_OrderClearRecDetailListDetail').style.display = 'none';

        }


        /// 显示清场记录明细项信息
        ///daiwanshan
        function ShowOrderClearRecDetail(sCRNo) {

            var Data = '';
            var Params = { No: sCRNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#table_OrderClearRecDetailListDetail',
                    id: 'OrderClearRecDetailID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=14-1&Data=' + Data,
                    height: '260',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers' },
                        { field: 'ItemNo', title: '序号', width: 40 },
                        { field: 'CPTxt', title: '清场内容', width: 230 },
                        { field: 'Result', title: '结果', width: 100 },
                        //{ field: 'InMan', title: '录入人', minWidth: 90 },
                        //{ field: 'InDate2', title: '录入时间', width: 150 },
                        /*{ field: 'op', title: '操作', width: 80, toolbar: '#bar_OrderClearPlanRocDetail', fixed: 'right' }*/
                    ]],
                    page: true
                });

            });  // layui.use('table', function () {

            document.getElementById('div_OrderClearRecDetailListExe').style.display = 'none';
            document.getElementById('div_OrderClearRecDetailListDetail').style.display = 'block';

        }

        /// 新增页面显示清场计划明细项信息
        ///daiwanshan
        function ShowClearPlanDetailAdd(sCPNo, sCPVer) {

            var Data = '';
            var Params = { No: sCPNo, CPVer: sCPVer, Item: "", Status: "", BDate: "", EDate: "", A: sCPVer, B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#table_OrderClearRecDetailListDetail',
                    id: 'ClearPlanDetailAddID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=13-1&Data=' + Data,
                    height: '400',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers' },
                        { field: 'SNO', title: '序号', width: 40 },
                        { field: 'ItemNo', title: '内容编号', width: 100 },
                        { field: 'CPTxt', title: '清场内容', width: 230 },
                        { field: 'Type', title: '数据类型', width: 100 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate2', title: '录入时间', width: 150 }
                        //{ field: 'op', title: '操作', width: 230, toolbar: '#barDemo_Detail', fixed: 'right' }
                    ]],
                    page: true
                });

            });  // layui.use('table', function () {


        }

        /// 显示清场计划明细项信息
        ///daiwanshan
        function ShowClearPlanDetailList(sCPNo, sCPVer) {

            var Data = '';
            var Params = { No: sCPNo, CPVer: sCPVer, Item: "", Status: "", BDate: "", EDate: "", A: sCPVer, B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#ClearPlanDetailList',
                    id: 'ClearPlanDetailID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=13-1&Data=' + Data,
                    height: '400',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers' },
                        { field: 'SNO', title: '序号', width: 40 },
                        { field: 'ItemNo', title: '内容编号', width: 100 },
                        { field: 'CPTxt', title: '清场内容', width: 230 },
                        { field: 'Type', title: '数据类型', width: 100 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate2', title: '录入时间', width: 150 }
                        //{ field: 'op', title: '操作', width: 230, toolbar: '#barDemo_Detail', fixed: 'right' }
                    ]],
                    page: true
                });

            });  // layui.use('table', function () {


        }

        //清场记录生成PDF
        function ClearRecPDF() {
            var sCRNo = $("#txtCRNo").val()
            var sCROrderNo = $("#txtCROrderNo").val()
            var Params = { No: sCRNo, Name: sCROrderNo, Item: "", Status: "", A: "CleanRecord", B: "", C: "", D: "", Remark: "", Flag: "100" };
            var Data = JSON.stringify(Params);

            $("#ClearRecPDF").attr("disabled", true)

            $.ajax({
                url: "../Service/PrdAssistAjax.ashx?OP=GenerateRecordFile",
                type: "POST",
                data: {
                    Data: Data
                },
                success: function (res) {
                    var parsedJson = jQuery.parseJSON(res);
                    if (parsedJson.Msg == "LoginError") {
                        ErrorMessage("登录超时，请重新登录！", 2000);
                        $("#ClearRecPDF").removeAttr("disabled")
                    } else if (parsedJson.Msg == "ParamsError") {
                        ErrorMessage("请求参数出错，请重试！", 2000);
                        $("#ClearRecPDF").removeAttr("disabled")
                    } else if (parsedJson.Msg == "Success") {  //如过是Success没有生成过就直接生成
                        ClearRecCreatePDF()
                    } else if (parsedJson.Msg == "Y_EXIST") {  //如过是Y_EXIST就说明已经生成过了一次则提示一下是否再次生成PDF
                        layer.confirm('该编号已经生成过，确定再次生成吗？', {
                            btn: ['确定', '取消'], // 按钮
                            cancel: function () {
                                $("#ClearRecPDF").removeAttr("disabled")
                            }
                        }, function () {
                            ClearRecCreatePDF();
                        }, function () {
                            $("#ClearRecPDF").removeAttr("disabled")
                        });
                    }
                },
                error: function () {
                    ErrorMessage("系统出错，请重试！", 2000);
                    $("#ClearRecPDF").removeAttr("disabled")
                    return;
                }
            })
        }

        //清场记录生成PDF
        function ClearRecCreatePDF() {
            var sCRNo = $("#txtCRNo").val()
            var sCROrderNo = $("#txtCROrderNo").val()
            var Params = { No: sCRNo, Name: sCROrderNo, Item: "", Status: "", A: "", B: "", C: "", D: "", Remark: "", Flag: "QC" };
            var Data = JSON.stringify(Params);
            if (sCRNo == "") {
                ErrorMessage("获取不到清场记录编号", 2000);
                return;
            }
            if (sCROrderNo == "") {
                ErrorMessage("获取不到工单编号", 2000);
                return;
            }

            $("#ClearRecPDF").attr("disabled", true)

            $.ajax({
                url: "../Service/PrdAssistAjax.ashx?OP=GenerateRecordFile",
                type: "POST",
                data: {
                    Data: Data
                },
                success: function (res) {
                    var parsedJson = jQuery.parseJSON(res);
                    if (parsedJson.Msg == "ImportError") {
                        ErrorMessage("模板导入失败，请重试！", 2000);
                    } else if (parsedJson.Msg == "CreateError") {
                        ErrorMessage("生成失败！", 2000);
                    } else if (parsedJson.Msg == "Error") {
                        ErrorMessage("生成失败！" + parsedJson.ExceptionMessage, 2000);
                    } else if (parsedJson.Msg == "Success") {
                        layer.msg("生成成功")
                        GetClearRecPDF();
                    }
                    $("#ClearRecPDF").removeAttr("disabled")
                },
                error: function () {
                    ErrorMessage("系统出错，请重试！", 2000);
                    $("#ClearRecPDF").removeAttr("disabled")
                    return;
                }
            })
        }

        function GetClearRecPDF() {
            var sCRNo = $("#txtCRNo").val()
            var Data = '';
            var Params = { No: sCRNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: "CleanRecord", B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);
            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#ClearRecPDFList',
                    id: 'ClearRecPDFListID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=28&Data=' + Data,
                    height: '280',
                    cellMinWidth: 80,
                    count: 50,
                    limit: 20,
                    limits: [10, 20, 30, 40, 50],
                    cols: [[
                        { type: 'numbers' },
                        { field: 'No', title: '编号', width: 200 },
                        { field: 'Name', title: '名称' },
                        { field: 'InMan', title: '录入人', width: 100 },
                        { field: 'InDate2', title: '录入时间', width: 170 },
                        { field: 'op', title: '操作', width: 100, toolbar: '#bar_ClearRecPDFList', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(ClearRecPDFList)', function (obj) {
                    var data = obj.data,
                        layEvent = obj.event;
                    if (layEvent == 'view') {
                        window.open(data.FilePath)
                    }
                })
            })
        }

    </script>


    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

        })



    </script>

    <style type="text/css">

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: white;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=60);
        }
    </style>

</head>
<body>
    <div class="div_find">

        <p>
            <label class="find_labela">清场记录编号：</label> <input type="text" id="txtSCRNo" class="find_input" />
            <label class="find_labela">工单号：</label><input type="text" id="txtSCROrderNo" class="find_input" />
            <!--<label class="find_labela">描述：</label><input type="text" id="txtSCPTxt" class="find_input" />-->
            <input type="button" value="搜索" class="find_but" id="OrderClearRecBut_open">
        </p>
    </div>
    <div style="text-align:right">
        <i class="add2_i"></i><a href="JavaScript:void(0)" onclick="openDialog(1)" style="color: Blue">添加</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <!-- <i class="down_i" ></i><a href="JavaScript:void(0)" onclick="openDialog(1)" class="add_a">导出</a> -->
    </div>

    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="OrderClearReclist" lay-filter="OrderClearReclist"></table>

        <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-xs" lay-event="exe">执行</a>
            <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
            <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
        </script>

    </div>
    <!--清场记录详情弹窗层-->
    <div id="div_OrderClearRecDetail" style="display: none;position: absolute;top: 1%;left: 5%;right: 5%;width: 90%;height: 660px;border: 2px solid #21b6b4; background-color: white;z-index: 1002;overflow: auto;">
        <div style="background-color:#26d0a1; height:40px;">
            <label id="L_ShowOrderClearRec" style=" padding:5px;font-size: 14px; color:White; ">清场记录详情</label>
            <label onclick="closeDialog(2)" style="float:right; margin:5px 5px 0 0; cursor:pointer;">X</label>
        </div>
        <div id="div_warning" role="alert" style="text-align: center; display:none;color: Red ">
            <strong id="divsuccess" style="color: Red"></strong>
        </div>
        <div style=" font-weight: bold; padding: 5px; height: 35px; line-height: 25px; margin-top: 5px; border: 0px; border-bottom: solid 1px #ccc;">
            清场记录基本信息
        </div>
        <table cellspacing="0" cellpadding="0" border='0' style="width:99%; ">

            <tr style=" height:40px;">
                <td style=" width:100px; text-align:right;">
                    编号:
                </td>
                <td colspan="2">
                    <input type="text" class="form-control" id="txtCRNo" name="txtCRNo" readonly=readonly placeholder="系统自动产生" style="height:30px;" />
                </td>
                <td style=" width:100px; text-align:right;">
                    工单:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtCROrderNo" name="txtCROrderNo" readonly=readonly style="height:30px;" />
                </td>
                <td style=" width:20px; text-align:left;">
                    <input type="button" value="选择" class="find_but" id="OrderBut_open" onclick="openDialog(2)" style="height:30px;" />
                </td>
                <td style=" width:100px; text-align:right;">
                    型号:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtCRMaterSpec" name="txtCRMaterSpec" readonly=readonly style="height:30px;" />
                </td>
                <td style=" width:100px; text-align:right;">
                    物料编码:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtCRMaterNo" name="txtCRMaterNo" readonly=readonly style="height:30px;" />
                </td>
            </tr>

            <tr style=" height:40px;">
                <td style=" width:100px; text-align:right;">
                    部门:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtCRDeptName" name="txtCRDeptName" readonly=readonly style="height:30px;" />
                </td>
                <td style=" width:20px; text-align:left;">
                    <input type="button" value="选择" class="find_but" id="btn_DeptInfo_open" onclick="openDialog(4)" style="height:30px;" />
                </td>
                <td style=" width:100px; text-align:right;">
                    组别:
                </td>
                <td colspan="2">
                    <input type="text" class="form-control" id="txtCRGroupName" name="txtCRGroupName" style="height:30px;" /><!--readonly=readonly-->
                </td>
                <td style=" width:100px; text-align:right;">
                    创建人:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtCRInMan" name="txtCRInMan" readonly=readonly style="height:30px;" />
                </td>
                <td style=" width:20px; text-align:right;">
                    创建日期:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtCRInDate" name="txtCRInDate" readonly=readonly style="height:30px;" />
                </td>
            </tr>

            <tr>
                <td style=" width:40px; text-align:right;">
                    清场计划:
                </td>
                <td style=" width:150px; text-align:right;">
                    <input type="text" class="form-control" id="txtCRCPNo" name="txtCRCPNo" readonly=readonly style="height:30px;" />
                </td>
                <td style=" width:20px; text-align:left;">
                    <input type="button" value="选择" class="find_but" id="btn_CRCP_open" onclick="openDialog(3)" style="height:30px;" />
                </td>
                <td style=" width:20px; text-align:right;">
                    版本:
                </td>
                <td colspan="2" style=" width: 100px; text-align:right;">
                    <input type="text" class="form-control" id="txtCRCPVer" name="txtCRCPVer" readonly=readonly style="height:30px;" />
                </td>
                <td style=" width:20px; text-align:right;">
                    状态:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtCRStatus" name="txtCRStatus" readonly=readonly style="height:30px;" />
                </td>
                <td>
                    <input type="text" class="form-control" id="txtCRMaterName" name="txtCRMaterName" readonly=readonly style="height: 30px; display: none;" />
                </td>
            </tr>
        </table>

        <br />

        <div align="center">
            <table cellspacing="0" cellpadding="0" border='0' style="width:10%; ">
                <tr>
                    <td>
                        <input type='button' id="btn_OrderClearRecDetailSave" value='保存' class="find_but" style="width: 50px; height: 30px;" />
                    </td>
                    <td>
                        <input type='button' id="btn_OrderClearRecDetailClose" value='关闭' class="find_but" onclick='closeDialog(2)' style="width: 50px; height: 30px;" />
                    </td>
                </tr>
            </table>

        </div>

        <div style=" font-weight: bold; padding: 5px; height: 35px; line-height: 25px; margin-top: 5px; border: 0px; border-bottom: solid 1px #ccc;">
            清场记录内容列表
        </div>

        <div id="div_OrderClearRecDetailListExe" class="wangid_conbox" style="display:none; ">
            <!-- 下面写内容   -->
            <table class="layui-hide" id="table_OrderClearRecDetailListExe" lay-filter="table_OrderClearRecDetailListExe"></table>

            <script type="text/html" id="bar_OrderClearPlanRocExe">
                <a class="layui-btn layui-btn-xs" lay-event="ClearCheck">清场确认</a>
            </script>
        </div>
        <div id="div_OrderClearRecDetailListDetail" class="wangid_conbox" style="display:none; ">
            <!-- 下面写内容   -->
            <table class="layui-hide" id="table_OrderClearRecDetailListDetail" lay-filter="table_OrderClearRecDetailListDetail"></table>
        </div>
        <div class="wangid_conbox" id="div_ClearRecPDFList">
            <div style="display:flex;justify-content:space-between;border-bottom: solid 1px #ccc;padding-bottom:3px;margin-top:15px">
                <span style="font-weight: bold; ">清场记录文件</span>
                <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="ClearRecPDF" onclick="ClearRecPDF()" style="width:70px">生成PDF</button>
            </div>
            <!-- 下面写内容   -->
            <table class="layui-hide" id="ClearRecPDFList" lay-filter="ClearRecPDFList"></table>

            <script type="text/html" id="bar_ClearRecPDFList">
                <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="view">查看</button>
            </script>

        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>
        <div id="fade_OrderClearRecDetail" class="black_overlay"> </div>
    </div>


    <!--选择清场计划弹窗-->
    <div id="div_ClearPlanInfoSelect" style="display: none;position: absolute;top: 1%;left: 5%;right: 2%;width: 90%;height: 700px;border: 2px solid #21b6b4; background-color: white;z-index: 1002;overflow: auto;">
        <div style="background-color:#26d0a1; height:40px;">
            <label id="L_ShowClearPlanInfo" style=" padding:5px;font-size: 14px; color:White; ">清场计划搜索</label>
            <label onclick="closeDialog(2)" style="float:right; margin:5px 5px 0 0; cursor:pointer;">X</label>
        </div>
        <div id="div_warningSelectClearPlanInfo" role="alert" style="text-align: center; display:none;color: Red ">
            <strong id="divsuccessSelectClearPlanInfo" style="color: Red"></strong>
        </div>
        <div style=" font-weight: bold; padding: 5px; height: 35px; line-height: 25px; margin-top: 5px; border: 0px; border-bottom: solid 1px #ccc;">
            查询
        </div>

        <div class="div_find">

            <p>
                <label class="find_labela">清场计划编号：</label> <input type="text" id="txtSCPNo" class="find_input" />
                <label class="find_labela">版本号：</label><input type="text" id="txtSCPVer" class="find_input" />
                <!--<label class="find_labela">描述：</label><input type="text" id="txtSCPTxt" class="find_input" />-->
                <input type="button" value="搜索" class="find_but" id="ClearPlanBut_open">
            </p>
        </div>
        <div align="center">
            <input type="button" value="关闭" class="find_but" id="btn_SelectClearPlanInfoClose" onclick="closeDialog(1)" style="height:30px;" />
        </div>
        <br />

        <div style=" font-weight: bold; padding: 5px; height: 35px; line-height: 25px; margin-top: 5px; border: 0px; border-bottom: solid 1px #ccc;">
            清场计划列表
        </div>

        <div class="wangid_conbox">
            <!-- 下面写内容   -->
            <table class="layui-hide" id="table_ClearPlanSelectList" lay-filter="table_ClearPlanSelectList"></table>

            <script type="text/html" id="barDemo_CPDetail">
                <a class="layui-btn layui-btn-xs" lay-event="CPDetail">详情</a>
                <a class="layui-btn layui-btn-xs" lay-event="CPSelect">选择</a>
            </script>

        </div>

    </div>

    <!--选择订单信息弹窗-->
    <div id="div_OrderInfoSelect" style="display: none;position: absolute;top: 1%;left: 5%;right: 2%;width: 90%;height: 650px;border: 2px solid #21b6b4; background-color: white;z-index: 1002;overflow: auto;">
        <div style="background-color:#26d0a1; height:40px;">
            <label id="L_ShowSelectOrderInfo" style=" padding:5px;font-size: 14px; color:White; ">工单搜索</label>
            <label onclick="closeDialog(1)" style="float:right; margin:5px 5px 0 0; cursor:pointer;">X</label>
        </div>
        <div id="div_warningSelectOrderInfo" role="alert" style="text-align: center; display:none;color: Red ">
            <strong id="divsuccessSelectOrderInfo" style="color: Red"></strong>
        </div>
        <div style=" font-weight: bold; padding: 5px; height: 35px; line-height: 25px; margin-top: 5px; border: 0px; border-bottom: solid 1px #ccc;">
            查询
        </div>

        <div class="div_find">
            <p>
                <label class="find_labela">工单编号：</label> <input type="text" id="txtSOrderNo" class="find_input" />
                <label class="find_labela">物料编码：</label><input type="text" id="txtSMaterNo" class="find_input" />
                <label class="find_labela">物料描述：</label><input type="text" id="txtSMaterSpec" class="find_input" />

                <input type="button" value="搜索" class="find_but" id="OrderInfBut_open">
            </p>
        </div>
        <div align="center">
            <input type="button" value="关闭" class="find_but" id="btn_SelectOrderInfoClose" onclick="closeDialog(1)" style="height:30px;" />
        </div>

        <div style=" font-weight: bold; padding: 5px; height: 35px; line-height: 25px; margin-top: 5px; border: 0px; border-bottom: solid 1px #ccc;">
            工单信息列表
        </div>


        <div class="wangid_conbox">
            <!-- 下面写内容   -->
            <table class="layui-hide" id="table__OrderInfoSelectList" lay-filter="table__OrderInfoSelectList"></table>

            <script type="text/html" id="barDemo_OrderDetail">
                <!--<a class="layui-btn layui-btn-xs" lay-event="OrderDetail">详情</a>-->
                <a class="layui-btn layui-btn-xs" lay-event="OrderSelect">选择</a>
            </script>

        </div>

    </div>

    <!--弹窗选择部门-->
    <div id="div_selectDeptInfo" style="display: none;position: absolute;top: 1%;left: 5%;right: 5%;width: 90%;height: 660px;border: 2px solid #21b6b4; background-color: white;z-index: 1002;overflow: auto;">
        <div id="div_selectDeptInfoHead" style="background-color: #26d0a1; height: 40px;">
            <!--<label id="label_ShowDeviceInfo" style=" padding:10px;font-size: 14px; color:White; "></label>-->
            <label id="label_ShowDeptInfo" style=" padding:5px;font-size: 14px; color:White; ">选择部门信息</label>
            <label onclick="closeDialog(1)" style="float:right; margin:5px 5px 0 0; cursor:pointer;">X</label>
        </div>
        <div id="div_warningDeptInfo" role="alert" style="text-align: center; display:none;color: Red ">
            <strong id="divsuccessDeptInfo" style="color: Red"></strong>
        </div>

        <div class="div_find2">
            <p>
                <label class="find_labela">部门编号：</label> <input type="text" id="txtSWNo" class="find_input" />
                <label class="find_labela">部门名称：</label><input type="text" id="txtSWDesc" class="find_input" />
                <input type="button" value="搜索" class="find_but" id="btn_DeptInfo_open">
            </p>
        </div>

        <div style=" font-weight: bold; padding: 5px; height: 35px; line-height: 25px; margin-top: 5px; border: 0px; border-bottom: solid 1px #ccc;">
            基本信息
        </div>
        <div class="wangid_conbox">
            <!-- 下面写内容   -->
            <table class="layui-hide" id="table_SelectDeptInfo" lay-filter="table_SelectDeptInfo"></table>

            <script type="text/html" id="barDemo_SelectDeptInfo">
                <a class="layui-btn layui-btn-xs" lay-event="slc">选择</a>
            </script>
        </div>

    </div>


    <!--清场计划内容清单弹窗-->
    <div id="div_ClearPlanDetail" style="display: none;position: absolute;top: 1%;left: 2%;right: 2%;width: 96%;height: 660px;border: 2px solid #21b6b4; background-color: white;z-index: 1002;overflow: auto;">
        <div style="background-color:#26d0a1; height:40px;">
            <label id="L_ShowClearPlanDetail" style=" padding:5px;font-size: 14px; color:White; ">清场计划详情</label>
            <label onclick="closeDialog(3)" style="float:right; margin:5px 5px 0 0; cursor:pointer;">X</label>
        </div>
        <div id="div_warningDetail" role="alert" style="text-align: center; display:none;color: Red ">
            <strong id="divsuccessDetail" style="color: Red"></strong>
        </div>
        <div style=" font-weight: bold; padding: 5px; height: 35px; line-height: 25px; margin-top: 5px; border: 0px; border-bottom: solid 1px #ccc;">
            清场计划
        </div>
        <table cellspacing="0" cellpadding="0" border='0' style="width:99%; ">

            <tr style=" height:40px;">
                <td style=" width:100px; text-align:right;">
                    清场计划编号:
                </td>
                <td colspan="4">
                    <input type="text" class="form-control" id="txtItemCPDNo" name="txtItemCPDNo" readonly=readonly placeholder="系统自动产生" style="height:30px;" />
                </td>
            </tr>

            <tr style=" height:40px;">
                <td style=" width:100px; text-align:right;">
                    版本:
                </td>
                <td colspan="4">
                    <input type="text" class="form-control" id="txtItemCPDVer" name="txtItemCPDDVer" readonly=readonly style="height:30px;" />
                </td>
            </tr>

            <!--<table cellspacing="0" cellpadding="0" border='0' style="width:99%; ">-->
            <tr>
                <td colspan="4">
                </td>
            </tr>
            <tr style=" height:20px;">
                <td style=" width: 180px; height: 20px; text-align: right;">
                </td>
                <td colspan="3">
                    <!--<div style="text-align:right">
                    <i class="add2_i"></i><a href="JavaScript:void(0)" onclick="openDialog(2)" style="color: Blue">添加</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-->
                    <!-- <i class="down_i" ></i><a href="JavaScript:void(0)" onclick="openDialog(1)" class="add_a">导出</a> -->
                    <!--</div>-->
                </td>
            </tr>
            <tr style=" height:40px;">
                <td colspan="4">
                    <div class="wangid_conbox">
                        <!-- 下面写内容   -->
                        <table class="layui-hide" id="ClearPlanDetailList" lay-filter="ClearPlanDetailList"></table>
                    </div>
                </td>
            </tr>

        </table>

        <div id="ClearPlanDetail_fade" class="black_overlay">
        </div>

    </div>


    <div id="fade" class="black_overlay"> </div>

</body>
</html>