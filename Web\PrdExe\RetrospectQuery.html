﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/XC.css" rel="stylesheet" />
    <!--layui css-->
    <link rel="stylesheet" href="/css/layuiM.css" />
    <!--layui js-->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/Order.js"></script>

    <script>
        layui.use(['table', 'form'], function () {
            var table = layui.table;
            var form = layui.form;

            // 定义每种类型的表头
            var tableConfigs = {
                material: {
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40, },
                        { field: 'OrderNo', title: '工单' },
                        { field: 'BatchNo', title: '序列号' },
                        { field: 'FMaterNo', title: '产品编码' },
                        { field: 'ProcedureName', title: '工序' },
                        { field: 'MaterNo', title: '物料编码' },
                        { field: 'MaterBatchNo', title: '序列号/批次' },
                        { field: 'UseNum', title: '使用数' },
                        { field: 'RUnitNo', title: '作业单元' },
                        { field: 'Status', title: '状态' },
                        { field: 'InMan', title: '操作人' },
                        { field: 'InDate2', title: '操作时间' },
                    ]],
                },
                device: {
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40, },
                        { field: 'OrderNo', title: '工单' },
                        { field: 'BatchNo', title: '序列号' },
                        { field: 'ProductNo', title: '产品编码' },
                        { field: 'ProcedureName', title: '工序' },
                        { field: 'MaterNo', title: '分类编码' },
                        { field: 'DeviceNo', title: '设备编号' },
                        { field: 'DeviceName', title: '设备名称' },
                        { field: 'UseDate', title: '校准有效期' },
                        { field: 'CheckReq', title: '点检要求' },
                        { field: 'Status', title: '状态' },
                        { field: 'RUnitNo', title: '作业单元' },
                        { field: 'InMan', title: '操作人' },
                        { field: 'InDate2', title: '操作时间' },
                    ]],
                },
                person: {
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40, },
                        { field: 'OrderNo', title: '工单' },
                        { field: 'BatchNo', title: '序列号' },
                        { field: 'ProductNo', title: '产品编码' },
                        { field: 'ProcedureName', title: '工序' },
                        { field: 'RUnitNo', title: '作业单元' },
                        { field: 'UserNo', title: '开始作业员', align: 'center' },
                        { field: 'StartDate2', title: '开始时间' },
                        { field: 'EndUserNo', title: '结束作业员', align: 'center' },
                        { field: 'EndDate2', title: '完工时间' },
                        { field: 'Status', title: '状态', align: 'center' },
                    ]],
                },
                unit: {
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40, },
                        { field: 'OrderNo', title: '工单' },
                        { field: 'BatchNo', title: '序列号' },
                        { field: 'ProductNo', title: '产品编码' },
                        { field: 'ProcedureName', title: '工序' },
                        { field: 'RUnitNo', title: '作业单元' },
                        { field: 'UserNo', title: '开始作业员', align: 'center' },
                        { field: 'StartDate2', title: '开始时间' },
                        { field: 'EndUserNo', title: '结束作业员', align: 'center' },
                        { field: 'EndDate2', title: '完工时间' },
                        { field: 'Status', title: '状态', align: 'center' },
                    ]],
                }
            };

            // 定义每种类型需要显示的输入框
            var typeInputMap = {
                material: ['queryInput', 'txtOrderNo', 'txtSerial', 'txtProcedure', 'txtBatchSerial'],
                device: ['queryInput', 'txtOrderNo', 'txtSerial', 'txtProcedure'],
                person: ['queryInput', 'txtOrderNo', 'txtSerial', 'txtProcedure'],
                unit: ['queryInput', 'txtOrderNo', 'txtSerial', 'txtProcedure'],
            };


            // 渲染表格函数
            function renderTableByType() {
                var type = $('#queryType').val();
                var input = $('#queryInput').val();
                var orderNo = $('#txtOrderNo').val();
                var serialNo = $('#txtSerial').val();
                var procedure = $('#txtProcedure').val();
                var batchSerial = $('#txtBatchSerial').val();
                var procNo = ""; 
                if (procedure != "" && procedure != null) {
                    var procNo = procedure.substr(1, procedure.indexOf(")") - 1);  
                }
               

                var Data = '';
                var Params = { No: serialNo, Item: orderNo, Name: type, MNo: "", MName: "", Status: status, BDate: "", EDate: "", A: input, B: procNo, C: batchSerial, D: "" };
                var Data = JSON.stringify(Params);

                var config = tableConfigs[type];
                table.render({
                    elem: '#resultTable',
                    id: "resultTableID",
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=244&Data=' + Data,
                    height: 'full-50',
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                    limit: 20,
                    cols: config.cols,
                    page: true
                });
            }

            // 切换 placeholder 并渲染全部数据
            $('#queryType').on('change', function () {
                var type = $(this).val();
                var placeholder = '';
                switch (type) {
                    case 'material':
                        placeholder = '请输入物料编码';
                        break;
                    case 'device':
                        placeholder = '请输入设备编号';
                        break;
                    case 'person':
                        placeholder = '请输入工号';
                        break;
                    case 'unit':
                        placeholder = '请输入作业单元编号';
                        break;
                }

                $('#queryInput').attr('placeholder', placeholder);
                $('#queryInput, #txtOrderNo, #txtSerial, #txtProcedure ,#txtBatchSerial').hide();
                $('#queryInput, #txtOrderNo, #txtSerial, #txtProcedure ,#txtBatchSerial').val("")


                // 显示需要的
                var showIds = typeInputMap[type] || [];
                showIds.forEach(function (id) {
                    $('#' + id).show();
                });

                renderTableByType();
            });


            // 查询按钮点击
            $('#DHRQuery_open').on('click', function () {
                renderTableByType();
            });

            $('#queryInput,#txtStatus, #txtOrderNo, #txtSerial, #txtProcedure,#txtBatchSerial').hide();

            GetProcedureList()
        });


        function GetProcedureList() {
            var sSs = "工序";
            var keywords = encodeURI(sSs);
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/TechAjax.ashx?OP=GetProcedureList&CFlag=111-1&CKind=" + keywords,
                success: function (data) {
                    var parsedJson = eval(data).Msg;
                    $("#txtProcedure").empty();
                    $("#txtProcedure").append("<option value='' disabled selected hidden>请选择工序</option>");
                    $("#txtProcedure").append(parsedJson);
                }
            });
        }
    </script>

    <style>
        .div_find {
            width: 99%
        }

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
            padding-left: 5px;
        }

        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }

        .dropdown-menu, .filter-option {
            font-size: 12px;
        }
    </style>
</head>

<body>
    <div class="div_find">

        <select id="queryType" class="find_input">
            <option value="" disabled selected hidden>请选择查询类型</option>
            <option value="material">物料</option>
            <option value="device">设备</option>
            <option value="person">人员</option>
            <option value="unit">作业单元</option>
        </select>

        <input id="queryInput" type="text" class="find_input" placeholder="" />

        <input type="text" id="txtOrderNo" class="find_input" placeholder="请输入工单" />

        <input type="text" id="txtSerial" class="find_input" placeholder="请输入序列号" />

        <input type="text" id="txtBatchSerial" class="find_input" placeholder="序列号/批次" />

        <select class="find_input" id="txtProcedure"></select>

        <!--<label class="find_labela">时间</label><input type="datetime-local" id="txtStartDate" class="find_input" style="margin-right:0.5%" /> - <input type="datetime-local" id="txtSEndDate" class="find_input" />-->
        <input type="button" value="搜索" class="XC-Btn-Green XC-Size-xs XC-Btn-md" id="DHRQuery_open" />
    </div>
    <div class="wangid_conbox">
        <table class="layui-hide" id="resultTable" lay-filter="resultTable"></table>
    </div>


</body>

</html>