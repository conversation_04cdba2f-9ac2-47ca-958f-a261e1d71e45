﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>仓库信息</title>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <!-- layui css -->

    <link rel="stylesheet" href="../layui/css/layui.css">
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <!--<script src="../js/BaseModule.js" type="text/javascript"></script>-->
    <script src="../js/PrdAssist.js" type="text/javascript"></script>

    <link href="../css/XC.css" rel="stylesheet" />

    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=23&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=23&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        //$('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=25-1' });
                    }
                }
            })
        });



    </script>


    <script type="text/javascript">

        var sURL = "../Service/PrdAssistAjax.ashx?OP=GetWareHouseInfo&CFlag=18"; // OP=GetBOMInfo&CFlag=44";

        layui.config({
            base: 'module/'    /* 修改为你module文件夹地址*/
        }).extend({
            tableEdit: '../js/tableEdit',  //表格树依赖我另外写的tableEdit模块，本项目就有。  -- 没有用到
            tableTree: '../js/tableTree'   // -- 没有用到
        }).use(['table', 'treetable'], function () {
            var $ = layui.jquery;
            var table = layui.table;
            var treetable = layui.treetable;

            // 渲染表格
            var renderTable = function () {
                layer.load(2);
                treetable.render({
                    size: 'lg', //尺寸
                    treeColIndex: 0, //设置下拉箭头在第几列
                    treeSpid: "0",   //最上级的父级id
                    treeIdName: 'WNo', //id字段的名称(自己的id)
                    treePidName: 'FWNo', //pid字段的名称（父亲的id）
                    treeDefaultClose: true, //是否默认折叠
                    treeLinkage: false, //父级展开时是否自动展开所有子级
                    elem: '#WhInfo', //表格的id
                    id: 'WhID',
                    url: sURL,
                    //url:'../json/Dept.json',
                    height: 'full-120',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                    cols: [   //列数和列名可根据需要更改
                        [
                            { field: 'WNo', width: "30%", align: 'left', title: '编码' }, //filed:json数据的key
                            { field: 'WName', width: "30%", align: 'center', title: '名称' },
                            { field: 'Kind', align: 'center', title: '类别' },
                            { field: 'DeptName', align: 'center', title: '部门' },
                            { field: 'Layer', align: 'center', title: '层级' },
                            { field: 'op', title: '操作', width: 200, toolbar: '#barDemo' }
                        ]
                    ],
                    page: true,
                    done: function () {
                        layer.closeAll('loading');
                    }
                });
            };
            renderTable();

            //监听工具条
            table.on('tool(WhInfo)', function (obj) { //注：tool是工具条事件名，table1是table表格的属性 lay-filter="对应的值"
                var data = obj.data; //获得当前行数据
                var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）
                var tr = obj.tr; //获得当前行 tr 的DOM对象

                if (layEvent === 'del') { //删除

                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("确定要禁用该该仓库信息吗？仓库编号：")

                    //设置删除的对象
                    $("#hint-value").html(data.WNo)

                    $("#txtFWNo").val(data.FWNo)
                    $("#txtWNo").val(data.WNo)

                } else if (layEvent === 'edit') { //编辑
                    $('#head-title1').html("修改仓库信息");
                    $('#txtAEFlag').val("14");
                    $("#IsAutoSubmitAndAudit").prop("checked", data.IsAutoSubmitAndAudit === "true");
                    if (data.FWNo != "0") {  // 说明修改的不是上级工作中心信息，

                        $("#txtFWNo").val(data.FWNo);
                        $("#txtFWName").val(data.FWName);
                        $("#txtWNo").val(data.WNo);
                        $("#txtWName").val(data.WName);
                        $("#txtKind").val(data.Kind);
                        $("#txtRemark").val(data.Remark);  //
                        $("#txtFWNo").attr({ "disabled": "disabled" });
                        $("#txtWNo").attr({ "disabled": "disabled" });
                        $("#txtFWName").attr({ "disabled": "disabled" });

                        $("#ShowOne").css("display", "block")
                        $("#ShowOne-fade").css("display", "block")
                    }
                    else { // 说明现在点击的是最顶层父零件
                        layer.msg('顶层仓库信息不需要修改，请确认！')
                    }
                    GetDeptInfoList(data.DeptNo)
                    //layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }
                else if (layEvent === 'add') { //添加
                    $('#head-title1').html("添加仓库信息");
                    $('#txtAEFlag').val("13");
                    $("#txtFWNo").val(data.WNo);
                    $("#txtFWName").val(data.WName);
                    $("#txtWNo").val('');
                    $("#txtWName").val('');
                    $("#txtRemark").val('');  //
                    $("#txtKind").val('');  //
                    $("#txtFWNo").attr({ "disabled": "disabled" });
                    $("#txtFWName").attr({ "disabled": "disabled" });
                    $("#txtWNo").removeAttr("disabled");

                    $("#ShowOne").css("display", "block")
                    $("#ShowOne-fade").css("display", "block")
                    GetDeptInfoList(data.DeptNo)
                }
            });


            //  查询1 -- WH信息查询
            $('#WHBut_open').click(function (obj) {
                var sFNo = $("#txtSFNo").val();  //编码
                var sFName = encodeURI($("#txtSFName").val());  // 名称
                var sCNo = $("#txtSCNo").val();  //编码
                var sCName = encodeURI($("#txtSCName").val());  // 名称// 解决中文乱码的问题。
                var data = obj.data; //获得当前行数据

                var Data = '';
                var Params = { FNo: sFNo, FName: sFName, CNo: sCNo, CName: sCName };
                var Data = JSON.stringify(Params);

                sURL = '../Service/PrdAssistAjax.ashx?OP=GetWareHouseInfo&Data=' + Data;

                table.reload('WhID', {
                    // method: 'post',
                    // url: '../Service/BaseModuleAjax.ashx?OP=GetWhInfo&CFlag=41&Data='+Data,
                    where: {
                        'No': sFNo,
                        'name': sFName
                    }, page: {
                        curr: 1
                    }
                });

                renderTable();
            });


            //
            $("#WHDelBtn").click(function () {
                var sFNo = $("#txtFWNo").val()
                var sCNo = $("#txtWNo").val()
                var sFlag = "15";

                var Data = '';
                var Params = { No: sFNo, Name: "", Item: sCNo, MNo: "", MName: "", A: "", B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);
                $.ajax({
                    type: "POST",
                    url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=15",
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('删除成功！');

                            $('#WHBut_open').click()

                            closeDialog()

                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                            layer.msg('该仓库下面有子仓库信息，不能删除！');
                        }
                        else {
                            layer.msg('删除失败，请重试！')
                        }
                    },
                    error: function (data) {
                        layer.msg('删除失败2，请重试！')
                    }
                });
            })


        });   // table 方法体


        function openDialog(n) {  // 新增弹窗
            $('#head-title1').html("新增工厂信息");
            $('#txtAEFlag').val("13");

            //var sCompName = "";
            //var sCompNo = "";

            sURL = '../Service/PrdAssistAjax.ashx?OP=GetCompanyInfo&CFlag=66';
            var Data = '';
            $.ajax({
                url: sURL,
                data: { Data: Data },
                success: function (data) {                    //数据成功读取后的处理

                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.length > 0) {
                        //sCompNo = parsedJson[0].CompanyNo;
                        //sCompName = parsedJson[0].CompName;
                        $("#txtFWNo").val(parsedJson[0].CompanyNo);
                        $("#txtFWName").val(parsedJson[0].CompName);
                    }
                },
                error: function (xhr, status, error) {
                    //错误处理
                    $("body").append("<div>读取数据失败：" + error + "</div>");
                }
            });

            //$("#txtFWNo").val(sCompNo);
            //$("#txtFWName").val(sCompName);
            $("#txtWNo").val('');
            $("#txtWName").val('');
            $("#txtRemark").val('');  //
            $("#txtKind").val('工厂');  //
            $("#txtFWNo").attr({ "disabled": "disabled" });
            $("#txtFWName").attr({ "disabled": "disabled" });
            $("#txtWNo").removeAttr("disabled");


            $("#ShowOne").css("display", "block")
            $("#ShowOne-fade").css("display", "block")
            GetDeptInfoList()
        }

        function closeDialog() {
            $("#ShowOne").css("display", "none")
            $("#ShowOne-fade").css("display", "none")
            $("#ShowDel").css("display", "none")
            $("#ShowDel-fade").css("display", "none")
        }


        // 下拉选择系统类别--部门信息
        function GetDeptInfoList(deptNo) {
            var CNo = "";
            var CKind = encodeURI("部门信息");
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/BaseModuleAjax.ashx?OP=GetDeptInfoList&CFlag=41&CMNO=" + CNo + "&CKind=" + CKind,
                success: function (data) {
                    var parsedJson = eval(data).Msg;
                    var sKong = "<option value=''> </option>";
                    $("#txtDept").empty();
                    $("#txtDept").append(sKong + parsedJson);
                    $('#txtDept').val(deptNo);
                }
            });
        }
    </script>


    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

        })
    </script>

    <style type="text/css">

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        /*.black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: #bbbcc7;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=60);
        }*/

        .wangid_conbox td, .wangid_conbox th, #ShowOne td {
            font-size: 12px
        }

        .find_input {
            width: 12.5%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }

        .LabelDelBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #da542e;
        }

        .LabelAddBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #579fd8;
        }

        #ShowOne tr {
            height: 40px;
        }

        #ShowOne .XC-Input-block {
            width: 100%;
            margin-left: 5px
        }
        .layui-table, .layui-table-view {
            margin: 0px 0px 10px 0px;
        }
    </style>





</head>
<body>
    <div class="div_find">

        <p>
            <label class="find_labela">上级代码</label> <input type="text" id="txtSFNo" class="find_input" />
            <label class="find_labela">上级名称</label><input type="text" id="txtSFName" class="find_input" />
            <label class="find_labela">下级代码</label><input type="text" id="txtSCNo" class="find_input" />
            <label class="find_labela">下级名称</label><input type="text" id="txtSCName" class="find_input" />
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="WHBut_open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="openDialog(1)">添加</button>
        </p>
        <p id="open" style="display:none">
            <label class="find_labela">工序：</label> <input type="text" id="txtSGX" class="find_input" />
        </p>
    </div>

    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="WhInfo" lay-filter="WhInfo"></table>

        <script type="text/html" id="barDemo">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" style="height:25px" lay-event="add">添加</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" style="height:25px" lay-event="edit">修改</button>
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" style="height:25px" lay-event="del">删除</button>
        </script>
    </div>



    <!--弹出层-->
    <div class="XC-modal XC-modal-md" id="ShowOne" style="height:480px;">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1"></span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <table cellspacing="0" cellpadding="0" border='0' style="width:99%; ">
                <tr>
                    <td style=" max-width:80px;width:80px;text-align:right;">
                        <label id="lbl_SJNo">上级编码</label>
                    </td>
                    <td>
                        <input type="text" class="XC-Input-block" id="txtFWNo" name="txtFNo" />
                    </td>
                </tr>
                <tr>
                    <td style=" max-width: 80px; width: 80px; text-align: right;">
                        <label id="lbl_SJName">上级名称</label>
                    </td>
                    <td>
                        <input type="text" class="XC-Input-block" id="txtFWName" name="txtFName" />
                    </td>
                </tr>
                <tr>
                    <td style=" max-width: 80px; width: 80px; text-align: right;">
                        下级编码
                    </td>
                    <td>
                        <input type="text" class="XC-Input-block" id="txtWNo" name="txtWNo" />
                    </td>
                </tr>
                <tr>
                    <td style=" max-width: 80px; width: 80px; text-align: right;">
                        下级名称
                    </td>
                    <td>
                        <input type="text" class="XC-Input-block" id="txtWName" name="txtWName" />
                    </td>
                </tr>
                <tr>
                    <td style=" max-width: 80px; width: 80px; text-align: right;">
                        类别
                    </td>
                    <td>
                        <select class="XC-Input-block" id="txtKind">
                            <option></option>
                            <option>公司</option>
                            <option>工厂</option>
                            <option>仓库</option>
                            <option>区域</option>
                            <option>排</option>
                            <option>列</option>
                            <option>层</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td style=" max-width: 80px; width: 80px; text-align: right;">
                        部门
                    </td>
                    <td>
                        <select class="XC-Input-block" id="txtDept"></select>
                    </td>
                </tr>
                <tr>
                    <td style="max-width: 80px; width: 80px; text-align: right; ">
                    </td>
                    <td>
                        <input type="checkbox" id="IsAutoSubmitAndAudit" name="IsAutoSubmitAndAudit" value="" style="vertical-align:middle" /> 是否WMS仓
                    </td>
                </tr>
                <tr>
                    <td style=" max-width: 80px; width: 80px; text-align: right;">
                        备注
                    </td>
                    <td>
                        <textarea class="XC-Input-block" id="txtRemark" name="txtRemark" style="height:60px;"> </textarea>
                    </td>
                </tr>
                <tr id="changeF1" style=" height:35px; display:none;">
                    <td style=" width:80px; text-align:right;">
                        <input type="checkbox" id="NewFNo" style="height:15px; width:15px;">更改上级编码</input>
                    </td>
                    <td>
                        <input type="text" class="XC-Input-block" id="txtNewFNo" name="txtNewFWNo" readonly="readonly" />
                    </td>
                </tr>
                <tr id="changeF2" style=" height:35px; display:none;">
                    <td style=" width:80px; text-align:right;">
                        新上级名称:
                    </td>
                    <td>
                        <input type="text" class="XC-Input-block" id="txtNewFWName" name="txtNewFName" readonly="readonly" />
                    </td>
                </tr>
            </table>

        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="WHSaveBtn">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="WHsaveClose" onclick="closeDialog()">取消</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>

    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtFWNo" name="txtFWNo" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtWNo" name="txtWNo" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="WHDelBtn">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>

    <!--消息提示-->
    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>
</html>