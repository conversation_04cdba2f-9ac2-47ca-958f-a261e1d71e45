<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" >
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>申请流程提交</title>
    <script src="../js/jquery-1.9.1.min.js" type="text/javascript"></script>
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <script src="../js/bootstrap.min.js" type="text/javascript"></script>
    <script src="../js/bootstrap-table.min.js" type="text/javascript"></script>
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <script src="../js/ajaxfileupload.js" type="text/javascript"></script>
    <script src="../js/FConfig.js" type="text/javascript"></script>

    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }


        $(function() {
            var sItemNo = GetQueryString("BSNo");

            GetFlowList(""); // 获取所有流程

            $.ajax({
                url: "../Service/FlowConfigAjax.ashx?OP=GetBSFlowMX&sFlag=71&BSNo=" + sItemNo,
                success: function(data) {
                    var parsedJson = jQuery.parseJSON(data);


                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                        //location.href = "../Login.htm?sFlag=31";
                        $("#loginError").html("系统无此账号，请确认！")
                        $("#loginError").show();

                    }

                    if (parsedJson[0].NEFlag == "E") {

                        $('#txtBSApplyNo').val(parsedJson[0].BSNo);
                        $('#txtBSApplyName').val(parsedJson[0].BSName);
                        $('#txtFlowName').val(parsedJson[0].FlowName + '(' + parsedJson[0].FlowNo + ')');
                        $('#txtF01').val(parsedJson[0].FOne); $('#txtF02').val(parsedJson[0].FTwo); $('#txtF03').val(parsedJson[0].FThree); $('#txtF04').val(parsedJson[0].FFour);
                        $('#txtF05').val(parsedJson[0].FFive); $('#txtF06').val(parsedJson[0].FSix); $('#txtF07').val(parsedJson[0].FSeven); $('#txtF08').val(parsedJson[0].FEight);
                        $('#txtF09').val(parsedJson[0].FNine); $('#txtF10').val(parsedJson[0].FTen);
                        $('#txtOperateName').val(parsedJson[0].OperateName);
                        $('#txtOperate').val(parsedJson[0].Operate);
                        $('#txtCompanyNo').val(parsedJson[0].CompanyNo);
                        $('#txtNEFlag').val(parsedJson[0].NEFlag);
                        $('#txtStatus').val(parsedJson[0].Status);
                        $("#imgs1").attr("src", parsedJson[0].PathOne); $("#imgs1").attr("alt", parsedJson[0].PathOne); $("#imgs2").attr("src", parsedJson[0].PathTwo); $("#imgs3").attr("src", parsedJson[0].PathThr);
                        $("#Video1").attr("src", parsedJson[0].PathVideo); 

                        $('#txtBSApplyName').attr({ "disabled": "disabled" });
                        $('#txtFlowName').attr({ "disabled": "disabled" });
                        if (parsedJson[0].Status != "待提交") {
                            GetFlowTxt(parsedJson[0].BSNo, "3");  //  显示当时保存的流程节点  -- 当时
                            $('#txtRemark').val(parsedJson[0].Remark);
                        }
                        else {
                            GetFlowTxt(parsedJson[0].BSNo, "4");  //  显示现在这个流程的流程节点 -- 申请流程
                        }

                        $('#txtRemark').removeAttr("disabled");

                        if ((parsedJson[0].Operate == parsedJson[0].InMan) && (parsedJson[0].Status != "已完成") && (parsedJson[0].Status != "已撤回")) {
                            $("#CBH").show(); 
                        }

                        // 显示输入框
                        if (parsedJson[0].FOne !== "") {
                            $('#txtF01').removeAttr("disabled"); $("#F01").show();
                        }
                        if (parsedJson[0].FTwo !== "") {
                            $('#txtF02').removeAttr("disabled"); $("#F02").show();
                        }
                        if (parsedJson[0].FThree !== "") {
                            $('#txtF03').removeAttr("disabled"); $("#F03").show();
                        }
                        if (parsedJson[0].FFour !== "") {
                            $('#txtF04').removeAttr("disabled"); $("#F04").show();
                        }
                        if (parsedJson[0].FFive !== "") {
                            $('#txtF05').removeAttr("disabled"); $("#F05").show();
                        }
                        if (parsedJson[0].FSix !== "") {
                            $('#txtF06').removeAttr("disabled"); $("#F06").show();
                        }
                        if (parsedJson[0].FSeven !== "") {
                            $('#txtF07').removeAttr("disabled"); $("#F07").show();
                        }
                        if (parsedJson[0].FEight !== "") {
                            $('#txtF08').removeAttr("disabled"); $("#F08").show();
                        }
                        if (parsedJson[0].FNine !== "") {
                            $('#txtF09').removeAttr("disabled"); $("#F09").show();
                        }
                        if (parsedJson[0].FTen !== "") {
                            $('#txtF10').removeAttr("disabled"); $("#F10").show();
                        }

                        if (parsedJson[0].PathOne != undefined && parsedJson[0].PathOne != "") {
                            $("#txtPic1").val(parsedJson[0].PathOne);
                        }
                        else {
                            $("#Btn1").css({ "display": "none" });
                            $("#txtPic1").css({ "display": "none" });
                        }
                        // 显示图片
                        if (parsedJson[0].PathTwo != undefined && parsedJson[0].PathTwo != "") {
                            $("#txtPic2").val(parsedJson[0].PathTwo);
                        }
                        else {
                            $("#Btn2").css({ "display": "none" });
                            $("#txtPic2").css({ "display": "none" });
                        }

                        if (parsedJson[0].PathThr != undefined && parsedJson[0].PathThr != "") {
                            $("#txtPic3").val(parsedJson[0].PathThr);
                        }
                        else {
                            $("#Btn3").css({ "display": "none" });
                            $("#txtPic3").css({ "display": "none" });
                        }
                        if (parsedJson[0].PicPath != undefined && parsedJson[0].PicPath != "") {
                            $("#txtPath").val(parsedJson[0].PicPath);
                        }

                        if (parsedJson[0].PathVideo != undefined && parsedJson[0].PathVideo != "") {
                            $("#txtVideoPath").val(parsedJson[0].PathVideo); $("#ShowVideo").show();
                        }

                        $('#dgBSDataList').bootstrapTable('refresh', { url: '../Service/FlowConfigAjax.ashx?OP=BSFlowDataList&BSNo=' + parsedJson[0].BSNo });  // 刷新数据
                    }
                    else {  // 新增  
                        $("#txtNEFlag").val(parsedJson[0].NEFlag);
                        $("#txtOperate").val(parsedJson[0].Operate);
                        $("#txtOperateName").val(parsedJson[0].OperateName);

                        $('#txtObjectName').val('');
                        for (var i = 1; i <= 9; i++) {
                            $('#txtF0' + i).val('');
                        }
                        $('#txtF10').val('');

                    }
                }
            })
        })


        function GetFlowList(sComp) {  // 获取所有流程
            var keywords = encodeURI(sComp);  // 解决中文乱码的问题。
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/FlowConfigAjax.ashx?OP=GetFlowList&Comp=" + keywords,
                success: function(data) {
                    var parsedJson = eval(data).Msg;
                    var sKong = "<option value=''> </option>";
                    
                    $("#txtFlowName").empty();
                    $("#txtFlowName").append(sKong + parsedJson);
                }
            });
        }
        

        function GetFlowTxt(sFlow,sF) {
            var keywords = encodeURI(sFlow);  // 解决中文乱码的问题。
            $.ajax({
                url: "../Service/FlowConfigAjax.ashx?OP=GetFlowTxt&LCZD=" + keywords + "&sFlag=" + sF,
                success: function(data) {
                    var Json = jQuery.parseJSON(data);

                    // 给字段一，字段二赋具体描述
                    for (var i = 1; i <= 9; i++) {
                        $("#txtF0" + i).attr({ "disabled": "disabled" });
                        //$('#txtF0' + i).val('');  // 不要清空，默认显示第一个记录的信息
                        $("#F0" + i).hide();
                    }
                    $("#txtF10").attr({ "disabled": "disabled" });
                    $('#txtObjectName').val('');
                    // $('#txtF10').val('');
                    $("#F10").hide();   // 切换选择流程时，如果不增加下面的隐藏代码，无法实现不需要的字段系统帮隐藏

                    $('#FName01').html("*" + Json[0].FOne); $('#FName02').html("*" + Json[0].FTwo); $('#FName03').html(Json[0].FThree); $('#FName04').html(Json[0].FFour); $('#FName05').html(Json[0].FFive);
                    $('#FName06').html(Json[0].FSix); $('#FName07').html(Json[0].FSeven); $('#FName08').html(Json[0].FEight); $('#FName09').html(Json[0].FNine); $('#FName10').html(Json[0].FTen);

                    $('#F0001').html(Json[0].FOne); $('#F0002').html(Json[0].FTwo); $('#F0003').html(Json[0].FThree); $('#F0004').html(Json[0].FFour); $('#F0005').html(Json[0].FFive);
                    $('#F0006').html(Json[0].FSix); $('#F0007').html(Json[0].FSeven); $('#F0008').html(Json[0].FEight); $('#F0009').html(Json[0].FNine); $('#F0010').html(Json[0].FTen);


                    // 如果是新增流程，改变字段底色
                    if (sF == "4") {
                        $("#FlowName").css("background-color", "#FFFFFF");  $("#BSApplyName").css("background-color", "#FFFFFF");
                        for (var i = 1; i <= 15; i++) {
                            if (i <= 9) {
                                $("#FName0" + i).css("background-color", "#FFFFFF");
                            }
                            else {
                                $("#N" + i).css("background-color", "#FFFFFF");
                            }
                        }
                    }


                    if (Json[0].FOne !== "") {
                        $('#txtF01').removeAttr("disabled"); $("#F01").show();
                    }
                    if (Json[0].FTwo !== "") {
                        $('#txtF02').removeAttr("disabled"); $("#F02").show();
                    }
                    if (Json[0].FThree !== "") {
                        $('#txtF03').removeAttr("disabled"); $("#F03").show();
                    }
                    if (Json[0].FFour !== "") {
                        $('#txtF04').removeAttr("disabled"); $("#F04").show();
                    }
                    if (Json[0].FFive !== "") {
                        $('#txtF05').removeAttr("disabled"); $("#F05").show();
                    }
                    if (Json[0].FSix !== "") {
                        $('#txtF06').removeAttr("disabled"); $("#F06").show();
                    }
                    if (Json[0].FSeven !== "") {
                        $('#txtF07').removeAttr("disabled"); $("#F07").show();
                    }
                    if (Json[0].FEight !== "") {
                        $('#txtF08').removeAttr("disabled"); $("#F08").show();
                    }
                    if (Json[0].FNine !== "") {
                        $('#txtF09').removeAttr("disabled"); $("#F09").show();
                    }
                    if (Json[0].FTen !== "") {
                        $('#txtF10').removeAttr("disabled"); $("#F10").show();
                    }

                    // 初始化审批节点及节点的审批人，审批内容
                    for (var i = 1; i <= 15; i++) {
                        $('#NName' + i).html(''); $('#AudMan' + i).html('');

                        if (i <= 9) {
                            $("#N0" + i).hide();
                        }
                        else {
                            $("#N" + i).hide();
                        }
                    }

                    var NowStatus = $('#txtStatus').val();  // 当前申请的节点

                    // 显示流程节点 -- 给流程节点赋值
                    if (Json[0].S01 !== "") {
                        if (Json[0].S01 == NowStatus) {
                            $("#imgs01").attr("src", "../fonts/TB01.gif"); $('#NName1').css("color", "blue");
                        }
                        $('#NName1').html("1_" + Json[0].S01); $("#N01").show(); $('#AudMan1').html(""); $('#AudRec1').html("");  // 审批人，审批内容在下面增加
                    }
                    if (Json[0].S02 !== "") {
                        if (Json[0].S02 == NowStatus) {
                            $("#imgs02").attr("src", "../fonts/TB01.gif"); $('#NName2').css("color", "blue");
                        }
                        $('#NName2').html("2_" + Json[0].S02); $("#N02").show(); $('#AudMan2').html(""); $('#AudRec2').html("");
                    }
                    if (Json[0].S03 !== "") {
                        if (Json[0].S03 == NowStatus) {
                            $("#imgs03").attr("src", "../fonts/TB01.gif"); $('#NName3').css("color", "blue");
                        }
                        $('#NName3').html("3_" + Json[0].S03); $("#N03").show(); $('#AudMan3').html(""); $('#AudRec3').html("");
                    }
                    if (Json[0].S04 !== "") {
                        if (Json[0].S04 == NowStatus) {
                            $("#imgs04").attr("src", "../fonts/TB01.gif"); $('#NName4').css("color", "blue");
                        }
                        $('#NName4').html("4_" + Json[0].S04); $("#N04").show(); $('#AudMan4').html(""); $('#AudRec4').html("");
                    }
                    if (Json[0].S05 !== "") {
                        if (Json[0].S05 == NowStatus) {
                            $("#imgs05").attr("src", "../fonts/TB01.gif"); $('#NName5').css("color", "blue");
                        }
                        $('#NName5').html("5_" + Json[0].S05); $("#N05").show(); $('#AudMan5').html(""); $('#AudRec5').html("");
                    }
                    if (Json[0].S06 !== "") {
                        if (Json[0].S06 == NowStatus) {
                            $("#imgs06").attr("src", "../fonts/TB01.gif"); $('#NName6').css("color", "blue");
                        }
                        $('#NName6').html("6_" + Json[0].S06); $("#N06").show(); $('#AudMan6').html(""); $('#AudRec6').html("");
                    }
                    if (Json[0].S07 !== "") {
                        if (Json[0].S07 == NowStatus) {
                            $("#imgs07").attr("src", "../fonts/TB01.gif"); $('#NName7').css("color", "blue");
                        }
                        $('#NName7').html("7_" + Json[0].S07); $("#N07").show(); $('#AudMan7').html(""); $('#AudRec7').html("");
                    }
                    if (Json[0].S08 !== "") {
                        if (Json[0].S08 == NowStatus) {
                            $("#imgs08").attr("src", "../fonts/SX02.gif"); $('#NName8').css("color", "blue");
                        }
                        $('#NName8').html("8 " + Json[0].S08); $("#N08").show(); $('#AudMan8').html(""); $('#AudRec8').html("");
                    }
                    if (Json[0].S09 !== "") {
                        if (Json[0].S09 == NowStatus) {
                            $("#imgs09").attr("src", "../fonts/TB01.gif"); $('#NName9').css("color", "blue");
                        }
                        $('#NName9').html("9_" + Json[0].S09); $("#N09").show(); $('#AudMan9').html(""); $('#AudRec9').html("");
                    }
                    if (Json[0].S10 !== "") {
                        if (Json[0].S10 == NowStatus) {
                            $("#imgs10").attr("src", "../fonts/TB01.gif"); $('#NName10').css("color", "blue");
                        }
                        $('#NName10').html("10_" + Json[0].S10); $("#N10").show(); $('#AudMan10').html(""); $('#AudRec10').html("");
                    }
                    if (Json[0].S11 !== "") {
                        if (Json[0].S11 == NowStatus) {
                            $("#imgs11").attr("src", "../fonts/TB01.gif"); $('#NName11').css("color", "blue");
                        }
                        $('#NName11').html("11_" + Json[0].S11); $("#N11").show(); $('#AudMan11').html(""); $('#AudRec11').html("");
                    }
                    if (Json[0].S12 !== "") {
                        if (Json[0].S12 == NowStatus) {
                            $("#imgs12").attr("src", "../fonts/TB01.gif"); $('#NName12').css("color", "blue");
                        }
                        $('#NName12').html("12_" + Json[0].S12); $("#N12").show(); $('#AudMan12').html(""); $('#AudRec12').html("");
                    }
                    if (Json[0].S13 !== "") {
                        if (Json[0].S13 == NowStatus) {
                            $("#imgs13").attr("src", "../fonts/TB01.gif"); $('#NName13').css("color", "blue");
                        }
                        $('#NName13').html("13_" + Json[0].S13); $("#N13").show(); $('#AudMan13').html(""); $('#AudRec13').html("");
                    }
                    if (Json[0].S14 !== "") {
                        if (Json[0].S14 == NowStatus) {
                            $("#imgs14").attr("src", "../fonts/TB01.gif"); $('#NName14').css("color", "blue");
                        }
                        $('#NName14').html("14_" + Json[0].S14); $("#N14").show(); $('#AudMan14').html(""); $('#AudRec14').html("");
                    }
                    if (Json[0].S15 !== "") {
                        if (Json[0].S15 == NowStatus) {
                            $("#imgs15").attr("src", "../fonts/TB01.gif"); $('#NName15').css("color", "blue");
                        }
                        $('#NName15').html("15_" + Json[0].S15); $("#N15").show(); $('#AudMan15').html(""); $('#AudRec15').html("");
                    }

                    // 初始化审批节点，默认显示都需要增加人的节点按钮。
                    for (var i = 1; i <= 15; i++) {
                        if (i <= 9) {
                            $("#Add0" + i).show();
                        }
                        else {
                            $("#Add" + i).show();
                        }
                    }

                    // 显示流程节点的审批人  -- 已有审核人的，不能再添加审核人了。
                    if (Json[0].M01 !== "") {
                        $('#AudMan1').html("审核人；" + Json[0].M01); $("#Add01").hide();
                    }
                    if (Json[0].M02 !== "") {
                        $('#AudMan2').html("审核人；" + Json[0].M02); $("#Add02").hide();
                    }
                    if (Json[0].M03 !== "") {
                        $('#AudMan3').html("审核人；" + Json[0].M03); $("#Add03").hide();
                    }
                    if (Json[0].M04 !== "") {
                        $('#AudMan4').html("审核人；" + Json[0].M04); $("#Add04").hide();
                    }
                    if (Json[0].M05 !== "") {
                        $('#AudMan5').html("审核人；" + Json[0].M05); $("#Add05").hide();
                    }
                    if (Json[0].M06 !== "") {
                        $('#AudMan6').html("审核人；" + Json[0].M06); $("#Add06").hide();
                    }
                    if (Json[0].M07 !== "") {
                        $('#AudMan7').html("审核人；" + Json[0].M07); $("#Add07").hide();
                    }
                    if (Json[0].M08 !== "") {
                        $('#AudMan8').html("审核人；" + Json[0].M08); $("#Add08").hide();
                    }
                    if (Json[0].M09 !== "") {
                        $('#AudMan9').html("审核人；" + Json[0].M09); $("#Add09").hide();
                    }
                    if (Json[0].M10 !== "") {
                        $('#AudMan10').html("审核人；" + Json[0].M10); $("#Add10").hide();
                    }
                    if (Json[0].M11 !== "") {
                        $('#AudMan11').html("审核人；" + Json[0].M11); $("#Add11").hide();
                    }
                    if (Json[0].M12 !== "") {
                        $('#AudMan12').html("审核人；" + Json[0].M12); $("#Add12").hide();
                    }
                    if (Json[0].M13 !== "") {
                        $('#AudMan13').html("审核人；" + Json[0].M13); $("#Add13").hide();
                    }
                    if (Json[0].M14 !== "") {
                        $('#AudMan14').html("审核人；" + Json[0].M14); $("#Add14").hide();
                    }
                    if (Json[0].M15 !== "") {
                        $('#AudMan15').html("审核人；" + Json[0].M15); $("#Add15").hide();
                    }

                    // 显示流程节点的审批内容
                    if (Json[0].D01 !== "") {
                        $('#AudRec1').html(Json[0].D01);
                        if (Json[0].D01.indexOf("不通过") > 0) {
                            $('#AudRec1').css("color", "red"); $('#txtInFlag').val("审核不通过");
                        }
                    }
                    if (Json[0].D02 !== "") {
                        $('#AudRec2').html(Json[0].D02);
                        if (Json[0].D02.indexOf("不通过") > 0) {
                            $('#AudRec2').css("color", "red"); $('#txtInFlag').val("审核不通过");
                        }
                    }
                    if (Json[0].D03 !== "") {
                        $('#AudRec3').html(Json[0].D03);
                        if (Json[0].D03.indexOf("不通过") > 0) {
                            $('#AudRec3').css("color", "red"); $('#txtInFlag').val("审核不通过");
                        }
                    }
                    if (Json[0].D04 !== "") {
                        $('#AudRec4').html(Json[0].D04);
                        if (Json[0].D04.indexOf("不通过") > 0) {
                            $('#AudRec4').css("color", "red"); $('#txtInFlag').val("审核不通过");
                        }
                    }
                    if (Json[0].D05 !== "") {
                        $('#AudRec5').html(Json[0].D05);
                        if (Json[0].D05.indexOf("不通过") > 0) {
                            $('#AudRec5').css("color", "red"); $('#txtInFlag').val("审核不通过");
                        }
                    }
                    if (Json[0].D06 !== "") {
                        $('#AudRec6').html(Json[0].D06);
                        if (Json[0].D06.indexOf("不通过") > 0) {
                            $('#AudRec6').css("color", "red"); $('#txtInFlag').val("审核不通过");
                        }
                    }
                    if (Json[0].D07 !== "") {
                        $('#AudRec7').html(Json[0].D07);
                        if (Json[0].D07.indexOf("不通过") > 0) {
                            $('#AudRec7').css("color", "red"); $('#txtInFlag').val("审核不通过");
                        }
                    }
                    if (Json[0].D08 !== "") {
                        $('#AudRec8').html(Json[0].D08);
                        if (Json[0].D08.indexOf("不通过") > 0) {
                            $('#AudRec8').css("color", "red"); $('#txtInFlag').val("审核不通过");
                        }
                    }
                    if (Json[0].D09 !== "") {
                        $('#AudRec9').html(Json[0].D09);
                        if (Json[0].D09.indexOf("不通过") > 0) {
                            $('#AudRec9').css("color", "red"); $('#txtInFlag').val("审核不通过");
                        }
                    }
                    if (Json[0].D10 !== "") {
                        $('#AudRec10').html(Json[0].D10);
                        if (Json[0].D10.indexOf("不通过") > 0) {
                            $('#AudRec10').css("color", "red"); $('#txtInFlag').val("审核不通过");
                        }
                    }
                    if (Json[0].D11 !== "") {
                        $('#AudRec11').html(Json[0].D11);
                        if (Json[0].D11.indexOf("不通过") > 0) {
                            $('#AudRec11').css("color", "red"); $('#txtInFlag').val("审核不通过");
                        }
                    }
                    if (Json[0].D12 !== "") {
                        $('#AudRec12').html(Json[0].D12);
                        if (Json[0].D12.indexOf("不通过") > 0) {
                            $('#AudRec12').css("color", "red"); $('#txtInFlag').val("审核不通过");
                        }
                    }
                    if (Json[0].D13 !== "") {
                        $('#AudRec13').html(Json[0].D13);
                        if (Json[0].D13.indexOf("不通过") > 0) {
                            $('#AudRec13').css("color", "red"); $('#txtInFlag').val("审核不通过");
                        }
                    }
                    if (Json[0].D14 !== "") {
                        $('#AudRec14').html(Json[0].D14);
                        if (Json[0].D14.indexOf("不通过") > 0) {
                            $('#AudRec14').css("color", "red"); $('#txtInFlag').val("审核不通过");
                        }
                    }
                    if (Json[0].D15 !== "") {
                        $('#AudRec15').html(Json[0].D15);
                        if (Json[0].D15.indexOf("不通过") > 0) {
                            $('#AudRec15').css("color", "red"); $('#txtInFlag').val("审核不通过");
                        }
                    }

                    // 只有在待审核的情况下，才这样赋值;$('#txtInFlag').val("审核不通过");其他节点清空：
                    if (Json[0].Status != "待提交") {
                        $('#txtInFlag').val("");
                    }

                    // 显示每个节点的审批人是否可以重新添加。
                    if (Json[0].B01 !== "") {
                        $("#Add01").show();
                    }
                    if (Json[0].B02 !== "") {
                        $("#Add02").show();
                    }
                    if (Json[0].B03 !== "") {
                        $("#Add03").show();
                    }
                    if (Json[0].B04 !== "") {
                        $("#Add04").show();
                    }
                    if (Json[0].B05 !== "") {
                        $("#Add05").show();
                    }
                    if (Json[0].B06 !== "") {
                        $("#Add06").hide();
                    }
                    if (Json[0].B07 !== "") {
                        $("#Add07").hide();
                    }
                    if (Json[0].B08 !== "") {
                        $("#Add08").show();
                    }
                    if (Json[0].B09 !== "") {
                        $("#Add09").show();
                    }
                    if (Json[0].B10 !== "") {
                        $("#Add10").show();
                    }
                    if (Json[0].B11 !== "") {
                        $("#Add11").show();
                    }
                    if (Json[0].B12 !== "") {
                        $("#Add12").show();
                    }
                    if (Json[0].B13 !== "") {
                        $("#Add13").show();
                    }
                    if (Json[0].B14 !== "") {
                        $("#Add14").show();
                    }
                    if (Json[0].B15 !== "") {
                        $("#Add15").show();
                    }

                    // 如果已提交，这不能再修改了
                    if ((NowStatus != "待提交") && (NowStatus != "")) {
                        //--1 - 设置输入框不可修改
                        for (var i = 1; i <= 9; i++) {
                            $("#txtF0" + i).attr({ "disabled": "disabled" });
                        }

                        for (var i = 1; i <= 9; i++) {
                            $("#Add0" + i).hide();
                        }
                        $("#Add10").hide(); $("#Add11").hide(); $("#Add12").hide(); $("#Add13").hide(); $("#Add14").hide(); $("#Add15").hide();

                        $("#txtF10").attr({ "disabled": "disabled" });
                        $("#txtRemark").attr({ "disabled": "disabled" });

                        $("#btnBSSave").css({ "display": "none" });  // 隐藏按钮
                        $("#btnDelBSData").css({ "display": "none" });
                        $("#btnSBUpload").css({ "display": "none" });
                        $("#btnVideoUpload").css({ "display": "none" });
                        $("#btnBSDataSubmit").attr({ "disabled": "disabled" });
                    }

                },
                error: function(data) {
                    $("#sMessage").html("系统出错，请重试2！")
                    $("#sMessage").show();
                }
            });
        }

         // 隐藏显示
        function AudManShow_Click(SeqNo) {
            if ($("#DIV" + SeqNo).is(":hidden")) {
                $("#DIV" + SeqNo).show(); ;    //如果元素为隐藏,则将它显现
            } else {
                $("#DIV" + SeqNo).hide(); ;     //如果元素为显现,则将其隐藏
                $("#txtIn" + SeqNo).val('');
            }
            
            
        }
        


        $(function() {
            $(".fixed-table-header").attr("hidden", "hidden");  // 隐藏表头 
            var $result = $('#events-result');
            $('#dgBSDataList').bootstrapTable({

            }).on('all.bs.table', function(e, name, args) {
               console.log('Event:', name, ', data:', args);
            }).on('click-row.bs.table', function(e, row, $element) {
              $('#txtF01').val(row.FOne);
              $('#txtF02').val(row.FTwo);
              $('#txtF03').val(row.FThree);
              $('#txtF04').val(row.FFour);
              $('#txtF05').val(row.FFive);
              $('#txtF06').val(row.FSix);
              $('#txtF07').val(row.FSeven);
              $('#txtF08').val(row.FEight);
              $('#txtF09').val(row.FNine);
              $('#txtF10').val(row.FTen);
              
            });
        });

        function fileSelected() {
            var file = document.getElementById('fileToUpload').files[0];
            if (file) {
                var fileSize = 0;
                if (file.size > 1024 * 1024)
                    fileSize = (Math.round(file.size * 100 / (1024 * 1024)) / 100).toString() + 'MB';
                else
                    fileSize = (Math.round(file.size * 100 / 1024) / 100).toString() + 'KB';

                document.getElementById('fileName').innerHTML = 'Name: ' + file.name;
                document.getElementById('fileSize').innerHTML = 'Size: ' + fileSize;
                document.getElementById('fileType').innerHTML = 'Type: ' + file.type;


                if (file.size / (1024 * 1024) < 435) {   // 不能上传大于 4 M  file.size/(1024*1024)  得到的是M单位了
                    $("#btnVideoUpload").show();
                }
                else {
                    $("#btnVideoUpload").hide();
                    document.getElementById('progressNumber').innerHTML = '只能上传小于4M的文件！';
                }
            }
        }
        
    </script>

</head>
<body>
    <form role="form" id="form1" name="form1" onload="ValidateLogin();">
    <div class="panel panel-primary" style="background-color: #f9f9f9">
        <div class="panel-heading">
            <h3 class="panel-title" style="color:Red">申请流程提交</h3>
        </div>
        <div class="panel-body" style="background-color: #f2f1f1">
            <div class="input-group" id="CHTxt" style="width: 100%; display:none">
                 <span class="input-group-addon">撤回说明</span>   
                    <textarea type="text" class="form-control" id="txtCHDesc" name="txtCHDesc"></textarea>
                 <span class="input-group-addon" style="padding-top:2px; padding-bottom:0px;">
                     <button type="button" id="btnCHCommit" name="btnCHCommit" class="btn btn-primary" style="height:30px; line-height:11px; color:Red; font-weight:bold"> 撤回提交</button>
                </span>
            </div>
            <div  class="input-group" id="CBH" style="width: 100%; display:none">
               <span class="input-group-addon" style="border: 0;border-bottom: 1px solid #EEC9FF; width:50%; text-align:right;">操作：</span>
               <button type="button" id="btnCB" name="btnCB" class="btn btn-primary" style="height:30px; width:50%; background-color:#99ccff"> 催办</button>
               <button type="button" id="btnCH" name="btnCH" class="btn btn-primary" style="height:30px; width:50%; background-color:#99ccff"> 撤回</button>
            </div>
            <div style="color:Blue; font-size:10px">
               1、选择需申请的流程，并填写申请流程名称，再根据流程字段填写要申请内容（可填写多个记录）。
            </div>
            <div class="input-group">
                <span class="input-group-addon" style="border: 0;border-bottom: 1px solid #EEC900;">申请编号</span>
                <input type="text" class="form-control" id="txtBSApplyNo" name="txtBSApplyNo" placeholder="系统自动赋值" readonly="readonly" style="border: 0;border-bottom: 1px solid #EEC900;"/>
            </div>
            <div  class="input-group">
                <span class="input-group-addon" id="FlowName" style="border: 0;border-bottom: 1px solid #EEC900;">申请流程</span>
                <select name="type" id="txtFlowName" class="form-control" data-size="5" style="border: 0;border-bottom: 1px solid #EEC900;" onchange="GetFlowTxt(this.options[this.options.selectedIndex].value,4)"> 
                  <option value=""></option>
                </select>
            </div>
            <div class="input-group">
                <span class="input-group-addon"  id="BSApplyName" style="border: 0;border-bottom: 1px solid #EEC900;">申请名称</span>
                <input type="text" class="form-control" id="txtBSApplyName" name="txtBSApplyName" placeholder="李逍遥_研发部_采购新物料" style="border: 0;border-bottom: 1px solid #EEC900;"/>
            </div>
            <div style="color:SkyBlue; font-size:8px">
               字段填写说明：前两个字段信息不一样时，系统会保存两个记录，如一样，系统只更新记录信息
            </div>
            <div class="input-group" id="F01" style="display:none;">
                <span class="input-group-addon" id="FName01" style="color:Red;border: 0;border-bottom: 1px solid #EEC900;">*字段一</span>
                <input type="text" class="form-control" id="txtF01" name="txtF01" disabled="disabled" style="border: 0;border-bottom: 1px solid #EEC900;"/>
            </div>
            <div class="input-group" id="F02" style="display:none">
                <span class="input-group-addon" id="FName02" style="color:Red;border: 0;border-bottom: 1px solid #EEC900;">*字段二</span>
                <input type="text" class="form-control" id="txtF02" name="txtF02" disabled="disabled" style="border: 0;border-bottom: 1px solid #EEC900;"/>
            </div>
            <div class="input-group" id="F03" style="display:none">
                <span class="input-group-addon" id="FName03" style="border: 0;border-bottom: 1px solid #EEC900;">字段三</span>
                <input type="text" class="form-control" id="txtF03" name="txtF03" disabled="disabled" style="border: 0;border-bottom: 1px solid #EEC900;"/>
            </div>
            <div class="input-group" id="F04" style="display:none">
                <span class="input-group-addon" id="FName04" style="border: 0;border-bottom: 1px solid #EEC900;">字段四</span>
                <input type="text" class="form-control" id="txtF04" name="txtF04" disabled="disabled" style="border: 0;border-bottom: 1px solid #EEC900;"/>
            </div>
            <div class="input-group" id="F05" style="display:none">
                <span class="input-group-addon" id="FName05" style="border: 0;border-bottom: 1px solid #EEC900;">字段五</span>
                <input type="text" class="form-control" id="txtF05" name="txtF05" disabled="disabled" style="border: 0;border-bottom: 1px solid #EEC900;"/>
            </div>
            <div class="input-group" id="F06" style="display:none">
                <span class="input-group-addon" id="FName06" style="border: 0;border-bottom: 1px solid #EEC900;">字段六</span>
                <input type="text" class="form-control" id="txtF06" name="txtF06" disabled="disabled" style="border: 0;border-bottom: 1px solid #EEC900;"/>
            </div>
            <div class="input-group" id="F07" style="display:none">
                <span class="input-group-addon" id="FName07" style="border: 0;border-bottom: 1px solid #EEC900;">字段七</span>
                <input type="tel" class="form-control" id="txtF07" name="txtF07" disabled="disabled" style="border: 0;border-bottom: 1px solid #EEC900;"/>
            </div>
            <div class="input-group" id="F08" style="display:none">
                <span class="input-group-addon" id="FName08" style="border: 0;border-bottom: 1px solid #EEC900;">字段八</span>
                <input type="text" class="form-control" id="txtF08" name="txtF08" disabled="disabled" style="border: 0;border-bottom: 1px solid #EEC900;"/>
            </div>
            <div class="input-group" id="F09" style="display:none">
                <span class="input-group-addon" id="FName09" style="border: 0;border-bottom: 1px solid #EEC900;">字段九</span>
                <input type="text" class="form-control" id="txtF09" name="txtF09" disabled="disabled" style="border: 0;border-bottom: 1px solid #EEC900;"/>
            </div>
            <div class="input-group" id="F10" style="display:none">
                <span class="input-group-addon" id="FName10" style="border: 0;border-bottom: 1px solid #EEC900;">字段十</span>
                <input type="text" class="form-control" id="txtF10" name="txtF10" disabled="disabled" style="border: 0;border-bottom: 1px solid #EEC900;"/>
            </div>
            <div style="color:Blue; font-size:10px">
               2、保存填写的流程信息，一个申请流程可填写多个记录；需要时上传图片作为流程附件。
            </div>
            <div  class="input-group" style="width: 100%;">
              <button type="button" id="btnBSSave" name="btnBSSave"  style="width: 70%"> 保存</button>
              <button type="button" id="btnDelBSData" name="btnDelBSData" style="width: 30%;color:Red; font-weight:bold">删除</button>
            </div>
            <div>
                <strong id="divsuccess" style="color: Red"></strong>
            </div>
            <div class="form-group">
                <div id="divBSDataList" style="background-color: #f2f1f1; float: left; width: 100%">
                   <div class="table-responsive">
                    <table id="dgBSDataList" data-toggle="table" data-cache="false" data-pagination="true" 
                            data-search="false" data-show-refresh="false" data-show-toggle="false" data-sort-order="desc">
                        <thead>
                          <tr>
                            <th data-field="FOne" data-align="left" style="width: 10%">
                               <label id="F0001"  for="exampleInputPassword1">字段一</label>
                            </th>
                            <th data-field="FTwo" data-align="left" style="width: 10%;">
                               <label id="F0002">字段二</label>
                            </th>
                            <th data-field="FThree" data-align="left" style="width: 10%;">
                               <label id="F0003">字段三</label>
                            </th>
                            <th data-field="FFour" data-align="left" style="width: 10%;">
                               <label id="F0004">字段四</label>
                            </th>
                            <th data-field="FFive" data-align="left" style="width: 10%">    
                               <label id="F0005">字段五</label>
                            </th>
                            <th data-field="FSix" data-align="left" style="width: 10%;">   
                               <label id="F0006">字段六</label>
                            </th>
                            <th data-field="FSeven" data-align="left" style="width: 10%;">   
                               <label id="F0007">字段七</label>
                            </th>
                            <th data-field="FEight" data-align="left" style="width: 10%;">
                               <label id="F0008">字段八</label>
                            </th>
                            <th data-field="FNine" data-align="left" style="width: 10%;">
                               <label id="F0009">字段九</label>
                            </th>
                            <th data-field="FTen" data-align="left" style="width: 10%;">
                               <label id="F0010">字段十</label>
                            </th>
                          </tr>
                       </thead>
                     </table>
                  </div>
                </div>
                <div style="color:Blue; font-weight:bold">
                  3上传附件;并确认各节点审批人，如节点无审批人，请选择添加。
                </div>
                <div style="color:Blue; font-size:10px">
                  3.1上传附件,填写备注（非必须）。
                </div>
                <div>
                  <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                     <tr>
                       <td colspan="4">
                           <input type="file" id="UpImg1" name="UpImg1" value=" " style="width: 80%; float: left" accept="image/*" />
                           <input type="button" id="btnSBUpload" value="上传图片" name="btnSBUpload" style="float: right; width: 20%" />
                       </td>
                     </tr>
                     <tr>
                        <td  width="30%">
                           <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                              <tr>
                                <td>
                                   <label for="exampleInputPassword1">图片附件一 </label>
                                   <img id="imgs1" src="" style="width: 100%;" alt="" />
                                </td>
                              </tr>
                              <tr>
                                <td>
                                   <input type="Button" id="Btn1" value='删' onclick='delTableRowTwo(1)' style='width: 30px;  height: 30px' />
                                   <input type="text" class="form-control" id="txtPic1" name="txtPic1" style="display:none">
                                </td>
                              </tr>
                           </table>
                        </td>
                        <td width="30%">
                           <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                              <tr>
                                <td>
                                   <label for="exampleInputPassword1">图片附件二 </label>
                                   <img id="imgs2" src="" style="width: 100%;" alt="" />
                                </td>
                              </tr>
                              <tr>
                                <td>
                                   <input type="Button" id="Btn2" value='删' onclick='delTableRowTwo(2)' style='width: 50px;  height: 30px' />
                                   <input type="text" class="form-control" id="txtPic2" name="txtPic1" style="display:none">
                                </td>
                              </tr>
                           </table>
                        </td>
                        <td width="30%">
                           <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                              <tr>
                                <td>
                                   <label for="exampleInputPassword1">图片附件三 </label>
                                   <img id="imgs3" src="" style="width: 100%;" alt="" />
                                </td>
                              </tr>
                              <tr>
                                <td>
                                   <input type="Button" id="Btn3" value='删' onclick='delTableRowTwo(3)' style='width: 50px;  height: 30px' />
                                   <input type="text" class="form-control" id="txtPic3" name="txtPic3" style="display:none">
                                </td>
                              </tr>
                           </table>
                        </td>
                        <td width="10%">
                        
                        </td>
                     </tr>
                  </table>
                </div>
                <div id="Loading" style="display: none; z-index: 1000">
                   <img src="../fonts/loading.gif" width="60px" height="12px" />
                </div>
                <div class="form-group">
                    <input type="text" class="form-control" id="txtPath" name="txtPath" readonly="readonly">
                </div>
                <div>
                    <strong id="sUpMessage" style="color: Red"></strong>
                </div>
                <div>
                  <div style="color:Red; font-size:12px; font-weight:bold;">
                     上传视频：
                  </div>
                  <div>
                     <input type="file" name="fileToUpload" id="fileToUpload" onchange="fileSelected();" style="width: 80%; float: left"/>
                     <input type="button" id="btnVideoUpload" value="上传视频" name="btnVideoUpload" style="float: right; width: 20%" />
                  </div>
                  <div id="ShowVideo" style=" display:none">
                    <video width="100%" height="240" src="" id = "Video1" controls="controls">
                  </div>
                  <div>
                     <div id="fileName"></div>
                     <div id="fileSize"></div>
                     <div id="fileType"></div>  
                  </div>
                  <div class="form-group">
                    <input type="text" class="form-control" id="txtVideoPath" name="txtVideoPath" readonly="readonly">
                  </div>
                  <div id="progressNumber" style="color:Red">
                  </div>
                </div>
                <div class="form-group">
                    <label for="exampleInputPassword1">备注说明</label>
                    <textarea type="text" class="form-control" id="txtRemark" name="txtRemark"></textarea>
                </div>
                <div class="input-group">
                  <span class="input-group-addon"> 操&nbsp;&nbsp;作&nbsp;&nbsp;人</span>
                  <input type="text" class="form-control" id="txtOperateName" name="txtOperateName" readonly="readonly" />
                  <input type="text" class="form-control" id="txtOperate" name="txtOperate" readonly="readonly" />
                </div>
                <div class="input-group">
                   <span class="input-group-addon">编号</span>
                   <input type="text" class="form-control" id="txtStatus" name="txtStatus" readonly="readonly" style="width:40%"/>
                   <input type="text" class="form-control" id="txtCompanyNo" name="txtCompanyNo" readonly="readonly" style="width:20%"/>
                   <input type="text" class="form-control" id="txtNEFlag" name="txtNEFlag" readonly="readonly" style="width:20%"/>
                   <input type="text" class="form-control" id="txtInFlag" name="txtInFlag" readonly="readonly" style="width:20%"/>
                </div>
                <div>
                    <strong id="loginError" style="color: Red"></strong>
                </div>
                <p>
                </p>
                <div style="color:Blue; font-size:10px">
                  3.2、确认各节点审批人，如节点无审批人，请选择添加。
                </div>
                <p>
                </p>
                <div>
                   <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                     <tr id="N01" style="display:none">
                        <td>
                           <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                             <tr>
                                <td style="width:18px;">
                                   <img id="imgs01" src="../fonts/TB02.gif"  style="width:18px; height:18px" alt="" /> 
                                </td>
                                <td>
                                   <label id="NName1" style="margin:0px;padding-left:5px;">主管审批</label> 
                                   <span id="Add01" onclick="AudManShow_Click('01');" style="cursor:pointer;color: #09e; font-size:8px;">+审核人</span>
                                </td>
                             </tr>
                             <tr>
                               <td  style="width:18px;" align="center">
                                   <img id="img2" src="../fonts/SX02.gif" style="height:100px" alt="" /> 
                               </td>
                               <td valign="top">
                                  <div id="DIV01" class="input-group" style="display:none">
                                     <span class="input-group-addon" id="Span1">名/号</span>
                                     <input type="text" class="form-control" id="txtIn1" name="txtIn1" onblur="txt_Onblur('1')"/>
                                     <label id="sMsg1" style="font-weight:normal; font-size:8px;"></label>
                                  </div>
                                  <div >
                                     <label id="AudMan1" style="font-weight:normal; font-size:8px;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                                  <div>
                                     <label id="AudRec1" style="font-weight:normal; font-size:8px;color:Blue;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                               </td>
                             </tr>
                           </table>
                        </td>
                     </tr>
                     <tr id="N02" style="display:none">
                        <td>
                           <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                             <tr>
                                <td style="width:18px;">
                                   <img id="imgs02" src="../fonts/TB02.gif"  style="width:18px; height:18px" alt="" /> 
                                </td>
                                <td>
                                    <label id="NName2" style="margin:0px;padding-left:5px;">经理审核</label>
                                   <span id="Add02" onclick="AudManShow_Click('02');" style="cursor:pointer;color: #09e; font-size:8px; " >+审核人</span>
                                </td>
                             </tr>
                             <tr>
                               <td  style="width:18px;" align="center">
                                   <img id="img4" src="../fonts/SX02.gif" style="height:100px" alt="" /> 
                               </td>
                               <td valign="top">
                                  <div id="DIV02"  class="input-group" style="display:none">
                                     <span class="input-group-addon" id="Span02">名/号</span>
                                     <input type="text" class="form-control" id="txtIn2" name="txtIn2" onblur="txt_Onblur('2')"/>
                                  </div>
                                  <div >
                                     <label id="AudMan2" style="font-weight:normal; font-size:8px;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                                  <div>
                                    <label id="AudRec2" style="font-weight:normal; font-size:8px;color:Blue;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                               </td>
                             </tr>
                           </table>
                        </td>
                     </tr>
                     <tr id="N03" style="display:none">
                        <td>
                           <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                             <tr>
                                <td style="width:18px;">
                                   <img id="imgs03" src="../fonts/TB02.gif"  style="width:18px; height:18px" alt="" /> 
                                </td>
                                <td>
                                   <label id="NName3" style="margin:0px;padding-left:5px;">总监审批</label>
                                   <span id="Add03" onclick="AudManShow_Click('03');" style="cursor:pointer;color: #09e; font-size:8px; " >+审核人</span>
                                </td>
                             </tr>
                             <tr>
                               <td  style="width:18px;" align="center">
                                   <img id="img6" src="../fonts/SX02.gif" style="height:100px" alt="" /> 
                               </td>
                               <td valign="top">
                                  <div id="DIV03"  class="input-group" style="display:none">
                                     <span class="input-group-addon" id="Span3">名/号</span>
                                     <input type="text" class="form-control" id="txtIn3" name="txtIn3" onblur="txt_Onblur('3')"/>
                                  </div>
                                  <div >
                                     <label id="AudMan3" style="font-weight:normal; font-size:8px;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                                  <div>
                                     <label id="AudRec3" style="font-weight:normal; font-size:8px;color:Blue;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                               </td>
                             </tr>
                           </table>
                        </td>
                     </tr>
                     <tr id="N04" style="display:none">
                        <td>
                           <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                             <tr>
                                <td style="width:18px;">
                                   <img id="imgs04" src="../fonts/TB02.gif"  style="width:18px; height:18px" alt="" /> 
                                </td>
                                <td>
                                   <label id="NName4" style="margin:0px;padding-left:5px;">名/号</label>
                                   <span id="Add04" onclick="AudManShow_Click('04');" style="cursor:pointer;color: #09e; font-size:8px;" >+审核人</span>
                                </td>
                             </tr>
                             <tr>
                               <td  style="width:18px;" align="center">
                                   <img id="img8" src="../fonts/SX02.gif" style="height:100px" alt="" /> 
                               </td>
                               <td valign="top">
                                  <div id="DIV04"  class="input-group" style="display:none">
                                     <span class="input-group-addon" id="Span5">名/号</span>
                                     <input type="text" class="form-control" id="txtIn4" name="txtIn4" onblur="txt_Onblur('4')"/>
                                  </div>
                                  <div >
                                     <label id="AudMan4" style="font-weight:normal; font-size:8px;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                                  <div>
                                     <label id="AudRec4" style="font-weight:normal; font-size:8px;color:Blue;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                               </td>
                             </tr>
                           </table>
                        </td>
                     </tr>
                     <tr id="N05" style="display:none">
                        <td>
                           <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                             <tr>
                                <td style="width:18px;">
                                   <img id="imgs05" src="../fonts/TB02.gif"  style="width:18px; height:18px" alt="" /> 
                                </td>
                                <td>
                                   <label id="NName5" style="margin:0px;padding-left:5px;">总监审批</label>
                                   <span id="Add05" onclick="AudManShow_Click('05');" style="cursor:pointer;color: #09e; font-size:8px; " >+审核人</span>
                                </td>
                             </tr>
                             <tr>
                               <td  style="width:18px;" align="center">
                                   <img id="img10" src="../fonts/SX02.gif" style="height:100px" alt="" /> 
                               </td>
                               <td valign="top">
                                  <div id="DIV05"  class="input-group" style="display:none">
                                     <span class="input-group-addon" id="Span7">名/号</span>
                                     <input type="text" class="form-control" id="txtIn5" name="txtIn5" onblur="txt_Onblur('5')"/>
                                  </div>
                                  <div >
                                     <label id="AudMan5" style="font-weight:normal; font-size:8px;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                                  <div>
                                     <label id="AudRec5" style="font-weight:normal; font-size:8px;color:Blue;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                               </td>
                             </tr>
                           </table>
                        </td>
                     </tr>
                     <tr id="N06" style="display:none">
                        <td>
                           <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                             <tr>
                                <td style="width:18px;">
                                   <img id="imgs06" src="../fonts/TB02.gif"  style="width:18px; height:18px" alt="" /> 
                                </td>
                                <td>
                                   <label id="NName6" style="margin:0px;padding-left:5px;">总监审批</label>
                                   <span id="Add06" onclick="AudManShow_Click('06');" style="cursor:pointer;color: #09e; font-size:8px; " >+审核人</span>
                                </td>
                             </tr>
                             <tr>
                               <td  style="width:18px;" align="center">
                                   <img id="img12" src="../fonts/SX02.gif" style="height:100px" alt="" /> 
                               </td>
                               <td valign="top">
                                  <div id="DIV06"  class="input-group" style="display:none">
                                     <span class="input-group-addon" id="Span9">名/号</span>
                                     <input type="text" class="form-control" id="txtIn6" name="txtIn6" onblur="txt_Onblur('6')"/>
                                  </div>
                                  <div >
                                     <label id="AudMan6" style="font-weight:normal; font-size:8px;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                                  <div>
                                     <label id="AudRec6" style="font-weight:normal; font-size:8px;color:Blue;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                               </td>
                             </tr>
                           </table>
                        </td>
                     </tr>
                     <tr id="N07" style="display:none">
                        <td>
                           <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                             <tr>
                                <td style="width:18px;">
                                   <img id="imgs07" src="../fonts/TB02.gif"  style="width:18px; height:18px" alt="" /> 
                                </td>
                                <td>
                                   <label id="NName7" style="margin:0px;padding-left:5px;">总监审批</label>
                                   <span id="Add07" onclick="AudManShow_Click('07');" style="cursor:pointer;color: #09e; font-size:8px; " >+审核人</span>
                                </td>
                             </tr>
                             <tr>
                               <td  style="width:18px;" align="center">
                                   <img id="img14" src="../fonts/SX02.gif" style="height:100px" alt="" /> 
                               </td>
                               <td valign="top">
                                  <div id="DIV07"  class="input-group" style="display:none">
                                     <span class="input-group-addon" id="Span11">名/号</span>
                                     <input type="text" class="form-control" id="txtIn7" name="txtIn7" onblur="txt_Onblur('7')"/>
                                  </div>
                                  <div >
                                     <label id="AudMan7" style="font-weight:normal; font-size:8px;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                                  <div>
                                     <label id="AudRec7" style="font-weight:normal; font-size:8px;color:Blue;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                               </td>
                             </tr>
                           </table>
                        </td>
                     </tr>
                     <tr id="N08" style="display:none">
                        <td>
                           <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                             <tr>
                                <td style="width:18px;">
                                   <img id="imgs08" src="../fonts/TB02.gif"  style="width:18px; height:18px" alt="" /> 
                                </td>
                                <td>
                                   <label id="NName8" style="margin:0px;padding-left:5px;">总监审批</label>
                                   <span id="Add08" onclick="AudManShow_Click('08');" style="cursor:pointer;color: #09e; font-size:8px; " >+审核人</span>
                                </td>
                             </tr>
                             <tr>
                               <td  style="width:18px;" align="center">
                                   <img id="img16" src="../fonts/SX02.gif" style="height:100px" alt="" /> 
                               </td>
                               <td valign="top">
                                  <div id="DIV08"  class="input-group" style="display:none">
                                     <span class="input-group-addon" id="Span13">名/号</span>
                                     <input type="text" class="form-control" id="txtIn8" name="txtIn8" onblur="txt_Onblur('8')"/>
                                  </div>
                                  <div >
                                     <label id="AudMan8" style="font-weight:normal; font-size:8px;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                                  <div>
                                     <label id="AudRec8" style="font-weight:normal; font-size:8px;color:Blue;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                               </td>
                             </tr>
                           </table>
                        </td>
                     </tr>
                     <tr id="N09" style="display:none">
                        <td>
                           <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                             <tr>
                                <td style="width:18px;">
                                   <img id="imgs09" src="../fonts/TB02.gif"  style="width:18px; height:18px" alt="" /> 
                                </td>
                                <td>
                                   <label id="NName9" style="margin:0px;padding-left:5px;">总监审批</label>
                                   <span id="Add09" onclick="AudManShow_Click('09');" style="cursor:pointer;color: #09e; font-size:8px; " >+审核人</span>
                                </td>
                             </tr>
                             <tr>
                               <td  style="width:18px;" align="center">
                                   <img id="img18" src="../fonts/SX02.gif" style="height:100px" alt="" /> 
                               </td>
                               <td valign="top">
                                  <div id="DIV09"  class="input-group" style="display:none">
                                     <span class="input-group-addon" id="Span15">名/号</span>
                                     <input type="text" class="form-control" id="txtIn9" name="txtIn9" onblur="txt_Onblur('9')"/>
                                  </div>
                                  <div >
                                    <label id="AudMan9" style="font-weight:normal; font-size:8px;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                                  <div>
                                     <label id="AudRec9" style="font-weight:normal; font-size:8px;color:Blue;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                               </td>
                             </tr>
                           </table>
                        </td>
                     </tr>
                     <tr id="N10" style="display:none">
                        <td>
                           <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                             <tr>
                                <td style="width:18px;">
                                   <img id="imgs10" src="../fonts/TB02.gif"  style="width:18px; height:18px" alt="" /> 
                                </td>
                                <td>
                                   <label id="NName10" style="margin:0px;padding-left:5px;">总监审批</label>
                                   <span id="Add10" onclick="AudManShow_Click('10');" style="cursor:pointer;color: #09e; font-size:8px; " >+审核人</span>
                                </td>
                             </tr>
                             <tr>
                               <td  style="width:18px;" align="center">
                                   <img id="img20" src="../fonts/SX02.gif" style="height:100px" alt="" /> 
                               </td>
                               <td valign="top">
                                  <div id="DIV10"  class="input-group" style="display:none">
                                     <span class="input-group-addon" id="Span17">名/号</span>
                                     <input type="text" class="form-control" id="txtIn10" name="txtIn10" onblur="txt_Onblur('10')"/>
                                  </div>
                                  <div >
                                     <label id="AudMan10" style="font-weight:normal; font-size:8px;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                                  <div>
                                    <label id="AudRec10" style="font-weight:normal; font-size:8px;color:Blue;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                               </td>
                             </tr>
                           </table>
                        </td>
                     </tr>
                     <tr id="N11" style="display:none">
                        <td>
                           <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                             <tr>
                                <td style="width:18px;">
                                   <img id="imgs11" src="../fonts/TB02.gif"  style="width:18px; height:18px" alt="" /> 
                                </td>
                                <td>
                                    <label id="NName11" style="margin:0px;padding-left:5px;">总监审批</label>
                                   <span id="Add11" onclick="AudManShow_Click('11');" style="cursor:pointer;color: #09e; font-size:8px; " >+审核人</span>
                                </td>
                             </tr>
                             <tr>
                               <td  style="width:18px;" align="center">
                                   <img id="img22" src="../fonts/SX02.gif" style="height:100px" alt="" /> 
                               </td>
                               <td valign="top">
                                  <div id="DIV11"  class="input-group" style="display:none">
                                     <span class="input-group-addon" id="Span19">名/号</span>
                                     <input type="text" class="form-control" id="txtIn11" name="txtIn11" onblur="txt_Onblur('11')"/>
                                  </div>
                                  <div >
                                     <label id="AudMan11" style="font-weight:normal; font-size:8px;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                                  <div>
                                     <label id="AudRec11" style="font-weight:normal; font-size:8px;color:Blue;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                               </td>
                             </tr>
                           </table>
                        </td>
                     </tr>
                     <tr id="N12" style="display:none">
                        <td>
                           <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                             <tr>
                                <td style="width:18px;">
                                   <img id="imgs12" src="../fonts/TB02.gif"  style="width:18px; height:18px" alt="" /> 
                                </td>
                                <td>
                                    <label id="NName12" style="margin:0px;padding-left:5px;">总监审批</label>
                                   <span id="Add12" onclick="AudManShow_Click('12');" style="cursor:pointer;color: #09e; font-size:8px; " >+审核人</span>
                                </td>
                             </tr>
                             <tr>
                               <td  style="width:18px;" align="center">
                                   <img id="img24" src="../fonts/SX02.gif" style="height:100px" alt="" /> 
                               </td>
                               <td valign="top">
                                  <div id="DIV12"  class="input-group" style="display:none">
                                     <span class="input-group-addon" id="Span21">名/号</span>
                                     <input type="text" class="form-control" id="txtIn12" name="txtIn12" onblur="txt_Onblur('12')"/>
                                  </div>
                                  <div >
                                     &<label id="AudMan12" style="font-weight:normal; font-size:8px;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                                  <div>
                                     <label id="AudRec12" style="font-weight:normal; font-size:8px;color:Blue;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                               </td>
                             </tr>
                           </table>
                        </td>
                     </tr>
                     <tr id="N13" style="display:none">
                        <td>
                           <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                             <tr>
                                <td style="width:18px;">
                                   <img id="imgs13" src="../fonts/TB02.gif"  style="width:18px; height:18px" alt="" /> 
                                </td>
                                <td>
                                   <label id="NName13" style="margin:0px;padding-left:5px;">总监审批</label>
                                   <span id="Add13" onclick="AudManShow_Click('13');" style="cursor:pointer;color: #09e; font-size:8px; " >+审核人</span>
                                </td>
                             </tr>
                             <tr>
                               <td  style="width:18px;" align="center">
                                   <img id="img26" src="../fonts/SX02.gif" style="height:100px" alt="" /> 
                               </td>
                               <td valign="top">
                                  <div id="DIV13"  class="input-group" style="display:none">
                                     <span class="input-group-addon" id="Span23">名/号</span>
                                     <input type="text" class="form-control" id="txtIn13" name="txtIn13" onblur="txt_Onblur('13')"/>
                                  </div>
                                  <div >
                                     <label id="AudMan13" style="font-weight:normal; font-size:8px;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                                  <div>
                                     <label id="AudMan13" style="font-weight:normal; font-size:8px;color:Blue;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                               </td>
                             </tr>
                           </table>
                        </td>
                     </tr>
                     <tr id="N14" style="display:none">
                        <td>
                           <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                             <tr>
                                <td style="width:18px;">
                                   <img id="imgs14" src="../fonts/TB02.gif"  style="width:18px; height:18px" alt="" /> 
                                </td>
                                <td>
                                   <label id="NName14" style="margin:0px;padding-left:5px;">总监审批</label>
                                   <span id="Add14" onclick="AudManShow_Click('14');" style="cursor:pointer;color: #09e; font-size:8px; " >+审核人</span>
                                </td>
                             </tr>
                             <tr>
                               <td  style="width:18px;" align="center">
                                   <img id="img28" src="../fonts/SX02.gif" style="height:100px" alt="" /> 
                               </td>
                               <td valign="top">
                                  <div id="DIV14"  class="input-group" style="display:none">
                                     <span class="input-group-addon" id="Span25">名/号</span>
                                     <input type="text" class="form-control" id="txtIn14" name="txtIn14" onblur="txt_Onblur('14')"/>
                                  </div>
                                  <div >
                                     <label id="AudMan14" style="font-weight:normal; font-size:8px;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                                  <div>
                                     <label id="AudRec14" style="font-weight:normal; font-size:8px;color:Blue;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                               </td>
                             </tr>
                           </table>
                        </td>
                     </tr>
                     <tr id="N15" style="display:none">
                        <td>
                           <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                             <tr>
                                <td style="width:18px;">
                                   <img id="imgs15" src="../fonts/TB02.gif"  style="width:18px; height:18px" alt="" /> 
                                </td>
                                <td>
                                   <label id="NName15" style="margin:0px;padding-left:5px;">总监审批</label>
                                   <span id="Add15" onclick="AudManShow_Click('15');" style="cursor:pointer;color: #09e; font-size:8px; " >+审核人</span>
                                </td>
                             </tr>
                             <tr>
                               <td  style="width:18px;" align="center">
                                   <img id="img30" src="../fonts/SX02.gif" style="height:100px" alt="" /> 
                               </td>
                               <td valign="top">
                                  <div id="DIV15"  class="input-group" style="display:none">
                                     <span class="input-group-addon" id="Span27">名/号</span>
                                     <input type="text" class="form-control" id="txtIn15" name="txtIn15" onblur="txt_Onblur('15')"/>
                                     <label id="sMsg15" style="font-weight:normal; font-size:8px;"></label>
                                  </div>
                                  <div >
                                     <label id="AudMan15" style="font-weight:normal; font-size:8px;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                                  <div>
                                     <label id="AudRec15" style="font-weight:normal; font-size:8px;color:Blue;margin:0px;padding-left:5px;">dylen</label>
                                  </div>
                               </td>
                             </tr>
                           </table>
                        </td>
                     </tr>
                   </table>
                </div>
                <p>
                </p>
                <div style="color:Blue">
                  4、提交给上级领导审批。
                </div>
                <button type="button" id="btnBSDataSubmit" name="btnBSDataSubmit" class="btn btn-primary" style="width: 99%"> 提交</button>
                <div id="div_warning" class="alert alert-warning" role="alert" style="text-align: center; display: none">
                    <strong id="sMessage"></strong>
                </div>
            </div>
        </div>
     </div>   
    </form>
    
    <script type="text/javascript">
        function delTableRow(i) {
            //  alert(i.parentNode.childNodes[0].alt);
            var imgSrc = i.parentNode.childNodes[0].childNodes[0].alt;
            //            alert(imgSrc);

            var sPath = $("#txtPath").val();
            $("#txtPath").val(sPath.replace(imgSrc, ''));

            i.parentNode.innerHTML = "";
        }

        function delTableRowTwo(i) {
            //  alert(i.parentNode.childNodes[0].alt);
            //var imgSrc = i.parentNode.childNodes[0].childNodes[0].alt;
            //            alert(imgSrc);
            var sPath = $("#txtPath").val();
            var sP = $("#txtPic" + i).val();
            $("#txtPath").val(sPath.replace(sP, ''));

            $("#txtPic" + i).val('');
            // $("#txtPath").val(sPath.replace(imgSrc, ''));
            $("#txtPic" + i).val('');
            $("#imgs" + i).attr("src", '');
            $("#imgs" + i).attr("alt", '');
            // $("#Btn" + i).css({ "display": "none" });
            $("#txtPic" + i).css({ "display": "none" });

        }
        
        
    </script>
    
</body>
</html>