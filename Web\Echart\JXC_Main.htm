<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>看板</title>
    <script src="./js/jquery-3.3.1.min.js"></script>
    <link href="../Echart/css/XC.css" rel="stylesheet" />
    <link href="../Echart/css/layui.css" rel="stylesheet" />
    <script src="../Echart/js/layui.js"></script>


    <script>
        var sLogin = "";
        var sCorpID = "";
        var sCName = ""
        $.ajax({
            url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);
                if (parsedJson.Msg == 'loginError') {
                    var Num = parseInt(Math.random() * 1000);
                    window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                }
                else {
                    sLogin = parsedJson.Man
                }
            }
        })

        var index = 0;
        layui.use(function () {

            updateDateTime();
            GetDeptInfoList("")
            var data = GetTempInfoList(layui)
            var IntervalData = null
            var IntervalPage = null


            $("#ConditionCheckSubmit").click(async function () {
                //初始化定时任务
                clearInterval(IntervalData);
                clearInterval(IntervalPage);
                index = 0;

                const department = $("#department").val();
                const temp = data.getData('ID-transfer-demo');
                const rate = $("#rate").val()
                const startDate = new Date($("#startDate").val())
                const endDate = new Date($("#endDate").val())
                // 计算日期间隔的毫秒数差值
                const timeDiff = endDate.getTime() - startDate.getTime();
                const day = (timeDiff / (1000 * 3600 * 24)) > 30;

                if (department == "" || department == null) {
                    layer.msg("请选择部门！")
                    return
                } else if (temp.length < 1 || temp.length > 3) {
                    layer.msg("看板数量至少选择一个，并且不能超过三个！")
                    return
                } else if (day) {
                    layer.msg("日期天数不能超过一个月")
                    return
                }

                $("#loading").show()

                fetchAndUpdateData(temp)

                IntervalUpdatePage(temp)

                // 定时更新数据
                IntervalData = setInterval(function () {
                    // 调用 fetchAndUpdateData 但不等待它完成
                    fetchAndUpdateData(temp)

                }, 30000);



                IntervalPage = setInterval(function () {
                    IntervalUpdatePage(temp)
                }, rate)

                CloseModal()
                $("#loading").hide()
            })

            $("#department").change(function () {
                const department = $("#department").val();
                GetDeptInfoListId(department)
            })
        })

        // 定时任务函数
        function fetchAndUpdateData(temp) {

            const department = $("#department").val();
            const startDate = $("#startDate").val() + " 00:00:00";
            const endDate = $("#endDate").val() + " 23:59:59";


            const flag = temp.map(item => item.title.substring(1, item.title.indexOf(")"))).join(',');

            const Params = { No: "", Name: "", Item: "", MNo: "", MName: "", Status: "", BDate: startDate, EDate: endDate, A: "", B: "", C: "", D: "", E: "", F: "", G: "", H: "", I: "", J: "", K: sLogin, L: department, Remark: "", Flag: flag };
            const Data = JSON.stringify(Params);

            $.ajax({
                url: "../Service/EchartAjax.ashx?OP=GetEchartData&Data=" + encodeURI(Data),
                type: "GET",
                data: {},
                async: false,
                success: function (response) {
                    const jsonData = JSON.parse(response)
                    Object.keys(jsonData).forEach(function (key) {
                        sessionStorage.setItem(key, JSON.stringify(jsonData[key]));
                    });
                },
                error: function (xhr, status, error) {
                    // 请求失败时的回调
                    console.error("网络请求失败:", status, error);
                },
            });

            //  API URL
            //const apiUrl = "../Service/EchartAjax.ashx?OP=GetEchartData&Data=" + encodeURI(Data);

            //const response = await fetch(apiUrl);

            //if (response.ok) {
            //    const data = await response.json();
            //    Object.keys(data).forEach(function (key) {
            //        sessionStorage.setItem(key, JSON.stringify(data[key]));
            //    });
            //} else {
            //    console.log('网络请求失败: ' + response.status + "," + response.statusText)
            //}
        }

        function IntervalUpdatePage(temp) {

            $("#templet").css("display", "block")
            var sTName = ""

            if (temp.length > 1) {

                index = (index + 1) % temp.length;
                sTName = temp[index].title.substring(temp[index].title.indexOf(")") + 1)

                $("#top-titile").html(sCName + sTName)

                $("#templet").prop("src", temp[index].value)

            } else {
                sTName = temp[index].title.substring(temp[index].title.indexOf(")") + 1)
                $("#top-titile").html(sCName + sTName)
                $("#templet").prop("src", temp[index].value)
            }
        }


        // 下拉选择系统类别--部门信息
        function GetDeptInfoList(CNo) {

            var CKind = encodeURI("部门信息");

            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/BaseModuleAjax.ashx?OP=GetDeptInfoList&CFlag=41&CMNO=" + CNo + "&CKind=" + CKind,
                success: function (data) {
                    var parsedJson = eval(data).Msg;

                    var sKong = "<option value=''> </option>";
                    $("#department").empty();
                    $("#department").append(sKong + parsedJson);
                }
            });
        }

        function GetDeptInfoListId(CNo) {



            var Params = { No: CNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);

            $.ajax({
                type: "get",
                url: "../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=12&&page=1&limit=1&Data=" + Data,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.data.length > 0) {
                        sCName = parsedJson.data[0].DeptName
                    }
                }
            });
        }

        //获取所有的模板
        function GetTempInfoList(layui) {
            var Data = '';
            var Params = { No: "", Name: "", Item: "", MNo: "", MName: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "", E: "", F: "", G: "", H: "", Remark: "", Flag: "100" };
            var Data = JSON.stringify(Params);
            var transfer = layui.transfer;
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/EchartAjax.ashx?OP=GetTemplateBase&page=1&limit=100&Data=" + Data,
                success: function (res) {
                    // 渲染
                    transfer.render({
                        elem: '#ID-transfer-demo',
                        id: "ID-transfer-demo",
                        title: ['未选', '已选'],
                        data: res.data,
                        parseData: function (res) {//数据源解析
                            return {
                                "value": res.BoardPath //数据值
                                , "title": '(' + res.BoardNo + ')' + res.BoardName //数据标题
                                , "disabled": res.disabled  //是否禁用
                                , "checked": res.checked //是否选中
                            }
                        }

                    });
                }
            });
            // 获得右侧数据
            return transfer;
        }

        //数据格式化
        function formatNum(str) {
            var newStr = "";
            var count = 0;
            if (str.indexOf(".") == -1) {
                for (var i = str.length - 1; i >= 0; i--) {
                    if (count % 3 == 0 && count != 0) {
                        newStr = str.charAt(i) + "," + newStr;
                    } else {
                        newStr = str.charAt(i) + newStr;
                    }
                    count++;
                }
                str = newStr;
                return str;
            }

        }

        // 更新日期和时间的函数
        function updateDateTime() {
            var now = new Date();

            // 获取年、月、日
            var year = now.getFullYear();
            var month = (now.getMonth() + 1).toString().padStart(2, '0');
            var date = now.getDate().toString().padStart(2, '0');

            // 获取星期
            var daysOfWeek = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
            var dayOfWeek = daysOfWeek[now.getDay()];

            // 获取时、分、秒
            var hours = now.getHours().toString().padStart(2, '0');
            var minutes = now.getMinutes().toString().padStart(2, '0');
            var seconds = now.getSeconds().toString().padStart(2, '0');

            // 拼接成字符串
            var currentDateTimeString = year + '/' + month + '/' + date + ' ' + dayOfWeek + ' ' + hours + ':' + minutes + ':' + seconds;

            // 显示在网页上
            document.getElementById('currentDateTime').textContent = currentDateTimeString;
        }

        // 每秒更新时间
        setInterval(updateDateTime, 1000);

        //打开条件框
        function OpenModal() {
            $("#ShowOne").css("display", "block")
            $("#ShowOne-fade").css("display", "block")

            // 获取当前日期
            let currentDate = new Date();
            let currentDateString = currentDate.toISOString().split('T')[0];

            // 初始化开始日期和结束日期
            let startDateInput = document.getElementById('startDate');
            let endDateInput = document.getElementById('endDate');

            endDateInput.value = currentDateString
            startDateInput.value = (new Date(currentDate.getTime() - (7 * 24 * 60 * 60 * 1000))).toISOString().split('T')[0];

            //endDateInput.value = "2024-08-10"
            //startDateInput.value = "2024-07-15"
        }

        //关闭条件框
        function CloseModal() {
            $("#ShowOne").css("display", "none")
            $("#ShowOne-fade").css("display", "none")
        }


    </script>

    <style>
        body {
            display: block;
            margin: 0px;
        }

        #container {
            height: 100vh;
            background: #15181d;
            overflow: hidden;
            position: relative;
            color: #ffffff;
        }

        .top-titile {
            font-family: 黑体;
            height: 100%;
            font-size: 28px;
            text-align: center;
            font-weight: bold;
        }

        .option select {
            border: 1px solid white;
            color: black;
            width: 200px;
            padding: 2px 5px;
            font-size: 11px;
            border-radius: 1px;
            height: 28px;
        }

            .option select:focus {
                outline: none;
            }

        .option input[type="checkbox"] {
            vertical-align: middle;
            width: 14px;
            height: 14px;
        }

        .page_top {
            height: 6%;
            display: flex;
            /*            flex-wrap: nowrap;
            justify-content: space-between;*/
            align-items: center;
            padding: 0px 20px;
        }

        #templet {
            width: 100%;
            height: calc(100vh - 56px);
        }

        .XC-Span-Select-block {
            line-height: 30px;
        }

        .layui-form-checkbox > div {
            padding: 0 11px;
            font-size: 12px;
            border-radius: 2px 0 0 2px;
            background-color: #d2d2d2;
            color: #fff;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        /* 自定义滚动条样式 */
        .layui-transfer-data::-webkit-scrollbar {
            width: 6px;
        }

        /* 滚动条滑块样式 */
        .layui-transfer-data::-webkit-scrollbar-thumb {
            background-color: #d2d2d2;
            border-radius: 5px;
        }

        .top_left, .top_center, .top_right {
            flex: 1
        }

        .top_right {
            text-align: right
        }
    </style>
</head>

<body>
    <div id="container">
        <div class="page_top">
            <div class="top_left">
                <button type="button" class="XC-Btn-Gray XC-Btn-md XC-Size-sm" onclick="OpenModal()">设置</button>
            </div>
            <div class="top_center">
                <div class="top-titile" id="top-titile"></div>
            </div>
            <div class="top_right">
                <span id="currentDateTime"></span>
            </div>
        </div>
        <iframe id="templet" src="" frameborder="0" data-id="0" style="display:none"></iframe>
    </div>

    <div class="XC-modal XC-modal-md" id="ShowOne" style="height:650px;width: 900px">
        <div class="XC-modal-head" style="background-color: #15181d;color: white;">
            <span class="head-title" id="head-title1">设置条件</span>
            <span class="head-close" onclick="CloseModal()">X</span>
        </div>
        <div class="XC-modal-body">
            <form class="XC-Form-block">
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">部门</span>
                    <select class="XC-Select-block" id="department">
                    </select>
                </div>

                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">看板</span>
                    <div id="ID-transfer-demo"></div>
                </div>

                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">频率</span>
                    <select class="XC-Select-block" id="rate">
                        <option value="20000">20秒</option>
                        <option value="40000" selected>40秒</option>
                        <option value="60000">1分钟</option>
                        <option value="180000">3分钟</option>
                        <option value="300000">5分钟</option>
                        <option value="600000">10分钟</option>
                        <option value="1800000">30分钟</option>
                    </select>
                </div>
                <div class="XC-Form-block-Item">
                    <span class="XC-Span-Select-block">日期</span>
                    <input type="date" class="XC-Input-block" id="startDate" />
                    <span style="display: inline-block;height: 30px;line-height: 30px;margin: 0px 10px;">--</span>
                    <input type="date" class="XC-Input-block" id="endDate" />
                </div>
            </form>
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="ConditionCheckSubmit">确定</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="BatchSerialClose"
                        onclick='CloseModal()'>
                    关闭
                </button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>

    <div id="loading" class="black_overlay" style="z-index: 1008; ">
        <div style="position: fixed;  top: 50%;  left: 50%;  transform: translate(-50%, -50%); ">
            <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
        </div>
        <div class="docs-icon-name" style="position: fixed;  top: 55%;  left: 50%;  transform: translate(-46.5%, -50%);font-size:12px ">正在加载中，请稍等！</div>
    </div>
</body>

</html>