﻿using System;
using System.Web;
using grsvr6Lib;

namespace Web.appcode
{
    public class ReportGenerator
    {
        protected HttpContext _context = null;

        public GridppReportServer report = new GridppReportServer();

        public HttpContext context
        {
            get
            {
                return _context;
            }
        }

        public ReportGenerator(HttpContext context)
        {
            this._context = context;
        }

        //根据HTTP请求中的 report 参数加载报表模板
        public void LoadReport()
        {
            string reportID = _context.Request.QueryString["report"];

            if (reportID == null || reportID.Length == 0)
                throw new Exception("没有在URL参数中指定\"report\"参数！");

            //载入报表模板。模板路径获取应该按实际情况进行调整
            string reportPathFile = _context.Server.MapPath("../../grf/") + reportID + ".grf"; //根据当前页进行相对寻址
            //string reportPathFile = _context.Server.MapPath("/grf/") + reportID + ".grf";     //根据WEB服务器根目录寻址
            LoadReportEx(reportPathFile);
        }

        //根据路径文件名加载报表模板，参数为当前网页的相对文件路径名
        public void LoadReport(string relativePathFile)
        {
            string reportPathFile = _context.Server.MapPath(relativePathFile);
            LoadReportEx(reportPathFile);
        }

        //根据完整的路径文件名加载报表模板
        public void LoadReportEx(string fullPathFile)
        {
            bool success = report.LoadFromFile(fullPathFile);
            if (!success)
                throw new Exception(string.Format("载入报表模板 '{0}' 失败！", fullPathFile));

            //如果要禁止用拉模式获取报表数据，需要将报表模板中的数据连接串置空
            //如果确实要用拉模式获取报表数据，请将以下代码注释掉
            //report.ConnectionString = "";
            //if (report.DetailGrid != null)
            //    report.DetailGrid.Recordset.ConnectionString = "";
        }

        //从 XML 或 JSON 文本数据包加载报表数据。数据形式必须满足Grid++report的约定要求。
        public void LoadReportData(string DataText)
        {
            bool success = report.LoadDataFromXML(DataText);
            if (!success)
                throw new Exception(string.Format("载入报表数据:\r\n '{0}' \r\n失败！", DataText));
        }

        //生成报表结果到二进制数据包对象中，并将数据响应给请求的客户端
        //HTTP请求中包含的参数：
        //report: 指定哪个报表
        //type: 指定生成的数据类型，可选[pdf|xls|csv|txt|rtf|img|grd|grp]。如果不指定，默认为pdf
        //img: 指定生成的图像数据格式，仅当生成图像数据时需要，可选[png|bmp|jpg|tif]。如果不指定，默认为png
        //open: 指定生成的数据打开模式，可选[inline|attachment]，"inline"表示在网页中内联显示，"attachment"表示以附件文件形式下载。如果不指定，由浏览器自动确定打开方式
        //filename: 指定下载(或保存)文件时的默认文件名称
        public void Generate()
        {
            string TypeText = _context.Request.QueryString["type"];
            string ImageTypeText = _context.Request.QueryString["img"];
            string FileName = _context.Request.QueryString["filename"];
            string OpenMode = _context.Request.QueryString["open"];

            Generate(TypeText, FileName, OpenMode, ImageTypeText);
        }

        public void Generate(string TypeText, string FileName)
        {
            Generate(TypeText, FileName, "", "");
        }

        public void Generate(string TypeText, string FileName, string OpenMode, string ImageTypeText)
        {
            //确定导出数据类型及数据的ContentType
            ReportGenerateInfo GenerateInfo = new ReportGenerateInfo();
            GenerateInfo.Build(TypeText, ImageTypeText);

            IGRBinaryObject ResultDataObject;
            if (GenerateInfo.IsGRD)
            {
                ResultDataObject = report.GenerateDocumentData();
            }
            else
            {
                IGRExportOption ExportOption = report.PrepareExport(GenerateInfo.ExportType);

                if (GenerateInfo.ExportType == GRExportType.gretIMG)
                {
                    IGRE2IMGOption E2IMGOption = ExportOption.AsE2IMGOption;
                    E2IMGOption.ImageType = GenerateInfo.ImageType;
                    E2IMGOption.AllInOne = true; //所有页产生在一个图像文件中
                    //E2IMGOption.VertGap = 20;    //页之间设置20个像素的间距
                }

                ResultDataObject = report.ExportToBinaryObject();
                report.UnprepareExport();
            }

            #region 响应生成的报表结果数据
            //如果参数中没指定文件名，则用报表模板中的“标题”属性设置一个默认文件名
            if (FileName == null || FileName.Length == 0)
            {
                if (report.Title == null || report.Title.Length == 0)
                    FileName = "gridreport";
                else
                    FileName = report.Title;
                FileName += "." + GenerateInfo.ExtFileName;
            }

            ServerUtility.ResponseBinary(_context, ResultDataObject, FileName, "", "", "", "", "", "", "", "", "", "","", "", GenerateInfo.ContentType, OpenMode, "");
            #endregion
        }
    }
}
