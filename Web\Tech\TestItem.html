﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>测试项库</title>
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <script src="../js/ajaxfileupload.js" type="text/javascript"></script>
    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all">
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/Tech.js" type="text/javascript"></script>

    <link href="../css/XC.css" rel="stylesheet" />


    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        $('#txtInMan').val(parsedJson.Man);
                        $('#txtInName').val(parsedJson.InName);

                        GetProcedureList(); // 加载工序
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=28-1' });
                    }
                }
            })
        });



    </script>


    <script type="text/javascript">
        //获取当前所在的模块、菜单
        const currentTabId = sessionStorage.getItem("currentTabId");
        const ModuleName = currentTabId != null ? JSON.parse(currentTabId).ModuleName : null;

        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            var temp_table_list = [];
            var temp_all_list = [];
            table.render({
                elem: '#TestItemlist',
                id: 'TestItemID',
                url: '../Service/TechAjax.ashx?OP=GetTechInfo&CFlag=117',
                height: 'full-80',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 100, //数据总数 服务端获得
                limit: 100, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [20, 50, 100, 150, 200, 250, 300], //分页显示每页条目下拉选择
                cols: [[
                    { type: "checkbox" },
                    { type: 'numbers', title: '序号', width: 40,},
                    { field: 'TechNo', title: '产品代号', width: 100, sort: true },
                    { field: 'SpecNo', title: '测试项编号', width: 100, sort: true },
                    { field: 'Model', title: '适用型号', width: 180, sort: true },
                    { field: 'ProcedureNo', title: '工序编号', width: 100, sort: true },
                    { field: 'ProcedureName', title: '工序名称', width: 120 },
                    { field: 'NameCH', title: '检验项目', width: 200 },
                    { field: 'DescCH', title: '检验要求', width: 300 },
                    { field: 'ValueKind', title: '数据类型', width: 80 },
                    { field: 'RangeKind', title: '区间', width: 70 },
                    { field: 'ThisValue', title: '下限', width: 70 },
                    { field: 'ToValue', title: '上限', width: 70 },
                    { field: 'SpecUnit', title: '单位', width: 70 },
                    { field: 'Remark', title: '备注', width: 150 },
                    { field: 'InMan', title: '录入人', width: 90 },
                    { field: 'InDate2', title: '录入时间', width: 180 },
                    { field: 'op', title: '操作', width: 120, toolbar: '#barDemo', fixed: 'right' }
                ]],
                page: true,
                done: function (res) {
                    temp_table_list = res.data;
                    temp_table_list.forEach(function (o) {
                        temp_all_list.forEach(function (selected) {
                            if (selected.SpecNo === o.SpecNo) {
                                o["LAY_CHECKED"] = true;
                                var index = o['LAY_TABLE_INDEX'];
                                $('.layui-table tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', true).next().addClass('layui-form-checked');
                            }
                        });
                    });
                }
            });





            table.on('checkbox(TestItemlist)', function (obj) {
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID
                if (obj.checked) {
                    if (obj.type === 'one') {
                        var index = temp_all_list.findIndex(function (item) {
                            return item.SpecNo == obj.data.SpecNo;
                        });
                        if (index == -1) {
                            temp_all_list.push(obj.data);
                        }
                    } else {
                        temp_table_list.forEach(function (o) {
                            if (temp_all_list.findIndex(function (item) { return item.SpecNo == o.SpecNo; }) == -1) {
                                temp_all_list.push(o);
                            }
                        });
                    }

                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    //$(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                } else {
                    if (obj.type === 'one') {
                        temp_all_list = temp_all_list.filter(function (item) {
                            return item.SpecNo !== obj.data.SpecNo;
                        });
                    } else {
                        for (var i = 0; i < temp_table_list.length; i++) {
                            for (var j = 0; j < temp_all_list.length; j++) {
                                if (temp_table_list[i].SpecNo == temp_all_list[j].SpecNo) {
                                    temp_all_list.splice(j, 1)
                                }
                            }
                        }
                    }
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    //$(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
            });


            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(TestItemlist)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

            });

            //监听单元格编辑
            table.on('edit(TestItemlist)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });


            //监听行工具事件
            table.on('tool(TestItemlist)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值

                if (layEvent === 'del') {

                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("您确定要删除该测试项目吗？ 产品代号：")
                    //设置删除的对象
                    $("#hint-value").html(data.TechNo)

                    $("#txtTechNo").val(data.TechNo)
                    $("#txtProcedureNo").val(data.ProcedureNo)
                    $("#txtProcedureName").val(data.ProcedureName)
                    $("#txtSpecNo").val(data.SpecNo)
                    $("#txtFlag").val("28-3")

                } else if (layEvent === 'edit') {
                    $('.head-title1').html("修改测试项信息");
                    $('#txtAEFlag').val("28-2");

                    $("#txtTechNo").val(data.TechNo);
                    $("#txtModel").val(data.Model);
                    $("#txtProcNo").val("(" + data.ProcedureNo + ")" + data.ProcedureName);
                    $("#txtSeqNo").val(data.SeqNo);  // 唯一码，暂时不用
                    $("#txtSpecNo").val(data.SpecNo);
                    $("#txtNameCH").val(data.NameCH);  //
                    $("#txtDescCH").val(data.DescCH); //
                    $("#txtStandardCH").val(data.StandardCH);
                    $("#txtUnit").val(data.SpecUnit);
                    $("#txtBaseValueKind").val(data.ValueKind);
                    $("#txtBaseRangeKind").val(data.RangeKind);   // 请填写区间值
                    $("#txtDownV").val(data.ThisValue);   // 下限
                    $("#txtUpV").val(data.ToValue);   // 上限值

                    $("#txtEditKind").val(data.ProcedureNo)

                    $("#CH_Down").removeProp("checked"); //设置为不选中状态
                    $("#CH_Up").removeProp("checked"); //设置为不选中状态

                    $("#div_QJ").hide();
                    if (data.ValueKind == "数值类型") {
                        $("#div_QJ").show();

                        if (data.IncludeOne == "1") {  //  包含下限值
                            $("#CH_Down").prop("checked", "checked");
                        }
                        if (data.IncludeTwo == "1") {  //  包含下限值
                            $("#CH_Up").prop("checked", "checked");
                        }

                        $("#CH_Down").hide();
                        $("#L_BHD").hide();
                        $("#txtDownV").hide();
                        $("#L_Down").hide();
                        $("#L_Deng").hide();
                        $("#L_Up").hide();
                        $("#txtUpV").hide();
                        $("#CH_Up").hide();
                        $("#L_BHU").hide();
                        if (data.RangeKind == "大于") {
                            $("#CH_Down").show();
                            $("#L_BHD").show();
                            $("#txtDownV").show();
                            $("#L_Down").show();
                        }
                        else if (data.RangeKind == "小于") {
                            $("#L_Up").show();
                            $("#txtUpV").show();
                            $("#CH_Up").show();
                            $("#L_BHU").show();
                        }
                        else if (data.RangeKind == "介于") {
                            $("#CH_Down").show();
                            $("#L_BHD").show();
                            $("#txtDownV").show();
                            $("#L_Down").show();
                            $("#L_Up").show();
                            $("#txtUpV").show();
                            $("#CH_Up").show();
                            $("#L_BHU").show();
                        }
                        else if (data.RangeKind == "等于") {
                            $("#L_Deng").show();
                            $("#txtUpV").show();
                        }
                    }

                    $("#txtTechNo").attr({ "disabled": "disabled" });

                    $('#ShowOne').css("display", "block");
                    $('#ShowTow-fade').css("display", "block");

                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }





            });


            //  查询 --
            $('#BaseTestItemBut_open').click(function () {

                var sTechNo = $("#txtSTechNo").val();  //工序编号
                var sTNo = $("#txtSProcNo").val();  //工序  (05)总
                var sGXNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置
                var sGXName = sTNo.substr(sTNo.indexOf(")") + 1, sTNo.length);
                var sModel = $("#txtSModel").val();  //型号

                var Data = '';
                var Params = { No: sTechNo, Item: sGXNo, Name: sModel, MNo: "", MName: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "" };
                var Data = JSON.stringify(Params);

                table.reload('TestItemID', {
                    method: 'post',
                    url: '../Service/TechAjax.ashx?OP=GetTechInfo&CFlag=117&Data=' + Data,
                    where: {
                        'No': sTechNo,
                        'name': sModel
                    }, page: {
                        curr: 1
                    }
                });
            });

            $("#BatchDeleteTestItem").click(function () {
                if (temp_all_list.length == 0) {
                    layer.msg("请选择要删除的测试项")
                    return;
                } else if (temp_all_list.length > 100) {
                    layer.msg("单次最多只能删除一百项")
                    return;
                }

                $("#ShowDel").css("display", "block")
                $("#ShowDel-fade").css("display", "block")

                $("#XC-Icon").removeClass()
                $("#hint-value").removeClass()
                $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                $("#hint-value").addClass("XC-Font-Red")

                //设置删除的标题
                $("#hint-title").html("确定要删除吗？ 已选：")
                //设置删除的对象
                $("#hint-value").html(temp_all_list.length)
                $("#txtFlag").val("28-4")
            })

            $("#TestItemBaseDel_Btn").click(function () {
                //向服务端发送禁用指令
                var sTech = $("#txtTechNo").val();
                var sGX = $("#txtProcedureNo").val();
                var sGXName = $("#txtProcedureName").val();
                var sSpec = $("#txtSpecNo").val();
                var sFlag = $("#txtFlag").val();

                //删除多个
                if (sFlag == "28-4") {
                    var No = "";
                    for (var i = 0; i < temp_all_list.length; i++) {
                        No += (i > 0 ? "," : "") + "'" + temp_all_list[i].SpecNo + "'";
                    }
                    sSpec = No
                }

                var Data = '';
                var Params = { No: sTech, Name: sGXName, Item: sGX, MNo: "", MName: "", A: "", B: "", C: "", D: "", E: "", F: "", G: "", H: "", I: "", J: sSpec, K: "", L: "", M: "", N: "", O: "", P: ModuleName, Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);

                $.ajax({
                    type: "POST",
                    url: "../Service/TechAjax.ashx?OP=OPTechInfo&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('删除成功！');
                            $("#ShowDel").css("display", "none")
                            $("#ShowDel-fade").css("display", "none")
                            $('#BaseTestItemBut_open').click();  // 重新查询

                            if (sFlag == "28-4") {
                                temp_all_list = []
                            }
                        }
                        else {
                            layer.msg('删除失败，请重试！');
                            $('#BaseTestItemBut_open').click();  // 重新查询
                        }
                    },
                    error: function (data) {
                        layer.msg('删除失败2，请重试！');
                        $('#BaseTestItemBut_open').click();  // 重新查询
                    }
                });
            })

        });




        function openDialog(n) {  // 新增
            $('.head-title1').html("新增测试项信息");
            $('#txtAEFlag').val("28-1");


            $("#txtTechNo").val("");
            $("#txtModel").val("");
            $("#txtProcNo").val("");
            $("#txtNameCH").val("");  //  检验项目
            $("#txtDescCH").val(""); //  检验描述
            $("#txtStandardCH").val("");  //  要求 -- 231029：不需要维护这个，和描述一起即可
            $("#txtBaseValueKind").val("");  // 数据类型
            $("#txtUnit").val("");
            $("#div_QJ").hide();

            $("#CH_Down").hide();
            $("#L_BHD").hide();
            $("#txtDownV").hide();
            $("#L_Down").hide();
            $("#L_Deng").hide();
            $("#L_Up").hide();
            $("#txtUpV").hide();
            $("#CH_Up").hide();
            $("#L_BHU").hide();

            $("#txtTechNo").removeAttr("disabled");

            $('#ShowOne').css("display", "block");
            $('#ShowTow-fade').css("display", "block");
        }




        function GetProcedureList() {  // 加载工序

            var sSs = "工序";

            var keywords = encodeURI(sSs);  // 解决中文乱码的问题。 txtSProcNo
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/TechAjax.ashx?OP=GetProcedureList&CFlag=111-1&CKind=" + keywords,
                success: function (data) {
                    var parsedJson = eval(data).Msg;

                    var sKong = "<option value=''> </option>";
                    $("#txtProcNo").empty();
                    $("#txtProcNo").append(sKong + parsedJson);

                    $("#txtSProcNo").empty();
                    $("#txtSProcNo").append(sKong + parsedJson);

                    $('#txtProcNo').val($('#txtProcNoR').val());
                }
            });
        }



        function closeDialog() {
            $('#ShowOne').css("display", "none");
            $('#ShowTow-fade').css("display", "none");
            $("#ShowDel").css("display", "none")
            $("#ShowDel-fade").css("display", "none")

        }



    </script>


    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

        })



    </script>

    <style type="text/css">

                #HDiv {
                    position: fixed;
                    top: 0;
                    width: 100%;
                    height: 40px;
                }

                .table > tbody > tr > td {
                    border: 0px;
                }

                /*  .black_overlay {
                    display: none;
                    position: absolute;
                    top: 0%;
                    left: 0%;
                    width: 100%;
                    height: 100%;
                    background-color: #bbbcc7;
                    z-index: 1001;
                    -moz-opacity: 0.8;
                    opacity: .80;
                    filter: alpha(opacity=60);
                }
        */
                .LabelDelBtn {
                    color: White;
                    font-size: 12px;
                    font-weight: bold;
                    cursor: pointer;
                    border-radius: 5px;
                    padding: 3px 5px;
                    background: #da542e;
                }

                .LabelAddBtn {
                    color: White;
                    font-size: 12px;
                    font-weight: bold;
                    cursor: pointer;
                    border-radius: 5px;
                    padding: 3px 5px;
                    background: #579fd8;
                }


                .wangid_conbox td, .wangid_conbox th {
                    font-size: 12px
                }

                #ShowTow .XC-modal-body span {
                    width: 85px
                }

                .find_input {
                    width: 13%;
                    height: 28px;
                    border: solid 1px #ccc;
                    border-radius: 2px;
                    margin-right: 1%;
                    margin-left: 0.5%;
                }

                .XC-Span-Textarea-block, .XC-Span-Input-block, .XC-Span-Select-block {
                    width: 85px;
                }

                .XC-Textarea-block {
                    max-height: none;
                }
    </style>




</head>
<body>
    <div class="div_find">

        <p>
            <label class="find_labela">产品代号</label> <input type="text" id="txtSTechNo" class="find_input" />
            <label class="find_labela">工序</label>
            <select class="find_input" id="txtSProcNo">
                <option></option>
            </select>
            <label class="find_labela">适应型号</label><input type="text" id="txtSModel" class="find_input" />
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="BaseTestItemBut_open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="javascript:openDialog(1)">添加</button>
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" id="BatchDeleteTestItem">删除</button>
        </p>
    </div>

    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="TestItemlist" lay-filter="TestItemlist"></table>

        <script type="text/html" id="barDemo">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="edit">修改</button>
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>
        </script>

    </div>

    <div class="XC-modal XC-modal-xl" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title1">标题</span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <form class="XC-Form-block">
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">产品代号</span>
                        <input type="text" class="XC-Input-block" id="txtTechNo" name="txtTechNo" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Select-block" style="line-height:30px;">工序</span>
                        <select class="XC-Select-block" id="txtProcNo">
                            <option></option>
                        </select>
                        <input type="text" class="form-control" id="txtProcNoR" name="txtProcNoR" style=" height: 30px; width: 90%; display: none;" />
                        <input type="text" class="form-control" id="txtSeqNo" name="txtSeqNo" style=" height:30px; width:50px;display:none;" />
                        <input type="text" class="form-control" id="txtSpecNo" name="txtSpecNo" style=" height:30px; width:50px;display:none;" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Textarea-block">检验项目<span class="XC-Font-Red">*</span></span>
                        <textarea class="XC-Textarea-block" id="txtNameCH" name="txtNameCH" style="height: 90px; width: 99%;"> </textarea>
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Textarea-block">检验要求<span class="XC-Font-Red">*</span></span>
                        <textarea class="XC-Textarea-block" id="txtDescCH" name="txtDescCH" style="height: 170px; width: 99%;"> </textarea>
                    </div>
                    <div class="XC-Form-block-Item" style="display:none">
                        <span class="XC-Span-Textarea-block">检验要求</span>
                        <textarea class="XC-Textarea-block" id="txtStandardCH" name="txtStandardCH" style="height: 100px; width: 99%;"> </textarea>
                    </div>

                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Textarea-block">适用型号</span>
                        <textarea class="XC-Textarea-block" id="txtModel" name="txtModel" style="height: 70px; width: 99%;"> </textarea>
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">单位</span>
                        <input type="text" class="XC-Input-block" id="txtUnit" name="txtUnit" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Select-block" style="line-height:30px;">数据类型<span class="XC-Font-Red">*</span></span>
                        <select class="XC-Select-block" id="txtBaseValueKind">
                            <option value="文本类型">文本类型</option>
                            <option value="数值类型">数值类型</option>
                            <option value="布尔类型">布尔类型</option>
                            <option value="日期类型">日期类型</option>
                        </select>
                    </div>
                </form>

                <div id="div_QJ" style="width:100%;display:none; ">
                    <hr />
                    <table cellspacing="0" cellpadding="0" border='0' style="width:98%; ">
                        <tr style=" height:30px;">
                            <td style=" width: 120px; color: blue;">
                                请填写区间值
                            </td>
                            <td style=" width:120px; text-align:center;">
                                <select class="XC-Select-block" id="txtBaseRangeKind" style="width:99%;height:25px;">
                                    <option value=""></option>
                                    <option value="大于">大于</option>
                                    <option value="小于">小于</option>
                                    <option value="介于">介于</option>
                                    <option value="等于">等于</option>
                                </select>
                            </td>
                            <td style="width:10px;">
                            </td>
                            <td>
                                <input type="checkbox" id="CH_Down" style="vertical-align:middle;margin:0px" /> <label id="L_BHD"> 包含下限 </label>
                                <input type="text" id="txtDownV" name="txtDownV" style=" height:25px; width:55px" class="XC-Input-block" />
                                <label id="L_Down"> < </label>
                                <label style="color:blue;font-size:14px;"> 测试值 </label>
                                <label id="L_Deng" style="display:none;"> = </label>
                                <label id="L_Up"> < </label>
                                <input type="text" id="txtUpV" name="txtUpV" style=" height:25px; width:55px" class="XC-Input-block" />&nbsp;
                                <input type="checkbox" id="CH_Up" style="vertical-align:middle;margin:0px" /> <label id="L_BHU"> 包含上限 </label>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtEditKind" name="txtEditKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="TestItemBaseSave_Btn">提交</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="TestItemBaseSave_Close" onclick='closeDialog()'>关闭</button>
            </div>
        </div>
    </div>
    <div id="ShowTow-fade" class="black_overlay">
    </div>

    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>


    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtTechNo" name="txtTechNo" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtProcedureNo" name="txtProcedureNo" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtProcedureName" name="txtProcedureName" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtSpecNo" name="txtSpecNo" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtFlag" name="txtFlag" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="TestItemBaseDel_Btn">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>
</body>
</html>