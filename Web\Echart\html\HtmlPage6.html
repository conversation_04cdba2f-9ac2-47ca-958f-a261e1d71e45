﻿<!DOCTYPE html
          PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>控制图</title>
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/echarts.js"></script>
    <script src="../js/layui.js"></script>
    <link href="../css/layui.css" rel="stylesheet" />

    <style>
        * {
            padding: 0;
            margin: 0
        }

        #container {
            padding: 0px 15px;
            height: 100vh;
            background: #ffffff;
            overflow: auto;
            position: relative;
            color: #ffffff;
        }

            #container .layui-panel {
                margin-top: 15px;
                padding: 15px
            }

            /* 自定义滚动条样式 */
            #container::-webkit-scrollbar {
                width: 8px;
            }

            /* 滚动条滑块样式 */
            #container::-webkit-scrollbar-thumb {
                background-color: #d2d2d2;
                border-radius: 5px;
            }
    </style>
    <script>
        var sLogin = "";
        var sCorpID = "";
        var myChart1 = null
        var myChart2 = null
        var No = sessionStorage.getItem("No")
        var SC = sessionStorage.getItem("SC")
        var GJGZ = JSON.parse(sessionStorage.getItem("GJGZ"))
        var toplimit = sessionStorage.getItem("toplimit")
        var floor = sessionStorage.getItem("floor")
        var WarningOne = []
        var WarningTow = []
        var Interval = null
        var constant = [
            { 'n': 2, 'A3': 2.66, 'B3': 0, 'B4': 3.27 },
            { 'n': 3, 'A3': 1.95, 'B3': 0, 'B4': 2.57 },
            { 'n': 4, 'A3': 1.63, 'B3': 0, 'B4': 2.27 },
            { 'n': 5, 'A3': 1.43, 'B3': 0, 'B4': 2.09 },
            { 'n': 6, 'A3': 1.29, 'B3': 0.03, 'B4': 1.97 },
            { 'n': 7, 'A3': 1.18, 'B3': 0.12, 'B4': 1.88 },
            { 'n': 8, 'A3': 1.10, 'B3': 0.19, 'B4': 1.82 },
            { 'n': 9, 'A3': 1.03, 'B3': 0.24, 'B4': 1.76 },
            { 'n': 10, 'A3': 0.98, 'B3': 0.28, 'B4': 1.72 }
        ];

        $(function () {
            WarningOne = []
            WarningTow = []
            myChart1 = echarts.init(document.getElementById('myChart1'));
            myChart2 = echarts.init(document.getElementById('myChart2'));
            $.ajax({
                url: "/Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else {
                        sLogin = parsedJson.Man
                    }
                }
            })

            fetchAndUpdateData(No, sLogin)

            clearInterval(Interval)

            Interval = setInterval(function () {
                fetchAndUpdateData(No, sLogin)
            }, 30000)

            $(window).resize(function () {
                myChart1.resize();
                myChart2.resize();
            })
        })

        // 定时任务函数
        async function fetchAndUpdateData(No, sLogin) {
            WarningOne = []
            WarningTow = []
            var Params = { No: No, Name: "", Item: "", MNo: "", MName: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "", E: "", F: "", G: "", H: "", I: "", J: "", K: sLogin, L: "", Remark: "", Flag: "103" };
            var Data = JSON.stringify(Params);

            //  API URL
            const apiUrl = "/Service/EchartAjax.ashx?OP=GetEchartData&Data=" + encodeURI(Data);

            const response = await fetch(apiUrl);

            if (response.ok) {
                const data = await response.json();
                var result = []
                Object.keys(data).forEach(function (key) {
                    result = data[key][0]
                });

                var GorupNo = [...new Set(result.map(item => item.Id))];
                var JunZhi = []
                var JiCha = []

                GorupNo.forEach(ele => {
                    let array = result.filter(item => item.Id == ele).map(item => JSON.parse(item.TestValue))

                    let total = array.reduce((accumulator, currentValue) => accumulator + currentValue, 0);

                    let mean = total / array.length;

                    JunZhi.push(mean)

                    // 计算方差
                    let variance = array.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / (array.length - 1);

                    //标准差
                    var BiaoJunCha = Math.sqrt(variance)
                    JiCha.push(parseFloat(BiaoJunCha.toFixed(3)))
                })

                var table = {
                    elem: '#ID-table-demo-data',
                    cols: [[ //标题栏
                        { type: 'numbers', title: "序号" },
                        { field: 'SerialNo', title: '序列号' },
                        { field: 'TestValue', title: '测试值' },
                        { field: 'Id', title: '组别' },
                        { field: 'TestDate', title: '测试时间' },
                    ]],
                    data: result,
                    skin: 'line', // 表格风格
                    even: true,
                    page: true, // 是否显示分页
                    limits: [20, 40, 60, 80, 100],
                    limit: 20 // 每页默认显示的数量
                }

                var MaterNo = result.length > 0 ? result[0].MaterNo : ""
                var ProcduceName = result.length > 0 ? result[0].ProcedureName : ""
                var DescCH = result.length > 0 ? result[0].NameCH : ""
                var constantItem = constant.filter(item => item.n == SC)
                var JiChaSum = JiCha.reduce((acc, curr) => acc + curr, 0);
                var JiChaAvg = JiChaSum / JiCha.length
                var JunZhiSum = JunZhi.reduce((acc, curr) => acc + curr, 0);
                var JunZhiAvg = JunZhiSum / JunZhi.length


                var S = JiChaAvg;

                var CP = (toplimit - floor) / (6 * S);

                var CPU = (toplimit - JunZhiAvg) / (3 * S);

                var CPL = (JunZhiAvg - floor) / (3 * S);

                var CPK = Math.min(...[CPU, CPL])

                var Xucl = JunZhiAvg + (constantItem.length > 0 ? constantItem[0].A3 * JiChaAvg : 0);

                var Xlcl = JunZhiAvg - (constantItem.length > 0 ? constantItem[0].A3 * JiChaAvg : 0);

                var Rucl = (constantItem.length > 0 ? constantItem[0].B4 * JiChaAvg : 0);

                var Rlcl = (constantItem.length > 0 ? constantItem[0].B3 * JiChaAvg : 0);

                EchartOne(GorupNo, JunZhi, S, JunZhiAvg, Xucl, Xlcl)

                Echarttow(GorupNo, JiCha, S, JiChaAvg, Rucl, Rlcl)

                TableData(table)

                $("#SValue").html(S.toFixed(3))
                $("#CPValue").html(CP.toFixed(3))
                $("#CPUValue").html(CPU.toFixed(3))
                $("#CPLValue").html(CPL.toFixed(3))
                $("#CPKValue").html(CPK.toFixed(3))
                $("#MaterNo").html(MaterNo)
                $("#ProcduceName").html(ProcduceName)
                $("#DescCH").html(DescCH)

                $("#tbody-item").empty()
                var concat = WarningOne.concat(WarningTow)
                concat.forEach((item, index) => {
                    let str = "<tr>";
                    str += "<td>" + (index + 1) + "</td>"
                    str += "<td>" + item.name + "</td>"
                    str += "<td>" + item.type + "</td>"
                    str += "<td>" + item.data.join(',') + "</td>"
                    str += "</tr>"
                    $("#tbody-item").append(str)
                })
            } else {
                console.log('网络请求失败: ' + response.status + "," + response.statusText)
            }
        }
        function TableData(data) {
            layui.use('table', function () {
                var table = layui.table;
                table.render(data);
            });
        }

        function EchartOne(GroupNo, JunZhi, S, JunZhiAvg, Xucl, Xlcl) {

            //把数据转换成匹配的格式去校验
            var checkData = ConvertData(GroupNo, JunZhi)

            S = (Xucl - Xlcl) / 6;

            //GJGZ 已选的规则
            GJGZ.forEach(item => {
                switch (item.value) {
                    case "1":
                        checkRule1(checkData, S, JunZhiAvg, item, '均值图')
                        break;
                    case "2":
                        checkRule2(checkData, JunZhiAvg, item, '均值图')
                        break;
                    case "3":
                        checkRule3(checkData, item, '均值图')
                        break;
                    case "4":
                        checkRule4(checkData, JunZhiAvg, item, '均值图')
                        break;
                    case "5":
                        checkRule5(checkData, S, JunZhiAvg, item, '均值图')
                        break;
                    case "6":
                        checkRule6(checkData, S, JunZhiAvg, item, '均值图')
                        break;
                    case "7":
                        checkRule7(checkData, S, JunZhiAvg, item, '均值图')
                        break;
                    case "8":
                        checkRule8(checkData, S, JunZhiAvg, item, '均值图')
                        break;
                    default:
                }
            })

            var unusualGroupNo = []
            WarningOne.forEach(item => {
                unusualGroupNo.push(...item.data);
            })
            unusualGroupNo = Array.from(new Set(unusualGroupNo))

            var sData = []
            checkData.forEach(item => {
                if (unusualGroupNo.includes(item.GorupNo)) {
                    sData.push({ value: item.Value, itemStyle: { color: "red" }, symbol: 'rect' })
                } else {
                    sData.push({ value: item.Value, itemStyle: { color: "black" }, symbol: 'circle' })
                }
            })

            var max = Math.max(...[Xucl, Math.max(...JunZhi)]);
            var min = Math.min(...[Xlcl, Math.min(...JunZhi)]);
            max = max + (max * 0.01)
            min = min - (min * 0.01)

            var option1 = {
                title: {
                    text: '样本均值图',
                    textStyle: {
                        fontWeight: "lighter",
                        fontSize: "15px",
                        fontWeight: "500"
                    },
                    left: "center"
                },
                xAxis: {
                    type: 'category',
                    data: GroupNo,
                    axisLine: {
                        lineStyle: {
                            color: 'black'  // 设置 y 轴线颜色为黑色
                        }
                    },
                },
                grid: {
                    // 定义画布的位置，这里设为居中
                    left: 'center',
                    right: 'center',
                    top: '15%',
                    bottom: 'middle',
                    // 定义画布的大小，这里将其宽和高都设置为 50%
                    width: '90%',
                    height: '76%'
                },
                legend: {
                    data: ['样本均值'],
                    left: 'left',
                },
                toolbox: {
                    show: true,
                    feature: {
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ['line', 'bar'] },
                        restore: { show: true },
                        saveAsImage: { show: true }
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        show: true,  // 是否显示坐标轴轴线。
                        lineStyle: {
                            color: 'black'  // 设置 x 轴线颜色为黑色
                        }
                    },
                    axisTick: {
                        show: true,  // 是否显示坐标轴刻度。
                    },
                    splitLine: {
                        show: false
                    },
                    min: min.toFixed(2),
                    max: max.toFixed(2)
                },
                series: [
                    {
                        name: "样本均值",
                        data: sData,
                        type: 'line',
                        symbol: 'circle',     //设定为实心点
                        symbolSize: 10,   //设定实心点的大小
                        itemStyle: {
                            normal: {
                                color: 'black', //改变折线点的颜色
                                lineStyle: {
                                    color: 'black' //改变折线颜色
                                }
                            }
                        },
                        markLine: {
                            symbol: "none",
                            data: [
                                {
                                    yAxis: Xucl.toFixed(3),
                                    lineStyle: {
                                        color: 'red',
                                        type: "solid"
                                    },
                                    label: {
                                        position: "end",
                                        formatter: "UCL=" + Xucl.toFixed(3),

                                    }
                                },
                                {
                                    yAxis: Xlcl.toFixed(3),
                                    lineStyle: {
                                        color: 'red',
                                        type: "solid"
                                    },
                                    label: {
                                        position: "end",
                                        formatter: "LCL=" + Xlcl.toFixed(3),
                                    }
                                },
                                {
                                    type: 'average',
                                    name: '平均值',
                                    lineStyle: {
                                        color: 'green',
                                        type: "solid"
                                    },
                                    label: {
                                        position: "end",
                                        formatter: function (params) {
                                            return "X=" + params.data.value
                                        },
                                    }
                                }
                            ]
                        },
                    }
                ]
            };

            myChart1.setOption(option1);
        }

        function Echarttow(GroupNo, JiCha, S, JiChaAvg, Rucl, Rlcl) {

            if (JiCha.some(item => isNaN(item))) {
                $("#message").show()
                $("#message").html("提示：标准差图中检测到空值。请检查您的抽样参数设置是否正确。如确认无误，可选择忽略此提示。")
            } else {
                $("#message").hide()
                $("#message").html("")
            }

            //把数据转换成匹配的格式去校验
            var checkData = ConvertData(GroupNo, JiCha)

            S = (Rucl - Rlcl) / 6;

            //GJGZ 已选的规则
            GJGZ.forEach(item => {
                switch (item.value) {
                    case "1":
                        checkRule1(checkData, S, JiChaAvg, item, '标准差图')
                        break;
                    case "2":
                        checkRule2(checkData, JiChaAvg, item, '标准差图')
                        break;
                    case "3":
                        checkRule3(checkData, item, '标准差图')
                        break;
                    case "4":
                        checkRule4(checkData, JiChaAvg, item, '标准差图')
                        break;
                    case "5":
                        checkRule5(checkData, S, JiChaAvg, item, '标准差图')
                        break;
                    case "6":
                        checkRule6(checkData, S, JiChaAvg, item, '标准差图')
                        break;
                    case "7":
                        checkRule7(checkData, S, JiChaAvg, item, '标准差图')
                        break;
                    case "8":
                        checkRule8(checkData, S, JiChaAvg, item, '标准差图')
                        break;
                    default:
                }
            })

            var unusualGroupNo = []
            WarningTow.forEach(item => {
                unusualGroupNo.push(...item.data);
            })
            unusualGroupNo = Array.from(new Set(unusualGroupNo))

            var sData = []
            checkData.forEach(item => {
                if (unusualGroupNo.includes(item.GorupNo)) {
                    sData.push({ value: item.Value, itemStyle: { color: "red" }, symbol: 'rect' })
                } else {
                    sData.push({ value: item.Value, itemStyle: { color: "black" }, symbol: 'circle' })
                }
            })

            var max = Math.max(...[Rucl, Math.max(...JiCha)]);
            var min = Math.min(...[Rlcl, Math.min(...JiCha)]);
            max = max + (max * 0.01)
            min = min - (min * 0.01)

            var option2 = {
                title: {
                    text: '样本标准差图',
                    textStyle: {
                        fontWeight: "lighter",
                        fontSize: "15px",
                        fontWeight: "500"
                    },
                    left: "center"
                },
                xAxis: {
                    type: 'category',
                    data: GroupNo,
                    axisLine: {
                        lineStyle: {
                            color: 'black'  // 设置 y 轴线颜色为黑色
                        }
                    },
                },
                grid: {
                    // 定义画布的位置，这里设为居中
                    left: 'center',
                    right: 'center',
                    top: '17%',
                    bottom: 'middle',
                    // 定义画布的大小，这里将其宽和高都设置为 50%
                    width: '90%',
                    height: '76%'
                },
                legend: {
                    data: ['样本标准差'],
                    left: 'left',
                },
                toolbox: {
                    show: true,
                    feature: {
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ['line', 'bar'] },
                        restore: { show: true },
                        saveAsImage: { show: true }
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        show: true,  // 是否显示坐标轴轴线。
                        lineStyle: {
                            color: 'black'  // 设置 y 轴线颜色为黑色
                        }
                    },
                    axisTick: {
                        show: true,  // 是否显示坐标轴刻度。
                    },
                    splitLine: {
                        show: false
                    },
                    min: min.toFixed(2),
                    max: max.toFixed(2)
                },
                series: [
                    {
                        data: sData,
                        name: "样本标准差",
                        type: 'line',
                        symbol: 'circle',     //设定为实心点
                        symbolSize: 10,   //设定实心点的大小
                        itemStyle: {
                            normal: {
                                color: 'black', //改变折线点的颜色
                                lineStyle: {
                                    color: 'black' //改变折线颜色
                                }
                            }
                        },
                        markLine: {
                            symbol: "none",
                            data: [
                                {
                                    yAxis: Rucl.toFixed(3),
                                    lineStyle: {
                                        color: 'red',
                                        type: "solid"
                                    },
                                    label: {
                                        position: "end",
                                        formatter: "UCL=" + Rucl.toFixed(3),

                                    }
                                },
                                {
                                    yAxis: Rlcl.toFixed(3),
                                    lineStyle: {
                                        color: 'red',
                                        type: "solid"
                                    },
                                    label: {
                                        position: "end",
                                        formatter: "LCL=" + Rlcl.toFixed(3),
                                    }
                                },
                                {
                                    type: 'average',
                                    name: '平均值',
                                    lineStyle: {
                                        color: 'green',
                                        type: "solid"
                                    },
                                    label: {
                                        position: "end",
                                        formatter: function (params) {
                                            return "S=" + params.data.value
                                        },
                                    }
                                }
                            ]
                        },
                    }
                ],
            };

            myChart2.setOption(option2);
        }

        //把组号和值转换成JSON
        function ConvertData(key, val) {
            return key.map((item, index) => {
                return {
                    GorupNo: item,
                    Value: val[index]
                };
            });
        }

        //规则 1：一点超过 ±3σ（通常是控制限）
        function checkRule1(data, S, JunZhiAvg, rule, type) {
            let result = []
            data.forEach(item => {
                if (item.Value > JunZhiAvg + (S * 3) || item.Value < JunZhiAvg - (S * 3)) {
                    result.push(item.GorupNo)
                }
            });
            if (result.length > 0) {
                if (type == "均值图") {
                    WarningOne.push({ name: rule.name, type: type, data: result })
                } else {
                    WarningTow.push({ name: rule.name, type: type, data: result })
                }
            }
        }

        //规则 2：九点连续在中心线同一侧
        function checkRule2(data, avg, rule, type) {
            for (let i = 0; i < data.length - 8; i++) {
                let subset = data.slice(i, i + 9);
                if (subset.every(item => item.Value > avg) || subset.every(item => item.Value < avg)) {
                    let result = []
                    for (let j = i; j < i + 9; j++) {
                        result.push(data[j].GorupNo);
                    }
                    if (type == "均值图") {
                        WarningOne.push({ name: rule.name, type: type, data: result })
                    } else {
                        WarningTow.push({ name: rule.name, type: type, data: result })
                    }
                }
            }
        }

        //规则 3：六点连续递增或递减
        function checkRule3(data, rule, type) {
            for (let i = 0; i < data.length - 5; i++) {
                let subset = data.slice(i, i + 6);
                let result = []
                if (subset.every((item, index) => index === 0 || (item.Value > subset[index - 1].Value))) { //找出连续递增的组号
                    for (let j = i; j < i + 6; j++) {
                        result.push(data[j].GorupNo);
                    }
                    if (type == "均值图") {
                        WarningOne.push({ name: rule.name, type: type, data: result })
                    } else {
                        WarningTow.push({ name: rule.name, type: type, data: result })
                    }
                }
                if (subset.every((item, index) => index === 0 || (item.Value < subset[index - 1].Value))) { //找出连续递减的组号
                    for (let j = i; j < i + 6; j++) {
                        result.push(data[j].GorupNo);
                    }
                    if (type == "均值图") {
                        WarningOne.push({ name: rule.name, type: type, data: result })
                    } else {
                        WarningTow.push({ name: rule.name, type: type, data: result })
                    }
                }
            }


        }


        //规则 4：十四点或更多交替上升和下降
        function checkRule4(data, avg, rule, type) {
            let increasing = data[0].Value > avg ? true : false;//上升
            let decreasing = data[0].Value < avg ? true : false;//下降
            let index = [];
            let result = []
            for (let i = 0; i < data.length; i++) {
                if ((increasing && data[i].Value > avg) || (decreasing && data[i].Value < avg)) {
                    index.push(i)
                    increasing = !increasing;
                    decreasing = !decreasing;
                } else {
                    if (index.length >= 14) {
                        index.forEach(item => {
                            result.push(data[item].GorupNo)
                        })
                        if (type == "均值图") {
                            WarningOne.push({ name: rule.name, type: type, data: result })
                        } else {
                            WarningTow.push({ name: rule.name, type: type, data: result })
                        }
                    }
                    index = []
                    result = []
                }
            }

            if (index.length >= 14) {
                index.forEach(item => {
                    result.push(data[item].GorupNo)
                })
                if (type == "均值图") {
                    WarningOne.push({ name: rule.name, type: type, data: result })
                } else {
                    WarningTow.push({ name: rule.name, type: type, data: result })
                }
            }
        }

        //规则 5：两点或两点以上连续距离中心线同一侧超过 ±2σ，但未超过 ±3σ
        function checkRule5(data, S, JunZhiAvg, rule, type) {
            let result = []
            for (let i = 0; i < data.length; i++) {
                let subset = data.slice(i, i + 2);
                let nums = []
                for (let j = 0; j < subset.length; j++) {
                    let VerificationResult = Verification(subset[j].Value, S, JunZhiAvg, 3, 2)
                    if (VerificationResult > 0) {
                        nums.push(VerificationResult)
                    }
                }

                if (nums.length > 1) {
                    let flag = nums.every(value => value === nums[0])
                    if (flag) {
                        subset.forEach(item => {
                            result.push(item.GorupNo)
                        });
                    }
                }
            }

            if (result.length > 0) {
                if (type == "均值图") {
                    WarningOne.push({ name: rule.name, type: type, data: Array.from(new Set(result)) })
                } else {
                    WarningTow.push({ name: rule.name, type: type, data: Array.from(new Set(result)) })
                }
            }
        }

        //规则 6：四点或四点以上连续距离中心线同一侧超过 ±1σ，但未超过 ±2σ
        function checkRule6(data, S, JunZhiAvg, rule, type) {
            let result = []
            for (let i = 0; i < data.length; i++) {
                let subset = data.slice(i, i + 4);
                let nums = []
                for (let j = 0; j < subset.length; j++) {
                    let VerificationResult = Verification(subset[j].Value, S, JunZhiAvg, 2, 1)
                    if (VerificationResult > 0) {
                        nums.push(VerificationResult)
                    }
                }

                if (nums.length > 3) {
                    let flag = nums.every(value => value === nums[0])
                    if (flag) {
                        subset.forEach(item => {
                            result.push(item.GorupNo)
                        });
                    }
                }
            }

            if (result.length > 0) {
                if (type == "均值图") {
                    WarningOne.push({ name: rule.name, type: type, data: Array.from(new Set(result)) })
                } else {
                    WarningTow.push({ name: rule.name, type: type, data: Array.from(new Set(result)) })
                }
            }
        }


        function Verification(value, S, avg, max, min) {
            if (value > avg - max * S && value < avg - S * min) {
                return 1
            } else if (value > avg + S * min && value < avg + max * S) {
                return 2
            } else {
                return 0
            }
        }


        //规则 7：十五点或更多连续在 ±1σ 以内
        function checkRule7(data, S, JunZhiAvg, rule, type) {
            let index = [];
            let result = [];
            for (let i = 0; i < data.length; i++) {
                if (data[i].Value < JunZhiAvg + S && data[i].Value > JunZhiAvg - S) {
                    index.push(i)

                } else {
                    if (index.length >= 15) {
                        index.forEach(item => {
                            result.push(data[item].GorupNo)
                        })
                        if (type == "均值图") {
                            WarningOne.push({ name: rule.name, type: type, data: result })
                        } else {
                            WarningTow.push({ name: rule.name, type: type, data: result })
                        }
                    }
                    index = []
                    result = []
                }
            }
            if (index.length >= 15) {
                index.forEach(item => {
                    result.push(data[item].GorupNo)
                })
                if (type == "均值图") {
                    WarningOne.push({ name: rule.name, type: type, data: result })
                } else {
                    WarningTow.push({ name: rule.name, type: type, data: result })
                }
            }
        }

        //规则 8：八点或更多连续在中心线和 +1σ 之间无,一点在 ±10 以外
        function checkRule8(data, S, JunZhiAvg, rule, type) {
            let index = [];
            let result = [];
            for (let i = 0; i < data.length; i++) {
                if (data[i].Value > JunZhiAvg + S || data[i].Value < JunZhiAvg - S) {
                    index.push(i)

                } else {
                    if (index.length >= 8) {
                        index.forEach(item => {
                            result.push(data[item].GorupNo)
                        })
                        if (type == "均值图") {
                            WarningOne.push({ name: rule.name, type: type, data: result })
                        } else {
                            WarningTow.push({ name: rule.name, type: type, data: result })
                        }
                    }
                    index = []
                    result = []
                }
            }
            if (index.length >= 8) {
                index.forEach(item => {
                    result.push(data[item].GorupNo)
                })
                if (type == "均值图") {
                    WarningOne.push({ name: rule.name, type: type, data: result })
                } else {
                    WarningTow.push({ name: rule.name, type: type, data: result })
                }
            }
        }
    </script>
</head>


<body>
    <div id="container">
        <div class="layui-panel" style="height: 30%;">
            <div id="myChart1" style="width: 100%; height: 100%;"></div>
        </div>
        <div class="layui-panel" style="height: 30%;">
            <div id="myChart2" style="width: 100%; height: 100%;"></div>
        </div>
        <div class="layui-panel">
            <div class="layui-collapse" lay-filter="filter-collapse">
                <div class="layui-colla-item">
                    <div class="layui-colla-title">警告信息</div>
                    <div class="layui-colla-content">
                        <table class="layui-table" lay-skin="line">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>触发规则</th>
                                    <th>图类型</th>
                                    <th>异常点</th>
                                </tr>
                            </thead>
                            <tbody id="tbody-item">
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="layui-colla-item">
                    <div class="layui-colla-title">整体能力</div>
                    <div class="layui-colla-content">
                        <table class="layui-table">
                            <colgroup>
                                <col width="150" />
                                <col width="150" />
                                <col width="150" />
                                <col width="150" />
                                <col width="150" />
                                <col width="150" />
                            </colgroup>
                            <tr>
                                <td style="font-weight:bold">产品编码</td>
                                <td id="MaterNo"></td>
                                <td style="font-weight:bold">工序</td>
                                <td id="ProcduceName"></td>
                                <td style="font-weight:bold">管制对象</td>
                                <td id="DescCH"></td>
                            </tr>
                            <tr>
                                <td style="font-weight:bold">CP</td>
                                <td id="CPValue"></td>
                                <td style="font-weight:bold">CPL</td>
                                <td id="CPLValue"></td>
                                <td style="font-weight:bold">CPU</td>
                                <td id="CPUValue"></td>
                            </tr>
                            <tr>
                                <td style="font-weight:bold">CPK</td>
                                <td id="CPKValue"></td>
                                <td style="font-weight:bold">S</td>
                                <td id="SValue"></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div class="layui-colla-item">
                    <div class="layui-colla-title">详细数据</div>
                    <div class="layui-colla-content">
                        <table class="layui-hide layui-table" id="ID-table-demo-data"></table>
                    </div>
                </div>
            </div>
            <div id="message" style="margin-top:15px;color:red;font-size:12px"></div>
        </div>
    </div>
</body>

</html>