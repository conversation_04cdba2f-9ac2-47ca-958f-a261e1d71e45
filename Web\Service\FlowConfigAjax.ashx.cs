﻿using System;
using System.Collections;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml.Linq;
using BLL;
using Newtonsoft.Json;
using System.Web.SessionState;
using Common;
using System.Collections.Generic;
using WXCommon;
using System.IO;
using System.Text;

namespace Web.Service
{
    /// <summary>
    /// $codebehindclassname$ 的摘要说明
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class FlowConfigAjax : IHttpHandler, IRequiresSessionState
    {

        string sAMFlag = string.Empty;
        string sRA = string.Empty;
        string sCCorpID = string.Empty;

        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "text/plain";
            string Result = string.Empty;
            string Operate = context.Request.Params["OP"];
            string sBSNo = context.Request.Params["BSNo"];
            string DataParams = context.Request.Params["Data"];
            string sObjectNo = context.Request.Params["ObjectNo"];
            string sSCFlag = context.Request.Params["SCFlag"];   // 给审批查询列表用 1：进入界面显示的类别，2：点击查询显示的列表查询
            sAMFlag = context.Request.Params["sFlag"];
            sCCorpID = context.Request.Params["CorpID"];

            switch (Operate)
            {

                case "JustRight": //判断是否有操作权限
                    Result = JustOperateRight(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "FObjectList": // 流程对象字段列表 
                    Result = FObjectFieldList(DataParams, "");
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetFObjectMX": // 显示流程对象字段明细
                    Result = GetFObjectFieldMX(DataParams, sObjectNo);  //
                    break;

                case "AddObjectField": //插入对象字段信息
                    Result = InsertObjectField(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetFlowList": //获取流程下拉列表 
                    Result = GetFlowList(DataParams, "");
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetFlowTxt": //获取流程申请时，需要显示的字段。  
                    Result = GetFlowTxt(DataParams, sAMFlag);
                    break;


                case "ShowBSFlowList": // 显示申请的流程清单  
                    Result = ShowBSFlowList(DataParams, sBSNo);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "GetBSFlowMX": // 显示申请的流程清单的明细信息  
                    Result = ShowBSFlowMX(DataParams, sBSNo);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "SaveBSFlowData": //保存设置每个流程申请的内容，比如采购申请流程，这里设置申请的物料清单   
                    Result = SaveBSFlowDataInfo(DataParams); 
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "BSFlowDataList": // 显示流程申请时，填写的业务数据清单，如：采购申请，显示申请的物料清单 
                    Result = ShowBSFlowDataList(DataParams, sBSNo);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "DelBSData": //删除申请流程填写的业务数据
                    Result = DeleteBSFlowData(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "CommitFlow": //提交申请的流程   
                    Result = SaveFlowAndBSInfo(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "AddAudMan": //获取审核人
                    Result = GetFlowAudMan(DataParams);
                    break;

                case "SaveAudNote": //保存流程节点及节点审批人
                    Result = SaveNoteAndAudMan(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "ShowAudList": // 显示审核单据列表
                    Result = ShowBSAudList(DataParams, sSCFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "SaveAudRec": //各个节点的审批人提交审批记录  
                    Result = SaveAudRec(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "CBBill": // 催办单据
                    Result = CBBill(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;

                case "CHBill": // 撤回单据
                    Result = CHBill(DataParams);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


                case "GetTxtInfo": //获取txt文件数据    
                    Result = GetTxtInfo(DataParams, sBSNo, sSCFlag);
                    context.Response.Flush();
                    context.Response.Write(Result);
                    break;


            }

        }

        #region 判断用户是否有操作权限
        public string JustOperateRight(string Params)
        {
            string Result = string.Empty;
            string Message = "YES";
            string sInMan = string.Empty;

            HttpContext.Current.Session["AMFlag"] = sAMFlag;
            HttpContext.Current.Session["CCorpID"] = sCCorpID; //"wx9f1cde5f70b71497"; 


            //--@_@--   测试使用，发布到测试系统需要屏蔽 -- 不需要调用微信接口（调用微信接口需要用微信调用的，）
            HttpContext.Current.Session["LoginName"] = "wudong";// wd(WD_冰冻)     wudong001(吴东)  梧桐(wd02)       许春波(xuchunbo) 
            HttpContext.Current.Session["FullName"] = "吴冬";   //  汪灵(E01907)	柏华E10707	
            HttpContext.Current.Session["CompanyNo"] = "C0001";
            //--@_@--

            if (HttpContext.Current.Session["LoginName"] != null)  // HttpContext.Current.Session["LoginName"] != null
            {
                sInMan = HttpContext.Current.Session["LoginName"].ToString();
            }
            else
            {
                Message = "loginError";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }


            string sNo = "判断当前登录人是否有操作权限：" + sInMan + "BZ:" + sAMFlag;
            string sSQL = " insert into T_Temp(MaterNo) values('" + sNo + "')";
            Common.DBHelper.ExecuteCommand(sSQL);


            if (sAMFlag == "1") //流程对象 对应 的字段设置
            {
                Message = FConfigBll.JustOperateRight(sInMan, "LCDX"); // LCDX 流程对象 这种编号是功能菜单的编号，固定的。
            }
            else if (sAMFlag == "2") // 流程设置
            {
                Message = FConfigBll.JustOperateRight(sInMan, "LCSZ");
            }
            else if (sAMFlag == "3") // 流程对象维护及申请
            {
                Message = FConfigBll.JustOperateRight(sInMan, "DXSQ");
            }
            else if (sAMFlag == "4") //  效果图业代审核
            {
                Message = FConfigBll.JustOperateRight(sInMan, "XGTSH");
            }
            else if (sAMFlag == "5") // 店招验收
            {
                Message = FConfigBll.JustOperateRight(sInMan, "DZYS");
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, Man = sInMan });  //20160331：如果在前端的函数调用这个函数，需要这个语句
            return Result;
        }
        #endregion


        #region 显示数据对象列表（流程对象字段，流程列表等）
        public string FObjectFieldList(string Params, string sStatus)
        {
            string sCons = string.Empty;
 
            string sSNo = string.Empty;
            string sSName = string.Empty;
            string sLoginMan = string.Empty;
            string sComp = string.Empty;
            string sAdmin = System.Configuration.ConfigurationManager.AppSettings["DQAudMan"];

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLoginMan = HttpContext.Current.Session["LoginName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                sLoginMan = "";
            }

            string sNo = "当前查询人：" + sLoginMan + "状态: " + sStatus + "标识：" + sAMFlag;
            string sSQL = " insert into T_Temp(MaterNo) values('" + sNo + "')";
            Common.DBHelper.ExecuteCommand(sSQL);

            if (!string.IsNullOrEmpty(Params))
            {
                string Result = string.Empty;
                var AnonymousUser = new { SName = String.Empty, SNo = String.Empty };

                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

                sSNo = Item.SNo;
                sSName = Item.SName;

            }


            return JsonConvert.SerializeObject(FConfigBll.GetListByCondition(sSName, sSNo, "", "", sComp, "1")); // 1： 查询数据列表
        }
        #endregion



        #region 显示流程对象中字段的明细
        public string GetFObjectFieldMX(string Params, string sNo)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sManName = string.Empty;
            HttpContext context = HttpContext.Current;
            string sMan = string.Empty;
            string sNEFlag = string.Empty;
            string sComp = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            DataTable CLdt = FConfigBll.GetListByCondition("", sNo, "", "", sComp, "2");  // 2： 显示明细
            if (CLdt.Rows.Count > 0)
            {
                sNEFlag = "E"; //如果目标客户记录表有记录，不管是自己新增还是办事处导入的，表示修改
                Message = "Success";
            }
            else
            {
                sNEFlag = "N"; // 无记录，表示新增

                //新增一行
                DataRow newRow;
                newRow = CLdt.NewRow();

                newRow["InMan"] = sMan;
                CLdt.Rows.Add(newRow);
            }


            // 增加当前操作人编号
            DataColumn Operate = new DataColumn("Operate", typeof(string));
            Operate.DefaultValue = sMan;
            CLdt.Columns.Add(Operate); // 增加一列
            // 增加当前操作人
            DataColumn OperateName = new DataColumn("OperateName", typeof(string));
            OperateName.DefaultValue = sManName;
            CLdt.Columns.Add(OperateName); // 增加一列

            // 是新增还是修改标识
            DataColumn NEFlag = new DataColumn("NEFlag", typeof(string));
            NEFlag.DefaultValue = sNEFlag;
            CLdt.Columns.Add(NEFlag); // 增加一列



            string cjson = JsonConvert.SerializeObject(CLdt);
            context.Response.Write(cjson);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 插入流程对象显示字段信息，如：采购流程：物料编码，物料名称，数量，金额
        public string InsertObjectField(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sName = string.Empty;
            string HttpUrl = System.Configuration.ConfigurationManager.AppSettings["HttpUrl"];
            string sFlag = string.Empty;
            string sOKFlag = string.Empty;


            //Director,DirectorPhone,SalesSuper,SalesSuperPhone,ShopSignsKind,ApplyMan,ApplyDate,ApplyDesc,InMan,Status,CompanyNo 店招待审核
            var AnonymousUser = new
            {
                ObjectNo = String.Empty,
                ObjectName = String.Empty,
                FOne = String.Empty,
                FTwo = String.Empty,
                FThree = String.Empty,
                FFour = String.Empty,
                FFive = String.Empty,
                FSix = String.Empty,
                FSeven = String.Empty,
                FEight = String.Empty,
                FNine = String.Empty,
                FTen = String.Empty,
                InMan = String.Empty,
                InManName = String.Empty,
                CompanyNo = String.Empty,
                Remark = String.Empty,
                NEFlag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            Model.ObjectField OBF = new Model.ObjectField();

            //用输入的账号，判断是否是否存在。
            sLogin = Item.InMan;
            sName = Item.InManName;


            /*登录逻辑*/
            DataTable dt = FConfigBll.GetList(" LoginName='" + sLogin + "' and IsApproval=1 ");
            if (dt.Rows.Count > 0)
            {
                OBF.InMan = dt.Rows[0]["LoginName"].ToString();
                OBF.CompanyNo = dt.Rows[0]["CompanyNo"].ToString();
                HttpContext.Current.Session["LoginName"] = dt.Rows[0]["LoginName"].ToString();
                HttpContext.Current.Session["FullName"] = dt.Rows[0]["FullName"].ToString();
                HttpContext.Current.Session["CompanyNo"] = dt.Rows[0]["CompanyNo"].ToString();
                HttpContext.Current.Session["QYNo"] = dt.Rows[0]["QYNo"].ToString();
            }
            else
            {
                Message = "loginError";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }

            sFlag = Item.NEFlag;
            OBF.ObjectNo = Item.ObjectNo;
            OBF.ObjectName = Item.ObjectName;
            OBF.F01 = Item.FOne;
            OBF.F02 = Item.FTwo;
            OBF.F03 = Item.FThree;
            OBF.F04 = Item.FFour;
            OBF.F05 = Item.FFive;
            OBF.F06 = Item.FSix;
            OBF.F07 = Item.FSeven;
            OBF.F08 = Item.FEight;
            OBF.F09 = Item.FNine;
            OBF.F10 = Item.FTen;
            OBF.InMan = Item.InMan;
            OBF.InManName = Item.InManName;
            OBF.Remark = Item.Remark;


            if (sFlag == "E") // 修改
            {
                // 如果这些流程对象字段已被使用，不能增加，减少字段，但是可以修改字段名称


                // 更新记录
                sOKFlag = FConfigBll.InsertOrUpdateObjectField(OBF, "E");

            }
            else // 新增
            {
                //  生产单号
                OBF.ObjectNo = CreateNewBillNo();  // 设置默认值 -- 取的不是服务器时间，如果用户时间不正确，有点问题

                // 插入信息
                sOKFlag = FConfigBll.InsertOrUpdateObjectField(OBF, "N");
            }


            if (sOKFlag.Length <= 2)
            {

                Message = "Success";

            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion




        #region 获取流程列表给下拉选择
        public string GetFlowList(string Params, string sNo)
        {
            string Result = string.Empty;
            string Message = "Success";
            HttpContext context = HttpContext.Current;
            string sMan = string.Empty;
            string sComp = string.Empty;
            string sCons = string.Empty;
            string sC = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message });
                return Result;
            }

            sCons = " where CompanyNo = '" + sComp + "' and UseFlag='是' ";


            DataTable dt = FConfigBll.GetFlowInfo(sCons,"",sMan, "1");
            if (dt.Rows.Count > 0)
            {
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    sC = dt.Rows[i]["ObjectName"].ToString() + "(" + dt.Rows[i]["ObjectNo"].ToString() + ")";

                    //if(i==0)
                    Message += "<option value='" + sC + "'>" + sC + "</option>";
                    //else Message += Message + "," + dt.Rows[i]["ItemName"].ToString();
                }
            }
            else
            {
                Message = "Error";
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 显示流程需要申请人填写的业务数据字段
        public string GetFlowTxt(string Params, string sNo)
        {
            string Result = string.Empty;
            string Message = "Success";
            HttpContext context = HttpContext.Current;
            string sMan = string.Empty;
            string sComp = string.Empty;
            string sFlowNo = string.Empty;
            string sLC = context.Request.Params["LCZD"]; //    BS17061300001   
            if (sLC.Substring(0, 2) != "BS")
            {
                int sW = sLC.IndexOf('(');   
                sFlowNo = sLC.Substring(sW + 1, sLC.Length - sW-2);
            }
            else
            {
                sFlowNo = sLC;
            }

            string sCons = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message });
                return Result;
            }

            sCons = " where CompanyNo = '" + sComp + "' and ObjectNo='" + sFlowNo + "' ";

            DataTable dt = FConfigBll.GetFlowInfo(sCons, sFlowNo,sMan, sNo);
            if (dt.Rows.Count > 0)
            {
                string cjson = JsonConvert.SerializeObject(dt);
                context.Response.Write(cjson);
            }
            else
            {
                Message = "Error";
            }


            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion



        #region 显示申请的流程列表
        public string ShowBSFlowList(string Params, string sNo)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sSName = string.Empty;
            string sBDate = string.Empty;
            string sEDate = string.Empty;
            string sLoginMan = string.Empty;
            string sLoginName= string.Empty;
            string sComp = string.Empty;
            string sCon = string.Empty;
            DataTable CLdt;
            HttpContext context = HttpContext.Current;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLoginMan = HttpContext.Current.Session["LoginName"].ToString();
                sLoginName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message });
                return Result;
            }


            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { BSName = String.Empty, BDate = String.Empty, EDate = String.Empty };

                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

                sBDate = Item.BDate;
                sEDate = Item.EDate;
                sSName = Item.BSName;

            }

            // 默认当然第一天，最后一天
            DateTime now = DateTime.Now;
            DateTime d1 = new DateTime(now.Year, now.Month, 1);
            DateTime d2 = d1.AddMonths(1).AddDays(-1);

            if (string.IsNullOrEmpty(sBDate) || (sBDate == null) || (sBDate == "null"))
            {
                sBDate = d1.ToString("yyyy-MM-dd"); 
            }
            if (string.IsNullOrEmpty(sEDate) || (sEDate == null) || (sEDate == "null"))
            {
                sEDate = d2.ToString("yyyy-MM-dd");
            }
            if (string.IsNullOrEmpty(sSName) || (sSName == null) || (sSName == "null"))
            {
                sSName = "";
            }

            if (sAMFlag == "71") // 说明是显示 申请流程列表 - 只显示本人申请的流程，其他人的不能显示。（如果主管能看下属的，在这里优化即可）
            {
                sCon = "and a.InMan='" + sLoginMan + "' ";
            }

            CLdt = FConfigBll.GetBSFlowListByCondition(sCon, sSName, "", sBDate,sEDate, sComp, "1");  // 1： 显示列表

            return JsonConvert.SerializeObject(CLdt); // 1： 查询数据列表
        }
        #endregion


        #region 显示申请的流程明细
        public string ShowBSFlowMX(string Params, string sNo)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sCons = string.Empty;
            string sNEFlag = string.Empty;
            string sSName = string.Empty;
            string sLoginMan = string.Empty;
            string sLoginName = string.Empty;
            string sComp = string.Empty;
            string sBSNo = string.Empty;
            string sPath = string.Empty;
            int iBZ = 0;
            DataTable CLdt;
            HttpContext context = HttpContext.Current;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLoginMan = HttpContext.Current.Session["LoginName"].ToString();
                sLoginName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message });
                return Result;
            }


            if (string.IsNullOrEmpty(sNo) || (sNo == "null"))
            {
                sBSNo = "";
            }
            else  // 有单号
            {
                iBZ = sNo.IndexOf('@');  // > 0 说明这个单号是发送信息，点击信息的链接进去的。

                if (iBZ < 1)  // 说明是点击网格进入明细界面的
                {
                    sBSNo = sNo;
                }
                else // 说明这个单号是发送信息，点击信息的链接进去的。
                {
                    sBSNo = sNo.Substring(3, iBZ - 3); // 前 3 位是随机码,标识位 @ 为单据序号结束位，后面是登录账号，： 122WD17030800002@wudong001
                    sLoginMan = sNo.Substring(iBZ + 1, sNo.Length - iBZ - 1);

                    /*登录逻辑*/
                    DataTable Udt = LoginBll.GetList(" LoginName='" + sLoginMan + "' and IsApproval=1 ");
                    if (Udt.Rows.Count > 0)
                    {
                        Message = "Success";

                        sLoginMan = Udt.Rows[0]["LoginName"].ToString();
                        sLoginName = Udt.Rows[0]["FullName"].ToString();
                        HttpContext.Current.Session["LoginName"] = sLoginMan;
                        HttpContext.Current.Session["FullName"] = sLoginName;
                        HttpContext.Current.Session["CompanyNo"] = Udt.Rows[0]["CompanyNo"].ToString();
                    }
                    else
                    {
                        Message = "loginError";
                        Result = JsonConvert.SerializeObject(new { Msg = Message });
                        context.Response.Flush();
                        context.Response.Write(Result);
                        return Result;
                    }
                }
            }

            CLdt = FConfigBll.GetBSFlowListByCondition("", sSName, sBSNo, "", sLoginMan, sComp, "2");  // 2： 显示明细
            if (CLdt.Rows.Count > 0)
            {
                sNEFlag = "E";
            }
            else
            {
                sNEFlag = "N"; // 无记录，表示新增

                //新增一行
                DataRow newRow;
                newRow = CLdt.NewRow();

                newRow["InMan"] = sLoginMan;
                CLdt.Rows.Add(newRow);
            }


            // 增加当前操作人编号
            DataColumn Operate = new DataColumn("Operate", typeof(string));
            Operate.DefaultValue = sLoginMan;
            CLdt.Columns.Add(Operate); // 增加一列
            // 增加当前操作人
            DataColumn OperateName = new DataColumn("OperateName", typeof(string));
            OperateName.DefaultValue = sLoginName;
            CLdt.Columns.Add(OperateName); // 增加一列

            // 是新增还是修改标识
            DataColumn NEFlag = new DataColumn("NEFlag", typeof(string));
            NEFlag.DefaultValue = sNEFlag;
            CLdt.Columns.Add(NEFlag); // 增加一列


            // 查询图片路径 
            DataTable Pdt = FConfigBll.GetFlowInfoPicPath(sBSNo, "", "流程提交");

            if (Pdt.Rows.Count > 0)
            {
                for (int i = 0; i < Pdt.Rows.Count; i++)
                {
                    if (i == 0)
                    {
                        DataColumn PathOne = new DataColumn("PathOne", typeof(string)); // 增加一列
                        PathOne.DefaultValue = Pdt.Rows[0]["PicPath"].ToString();
                        CLdt.Columns.Add(PathOne);
                        sPath = ";" + Pdt.Rows[0]["PicPath"].ToString();
                    }
                    else if (i == 1)
                    {
                        DataColumn PathTwo = new DataColumn("PathTwo", typeof(string)); // 增加一列
                        PathTwo.DefaultValue = Pdt.Rows[1]["PicPath"].ToString();
                        CLdt.Columns.Add(PathTwo);
                        sPath = sPath + ";" + Pdt.Rows[1]["PicPath"].ToString();
                    }
                    else if (i == 2)
                    {
                        DataColumn PathThr = new DataColumn("PathThr", typeof(string)); // 增加一列
                        PathThr.DefaultValue = Pdt.Rows[2]["PicPath"].ToString();
                        CLdt.Columns.Add(PathThr);
                        sPath = sPath + ";" + Pdt.Rows[2]["PicPath"].ToString();
                    }
                }

                DataColumn PicPath = new DataColumn("PicPath", typeof(string));
                PicPath.DefaultValue = sPath;
                CLdt.Columns.Add(PicPath); // 增加一列
            }

            // 视频路径
            DataTable Vdt = FConfigBll.GetFlowInfoPicPath(sBSNo, "", "视频-流程提交");
            if (Vdt.Rows.Count > 0)
            {
                DataColumn PathVideo = new DataColumn("PathVideo", typeof(string)); // 增加一列
                PathVideo.DefaultValue = Vdt.Rows[0]["PicPath"].ToString();
                CLdt.Columns.Add(PathVideo);
            }

            return JsonConvert.SerializeObject(CLdt); // 1： 查询数据列表
        }
        #endregion



        #region 保存业务数据：准备提交申请的业务数据，如：采购申请单里面的具体申请了哪些物料，单价是多少
        public string SaveBSFlowDataInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sName = string.Empty;
            string HttpUrl = System.Configuration.ConfigurationManager.AppSettings["HttpUrl"];
            string sFlag = string.Empty;
            string sOKFlag = string.Empty;
            string sLC = string.Empty;
            int sW = 0;


            //Director,DirectorPhone,SalesSuper,SalesSuperPhone,ShopSignsKind,ApplyMan,ApplyDate,ApplyDesc,InMan,Status,CompanyNo 店招待审核
            var AnonymousUser = new
            {
                BSNo = String.Empty,
                BSName = String.Empty,
                FlowNo = String.Empty,
                FlowName = String.Empty,
                FOne = String.Empty,
                FTwo = String.Empty,
                FThree = String.Empty,
                FFour = String.Empty,
                FFive = String.Empty,
                FSix = String.Empty,
                FSeven = String.Empty,
                FEight = String.Empty,
                FNine = String.Empty,
                FTen = String.Empty,
                InMan = String.Empty,
                InManName = String.Empty,
                CompanyNo = String.Empty,
                Remark = String.Empty,
                NEFlag = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            Model.BusinessData BSData = new Model.BusinessData();

            //用输入的账号，判断是否是否存在。
            sLogin = Item.InMan;
            sName = Item.InManName;


            /*登录逻辑*/
            DataTable dt = FConfigBll.GetList(" LoginName='" + sLogin + "' and IsApproval=1 ");
            if (dt.Rows.Count > 0)
            {
                BSData.InMan = dt.Rows[0]["LoginName"].ToString();
                BSData.CompanyNo = dt.Rows[0]["CompanyNo"].ToString();
                HttpContext.Current.Session["LoginName"] = dt.Rows[0]["LoginName"].ToString();
                HttpContext.Current.Session["FullName"] = dt.Rows[0]["FullName"].ToString();
                HttpContext.Current.Session["CompanyNo"] = dt.Rows[0]["CompanyNo"].ToString();
                HttpContext.Current.Session["QYNo"] = dt.Rows[0]["QYNo"].ToString();
            }
            else
            {
                Message = "loginError";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }

            sFlag = Item.NEFlag;
            BSData.BSNo = Item.BSNo;
            BSData.BSName = Item.BSName;
            sW = Item.FlowName.IndexOf('(');  // 采购申请流程(FL1702210002)
            BSData.FlowNo = Item.FlowName.Substring(sW + 1, Item.FlowName.Length - sW - 2);  // FL1702210002
            BSData.FlowName = Item.FlowName.Substring(0, sW); // 采购申请流程
            BSData.F01 = Item.FOne;
            BSData.F02 = Item.FTwo;
            BSData.F03 = Item.FThree;
            BSData.F04 = Item.FFour;
            BSData.F05 = Item.FFive;
            BSData.F06 = Item.FSix;
            BSData.F07 = Item.FSeven;
            BSData.F08 = Item.FEight;
            BSData.F09 = Item.FNine;
            BSData.F10 = Item.FTen;
            BSData.InMan = Item.InMan;
            BSData.InManName = Item.InManName;
            BSData.Status = "待提交";
            BSData.Remark = Item.Remark;


            //  生产流程申请单编号
            if (string.IsNullOrEmpty(Item.BSNo))
            {
                BSData.BSNo = CreateNewBSDatalNo();
            }

            // 插入或更新信息
            sOKFlag = FConfigBll.SaveBSFlowDataInfo(BSData, "N");// 第二个参数暂时不用，是新增还是修改在DAL判定


            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, BSNo = BSData.BSNo });

            return Result;
        }
        #endregion




        #region 显示流程申请的业务数据清单，如采购申请的申请物料清单
        public string ShowBSFlowDataList(string Params, string sNo)
        {
            string sCons = string.Empty;
            string sLoginMan = string.Empty;
            string sComp = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLoginMan = HttpContext.Current.Session["LoginName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();
            }
            else
            {
                sLoginMan = "";
            }

            sCons = " where BSNo = '" + sNo + "' ";

            return JsonConvert.SerializeObject(FConfigBll.ShowBusinessDataList(sCons,"", "1")); // 
        }
        #endregion


        #region 删除流程申请的业务数据
        public string DeleteBSFlowData(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sOKFlag = string.Empty;
            string sFlowNo = string.Empty;
            int sW = 0;

            var AnonymousUser = new
            {
                BSApplyNo = String.Empty,
                FlowName = String.Empty,
                F01 = String.Empty,
                F02 = String.Empty,
                LoginName = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            sW = Item.FlowName.IndexOf('(');  // 采购申请流程(FL1702210002)
            sFlowNo = Item.FlowName.Substring(sW + 1, Item.FlowName.Length - sW - 2);  // FL1702210002


            //用输入的账号，判断是否是否存在。
            sLogin = Item.LoginName;
            /*登录逻辑*/
            DataTable dt = FConfigBll.GetList(" LoginName='" + sLogin + "' and IsApproval=1 ");
            if (dt.Rows.Count > 0)
            {
                sLogin = dt.Rows[0]["LoginName"].ToString();
            }
            else
            {
                Message = "loginError";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }


            // 插入或更新信息
            sOKFlag = FConfigBll.DeleteBSFlowData(Item.BSApplyNo, sFlowNo, Item.F01, Item.F02, sLogin, "Del");// 第二个参数暂时不用，是新增还是修改在DAL判定


            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message, BSNo = Item.BSApplyNo });

            return Result;
        }
        #endregion


        #region 提交流程申请单，并发信息给相关人员
        public string SaveFlowAndBSInfo(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sFullName = string.Empty;
            string sStatus = string.Empty;
            string sPicPath = string.Empty;
            string sPath1 = string.Empty;
            string sPath2 = string.Empty;
            string sPath3 = string.Empty;
            string sName = string.Empty;
            string sOKFlag = string.Empty;
            string sFlag = string.Empty;
            string sFlowNo = string.Empty;
            string sFlowName = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sDate = string.Empty;
            string sAudM = string.Empty;
            string sComp = string.Empty;
            string sTNote = string.Empty;
            string sNote = string.Empty;
            string sAudList = string.Empty;
            int sW = 0; int iD = 0; 
            string iN = "0";
            string sNowAud = string.Empty;
            string sSentMan = string.Empty;
            string sMsg = string.Empty;
            string sSq = string.Empty;
            string HttpUrl = System.Configuration.ConfigurationManager.AppSettings["HttpUrl"];

            // BSApplyNo: BSApplyNo, FlowName: FlowName, Remark: Remark, LoginName: LoginName,PicPath: PicPath
            var AnonymousUser = new
            {
                BSApplyNo = String.Empty,
                FlowName = String.Empty,
                LoginName = String.Empty,
                FullName = String.Empty,
                Remark = String.Empty,
                PicPath = String.Empty,
                PicPathV = String.Empty,
                Note = String.Empty,
                Status = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            //用输入的账号，判断是否是否存在。
            sLogin = Item.LoginName;

            /*登录逻辑*/
            DataTable dt = FConfigBll.GetList(" LoginName='" + sLogin + "' and IsApproval=1 ");
            if (dt.Rows.Count > 0)
            {
                sLogin = dt.Rows[0]["LoginName"].ToString();
                sFullName = dt.Rows[0]["FullName"].ToString();
                sComp = dt.Rows[0]["CompanyNo"].ToString();
            }
            else
            {
                Message = "loginError";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }

            sW = Item.FlowName.IndexOf('(');  // 采购申请流程(FL1702210002)
            sFlowName = Item.FlowName; //Item.FlowName.Substring(0, Item.FlowName.Length - 1);
            sFlowNo = Item.FlowName.Substring(sW + 1, Item.FlowName.Length - sW - 2);  // FL1702210002

            // 获得当前单据状态
            sStatus = FConfigBll.GetApplyStatus(Item.BSApplyNo,"","","","1");

            if ((sStatus != "待提交") && (sStatus != "审批不通过"))
            {
                Message = "StatusError";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }

            // 把图片路径拆成单个图片路径。
            sPicPath = Item.PicPath;  // 图片路径
            List<string> listPath = sPicPath.Split(';').ToList();
            for (int i = 0; i < listPath.Count(); i++)
            {
                if (!string.IsNullOrEmpty(listPath[i]))
                {
                    if (sPath1 == "")
                    {
                        sPath1 = listPath[i].ToString();
                    }
                    else if (sPath2 == "")
                    {
                        sPath2 = listPath[i].ToString();
                    }
                    else if (sPath3 == "")
                    {
                        sPath3 = listPath[i].ToString();
                    }
                }
            }

            string sDelStr = " delete T_PicPath where sNo='" + Item.BSApplyNo + "' and Module='流程提交' "; 


            // 下面是插入上传图片的路径
            if (sPath1 != "")
            {
                sSQL = " insert into T_PicPath(sNo,Module,PicPath,Flag,CompanyNo,InMan) " +
                       " values('" + Item.BSApplyNo + "','流程提交','" + sPath1 + "','1','','" + sLogin + "') ";  // 图片附件一
            }
            if (sPath2 != "")
            {
                sSQL = sSQL + " insert into T_PicPath(sNo,Module,PicPath,Flag,CompanyNo,InMan) " +
                       " values('" + Item.BSApplyNo + "','流程提交','" + sPath2 + "','2','','" + sLogin + "') "; // 图片附件二
            }
            if (sPath3 != "")
            {
                sSQL = sSQL + " insert into T_PicPath(sNo,Module,PicPath,Flag,CompanyNo,InMan) " +
                       " values('" + Item.BSApplyNo + "','流程提交','" + sPath3 + "','3','','" + sLogin + "') ";  // 图片附件三
            }

            if (!string.IsNullOrEmpty(Item.PicPathV))
            {
                sSQL = sSQL + " insert into T_PicPath(sNo,Module,PicPath,Flag,CompanyNo,InMan) " +
                       " values('" + Item.BSApplyNo + "','视频-流程提交','" + Item.PicPathV + "','4','','" + sLogin + "') ";  // 保存视频
            }



            // 拼接保存审批节点的插入代码
            List<string> ListNote = Item.Note.Split('^').ToList();
            for (int i = 1; i < ListNote.Count; i++)
            {
                sTNote = ListNote[i];  // 主管审批AudMan:审核人；吴冬(wudong)；WD_冰冻(wd)   -- 无审核人的情况：1 主管审核AudMan:；吴冬(wudong)；WD_冰冻(wd)
                sW = sTNote.IndexOf("AudMan:");
                sNote = sTNote.Substring(0, sW);  //得到： 主管审批
                iD = sTNote.IndexOf("审核人");  // = -1
                if (iD <= 0)
                {
                    sW = sW + 8;
                }
                else
                {
                    sW = sW + 11;
                }

                if (i == 1)
                {
                    iN = "1";  // 先默认第一个节点为当前需要审批的节点
                    iD = sNote.IndexOf("_");  // = -1
                    sNowAud = sNote.Substring(iD + 1, sNote.Length - (iD + 1));
                }
                else
                {
                    iN = "0";
                }

                sAudList = sTNote.Substring(sW, sTNote.Length - sW).Replace("+", ""); //原来：吴冬(wudong)；+WD_冰冻(wd)   得到：吴冬(wudong)；WD_冰冻(wd)  -- 主要用来显示在流程节点里面

                //sSeqNo = i < 10 ? "S0" + i.ToString() : "S" + i.ToString();
                //sSQL = sSQL + "insert into T_FlowInfo(FlowNo,SeqNo,FlowName,Note,AudMan,CompanyNo,InMan) Values('" + sFlowNo + "','" + sSeqNo + "','" + Item.FName + "','" + sNote + "','" + sAudList + "','" + sComp + "','" + sLogin + "') ";


                // 拼接保存审批节点的插入代码
                List<string> ListMan = sTNote.Split('；').ToList();
                iD = sNote.IndexOf("_");  // = -1
                sSq = "S" + sNote.Substring(0, iD); //  6_人力资源经理审核 得到：6 
                if (Item.Status != "审核不通过")
                {
                    for (int j = 1; j < ListMan.Count; j++)
                    {
                        sAudM = ListMan[j];
                        sSQLs = sSQLs + "insert into T_BusinessDataAndFlow(BSNo,FlowNo,SeqNo,Note,AudManList,AudMan,NowAud,InMan) values('" + Item.BSApplyNo + "','" + sFlowNo + "','" + sSq + "','" + sNote + "','" + sAudList + "','" + sAudM + "','" + iN + "','" + sLogin + "') ";
                    }
                }
                else  // 如果是审核不通过，新增审批人，有 “+”号表示，这个时候只保存这些新增的审批人即可，
                {
                    iD = sTNote.IndexOf("+");    // 说明有新增的审批人;说明在被审核人打回来以后，新增了审批人员
                    if (iD > 0)
                    {
                        var iC = ListMan.Count - 1;
                        for (int j = 1; j < ListMan.Count; j++)
                        {
                            sAudM = ListMan[j];
                            iD = sAudM.IndexOf("+");  //+吴东(wudong001) 
                            var ssF = string.Empty;
                            
                            if (iD >= 0)  // 
                            {
                                ssF = "F";
                                sAudM = sAudM.Substring(1, sAudM.Length - 1); // 原来：+吴东(wudong001)   得到：吴东(wudong001) 
                                sSQLs = sSQLs + "insert into T_BusinessDataAndFlow(BSNo,FlowNo,SeqNo,Note,AudManList,AudMan,NowAud,InMan) values('" + Item.BSApplyNo + "','" + sFlowNo + "','" + sSq + "','" + sNote + "','" + sAudList + "','" + sAudM + "','" + iN + "','" + sLogin + "') ";
                            }

                            if ((ssF == "F") && (j == iC))  // 更新本节点的审批人列表一致
                            {
                                sSQLs = sSQLs + " update T_BusinessDataAndFlow set AudManList='" + sAudList + "' where SeqNo='" + sSq + "' ";
                            }
                        }
                    }
                }
            }


            // 更新记录 (sSQL,sBSNo, sFlowNo, sF01, sF02, sLogin, sFullName, Remark, sFlag);
            sOKFlag = FConfigBll.UpdateBSFlowData(sDelStr,sSQL,sSQLs, Item.BSApplyNo, sFlowNo, Item.LoginName, Item.FullName, Item.Remark,sNowAud, "1");


            if (sOKFlag.Length <= 2)
            {
                Message = "Success";

                // 发送微信信息给下一个审批人
                // 第一个需要审批节点的审批人
                sTNote = ListNote[1];  //主管审批AudMan:；吴冬(wudong)；WD_冰冻(wd)
                List<string> SentLit = sTNote.Split('(').ToList();
                for (int j = 1; j < SentLit.Count; j++)
                {
                    sAudM = SentLit[j];  // wudong)；WD_冰冻
                    sW = sAudM.IndexOf(")");
                    sAudM = sAudM.Substring(0, sW);
                    sSentMan = sSentMan + "|" + sAudM;  // |吴冬(wudong)|WD_冰冻(wd)
                }
                sSentMan = sSentMan.Substring(1, sSentMan.Length - 1);

                // 发微信信息
                try
                {
                    sDate = DateTime.Now.ToString("yyyy-MM-dd hh:mm");

                    DataTable Dt = WeiXinInfo.GetCorpSecret("", "LCGL", sComp);   // LCGL :流程管理
                    string CorpID = Dt.Rows[0]["CorpID"].ToString();
                    string Secret = Dt.Rows[0]["Secret"].ToString();
                    string toparty = Dt.Rows[0]["toparty"].ToString();
                    string agentid = Dt.Rows[0]["agentid"].ToString();
                    string sGZH = Dt.Rows[0]["GZHKind"].ToString();

                    Random rad = new Random();//实例化随机数产生器rad；
                    int value = rad.Next(100, 999);//用rad生成大于等于1000，小于等于9999的随机数；
                    string sR = value.ToString(); //转化为字符串；

                    // 发信息给业代 
                    sMsg = "您有流程审批，请点击进入" + "\n" + "流程名称：" + sFlowName + "\n" + "申请人：" + sFullName + "\n" + "申请时间：" + sDate + "\n" + "说明：" + Item.Remark;
                    string sUrl = HttpUrl + "Flow/AudList.htm?RNo=" + sR + "&CorpID=" + CorpID;
                    WXSendMessage.SentNewsToWeiXin(sSentMan, "流程审批提醒", sMsg, sUrl, CorpID, Secret, toparty, agentid, "G", sGZH); // 以新闻形式发，可以发链接的
                }
                catch
                {
                    Message = "SentError";
                    Result = JsonConvert.SerializeObject(new { Msg = Message }); // CLNo 主要是ChenLieList文件用
                }
            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }


            Result = JsonConvert.SerializeObject(new { Msg = Message, BSNo = Item.BSApplyNo });

            return Result;
        }
        #endregion


        #region 保存流程节点及节点审批人
        public string SaveNoteAndAudMan(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sFullName = string.Empty;
            string sStatus = string.Empty;
            string sSeqNo = string.Empty;
            string sOKFlag = string.Empty;
            string sFlowNo = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sTNote = string.Empty;
            string sNote = string.Empty;
            string sAudM = string.Empty;
            string sAudList = string.Empty;
            string sComp = string.Empty;
            int iD = 0;

            // BSApplyNo: BSApplyNo, FlowName: FlowName, Remark: Remark, LoginName: LoginName,PicPath: PicPath
            var AnonymousUser = new
            {
                FNo = String.Empty,
                FName = String.Empty,
                Note = String.Empty,
                SeqNo = String.Empty,
                Man = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            //用输入的账号，判断是否是否存在。
            sLogin = Item.Man;
            sFlowNo = Item.FNo;

            /*登录逻辑*/
            DataTable dt = FConfigBll.GetList(" LoginName='" + sLogin + "' and IsApproval=1 ");
            if (dt.Rows.Count > 0)
            {
                sLogin = dt.Rows[0]["LoginName"].ToString();
                sFullName = dt.Rows[0]["FullName"].ToString();
                sComp = dt.Rows[0]["CompanyNo"].ToString();
            }
            else
            {
                Message = "loginError";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }

            //sW = Item.FlowName.IndexOf('(');  // 采购申请流程(FL1702210002)
            //sFlowNo = Item.FlowName.Substring(sW + 1, Item.FlowName.Length - sW - 2);  // FL1702210002

            // 获得当前单据状态
            //sStatus = FConfigBll.GetApplyStatus(Item.BSApplyNo);

            //if ((sStatus != "待提交") && (sStatus != "审批不通过"))
            //{
            //    Message = "StatusError";
            //    return JsonConvert.SerializeObject(new { Msg = Message });
            //}


            // 拼接保存审批节点的插入代码
            List<string> ListNote = Item.Note.Split('^').ToList();
            for (int i = 1; i < ListNote.Count; i++)
            {
                sTNote = ListNote[i];  // 主管审批AudMan:；吴冬(wudong)；WD_冰冻(wd)
                iD=sTNote.IndexOf("AudMan:");
                sNote = sTNote.Substring(0,iD);  //得到： 主管审批
                if (sTNote.IndexOf("AudMan:；") > 0) // 说明这个审批节点选择了审批人，注意有一个“；”
                {
                    sAudList = sTNote.Substring(iD + 8, sTNote.Length - (iD + 8)); //得到：吴冬(wudong)；WD_冰冻(wd)  -- 主要用来显示在流程节点里面
                }
                else
                {
                    sAudList = "";
                }

                sSeqNo = i < 10 ? "S0" + i.ToString() : "S"+i.ToString();
                sSQL = sSQL + "insert into T_FlowInfo(FlowNo,SeqNo,FlowName,Note,AudManList,CompanyNo,InMan) Values('" + sFlowNo + "','" + sSeqNo + "','" + Item.FName + "','" + sNote + "','" + sAudList + "','" + sComp + "','" + sLogin + "') ";

                // 拼接保存审批节点的插入代码
                List<string> ListMan = sTNote.Split('；').ToList();
                for (int j = 1; j < ListMan.Count; j++)
                {
                    sAudM = ListMan[j];
                    sSQLs = sSQLs + "insert into T_FlowAudMan(FlowNo,SeqNo,AudMan,InMan) values('" + sFlowNo + "','" + sSeqNo + "','" + sAudM + "','" + sLogin + "') ";
                }
            }

            // 更新记录 (sSQL,sBSNo, sFlowNo, sF01, sF02, sLogin, sFullName, Remark, sFlag);
            sOKFlag = FConfigBll.InsertFlowInfo(sFlowNo,sSQL, sSQLs, sLogin, "1");


            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }


            Result = JsonConvert.SerializeObject(new { Msg = Message, FNo = Item.FNo });

            return Result;
        }
        #endregion


        #region 各个节点的审批人提交审批记录
        public string SaveAudRec(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sFullName = string.Empty;
            string sStatus = string.Empty;
            string sNStatus = string.Empty;
            string sBSNo = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sSendMan = string.Empty;
            string sTime = string.Empty;
            string sDate = string.Empty;
            string sMsg = string.Empty;
            string HttpUrl = System.Configuration.ConfigurationManager.AppSettings["HttpUrl"];
            int iD = 0;

            // BSNo:sBSNo,IsPass:sIsPass, AudDesc:sAudDesc,InMan:LoginName,Status
            var AnonymousUser = new
            {
                BSNo = String.Empty,
                Flow = String.Empty,
                IsPass = String.Empty,
                AudDesc = String.Empty,
                InMan = String.Empty,
                Status = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            //用输入的账号，判断是否是否存在。
            sLogin = Item.InMan;
            sBSNo = Item.BSNo;

            /*登录逻辑*/
            DataTable dt = FConfigBll.GetList(" LoginName='" + sLogin + "' and IsApproval=1 ");
            if (dt.Rows.Count > 0)
            {
                sLogin = dt.Rows[0]["LoginName"].ToString();
                sFullName = dt.Rows[0]["FullName"].ToString();
                sComp = dt.Rows[0]["CompanyNo"].ToString();
            }
            else
            {
                Message = "loginError";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }


            // 获得审核单据的下一个状态，如果为审核完成，则返回
            sStatus = FConfigBll.GetApplyStatus(sBSNo, Item.Status, Item.IsPass,sLogin, "2");  // 如果不是“NO” 说明这个状态是当前状态的下一个状态

            if (sStatus == "NO")
            {
                Message = "StatusError";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }
            else
            {
                iD = sStatus.IndexOf("_");  // = -1
                sNStatus = sStatus.Substring(iD + 1, sStatus.Length - (iD + 1)); //  6_人力资源经理审核 得到：人力资源经理审核 
            }

            // 查找一下这个状态是否还有没有审核完的人，如果是，则不用变状态。如果已审核完，返回下一个节点的审核人。
            // --1 返回当前准备审核的次数
            sTime = FConfigBll.GetApplyStatus(sBSNo, Item.Status,"", sLogin, "3");

            // (string sBSNo,string sStatus,string NStatus,string sTime, string sFlag)
            if (Item.IsPass == "通过")
            {
                sSendMan = FConfigBll.GetSendWeiXinMan(sBSNo, Item.Status, sNStatus, sTime, "1");  //  
            }
            else
            {
                sSendMan = FConfigBll.GetSendWeiXinMan(sBSNo, Item.Status, sNStatus, sTime, "2");  // 返回提单人
               // sNStatus = Item.Status; //  如果审核不通过的，在日志里面，这个状态记录当前状态即可。
            }

            if (sStatus == "已完成")  // 给流程发起人发信息，告诉流程已完成
            {
                sSendMan = FConfigBll.GetSendWeiXinMan(sBSNo, Item.Status, sNStatus, sTime, "2");  // 返回提单人
            }



            // 如果没有返回发送微信的人，则说明本节点，本轮未审批完成，不用给下一个节点发信息
            //sStatus = (sSendMan == "") ? Item.Status : sNStatus;

            // 更新记录 (string sDelStr,string sPicStr,string sSrs,string sBSNo, string sFlowNo, string sLogin,string sFullName,string Remark,string sNowAud, string sFlag)
            sOKFlag = FConfigBll.UpdateBSFlowData(sSendMan, sNStatus, Item.Status, sBSNo, sTime, sLogin, sFullName, Item.AudDesc, Item.IsPass, "2");


            if (sOKFlag.Length <= 2)
            {
                Message = "Success";

                // 发微信给下一个节点的人 -- 所有人审批完成以后
                if (sSendMan != "")
                {
                    try
                    {
                        sDate = DateTime.Now.ToString("yyyy-MM-dd hh:mm");

                        DataTable Dt = WeiXinInfo.GetCorpSecret("", "LCGL", sComp);   // LCGL :流程管理
                        string CorpID = Dt.Rows[0]["CorpID"].ToString();
                        string Secret = Dt.Rows[0]["Secret"].ToString();
                        string toparty = Dt.Rows[0]["toparty"].ToString();
                        string agentid = Dt.Rows[0]["agentid"].ToString();
                        string sGZH = Dt.Rows[0]["GZHKind"].ToString();

                        Random rad = new Random();//实例化随机数产生器rad；
                        int value = rad.Next(100, 999);//用rad生成大于等于1000，小于等于9999的随机数；
                        string sR = value.ToString(); //转化为字符串；


                        if (sStatus != "已完成")
                        {
                            sMsg = "您有流程审批，请点击进入" + "\n" + "流程名称：" + Item.Flow + "\n" + "申请人：" + sFullName + "\n" + "申请时间：" + sDate + "\n" + "说明：" + Item.AudDesc;
                        }
                        else
                        {
                            sMsg = "您提交的流程已完成！" + "\n" + "流程名称：" + Item.Flow + "\n" + "申请人：" + sFullName + "\n" + "申请时间：" + sDate + "\n" + "说明：" + Item.AudDesc;
                        }

                        string sUrl = HttpUrl + "Flow/AudList.htm?RNo=" + sR + "&CorpID=" + CorpID;
                        WXSendMessage.SentNewsToWeiXin(sSendMan, "流程审批提醒", sMsg, sUrl, CorpID, Secret, toparty, agentid, "G", sGZH); // 以新闻形式发，可以发链接的
                    }
                    catch
                    {
                        Message = "SentError";
                        Result = JsonConvert.SerializeObject(new { Msg = Message }); // CLNo 主要是ChenLieList文件用
                    }
                }
            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }


            Result = JsonConvert.SerializeObject(new { Msg = Message, BSNo = sBSNo });

            return Result;
        }
        #endregion


        #region  催办单据
        public string CBBill(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sFullName = string.Empty;
            string sStatus = string.Empty;
            string sBSNo = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sSendMan = string.Empty;
            string sMsg = string.Empty;
            string HttpUrl = System.Configuration.ConfigurationManager.AppSettings["HttpUrl"];

            // BSNo:sBSNo,IsPass:sIsPass, AudDesc:sAudDesc,InMan:LoginName,Status
            var AnonymousUser = new
            {
                BSNo = String.Empty,
                Flow = String.Empty,
                LoginName = String.Empty,
                Status = String.Empty,
                AudDesc = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            //用输入的账号，判断是否是否存在。
            sLogin = Item.LoginName;
            sBSNo = Item.BSNo;

            /*登录逻辑*/
            DataTable dt = FConfigBll.GetList(" LoginName='" + sLogin + "' and IsApproval=1 ");
            if (dt.Rows.Count > 0)
            {
                sLogin = dt.Rows[0]["LoginName"].ToString();
                sFullName = dt.Rows[0]["FullName"].ToString();
                sComp = dt.Rows[0]["CompanyNo"].ToString();
            }
            else
            {
                Message = "loginError";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }


            // 获得审核单据的下一个状态，如果为审核完成，则返回
            sStatus = FConfigBll.GetApplyStatus(sBSNo, Item.Status, "", sLogin, "1");  // 如果不是“NO” 说明这个状态是当前状态的下一个状态

            if (sStatus == "已完成")
            {
                Message = "StatusOver";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }

            sSendMan = FConfigBll.GetSendWeiXinMan(sBSNo, "", "", "", "3");  // 催办当前审核人


            // 发微信给下一个节点的人 -- 所有人审批完成以后
            if (sSendMan != "")
            {
                try
                {
                    DataTable Dt = WeiXinInfo.GetCorpSecret("", "LCGL", sComp);   // LCGL :流程管理
                    string CorpID = Dt.Rows[0]["CorpID"].ToString();
                    string Secret = Dt.Rows[0]["Secret"].ToString();
                    string toparty = Dt.Rows[0]["toparty"].ToString();
                    string agentid = Dt.Rows[0]["agentid"].ToString();
                    string sGZH = Dt.Rows[0]["GZHKind"].ToString();

                    Random rad = new Random();//实例化随机数产生器rad；
                    int value = rad.Next(100, 999);//用rad生成大于等于1000，小于等于9999的随机数；
                    string sR = value.ToString(); //转化为字符串；

                    sMsg = "催办：您有流程审批，请点击进入" + "\n" + "流程名称：" + Item.Flow + "\n" + "申请人：" + sFullName + "\n" + "\n" + "说明：" + Item.AudDesc;

                    string sUrl = HttpUrl + "Flow/AudList.htm?RNo=" + sR + "&CorpID=" + CorpID;
                    WXSendMessage.SentNewsToWeiXin(sSendMan, "流程审批提醒", sMsg, sUrl, CorpID, Secret, toparty, agentid, "G", sGZH); // 以新闻形式发，可以发链接的
                }
                catch
                {
                    Message = "SentError";
                    Result = JsonConvert.SerializeObject(new { Msg = Message }); // CLNo 主要是ChenLieList文件用
                }
            }


            Result = JsonConvert.SerializeObject(new { Msg = Message, BSNo = sBSNo });

            return Result;
        }
        #endregion



        #region  撤回单据
        public string CHBill(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sLogin = string.Empty;
            string sFullName = string.Empty;
            string sStatus = string.Empty;
            string sBSNo = string.Empty;
            string sComp = string.Empty;
            string sOKFlag = string.Empty;
            string sSendMan = string.Empty;
            string sMsg = string.Empty;
            string HttpUrl = System.Configuration.ConfigurationManager.AppSettings["HttpUrl"];


            // BSNo:sBSNo,IsPass:sIsPass, AudDesc:sAudDesc,InMan:LoginName,Status
            var AnonymousUser = new
            {
                BSNo = String.Empty,
                Flow = String.Empty,
                InMan = String.Empty,
                Status = String.Empty,
                CHDesc = String.Empty,
            };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

            //用输入的账号，判断是否是否存在。
            sLogin = Item.InMan;
            sBSNo = Item.BSNo;

            /*登录逻辑*/
            DataTable dt = FConfigBll.GetList(" LoginName='" + sLogin + "' and IsApproval=1 ");
            if (dt.Rows.Count > 0)
            {
                sLogin = dt.Rows[0]["LoginName"].ToString();
                sFullName = dt.Rows[0]["FullName"].ToString();
                sComp = dt.Rows[0]["CompanyNo"].ToString();
            }
            else
            {
                Message = "loginError";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }


            // 获得审核单据的下一个状态，如果为审核完成，则返回
            sStatus = FConfigBll.GetApplyStatus(sBSNo, Item.Status, "", sLogin, "1");  // 如果不是“NO” 说明这个状态是当前状态的下一个状态

            if (sStatus == "已完成")
            {
                Message = "StatusOver";
                return JsonConvert.SerializeObject(new { Msg = Message });
            }

            // 更新记录 (string sDelStr,string sPicStr,string sSrs,string sBSNo, string sFlowNo, string sLogin,string sFullName,string Remark,string sNowAud, string sFlag)
            sOKFlag = FConfigBll.UpdateBSFlowData(sSendMan, "", Item.Status, sBSNo, "", sLogin, sFullName, Item.CHDesc, "", "3");


            if (sOKFlag.Length <= 2)
            {
                Message = "Success";
            }
            else
            {
                Message = "Error" + sOKFlag;

                string Secret = "PUEpk6_aMU5BxyK_lU3KGmB7u59qXDaNKDhzv6yPYc__svuduCPBms8Vc6B3gl8a";//Dt.Rows[0]["Secret"].ToString();
                // WXSendMessage.SentNewsToWeiXin("wudong001", "插入更新数据是吧", sssss, "", "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");
                WXSendMessage.SentMessageToWeiXin("wudong001", Message, "wx9f1cde5f70b71497", Secret, "1", "1", "G", "达能公众号");//  G:标识个人发，如果给部门全体人发，随便字符即可
            }


            Result = JsonConvert.SerializeObject(new { Msg = Message, BSNo = sBSNo });

            return Result;
        }
        #endregion




        #region 测试生成一个新的编号-流程字段设置编号
        public static string CreateNewBillNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_ObjectFieldConfig where convert(char(10),InDate,120)='" + sDate + "' ", "ObjectNo");//F170124 00001
            if (sMaxNo == "")
            {
                sNo = "F" + CreateAllNo.CreateBillNo(2, 4) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(7, 5);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 5 - len;

                sNo = "F" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion


        #region 获取客户信息
        public string GetFlowAudMan(string Params)
        {
            string Result = string.Empty;
            string Message = "Success";
            string sMan = string.Empty;
            string sManName = string.Empty;
            string sItem = string.Empty;
            string sStatus = string.Empty;
            string sSeqNo = string.Empty;

            var AnonymousUser = new { NNAME = String.Empty };

            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sMan = HttpContext.Current.Session["LoginName"].ToString();
                sManName = HttpContext.Current.Session["FullName"].ToString();
            }
            else
            {
                Message = "LoginError";
                Result = JsonConvert.SerializeObject(new { Msg = Message, Login = sManName });
                return Result;
            }


            //用输入的账号，判断是否是否存在。
            DataTable dt = FConfigBll.GetList(" IsApproval=1 and (LoginName='" + Item.NNAME + "' or FullName ='" + Item.NNAME + "') ");
            if (dt.Rows.Count > 0)
            {
                Message = "Success";
            }
            else
            {
                Message = "NoUser";

                DataRow row = dt.NewRow();
                row["FullName"] = "无此用户，请确认";
                dt.Rows.Add(row);

                //return JsonConvert.SerializeObject(new { Msg = Message });
            }



            // 增加操作权限列
            DataColumn ORight = new DataColumn("ORight", typeof(string));
            ORight.DefaultValue = Message;  //HttpContext.Current.Session["LoginName"].ToString();
            dt.Columns.Add(ORight); // 增加一列



            HttpContext context = HttpContext.Current;

            string json = JsonConvert.SerializeObject(dt);
            context.Response.Write(json);

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;
        }
        #endregion


        #region 显示流程申请提交后的审核列表，主要显示给各个领导审批
        public string ShowBSAudList(string Params, string sNo)
        {
            string sCons = string.Empty;
            string sLoginMan = string.Empty;
            string sFullName = string.Empty;
            string sAudList = string.Empty;
            string sComp = string.Empty;
            string sBDate = string.Empty;
            string sEDate = string.Empty;
            string sSName = string.Empty;

            if (HttpContext.Current.Session["LoginName"] != null)
            {
                sLoginMan = HttpContext.Current.Session["LoginName"].ToString();
                sFullName = HttpContext.Current.Session["FullName"].ToString();
                sComp = HttpContext.Current.Session["CompanyNo"].ToString();

                sAudList = sFullName + "(" + sLoginMan + ")";
            }
            else
            {
                sLoginMan = "";
            }


            if (!string.IsNullOrEmpty(Params))
            {
                var AnonymousUser = new { BSName = String.Empty, BDate = String.Empty, EDate = String.Empty };

                var Item2 = JsonConvert.DeserializeObject(Params);
                var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);

                sBDate = Item.BDate;
                sEDate = Item.EDate;
                sSName = Item.BSName;

            }


            // 默认当然第一天，最后一天
            DateTime now = DateTime.Now;
            DateTime d1 = new DateTime(now.Year, now.Month, 1);
            DateTime d2 = d1.AddMonths(1).AddDays(-1);

            if (string.IsNullOrEmpty(sBDate) || (sBDate == null) || (sBDate == "null"))
            {
                sBDate = d1.ToString("yyyy-MM-dd");
            }
            if (string.IsNullOrEmpty(sEDate) || (sEDate == null) || (sEDate == "null"))
            {
                sEDate = d2.ToString("yyyy-MM-dd");
            }
            if (string.IsNullOrEmpty(sSName) || (sSName == null) || (sSName == "null"))
            {
                sSName = "";
            }


            if (sNo == "1")
            {
                sCons = " and ltrim(rtrim(a.BSNo))+ltrim(rtrim(a.Status)) in (select ltrim(rtrim(BSNo))+rtrim(substring(Note,charindex('_',Note)+1,100)) as Note from T_BusinessDataAndFlow where NowAud=1 and AudMan='" + sAudList + "') ";
            }
            else if (sNo == "2")  // 点击查询： 审核界面，审核人员可以随时看到自己审核的单据
            {
                sCons = "and a.BSName like '%" + sSName + "%' and convert(char(10),a.InDate,120) >='" + sBDate + "' and convert(char(10),a.InDate,120) <='" + sEDate + "' and a.BSNo in (select BSNo from T_FlowCommitLog where InMan='" + sLoginMan + "')";
            }

            return JsonConvert.SerializeObject(FConfigBll.ShowBusinessDataList(sCons,sComp, "2")); // 
        }
        #endregion


        #region 获取TXT文件信息
        public string GetTxtInfo(string Params, string No, string sFlag)
        {
            string Result = string.Empty;
            string Message = string.Empty;
            string sComp = string.Empty;
            string sTNo = string.Empty;
            string sTName = string.Empty;



            var AnonymousUser = new
            {
                No = String.Empty,Item = String.Empty,Path = String.Empty,A = String.Empty,B = String.Empty,C = String.Empty,D = String.Empty,Flag = String.Empty,
            };


            var Item2 = JsonConvert.DeserializeObject(Params);
            var Item = JsonConvert.DeserializeAnonymousType(JsonConvert.SerializeObject(Item2), AnonymousUser);


            try
            {
                Message = File.ReadAllText(Item.Path, Encoding.GetEncoding("GB2312"));
            }
            catch (Exception e)
            {
                // 处理读取文件失败的异常情况
            }

            Result = JsonConvert.SerializeObject(new { Msg = Message });

            return Result;

        }
        #endregion













        #region 测试生成一个新的编号-业务流程申请编号
        public static string CreateNewBSDatalNo()
        {
            string sDate = DateTime.Now.ToString("yyyy-MM-dd");
            string sNo = string.Empty;
            int i = 0;
            int iMax = 0;

            string sMaxNo = DBHelper.GetMaxNo("T_BusinessDataApply where convert(char(10),InDate,120)='" + sDate + "' ", "BSNo");//BS170224 00001
            if (sMaxNo == "")
            {
                sNo = "BS" + CreateAllNo.CreateBillNo(2, 4) + "1";
            }
            else
            {
                string sTemp = sMaxNo.Substring(8, 5);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                i = 5 - len;

                sNo = "BS" + CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
            }
            return sNo;

        }
        #endregion















        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}
