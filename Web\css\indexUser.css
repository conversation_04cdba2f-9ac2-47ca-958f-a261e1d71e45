::-webkit-scrollbar-track{border-radius: 10px;-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0);}
::-webkit-scrollbar-thumb{background-color: rgba(0,0,0,0.05);border-radius: 10px;-webkit-box-shadow: inset 1px 1px 0 rgba(0,0,0,.1);}
::-webkit-scrollbar-thumb{background-color: rgba(0,0,0,0.2);border-radius: 10px;-webkit-box-shadow: inset 1px 1px 0 rgba(0,0,0,.1);}
::-webkit-scrollbar{width: 16px;height: 16px;}
::-webkit-scrollbar-track,
::-webkit-scrollbar-thumb{border-radius: 999px;border: 5px solid transparent;}
::-webkit-scrollbar-track{box-shadow: 1px 1px 5px rgba(0,0,0,.2) inset;}
::-webkit-scrollbar-thumb{min-height: 20px;background-clip: content-box;box-shadow: 0 0 0 5px rgba(0,0,0,.2) inset;}
::-webkit-scrollbar-corner{background: transparent;}
html,body{min-height:100%;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;font-family:'Source Sans Pro','Helvetica Neue',Helvetica,Arial,sans-serif;font-weight:400;font-size:12px;}
.wrapper{min-height:100%;position:relative;overflow:hidden}
.wrapper:before,.wrapper:after{content:" ";display:table}
.wrapper:after{clear:both}
@media (max-width:767px){.content-wrapper,.right-side,.main-footer{margin-left:0}}@media (min-width:768px){.sidebar-collapse .content-wrapper,.sidebar-collapse .right-side,.sidebar-collapse .main-footer{margin-left:0}}@media (max-width:767px){.sidebar-open .content-wrapper,.sidebar-open .right-side,.sidebar-open .main-footer{-webkit-transform:translate(230px, 0);-ms-transform:translate(230px, 0);-o-transform:translate(230px, 0);transform:translate(230px, 0)}}.content-wrapper,.right-side{min-height:100%;background-color:#ecf0f5;z-index:800}
body.hold-transition .content-wrapper,body.hold-transition .right-side,body.hold-transition .main-footer,body.hold-transition .main-sidebar,body.hold-transition .left-side,body.hold-transition .main-header>.navbar,body.hold-transition .main-header .logo{-webkit-transition:none;-o-transition:none;transition:none}
h1,h2,h3,h4,h5,h6,.h1,.h2,.h3,.h4,.h5,.h6{font-family:'Source Sans Pro',sans-serif}
a{color:#475059}
a:hover,a:active,a:focus{outline:none;text-decoration:none;color:#72afd2}
.main-header{position:relative;max-height:100px;z-index:1030}
.main-header>.navbar{-webkit-transition:margin-left .3s ease-in-out;-o-transition:margin-left .3s ease-in-out;transition:margin-left .3s ease-in-out;margin-bottom:0;margin-left:230px;border:none;min-height:50px;border-radius:0}
.main-header #navbar-search-input.form-control{background:rgba(255,255,255,0.2);border-color:transparent}
.main-header #navbar-search-input.form-control:focus,.main-header #navbar-search-input.form-control:active{border-color:rgba(0,0,0,0.1);background:rgba(255,255,255,0.9)}
.main-header #navbar-search-input.form-control::-moz-placeholder{color:#ccc;opacity:1}
.main-header #navbar-search-input.form-control:-ms-input-placeholder{color:#ccc}
.main-header #navbar-search-input.form-control::-webkit-input-placeholder{color:#ccc}
.main-header .navbar-custom-menu,.main-header .navbar-right{float:right}
@media (max-width:991px){.main-header .navbar-custom-menu a,.main-header .navbar-right a{color:inherit;background:transparent}}@media (max-width:767px){.main-header .navbar-right{float:none}
.main-header .navbar-right>li{color:inherit;border:0}}.main-header .sidebar-toggle{float:left;background-color:transparent;background-image:none;padding:16px 15px;font-family:fontAwesome}
.main-header .sidebar-toggle:before{content:"\f0c9"}
.main-header .sidebar-toggle:hover{color:#fff;cursor:pointer;}
.main-header .sidebar-toggle:focus,.main-header .sidebar-toggle:active{background:transparent}
.main-header .sidebar-toggle .icon-bar{display:none}
.main-header .navbar .nav>li.user>a>.fa,.main-header .navbar .nav>li.user>a>.glyphicon,.main-header .navbar .nav>li.user>a>.ion{margin-right:5px}
.main-header .navbar .nav>li>a>.label{position:absolute;top:9px;right:7px;text-align:center;font-size:9px;padding:2px 3px;line-height:.9}
.main-header .logo{-webkit-transition:width .3s ease-in-out;-o-transition:width .3s ease-in-out;transition:width .3s ease-in-out;display:block;float:left;height:52px;font-size:20px;line-height:52px;text-align:center;width:230px;font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;padding:0 15px;font-weight:300;overflow:hidden}
.main-header .logo .logo-lg{display:block;font-weight: 700;font-size: 20px;text-shadow: 3px 3px 7px #000;}

.main-header .logo .logo-lg strong{font-size: 20px;padding-left:3px;}
.main-header .logo .logo-mini{display:none;font-weight: bold;font-size: 25px;text-shadow: 3px 3px 7px #000;}
.main-header .navbar-brand{color:#fff}
.content-header{position:relative;padding:15px 15px 0 15px}
.content-header>h1{margin:0;font-size:24px}
.content-header>h1>small{font-size:15px;display:inline-block;padding-left:4px;font-weight:300}
.content-header>.breadcrumb{float:right;background:transparent;margin-top:0;margin-bottom:0;font-size:12px;padding:7px 5px;position:absolute;top:15px;right:10px;border-radius:2px}
.content-header>.breadcrumb>li>a{color:#444;text-decoration:none;display:inline-block}
.content-header>.breadcrumb>li>a>.fa,.content-header>.breadcrumb>li>a>.glyphicon,.content-header>.breadcrumb>li>a>.ion{margin-right:5px}
.content-header>.breadcrumb>li+li:before{content:'>\00a0'}
@media (max-width:991px){.content-header>.breadcrumb{position:relative;margin-top:5px;top:0;right:0;float:none;background:#d2d6de;padding-left:10px}
.content-header>.breadcrumb li:before{color:#97a0b3}}.navbar-toggle{color:#fff;border:0;margin:0;padding:15px 15px}
@media (max-width:991px){.navbar-custom-menu .navbar-nav>li{float:left}
.navbar-custom-menu .navbar-nav{margin:0;float:left}
.navbar-custom-menu .navbar-nav>li>a{padding-top:15px;padding-bottom:15px;line-height:20px}}@media (max-width:767px){.main-header{position:relative}
.main-header .logo,.main-header .navbar{width:100%;float:none}
.main-header .navbar{margin:0}
.main-header .navbar-custom-menu{float:right}}@media (max-width:991px){.navbar-collapse.pull-left{float:none !important}
.navbar-collapse.pull-left+.navbar-custom-menu{display:block;position:absolute;top:0;right:40px}}.main-sidebar,.left-side{position:absolute;top:0;left:0;padding-top:50px;min-height:100%;width:230px;z-index:810;-webkit-transition:-webkit-transform .3s ease-in-out,width .3s ease-in-out;-moz-transition:-moz-transform .3s ease-in-out,width .3s ease-in-out;-o-transition:-o-transform .3s ease-in-out,width .3s ease-in-out;transition:transform .3s ease-in-out,width .3s ease-in-out}
@media (max-width:767px){.main-sidebar,.left-side{padding-top:100px}}@media (max-width:767px){.main-sidebar,.left-side{-webkit-transform:translate(-230px, 0);-ms-transform:translate(-230px, 0);-o-transform:translate(-230px, 0);transform:translate(-230px, 0)}}@media (min-width:768px){.sidebar-collapse .main-sidebar,.sidebar-collapse .left-side{-webkit-transform:translate(-230px, 0);-ms-transform:translate(-230px, 0);-o-transform:translate(-230px, 0);transform:translate(-230px, 0)}}@media (max-width:767px){.sidebar-open .main-sidebar,.sidebar-open .left-side{-webkit-transform:translate(0, 0);-ms-transform:translate(0, 0);-o-transform:translate(0, 0);transform:translate(0, 0)}}.sidebar{padding-bottom:10px}
.sidebar-form input:focus{border-color: transparent;}
.sidebar-form .input-group-btn {font-size:14px;}
.user-panel{position: relative;width: 100%;padding: 10px;overflow: hidden;}
.user-panel:before,
.user-panel:after{content: " ";display: table;}
.user-panel:after{clear: both;}
.user-panel > .image > img{width: 100%;max-width: 45px;height: auto;}
.user-panel > .info{padding: 5px 5px 5px 15px;line-height: 1;position: absolute;left: 55px;top:0px;}
.user-panel > .info > p{font-weight: 600;margin-bottom: 9px;}
.user-panel > .info > a{text-decoration: none;padding-right: 5px;margin-top: 3px;font-size: 11px;}
.user-panel > .info > a > .fa,
.user-panel > .info > a > .ion,
.user-panel > .info > a > .glyphicon{margin-right: 5px;}
.sidebar-menu{list-style:none;margin:0;padding:0}
.sidebar-menu>li{position:relative;margin:0;padding:0}
.sidebar-menu>li>a{padding:18px 5px 18px 15px;display:block;font-size:12px;}
.sidebar-menu>li>a>.fa,.sidebar-menu>li>a>.glyphicon,.sidebar-menu>li>a>.ion{width:20px;}
.sidebar-menu>li .label,.sidebar-menu>li .badge{margin-top:3px;margin-right:5px}
.sidebar-menu li.header{padding:10px 25px 10px 15px;font-size:12px}
.sidebar-menu li>a>.fa-angle-left{width:auto;height:auto;padding:0;margin-right:10px;margin-top:3px}
.sidebar-menu li.active>a>.fa-angle-left{-webkit-transform:rotate(-90deg);-ms-transform:rotate(-90deg);-o-transform:rotate(-90deg);transform:rotate(-90deg)}
.sidebar-menu li.active>.treeview-menu{display:block;}
.sidebar-menu .treeview-menu{display:none;list-style:none;padding:0;margin:0;padding-left:20px;padding-top: 5px;padding-bottom:5px;}
.sidebar-menu .treeview-menu .treeview-menu{padding-left:20px}
.sidebar-menu .treeview-menu>li{margin:0;line-height:25px;}
.sidebar-menu .treeview-menu>li>a{padding:5px 5px 5px 15px;display:block;font-size:12px}
.sidebar-menu .treeview-menu>li>a>.fa,.sidebar-menu .treeview-menu>li>a>.glyphicon,.sidebar-menu .treeview-menu>li>a>.ion{width:20px;}
.sidebar-menu .treeview-menu>li>a>.fa-angle-left,.sidebar-menu .treeview-menu>li>a>.fa-angle-down{width:auto}
@media (min-width:768px){.sidebar-mini.sidebar-collapse .content-wrapper,.sidebar-mini.sidebar-collapse .right-side,.sidebar-mini.sidebar-collapse .main-footer{margin-left:50px !important;z-index:840}
.sidebar-mini.sidebar-collapse .main-sidebar{-webkit-transform:translate(0, 0);-ms-transform:translate(0, 0);-o-transform:translate(0, 0);transform:translate(0, 0);width:50px !important;z-index:850}
.sidebar-mini.sidebar-collapse .sidebar-menu>li{position:relative}
.sidebar-mini.sidebar-collapse .sidebar-menu>li>a{margin-right:0}
.sidebar-mini.sidebar-collapse .sidebar-menu>li>a>span{border-top-right-radius:4px}
.sidebar-mini.sidebar-collapse .sidebar-menu>li:not(.treeview)>a>span{border-bottom-right-radius:4px}
.sidebar-mini.sidebar-collapse .sidebar-menu>li>.treeview-menu{padding-top:5px;padding-bottom:5px;border-bottom-right-radius:4px}
.sidebar-mini.sidebar-collapse .sidebar-menu>li:hover>a>span:not(.pull-right),.sidebar-mini.sidebar-collapse .sidebar-menu>li:hover>.treeview-menu{display:block !important;position:absolute;width:180px;left:50px}
.sidebar-mini.sidebar-collapse .sidebar-menu>li:hover>a>span{top:0;margin-left:-3px;padding:12px 5px 12px 20px;background-color:inherit}
.sidebar-mini.sidebar-collapse .sidebar-menu>li:hover>.treeview-menu{top:40px;margin-left:-0.5px}
.sidebar-mini.sidebar-collapse .main-sidebar .user-panel>.info,.sidebar-mini.sidebar-collapse .sidebar-form,.sidebar-mini.sidebar-collapse .sidebar-menu>li>a>span,.sidebar-mini.sidebar-collapse .sidebar-menu>li>.treeview-menu,.sidebar-mini.sidebar-collapse .sidebar-menu>li>a>.pull-right,.sidebar-mini.sidebar-collapse .sidebar-menu li.header{display:none !important;-webkit-transform:translateZ(0)}
.sidebar-mini.sidebar-collapse .main-header .logo{width:50px}
.sidebar-mini.sidebar-collapse .main-header .logo>.logo-mini{display:block;margin-left:-15px;margin-right:-15px;font-size:18px}
.sidebar-mini.sidebar-collapse .main-header .logo>.logo-lg{display:none}
.sidebar-mini.sidebar-collapse .main-header .navbar{margin-left:50px}}.sidebar-menu,.main-sidebar .user-panel,.sidebar-menu>li.header{white-space:nowrap;overflow:hidden}
.sidebar-menu:hover{overflow:visible}
.sidebar-menu>li.header{overflow:hidden;text-overflow:clip}
.sidebar-menu li>a{position:relative}
.sidebar-menu li>a>.pull-right{position:absolute;right:10px;top:50%;margin-top:-7px}
.dropdown-menu{border-color:#eee;}
.dropdown-menu>li>a{color:#777;font-size:12px;}
.dropdown-menu>li>a>.glyphicon,.dropdown-menu>li>a>.fa,.dropdown-menu>li>a>.ion{margin-right:10px}
.dropdown-menu>li>a:hover{background-color:#e1e3e9;color:#333}
.dropdown-menu>.divider{background-color:#eee}
@media (max-width:991px){.navbar-nav>.user-menu>.dropdown-menu>.user-footer .btn-default:hover{background-color:#f9f9f9}}.navbar-nav>.user-menu .user-image{float:left;width:25px;height:25px;border-radius:50%;margin-right:10px;margin-top:-2px}
@media (max-width:767px){.navbar-nav>.user-menu .user-image{float:none;margin-right:0;margin-top:-8px;line-height:10px}}.open:not(.dropup)>.animated-dropdown-menu{backface-visibility:visible !important;-webkit-animation:flipInX .7s both;-o-animation:flipInX .7s both;animation:flipInX .7s both}

.content-wrapper {margin-left: 230px;}
.content-wrapper .content-tabs{position:relative;height:41px;line-height:39.5px;background:#fafafa;border-bottom:solid 2px #222d32;font-size:12px;}
.content-wrapper .content-tabs .roll-nav,.page-tabs-list{position:absolute;width:40px;height:39.5px;text-align:center;color:#475059;z-index:2;top:0}
.content-wrapper .content-tabs .roll-left{left:0;border-right:solid 1px #ddd}
.content-wrapper .content-tabs .roll-right{right:0;border-left:solid 1px #ddd}
.content-wrapper .content-tabs button{background:#fff;border:0;height:39px;line-height:39px;width:40px;outline:0}
.content-wrapper .content-tabs button:hover{background:#fafafa}
.content-wrapper .content-tabs button i{color: #999;}
.content-wrapper .content-tabs nav.page-tabs{margin-left:40px;width:100000px;height:40px;overflow:hidden}
.content-wrapper .content-tabs nav.page-tabs .page-tabs-content{float:left}
.content-wrapper .content-tabs .page-tabs a{display:block;float:left;border-right:solid 0px #ddd;padding:0 15px;padding-right:8px;text-decoration:none;color:#475059;}
.content-wrapper .content-tabs .page-tabs a:first-child{padding-right:15px;}
/*.content-wrapper .content-tabs .page-tabs a.active{background: #222d32;color: #fff;border-right: solid 1px #2c3e50;}*/
.content-wrapper .content-tabs .page-tabs a i{margin-top:-10px;right: 0px;position: relative;color: #999;}
.content-wrapper .content-tabs .page-tabs a i:hover{color:#c00 !important;}
.content-wrapper .content-tabs .page-tabs a.active i:hover{color:#fff !important;}
.content-wrapper .content-tabs .roll-right.tabRight{right:120px}
.content-wrapper .content-tabs .roll-right.btn-group{right:40px;width:80px;padding:0}
.content-wrapper .content-tabs .roll-right.btn-group button{width:80px}
.content-wrapper .content-tabs .roll-right.tabExit{background:#fff;height:39px;width:40px;outline:0}
.content-wrapper .content-tabs .dropdown-menu{border:solid 1px #ccc;border-top-left-radius:0px;border-top-right-radius:0px;right:-1px;}
.index_top{
font-size: 20px;
    padding-left: 3px;
    font-weight: 700;
    text-shadow: 3px 3px 7px #000;
    color: #ffffff;
    line-height: 52px;
}

/*color*/
.color_ul{color: #000 ;list-style: none;width: 82px !important;padding: 4px 0 8px 2px !important;}
.color_ul li{ float: left !important;width: 20px;height: 20px;margin: 3px;cursor: pointer}


/*home*/
#copyrightcontent {
    height: 30px;
    line-height: 29px;
    overflow: hidden;
    position: absolute;
    top: 100%;
    margin-top: -30px;
    width: 100%;
    background-color: #fff;
    border: 1px solid #e6e6e6;
    padding-left: 10px;
    padding-right: 10px;
}

.dashboard-stats {
    float: left;
    width: 25%;
}

.dashboard-stats-item {
    position: relative;
    overflow: hidden;
    color: #fff;
    cursor: pointer;
    height: 105px;
    margin-right: 10px;
    margin-bottom: 10px;
    padding-left: 15px;
    padding-top: 20px;
}

.dashboard-stats-item .m-top-none {
    margin-top: 5px;
}

.dashboard-stats-item h2 {
    font-size: 28px;
    font-family: inherit;
    line-height: 1.1;
    font-weight: 500;
    padding-left: 70px;
}

.dashboard-stats-item h2 span {
    font-size: 12px;
    padding-left: 5px;
}

.dashboard-stats-item h5 {
    font-size: 12px;
    font-family: inherit;
    margin-top: 1px;
    line-height: 1.1;
    padding-left: 70px;
}


.dashboard-stats-item .stat-icon {
    position: absolute;
    top: 18px;
    font-size: 50px;
    opacity: .3;
}

.dashboard-stats i.fa.stats-icon {
    width: 50px;
    padding: 20px;
    font-size: 50px;
    text-align: center;
    color: #fff;
    height: 50px;
    border-radius: 10px;
}

.panel-default {
    border: none;
    border-radius: 0px;
    margin-bottom: 0px;
    box-shadow: none;
    -webkit-box-shadow: none;
}

.panel-default > .panel-heading {
    color: #777;
    background-color: #fff;
    border-color: #e6e6e6;
    padding: 10px 10px;
}

.panel-default > .panel-body {
    padding: 10px;
    padding-bottom: 0px;
}

.panel-default > .panel-body ul {
    overflow: hidden;
    padding: 0;
    margin: 0px;
    margin-top: -5px;
}

.panel-default > .panel-body ul li {
    line-height: 27px;
    list-style-type: none;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.panel-default > .panel-body ul li .time {
    color: #a1a1a1;
    float: right;
    padding-right: 5px;
}

/*user_tail*/
*,h1,label,td,tr{padding: 0 ;margin: 0 }
.l_left{float: left}
.r_right{float: right}
td{border: none !important;}
::-webkit-input-placeholder,:-ms-input-placeholder  {color:#999;}
.table tr td:nth-child(2n+1){text-align: right;font-size: 12px;width:84px;line-height: 28px;}
.top_out{margin: 20px 0 20px 0}
.top td{padding: 0 3px 8px 0 !important;}
.top td .span{color: red;margin:3px 1px 0 2px;display: inline-block ;float: right;vertical-align: middle;line-height: 28px;width: 6px;text-align: center;}
.top select,.top input[type=text],.top input[type=password],.top input[type=email]{text-align: left;width: 247px;height:28px;border-radius: 4px;font-size: 12px;border: 1px #ccc solid}
.top td textarea{width: 586px;height: 70px;resize: none;border-radius: 4px;border: 1px #ccc solid}
.notice_bot{position: fixed;bottom: 0;background-color: #ecf0f5;padding: 5px 0 5px 0;z-index:9999;border-top:1px #ccc solid;width: 100%;}
.notice_bot>div{font-size: 12px;margin-left: 10px;color: #07141e;line-height: 28px}
.notice_bot>div input{margin:0 2px 0 0;vertical-align: middle}
.div_form>div{margin: 5px}
.but_p{margin-right: 10px}
.but_p button{width: 58px;height: 28px;line-height: 28px;border: none;background-color: #5cb85c;color: #fff;border-radius: 4px;cursor: pointer}
.but_p .but_close{background-color: #d9534f;margin-left: 10px}
.top .td_select{width: 78px;margin-right: 2px}
.dropdown_select{border: 1px #ccc solid;width: 247px;height: 28px;border-radius: 4px;position: relative;cursor: pointer;font-size: 12px}
.caret{position: relative;left: 230px;top:-14px}

.skin-blue .main-header .logo1{background-color: #00a65a;color: #ffffff;border-bottom: 0 solid transparent;}
.skin-blue .main-header .navbar1{background-color: #00a65a;}
.skin-blue .left-side1{background-color: #222d32;}
.skin-blue .sidebar-menu > li.header1{color: #4b646f;background: #1a2226;}
.skin-blue .sidebar-menu > li.active > a .a0{color: #ffffff;background: #1e282c;border-left-color: #3c8dbc;}
.skin-blue .sidebar-menu > li > .treeview-menu1{margin: 0 1px;background: #2c3b41;}
.content-wrapper .content-tabs .page-tabs a.active{background: #222d32;color: #fff;border-right: solid 1px #2c3e50;}

.skin-blue .main-header .logo2{background-color: #a92626;color: #ffffff;border-bottom: 0 solid transparent;}
.skin-blue .main-header .navbar2{background-color: #a92626;}
.skin-blue .left-side2{background-color: #963232;}
.skin-blue .sidebar-menu > li.header2{color: #eedcdc;background: #571d1d;}
.skin-blue .sidebar-menu > li.active > a .a2{color: #ffffff;background: #1e282c;border-left-color: lightseagreen;}
.skin-blue .sidebar-menu > li > .treeview-menu2{margin: 0 1px;background: #ad3939;}
.table .table_th{background-color: #e9dfd7 !important;}

.skin-blue .main-header .logo3{background-color: #421a70;color: #ffffff;border-bottom: 0 solid transparent;}
.skin-blue .main-header .navbar3{background-color: #421a70;}
.skin-blue .left-side3{background-color: #1a0a2c;}
.skin-blue .sidebar-menu > li.header3{color: #eedcdc;background: #260f40;}
.skin-blue .sidebar-menu > li.active > a .a3{color: #ffffff;background: #2c114a;border-left-color: lightseagreen;}
.skin-blue .sidebar-menu > li > .treeview-menu3{margin: 0 1px;background: #2c114a;}

.skin-blue .main-header .logo4{background-color: #d6443a;color: #ffffff;border-bottom: 0 solid transparent;}
.skin-blue .main-header .navbar4{background-color: #d6443a;}
.skin-blue .left-side4{background-color: #d6443a;}
.skin-blue .sidebar-menu > li.header4{color: #eedcdc;background: #a6352d;}
.skin-blue .sidebar-menu > li.active > a .a4{color: #ffffff;background: #a6352d;border-left-color: lightseagreen;}
.skin-blue .sidebar-menu > li > .treeview-menu4{margin: 0 1px;background: #a6352d;}


.skin-blue .main-header .logo5{background-color: #167ae0;color: #ffffff;border-bottom: 0 solid transparent;}
.skin-blue .main-header .navbar5{background-color: #167ae0;}
.skin-blue .left-side5{background-color: #18436f;}
.skin-blue .sidebar-menu > li.header5{color: #eedcdc;background: #0f2740;}
.skin-blue .sidebar-menu > li.active > a .a5{color: #ffffff;background: #112d4a;border-left-color: lightseagreen;}
.skin-blue .sidebar-menu > li > .treeview-menu5{margin: 0 1px;background: #112d4a;}




