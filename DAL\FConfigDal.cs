﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using Common;
using System.Text;


namespace DAL
{
    public partial class FConfigDal
    {

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public static DataTable GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select LoginName,FullName,Pwd,Kind,BSC,CompanyNo,LogoPic,DaQu,BanShiChu,ZhuGuan,WeiXinNo,QyNo FROM T_SPUser ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DBHelper.GetDataTable(strSql.ToString());

        }


        /// <summary>
        /// 返回登录人是否有操作模块的权限
        /// </summary>
        /// <param name="InMan"></param>
        /// <param name="Module"></param>
        /// <returns></returns>
        public static string JustOperateRight(string InMan, string Module)
        {
            string sSQL = string.Empty;
            string sBStr = string.Empty;

            // 判断登录人的身份
            sSQL = "select a.LoginName,a.RoleID,c.ModuleParent,c.ModuleName from T_SPUserRole a " +
                   "join T_SPRoleModule b on a.RoleID=b.RoleID " +
                   "join T_SPModule c on b.ModuleID=c.ModuleID " +
                   "where a.LoginName='" + InMan + "' and c.ModuleCode='" + Module + "' ";
            DataTable sdt = DBHelper.GetDataTable(sSQL);
            if (sdt.Rows.Count > 0)
            {
                sBStr = "YES";
            }
            else
            {
                sBStr = "NO";
            }

            return sBStr;
        }

        /// <summary>
        /// 根据传入的查询条件，显示各种数据的列表
        /// </summary>
        /// <param name="Con1">条件一</param>
        /// <param name="Con2">条件二</param>
        /// <param name="Con3">条件三</param>
        /// <param name="Con4">条件四</param>
        /// <param name="CompNo">企业编号</param>
        /// <param name="sFlag">标识</param>
        /// <returns></returns>
        public static DataTable GetListByCondition(string Con1,string Con2,string Con3,string Con4,string CompNo,string sFlag)
        {
            string sSQL = string.Empty;
            string sBStr = string.Empty;

            if (sFlag == "1")  // 对象字段列表
            {
                sSQL = "select * from T_ObjectFieldConfig where CompanyNo='" + CompNo + "' and ObjectName like '%" + Con1 + "%' and ObjectNo like '" + Con2 + "%' ";
            }
            else if (sFlag == "2")  // 显示对象字段明细
            {
                sSQL = "select a.ObjectNo,a.ObjectName,a.FOne,a.FTwo,a.FThree,a.FFour,a.FFive,a.FSix,a.FSeven,a.FEight,a.FNine,a.FTen,isnull(b.S01,'') as S01,isnull(b.S02,'') as S02,a.Remark, "+
                       "isnull(b.S03,'') as S03,isnull(b.S04,'') as S04,isnull(b.S05,'') as S05,isnull(b.S06,'') as S06,isnull(b.S07,'') as S07,isnull(b.S08,'') as S08,isnull(b.S09,'') as S09, "+
                       "isnull(b.S10,'') as S10,isnull(b.S11,'') as S11,isnull(b.S12,'') as S12,isnull(b.S13,'') as S13,isnull(b.S14,'') as S14,isnull(b.S15,'') as S15,isnull(c.M01,'') as M01, "+
                       "isnull(c.M02,'') as M02,isnull(c.M03,'') as M03,isnull(c.M04,'') as M04,isnull(c.M05,'') as M05,isnull(c.M06,'') as M06,isnull(c.M07,'') as M07,isnull(c.M08,'') as M08, " +
                       "isnull(c.M09,'') as M09,isnull(c.M10,'') as M10,isnull(c.M11,'') as M11,isnull(c.M12,'') as M12,isnull(c.M13,'') as M13,isnull(c.M14,'') as M14,isnull(c.M15,'') as M15,a.InMan " + 
                       "from T_ObjectFieldConfig a "+
                       "left join "+
                       " (select FlowNo,isnull(MAX(S01),'') as S01,isnull(MAX(S02),'') as S02,isnull(MAX(S03),'') as S03,isnull(MAX(S04),'') as S04,isnull(MAX(S05),'') as S05,isnull(MAX(S06),'') as S06,isnull(MAX(S07),'') as S07, "+
                       "   isnull(MAX(S08),'') as S08,isnull(MAX(S09),'') as S09,isnull(MAX(S10),'') as S10,isnull(MAX(S11),'') as S11,isnull(MAX(S12),'') as S12,isnull(MAX(S13),'') as S13,isnull(MAX(S14),'') as S14,isnull(MAX(S15),'') as S15 "+
                       "  from(select * from T_FlowInfo pivot(max(Note) for SeqNo in ([S01],[S02],[S03],[S04],[S05],[S06],[S07],[S08],[S09],[S10],[S11],[S12],[S13],[S14],[S15])) a where FlowNo='"+Con2+"' "+
                       " ) aa group by FlowNo "+
                       ") b on a.ObjectNo=b.FlowNo "+
                       "left join "+
                       "(select FlowNo,isnull(MAX(S01),'') as M01,isnull(MAX(S02),'') as M02,isnull(MAX(S03),'') as M03,isnull(MAX(S04),'') as M04,isnull(MAX(S05),'') as M05,isnull(MAX(S06),'') as M06,isnull(MAX(S07),'') as M07, isnull(MAX(S08),'') as M08, "+
                       "   isnull(MAX(S09),'') as M09,isnull(MAX(S10),'') as M10,isnull(MAX(S11),'') as M11,isnull(MAX(S12),'') as M12,isnull(MAX(S13),'') as M13,isnull(MAX(S14),'') as M14,isnull(MAX(S15),'') as M15 "+
                       " from(select * from T_FlowInfo pivot(max(AudManList) for SeqNo in ([S01],[S02],[S03],[S04],[S05],[S06],[S07],[S08],[S09],[S10],[S11],[S12],[S13],[S14],[S15])) a where FlowNo='"+Con2+"' "+ 
                       " ) aa group by FlowNo "+
                       ") c on a.ObjectNo=c.FlowNo where a.CompanyNo='" + CompNo + "' and a.ObjectNo = '" + Con2 + "' ";
            }


            DataTable sdt = DBHelper.GetDataTable(sSQL);

            return sdt;

        }


        /// <summary>
        /// 插入或更新流程对象字段记录
        /// </summary>
        /// <param name="OBF"> 流程对象字段食堂</param>
        /// <param name="sNEFlag">N:新增；E:更新</param>
        /// <returns></returns>
        public static string InsertOrUpdateObjectField(Model.ObjectField OBF,string sNEFlag)
        {
            string iFlag = string.Empty;
            string sstr = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sNo = string.Empty;
            string sChDes = string.Empty;
            string sK = string.Empty;
            int iMax;

            // 流程对象编号是：F17012400001 01
            string sMaxNo = DBHelper.GetMaxNo("T_ObjectFieldConfigLog where ChangeNo like '" + OBF.ObjectNo + "%' ", "ChangeNo");//
            if (sMaxNo == "")
            {
                sNo = OBF.ObjectNo + "01";
            }
            else
            {
                string sTemp = sMaxNo.Substring(sMaxNo.Length - 2, 2);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                if (len < 2)
                {
                    sNo = OBF.ObjectNo + "0" + iMax.ToString();
                }
                else
                {
                    sNo = OBF.ObjectNo + iMax.ToString();
                }
            }


            if (sNEFlag =="E") // 修改
            {
                sK = "修改内容";
                // 查找一下之前的记录
                sSQL = " select '对象名称:'+ObjectName+'字段1:'+FOne+'字段2:'+FTwo+'字段3:'+FThree+'字段4:'+FFour+'字段5:'+FFive+'字段6:'+FSix+'字段7:'+FSeven "+
                       " +'字段8:'+FEight+'字段9:'+FNine+'字段10:'+FTen as ChDesc from T_ObjectFieldConfig  where ObjectNo = '" + OBF.ObjectNo + "' ";
                DataTable sdt = DBHelper.GetDataTable(sSQL);
                sChDes = sdt.Rows[0]["ChDesc"].ToString();

                sSQL = " update T_ObjectFieldConfig set ObjectName='" + OBF.ObjectName + "',FOne='" + OBF.F01 + "',FTwo='" + OBF.F02 + "',FThree='" + OBF.F03 + "',FFour='" + OBF.F04 + "', " +
                       " FFive='" + OBF.F05 + "',FSix='" + OBF.F06 + "',FSeven='" + OBF.F07 + "',FEight='" + OBF.F08 + "',FNine='" + OBF.F09 + "',FTen='" + OBF.F10 + "' " +
                       " where ObjectNo='" + OBF.ObjectNo + "' ";
            }
            else // 新增
            {
                sK = "新增记录";

                sSQL = " insert into T_ObjectFieldConfig(ObjectNo,ObjectName,FOne,FTwo,FThree,FFour,FFive,FSix,FSeven,FEight,FNine,FTen,UseFlag,CompanyNo,InMan,Remark)  " +
                  " values('" + OBF.ObjectNo + "','" + OBF.ObjectName + "','" + OBF.F01 + "','" + OBF.F02 + "','" + OBF.F03 + "','" + OBF.F04 + "','" + OBF.F05 + "','" + OBF.F06 + "', " +
                  " '" + OBF.F07 + "','" + OBF.F08 + "','" + OBF.F09 + "','" + OBF.F10 + "','是','" + OBF.CompanyNo + "','" + OBF.InMan + "','" + OBF.Remark + "') ";
            }

            // 插入日志
            sSQLs = " insert into T_ObjectFieldConfigLog(ChangeNo,ChangeMan,ChangeKind,InMan,Remark)" +
                    " values('" + sNo + "','" + OBF.InManName + "','" + sK + "','" + OBF.InMan + "','" + sChDes + "') ";

            

            List<string> links = new List<string>();
            links.Add(sSQL);
            links.Add(sSQLs);

            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }


        /// <summary>
        /// 获取流程信息
        /// </summary>
        /// <param name="strWhere">查询条件</param>
        /// <param name="sFlag">1：流程列表；2：流程节点信息；3：显示流程具有的字段信息</param>
        /// <returns></returns>
        public static DataTable GetFlowInfo(string strWhere, string sNo, string InMan, string sFlag)
        {
            string sSQL = string.Empty;
            string sFlowNo = string.Empty;
            string sCon = string.Empty;
            DataTable sdt = new DataTable();

            if (sFlag == "1")
            {
                sSQL = " select ObjectNo, ObjectName from T_ObjectFieldConfig " + strWhere + " order by ObjectName ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "2")
            {
                sSQL = "select * from T_FlowInfo " + strWhere + " order by FlowName ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if ((sFlag == "3") || (sFlag == "4"))
            {
                SqlParameter[] parameters = {
                    new SqlParameter("@lnBSNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnFNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnFName", SqlDbType.NVarChar,60),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
					new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnFlag", SqlDbType.NVarChar,5),
					new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
                parameters[0].Value = sNo;
                parameters[1].Value = "";
                parameters[2].Value = "";
                parameters[3].Value = "";
                parameters[4].Value = "";
                parameters[5].Value = 10;
                parameters[6].Value = 1;
                parameters[7].Value = InMan;
                parameters[8].Value = sFlag;
                parameters[9].Value = "";

                DataSet DS = DBHelper.RunProcedureForDS("P_GetFlowInfo", parameters);
                sdt = DS.Tables[0];
            }

            return sdt;
        }


        /// <summary>
        /// 插入或更新业务数据，如采购数据:物料编码，物料描述，单据，数量
        /// </summary>
        /// <param name="OBF"> 流程对象字段</param>
        /// <param name="sNEFlag">N:新增；E:更新</param>
        /// <returns></returns>
        public static string SaveBSFlowDataInfo(Model.BusinessData BSData, string sNEFlag)
        {
            string iFlag = string.Empty;
            string sstr = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sNo = string.Empty;
            int iMax;


            sSQL = " select BSName from T_BusinessDataApply where BSNo='" + BSData.BSNo + "' and FlowNo='" + BSData.FlowNo + "' and FOne='" + BSData.F01 + "' and FTwo='" + BSData.F02 + "' ";
            DataTable dt = DBHelper.GetDataTable(sSQL);
            if (dt.Rows.Count > 0)   // 修改
            {
                // 业务数据编号编号是：BS17022400001 01
                string sMaxNo = DBHelper.GetMaxNo("T_BusinessDataApplyLog where ChangeNo like '" + BSData.BSNo + "%' ", "ChangeNo");//
                if (sMaxNo == "")
                {
                    sNo = BSData.BSNo + "01";
                }
                else
                {
                    string sTemp = sMaxNo.Substring(sMaxNo.Length - 2, 2);
                    iMax = int.Parse(sTemp) + 1;
                    int len = iMax.ToString().Length;
                    if (len < 2)
                    {
                        sNo = BSData.BSNo + "0" + iMax.ToString();
                    }
                    else
                    {
                        sNo = BSData.BSNo + iMax.ToString();
                    }
                }

                // 先保存修改前数据
                sSQLs = "Insert into T_BusinessDataApplyLog(ChangeNo,BSNo,BSName,FlowNo,FlowName,FOne,FTwo,FThree,FFour,FFive,FSix,FSeven,FEight,FNine,FTen,InMan,Remark) " +
                         "select '" + sNo + "',BSNo,BSName,FlowNo,FlowName,FOne,FTwo,FThree,FFour,FFive,FSix,FSeven,FEight,FNine,FTen,'" + BSData.InMan + "','修改记录' " +
                         "from T_BusinessDataApply where BSNo='" + BSData.BSNo + "' and FlowNo='" + BSData.FlowNo + "' and FOne='" + BSData.F01 + "' and FTwo='" + BSData.F02 + "' ";

                sSQL = " update T_BusinessDataApply set BSName='" + BSData.BSName + "',FlowNo='" + BSData.FlowNo + "',FlowName='" + BSData.FlowName + "',FOne='" + BSData.F01 + "',FTwo='" + BSData.F02 + "',FThree='" + BSData.F03 + "', " +
                       " FFour='" + BSData.F04 + "',FFive='" + BSData.F05 + "',FSix='" + BSData.F06 + "',FSeven='" + BSData.F07 + "',FEight='" + BSData.F08 + "',FNine='" + BSData.F09 + "',FTen='" + BSData.F10 + "' " +
                       " where BSNo='" + BSData.BSNo + "' and FlowNo='" + BSData.FlowNo + "' and FOne='" + BSData.F01 + "' and FTwo='" + BSData.F02 + "' ";
            }
            else // 新增
            {
                sSQLs = "";

                sSQL = "Insert into T_BusinessDataApply(BSNo,BSName,FlowNo,FlowName,FOne,FTwo,FThree,FFour,FFive,FSix,FSeven,FEight,FNine,FTen,InMan,CompanyNo,Status,Remark) " +
                   " values('" + BSData.BSNo + "','" + BSData.BSName + "','" + BSData.FlowNo + "','" + BSData.FlowName + "','" + BSData.F01 + "','" + BSData.F02 + "','" + BSData.F03 + "','" + BSData.F04 + "', " +
                   " '" + BSData.F05 + "','" + BSData.F06 + "','" + BSData.F07 + "','" + BSData.F08 + "','" + BSData.F09 + "','" + BSData.F10 + "','" + BSData.InMan + "','" + BSData.CompanyNo + "','" + BSData.Status + "','" + BSData.Remark + "')";
            }


            List<string> links = new List<string>();
            links.Add(sSQLs);  // 要先执行这个语句，保存修改前的数据。
            links.Add(sSQL);
            

            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }


        /// <summary>
        /// 显示流程申请的业务数据清单，如采购申请的物料清单//流程审核列表
        /// </summary>
        /// <param name="strWhere">查询条件</param>
        /// <param name="sFlag">1：显示业务数据清单；</param>
        /// <returns></returns>
        public static DataTable ShowBusinessDataList(string strWhere, string sComp, string sFlag)
        {
            string sSQL = string.Empty;

            if (sFlag == "1")
            {
                sSQL = "select * from T_BusinessDataApply " + strWhere + " order by FOne, FTwo ";
            }
            else if (sFlag == "2")  // 流程审核列表
            {
                sSQL = " select distinct a.BSNo,a.BSName,a.InMan,a.Status ,Min(Convert(char(16),a.InDate,120)) as InDate,b.FullName,c.AudMan from T_BusinessDataApply a "+
                       " left join T_SPUser b on a.InMan=b.LoginName "+
                       " left join (select  BSNo,Max(AudMan) as AudMan from T_BusinessDataAndFlow  where NowAud=1 and IsAud=0 group by BSNo) c on a.BSNo=c.BSNo " +
                       " where a.CompanyNo='" + sComp + "' " + strWhere + " group by a.BSNo,a.BSName,a.InMan,a.Status,b.FullName,c.AudMan order by a.BSNo desc ";
            }

            return DBHelper.GetDataTable(sSQL);

        }


       /// <summary>
        /// 根据传入的查询条件，显示申请的流程
       /// </summary>
       /// <param name="BSName"></param>
       /// <param name="BSNo"></param>
       /// <param name="Status"></param>
       /// <param name="CompNo"></param>
       /// <param name="sFlag"></param>
       /// <returns></returns>
        public static DataTable GetBSFlowListByCondition(string sCon, string BSName, string BSNo, string sBDate, string sEDate, string CompNo, string sFlag)
        {
            string sSQL = string.Empty;
            string sMan = sEDate + ")";  // 跟下面的更新条件有关系，截取时还剩下 ) ，所以在名称增加这个括弧。

            if (sFlag == "1")  // 显示流程列表
            {
                sSQL = "select a.BSNo,a.BSName,a.FlowNo,a.FlowName,a.Status,Min(convert(char(10),a.InDate,120)) as InDate,b.AudMan from T_BusinessDataApply a "+
                       "left join (select BSNo,Max(AudMan) as AudMan from T_BusinessDataAndFlow where NowAud=1 and IsAud=0 group by BSNo) b on a.BSNo=b.BSNo " +
                       "where a.CompanyNo='" + CompNo + "' and a.BSName like '%" + BSName + "%' and convert(char(10),a.InDate,120) >='" + sBDate + "' " +
                       "and convert(char(10),a.InDate,120)<='" + sEDate + "' " + sCon + " group by a.BSNo,a.BSName,a.FlowNo,a.FlowName,a.Status,b.AudMan order by a.BSNo desc ";
            }
            else if (sFlag == "2")  // 显示根据选中的流程，显示流程明细
            {
                sSQL = "select a.*,case when a.Status=b.NowStatus then 'Y' else 'N' end as IsAud from T_BusinessDataApply a " +
                       "left join "+
                       " (select distinct BSNo, rtrim(substring(Note,charindex('_',Note)+1,100)) as NowStatus from T_BusinessDataAndFlow where BSNo='" + BSNo + "' and NowAud=1 " +
                       "  and substring(AudMan,charindex('(',AudMan)+1,charindex(')',AudMan))='" + sMan + "') b on a.BSNo=b.BSNo " +
                       "where a.CompanyNo='" + CompNo + "' and a.BSNo = '" + BSNo + "' ";
            }


            DataTable sdt = DBHelper.GetDataTable(sSQL);

            return sdt;

        }



        /// <summary>
        /// 删除申请流程填写的业务数据
        /// </summary>
        /// <param name="OBF"> 流程对象字段</param>
        /// <param name="sNEFlag">N:新增；E:更新</param>
        /// <returns></returns>
        public static string DeleteBSFlowData(string sBSNo, string sFlowNo, string sF01, string sF02, string sLogin, string sNEFlag)
        {
            string iFlag = string.Empty;
            string sstr = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sNo = string.Empty;
            int iMax;


            // 业务数据编号编号是：BS17022400001 01
            string sMaxNo = DBHelper.GetMaxNo("T_BusinessDataApplyLog where ChangeNo like '" + sBSNo + "%' ", "ChangeNo");//
            if (sMaxNo == "")
            {
                sNo = sBSNo + "01";
            }
            else
            {
                string sTemp = sMaxNo.Substring(sMaxNo.Length - 2, 2);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                if (len < 2)
                {
                    sNo = sBSNo + "0" + iMax.ToString();
                }
                else
                {
                    sNo = sBSNo + iMax.ToString();
                }
            }


            // 先保存修改前数据
            sSQLs = "Insert into T_BusinessDataApplyLog(ChangeNo,BSNo,BSName,FlowNo,FlowName,FOne,FTwo,FThree,FFour,FFive,FSix,FSeven,FEight,FNine,FTen,InMan,Remark) " +
                     "select '" + sNo + "',BSNo,BSName,FlowNo,FlowName,FOne,FTwo,FThree,FFour,FFive,FSix,FSeven,FEight,FNine,FTen,'" + sLogin + "','删除记录' " +
                     "from T_BusinessDataApply where BSNo='" + sBSNo + "' and FlowNo='" + sFlowNo + "' and FOne='" + sF01 + "' and FTwo='" + sF02 + "' ";

            sSQL = " delete T_BusinessDataApply where BSNo='" + sBSNo + "' and FlowNo='" + sFlowNo + "' and FOne='" + sF01 + "' and FTwo='" + sF02 + "' ";


            List<string> links = new List<string>();
            links.Add(sSQLs);  // 要先执行这个语句，保存修改前的数据。
            links.Add(sSQL);


            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }


        /// <summary>
        /// 返回单据状态
        /// </summary>
        /// <param name="sCLNo"></param>
        /// <returns></returns>
        public static string GetApplyStatus(string sNo, string sSt, string IsPass,string sLogin, string sFlag)
        {
            string sSQL = string.Empty;
            string sStatus = string.Empty;
            string sMan = sLogin + ")";  // 跟下面的更新条件有关系，截取时还剩下 ) ，所以在名称增加这个括弧。
            int sTime = 0;
            DataTable sdt;

            if (sFlag == "1") // 只是判定状态是否正确
            {
                sSQL = "select distinct Status from T_BusinessDataApply where BSNo='" + sNo + "' ";

                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sStatus = sdt.Rows[0]["Status"].ToString();
                }
                else
                {
                    sStatus = "NO";
                }
            }
            else if (sFlag == "2") // 判定状态的同时，返回下一个状态  -- 通过，不通过，返回状态不一样
            {
                if (IsPass == "通过")   //  wu - 1
                {
                    sSQL = "select distinct Status from T_BusinessDataApply where BSNo='" + sNo + "' ";
                    sdt = DBHelper.GetDataTable(sSQL);
                    if (sdt.Rows.Count > 0)  //  wu - 1
                    {
                        sStatus = sdt.Rows[0]["Status"].ToString();
                        if (sStatus == sSt) // 说明当前状态是正确的，找下一个状态返回去
                        {
                            sSQL = "SELECT top 1 Note FROM T_BusinessDataAndFlow where BSNo='" + sNo + "' and rtrim(substring(Note,charindex('_',Note)+1,100))<> '" + sSt + "' and IsAud=0 ";
                            sdt = DBHelper.GetDataTable(sSQL);
                            if (sdt.Rows.Count > 0)
                            {
                                sStatus = sdt.Rows[0]["Note"].ToString();
                            }
                            else  // 如果没有下一个节点，还需要判断当前节点是否所有人都已审核完成
                            {
                                sSQL = "SELECT top 1 Note FROM T_BusinessDataAndFlow where BSNo='" + sNo + "' and rtrim(substring(Note,charindex('_',Note)+1,100))= '" + sSt + "' and IsAud=0 " +
                                       "and substring(AudMan,charindex('(',AudMan)+1,charindex(')',AudMan))<>'" + sMan + "'"; // 看看除了当前审批人，是否还有其他人没有审批
                                sdt = DBHelper.GetDataTable(sSQL);
                                if (sdt.Rows.Count > 0) 
                                {
                                    sStatus = sdt.Rows[0]["Note"].ToString();
                                }
                                else
                                {
                                    sStatus = "已完成";
                                }
                            }
                        }
                        else
                        {
                            sStatus = "NO";
                        }
                    }  //  wu - 1
                }  //  wu - 1
                else  // 审核不通过 -- 直接回到待提交状态
                {
                    sStatus = "待提交";
                }
            }
            else if (sFlag == "3") // 返回当前的审核次数：先找到之前的审核次数，再加 1 IsPass
            {
                sSQL = "SELECT top 1 AudTime FROM T_BusinessDataAndFlow where BSNo='" + sNo + "' and rtrim(substring(Note,charindex('_',Note)+1,100))= '" + sSt + "' "+
                       "and substring(AudMan,charindex('(',AudMan)+1,charindex(')',AudMan))='" + sMan + "' ";

                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sTime=int.Parse(sdt.Rows[0]["AudTime"].ToString()) + 1;
                    sStatus = sTime.ToString();
                }
                else
                {
                    sStatus = "NO";
                }
            }


            return sStatus;
        }



        /// <summary>
        /// 更新申请流程填写过程审批状态
        /// </summary>
        /// <param name="OBF"> 流程对象字段</param>
        /// <param name="sNEFlag">N:新增；E:更新</param>
        /// <returns></returns>
        public static string UpdateBSFlowData(string sDelStr,string sPicStr,string sSrs,string sBSNo, string sFlowNo, string sLogin,string sFullName,string Remark,string sNowAud, string sFlag)
        {
            string iFlag = string.Empty;
            string sstr = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sSs = string.Empty;
            string sSSs = string.Empty;
            string sSSSs = string.Empty;
            string sStrss = string.Empty;
            string sS = string.Empty;
            string sNo = string.Empty;
            string sSqNo = string.Empty;
            string sSumAud = string.Empty;
            string sTi = string.Empty;
            string sMan = sLogin + ")";  // 跟下面的更新条件有关系，截取时还剩下 ) ，所以在名称增加这个括弧。
            int iMax;
            string sRe = string.Empty;


            // 业务数据编号编号是：BS17022400001 01
            string sMaxNo = DBHelper.GetMaxNo("T_FlowCommitLog where AudNo like '" + sBSNo + "%' ", "AudNo");//
            if (sMaxNo == "")
            {
                sNo = sBSNo + "001";
            }
            else
            {
                string sTemp = sMaxNo.Substring(sMaxNo.Length - 3, 3);
                iMax = int.Parse(sTemp) + 1;
                int len = iMax.ToString().Length;
                if (len == 1)  // 1 2 3 4 5 6 7 8 9 
                {
                    sNo = sBSNo + "00" + iMax.ToString();
                }
                else if (len == 2) //  10  11 12    99
                {
                    sNo = sBSNo + "0" + iMax.ToString();
                }
                else
                {
                    sNo = sBSNo + iMax.ToString();
                }
            }


            List<string> links = new List<string>();
            if (sFlag == "1") // 申请人提交流程
            {
                sTi = GetAudTime(sBSNo, sSrs, sLogin); // 获取审核次数：当前次数+1 
                // 保存日志
                sRe = "第 " + sTi + " 次提交流程";
                sSQLs = "insert into T_FlowCommitLog(AudNo,AudMan,AudDate,IsPass,AudDesc,AudType,BSNo,AudStatus,AudTime,InMan,Remark)" +
                         "values('" + sNo + "','" + sFullName + "',convert(char(16),getdate(),120),'通过','" + Remark + "','提交申请流程','" + sBSNo + "','流程提交','" + sTi + "','" + sLogin + "','" + sRe + "') ";

                sSumAud = DateTime.Now.ToString("yyMMdd") + "：" + Remark;
                sSQL = " update T_BusinessDataApply set Status='" + sNowAud + "',Remark=Remark+'" + sSumAud + "' where BSNo='" + sBSNo + "' and FlowNo='" + sFlowNo + "' ";
                // 更新现在的节点为当前审批节点
                sStrss = " update T_BusinessDataAndFlow set NowAud='1' where BSNo='" + sBSNo + "' and rtrim(substring(Note,charindex('_',Note)+1,100))= '" + sNowAud + "'  ";

                links.Add(sDelStr); 
                links.Add(sPicStr);
                links.Add(sSrs);   // 插入每个节点的审核人
                links.Add(sSQLs);  // 要先执行这个语句，保存修改前的数据。
                links.Add(sSQL);
                links.Add(sStrss);
            }
            else if (sFlag == "2") // 流程审批
            {
                sTi = sFlowNo; // 获取审核次数：当前次数+1 -- 前台传入

                // 变量说明 sDelStr：是否需要发送微信，如果空，则不需要，也不用更新下一个节点的状态, sPicStr：下一个审批节点,  sSrs:当前审批节点
                // 保存日志
                sSQLs = "insert into T_FlowCommitLog(AudNo,AudMan,AudDate,IsPass,AudDesc,AudType,BSNo,AudStatus,AudTime,InMan,Remark)" +
                         "values('" + sNo + "','" + sFullName + "',convert(char(16),getdate(),120),'" + sNowAud + "','" + Remark + "','" + sSrs + "','" + sBSNo + "','流程审批','" + sTi + "','" + sLogin + "','') ";

                // 更新这个审核人的审批记录：已审核，审批说明  
                if (sNowAud == "通过")
                {
                    sSs = "update T_BusinessDataAndFlow set IsAud=1,NowAud=1,AudDesc='" + Remark + "',AudTime='" + sTi + "' where BSNo='" + sBSNo + "' and rtrim(substring(Note,charindex('_',Note)+1,100))= '" + sSrs + "' " +
                          "and substring(AudMan,charindex('(',AudMan)+1,charindex(')',AudMan))='" + sMan + "' ";   // substring(AudMan,charindex('(',AudMan)+1,charindex(')',AudMan)) 截取得到：wudong001)
                }
                else // 如果不通过，则所有节点的审批记录恢复初始状态。
                {
                    sSs = "update T_BusinessDataAndFlow set IsAud=0,NowAud=0 where BSNo='" + sBSNo + "' ";   // substring(AudMan,charindex('(',AudMan)+1,charindex(')',AudMan)) 截取得到：wudong001)
                    sSQL = "update T_BusinessDataAndFlow set AudTime='" + sFlowNo + "' where BSNo='" + sBSNo + "' and rtrim(substring(Note,charindex('_',Note)+1,100))= '" + sSrs + "'  ";  //如果不通过，这个节点默认全部审核完
                }

                // 保存审核记录：根据每个节点汇总审批记录，方便显示明细使用
                sStrss = "SELECT SeqNo FROM T_FlowSumAudInfo where BSNo='" + sBSNo + "' and rtrim(substring(Note,charindex('_',Note)+1,100))= '" + sSrs + "' ";
                DataTable udt = DBHelper.GetDataTable(sStrss);
                if (udt.Rows.Count > 0)  // 记录存在，更新即可
                {
                    sSqNo = udt.Rows[0]["SeqNo"].ToString();
                    sSumAud = "<br>◆" + sFullName + DateTime.Now.ToString("yyyy-MM-dd HH:mm") + sNowAud+ "：" + Remark;
                    sStrss = "update T_FlowSumAudInfo set AudDesc=AudDesc+'" + sSumAud + "' where BSNo='" + sBSNo + "' and rtrim(substring(Note,charindex('_',Note)+1,100))= '" + sSrs + "' ";   // 本审批节点
                }
                else
                {
                    sSumAud = "◆" + sFullName + DateTime.Now.ToString("yyyy-MM-dd HH:mm") + sNowAud + "：" + Remark;
                    sStrss = "insert into T_FlowSumAudInfo(BSNo,FlowNo,SeqNo,Note,AudDesc,InMan) " +
                             "select distinct BSNo,FlowNo,SeqNo,Note,'" + sSumAud + "','" + sLogin + "' from T_BusinessDataAndFlow where BSNo='" + sBSNo + "' and rtrim(substring(Note,charindex('_',Note)+1,100))= '" + sSrs + "' ";
                }

                //更新本节点，本人已审批
                //sSQL = "update T_BusinessDataAndFlow set NowAud=1 where BSNo='" + sBSNo + "' and Note like '%" + sSrs + "' and substring(AudMan,charindex('(',AudMan)+1,charindex(')',AudMan))='" + sMan + "' ";   // 本审批节点

                // 如果当前状态已完成，则到下一个状态/。不通过时，系统会发信息给流程提交人，这是流程节点是“待审批”，所以更新 sSSs 这个语句没有什么影响
                if ((sDelStr != "") || (sPicStr=="已完成"))  // 审批完这个节点，状态更新到下一个节点。 sDelStr：是否需要发送微信，如果空，则不需要，也不用更新下一个审批节点的状态,
                {
                    sSSSs = "update T_BusinessDataApply set Status='" + sPicStr + "' where BSNo='" + sBSNo + "' ";
                    sS = "update T_BusinessDataAndFlow set NowAud=0 where BSNo='" + sBSNo + "' and rtrim(substring(Note,charindex('_',Note)+1,100))= '" + sSrs + "' ";  // 更新本节点为 不是当前审核节点
                    sSSs = "update T_BusinessDataAndFlow set NowAud=1 where BSNo='" + sBSNo + "' and rtrim(substring(Note,charindex('_',Note)+1,100))= '" + sPicStr + "' ";   // 更新 下一个节点 为：当前审核节点
                }


                links.Add(sSQLs);
                links.Add(sSSSs);
                links.Add(sSQL);
                links.Add(sSSs); 
                links.Add(sStrss);
                links.Add(sSs);   //  
                links.Add(sS); 
            }
            else if (sFlag == "3") // 撤回单据
            {
                // 变量说明  sPicStr：下一个审批节点,  sSrs:当前审批节点
                // 保存日志
                sSQLs = "insert into T_FlowCommitLog(AudNo,AudMan,AudDate,IsPass,AudDesc,AudType,BSNo,AudStatus,AudTime,InMan,Remark)" +
                         "values('" + sNo + "','" + sFullName + "',convert(char(16),getdate(),120),'撤回','" + Remark + "','撤回单据','" + sBSNo + "','" + sSrs + "','1','" + sLogin + "','') ";

                // 更新这个审核人的审批记录：已审核，审批说明  
                sSQL = "update T_BusinessDataApply set Status='已撤回' where BSNo='" + sBSNo + "' ";

                links.Add(sSQLs);
                links.Add(sSQL);
            }


            try
            {
                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }


        /// <summary>
        /// 通过申请单号和所在功能模块获取图片路径
        /// </summary>
        /// <param name="sAPLNo"></param>
        /// <param name="sKind"></param>
        /// <returns></returns>
        public static DataTable GetFlowInfoPicPath(string sBSNo,string sFlowNo, string sKind)
        {
            string sSQL = string.Empty;

            sSQL = " select sNo,PicPath from T_PicPath where  sNo='" + sBSNo + "' and Module='" + sKind + "' ";

            return DBHelper.GetDataTable(sSQL);
        }


        /// <summary>
        /// 插入流程节点及每个流程审核人
        /// </summary>
        /// <param name="sSQL"></param>
        /// <param name="sSQLs"></param>
        /// <param name="sLogin"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string InsertFlowInfo(string sNo,string sSQL, string sSQLs,string sLogin,string sFlag)
        {
            string iFlag = string.Empty;
            string sstr = string.Empty;
            string sDelStr = string.Empty;
            string sDels = string.Empty;

            sDelStr = " delete T_FlowInfo where FlowNo='" + sNo + "' ";
            sDels = " delete T_FlowAudMan where FlowNo='" + sNo + "' ";

            List<string> links = new List<string>();
            links.Add(sDelStr);    // 先删除，在插入
            links.Add(sDels);  
            links.Add(sSQL);  
            links.Add(sSQLs);

            try
            {
                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }

        /// <summary>
        /// 获取发送微信的信息，
        /// </summary>
        /// <param name="sBSNo"></param>
        /// <param name="sStatus"></param>
        /// <param name="NStatus">下一个节点的状态</param>
        /// <param name="sTime">第几次审批</param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string GetSendWeiXinMan(string sBSNo,string sStatus,string NStatus,string sTime, string sFlag)
        {
            string sSQL = string.Empty;
            string sWXNo = string.Empty;
            string sNo = string.Empty;
            string sTNo = string.Empty;
            int iN = 0; int iA = 0; int sW = 0;
            DataTable sdt;

            if (sFlag == "1")
            {    // 这个节点需要审批人的个数
                sSQL = "SELECT count(AudMan) as ManCount FROM T_BusinessDataAndFlow WHERE BSNo='" + sBSNo + "' and rtrim(substring(Note,charindex('_',Note)+1,100))= '" + sStatus + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0){
                    iN = int.Parse(sdt.Rows[0]["ManCount"].ToString());
                }

                // 本轮审批，已审批的人数
                sSQL = "select count(AudMan) as ManCount from T_FlowCommitLog where BSNo='" + sBSNo + "' and AudType='" + sStatus + "' and AudTime='" + sTime + "' ";  
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0) {
                    iA = int.Parse(sdt.Rows[0]["ManCount"].ToString());
                    iA = iA + 1; // +1 表示正在审批算一次。
                }

                if ((iA == iN) ||(iN==1))  //说明本节点已审批完成，找下一个节点的审批人。
                {
                    sSQL = "SELECT AudMan FROM T_BusinessDataAndFlow WHERE BSNo='" + sBSNo + "' and rtrim(substring(Note,charindex('_',Note)+1,100))= '" + NStatus + "' ";
                    sdt = DBHelper.GetDataTable(sSQL);
                    if (sdt.Rows.Count > 0)
                    {
                        int iC = sdt.Rows.Count - 1;

                        for (int i = 0; i < sdt.Rows.Count; i++)
                        {
                            sTNo = sdt.Rows[i]["AudMan"].ToString();  // 梧桐(wd02) 许春波(xuchunbo)
                            sW = sTNo.IndexOf("(");
                            sNo = sTNo.Substring(sW + 1, sTNo.Length - (sW + 2));

                            if (i != iC)
                            {
                                sWXNo += sNo + "|";
                            }
                            else // 最后一个企业号不用 |
                            {
                                sWXNo += sNo;
                            }
                        }

                    }
                }
                else  // 本节点，本轮还没有审批完成
                {
                    sWXNo = "";
                }

            }
            else if (sFlag == "2") // 返回提单人
            {
                sSQL = "SELECT top 1 InMan FROM T_BusinessDataApply WHERE BSNo='" + sBSNo + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sWXNo = sdt.Rows[0]["InMan"].ToString();
                }
            }
            else if (sFlag == "3") // 催办时，发给当前审核人
            {
                sSQL = "select AudMan from T_BusinessDataAndFlow where BSNo='" + sBSNo + "' and NowAud=1 and IsAud=0 ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    int iC = sdt.Rows.Count - 1;

                    for (int i = 0; i < sdt.Rows.Count; i++)
                    {
                        sTNo = sdt.Rows[i]["AudMan"].ToString();  // 梧桐(wd02) 许春波(xuchunbo)
                        sW = sTNo.IndexOf("(");
                        sNo = sTNo.Substring(sW + 1, sTNo.Length - (sW + 2));

                        if (i != iC)
                        {
                            sWXNo += sNo + "|";
                        }
                        else // 最后一个企业号不用 |
                        {
                            sWXNo += sNo;
                        }
                    }

                }
            }




            return sWXNo;
        }

        /// <summary>
        /// 获取审核次数
        /// </summary>
        /// <param name="sBSNo"></param>
        /// <param name="Note"></param>
        /// <param name="sLogin"></param>
        /// <returns></returns>
        public static string GetAudTime(string sBSNo,string Note,string sLogin)
        {
            string sSQL = string.Empty;
            string sTime = string.Empty;
            string sMan = sLogin + ")";
            int iMax = 0;


            // 业务数据编号编号是：BS17022400001 01
            string sMaxNo = DBHelper.GetMaxNo("T_FlowCommitLog where BSNo='" + sBSNo + "' and AudType='提交申请流程' ", "AudTime");//
            if (sMaxNo == "")
            {
                sTime = "1";
            }
            else
            {
                iMax = int.Parse(sMaxNo) + 1;
                sTime = iMax.ToString();
            }

            return sTime;
        }






    }
}
