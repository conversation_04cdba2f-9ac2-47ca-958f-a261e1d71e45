/*
 * JQuery zTree excheck 3.5.14
 * http://zTree.me/
 *
 * Copyright (c) 2010 Hunter.z
 *
 * Licensed same as jquery - MIT License
 * http://www.opensource.org/licenses/mit-license.php
 *
 * email: <EMAIL>
 * Date: 2013-06-28
 */
(function(m){var p,q,r,o={event:{CHECK:"ztree_check"},id:{CHECK:"_check"},checkbox:{STYLE:"checkbox",DEFAULT:"chk",DISABLED:"disable",FALSE:"false",TRUE:"true",FULL:"full",PART:"part",FOCUS:"focus"},radio:{STYLE:"radio",TYPE_ALL:"all",TYPE_LEVEL:"level"}},v={check:{enable:!1,autoCheckTrigger:!1,chkStyle:o.checkbox.STYLE,nocheckInherit:!1,chkDisabledInherit:!1,radioType:o.radio.TYPE_LEVEL,chkboxType:{Y:"ps",N:"ps"}},data:{key:{checked:"checked"}},callback:{beforeCheck:null,onCheck:null}};p=function(b,
a){if(a.chkDisabled===!0)return!1;var c=f.getSetting(b.data.treeId),d=c.data.key.checked;if(k.apply(c.callback.beforeCheck,[c.treeId,a],!0)==!1)return!0;a[d]=!a[d];e.checkNodeRelation(c,a);d=n(a,j.id.CHECK,c);e.setChkClass(c,d,a);e.repairParentChkClassWithSelf(c,a);c.treeObj.trigger(j.event.CHECK,[b,c.treeId,a]);return!0};q=function(b,a){if(a.chkDisabled===!0)return!1;var c=f.getSetting(b.data.treeId),d=n(a,j.id.CHECK,c);a.check_Focus=!0;e.setChkClass(c,d,a);return!0};r=function(b,a){if(a.chkDisabled===
!0)return!1;var c=f.getSetting(b.data.treeId),d=n(a,j.id.CHECK,c);a.check_Focus=!1;e.setChkClass(c,d,a);return!0};m.extend(!0,m.fn.zTree.consts,o);m.extend(!0,m.fn.zTree._z,{tools:{},view:{checkNodeRelation:function(b,a){var c,d,h,i=b.data.key.children,l=b.data.key.checked;c=j.radio;if(b.check.chkStyle==c.STYLE){var g=f.getRadioCheckedList(b);if(a[l])if(b.check.radioType==c.TYPE_ALL){for(d=g.length-1;d>=0;d--)c=g[d],c[l]=!1,g.splice(d,1),e.setChkClass(b,n(c,j.id.CHECK,b),c),c.parentTId!=a.parentTId&&
e.repairParentChkClassWithSelf(b,c);g.push(a)}else{g=a.parentTId?a.getParentNode():f.getRoot(b);for(d=0,h=g[i].length;d<h;d++)c=g[i][d],c[l]&&c!=a&&(c[l]=!1,e.setChkClass(b,n(c,j.id.CHECK,b),c))}else if(b.check.radioType==c.TYPE_ALL)for(d=0,h=g.length;d<h;d++)if(a==g[d]){g.splice(d,1);break}}else a[l]&&(!a[i]||a[i].length==0||b.check.chkboxType.Y.indexOf("s")>-1)&&e.setSonNodeCheckBox(b,a,!0),!a[l]&&(!a[i]||a[i].length==0||b.check.chkboxType.N.indexOf("s")>-1)&&e.setSonNodeCheckBox(b,a,!1),a[l]&&
b.check.chkboxType.Y.indexOf("p")>-1&&e.setParentNodeCheckBox(b,a,!0),!a[l]&&b.check.chkboxType.N.indexOf("p")>-1&&e.setParentNodeCheckBox(b,a,!1)},makeChkClass:function(b,a){var c=b.data.key.checked,d=j.checkbox,h=j.radio,i="",i=a.chkDisabled===!0?d.DISABLED:a.halfCheck?d.PART:b.check.chkStyle==h.STYLE?a.check_Child_State<1?d.FULL:d.PART:a[c]?a.check_Child_State===2||a.check_Child_State===-1?d.FULL:d.PART:a.check_Child_State<1?d.FULL:d.PART,c=b.check.chkStyle+"_"+(a[c]?d.TRUE:d.FALSE)+"_"+i,c=a.check_Focus&&
a.chkDisabled!==!0?c+"_"+d.FOCUS:c;return j.className.BUTTON+" "+d.DEFAULT+" "+c},repairAllChk:function(b,a){if(b.check.enable&&b.check.chkStyle===j.checkbox.STYLE)for(var c=b.data.key.checked,d=b.data.key.children,h=f.getRoot(b),i=0,l=h[d].length;i<l;i++){var g=h[d][i];g.nocheck!==!0&&g.chkDisabled!==!0&&(g[c]=a);e.setSonNodeCheckBox(b,g,a)}},repairChkClass:function(b,a){if(a&&(f.makeChkFlag(b,a),a.nocheck!==!0)){var c=n(a,j.id.CHECK,b);e.setChkClass(b,c,a)}},repairParentChkClass:function(b,a){if(a&&
a.parentTId){var c=a.getParentNode();e.repairChkClass(b,c);e.repairParentChkClass(b,c)}},repairParentChkClassWithSelf:function(b,a){if(a){var c=b.data.key.children;a[c]&&a[c].length>0?e.repairParentChkClass(b,a[c][0]):e.repairParentChkClass(b,a)}},repairSonChkDisabled:function(b,a,c,d){if(a){var h=b.data.key.children;if(a.chkDisabled!=c)a.chkDisabled=c;e.repairChkClass(b,a);if(a[h]&&d)for(var i=0,l=a[h].length;i<l;i++)e.repairSonChkDisabled(b,a[h][i],c,d)}},repairParentChkDisabled:function(b,a,c,
d){if(a){if(a.chkDisabled!=c&&d)a.chkDisabled=c;e.repairChkClass(b,a);e.repairParentChkDisabled(b,a.getParentNode(),c,d)}},setChkClass:function(b,a,c){a&&(c.nocheck===!0?a.hide():a.show(),a.removeClass(),a.addClass(e.makeChkClass(b,c)))},setParentNodeCheckBox:function(b,a,c,d){var h=b.data.key.children,i=b.data.key.checked,l=n(a,j.id.CHECK,b);d||(d=a);f.makeChkFlag(b,a);a.nocheck!==!0&&a.chkDisabled!==!0&&(a[i]=c,e.setChkClass(b,l,a),b.check.autoCheckTrigger&&a!=d&&b.treeObj.trigger(j.event.CHECK,
[null,b.treeId,a]));if(a.parentTId){l=!0;if(!c)for(var h=a.getParentNode()[h],g=0,k=h.length;g<k;g++)if(h[g].nocheck!==!0&&h[g].chkDisabled!==!0&&h[g][i]||(h[g].nocheck===!0||h[g].chkDisabled===!0)&&h[g].check_Child_State>0){l=!1;break}l&&e.setParentNodeCheckBox(b,a.getParentNode(),c,d)}},setSonNodeCheckBox:function(b,a,c,d){if(a){var h=b.data.key.children,i=b.data.key.checked,l=n(a,j.id.CHECK,b);d||(d=a);var g=!1;if(a[h])for(var k=0,m=a[h].length;k<m&&a.chkDisabled!==!0;k++){var o=a[h][k];e.setSonNodeCheckBox(b,
o,c,d);o.chkDisabled===!0&&(g=!0)}if(a!=f.getRoot(b)&&a.chkDisabled!==!0){g&&a.nocheck!==!0&&f.makeChkFlag(b,a);if(a.nocheck!==!0&&a.chkDisabled!==!0){if(a[i]=c,!g)a.check_Child_State=a[h]&&a[h].length>0?c?2:0:-1}else a.check_Child_State=-1;e.setChkClass(b,l,a);b.check.autoCheckTrigger&&a!=d&&a.nocheck!==!0&&a.chkDisabled!==!0&&b.treeObj.trigger(j.event.CHECK,[null,b.treeId,a])}}}},event:{},data:{getRadioCheckedList:function(b){for(var a=f.getRoot(b).radioCheckedList,c=0,d=a.length;c<d;c++)f.getNodeCache(b,
a[c].tId)||(a.splice(c,1),c--,d--);return a},getCheckStatus:function(b,a){if(!b.check.enable||a.nocheck||a.chkDisabled)return null;var c=b.data.key.checked;return{checked:a[c],half:a.halfCheck?a.halfCheck:b.check.chkStyle==j.radio.STYLE?a.check_Child_State===2:a[c]?a.check_Child_State>-1&&a.check_Child_State<2:a.check_Child_State>0}},getTreeCheckedNodes:function(b,a,c,d){if(!a)return[];for(var h=b.data.key.children,i=b.data.key.checked,e=c&&b.check.chkStyle==j.radio.STYLE&&b.check.radioType==j.radio.TYPE_ALL,
d=!d?[]:d,g=0,k=a.length;g<k;g++){if(a[g].nocheck!==!0&&a[g].chkDisabled!==!0&&a[g][i]==c&&(d.push(a[g]),e))break;f.getTreeCheckedNodes(b,a[g][h],c,d);if(e&&d.length>0)break}return d},getTreeChangeCheckedNodes:function(b,a,c){if(!a)return[];for(var d=b.data.key.children,h=b.data.key.checked,c=!c?[]:c,i=0,e=a.length;i<e;i++)a[i].nocheck!==!0&&a[i].chkDisabled!==!0&&a[i][h]!=a[i].checkedOld&&c.push(a[i]),f.getTreeChangeCheckedNodes(b,a[i][d],c);return c},makeChkFlag:function(b,a){if(a){var c=b.data.key.children,
d=b.data.key.checked,h=-1;if(a[c])for(var i=0,e=a[c].length;i<e;i++){var g=a[c][i],f=-1;if(b.check.chkStyle==j.radio.STYLE)if(f=g.nocheck===!0||g.chkDisabled===!0?g.check_Child_State:g.halfCheck===!0?2:g[d]?2:g.check_Child_State>0?2:0,f==2){h=2;break}else f==0&&(h=0);else if(b.check.chkStyle==j.checkbox.STYLE)if(f=g.nocheck===!0||g.chkDisabled===!0?g.check_Child_State:g.halfCheck===!0?1:g[d]?g.check_Child_State===-1||g.check_Child_State===2?2:1:g.check_Child_State>0?1:0,f===1){h=1;break}else if(f===
2&&h>-1&&i>0&&f!==h){h=1;break}else if(h===2&&f>-1&&f<2){h=1;break}else f>-1&&(h=f)}a.check_Child_State=h}}}});var m=m.fn.zTree,k=m._z.tools,j=m.consts,e=m._z.view,f=m._z.data,n=k.$;f.exSetting(v);f.addInitBind(function(b){b.treeObj.bind(j.event.CHECK,function(a,c,d,h){k.apply(b.callback.onCheck,[c?c:a,d,h])})});f.addInitUnBind(function(b){b.treeObj.unbind(j.event.CHECK)});f.addInitCache(function(){});f.addInitNode(function(b,a,c,d){if(c){a=b.data.key.checked;typeof c[a]=="string"&&(c[a]=k.eqs(c[a],
"true"));c[a]=!!c[a];c.checkedOld=c[a];if(typeof c.nocheck=="string")c.nocheck=k.eqs(c.nocheck,"true");c.nocheck=!!c.nocheck||b.check.nocheckInherit&&d&&!!d.nocheck;if(typeof c.chkDisabled=="string")c.chkDisabled=k.eqs(c.chkDisabled,"true");c.chkDisabled=!!c.chkDisabled||b.check.chkDisabledInherit&&d&&!!d.chkDisabled;if(typeof c.halfCheck=="string")c.halfCheck=k.eqs(c.halfCheck,"true");c.halfCheck=!!c.halfCheck;c.check_Child_State=-1;c.check_Focus=!1;c.getCheckStatus=function(){return f.getCheckStatus(b,
c)};b.check.chkStyle==j.radio.STYLE&&b.check.radioType==j.radio.TYPE_ALL&&c[a]&&f.getRoot(b).radioCheckedList.push(c)}});f.addInitProxy(function(b){var a=b.target,c=f.getSetting(b.data.treeId),d="",h=null,e="",l=null;if(k.eqs(b.type,"mouseover")){if(c.check.enable&&k.eqs(a.tagName,"span")&&a.getAttribute("treeNode"+j.id.CHECK)!==null)d=k.getNodeMainDom(a).id,e="mouseoverCheck"}else if(k.eqs(b.type,"mouseout")){if(c.check.enable&&k.eqs(a.tagName,"span")&&a.getAttribute("treeNode"+j.id.CHECK)!==null)d=
k.getNodeMainDom(a).id,e="mouseoutCheck"}else if(k.eqs(b.type,"click")&&c.check.enable&&k.eqs(a.tagName,"span")&&a.getAttribute("treeNode"+j.id.CHECK)!==null)d=k.getNodeMainDom(a).id,e="checkNode";if(d.length>0)switch(h=f.getNodeCache(c,d),e){case "checkNode":l=p;break;case "mouseoverCheck":l=q;break;case "mouseoutCheck":l=r}return{stop:e==="checkNode",node:h,nodeEventType:e,nodeEventCallback:l,treeEventType:"",treeEventCallback:null}},!0);f.addInitRoot(function(b){f.getRoot(b).radioCheckedList=[]});
f.addBeforeA(function(b,a,c){b.check.enable&&(f.makeChkFlag(b,a),c.push("<span ID='",a.tId,j.id.CHECK,"' class='",e.makeChkClass(b,a),"' treeNode",j.id.CHECK,a.nocheck===!0?" style='display:none;'":"","></span>"))});f.addZTreeTools(function(b,a){a.checkNode=function(a,c,i,f){var g=b.data.key.checked;if(a.chkDisabled!==!0&&(c!==!0&&c!==!1&&(c=!a[g]),f=!!f,(a[g]!==c||i)&&!(f&&k.apply(this.setting.callback.beforeCheck,[b.treeId,a],!0)==!1)&&k.uCanDo(this.setting)&&b.check.enable&&a.nocheck!==!0))a[g]=
c,c=n(a,j.id.CHECK,b),(i||b.check.chkStyle===j.radio.STYLE)&&e.checkNodeRelation(b,a),e.setChkClass(b,c,a),e.repairParentChkClassWithSelf(b,a),f&&b.treeObj.trigger(j.event.CHECK,[null,b.treeId,a])};a.checkAllNodes=function(a){e.repairAllChk(b,!!a)};a.getCheckedNodes=function(a){var c=b.data.key.children;return f.getTreeCheckedNodes(b,f.getRoot(b)[c],a!==!1)};a.getChangeCheckedNodes=function(){var a=b.data.key.children;return f.getTreeChangeCheckedNodes(b,f.getRoot(b)[a])};a.setChkDisabled=function(a,
c,f,j){c=!!c;f=!!f;e.repairSonChkDisabled(b,a,c,!!j);e.repairParentChkDisabled(b,a.getParentNode(),c,f)};var c=a.updateNode;a.updateNode=function(d,f){c&&c.apply(a,arguments);if(d&&b.check.enable&&n(d,b).get(0)&&k.uCanDo(b)){var i=n(d,j.id.CHECK,b);(f==!0||b.check.chkStyle===j.radio.STYLE)&&e.checkNodeRelation(b,d);e.setChkClass(b,i,d);e.repairParentChkClassWithSelf(b,d)}}});var s=e.createNodes;e.createNodes=function(b,a,c,d){s&&s.apply(e,arguments);c&&e.repairParentChkClassWithSelf(b,d)};var t=e.removeNode;
e.removeNode=function(b,a){var c=a.getParentNode();t&&t.apply(e,arguments);a&&c&&(e.repairChkClass(b,c),e.repairParentChkClass(b,c))};var u=e.appendNodes;e.appendNodes=function(b,a,c,d,h,i){var j="";u&&(j=u.apply(e,arguments));d&&f.makeChkFlag(b,d);return j}})(jQuery);
