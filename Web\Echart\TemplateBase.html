﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="./css/layui.css" rel="stylesheet" />
    <script src="./js/layui.js"></script>
    <script src="./js/jquery-3.3.1.min.js"></script>
    <link href="css/XC.css" rel="stylesheet" />
    <link href="../css/all.css" rel="stylesheet" />

    <script>
        var count = 0;

        var sLogin = "";
        var sCorpID = "";
        $.ajax({
            url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);
                if (parsedJson.Msg == 'loginError') {
                    var Num = parseInt(Math.random() * 1000);
                    window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                }
                else {
                    sLogin = parsedJson.Man
                }
            }
        })


        layui.use(function () {
            var table = layui.table;
            var form = layui.form;
            var upload = layui.upload;
            var element = layui.element;
            var $ = layui.$;

            var Data = GetRequestParams("", "", "100")

            // 创建渲染实例
            table.render({
                elem: '#ID-table-demo-css',
                url: '../Service/EchartAjax.ashx?OP=GetTemplateBase&Data=' + Data, // 此处为静态模拟数据，实际使用时需换成真实接口
                id: "templatelatebaseID",
                page: true,
                count: 50,
                limit: 20,
                limits: [10, 20, 30, 40, 50],
                height: 'full-80',
                lineStyle: 'height: 200px;', // 定义表格的多行样式
                className: 'layui-table-testcss', // 用于给表格主容器追加 css 类名
                cols: [[
                    { field: 'image', title: '图片', templet: '#ID-table-demo-css-img' },
                    { field: 'username', width: 530, title: '基本信息', templet: '#ID-table-demo-css-user' },
                ]],
            });

            // 渲染
            upload.render({
                elem: '#ID-upload-demo-btn',
                url: '../Service/ApplyUpPicFile.aspx?sFlag=22', // 实际使用时改成您自己的上传接口即可。
                before: function (obj) {
                    // 预读本地文件示例，不支持ie8
                    obj.preview(function (index, file, result) {
                        $('#ID-upload-demo-img').attr('src', result); // 图片链接（base64）
                    });

                    element.progress('filter-demo', '0%'); // 进度条复位

                },
                done: function (res) {
                    $("#upload-text").val(res.Path + res.FileName)
                },
                error: function () {

                },
                // 进度条
                progress: function (n, elem, e) {
                    element.progress('filter-demo', n + '%'); // 可配合 layui 进度条元素使用
                    if (n == 100) {
                        layer.msg('上传完毕');
                    }
                }
            });


            $("#TemplateQuery_open").click(function () {
                var txtsBoardNo = $("#txtsBoardNo").val()
                var txtsBoardName = $("#txtsBoardName").val()
                var Data = GetRequestParams(txtsBoardNo, txtsBoardName, "100")
                table.reload('templatelatebaseID', {
                    method: 'post',
                    url: '../Service/EchartAjax.ashx?OP=GetTemplateBase&Data=' + Data,
                    page: {
                        curr: 1
                    }
                });
            })

            table.on('tool(ID-table-demo-css)', function (obj) {
                var data = obj.data, layEvent = obj.event;

                if (layEvent == "edit") {
                    $("#txtAEFlag").val("2")
                    $("#boardNo-item").show()
                    $("#ShowOne").css("display", "block")
                    $("#ShowOne-fade").css("display", "block")
                    $("#boardNo").val(data.BoardNo);
                    $("#boardName").val(data.BoardName);
                    $("#boardDesc").val(data.BoardDesc);
                    $("#boardURL").val(data.BoardPath);//访问地址
                    $("#upload-text").val(data.BoardImage);
                    $('#ID-upload-demo-img').attr('src', data.BoardImage); // 图片链接（base64）
                    $("#head-title1").html("修改看板")
                    $("#Remark").val(data.Remark);
                } else if (layEvent == "delete") {

                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("确定要删除该看板吗？ 看板编号：")
                    //设置删除的对象
                    $("#hint-value").html(data.BoardNo)

                    $("#txtdelBoardNo").val(data.BoardNo)
                } else if (layEvent == "params") {
                    if (data.FieldNo == "") {
                        layer.msg("该模板未配置参数")
                        return
                    }

                    ParameterConditions(data.BoardNo, data.BoardName, data.FieldNo)
                    $("#txtBoardNo").val(data.BoardNo)
                    $("#ShowTow").css("display", "block")
                    $("#ShowTow-fade").css("display", "block")
                }

            })


            $("#TemplateBaseSave_Btn").click(function () {
                var sboardNo = $("#boardNo").val();
                var sboardName = $("#boardName").val();
                var sboardDesc = $("#boardDesc").val();
                var sboardURL = $("#boardURL").val();
                var sRemark = $("#Remark").val();
                var spath = $("#upload-text").val();
                var sFlag = $("#txtAEFlag").val()
                var valuesSet = new Set();
                var array = []

                if (sboardName == "") {
                    layer.msg("请输入看板名称")
                    return
                } else if (sboardURL == "") {
                    layer.msg("请输入访问地址")
                    return
                }


                var Data = '';
                var Params = { No: sboardNo, Name: sboardName, Item: sboardDesc, MNo: "", MName: "", A: sboardURL, B: spath, C: "", D: "", E: "", F: "", Remark: sRemark, Flag: sFlag };
                var Data = JSON.stringify(Params);

                $.ajax({
                    url: "../Service/EchartAjax.ashx?OP=TemplateBaseOP",
                    data: { Data: Data },
                    type: "POST",
                    success: function (res) {
                        var data = jQuery.parseJSON(res)
                        if (data.Msg == "Success") {
                            if (sFlag == "1")
                                layer.msg('添加成功');
                            else
                                layer.msg('修改成功');

                            closeDialog()
                            $("#TemplateQuery_open").click()
                        } else {
                            layer.Msg("系统出错！")
                        }
                    },
                    error: function (err) {
                        layer.Msg("系统出错！")
                    }
                })
            })


            $("#tempParams_Btn").click(function () {
                var valuesSet = new Set();
                var ParamsConditions = []
                var ParamsValue = []
                var sBoardNo = $("#txtBoardNo").val()

                var IsOk = true
                $("#ParameterConditions select").each(function (index, item) {
                    var value = $(item).val();
                    if (value != "" && value != null && !valuesSet.has(value)) {
                        valuesSet.add(value);
                    } else if (valuesSet.has(value)) {
                        layer.msg("有相同的条件")
                        IsOk = false;
                        return
                    }
                });

                if (!IsOk) { return }


                var ParamsConditions = []
                var ParamsValue = []

                for (var i = 1; i <= count; i++) {

                    if ($("#ParamsConditions" + i).val() != "" && $("#ParamsConditions" + i).val() != null) {

                        ParamsConditions.push($("#ParamsConditions" + i).val())

                        var str = "'" + $("#ParamsValue" + i).val()

                        if ($("#ParamsValueS" + i).val() != null && $("#ParamsValueS" + i).val() != "") {

                            if ($("#ParamsConditions" + i).val() == "State") {

                                str += $("#ParamsValueS" + i).val()

                                str += "'";
                            }
                            else {
                                var map = $("#ParamsValueS" + i).val().map(item => item)

                                str += map.length > 0 ? map.join(',') : "" + ""

                                str += "'";
                            }

                        } else {
                            str += "'";
                        }

                        ParamsValue.push(str)

                    }

                }

                var sParamsConditions = ParamsConditions.join(",");
                var sParamsValue = ParamsValue.join(",")

                var Data = '';
                var Params = { No: "", Name: "", Item: "", MNo: "", MName: "", A: sParamsConditions, B: sParamsValue, C: "", D: "", E: "", F: "", Remark: sBoardNo, Flag: "4" };
                var Data = JSON.stringify(Params);

                $.ajax({
                    url: "../Service/EchartAjax.ashx?OP=TemplateBaseOP",
                    data: { Data: Data },
                    type: "POST",
                    success: function (res) {
                        var data = jQuery.parseJSON(res)
                        if (data.Msg == "Success") {

                            layer.msg('设置成功');
                            closeParamsDialog()

                        } else {
                            layer.Msg("系统出错！")
                        }
                    },
                    error: function (err) {
                        layer.Msg("系统出错！")
                    }
                })
            })



            $("#addTemplate").click(function () {
                $("#boardNo-item").hide()
                $("#txtAEFlag").val("1")
                $("#ShowOne").css("display", "block")
                $("#ShowOne-fade").css("display", "block")
                $("#head-title1").html("新增看板")
            })

            $("#TemplateBaseDel_Btn").click(function () {

                var sboardNo = $("#txtdelBoardNo").val();

                var Data = '';
                var Params = { No: sboardNo, Name: "", Item: "", MNo: "", MName: "", A: "", B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: "3" };
                var Data = JSON.stringify(Params);

                $.ajax({
                    url: "../Service/EchartAjax.ashx?OP=TemplateBaseOP",
                    data: { Data: Data },
                    type: "POST",
                    success: function (res) {
                        $("#TemplateQuery_open").click()
                        layer.msg('删除成功');
                        closeDelDialog()
                    },
                    error: function (err) {

                    }
                })

            })

            $("#ViewTemplate").click(function () {
                window.open("/Echart/JXC_Main.htm")
            })

            $(".XC-Form-block-Item .xc-select").change(function () {
                var ele = $(this)
                GetValue(ele, "")
            })
        });


        function GetValue(ele, value) {
            var input = ele.next()
            var select = ele.next().next()
            var message = ele.parent().next()

             //方法
                            //if (ele.val() == "ProcedureNo" || ele.val() == "TechNo" || ele.val() == "DepartmentNo") {
                            //    var select = ele.next().next()
                            //    var values = data.data[i].DefaultValue.split(",");
                            //    select.val(values)
                            //} else {
                            //    ele.next().next().val(data.data[i].DefaultValue)
                            //}

            input.val("")
            select.empty()
            message.html("")

            if (ele.val() == "ProcedureNo" || ele.val() == "TechNo" || ele.val() == "DepartmentNo" || ele.val() == "Hour") {
                select.attr("multiple", "multiple")
                input.css("display", "none")
                message.html("按住Ctrl或Shift可以多选")

                select.css("display", "block")
                select.css("height", "190px")

                switch (ele.val()) {
                    case "ProcedureNo":
                        GetProcedureList(select, value)
                        break;
                    case "TechNo":
                        GetTechList(select, value)
                        break
                    case "DepartmentNo":
                        GetDeptInfoList(select, value)
                        break;
                    case "Hour":
                        GetHourList(select, value)
                        break;
                    default:
                }
            } else if (ele.val() == "State") {
                select.removeAttr("multiple")
                input.css("display", "none")
                select.css("display", "block")
                select.css("height", "30px")
                GetStateInfoList(select, value)
            } else {
                select.removeAttr("multiple")
                input.css("display", "block")
                select.css("display", "none")
                input.val(value)
            }
        }


        // 下拉选择系统类别--部门信息
        function GetDeptInfoList(ele,value) {
            var CNo = "";
            var CKind = encodeURI("部门信息");

            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/BaseModuleAjax.ashx?OP=GetDeptInfoList&CFlag=41&CMNO=" + CNo + "&CKind=" + CKind,
                success: function (data) {
                    var parsedJson = eval(data).Msg;

                    $(ele).empty();
                    $(ele).append(parsedJson);
                    $(ele).val(value.split(","))
                }
            });
        }

        function GetProcedureList(ele, value) {  // 加载工序
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/TechAjax.ashx?OP=GetTechInfo&CFlag=110&&page=1&limit=1000",
                success: function (res) {
                    var sKong = "";
                    for (var i = 0; i < res.data.length; i++) {
                        sKong += "<option value='" + res.data[i].ProcedureNo + "'>(" + res.data[i].ProcedureNo + ")" + res.data[i].ProcedureName + "</option>";
                    }

                    $(ele).empty();
                    $(ele).append(sKong);
                    $(ele).val(value.split(","))
                }
            });
        }

        function GetStateInfoList(ele, value) {

            const statusList = ['','生产中', '已完成', '维修中', '未开始'];
            const statusOptions = statusList.map(item => "<option value='" + item + "'>" + item +"</option>")
            $(ele).empty();
            $(ele).append(statusOptions.join(''));
            $(ele).val(value)
        }

        function GetHourList(ele, value) {
            var sKong = "";
            for (var i = 0; i < 24; i++) {
                sKong += "<option value='" + i + "'>" + i + " : 00</option>";
            }
            $(ele).empty();
            $(ele).append(sKong);
            $(ele).val(value.split(","))
        }

        function GetTechList(ele,value) {

            var Data = GetRequestParams("", "", "2")

            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/EchartAjax.ashx?OP=GetTemplateBase&page=1&limit=100&Data=" + Data,
                success: function (data) {
                    if (data.data.length > 0) {
                        var sKong = "";
                        for (var i = 0; i < data.data.length; i++) {
                            sKong += "<option value='" + data.data[i].TechNo + "'>" + data.data[i].TechNo + "</option>";
                        }
                        $(ele).empty();
                        $(ele).append(sKong);

                        $(ele).val(value.split(","))
                    }
                }
            });
        }

        function GetRequestParams(txtsBoardNo, txtsBoardName, flag) {

            var Data = '';
            var Params = { No: txtsBoardNo, Name: txtsBoardName, Item: "", MNo: "", MName: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "", E: "", F: "", G: "", H: "", I: "", J: "", K: "", L: "", Remark: "", Flag: flag };
            var Data = JSON.stringify(Params);

            return Data
        }

        function ParameterConditions(txtsBoardNo, txtsBoardName, FieldNo) {

            var Data = GetRequestParams(txtsBoardNo, FieldNo, "1")
            $.ajax({
                url: '../Service/EchartAjax.ashx?OP=GetTemplateBase&page=1&limit=100&Data=' + Data,
                data: {},
                type: "GET",
                success: function (res) {
                    var data = jQuery.parseJSON(res)

                    if (data.data.length > 0) {

                        for (var i = 1; i <= 6; i++) {
                            if (i <= data.data.length) {
                                $("#XC-Form-block-Item" + i).show()
                            } else {
                                $("#XC-Form-block-Item" + i).hide()
                            }
                        }

                        var sKong = "<option value=''></option>";
                        data.data.forEach(item => {
                            sKong += "<option value='" + item.FieldNameEN + "'>" + item.FieldNameCN + "</option>"
                        })

                        count = data.data.length;

                        var ele = null
                        for (var i = 0; i < data.data.length; i++) {
                            ele = $("#ParamsConditions" + (i + 1))
                            ele.empty()
                            ele.append(sKong)

                            ele.val(data.data[i].FieldNameEN)

                            GetValue(ele, data.data[i].DefaultValue)
                        }
                    }
                },
                error: function (err) {
                    layer.Msg("系统出错！")
                }
            })
        }




        function closeDialog() {
            var element = layui.element;
            $("#ShowOne").css("display", "none")
            $("#ShowOne-fade").css("display", "none")
            $("#boardNo").val("");
            $("#boardName").val("");
            $("#boardDesc").val("");
            $("#boardURL").val("");//访问地址
            $("#Remark").val("");
            $("#upload-text").val("");
            $('#ID-upload-demo-img').attr('src', "");
            element.progress('filter-demo', '0%'); // 进度条复位
        }


        function closeDelDialog() {
            $("#ShowDel").css("display", "none")
            $("#ShowDel-fade").css("display", "none")

        }

        function closeParamsDialog() {
            $("#ShowTow").css("display", "none")
            $("#ShowTow-fade").css("display", "none")

            for (var i = 1; i <= 6; i++) {
                $("#ParamsConditions" + i).empty("")
                $("#ParamsValue" + i).val("")
                $("#ParamsValueS" + i).empty("")
                $("#XC-Msg-Item" + i).html("")

                $("#ParamsValue" + i).css("display", "block")
                $("#ParamsValueS" + i).css("display", "none")
            }
        }
    </script>

    <style>
        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }

        .layui-table img {
            max-width: 100%;
        }

        .layui-table-view-1 .layui-table-body .layui-table tr .layui-table-cell {
            height: auto;
            max-height: 100% !important;
            white-space: normal;
            text-overflow: clip;
        }

        .datalist li {
            margin: 20px 0px;
            font-size: 14px
        }

        .datalist strong {
            width: 60px;
            text-align: right;
            margin-right: 20px;
            display: inline-block;
        }

        .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: white;
            z-index: 1001;
            opacity: 0.95;
        }

        .xc-select {
            width: 130px;
            flex: initial;
            margin-right: 10px;
        }

        .XC-Msg-Item {
            font-size: 12px;
            text-align: right;
            margin-bottom: 20px
        }


        .XC-Form-block-Item {
            display: flex;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="div_find" style="width:99%">
        <label class="find_labela">编号</label> <input type="text" id="txtsBoardNo" class="find_input" />
        <label class="find_labela">名称</label> <input type="text" id="txtsBoardName" class="find_input" />
        <input type="button" value="搜索" class="XC-Btn-Green XC-Size-xs XC-Btn-md" id="TemplateQuery_open" />
        <input type="button" value="添加" class="XC-Btn-Green XC-Size-xs XC-Btn-md" id="addTemplate" />
        <input type="button" value="查看" class="XC-Btn-Green XC-Size-xs XC-Btn-md" id="ViewTemplate" />
    </div>

    <div style="padding: 5px;">
        <table class="layui-hide" id="ID-table-demo-css" lay-filter="ID-table-demo-css"></table>
    </div>

    <script type="text/html" id="ID-table-demo-css-user">
        <ul class="datalist">
            <li><strong>编号</strong> {{d.BoardNo }} </li>
            <li><strong>名称</strong> {{d.BoardName }} </li>
            <li><strong>描述</strong> {{d.BoardDesc }} </li>
            <li><strong>访问地址</strong> {{d.BoardPath }} </li>
            <li><strong>图片地址</strong> {{d.BoardImage }} </li>
            <li><strong>操作人</strong> {{d.InMan }} </li>
            <li><strong>添加时间</strong> {{ d.InDate2 }} </li>
            <li><strong>备注</strong> {{ d.Remark }} </li>
        </ul>
        <div style="text-align:right">
            <button class="XC-Btn-Green XC-Size-xs XC-Btn-md" style="margin-left:0px" lay-event="edit"><i class="layui-icon layui-icon-edit" style="font-size:13px"></i>修改</button>
            <button class="XC-Btn-Red XC-Size-xs XC-Btn-md" style="margin-left:0px" lay-event="delete"><i class="layui-icon layui-icon-delete" style="font-size:13px"></i>删除</button>
            <button class="XC-Btn-Green XC-Size-xs XC-Btn-md" style="margin-left: 0px; width: 70px" lay-event="params"><i class="layui-icon layui-icon-edit" style="font-size:13px;"></i>设置参数</button>
        </div>
    </script>

    <script type="text/html" id="ID-table-demo-css-img">
        <img src="{{d.BoardImage}}" alt="">
    </script>



    <div class="XC-modal XC-modal-xl" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title1" id="head-title1">标题</span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <form class="XC-Form-block">
                    <div class="XC-Form-block-Item" id="boardNo-item">
                        <span class="XC-Span-Input-block">编号</span>
                        <input type="text" class="XC-Input-block" id="boardNo" name="boardNo" disabled="disabled" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">名称</span>
                        <input type="text" class="XC-Input-block" id="boardName" name="boardName" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Textarea-block">描述</span>
                        <textarea class="XC-Textarea-block" id="boardDesc" name="boardDesc" style="height: 100px; width: 99%;"> </textarea>
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Textarea-block">访问地址</span>
                        <textarea class="XC-Textarea-block" id="boardURL" name="boardURL" style="height: 70px; width: 99%;"> </textarea>
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Textarea-block">备注</span>
                        <textarea class="XC-Textarea-block" id="Remark" name="Remark" style="height: 70px; width: 99%;"> </textarea>
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Textarea-block">模板图片</span>
                        <div style="width: 100%;height:650px" id="ID-upload-demo-btn">
                            <div class="layui-upload-list" style="margin: 0px 0px 11px 0px; border: solid 1px #E5E5E5; height: 100% ">
                                <img class="layui-upload-img" id="ID-upload-demo-img" style="width: 100%; height: 100%;">
                                <input id="upload-text" style="display:none" type="text" />
                            </div>
                            <div class="layui-progress layui-progress-big" lay-showPercent="yes" lay-filter="filter-demo">
                                <div class="layui-progress-bar" lay-percent=""></div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtEditKind" name="txtEditKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="TemplateBaseSave_Btn">提交</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick='closeDialog()'>关闭</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>


    <div class="XC-modal XC-modal-xl" id="ShowTow">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1">设置参数</span>
            <span class="head-close" onclick="closeParamsDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <div id="ParameterConditions">
                    <div class="XC-Form-block-Item" id="XC-Form-block-Item1">
                        <select class="xc-select XC-Select-block" id="ParamsConditions1"></select>
                        <input type="text" class="XC-Input-block" id="ParamsValue1" name="name" value="" style="display:block" />
                        <select class="XC-Select-block" id="ParamsValueS1" name="name" value="" style="display:none;"> </select>
                    </div>
                    <div id="XC-Msg-Item1" class="XC-Msg-Item"></div>
                    <div class="XC-Form-block-Item" id="XC-Form-block-Item2">
                        <select class="xc-select XC-Select-block" id="ParamsConditions2"></select>
                        <input type="text" class="XC-Input-block" id="ParamsValue2" name="name" value="" style="display: block" />
                        <select class="XC-Select-block" id="ParamsValueS2" name="name" value="" style="display: none;  "> </select>
                    </div>
                    <div id="XC-Msg-Item2" class="XC-Msg-Item"></div>
                    <div class="XC-Form-block-Item" id="XC-Form-block-Item3">
                        <select class="xc-select XC-Select-block" id="ParamsConditions3"></select>
                        <input type="text" class="XC-Input-block" id="ParamsValue3" name="name" value="" style="display: block" />
                        <select class="XC-Select-block" id="ParamsValueS3" name="name" value="" style="display: none;  "> </select>
                    </div>
                    <div id="XC-Msg-Item3" class="XC-Msg-Item"></div>
                    <div class="XC-Form-block-Item" id="XC-Form-block-Item4">
                        <select class="xc-select XC-Select-block" id="ParamsConditions4"></select>
                        <input type="text" class="XC-Input-block" id="ParamsValue4" name="name" value="" style="display: block" />
                        <select class="XC-Select-block" id="ParamsValueS4" name="name" value="" style="display: none;  "> </select>
                    </div>
                    <div id="XC-Msg-Item4" class="XC-Msg-Item"></div>
                    <div class="XC-Form-block-Item" id="XC-Form-block-Item5">
                        <select class="xc-select XC-Select-block" id="ParamsConditions5"></select>
                        <input type="text" class="XC-Input-block" id="ParamsValue5" name="name" value="" style="display: block" />
                        <select class="XC-Select-block" id="ParamsValueS5" name="name" value="" style="display: none; "> </select>
                    </div>
                    <div id="XC-Msg-Item5" class="XC-Msg-Item"></div>
                    <div class="XC-Form-block-Item" id="XC-Form-block-Item6">
                        <select class="xc-select XC-Select-block" id="ParamsConditions6"></select>
                        <input type="text" class="XC-Input-block" id="ParamsValue6" name="name" value="" style="display: block" />
                        <select class="XC-Select-block" id="ParamsValueS6" name="name" value="" style="display: none; "> </select>
                    </div>
                    <div id="XC-Msg-Item6" class="XC-Msg-Item"></div>
                </div>
                <input type="text" name="" id="txtBoardNo" value="" style="display:none" />
            </div>
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="tempParams_Btn">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="ProcSaveClose" onclick="closeParamsDialog()">取消</button>
            </div>
        </div>
    </div>
    <div id="ShowTow-fade" class="black_overlay">
    </div>


    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDelDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtdelBoardNo" name="txtdelBoardNo" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="TemplateBaseDel_Btn">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDelDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>

    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>
</html>