﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="Web.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <appSettings>
    <add key="sqlconnectionstr" value="server=AEF\K;database=MES_PRD_DB;uid=sa;pwd=******" />
    <!--<add key="sqlconnectionstr" value="server=.\sqlexpress;database=MES_PRD_DB;uid=sa;pwd=******" />-->
    <!--<add key="SqlConnectionStr" value="server=MR50004822NB9;database=MKTQ0827_DB;uid=sa;pwd=*************" />-->
    <!--<add key="SqlConnectionStr" value="server=MR50004822NB9;database=Cash_DB;uid=sa;pwd=*************" />-->
    <!--<add key="SqlConnectionStr" value="server=MR50004822NB9;database=JFUDI_Test_DB;uid=sa;pwd=*************" />-->
    <add key="HttpUrl" value="http://localhost:24979/" />
    <!--app下载地址-->
    <add key="AppUrl" value="http://localhost:24979/app/麦视.apk" />
    <add key="COMPANY" value="KJ" />
    <add key="CorpToken" value="ycAiSOXnJF" />
    <add key="EncodingAESKey" value="mlYSndsylCAgto4Gdsxjbbi2hqaG42NJluE34yNTW2W" />
    <add key="CorpId" value="wx9824d60e1f9ee4ab" />
    <add key="DQAudMan" value="wudong" />
  </appSettings>
  <!--
    有关 web.config 更改的说明，请参见 http://go.microsoft.com/fwlink/?LinkId=235367。

    可在 <httpRuntime> 标记上设置以下特性。
      <system.Web>
        <httpRuntime targetFramework="4.8" />
      </system.Web>
  -->
  <system.web>
    <!-- 
            设置 compilation debug="true" 可将调试符号插入
            已编译的页面中。但由于这会 
            影响性能，因此只在开发过程中将此值 
            设置为 true。
        -->
    <compilation debug="true" targetFramework="4.8" />
    <!--
            通过 <authentication> 节可以配置 ASP.NET 用来 
            识别进入用户的
            安全身份验证模式。 
        -->
    <authentication mode="Windows" />
    <!--
            如果在执行请求的过程中出现未处理的错误，
            则通过 <customErrors> 节可以配置相应的处理步骤。具体说来，
            开发人员通过该节可以配置
            要显示的 html 错误页
            以代替错误堆栈跟踪。

        <customErrors mode="RemoteOnly" defaultRedirect="GenericErrorPage.htm">
            <error statusCode="403" redirect="NoAccess.htm" />
            <error statusCode="404" redirect="FileNotFound.htm" />
        </customErrors>
        -->
    <httpHandlers>
      <add verb="POST,GET" path="ajaxpro/*.ashx" type="AjaxPro.AjaxHandlerFactory,AjaxPro.2" />
    </httpHandlers>
    <sessionState timeout="360000" stateNetworkTimeout="360000" />
    <httpRuntime maxRequestLength="5120" executionTimeout="600" />
    <pages controlRenderingCompatibilityVersion="3.5" clientIDMode="AutoID" />
  </system.web>
  <!-- 
        在 Internet 信息服务 7.0 下运行 ASP.NET AJAX 需要 system.webServer
        节。对早期版本的 IIS 来说则不需要此节。
    -->
  <system.webServer>
      <validation validateIntegratedModeConfiguration="false" />

	  <staticContent>
		  <mimeMap fileExtension=".grf" mimeType="grf/gridreport" />
	  </staticContent>
  </system.webServer>
  <applicationSettings>
    <Web.Properties.Settings>
      <setting name="Web_ProductionInfoMgr_ProductionInfoMgr" serializeAs="String">
        <value>http://www.20gli.top:8080/WebService/ProductionInfoMgr.asmx</value>
      </setting>
    </Web.Properties.Settings>
  </applicationSettings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>