body, h1, h2, h3, h4, h5, h6, hr, p, blockquote, dl, dt, dd, ul, ol, li, pre, form, fieldset, legend, button, input, textarea, th, td, img{border:medium none;margin: 0;padding: 0;font-size: 100%;}
body,button, input, select, textarea{font-size: 14px;line-height: 1.5;font-family: Arial, 'Microsoft Yahei';}
h1, h2, h3, h4, h5, h6{font-weight: normal;}
em{font-style:normal;}
ul, ol{list-style: none;}
input[type="text"],
input[type="password"],
button{background: none;border: none;outline: none;-webkit-appearance: none;}
textarea{resize: none;}
a{text-decoration: none;color:#333;}
a:hover{text-decoration: none;
		color:#008ccb;
		-moz-transition: all 0.3s ease-in;
		-webkit-transition: all 0.3s ease-in;
		-o-transition: all 0.3s ease-in;
		transition: all 0.3s ease-in;
}
img{border:0px;  outline-width:0px;  vertical-align:top;}
.clearfix { *zoom: 1; }
.clearfix:before, .clearfix:after {  display: table; line-height: 0; content: ""; }
.clearfix:after { clear: both; }
.fl { float:left;}
.fr { float:right;}
.hide { display:none !important;}
.show { display:block !important;}


/* Loader container */
.shcl {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0 auto;
}


/* Progress status */
.shcl > span {
    font-family: Verdana, Tahoma, Arial, sans-serif;
    font-size: 14px;
    color: black;
}

/* Loader dots */
.shcl > div {
    position: absolute;
    visibility: hidden;
    width: 10px;
    height: 10px;
    background: transparent;
    box-shadow: 0 0 10px black;

    -webkit-border-radius: 5px;
    -webkit-animation-name: shcl_bounce;
    -webkit-animation-duration: 1s;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-direction: linear;

    -moz-border-radius: 5px;
    -moz-animation-name: shcl_bounce;
    -moz-animation-duration: 1s;
    -moz-animation-iteration-count: infinite;
    -moz-animation-direction: linear;

    -o-border-radius: 5px;
    -o-animation-name: shcl_bounce;
    -o-animation-duration: 1s;
    -o-animation-iteration-count: infinite;
    -o-animation-direction: linear;

    -ms-border-radius: 5px;
    -ms-animation-name: shcl_bounce;
    -ms-animation-duration: 1s;
    -ms-animation-iteration-count: infinite;
    -ms-animation-direction: linear;

    border-radius: 5px;
    animation-name: shcl_bounce;
    animation-duration: 1s;
    animation-iteration-count: infinite;
    animation-direction: linear;
}

/* Animation keyframes */

@-webkit-keyframes shcl_bounce {
    0% {-webkit-transform: scale(1);}
    80% {-webkit-transform: scale(.3);}
    100% {-webkit-transform: scale(1);}
}

@-moz-keyframes shcl_bounce {
    0% {-moz-transform: scale(1);}
    80% {-moz-transform: scale(.3);}
    100% {-moz-transform: scale(1);}
}

@-o-keyframes shcl_bounce {
    0% {-o-transform: scale(1);}
    80% {-o-transform: scale(.3);}
    100% {-o-transform: scale(1);}
}

@-ms-keyframes shcl_bounce {
    0% {-ms-transform: scale(1);}
    80% {-ms-transform: scale(.3);}
    100% {-ms-transform: scale(1);}
}

@keyframes shcl_bounce {
    0% {transform: scale(1);}
    80% {transform: scale(.3);}
    100% {transform: scale(1);}
}

#loader{
	width: 80px;
	height: 80px;
	position: absolute;
	top: 50%;
	left: 50%;
	margin: -40px 0 0 -40px;
	z-index: 1000;
}
