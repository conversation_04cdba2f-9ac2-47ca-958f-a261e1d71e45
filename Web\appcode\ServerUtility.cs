﻿using System;
using System.Collections;
using System.Web;
using System.Data;
using grsvr6Lib;
using System.IO;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using Common;
using System.Data.SqlClient;

namespace Web.appcode
{
    public class ReportGenerateInfo
    {
        public string ContentType;          //HTTP响应ContentType 
        public string ExtFileName;          //默认扩展文件名
        public bool IsGRD;                  //是否生成为 Grid++Report 报表文档格式
        public GRExportType ExportType;     //导出的数据格式类型
        public GRExportImageType ImageType; //导出的图像格式类型

        ///根据报表导出格式类型，生成对应的响应信息，将结果信息保存本类的成员变量中
        ///参数 ExportTypeText: 指定报表导出的导出格式类型
        ///参数 ImageTypeText: 指定生成的图像格式，仅当为导出图像时有效
        public void Build(string ExportTypeText, string ImageTypeText)
        {
            ExtFileName = ExportTypeText;
            ContentType = "application/";
            IsGRD = (ExportTypeText == "grd" || ExportTypeText == "grp");

            if (IsGRD)
            {
                //ContentType += ExportTypeText; //application/grd
                ContentType += "octet-stream"; //application/octet-stream
            }
            else
            {
                switch (ExportTypeText)
                {
                    case "xls":
                        ExportType = GRExportType.gretXLS;
                        ContentType += "x-xls"; //application/vnd.ms-excel application/x-xls
                        break;
                    case "csv":
                        ExportType = GRExportType.gretCSV;
                        ContentType += "vnd.ms-excel"; //application/vnd.ms-excel application/x-xls
                        break;
                    case "txt":
                        ExportType = GRExportType.gretTXT;
                        ContentType = "text/plain"; //text/plain
                        break;
                    case "rtf":
                        ExportType = GRExportType.gretRTF;
                        ContentType += "rtf"; //application/rtf
                        break;
                    case "img":
                        ExportType = GRExportType.gretIMG;
                        //ContentType 要在后面根据图像格式来确定
                        break;
                    default:
                        ExtFileName = "pdf"; //"type"参数如没有设置，保证 ExtFileName 被设置为"pdf"
                        ExportType = GRExportType.gretPDF;
                        ContentType += "pdf";
                        break;
                }

                //导出图像处理
                if (ExportType == GRExportType.gretIMG)
                {
                    ExtFileName = ImageTypeText;
                    switch (ImageTypeText)
                    {
                        case "bmp":
                            ImageType = GRExportImageType.greitBMP;
                            ContentType += "x-bmp";
                            break;
                        case "jpg":
                            ImageType = GRExportImageType.greitJPEG;
                            ContentType += "x-jpg";
                            break;
                        case "tif":
                            ImageType = GRExportImageType.greitTIFF;
                            ContentType = "image/tiff";
                            break;
                        default:
                            ExtFileName = "png";
                            ImageType = GRExportImageType.greitPNG;
                            ContentType += "x-png";
                            break;
                    }
                }
            }
        }
    }

    public class ServerUtility
    {
        private static readonly Random _random = new Random();

        /// <summary>
        /// 将报表生成的二进制数据响应给 HTTP 请求客户端
        /// </summary>
        /// <param name="context"> HTTP 请求对象</param>
        /// <param name="ExportResult">报表生成的二进制数据</param>
        /// <param name="FileName">指定下载(或保存)文件时的默认文件名称</param>
        /// <param name="ContentType">响应的ContentType</param>
        /// <param name="OpenMode">指定生成的数据打开模式，可选[inline|attachment]，"inline"表示在网页中内联显示，"attachment"表示以附件文件形式下载。如果不指定，由浏览器自动确定打开方式。</param>
        public static void ResponseBinary(HttpContext context, IGRBinaryObject ExportResult, string FileName, string Item, string No, string B, string A, string D, string IP, string E, string MNo, string Name,string CreateType, string Comp, string sMan, string ContentType, string OpenMode, string Flag)
        {
            string msg = string.Empty;
            string xuPath = string.Empty;
            try
            {
                // 指定要保存的文件路径  换成目标服务器对应的保存位置
                if (Flag == "PN")// 产品流程使用
                {
                    xuPath = "/DHRFile/" + Item + "/";
                }
                else
                {  // 作业执行使用
                    xuPath = "/DHRFile/" + Item + "/" + No + "/";
                }

                string filePath = xuPath + FileName;

                string path = context.Server.MapPath(filePath);//服务器路径

                //验证当前服务器是否有这个路径
                if (!Directory.Exists(context.Server.MapPath(xuPath)))
                {
                    Directory.CreateDirectory(context.Server.MapPath(xuPath));
                }

                string Disposition = "";

                if (OpenMode != null && OpenMode.Length > 0)
                    Disposition = OpenMode + "; ";

                Disposition += ServerUtility.EncodeAttachmentFileName(context.Request.UserAgent, FileName);

                context.Response.ContentType = ContentType;
                context.Response.AppendHeader("Content-Length", ExportResult.DataSize.ToString());
                context.Response.AppendHeader("Content-Disposition", Disposition);

                context.Response.ClearContent();

                object Data = ExportResult.SaveToVariant();

                // 假设 Data 包含要保存的二进制数据
                byte[] binaryData = (byte[])Data;

                // 将二进制数据写入文件
                File.WriteAllBytes(path, binaryData);

                if (Flag == "ZY")
                {
                    string FileNo = DateTime.Now.ToString("yyMMddhhmmssfff") + _random.Next(100, 1000);

                    string sql = "Insert into T_DHRFile(FileNo,FileName,OrderNo,SerialNo,MaterNo,ProcedureNo,ProcedureName,FlowOrder,RPath,APath,CreateWay,Status,CompanyNo,InMan,Remark) " +
                        "values(@FileNo,@FileName,@OrderNo,@SerialNo,@MaterNo,@ProcedureNo,@ProcedureName,@FlowOrder,@RPath,@APath,@CreateType,@Status,@CompanyNo,@InMan,@Remark)";
                    var a = CreateType;
                    DBHelper.ExecuteCommand(sql, new SqlParameter[]
                    {
                        new SqlParameter("@FileNo",FileNo),
                        new SqlParameter("@FileName",FileName),
                        new SqlParameter("@OrderNo",Item),
                        new SqlParameter("@SerialNo",No),
                        new SqlParameter("@MaterNo",MNo),
                        new SqlParameter("@ProcedureNo",B),
                        new SqlParameter("@ProcedureName",A),
                        new SqlParameter("@FlowOrder",D),
                        new SqlParameter("@RPath",xuPath),
                        new SqlParameter("@APath",""),
                        new SqlParameter("@CreateType",CreateType),
                        new SqlParameter("@Status",""),
                        new SqlParameter("@CompanyNo",Comp),
                        new SqlParameter("@InMan",sMan),
                        new SqlParameter("@Remark",E)
                    });
                }
                ResponseException(context, filePath, "Success", FileName, No, A, E, "");
            }
            catch (Exception ex)
            {
                LogHelper.LogError("添加", "添加文件记录失败! 工单：" + Item + "，序列号：" + No + "，顺序号：" + E + " 原因：" + ex.Message.ToString(), IP, Comp, sMan, Name);
                ResponseException(context, "", "Error", "", No, A, E, "生成报表发生错误");
            }
        }

        /// <summary>
        /// 将报表生成的二进制数据响应给 HTTP 请求客户端
        /// </summary>
        /// <param name="context"> HTTP 请求对象</param>
        /// <param name="ExportResult">报表生成的二进制数据</param>
        /// <param name="FileName">指定下载(或保存)文件时的默认文件名称</param>
        /// <param name="ContentType">响应的ContentType</param>
        /// <param name="OpenMode">指定生成的数据打开模式，可选[inline|attachment]，"inline"表示在网页中内联显示，"attachment"表示以附件文件形式下载。如果不指定，由浏览器自动确定打开方式。</param>
        public static void ResponseBinary(HttpContext context, IGRBinaryObject ExportResult, string ContentType, string OpenMode, string No, string Name, string sComp, string sLogin, string xuPath, string fileKind)
        {
            string time = DateTime.Now.ToString("yyyyMMddhhmmss");
            string FileName = time + ".pdf";

            //验证当前服务器是否有这个路径
            if (!Directory.Exists(context.Server.MapPath(xuPath)))
            {
                Directory.CreateDirectory(context.Server.MapPath(xuPath));
            }

            xuPath = xuPath + FileName;

            string path = context.Server.MapPath(xuPath);//服务器路径

            try
            {
                string Disposition = "";

                if (OpenMode != null && OpenMode.Length > 0)
                    Disposition = OpenMode + "; ";

                Disposition += ServerUtility.EncodeAttachmentFileName(context.Request.UserAgent, FileName);

                context.Response.ContentType = ContentType;
                context.Response.AppendHeader("Content-Length", ExportResult.DataSize.ToString());
                context.Response.AppendHeader("Content-Disposition", Disposition);

                context.Response.ClearContent();

                object Data = ExportResult.SaveToVariant();

                // 假设 Data 包含要保存的二进制数据
                byte[] binaryData = (byte[])Data;

                // 将二进制数据写入文件
                File.WriteAllBytes(path, binaryData);

                string sql = "INSERT INTO T_RecordFile(FileNo, FileKind, FilePath, No, Name, CompanyNo, InMan) " +
                   "VALUES (@FileNo, @FileKind, @FilePath, @No, @Name, @CompanyNo, @InMan)";


                DBHelper.ExecuteCommand(sql, new SqlParameter[]
                   {
                        new SqlParameter("@FileNo",FileName),
                        new SqlParameter("@FileKind",fileKind),
                        new SqlParameter("@FilePath",xuPath),
                        new SqlParameter("@No",No),
                        new SqlParameter("@Name",Name),
                        new SqlParameter("@CompanyNo",sComp),
                        new SqlParameter("@InMan",sLogin),
                   });

                context.Response.Write(JsonConvert.SerializeObject(new { Path = xuPath, Msg = "Success", Filename = FileName, ExceptionMessage = "" }));
            }
            catch (Exception ex)
            {
                context.Response.Write(JsonConvert.SerializeObject(new { Path = "", Msg = "Error", Filename = "", ExceptionMessage = ex.ToString() }));
            }
            context.Response.Flush();
        }

        /// <summary>
        /// 将异常信息文字响应给请求的客户端 
        /// </summary>
        /// <param name="context"></param>
        /// <param name="MessageText"></param>
        public static void ResponseException(HttpContext context, string MessageText)
        {
            context.Response.ContentType = "text/plain";
            context.Response.Write(MessageText);
        }

        public static void ResponseException(HttpContext context, string path, string Message, string fileName, string serialNo, string procedureName, string dHRType, string exceptionMessage)
        {
            context.Response.ContentType = "text/plain";
            var errorResponse = new
            {
                Path = path,
                Msg = Message,
                Filename = fileName,
                SerialNo = serialNo,
                ProcedureName = procedureName,
                DHRType = dHRType,
                ExceptionMessage = exceptionMessage
            };
            context.Response.Write(JsonConvert.SerializeObject(errorResponse));
        }

        /// <summary>
        /// 为了文件名中的汉字与特殊字符能正确，必须进行分浏览器处理
        /// </summary>
        /// <param name="BrowserAgent"></param>
        /// <param name="RawFileName"></param>
        /// <returns></returns>
        public static string EncodeAttachmentFileName(string BrowserAgent, string RawFileName)
        {
            System.Text.UTF8Encoding UTF8Encoding = new System.Text.UTF8Encoding();
            string EncodedFileName = HttpUtility.UrlEncode(RawFileName, UTF8Encoding);

            // 如果没有BrowserAgent，则默认使用IE的方式进行编码，因为毕竟IE还是占多数的  
            string ret = "filename=\"" + EncodedFileName + "\"";
            if (BrowserAgent != null && BrowserAgent.Length != 0)
            {
                BrowserAgent = BrowserAgent.ToLower();
                // msie 与 edge 采用默认的方式   
                if ((BrowserAgent.IndexOf("msie") == -1) && (BrowserAgent.IndexOf("edge") == -1))
                {
                    // Chrome浏览器，只能采用MimeUtility编码或ISO编码的中文输出  
                    if (BrowserAgent.IndexOf("applewebkit") != -1)
                    {
                        //EncodedFileName = MimeUtility.encodeText(RawFileName, "UTF8", "B");
                        System.Text.UTF8Encoding UTF8EncodingBOM = new System.Text.UTF8Encoding(true);
                        EncodedFileName = UTF8EncodingBOM.GetString(UTF8EncodingBOM.GetBytes(RawFileName));
                        ret = "filename=\"" + EncodedFileName + "\"";
                    }
                    // Safari浏览器，只能采用ISO编码的中文输出  
                    else if (BrowserAgent.IndexOf("safari") != -1)
                    {
                        //28591  iso-8859-1                1252   *
                        //ret = "filename=\"" + new String(filename.getBytes("UTF-8"), "ISO8859-1") + "\"";
                        byte[] UTF8Bytes = UTF8Encoding.GetBytes(RawFileName);
                        string ISO8859Text = System.Text.Encoding.GetEncoding(28591).GetString(UTF8Bytes);
                        ret = "filename=\"" + ISO8859Text + "\"";
                    }
                    // Opera浏览器只能采用filename*  
                    // FireFox浏览器，可以使用MimeUtility或filename*或ISO编码的中文输出  
                    else if ((BrowserAgent.IndexOf("opera") != -1) || (BrowserAgent.IndexOf("mozilla") != -1))
                    {
                        ret = "filename*=UTF-8''" + EncodedFileName;
                    }
                }
            }

            return ret;
        }

        /// <summary>
        /// 根据报表模板中的查询SQL获取报表数据
        /// </summary>
        //public static string BuildFromSelfSQL(GridppReportServer Report)
        //{
        //    string DataText = "";

        //    //从 XML 或 JSON 数据包中载入报表数据
        //    string MasterQuerySQL = Report.QuerySQL;
        //    string DetailQuerySQL = Report.DetailGrid != null ? Report.DetailGrid.Recordset.QuerySQL : null;
        //    bool MasterAssigned = (MasterQuerySQL != null && MasterQuerySQL.Length > 0);
        //    bool DetailAssigned = (DetailQuerySQL != null && DetailQuerySQL.Length > 0);
        //    if (MasterAssigned || DetailAssigned)
        //    {
        //        if (MasterAssigned && DetailAssigned)
        //        {
        //            string MasterTableName = Report.XmlTableName;
        //            if (MasterTableName == null || MasterTableName.Length == 0)
        //                MasterTableName = "Master";

        //            string DetailTableName = Report.DetailGrid.Recordset.XmlTableName;
        //            if (DetailTableName == null || DetailTableName.Length == 0)
        //                DetailTableName = "Detail";

        //            ArrayList QueryList = new ArrayList();
        //            QueryList.Add(new ReportQueryItem(DetailQuerySQL, DetailTableName));
        //            QueryList.Add(new ReportQueryItem(MasterQuerySQL, MasterTableName));
        //            DataText = DataTextProvider.Build(QueryList);
        //        }
        //        else
        //        {
        //            DataText = DataTextProvider.Build(MasterAssigned ? MasterQuerySQL : DetailQuerySQL);
        //        }
        //    }

        //    return DataText;
        //}

        private struct MatchFieldPairType
        {
            public IGRField grField;
            public int MatchColumnIndex;
        }

        // 将 DataReader 的数据转储到 Grid++Report 的记录集中
        public static void FillRecordToReport(IGridppReport Report, IDataReader dr)
        {
            MatchFieldPairType[] MatchFieldPairs = new MatchFieldPairType[Math.Min(Report.DetailGrid.Recordset.Fields.Count, dr.FieldCount)];

            //根据字段名称与列名称进行匹配，建立DataReader字段与Grid++Report记录集的字段之间的对应关系
            int MatchFieldCount = 0;
            for (int i = 0; i < dr.FieldCount; ++i)
            {
                foreach (IGRField fld in Report.DetailGrid.Recordset.Fields)
                {
                    if (String.Compare(fld.RunningDBField, dr.GetName(i), true) == 0)
                    {
                        MatchFieldPairs[MatchFieldCount].grField = fld;
                        MatchFieldPairs[MatchFieldCount].MatchColumnIndex = i;
                        ++MatchFieldCount;
                        break;
                    }
                }
            }


            // 将 DataReader 中的每一条记录转储到 Grid++Report 的记录集中去
            while (dr.Read())
            {
                Report.DetailGrid.Recordset.Append();

                for (int i = 0; i < MatchFieldCount; ++i)
                {
                    if (!dr.IsDBNull(MatchFieldPairs[i].MatchColumnIndex))
                        MatchFieldPairs[i].grField.Value = dr.GetValue(MatchFieldPairs[i].MatchColumnIndex);
                }

                Report.DetailGrid.Recordset.Post();
            }
        }

        // 将 DataTable 的数据转储到 Grid++Report 的记录集中
        public static void FillRecordToReport(IGridppReport Report, DataTable dt)
        {
            MatchFieldPairType[] MatchFieldPairs = new MatchFieldPairType[Math.Min(Report.DetailGrid.Recordset.Fields.Count, dt.Columns.Count)];

            //根据字段名称与列名称进行匹配，建立DataReader字段与Grid++Report记录集的字段之间的对应关系
            int MatchFieldCount = 0;
            for (int i = 0; i < dt.Columns.Count; ++i)
            {
                foreach (IGRField fld in Report.DetailGrid.Recordset.Fields)
                {
                    if (String.Compare(fld.Name, dt.Columns[i].ColumnName, true) == 0)
                    {
                        MatchFieldPairs[MatchFieldCount].grField = fld;
                        MatchFieldPairs[MatchFieldCount].MatchColumnIndex = i;
                        ++MatchFieldCount;
                        break;
                    }
                }
            }


            // 将 DataTable 中的每一条记录转储到 Grid++Report 的记录集中去
            foreach (DataRow dr in dt.Rows)
            {
                Report.DetailGrid.Recordset.Append();

                for (int i = 0; i < MatchFieldCount; ++i)
                {
                    if (!dr.IsNull(MatchFieldPairs[i].MatchColumnIndex))
                        MatchFieldPairs[i].grField.Value = dr[MatchFieldPairs[i].MatchColumnIndex];
                }

                Report.DetailGrid.Recordset.Post();
            }
        }

        public static uint RGBToOleColor(byte r, byte g, byte b)
        {
            return ((uint)b) * 256 * 256 + ((uint)g) * 256 + r;
        }

        public static uint ColorToOleColor(System.Drawing.Color val)
        {
            return RGBToOleColor(val.R, val.G, val.B);
        }

        //判断当前运行进程是不是64位程序
        public static bool Is64bitProcess()
        {
            //IsWow64Process
            //也可以直接用Environment.Is64BitProcess，不过需要DotNet4.0或以上版本。
            return (IntPtr.Size == 8);
        }

        //生成数据源连接串，根据当前运行环境与配置参数进行实际调整
        public static string BuildOLEDBConnectionString()
        {
            //Grid++Report的64位程序通过OLE DB连接Access与32位程序使用完全不同的数据驱动程序，
            //此数据驱动程序(AccessDatabaseEngine_X64.exe)没有跟随Windows系统自动安装，需要从
            //微软网站下载并安装, 下载地址：https://www.microsoft.com/zh-CN/download/details.aspx?id=13255
            //如果直接不能安装，要先将Ofiice卸载后再进行安装，驱动程序安装后，在重新安装Office。
            return (Is64bitProcess() ? "Provider = Microsoft.ACE.OLEDB.12.0;" : "Provider=Microsoft.Jet.OLEDB.4.0;") +
                "User ID=Admin;Data Source=" + @"C:\Grid++Report 6\Samples\Data\NorthWind.mdb";
        }
    }
}
