﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>试剂/质控品库</title>
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <script src="../js/ajaxfileupload.js" type="text/javascript"></script>
    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all">
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/PrdAssist.js" type="text/javascript"></script>

    <link href="../css/XC.css" rel="stylesheet" />

    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        table_QCWarehouse
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        //$('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=28-1' });
                    }
                }
            })
        });



    </script>


    <script type="text/javascript">


        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#table_QCWarehouse',
                id: 'table_QCWarehouseID',
                url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=23',//初始化界面查询对应的存过程标记信息，获取部门信息
                height: 'full-80',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'LotNo', title: '批号', width: 120, sort: true },
                    { field: 'MaterNo', title: '物料编码', width: 200, sort: true },
                    { field: 'MaterName', title: '名称', width: 200 },
                    { field: 'Spec', title: '规格', width: 120 },
                    { field: 'Qty', title: '数量', width: 200 },
                    { field: 'StorageConditions', title: '存储条件', width: 200 },
                    { field: 'ExpirationDate', title: '有效期', width: 200 },
                    { field: 'Remark', title: '备注', width: 300 },
                    { field: 'InMan', title: '录入人', width: 90 },
                    { field: 'InDate', title: '录入时间', width: 200 },
                    { field: 'op', title: '操作', width: 120, toolbar: '#barDemo_QCWarehouse', fixed: 'right' }
                ]],
                page: true,
                //  done: function(res, curr, count) {
                //     var that = this.elem.next();
                //     res.data.forEach(function(item, index) {
                //         if (item.Flag == "0") { // 说明评估日期已到期
                //             $(".layui-table-body tbody tr[data-index='" + index + "']").css({ 'backgroundColor': "Pink" });
                //             $(".layui-table-body tbody tr[data-index='" + index + "']").find('td[data-field="LastContactTime"]').css({ 'color': "red" });
                //
                //         }
                //     });
                // }


            });




            //监听是否选中操作
            table.on('checkbox(table_QCWarehouse)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);

            });


            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(table_QCWarehouse)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

            });

            //监听单元格编辑
            table.on('edit(table_QCWarehouse)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });


            //监听行工具事件
            table.on('tool(table_QCWarehouse)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值
                if (layEvent === 'del') {

                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("您确定要删除该试剂/质控品信息吗？ 批号：")

                    //设置删除的对象
                    $("#hint-value").html(data.LotNo)

                    $("#txtDelLotNo").val(data.LotNo)
                    $("#txtDelMaterNo").val(data.MaterNo)

                } else if (layEvent === 'edit') {
                    $('#head-title1').html("修改试剂/质控品信息");
                    $('#txtAEFlag').val("43");//修改标记

                    $("#txtLotNo").val(data.LotNo);
                    $("#txtMaterNo").val(data.MaterNo);
                    $("#txtMaterName").val(data.MaterName);
                    $("#txtSpec").val(data.Spec);

                    $("#txtQty").val(data.Qty);
                    $("#txtStorageConditions").val(data.StorageConditions);
                    $("#txtExpirationDate").val(data.ExpirationDate);
                    $("#txtRemark").val(data.Remark);

                    $("#txtLotNo").attr({ "disabled": "disabled" });
                    $("#txtExpirationDate").attr({ "disabled": "disabled" });

                    $("#div_warning").html("");
                    $("#div_warning").hide();

                    $('#ShowOne').css("display", "block")
                    $('#ShowOne-fade').css("display", "block")

                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }

            });
            //  查询 --
            $('#btn_QCWarehouse_Open').click(function () {

                var sWNo = $("#txtSLotNo").val();  //
                var sName = encodeURI($("#txtSMaterNo").val());  //


                var Data = '';
                var Params = { No: sWNo, Name: sName, Item: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "", E: "" };
                var Data = JSON.stringify(Params);

                table.reload('table_QCWarehouseID', {
                    method: 'post',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=23&Data=' + Data,
                    where: {
                        'No': sWNo,
                        'name': sName
                    }, page: {
                        curr: 1
                    }
                });
            });

            $("#btn_QCWarehouseDel").click(function () {
                //向服务端发送禁用指令
                var sLotNo = $("#txtDelLotNo").val()
                var sMaterNo = $("#txtDelMaterNo").val()

                var sFlag = "44";//删除标记

                var Data = '';
                var Params = { No: sLotNo, Name: sMaterNo, Item: "", A: "", B: "", C: "", D: "", E: "", F: "", I: "", J: "", K: "", L: "", Kind: "", Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);
                $.ajax({
                    type: "POST",
                    url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('删除成功！');

                            closeDelDialog()

                            $('#btn_QCWarehouse_Open').click();  // 重新查询


                        }
                        //else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                        //    layer.msg('工序已用于工艺流程，不能删除！');

                        //} else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST1') {
                        //    layer.msg('工序已用于BOM设计，不能删除！');

                        //} else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST2') {
                        //    layer.msg('工序已用于产品对应标贴，不能删除！');

                        //}
                        else {
                            layer.msg('删除失败，请重试！');
                            $('#btn_QCWarehouse_Open').click();  // 重新查询
                        }
                    },
                    error: function (data) {
                        layer.msg('删除失败2，请重试！');
                        $('#btn_QCWarehouse_Open').click();  // 重新查询
                    }
                });
            })

        });

        function openDialog(n) {
            if (n == 1)// 新增
            {
                $('#head-title1').html("新增试剂/质控品信息");
                $('#txtAEFlag').val("42");//新增的类型标记

                $("#txtQty").val("");
                $("#txtMaterNo").val("");
                $("#txtMaterName").val("");
                $("#txtSpec").val("");

                $("#txtLotNo").val("");
                $("#txtStorageConditions").val("");
                $("#txtExpirationDate").val("");
                $("#txtRemark").val("");

                $("#div_warning").html("");
                $("#div_warning").hide();

                $("#txtLotNo").removeAttr("disabled");
                $("#txtExpirationDate").removeAttr("disabled");

                $('#ShowOne').css("display", "block")
                $('#ShowOne-fade').css("display", "block")
            }
            else if (n == 2) {
                layui.use('table', function () {
                    var table = layui.table,
                        form = layui.form;
                    table.render({
                        elem: '#table_SelectMater',
                        id: 'MaterID',
                        url: '../Service/BaseModuleAjax.ashx?OP=GetMaterInfo&sFlag=51',
                        height: 'full-220',
                        cellMinWidth: 80,
                        count: 50, //数据总数 服务端获得
                        limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                        limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                        cols: [[
                            { type: 'numbers' },
                            //{ type: 'checkbox' },
                            { field: 'MaterNo', title: '物料编码', width: 150, unresize: true, sort: true },
                            { field: 'MaterName', title: '物料名称', width: 350, sort: true },
                            { field: 'MaterLB', title: '物料类别', minWidth: 80 },
                            //{ field: 'BigType', title: '大类', minWidth: 80 },
                            //{ field: 'FillMaterKind', title: '分类', width: 200 },
                            { field: 'MaterType', title: '类型(Type)', width: 200 },
                            { field: 'MaterSpec', title: '规格型号', width: 120 },
                            { field: 'MaterUnit', title: '单位', width: 60 },
                            { field: 'MaterKind', title: '物料类别', width: 90 },
                            { field: 'InMan', title: '录入人', width: 110, edit: 'text' },
                            { field: 'InDate', title: '录入时间', width: 150 },
                            { field: 'op', title: '操作', width: 120, toolbar: '#barDemo_SelectMater', fixed: 'right' }
                        ]],
                        page: true,
                        even: true
                    });


                    table.on('tool(table_SelectMater)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                        var data = obj.data, //获得当前行数据
                            layEvent = obj.event; //获得 lay-event 对应的值
                        if (layEvent === 'slc') {

                            $("#txtMaterNo").val(data.MaterNo);
                            $("#txtMaterName").val(data.MaterName);
                            $("#txtSpec").val(data.MaterSpec);

                            closeDialog(2);
                        }

                    });


                    //  查询 -- 物料信息
                    $('#btn_MaterInfo_open').click(function () {
                        var sNo = $("#txtSNo").val();  //编码
                        var sName = encodeURI($("#txtSName").val());  // 名称
                        var sSpec = encodeURI($("#txtSSpec").val());  // 规格
                        var sBT = encodeURI($("#txtSBigType").val());  // 大类

                        var Data = '';
                        var Params = { No: sNo, Name: sName, Spec: sSpec, BT: sBT };
                        var Data = JSON.stringify(Params);

                        table.reload('MaterID', {
                            method: 'post',
                            url: '../Service/BaseModuleAjax.ashx?OP=GetMaterInfo&sFlag=51&Data=' + Data,
                            where: {
                                'No': sNo,
                                'name': sName,
                                'TJ': sBT,
                            }, page: {
                                curr: 1
                            }
                        });
                    });

                    $('#ShowTow').css("display", "block")
                    $('#ShowTow-fade').css("display", "block")
                });
            }
        }

        function closeDialog(n) {
            if (n == 1) {
                $('#ShowOne').css("display", "none")
                $('#ShowOne-fade').css("display", "none")
            } else if (n == 2) {
                $('#ShowTow').css("display", "none")
                $('#ShowTow-fade').css("display", "none")
            }
        }


        function closeDelDialog() {
            $('#ShowDel').css("display", "none")
            $('#ShowDel-fade').css("display", "none")
        }

    </script>


    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

        })



    </script>

    <style type="text/css">

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        /* .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: #bbbcc7;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=60);
        }*/

        .LabelDelBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #da542e;
        }

        .LabelAddBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #579fd8;
        }

        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }
    </style>


</head>
<body>
    <div class="div_find">

        <p>
            <label class="find_labela">批号</label> <input type="text" id="txtSLotNo" class="find_input" />
            <label class="find_labela">物料编码</label><input type="text" id="txtSMaterNo" class="find_input" />
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="btn_QCWarehouse_Open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="javascript:openDialog(1)">添加</button>
        </p>
    </div>

    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="table_QCWarehouse" lay-filter="table_QCWarehouse"></table>

        <script type="text/html" id="barDemo_QCWarehouse">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="edit">修改</button>
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>
        </script>

    </div>

    <!--弹窗编辑-->
    <div class="XC-modal XC-modal-xl" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1">质控品维护</span>
            <span class="head-close" onclick="closeDialog(1)">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <form class="XC-Form-block">
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">批号<span class="XC-Font-Red">*</span></span>
                        <input type="text" class="XC-Input-block" id="txtLotNo" name="txtLotNo" value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">物料编码<span class="XC-Font-Red">*</span></span>
                        <input type="text" class="XC-Input-block" id="txtMaterNo" name="txtMaterNo" readonly=readonly value="" />
                        <button class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="btn_Mater_open" type="button" onclick="openDialog(2)" style="margin-left:5px">选择</button>
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">物料名称</span>
                        <input type="text" class="XC-Input-block" id="txtMaterName" name="txtMaterName" readonly=readonly value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">规格</span>
                        <input type="text" class="XC-Input-block" id="txtSpec" name="txtSpec" readonly=readonly value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">数量</span>
                        <input type="text" class="XC-Input-block" id="txtQty" name="txtQty" value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">存储条件</span>
                        <input type="text" class="XC-Input-block" id="txtStorageConditions" name="txtStorageConditions" value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">有效期</span>
                        <input type="date" class="XC-Input-block" id="txtExpirationDate" name="txtExpirationDate" value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Textarea-block">备注</span>
                        <textarea class="XC-Textarea-block" id="txtRemark" name="txtRemark"></textarea>
                    </div>
                </form>
            </div>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="btn_QCWarehouseSave">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="QCWarehouseSaveClose" onclick='closeDialog(1)'>关闭</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>

    <!--弹窗选择物料编码-->
    <div class="XC-modal XC-modal-xl" id="ShowTow">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title2">物料信息选择</span>
            <span class="head-close" onclick="closeDialog(2)">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <label class="find_labela">料号</label> <input type="text" id="txtSNo" class="find_input" />
                <label class="find_labela">名称</label><input type="text" id="txtSName" class="find_input" />
                <label class="find_labela">规格</label> <input type="text" id="txtSSpec" class="find_input" />
                <label class="find_labela">大类</label><input type="text" id="txtSBigType" class="find_input" />
                <input type="button" value="搜索" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="btn_MaterInfo_open">
                <div style=" font-weight: bold;padding-bottom:10px;margin-top:10px">
                    基本信息
                </div>
                <div class="wangid_conbox">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="table_SelectMater" lay-filter="table_SelectMater"></table>

                    <script type="text/html" id="barDemo_SelectMater">
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="slc">选择</button>
                    </script>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowTow-fade" class="black_overlay">
    </div>


    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDelDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelLotNo" name="txtDelLotNo" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelMaterNo" name="txtDelMaterNo" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="btn_QCWarehouseDel">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDelDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>


    <!--消息提示-->
    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>
</html>