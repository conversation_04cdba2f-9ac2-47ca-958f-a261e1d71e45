﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using Common;
using System.Text;
using System.Runtime.ConstrainedExecution;
using System.Runtime.Remoting.Metadata.W3cXsd2001;
using System.Security.Policy;
using System.Xml.Linq;

namespace DAL
{
    public class BaseModuleDal
    {
        /// <summary>
        /// 显示用户注册信息列表
        /// </summary>
        /// <param name="sConsSQL"></param>
        /// <param name="LoginMan"></param>
        /// <param name="Comp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable ShowUserRelationList(string sConsSQL, string LoginMan, string Comp, string sFlag)
        {
            string sSQL = string.Empty;
            string sStr = string.Empty;

            sSQL = " select a.LoginName,a.FullName,a.Supplier,a.Phone,a.<PERSON>,a.CompanyNo,b.PName,<PERSON><PERSON>,b.<PERSON> as <PERSON><PERSON><PERSON>,b.<PERSON> as <PERSON><PERSON><PERSON><PERSON>,CompanyName,b.Addr,a.Remark,a.BanShiChu,a.OperatorTime  " +
                   " from T_SPUser a left join T_Partner b on a.Kind=b.Kind and a.CompanyNo=b.CompanyNo " + sConsSQL + " order by b.PName  ";


            return DBHelper.GetDataTable(sSQL);
        }



        /// <summary>
        /// 获取用户基本信息
        /// </summary>
        /// <param name="sWhere"></param>
        /// <returns></returns>
        public static DataTable GetUserInfo(string sNo, string sName, string sDept, string sSex, string sComp, string sFlag)
        {
            string sSQL = string.Empty;

            if (sFlag == "10-99")
            {  // 员工对应作业单元使用，作业执行更换作业单元使用
                sSQL = "select * from T_SPUser where IsApproval=1 and LoginName like '" + sNo + "%' " +
                       " and FullName like '%" + sName + "%' and DeptName like '%" + sDept + "%' and UserSex like '%" + sSex + "%' and CompanyNo='" + sComp + "'order by LoginName ";
            }
            else
            {
                sSQL = "select *,CONVERT(char(16),OperatorTime,120) as InDate from T_SPUser where IsApproval=1 and LoginName like  '%" + sNo + "%' " +
                       " and FullName like '%" + sName + "%' and DeptName like '%" + sDept + "%' and UserSex like '%" + sSex + "%' and CompanyNo='" + sComp + "'order by LoginName ";
            }


            DataTable sdt = DBHelper.GetDataTable(sSQL);

            return sdt;
        }


        public static DataTable GetSysKind(string Kind, string sComp, string sFlag)
        {
            string sSQL = string.Empty;


            if (sFlag == "39")
            {
                sSQL = "select ItemName from T_SysKindList where TypeName = '" + Kind + "' and CompanyNo = '" + sComp + "' order by ItemOrder ";
            }
            else if (sFlag == "40")
            {
                sSQL = "select distinct TypeName,TypeNo,'<label onclick=''DelSysKind(1)'' class=''LabelDelBtn''>删除</label>' as DEL " +
                       "from T_SysKindList where TypeName like '%" + Kind + "%' and CompanyNo = '" + sComp + "' order by TypeName ";
            }
            else if (sFlag == "41")
            {
                sSQL = "select DeptNo as PNNo,'('+DeptNo+')'+ DeptName as PNName from T_DeptInfo where CompanyNo = '" + sComp + "' order by DeptNo ";
            }
            else
            {
                sSQL = "select TypeName,ItemName,ItemNo,ItemOrder,Remark,'<label onclick=''DelSysKind(2)''class=''LabelDelBtn''>删除</label>' as DEL " +
                       "from T_SysKindList where CompanyNo = '" + sComp + "'  and  TypeName='" + Kind + "' and ItemName <> '无' order by ItemOrder ";
            }

            DataTable sdt = DBHelper.GetDataTable(sSQL);

            return sdt;
        }


        /// <summary>
        /// 新增类别及项次，如菜单，系统类别。分级这种数据
        /// </summary>
        /// <param name="sKind"></param>
        /// <param name="sPINo"></param>
        /// <param name="sPName"></param>
        /// <param name="sIName"></param>
        /// <param name="sCode"></param>
        /// <returns></returns>
        public static string InsertOrUpdateSysKind(string sKind, string sPINo, string sPName, string sIName, string sCode, string sURL, string sSeq, string sComp, string sInMan, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sNo = string.Empty;
            string sChDes = string.Empty;
            string sstr = string.Empty;
            int iMax;
            DataTable sdt;

            if (sFlag == "1") // 新增大类
            {
                string sMaxNo = DBHelper.GetMaxNo("T_SysKindList ", "TypeNo");//
                if (sMaxNo == "")
                {
                    sNo = "0001";
                }
                else
                {
                    iMax = int.Parse(sMaxNo) + 1;
                    int len = iMax.ToString().Length;
                    if (len < 2)
                    {
                        sNo = "0" + iMax.ToString();
                    }
                    else if ((len >= 2) && (len < 3))
                    {
                        sNo = "00" + iMax.ToString();
                    }
                    else if ((len >= 3) && (len < 4))
                    {
                        sNo = "0" + iMax.ToString();
                    }
                    else
                    {
                        sNo = iMax.ToString();
                    }
                }

                sSQL = " insert into T_SysKindList(TypeNo,TypeName,ItemName,InMan,CompanyNo)  " +
                       " values('" + sNo + "','" + sIName + "','无','" + sInMan + "','" + sComp + "') ";

            }
            else if (sFlag == "2")   // 新增小类
            {
                sdt = DBHelper.GetDataTable("select TypeNo from T_SysKindList where TypeName='" + sPName + "' and ItemName='无' ");
                if (sdt.Rows.Count > 0)  // 说明是新增大类时插入的一个记录，还没有新增过小类，更新这个 无即可
                {
                    sSQL = " update T_SysKindList set ItemName='" + sIName + "' , ItemNo='" + sPINo + "' , ItemOrder='" + sSeq + "' where TypeName='" + sPName + "' and ItemName='无' ";
                }
                else
                {
                    sSQL = " insert into T_SysKindList(TypeNo,TypeName,ItemName,ItemNo,ItemOrder,InMan,CompanyNo)  " +
                           " select distinct TypeNo,'" + sPName + "','" + sIName + "','" + sPINo + "','" + sSeq + "','" + sInMan + "','" + sComp + "' from T_SysKindList where TypeName='" + sPName + "' and CompanyNo='" + sComp + "' ";
                }

            }
            else if (sFlag == "23-1") // 新增菜单--模块
            {
                sdt = DBHelper.GetDataTable("select ParentID from T_SPModule WHERE ModuleID='" + sPINo + "' ");
                if (sdt.Rows.Count > 0)  // 如果存在这个模块，更新名称即可
                {
                    string sPNo = sdt.Rows[0]["ParentID"].ToString();
                    sSQL = " update T_SPModule set ModuleName='" + sIName + "' , ModuleDesc='" + sIName + "',SeqNo='" + sSeq + "' where ModuleID='" + sPINo + "' ";
                    sSQLs = " update T_SPModule set ModuleParent='" + sIName + "' where ParentID='" + sPNo + "' and ModuleID<>'" + sPINo + "' ";
                }
                else // 如果不存在，提交 10002
                {
                    string sMaxNo = DBHelper.GetMaxNo("T_SPModule ", "ParentID");//
                    if (sMaxNo == "")
                    {
                        sNo = "10001";
                    }
                    else
                    {
                        iMax = int.Parse(sMaxNo) + 1;
                        sNo = iMax.ToString();
                    }

                    sSQL = " insert into T_SPModule(ParentID,ModuleParent,ModuleName,ModuleDesc,ModuleURL,ModuleCode,SeqNo,Icon,AllowExpand,IsEnable,CompanyNo)  " +
                           " values('" + sNo + "','模块名称','" + sIName + "','" + sIName + "','/default','','" + sSeq + "','fa fa-desktop','0','是','" + sComp + "') ";
                }
            }
            else if (sFlag == "23-2") // 新增菜单--功能模块
            {
                sdt = DBHelper.GetDataTable("select ParentID from T_SPModule WHERE ModuleID='" + sPINo + "' ");
                if (sdt.Rows.Count > 0)  // 如果存在这个模块，更新名称即可
                {
                    sSQL = " update T_SPModule set ModuleName='" + sIName + "',ModuleDesc='" + sIName + "',ModuleURL='" + sURL + "',ModuleCode='" + sCode + "',SeqNo='" + sSeq + "' where ModuleID='" + sPINo + "' ";
                }
                else // 如果不存在，提交 10002
                {
                    sSQL = " insert into T_SPModule(ParentID,ModuleParent,ModuleName,ModuleDesc,ModuleURL,ModuleCode,SeqNo,Icon,AllowExpand,IsEnable,CompanyNo)  " +
                           " select ParentID,ModuleName,'" + sIName + "','" + sIName + "','" + sURL + "','" + sCode + "','" + sSeq + "','','0','是',CompanyNo from T_SPModule " +
                           " where ModuleParent='模块名称' and ModuleName='" + sPName + "'and CompanyNo= '" + sComp + "' ";
                }
            }
            else if (sFlag == "23-5") // - 给一个公司   新增菜单--模块 -
            {
                sSQL = " insert into T_SPModule(ParentID,ModuleParent,ModuleName,ModuleDesc,ModuleURL,ModuleCode,SeqNo,Icon,AllowExpand,IsEnable,CompanyNo)  " +
                       " select ParentID,'模块名称','" + sPName + "','" + sPName + "','/default','','" + sSeq + "','fa fa-desktop','0','是','" + sCode + "' " +
                       " from T_SPModule where ModuleParent='模块名称' and ModuleName='" + sPName + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "23-6") // 给一个公司    新增菜单--功能模块
            {
                sSQL = " insert into T_SPModule(ParentID,ModuleParent,ModuleName,ModuleDesc,ModuleURL,ModuleCode,SeqNo,Icon,AllowExpand,IsEnable,CompanyNo)  " +
                       " select top 1 ParentID,ModuleParent,'" + sIName + "','" + sIName + "',ModuleURL,ModuleCode,'" + sSeq + "','','0','是','" + sCode + "' from T_SPModule " +
                       " where ModuleParent='" + sPName + "' and ModuleName='" + sIName + "' and CompanyNo= '" + sComp + "' ";
            }



            List<string> links = new List<string>();
            links.Add(sSQL);
            links.Add(sSQLs);

            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }

        /// <summary>
        /// 判断类别是否存在
        /// </summary>
        /// <param name="Kind"></param>
        /// <param name="KindList"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string JudgeSysKindExist(string Kind, string KindList, string QT, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string sStatus = string.Empty;

            if (sFlag == "1")
            {
                sSQL = " select TypeNo from T_SysKindList where TypeName = '" + KindList + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "2")
            {
                sSQL = " select TypeNo from T_SysKindList where TypeName = '" + Kind + "' and ItemName= '" + KindList + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "3")
            {
                sSQL = " select ItemName from T_SysKindList where TypeName = '" + Kind + "' and CompanyNo= '" + sComp + "' and ItemName<> '无' ";  // 判断大类下面有没有小类，有不能删除
            }
            else if (sFlag == "20-1")//判断该厂商识别码是否存在
            {
                sSQL = " select InMan from T_MCode where MCode='" + Kind + "' ";  //  and CompanyNo= '" + sComp + "'
            }
            else if (sFlag == "20-2")//判断该公司是否存在一个在用的厂商识别码
            {
                sSQL = " select InMan from T_MCode where CompanyNo= '" + sComp + "' and IsUse='是' ";
            }
            else if (sFlag == "20-3")//判断是否已被使用
            {
                sSQL = " select TOP 1 InMan  from T_DIInfo WHERE ZXXSDYCPBS LIKE '" + Kind + "%' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "20-5")//判断是否已有启用的识别码
            {
                sSQL = " select InMan from T_MCode where CompanyNo= '" + sComp + "' and IsUse='是' ";
            }
            else if (sFlag == "21-1")//判断该序列号物料是否存在
            {
                sSQL = " select InMan from T_SerielMater where CompanyNo= '" + sComp + "' and MaterNo='" + Kind + "' ";
            }
            else if (sFlag == "21-2")//判断规则是否存在了
            {
                sSQL = " select InMan from T_SerielMater where SectionOne+SectionTwo+SectionThr+SectionFour+SectionFive+SectionSix+SectionSev='" + Kind + "' and MaterNo<>'" + KindList + "' and CompanyNo= '" + sComp + "' ";  // 
            }
            else if (sFlag == "21-3")//判断该序列号物料是否已发号
            {
                sSQL = " select InMan from T_SerielMater where CompanyNo= '" + sComp + "' and MaterNo='" + Kind + "' ";
            }
            else if (sFlag == "23-1") // 判断模块是否存在
            {
                sSQL = " select IsEnable from T_SPModule where ModuleParent='模块名称' and ModuleName = '" + KindList + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "23-2")// 判断模块下面的菜单是否存在
            {
                sSQL = " select IsEnable from T_SPModule where ModuleParent= '" + Kind + "' and ModuleName = '" + KindList + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "23-3")// 判断该模块是否已分配给给公司了。
            {
                sSQL = " select IsEnable from T_SPModule where ModuleParent= '" + Kind + "'and ModuleName= '" + KindList + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "24-3")// 判断用户是否存在   and CompanyNo= '" + sComp + "'
            {
                sSQL = " select FullName from T_SPUser where LoginName= '" + Kind + "' ";
            }
            else if (sFlag == "25-5")// 判断角色是否存在
            {
                sSQL = " select OperatorID from T_SPRole where RoleName= '" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "25-6")// 判断角色是否已被使用
            {
                sSQL = " select UserRoleID from T_SPUserRole where RoleID= '" + Kind + "' ";
            }
            else if (sFlag == "28-1")// 判断该产品编码是否存在该标贴
            {
                sSQL = " select InMan from T_ProductAndLabel where PMaterNo='" + Kind + "' and MaterNo='" + KindList + "'and CompanyNo= '" + sComp + "' ";
            }
            //else if (sFlag == "28-2")// 判断是否已打标贴，如果是，不能删除，不能修改
            //{
            //    sSQL = " select InMan from T_ProductAndLabel where PMaterNo='" + Kind + "' and MaterNo='" + KindList + "' and CompanyNo= '" + sComp + "' ";
            //}
            else if (sFlag == "28-3")// 判断该产品编码下面的标贴有没有自动打印的标贴
            {
                sSQL = " select InMan from T_ProductAndLabel where PMaterNo='" + Kind + "' and  '('+ProcedureNo+')'+ProcedureName='" + QT + "' and AutoPrint='是' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "31-1")// 判断公司代码是否已存在
            {
                sSQL = " select InMan from T_CompanyInfo where CompID='" + Kind + "' ";
            }
            else if (sFlag == "50-3-1")// 新增 修改时，判断大类是否存在
            {
                sSQL = " select InMan from T_MaterType where TypeName='" + KindList + "' and Kind='大类' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "50-3-2")// 新增时，判断小类是否存在
            {
                sSQL = " select InMan from T_MaterType where FKind='" + Kind + "' and TypeName='" + KindList + "' and Location='" + QT + "' and Kind='小类'  and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "50-4")//删除大类钱，判断下面是否有小类
            {
                sSQL = " SELECT InMan FROM T_MaterType where FKind='" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "50-5")//修改时，判断物料分类是否存在  sComp 这个变量存放的是修改记录ID
            {
                sSQL = " select InMan from T_MaterType where TypeNo='" + Kind + "' and TypeName='" + KindList + "' and Location='" + QT + "' and MTID<> '" + sComp + "' ";
            }
            else if (sFlag == "51")//判断物料是否存在
            {
                sSQL = " select InMan from T_MaterInfo where MaterNo='" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "54-3")//新增客供物料，判断物料是否存在
            {
                sSQL = " select InMan from T_CustMater where MaterNo='" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "40-1")//判断供应商编码是否存在
            {
                sSQL = " select InMan from T_CustInfo where CustNo='" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "41-1")//判断供应商编码是否存在 ，不需要增加状态判断来判断
            {
                sSQL = " select InMan from T_SupplierInfo where SupplierNo='" + Kind + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "44-1")//判断父零件下是否有该子零件
            {
                sSQL = " select InMan from T_BOMInfo where FMaterNo='" + Kind + "' and CMaterNo='" + KindList + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "44-2")//判断该公司录入BOM的层级
            {
                // 先看有没有配置录入层级的控制
                string sNum = string.Empty;
                int iNum = 100;
                int j = 0;
                sSQL = " select SysValue from T_SysConfig where SysKind='BOM设计允许录入层级数' where CompanyNo= '" + sComp + "' ";
                DataTable dtc = DBHelper.GetDataTable(sSQL);
                if (dtc.Rows.Count > 0)  // 说明设置了层级限制
                {
                    sNum = dtc.Rows[0]["SysValue"].ToString();
                    if (sNum == "")
                    {
                        iNum = 100;  // BOM最多100层
                    }
                    else
                    {
                        iNum = int.Parse(sNum);
                    }

                    for (int i = 0; i <= iNum; i++)
                    {

                    }

                    sSQL = " select InMan from T_BOMInfo where CMaterNo='" + Kind + "' and CompanyNo= '" + sComp + "' ";
                }
                else
                {
                    sStatus = "N";
                    return sStatus;
                }


            }
            else if (sFlag == "44-3")//判断父零件下是否还有子零件，有不给删除
            {
                sSQL = " select InMan from T_BOMInfo where FMaterNo='" + KindList + "' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "44-4")//判断该主料下面是否维护了替代料
            {
                sSQL = " select InMan from T_BOMInfo where FMaterNo='" + Kind + "' and CMaterNo='" + KindList + "'  and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "44-5")//如果是选择了典型产品，判断这个工艺代号，系统是否已有典型产品了，如是，不给在设置：一个工艺代号，只能一个典型产品
            {
                if (KindList.Substring(0, 4) == "1201")
                {  // 主机
                    sSQL = " select InMan from T_BOMInfo where TechNo='" + Kind + "' and ClassFlow='是' and FMaterNo like '1201%' and CompanyNo= '" + sComp + "' and FMaterNo<>'" + KindList + "' ";
                }
                else
                {  // 整机
                    sSQL = " select InMan from T_BOMInfo where TechNo='" + Kind + "' and ClassFlow='是' and CompanyNo= '" + sComp + "' and (FMaterNo like '11%' or FMaterNo like '%-CTO-%') and FMaterNo<>'" + KindList + "' ";
                }
            }
            else if (sFlag == "26-1")//判断该工单是否已发号 SeqNo = sOKFlag.Substring(m + 3, sOKFlag.Length - m - 3);
            {
                sSQL = " select (case when count(SerielNo)='" + KindList + "' then 'true' else 'false' end) as IsOK from T_SerielInfo where OrderNo='" + Kind + "' ";
                DataTable dtc = DBHelper.GetDataTable(sSQL);
                if (dtc.Rows.Count > 0)
                {
                    return dtc.Rows[0]["IsOK"].ToString() == "true" ? "Y" : "N";
                }
                else
                {
                    return "Y";
                }
            }
            else if (sFlag == "26-2")//判断该序列号是否已使用 -- 主要判断是否已创建了流程，流程是都有的，BOM不一定有
            {
                sSQL = " select top 1 InMan from T_SerialBaseFlow WHERE SerialNo in (select SerielNo from T_SerielInfo where OrderNo='" + Kind + "') and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "26-3")//判断序列号是否已生产，如是，不可删除
            {
                sSQL = " select InMan from T_ProductBatchInfo WHERE BatchNo in (select SerielNo from T_SerielInfo where OrderNo='" + Kind + "') and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "27-1")//判断产品编码或型号是否已发放DI码
            {
                sSQL = " select  InMan from T_DIInfo where CPHHHBH = '" + Kind + "' ";  //  or GGXH= '" + KindList + "' 
            }
            else if (sFlag == "27-2")//修改主DI码时，判断修改后的主DI是否存在了。
            {
                sSQL = " select  InMan from T_DIInfo where ZXXSDYCPBS = '" + Kind + "' ";  //  or GGXH= '" + KindList + "' 
            }
            else if (sFlag == "29-1")//判断标贴模板是否存在
            {
                sSQL = " select InMan from T_LabelTemplate where TPNo='" + Kind + "' and TPVer='" + KindList + "' ";
            }
            else if (sFlag == "29-2")//判断状态是否正确：不是草稿状态
            {
                sSQL = " select InMan from T_LabelTemplate where TPNo='" + Kind + "' and TPVer='" + KindList + "' and Status<>'草稿' ";
            }
            else if (sFlag == "29-3")//判断状态是否正确：不是待审核状态
            {
                sSQL = " select InMan from T_LabelTemplate where TPNo='" + Kind + "' and TPVer='" + KindList + "' and Status<>'待审核' ";
            }
            else if (sFlag == "29-4")//判断状态是否正确：不是启用状态
            {
                sSQL = " select InMan from T_LabelTemplate where TPNo='" + Kind + "' and TPVer='" + KindList + "' and Status<>'启用' ";
            }
            else if (sFlag == "29-5")//启用：禁用状态下才可以启用
            {
                sSQL = " select InMan from T_LabelTemplate where TPNo='" + Kind + "' and TPVer='" + KindList + "' and Status<>'禁用' ";
            }
            else if (sFlag == "30-1")//判断物料有没有工艺路线
            {
                sSQL = " select top 1 InMan from T_ProductFlow where ProductNo='" + Kind + "' and CompanyNo='" + sComp + "' ";
            }
















            DataTable dt = DBHelper.GetDataTable(sSQL);
            if (dt.Rows.Count > 0)
            {
                sStatus = "Y";
            }
            else
            {
                sStatus = "N";
            }


            return sStatus;
        }

        /// <summary>
        /// 删除系统类别
        /// </summary>
        /// <param name="sKind"></param>
        /// <param name="sItem"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string DeleteSysKind(string sKind, string sItem, string sComp, string sInMan, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sstr = string.Empty;

            if (sFlag == "1")
            {
                sSQL = " delete T_SysKindList where TypeName='" + sKind + "' and CompanyNo='" + sComp + "' ";
            }
            else
            {
                sSQL = " delete T_SysKindList where TypeName='" + sKind + "' and ItemName= '" + sItem + "' and CompanyNo='" + sComp + "' ";
            }


            List<string> links = new List<string>();
            links.Add(sSQL);

            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }


        /// <summary>
        /// 获取客户信息/公司信息
        /// </summary>
        /// <param name="CustNo"></param>
        /// <param name="CustName"></param>
        /// <param name="Kind"></param>
        /// <param name="DQ"></param>
        /// <param name="BSC"></param>
        /// <param name="YD"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetCustInfo(string CustNo, string CustEn, string sTJ, string sFS, string sCKind, int Row, int num, string LoginMan, string sComp, string sFlag)
        {
            string sSQL = string.Empty;

            DataTable sdt = new DataTable();

            if ((sFlag == "40") || (sFlag == "31"))
            {
                SqlParameter[] parameters = {
                    new SqlParameter("@lnNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnTJ", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnFS", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnCKind", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnFlag", SqlDbType.NVarChar,10),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
                parameters[0].Value = CustNo;
                parameters[1].Value = CustEn;
                parameters[2].Value = sTJ;
                parameters[3].Value = sFS;
                parameters[4].Value = sCKind;
                parameters[5].Value = Row;
                parameters[6].Value = num;
                parameters[7].Value = LoginMan;
                parameters[8].Value = sFlag;
                parameters[9].Value = "";

                DataSet DS = DBHelper.RunProcedureForDS("P_GetCustForPage", parameters);
                sdt = DS.Tables[0];

                return sdt;
            }
            else if (sFlag == "41-2")  // 显示单个客户
            {
                sSQL = " SELECT  * FROM T_CustInfo where CompanyNo = '" + sComp + "' and CustNo = '" + CustNo + "' ";
            }
            else if (sFlag == "31-2")  // 查询用户表的公司代码
            {
                sSQL = " select distinct a.CompanyNo,case when b.CompName is not null then a.CompanyNo+'_'+b.CompName else a.CompanyNo end as CName from T_SPUser a left join T_CompanyInfo b on a.CompanyNo=b.CompanyNo ";
            }
            else
            {
                sSQL = " SELECT top 200 * FROM T_CustInfo order by CustNo ";
            }

            sdt = DBHelper.GetDataTable(sSQL);

            return sdt;
        }


        /// <summary>
        /// 获取分页记录
        /// </summary>
        /// <param name="sConn">传入查询条件</param>
        /// <param name="Row">每页显示行</param>
        /// <param name="Total">共记录数</param>
        /// <param name="num">现在要显示第几页</param>
        /// <param name="LoginMan">当前登录人</param>
        /// <param name="sCPNOStr">公司代码查询条件</param>
        /// <returns></returns>
        public static DataTable GetPageCustInfo(string sYDNo, string sYD, string sCustNo, string sName, string sBSC, string sCity, int Row, int num, string LoginMan)
        {
            DataTable sdt = new DataTable();

            SqlParameter[] parameters = {
                    new SqlParameter("@lnYDNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnYD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnCustNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnCustName", SqlDbType.NVarChar,200),
                    new SqlParameter("@lnBSC", SqlDbType.NVarChar,60),
                    new SqlParameter("@lnCity", SqlDbType.NVarChar,60),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
            parameters[0].Value = sYDNo;
            parameters[1].Value = sYD;
            parameters[2].Value = sCustNo;
            parameters[3].Value = sName;
            parameters[4].Value = sBSC;
            parameters[5].Value = sCity;
            parameters[6].Value = Row;
            parameters[7].Value = num;
            parameters[8].Value = LoginMan;
            parameters[9].Value = "";

            DataSet DS = DBHelper.RunProcedureForDS("P_GetCustInfo", parameters);
            sdt = DS.Tables[0];

            return sdt;
        }


        /// <summary>
        /// 获取功能菜单
        /// </summary>
        /// <param name="UserNo"></param>
        /// <param name="sComp">后面扩展用，根据不同公司显示不同菜单（可新增一个表，专门放公司使用菜单，从这个表获取菜单显示在界面）</param>
        /// <param name="sModel"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetMenu(string UserNo, string sModel, int sModelID, string sComp, string sFlag)
        {
            string sSQL = string.Empty;

            if (sFlag == "22-1")  // 加载左边导航菜单
            {
                sSQL = "select InMan from T_SysConfig where SysKind='管理用户登录权限' and SysValue='1' and CompanyNo = '" + sComp + "' ";
                DataTable Rdt = DBHelper.GetDataTable(sSQL);
                if (Rdt.Rows.Count > 0)  // 说明按登录人账号判断是否有哪些操作界面的权限
                {
                    sSQL = " select ModuleName,ParentID,AllowExpand,Icon,ModuleID from T_SPModule where IsEnable='是' and  ModuleParent ='模块名称' and CompanyNo = '" + sComp + "' " +
                           " and ParentID in (select distinct b.FModuleID from T_SPUserRole a join T_SPRoleModule b on a.RoleID=b.RoleID where a.LoginName='" + UserNo + "') " +
                           " order by SeqNo ";
                }
                else
                {
                    sSQL = " select ModuleName,ParentID,AllowExpand,Icon,ModuleID from T_SPModule where IsEnable='是' and  ModuleParent ='模块名称' and CompanyNo = '" + sComp + "' order by SeqNo ";
                }
            }
            else if (sFlag == "22-2")  // 加载左边导航菜单 - 下面的菜单
            {
                sSQL = "select InMan from T_SysConfig where SysKind='管理用户登录权限' and SysValue='1' and CompanyNo = '" + sComp + "' ";
                DataTable Rdt = DBHelper.GetDataTable(sSQL);
                if (Rdt.Rows.Count > 0)  // 说明按登录人账号判断是否有哪些操作界面的权限
                {
                    sSQL = " select ModuleName,ModuleID,ModuleDesc,ModuleCode,ModuleURL,Icon,SeqNo from T_SPModule where IsEnable='是' and  ModuleParent='" + sModel + "' and CompanyNo = '" + sComp + "' " +
                           " and ModuleID in (select distinct b.ModuleID from T_SPUserRole a join T_SPRoleModule b on a.RoleID=b.RoleID where a.LoginName='" + UserNo + "') " +
                           " order by SeqNo ";
                }
                else
                {
                    sSQL = " select ModuleName,ModuleID,ModuleDesc,ModuleCode,ModuleURL,Icon,SeqNo from T_SPModule where IsEnable='是' and  ModuleParent='" + sModel + "' and CompanyNo = '" + sComp + "' order by SeqNo ";
                }
            }
            else if (sFlag == "23-1")  // 模块管理：获取模块
            {
                sSQL = " select ModuleName,ParentID,AllowExpand,Icon,SeqNo,ModuleID,'<label onclick=''openDialog(1)''class=''LabelAddBtn''>编辑</label>' as EE,'<label onclick=''DistModel(1)''class=''LabelAddBtn''>分配公司</label>' as AddComp,'<label onclick=''DelModel(1)''class=''LabelDelBtn''>删除</label>' as DEL " +
                       " from T_SPModule where IsEnable='是' and  ModuleParent ='模块名称' and CompanyNo = '" + sComp + "' order by SeqNo ";
            }
            else if (sFlag == "23-2")  // 模块管理：获取模块下面的菜单
            {
                sSQL = " select ParentID,ModuleName,ModuleID,ModuleDesc,ModuleCode,ModuleURL,Icon,SeqNo,'<label onclick=''openDialog(2)''class=''LabelAddBtn''>编辑</label>' as EE,'<label onclick=''DistModel(2)''class=''LabelAddBtn''>分配公司</label>' as AddComp,'<label onclick=''DelModel(2)''class=''LabelDelBtn''>删除</label>' as DEL " +
                       " from T_SPModule where IsEnable='是' and  ModuleParent='" + sModel + "' and CompanyNo = '" + sComp + "' order by SeqNo ";
            }
            else if (sFlag == "23-3")  // 查询该大模块下有给了哪些公司
            {
                sSQL = " select a.CompanyNo,b.CompName,a.IsEnable,'<label onclick=''DelCompModel(6)''class=''LabelDelBtn''>删除</label>' as DEL " +
                       " from T_SPModule a left join T_CompanyInfo b on a.CompanyNo=b.CompanyNo where a.ModuleParent='模块名称' and a.ModuleName='" + sModel + "' order by a.CompanyNo ";
            }
            else if (sFlag == "23-4")  // 查询该功能菜单给了哪些公司
            {
                sSQL = " select a.CompanyNo,b.CompName,a.IsEnable,'<label onclick=''DelCompModel(7)''class=''LabelDelBtn''>删除</label>' as DEL " +
                       " from T_SPModule a left join T_CompanyInfo b on a.CompanyNo=b.CompanyNo where a.ModuleParent='" + sModel + "' and a.ModuleName='" + UserNo + "' order by a.CompanyNo ";
            }
            else if (sFlag == "24-1")  // 获取模块--角色管理用
            {
                sSQL = " select ModuleName,ParentID,ModuleID,'' as CheckFlag from T_SPModule where IsEnable='是' and  ModuleParent ='模块名称' and CompanyNo = '" + sComp + "' order by SeqNo ";
            }
            else if (sFlag == "24-2")  // 加载采购单下面的功能模块
            {
                sSQL = " select ModuleName,ModuleID,'' as CheckFlag from T_SPModule where IsEnable='是' and  ModuleParent='" + sModel + "' and CompanyNo = '" + sComp + "' order by SeqNo ";
            }
            else if (sFlag == "24-3")  // 功能模块下的功能按钮
            {
                sSQL = " select FuncID,FuncName,ModuleID from T_SPModuleFunc where  ModuleID=" + sModelID + " ";
            }
            else if (sFlag == "24-6")  // 角色管理，点击网格的角色，获取合格角色具有的操作权限（菜单）
            {
                sSQL = " select ModuleID from T_SPRoleModule where  RoleID='" + sModelID + "' ";
            }


            DataTable sdt = DBHelper.GetDataTable(sSQL);

            return sdt;
        }


        /// <summary>
        /// 删除模块或功能菜单
        /// </summary>
        /// <param name="ID"></param>
        /// <param name="Name"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string DeleteModelInfo(string ID, string Name, string sInMan, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sstr = string.Empty;

            sSQL = " delete T_SPModule where ModuleID='" + ID + "' ";

            List<string> links = new List<string>();
            links.Add(sSQL);

            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }

        /// <summary>
        /// 获取角色信息
        /// </summary>
        /// <param name="UserNo"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetRoleInfo(string UserNo, string sComp, string sFlag)
        {
            string sSQL = string.Empty;

            if (sFlag == "25-1")  // 按公司代码获取权限 select RoleID,RoleName,companyNo from T_SPRole
            {
                sSQL = " select RoleID,RoleName,companyNo,'<label onclick=''DelRole(1)''class=''LabelDelBtn''>删除</label>' as DEL,0 as UserRole from T_SPRole where CompanyNo = '" + sComp + "' order by RoleName ";
            }
            else if (sFlag == "25-2")  // 获取用户下的角色  -- 修改用户使用
            {
                sSQL = " select a.RoleID,a.RoleName,b.loginName,ISNULL(b.RoleID,0) as UserRole " +
                       " from T_SPRole a left join (select RoleID,loginName from T_SPUserRole where LoginName='" + UserNo + "') b on a.RoleID=b.RoleID where a.CompanyNo = '" + sComp + "' order by a.RoleName ";
            }

            DataTable sdt = DBHelper.GetDataTable(sSQL);

            return sdt;
        }


        /// <summary>
        /// 新增修改用户信息
        /// </summary>
        /// <param name="sKind"></param>
        /// <param name="sNo"></param>
        /// <param name="sName"></param>
        /// <param name="sDept"></param>
        /// <param name="sPwd"></param>
        /// <param name="sZW"></param>
        /// <param name="sSex"></param>
        /// <param name="sTelph"></param>
        /// <param name="sPhone"></param>
        /// <param name="sQQ"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string AddEditUser(string sKind, string sNo, string sName, string sDept, string sPwd, string sZW, string sSex, string sTelph, string sPhone, string sQQ, string sEmail, string sFile, string sPath, string sComp, string sRStr, string sInMan, string sFlag,string IP,string ModuleName)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sStr = string.Empty;
            string sSQLs = string.Empty;
            string sDNo = string.Empty;
            string sDName = string.Empty;
            string sstr = string.Empty;
            string expireTime = DateTime.Now.AddMonths(6).ToString("yyyy-MM-dd HH:mm:ss");

            //记录日志的参数
            string Message = string.Empty;      //日志信息
            string LogType = string.Empty;      //日志类型
            string NewValue = string.Empty;     //新数据
            string OldValue = string.Empty;     //旧数据
            string TableName = string.Empty;    //表名
            string Condition = string.Empty;    //查询条件
            string Column = string.Empty;       //列名


            if (!string.IsNullOrEmpty(sDept)) // 部门（0002）生产部
            {
                sDNo = sDept.Substring(1, sDept.IndexOf(")") - 1);
                sDName = sDept.Substring(sDept.IndexOf(")") + 1, sDept.Length - sDept.IndexOf(")") - 1);
            }


            if (sFlag == "24-3") // 新增用户
            {
                sSQL = " insert into T_SPUser(LoginName,FullName,Pwd,CompanyNo,DeptNo,DeptName,UserSex,DaQu,BSC,BanShiChu,ManagerNo,ManagerName,ZhuGuan,ZGName,Kind,WeiXinNo,Phone,QYNo,SFCode,Supplier,SignImg,ImgPath,OperatorID,OperatorTime,ExpireTime,Status) " +
                       " values('" + sNo + "','" + sName + "','" + sPwd + "','" + sComp + "','" + sDNo + "','" + sDName + "','" + sSex + "','','','','','','','','" + sZW + "','" + sTelph + "','" + sPhone + "','" + sQQ + "','" + sEmail + "','','" + sFile + "','" + sPath + "','" + sInMan + "',convert(char(16),getdate(),120),'" + expireTime + "','OK') ";

                Message = "添加用户(" + sNo + ")" + sName + "用户信息。";
                LogType = "添加";
                TableName = "T_SPUser";
                Condition = "LoginName='" + sNo + "'";
                Column = "*";
            }
            else if (sFlag == "24-4") //  修改用户
            {
                sSQL = " update T_SPUser set FullName='" + sName + "',Pwd='" + sPwd + "',DeptNo='" + sDNo + "',DeptName='" + sDName + "',UserSex='" + sSex + "',Kind='" + sZW + "',WeiXinNo='" + sTelph + "',Phone='" + sPhone + "',QYNo='" + sQQ + "',SFCode='" + sEmail + "',SignImg='" + sFile + "',ImgPath='" + sPath + "',ExpireTime='" + expireTime + "' " +
                       " where LoginName='" + sNo + "' ";

                // 删除之前的权限
                sStr = " delete T_SPUserRole where LoginName='" + sNo + "'";

                Message = "修改用户(" + sNo + ")" + sName + "用户信息。";
                LogType = "修改";
                TableName = "T_SPUser";
                Condition = "LoginName='" + sNo + "'";
                Column = "*";
            }
            else if (sFlag == "24-5") // 自己修改自己的信息
            {
                sSQL = " update T_SPUser set FullName='" + sName + "',Pwd='" + sPwd + "',UserSex='" + sSex + "',Kind='" + sZW + "',WeiXinNo='" + sTelph + "',Phone='" + sPhone + "',QYNo='" + sQQ + "',SFCode='" + sEmail + "',ExpireTime='" + expireTime + "' " +
                       " where LoginName='" + sNo + "' ";
                Message = "修改(" + sNo + ")" + sName + "个人用户信息。";
                LogType = "修改";
                TableName = "T_SPUser";
                Condition = "LoginName='" + sNo + "'";
                Column = "*";
            }
            else if (sFlag == "24-6")// 禁用账号
            {
                sSQL = "update T_SPUser set Status='NO' where LoginName='" + sNo + "'";
                Message = "禁用(" + sNo + ")" + sName + "用户。";
                LogType = "禁用";
            }
            else if (sFlag == "24-7")// 启用账号
            {
                sSQL = "update T_SPUser set Status='OK' where LoginName='" + sNo + "'";
                Message = "启用(" + sNo + ")" + sName + "用户。";
                LogType = "启用";
            }

            if (sFlag == "24-3" || sFlag == "24-4" || sFlag == "24-5")
            {
                //插入权限 不知2022 开发工具怎么有一个 on：sRStr = "(on)"
                sRStr = sRStr.Replace(",on", "");
                sRStr = sRStr.Replace("on", "");
                if (sRStr != "()")
                {
                    sSQLs = " insert into T_SPUserRole(LoginName,RoleID,OperatorID,OperatorTime)  select  '" + sNo + "',RoleID,'" + sInMan + "',convert(char(16),getdate(),120) from T_SPRole where RoleID in " + sRStr + " ";
                }
            }

            List<string> links = new List<string>();
            links.Add(sSQL);
            links.Add(sStr);
            links.Add(sSQLs);

            try
            {
                //删除、修改操作需要获取操作前的数据
                if (LogType == "删除" || LogType == "修改")
                {
                    OldValue = LogHelper.GetValue(TableName, Condition, Column);
                }

                //DBHelper.ExecuteCommand(sSQL);
                iFlag = DBHelper.ExecuteSqlTranStr(links);

                //新增、修改操作需要获取操作后的数据
                if (LogType == "添加" || LogType == "修改")
                {
                    NewValue = LogHelper.GetValue(TableName, Condition, Column);
                }

                //记录操作日志
                if (!iFlag.Contains("Err"))
                {
                    LogHelper.LogInfo(LogType, Message, IP, sComp, sInMan, ModuleName, NewValue, OldValue);
                }
                //记录错误日志
                else
                {
                    Message = Message + "法生错误，错误原因：" + iFlag;
                    sSQL = String.Join("/", links.ToArray());
                    LogHelper.LogError(LogType, Message, IP, sComp, sInMan, ModuleName, sSQL);
                }
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }


        /// <summary>
        /// 删除用户信息
        /// </summary>
        /// <param name="sNo"></param>
        /// <param name="sInMan"></param>
        /// <returns></returns>
        public static string DelUserInfo(string sNo, string sInMan,string IP,string sComp,string ModuleName)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sStr = string.Empty;
            string sstr = string.Empty;
            string OldValue = string.Empty;


            sStr = " delete T_SPUserRole where LoginName='" + sNo + "'";  // 删除用户对应的角色
            sSQL = " delete T_SPUser where LoginName='" + sNo + "'";  // 删除用户

            List<string> links = new List<string>();
            links.Add(sStr);
            links.Add(sSQL);

            try
            {
                OldValue = LogHelper.GetValue("T_SPUser", "LoginName='" + sNo + "'", "*");

                //DBHelper.ExecuteCommand(sSQL);
                iFlag = DBHelper.ExecuteSqlTranStr(links);

                //记录操作日志
                if (!iFlag.Contains("Err"))
                {
                    LogHelper.LogInfo("删除", "删除用户" + sNo, IP, sComp, sInMan, ModuleName, "", OldValue);
                }
                //记录错误日志
                else
                {
                    sSQL = String.Join("/", links.ToArray());
                    LogHelper.LogError("删除", "删除用户" + sNo + "。法生错误，错误原因：" + iFlag, IP, sComp, sInMan, ModuleName, sSQL);
                }
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }


        /// <summary>
        /// 插入角色对应的菜单
        /// </summary>
        /// <param name="sRoleID"></param>
        /// <param name="sComp"></param>
        /// <param name="sRStr"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string InsertRoleModule(string sRoleID, string sComp, string sRStr, string sFStr, string sInMan, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sStr = string.Empty;
            string sstr = string.Empty;

            // 删除这个角色之前所有模块
            sStr = " delete T_SPRoleModule where RoleID='" + sRoleID + "'";

            if (sRStr != "()")
            {
                //插入权限 -- 插入功能菜单
                sSQL = " insert into T_SPRoleModule(RoleID,FModuleID,ModuleID,IsExec,OperatorID)  select  '" + sRoleID + "',ParentID,ModuleID,1,'" + sInMan + "' from T_SPModule where ModuleID in " + sRStr + " ";
                //插入权限 -- 插入功能菜单对应的按钮 select FuncID from T_SPModuleFunc
                if (sFStr != "()")
                {
                    sSQLs = " insert into T_SPRoleModule(RoleID,FModuleID,ModuleID,IsExec,OperatorID)  select  '" + sRoleID + "',ModuleID,FuncID,1,'" + sInMan + "' from T_SPModuleFunc where FuncID in " + sFStr + " ";
                }
            }

            List<string> links = new List<string>();
            links.Add(sStr);
            links.Add(sSQL);
            links.Add(sSQLs);


            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }

        /// <summary>
        /// 添加角色信息
        /// </summary>
        /// <param name="sName"></param>
        /// <param name="sInMan"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string InsertRoleInfo(string sName, string sInMan, string sComp, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sstr = string.Empty;


            sSQL = " Insert into T_SPRole(RoleName,OperatorID,OperatorTime,CompanyNo) values('" + sName + "','" + sInMan + "',convert(char(16),getdate(),120),'" + sComp + "') ";

            List<string> links = new List<string>();
            links.Add(sSQL);

            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }

        /// <summary>
        /// 删除角色
        /// </summary>
        /// <param name="sNo"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string DeleteRole(string sNo, string sInMan, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sStr = string.Empty;
            string sstr = string.Empty;


            sStr = " delete T_SPRoleModule where RoleID='" + sNo + "'";  // 删除角色对应的模块
            sSQL = " delete T_SPRole where RoleID='" + sNo + "'";  // 删除角色 

            List<string> links = new List<string>();
            links.Add(sStr);
            links.Add(sSQL);

            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }

        /// <summary>
        /// 获取物料分类
        /// </summary>
        /// <param name="Kind"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetMaterType(string Kind, string sComp, string sFlag)
        {
            string sSQL = string.Empty;

            if (sFlag == "50")
            {
                sSQL = "select MTID,TypeNo,TypeName,Location,Remark,'<label onclick=''openDialog(3)'' class=''LabelAddBtn''>修改</label>' as Edit,'<label onclick=''DelMaterType(1)'' class=''LabelDelBtn''>删除</label>' as DEL " +
                       "from T_MaterType where Kind='大类' and TypeName like '%" + Kind + "%' and CompanyNo = '" + sComp + "' order by TypeName ";
            }
            else if (sFlag == "50-1")  // 查询子类
            {
                sSQL = "select MTID,TypeNo,TypeName,Location,Remark,FTypeName,'<label onclick=''openDialog(4)'' class=''LabelAddBtn''>修改</label>' as Edit,'<label onclick=''DelMaterType(2)''class=''LabelDelBtn''>删除</label>' as DEL " +
                       "from T_MaterType where CompanyNo = '" + sComp + "'and FKind='" + Kind + "' order by FTypeName,TypeNo ";
            }
            else if (sFlag == "50-2")  // 50-2  --根据类别查询，下拉类别使用 物料分类用
            {
                sSQL = "select TypeNo,TypeName from T_MaterType where FKind in (select MTID from T_MaterType where TypeName='" + Kind + "'  and CompanyNo='" + sComp + "') order by TypeNo ";
            }
            else if (sFlag == "50-3")  //--根据类别查询，下拉类别使用 -- 物料信息用
            {
                sSQL = " select TypeNo,TypeName from T_MaterType where FTypeName ='" + Kind + "' order by TypeNo ";
            }
            else if (sFlag == "60-1")  //--物料信息：获取抽样类别下拉
            {
                sSQL = " select distinct SPNo,SPName  from T_SamplingPlan where CompanyNo='" + sComp + "' order by SPNo ";
            }


            DataTable sdt = DBHelper.GetDataTable(sSQL);

            return sdt;
        }

        /// <summary>
        ///  添加物料分类
        /// </summary>
        /// <param name="KNo"></param>
        /// <param name="MName"></param>
        /// <param name="FKind"></param>
        /// <param name="Location"></param>
        /// <param name="Remark"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string AddEditMaterType(string MTID, string KNo, string MName, string FKID, string Location, string FType, string Remark, string sComp, string sInMan, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sKind = string.Empty;
            string sstr = string.Empty;

            if (sFlag == "1")// 添加的是大类
            {
                sKind = "大类";
                FKID = "";
            }
            else
            {
                sKind = "小类";
            }

            if ((sFlag == "1") || (sFlag == "2")) // 新增
            {
                sSQL = " insert into T_MaterType(TypeNo,TypeName,Location,Kind,FKind,FTypeName,CompanyNo,InMan,Remark)  " +
                       " values('" + KNo + "','" + MName + "','" + Location + "','" + sKind + "','" + FKID + "','" + FType + "','" + sComp + "','" + sInMan + "','" + Remark + "') ";
            }
            else // 修改
            {
                sSQL = " update  T_MaterType set TypeNo='" + KNo + "',TypeName='" + MName + "',Location='" + Location + "',FTypeName='" + FType + "',Remark='" + Remark + "' where MTID=" + MTID + " ";
            }

            List<string> links = new List<string>();
            links.Add(sSQL);

            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }

        /// <summary>
        /// 删除物料分类
        /// </summary>
        /// <param name="MTID"></param>
        /// <param name="sMater"></param>
        /// <param name="sNo"></param>
        /// <param name="sLoca"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string DelMaterType(string MTID, string sMater, string sNo, string sLoca, string sComp, string sInMan, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sstr = string.Empty;

            sSQL = " delete T_MaterType where MTID='" + MTID + "' ";

            // 保存操作日志

            List<string> links = new List<string>();
            links.Add(sSQL);

            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }


        /// <summary>
        ///  获取物料信息
        /// </summary>
        /// <param name="MaterNo"></param>
        /// <param name="MaterName"></param>
        /// <param name="Kind"></param>
        /// <param name="MaterSpec"></param>
        /// <param name="TechNo"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetMaterInfo(string MaterNo, string MaterName, string Kind, string MaterSpec, string BigType, int Row, int num, string sComp, string LoginMan, string sFlag)
        {
            string sSQL = string.Empty;
            DataTable sdt = new DataTable();


            if (sFlag == "51-1")  // 只显示列表
            {
                sSQL = " SELECT MaterNo,MaterName FROM T_MaterInfo where CompanyNo = '" + sComp + "' and MaterNo  like '" + MaterNo + "%' and MaterName like '" + MaterName + "%'  order by CustName ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "51-2")  //  
            {
                SqlParameter[] parameters = {
                    new SqlParameter("@lnMaterNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMaterName", SqlDbType.NVarChar,200),
                    new SqlParameter("@lnMaterSpec", SqlDbType.NVarChar,60),
                    new SqlParameter("@lnCustNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnBigType", SqlDbType.NVarChar,60),
                    new SqlParameter("@lnMaterType", SqlDbType.NVarChar,60),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
                parameters[0].Value = MaterNo;
                parameters[1].Value = MaterName;
                parameters[2].Value = MaterSpec;
                parameters[3].Value = "";  // 客户代码
                parameters[4].Value = BigType;
                parameters[5].Value = Kind;
                parameters[6].Value = Row;
                parameters[7].Value = num;
                parameters[8].Value = LoginMan;
                parameters[9].Value = "";

                DataSet DS = DBHelper.RunProcedureForDS("P_GetMaterInfo", parameters);
                sdt = DS.Tables[0];

                return sdt;
            }
            else if (sFlag == "33-99")  //根据物料编码返回物料名称
            {
                sSQL = " SELECT * FROM T_MaterInfo where CompanyNo = '" + sComp + "' and MaterNo  = '" + MaterNo + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "33-100")  //根据产品编码返回DI信息
            {
                sSQL = " SELECT a.*,b.CPBSBMTXMC,b.ZXXSDYCPBS,b.ZXXSDYZSYDYDSL,b.SYDYCPBS,b.CPBSFBRQ,b.CPBSZT,b.SFYZCBACPBSYZ,b.ZCBACPBS,b.SFYBTZJBS,b.BTCPBSYZXXSDYCPBSSFYZ,b.BTCPBS " +
                       " FROM T_MaterInfo a left join T_DIInfo b on a.MaterNo=b.CPHHHBH where a.CompanyNo = '" + sComp + "' and a.MaterNo  = '" + MaterNo + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "26-99")  //根据发号物料编码获取信息
            {
                sSQL = " select MaterNo,MaterName,Model,BatchNum from T_SerielMater where CompanyNo = '" + sComp + "' and MaterNo  = '" + MaterNo + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
            }
            else if (sFlag == "80-1")
            {
                sSQL = " SELECT MaterNo,MaterName,Stock FROM T_SampleInfo where MaterNo  = '" + MaterNo + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
            }


            return sdt;
        }

        //新增，修改物料信息
        public static string InsertOrUpdateMater(string No, string Name, string PN, string Spec, string wllb, string Kd, string Un, string Bt, string Ft, string lx, string cl, string cc, string xz, string na, string fov, string cg, string gq, string jj,
            string dm, string pt, string hd, string zj, string tx, string jk, string mtf, string qx, string gb, string bl, string bs, string xn, string gz, string rs, string sm, string date, string spl, string prd, string jz, string mz,
            string gl, string pldy, string tz, string C1, string C2, string C3, string C4, string C5, string C6, string C7, string C8, string C9, string C10, string Remark,string IP, string sComp, string sInMan, string sFlag)
        {
            //  No   Name   Spec   Un 单位    Kd  物料类别   Bt  大类  Ft  分类   lx 类型(Type)  cl  材料(Material)  cc 尺寸(Size)  xz  形状(Shape)   na  NA(F#)  fov   FOV  cg  传感器格式  gq  光圈
            //  jj     焦距   dm   -- 镀膜   pt   平坦度(Flatness)  波长范围   --- 镜头的   hd  厚度(Thickness)   (传感器)分辨率   --- 镜头的  
            // zj   光束直径  tx   图像大小   jk  接口  mtf MTF  qx   畸变  gb   光斑尺寸  bl   倍率  bs   放大倍数  xn   性能  gz    工作距离  rs  入射光直径  sm    扫描角度 rmk 备注

            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sstr = string.Empty;

            //记录日志的参数
            string Message = string.Empty;      //日志信息
            string LogType = string.Empty;      //日志类型
            string NewValue = string.Empty;     //新数据
            string OldValue = string.Empty;     //旧数据
            string ModuleName = string.Empty;   //操作模块
            string TableName = "T_MaterInfo";    //表名
            string Condition = "MaterNo='" + No + "'";    //查询条件
            string Column = "*";       //列名

            if (sFlag == "1") // 新增 还没用的字段：LastOutNum,MaxNum,MinNum,NewInDate,NewInNum,Num,PurchCircle,PurchPrice,StdPack,
            {
                sSQL = " insert into T_MaterInfo(MaterNo,MaterVer,MaterName,PName,MaterSpec,MaterCategory,MaterUnit,MaterKind,BigType,FillMaterKind,MaterType,ABCKind,MaterXH,Coating,TUnit,MLength,MThickness,MWidth,StockPlace,StockLacation,HUnit,Mount,Distortion," +
                       " LOSHFlag,SpecialProtection,MHeight,Diameter,BatchNo,LWUnit,BatchMg,InspectMg,EfftMg,KaifengMg,TechNo,LastOutDate,SuplyNo,PrdLine,jz,mz,gl,pldy,tz,JSNo,PartName,PartSpec,UseLDate,CMIITID,CSize,Note,SYJJ,Store,Transp,InMan,Remark,CompanyNo)  " +
                       " values('" + No + "','','" + Name + "','" + PN + "','" + Spec + "','" + wllb + "','" + Un + "','" + Kd + "','" + Bt + "', '" + Ft + "','" + lx + "','" + cl + "','" + cc + "','" + dm + "','" + xz + "','" + jj + "','" + hd + "','" + pt + "'," +
                       " '" + cg + "','" + gq + "', '" + fov + "','" + jk + "','" + qx + "','" + na + "','" + xn + "','" + gz + "','" + rs + "','" + sm + "','" + gb + "','" + bs + "','" + zj + "','" + tx + "','" + bl + "','" + mtf + "','" + date + "'," +
                       " '" + spl + "','" + prd + "','" + jz + "','" + mz + "','" + gl + "','" + pldy + "','" + tz + "','" + C1 + "','" + C2 + "','" + C3 + "','" + C4 + "','" + C5 + "','" + C6 + "','" + C7 + "','" + C8 + "','" + C9 + "','" + C10 + "','" + sInMan + "','" + Remark + "','" + sComp + "') ";

                Message = "添加(" + No + ")" + Name + "物料信息。";
                LogType = "添加";
                ModuleName = "基础数据>物料信息";
            }
            else if (sFlag == "2")  // 修改
            {
                sSQL = " update  T_MaterInfo set MaterName='" + Name + "',PName='" + PN + "',MaterSpec='" + Spec + "',MaterCategory='" + wllb + "',MaterUnit='" + Un + "',MaterKind='" + Kd + "',BigType='" + Bt + "',FillMaterKind='" + Ft + "',MaterType='" + lx + "',ABCKind='" + cl + "',MaterXH='" + cc + "', " +
                       " Coating='" + dm + "',TUnit='" + xz + "', MLength='" + jj + "',MThickness='" + hd + "',MWidth='" + pt + "',StockPlace='" + cg + "',StockLacation='" + gq + "',HUnit='" + fov + "',Mount='" + jk + "',Distortion='" + qx + "', LOSHFlag='" + na + "'," +
                       " SpecialProtection='" + xn + "',MHeight='" + gz + "',Diameter='" + rs + "',BatchNo='" + sm + "',LWUnit='" + gb + "',BatchMg='" + bs + "',InspectMg='" + zj + "',EfftMg='" + tx + "',KaifengMg='" + bl + "',TechNo='" + mtf + "',LastOutDate='" + date + "', " +
                       " SuplyNo='" + spl + "',PrdLine='" + prd + "',jz='" + jz + "',mz='" + mz + "',gl='" + gl + "',pldy='" + pldy + "',tz='" + tz + "',JSNo='" + C1 + "',PartName='" + C2 + "',PartSpec='" + C3 + "',UseLDate='" + C4 + "',CMIITID='" + C5 + "',CSize='" + C6 + "', " +
                       " Note='" + C7 + "',SYJJ='" + C8 + "',Store='" + C9 + "',Transp='" + C10 + "',Remark='" + Remark + "' " +
                       " where MaterNo='" + No + "' ";

                Message = "修改(" + No + ")" + Name + "物料信息。";
                LogType = "修改";
                ModuleName = "基础数据>物料信息";
            }
            else if (sFlag == "3")  // 删除
            {
                sSQL = " delete  T_MaterInfo  where MaterNo='" + No + "' ";

                Message = "删除(" + No + ")" + Name + "物料信息。";
                LogType = "删除";
                ModuleName = "基础数据>物料信息";
            }

            List<string> links = new List<string>();
            links.Add(sSQL);

            try
            {
                //DBHelper.ExecuteCommand(sSQL);
                //删除、修改操作需要获取操作前的数据
                if (LogType == "删除" || LogType == "修改")
                {
                    OldValue = LogHelper.GetValue(TableName, Condition, Column);
                }

                iFlag = DBHelper.ExecuteSqlTranStr(links);

                //新增、修改操作需要获取操作后的数据
                if (LogType == "添加" || LogType == "修改")
                {
                    NewValue = LogHelper.GetValue(TableName, Condition, Column);
                }

                //记录操作日志
                if (!iFlag.Contains("Err"))
                {
                    LogHelper.LogInfo(LogType, Message, IP, sComp, sInMan, ModuleName, NewValue, OldValue);
                }
                //记录错误日志
                else
                {
                    Message = Message + "法生错误，错误原因：" + iFlag;
                    sSQL = String.Join("/", links.ToArray());
                    LogHelper.LogError(LogType, Message, IP, sComp, sInMan, ModuleName, sSQL);
                }
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }




        /// <summary>
        /// 获取可供物料
        /// </summary>
        /// <param name="MaterNo"></param>
        /// <param name="MaterName"></param>
        /// <param name="MaterSpec"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetCustMaterInfo(string MaterNo, string MaterName, string MaterSpec, string sStock, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string sSt = string.Empty;


            if (sFlag == "54-1")  // 只显示列表
            {
                sSQL = " SELECT MaterNo,MaterName FROM T_MaterInfo where CompanyNo = '" + sComp + "' and MaterNo  like '" + MaterNo + "%' and MaterName like '" + MaterName + "%'  order by CustName ";
            }
            else if (sFlag == "54-2")  //  
            {
                if ((sStock == "全部") || (string.IsNullOrEmpty(sStock)))
                {
                    sSt = "";
                }
                else if (sStock == "大于0")
                {
                    sSt = " and Stock > 0 ";
                }
                else
                {
                    sSt = " and Stock <= 0 ";
                }


                sSQL = " SELECT  *,CONVERT(char(16),InDate,120) as CInDate  FROM T_CustMater where CompanyNo = '" + sComp + "' " +
                       " and MaterNo  like '" + MaterNo + "%' and MaterName like '%" + MaterName + "%' and MaterSpec like '%" + MaterSpec + "%' " + sSt;
            }

            DataTable sdt = DBHelper.GetDataTable(sSQL);

            return sdt;
        }

        /// <summary>
        /// 维护客供料信息
        /// </summary>
        /// <param name="MaterNo"></param>
        /// <param name="MName"></param>
        /// <param name="Spec"></param>
        /// <param name="Stock"></param>
        /// <param name="Remark"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string AddEditCustMater(string MaterNo, string MName, string Spec, string Stock, string Remark, string sComp, string sInMan, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sstr = string.Empty;


            if (sFlag == "1") // 新增
            {
                sSQL = " insert into T_CustMater(MaterNo,MaterName,MaterSpec,Stock,InMan,Remark,CompanyNo)  " +
                       " values('" + MaterNo + "','" + MName + "','" + Spec + "','" + Stock + "','" + sInMan + "','" + Remark + "','" + sComp + "') ";
            }
            else // 修改
            {
                sSQL = " update  T_CustMater set MaterName='" + MName + "',MaterSpec='" + Spec + "',Stock='" + Stock + "',Remark='" + Remark + "' where MaterNo='" + MaterNo + "' ";
            }

            List<string> links = new List<string>();
            links.Add(sSQL);

            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }

        /// <summary>
        /// 删除客供物料
        /// </summary>
        /// <param name="sNo"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string DelCustMater(string sNo, string sComp, string sInMan, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sstr = string.Empty;

            sSQL = " delete T_CustMater where MaterNo='" + sNo + "' and  CompanyNo = '" + sComp + "' ";

            // 保存操作日志

            List<string> links = new List<string>();
            links.Add(sSQL);

            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }

        /// <summary>
        ///  获取供应商信息
        /// </summary>
        /// <param name="SupplierNo"></param>
        /// <param name="SupplierEn"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetSupplierInfo(string SupplierNo, string SupplierEn, string sTJ, string sFS, int Row, int num, string LoginMan, string sComp, string sFlag)
        {
            string sSQL = string.Empty;

            DataTable sdt = new DataTable();

            if (sFlag == "41")  // 查询所有供应商信息
            {

                SqlParameter[] parameters = {
                    new SqlParameter("@lnNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnTJ", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnFS", SqlDbType.NVarChar,200),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
                parameters[0].Value = SupplierNo;
                parameters[1].Value = SupplierEn;
                parameters[2].Value = sTJ;
                parameters[3].Value = sFS;
                parameters[4].Value = Row;
                parameters[5].Value = num;
                parameters[6].Value = LoginMan;
                parameters[7].Value = "";

                DataSet DS = DBHelper.RunProcedureForDS("P_GetSupplierForPage", parameters);
                sdt = DS.Tables[0];

                return sdt;
            }
            else if (sFlag == "41-1") // 只显示列表
            {
                sSQL = " SELECT SupplierNo,SupplierEn FROM T_SupplierInfo where CompanyNo = '" + sComp + "' and SupplierNo  like '" + SupplierNo + "%' and SupplierEn like '" + SupplierEn + "%'  and Status='启用' order by SupplierEn ";
            }
            else if (sFlag == "41-99") // 根据供应商编码获取供应商信息-单个记录
            {
                sSQL = " SELECT 0 as NumCount,* FROM T_SupplierInfo  where CompanyNo = '" + sComp + "' and SupplierNo = '" + SupplierNo + "' and Status='启用' ";
            }
            else if (sFlag == "43")  // 获取供应商验厂信息
            {
                sSQL = " SELECT 0 as NumCount,a.*,b.InchangeMan,b.Phone,b.Addr,b.Fax,b.Email,convert(char(16),a.InDate,120) as InDate2,case when a.NPGTime<CONVERT(char(10),GETDATE(),120) and a.NPGTime<>'' then 0 else 1 end as Flag " +
                       " FROM T_QualityReport a left join T_SupplierInfo b on a.SupplierNo=b.SupplierNo where a.CompanyNo = '" + sComp + "' and a.SupplierNo like '" + SupplierNo + "%' and a.SupplierEn like '%" + SupplierEn + "%'  ";
            }


            sdt = DBHelper.GetDataTable(sSQL);

            return sdt;
        }

        /// <summary>
        /// 获取供应商编码
        /// </summary>
        /// <param name="sCode"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string GetSupplierNo(string sGrade, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string sNo = string.Empty;
            string sls = string.Empty;
            int iMax = 0;


            string sMaxNo = DBHelper.GetMaxNo("T_SupplierInfo where Grade='" + sGrade + "' and  CompanyNo = '" + sComp + "'", "SupplierNo");//
            if (sMaxNo == "")
            {
                sNo = sGrade + "01";
            }
            else
            {
                sls = sMaxNo.Substring(1, 2);
                iMax = int.Parse(sls) + 1;

                if (iMax > 99)
                {
                    sNo = "OVER";
                }
                else if (iMax > 9)
                {
                    sNo = sGrade + iMax.ToString();
                }
                else
                {
                    sNo = sGrade + "0" + iMax.ToString();
                }
            }


            return sNo;
        }



        /// <summary>
        /// 维护供应商信息
        /// </summary>
        /// <param name="sNo"></param>
        /// <param name="sName"></param>
        /// <param name="sDesc"></param>
        /// <param name="sEn"></param>
        /// <param name="sKind"></param>
        /// <param name="sGrade"></param>
        /// <param name="sCode"></param>
        /// <param name="sPItem"></param>
        /// <param name="sPType"></param>
        /// <param name="sSL"></param>
        /// <param name="sBZ"></param>
        /// <param name="sCode"></param>
        /// <param name="sIndust"></param>
        /// <param name="sSF"></param>
        /// <param name="sCity"></param>
        /// <param name="sInCH"></param>
        /// <param name="sPhone"></param>
        /// <param name="sFax"></param>
        /// <param name="sAddr"></param>
        /// <param name="sWebsite"></param>
        /// <param name="sRemark"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string InsertOrUpdateSupplier(string sNo, string sName, string sDesc, string sEn, string sKind, string sGrade, string sCode, string sPItem, string sPType, string sSL, string sBZ, string sIndust, string sSF, string sCity, string sInCH, string sPhone, string sFax, string sAddr, string sEmail, string sWebsite, string sRemark, string sComp, string sInMan, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sstr = string.Empty;
            string sQRNo = string.Empty;
            string sls = string.Empty;
            int iMax = 0;

            if (sFlag == "1") // 新增供应商
            {
                sSQL = " insert into T_SupplierInfo(SupplierNo,SupplierName,SupplierNameTwo,SupplierEn,SupplierKind,Grade,Code,PaymentTerms,PaymentType,TaxRate,Currency,Industry,CProvince,City,InchangeMan,Phone,Fax,Addr,Email,Website,CompanyNo,InMan,Remark)  " +
                    " values('" + sNo + "','" + sName + "','" + sDesc + "','" + sEn + "','" + sKind + "','" + sGrade + "','" + sCode + "','" + sPItem + "','" + sPType + "','" + sSL + "','" + sBZ + "','" + sIndust + "','" + sSF + "','" + sCity + "','" + sInCH + "','" + sPhone + "','" + sFax + "','" + sAddr + "','" + sEmail + "','" + sWebsite + "','" + sComp + "','" + sInMan + "','" + sRemark + "')";

            }
            else if (sFlag == "2")  // 修改供应商
            {
                sSQL = " update T_SupplierInfo set SupplierName='" + sName + "',SupplierNameTwo='" + sDesc + "',SupplierEn='" + sEn + "',SupplierKind='" + sKind + "',Grade='" + sGrade + "',Code='" + sCode + "',PaymentTerms='" + sPItem + "',PaymentType='" + sPType + "',TaxRate='" + sSL + "',Currency='" + sBZ + "',Industry='" + sIndust + "',CProvince='" + sSF + "', " +
                       " City='" + sCity + "',InchangeMan='" + sInCH + "', Phone='" + sPhone + "',Fax='" + sFax + "',Addr='" + sAddr + "',Email='" + sEmail + "',Website='" + sWebsite + "',Remark='" + sRemark + "'  " +
                       " where SupplierNo = '" + sNo + "' ";
            }
            else if (sFlag == "3")  // 删除  20210525 修改为 禁用 
            {
                sSQL = " update T_SupplierInfo set Status='禁用' where SupplierNo = '" + sNo + "' and  CompanyNo = '" + sComp + "' ";
            }
            else if (sFlag == "4")  // 启用  
            {
                sSQL = " update T_SupplierInfo set Status='启用' where SupplierNo = '" + sNo + "' and  CompanyNo = '" + sComp + "' ";
            }
            else if (sFlag == "6")  //插入供应商验厂信息
            {
                string sMaxNo = DBHelper.GetMaxNo("T_QualityReport where CompanyNo = '" + sComp + "' and Convert(char(10),InDate,120)=Convert(char(10),getdate(),120) ", "QRNo");//
                if (sMaxNo == "")
                {
                    sQRNo = "QR" + CreateAllNo.CreateBillNo(2, 3) + "1";
                }
                else
                {
                    sls = sMaxNo.Substring(8, 4); //  QR2102060023
                    iMax = int.Parse(sls) + 1;
                    sQRNo = "QR" + CreateAllNo.CreateBillNo(2, 4 - iMax.ToString().Length) + iMax.ToString();
                }

                // TJ: sBJ, FS: sNum, SL: sCoreP, BZ: sProductT, Code: sFinish, CMan: sPolishing, Phone: sTestD, Fax: sGoniometer, Addr: sCapacity, Email: sDistribute,sDesc:TIME
                sSQL = " insert into T_QualityReport(QRNo,SupplierNo,SupplierEn,backG,Num,CoreProduct,ProductType,Finish,Polishing,TestDevice,Goniometer,Capacity,Distribute,NPGTime,CompanyNo,InMan,Remark)  " +
                    " values('" + sQRNo + "','" + sNo + "','" + sEn + "','" + sPItem + "','" + sPType + "','" + sSL + "','" + sBZ + "','" + sCode + "','" + sInCH + "','" + sPhone + "','" + sFax + "','" + sAddr + "','" + sEmail + "','" + sDesc + "','" + sComp + "','" + sInMan + "','" + sRemark + "')";
                sSQLs = "update T_SupplierInfo set LastContactTime='" + sDesc + "' where SupplierNo = '" + sNo + "' "; // 更新最后评估时间
            }
            else if (sFlag == "7")  //修改供应商验厂信息
            {
                sSQL = " update T_QualityReport set backG='" + sPItem + "',Num='" + sPType + "',CoreProduct='" + sSL + "',ProductType='" + sBZ + "',Finish='" + sCode + "',Polishing='" + sInCH + "',TestDevice='" + sPhone + "',Goniometer='" + sFax + "',Capacity='" + sAddr + "', " +
                       " Distribute='" + sEmail + "',NPGTime='" + sDesc + "',Remark='" + sRemark + "' where QRNo = '" + sKind + "' ";
                sSQLs = "update T_SupplierInfo set LastContactTime='" + sDesc + "' where SupplierNo = '" + sNo + "' "; // 更新最后评估时间
            }
            else if (sFlag == "8")  // 删除供应商验厂信息
            {
                sSQL = " delete T_QualityReport where QRNo = '" + sKind + "' and  CompanyNo = '" + sComp + "' ";

                // 更新最后评估时间 -- 删除本次的记录后，查询最后一次的
                sSQLs = "update T_SupplierInfo set LastContactTime=isnull((select top 1 NPGTime from T_QualityReport where SupplierNo= '" + sNo + "' and  CompanyNo = '" + sComp + "' order by InDate desc),'')  where SupplierNo= '" + sNo + "' ";
            }



            List<string> links = new List<string>();
            links.Add(sSQL);
            links.Add(sSQLs);

            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }


        private static int length(int iMax)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// 返回BOM结构的树形
        /// </summary>
        /// <param name="FNo"></param>
        /// <param name="FName"></param>
        /// <param name="CNo"></param>
        /// <param name="CName"></param>
        /// <param name="GX"></param>
        /// <param name="Row"></param>
        /// <param name="num"></param>
        /// <param name="Comp"></param>
        /// <returns></returns>
        public static DataTable GetBOMInfo(string FNo, string FName, string CNo, string CName, string GX, string InStock, string sNo, string sA, string sB, string sC, string sD, int Row, int num, string Comp, string InMan, string Flag)
        {
            DataTable sdt = new DataTable();

            SqlParameter[] parameters = {
                    new SqlParameter("@lnFNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnFName", SqlDbType.NVarChar,200),
                    new SqlParameter("@lnCNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnCName", SqlDbType.NVarChar,200),
                    new SqlParameter("@lnGX", SqlDbType.NVarChar,60),
                    new SqlParameter("@lnSotck", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnNo", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnComp", SqlDbType.NVarChar,10),
                    new SqlParameter("@lnMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnFlag", SqlDbType.NVarChar,10),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
            parameters[0].Value = FNo;
            parameters[1].Value = FName;
            parameters[2].Value = CNo;
            parameters[3].Value = CName;
            parameters[4].Value = GX;
            parameters[5].Value = InStock;
            parameters[6].Value = sNo;
            parameters[7].Value = sA;
            parameters[8].Value = sB;
            parameters[9].Value = sC;
            parameters[10].Value = sD;
            parameters[11].Value = Row;
            parameters[12].Value = num;
            parameters[13].Value = Comp;
            parameters[14].Value = InMan;
            parameters[15].Value = Flag;
            parameters[16].Value = "";

            DataSet DS = DBHelper.RunProcedureForDS("p_CreateBomForTree", parameters);
            sdt = DS.Tables[0];

            return sdt;
        }


        // 返回工作中心树形结构
        public static DataTable GetWorkCenterInfo(string FNo, string FName, string CNo, string CName, string GX, int Row, int num, string Comp)
        {
            DataTable sdt = new DataTable();

            SqlParameter[] parameters = {
                    new SqlParameter("@lnFNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnFName", SqlDbType.NVarChar,200),
                    new SqlParameter("@lnCNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnCName", SqlDbType.NVarChar,200),
                    new SqlParameter("@lnGX", SqlDbType.NVarChar,60),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnComp", SqlDbType.NVarChar,30),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
            parameters[0].Value = FNo;
            parameters[1].Value = FName;
            parameters[2].Value = CNo;
            parameters[3].Value = CName;
            parameters[4].Value = GX;
            parameters[5].Value = Row;
            parameters[6].Value = num;
            parameters[7].Value = Comp;
            parameters[8].Value = "";

            DataSet DS = DBHelper.RunProcedureForDS("p_CreateWorkCenterForTree", parameters);
            sdt = DS.Tables[0];

            return sdt;
        }


        /// <summary>
        /// 新增，修改，删除BOM信息
        /// </summary>
        /// <param name="sFNo"></param>
        /// <param name="sFName"></param>
        /// <param name="sNewFNo"></param>
        /// <param name="sNewFName"></param>
        /// <param name="sCNo"></param>
        /// <param name="sCName"></param>
        /// <param name="sUseNum"></param>
        /// <param name="sGX"></param>
        /// <param name="sRemark"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string InsertOrUpdateBOM(string sFNo, string sFName, string sNewFNo, string sNewFName, string sCNo, string sCName, string sUseNum, string sGX, string sPack, string A, string B, string C, string D, string E, string F, string sRemark, string sComp, string sInMan, string sFlag, string IP)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sSQL1 = string.Empty;
            string sstr = string.Empty;
            string sNo = string.Empty;
            string sName = string.Empty;
            string sQRNo = string.Empty;
            string sls = string.Empty;

            //记录日志的参数
            string Message = string.Empty;      //日志信息
            string LogType = string.Empty;      //日志类型
            string NewValue = string.Empty;     //新数据
            string OldValue = string.Empty;     //旧数据
            string ModuleName = string.Empty;   //操作模块
            string TableName = string.Empty;    //表名
            string Condition = string.Empty;    //查询条件
            string Column = string.Empty;       //列名

            if (sFlag == "1") // 新增BOM
            {
                if (sGX.Length > 3) // 说明选择了工序 (05)包装   // 截取  S1: sTPNo, S2: sLVer, S3: sKind, S4: sTPName, S5: sLong, S6: sWide, S7: sProcNo, UseFlag: sCAuto,
                {
                    sNo = sGX.Substring(1, sGX.IndexOf(")") - 1);
                    sName = sGX.Substring(sGX.IndexOf(")") + 1, sGX.Length - sGX.IndexOf(")") - 1);
                }

                sSQL = " insert into T_BOMInfo(FMaterNo,FMaterVer,FMaterName,CMaterNo,CMaterVer,CMaterName,UseNum,ProcedureNo,ProcedureName,Pack,ControlWay,VirtualFlag,CompanyNo,InMan,Remark)  " +
                       " values('" + sFNo + "','1.0','" + sFName + "','" + sCNo + "','1.0','" + sCName + "','" + sUseNum + "','" + sNo + "','" + sName + "','" + sPack + "','" + A + "','" + B + "','" + sComp + "','" + sInMan + "','" + sRemark + "')";

                // 单独更新产品流程是否典型产品，针对产品编码更新  
                sSQL1 = " update T_BOMInfo set TechNo='" + C + "',Model='" + D + "',ClassFlow='" + E + "' where FMaterNo='" + sFNo + "' ";

                Message = "添加物料信息，父物料：" + sFNo + "，子物料：" + sCNo + "。";
                LogType = "添加";
                ModuleName = F;
                TableName = "T_BOMInfo";
                Condition = "FMaterNo = '" + sFNo + "' and CMaterNo = '" + sCNo + "' ";
                Column = "*";
            }
            else if (sFlag == "2")  // 修改BOM
            {
                if (sGX.Length > 3) // 说明选择了工序 (05)包装   // 截取  S1: sTPNo, S2: sLVer, S3: sKind, S4: sTPName, S5: sLong, S6: sWide, S7: sProcNo, UseFlag: sCAuto,
                {
                    sNo = sGX.Substring(1, sGX.IndexOf(")") - 1);
                    sName = sGX.Substring(sGX.IndexOf(")") + 1, sGX.Length - sGX.IndexOf(")") - 1);
                }

                sSQL = " update T_BOMInfo set FMaterNo='" + sNewFNo + "',FMaterName='" + sNewFName + "',UseNum='" + sUseNum + "',ProcedureNo='" + sNo + "',ProcedureName='" + sName + "',Pack='" + sPack + "',ControlWay='" + A + "',VirtualFlag='" + B + "',Remark='" + sRemark + "' " +
                       " where FMaterNo = '" + sFNo + "' and CMaterNo = '" + sCNo + "' ";

                // 单独更新产品流程是否典型产品，针对产品编码更新  
                sSQL1 = " update T_BOMInfo set TechNo='" + C + "',Model='" + D + "',ClassFlow='" + E + "' where FMaterNo='" + sFNo + "' ";

                sSQLs = "update T_BOMInfo set UseNum='" + sUseNum + "',ProcedureNo='" + sNo + "',ProcedureName='" + sName + "',Pack='" + sPack + "',ControlWay='" + A + "' where FMaterNo='" + sFNo + "' and AMMaterNo='" + sCNo + "'";

                Message = "修改物料信息，父物料：" + sFNo + "，子物料：" + sCNo + "。";
                LogType = "修改";
                ModuleName = F;
                TableName = "T_BOMInfo";
                Condition = "FMaterNo = '" + sFNo + "' and CMaterNo = '" + sCNo + "' ";
                Column = "*";
            }
            else if (sFlag == "3")  // 删除
            {
                sSQL = " delete T_BOMInfo where FMaterNo = '" + sFNo + "' and CMaterNo = '" + sCNo + "' ";

                Message = "删除物料信息，父物料：" + sFNo + "，子物料：" + sCNo + "。";
                LogType = "删除";
                ModuleName = F;
                TableName = "T_BOMInfo";
                Condition = "FMaterNo = '" + sFNo + "' and CMaterNo = '" + sCNo + "' ";
                Column = "*";
            }
            else if (sFlag == "4")  // 保存替代料   sFNo:替代料本身   sCNo：替代料
            {
                // 找到被替代料的父零件
                string sTNo = A;
                //sSQL = " select FMaterNo from T_BOMInfo where CMaterNo='" + sFNo + "' and CompanyNo= '" + sComp + "' ";
                //DataTable dtc5 = DBHelper.GetDataTable(sSQL);
                //if (dtc5.Rows.Count > 0){
                //    sTNo = dtc5.Rows[0]["FMaterNo"].ToString();
                //}

                sSQL = " select FMaterNo from T_BOMInfo where FMaterNo='" + sTNo + "' and CMaterNo='" + sCNo + "' and CompanyNo= '" + sComp + "' ";
                DataTable dtc6 = DBHelper.GetDataTable(sSQL);
                if (dtc6.Rows.Count == 0)  // 还没有替代关系，才插入
                {
                    sSQL = " insert into T_BOMInfo(FMaterNo,FMaterVer,FMaterName,CMaterNo,CMaterVer,CMaterName,UseNum,ProcedureNo,ProcedureName,Pack,AMFlag,AMMaterNo,ControlWay,VirtualFlag,TechNo,Model,ClassFlow,CompanyNo,InMan,Remark)  " +
                           " select FMaterNo,FMaterVer,FMaterName,'" + sCNo + "','1.0','" + sCName + "',UseNum,ProcedureNo,ProcedureName,Pack,'是','" + sFNo + "',ControlWay,VirtualFlag,TechNo,Model,ClassFlow,CompanyNo,InMan,Remark " +
                           " from T_BOMInfo where FMaterNo='" + sTNo + "' and CMaterNo='" + sFNo + "' ";

                    sSQLs = " insert into T_MaterReplace(TMaterNo,TMaterName,FMaterNo,FMaterName,MaterNo,MaterName,Status,InMan,CompanyNo,Remark)  " +
                            " select FMaterNo,FMaterName,CMaterNo,CMaterName,'" + sCNo + "','" + sCName + "','在用','" + sInMan + "','" + sComp + "','' " +
                            " from T_BOMInfo where FMaterNo='" + sTNo + "' and CMaterNo='" + sFNo + "' ";

                    Message = "添加替代料信息，父物料：" + sTNo + "，替代料：" + sCNo + "。";
                    LogType = "添加";
                    ModuleName = F;
                    TableName = "T_BOMInfo";
                    Condition = "FMaterNo = '" + sFNo + "' and CMaterNo = '" + sCNo + "' ";
                    Column = "*";
                }
                else
                {
                    sSQL = "";
                }
            }
            else if (sFlag == "5")  // 删除替代料  sFNo:替代料本身   sCNo：替代料
            {
                // 找到顶层编码
                string sTDNo = string.Empty;
                sSQL = " select FMaterNo,AMMaterNo from T_BOMInfo where FMaterNo='" + sFNo + "' and CMaterNo = '" + sCNo + "' and CompanyNo= '" + sComp + "' ";
                DataTable dtc5 = DBHelper.GetDataTable(sSQL);
                if (dtc5.Rows.Count > 0)
                {
                    sTDNo = dtc5.Rows[0]["AMMaterNo"].ToString();  //被替代的物料 
                }

                sSQL = " delete T_BOMInfo where FMaterNo = '" + sFNo + "' and CMaterNo = '" + sCNo + "' ";

                sSQLs = " delete T_MaterReplace where TMaterNo= '" + sFNo + "' and FMaterNo = '" + sTDNo + "' and MaterNo = '" + sCNo + "' and CompanyNo= '" + sComp + "' ";

                Message = "删除替代物料信息，父物料：" + sFNo + "，替代料：" + sCNo + "。";
                LogType = "删除";
                ModuleName = F;
                TableName = "T_BOMInfo";
                Condition = "FMaterNo = '" + sFNo + "' and CMaterNo = '" + sCNo + "' ";
                Column = "*";
            }



            List<string> links = new List<string>();
            links.Add(sSQL);
            links.Add(sSQLs);
            links.Add(sSQL1);

            try
            {
                //删除、修改操作需要获取操作前的数据
                if (LogType == "删除" || LogType == "修改")
                {
                    OldValue = LogHelper.GetValue(TableName, Condition, Column);
                }

                //DBHelper.ExecuteCommand(sSQL);
                iFlag = DBHelper.ExecuteSqlTranStr(links);

                //新增、修改操作需要获取操作后的数据
                if (LogType == "添加" || LogType == "修改")
                {
                    NewValue = LogHelper.GetValue(TableName, Condition, Column);
                }

                //记录操作日志
                if (!iFlag.Contains("Err"))
                {
                    LogHelper.LogInfo(LogType, Message, IP, sComp, sInMan, ModuleName, NewValue, OldValue);
                }
                //记录错误日志
                else
                {
                    Message = Message + "法生错误，错误原因：" + iFlag;
                    sSQL = String.Join("/", links.ToArray());
                    LogHelper.LogError(LogType, Message, IP, sComp, sInMan, ModuleName, sSQL);
                }
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }


        /// <summary>
        /// 新增，修改，删除客户信息
        /// </summary>
        /// <param name="sNo"></param>
        /// <param name="sName"></param>
        /// <param name="sEn"></param>
        /// <param name="sCode"></param>
        /// <param name="sPItem"></param>
        /// <param name="sPType"></param>
        /// <param name="sSL"></param>
        /// <param name="sBZ"></param>
        /// <param name="sInCH"></param>
        /// <param name="sPhone"></param>
        /// <param name="sFax"></param>
        /// <param name="sAddr"></param>
        /// <param name="sEmail"></param>
        /// <param name="sRemark"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string InsertOrUpdateCust(string sNo, string sName, string sEn, string sCode, string sPItem, string sPType, string sSL, string sBZ, string sInCH, string sPhone, string sFax, string sAddr, string sEmail, string sRemark, string sComp, string sInMan, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sstr = string.Empty;
            string sls = string.Empty;


            if (sFlag == "1") // 新增供应商
            {
                sSQL = " insert into T_CustInfo(CustNo,CustName,CustEn,QuDaoCode,PaymentTerms,PaymentType,TaxRate,Currency,InchangeMan,Phone,Fax,Addr,Email,CompanyNo,InMan,Remark)  " +
                    " values('" + sNo + "','" + sName + "','" + sEn + "','" + sCode + "','" + sPItem + "','" + sPType + "','" + sSL + "','" + sBZ + "','" + sInCH + "','" + sPhone + "','" + sFax + "','" + sAddr + "','" + sEmail + "','" + sComp + "','" + sInMan + "','" + sRemark + "')";

            }
            else if (sFlag == "2")  // 修改供应商
            {
                sSQL = " update T_CustInfo set CustName='" + sName + "',CustEn='" + sEn + "',QuDaoCode='" + sCode + "',PaymentTerms='" + sPItem + "',PaymentType='" + sPType + "',TaxRate='" + sSL + "',Currency='" + sBZ + "', " +
                       " InchangeMan='" + sInCH + "', Phone='" + sPhone + "',Fax='" + sFax + "',Addr='" + sAddr + "',Email='" + sEmail + "',Remark='" + sRemark + "'  " +
                       " where CustNo = '" + sNo + "' and  CompanyNo = '" + sComp + "' ";
            }
            else if (sFlag == "3")  // 删除
            {
                sSQL = " delete T_CustInfo where CustNo = '" + sNo + "' and  CompanyNo = '" + sComp + "' ";
            }



            List<string> links = new List<string>();
            links.Add(sSQL);
            links.Add(sSQLs);

            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }



        /// <summary>
        /// 获取厂商识别码信息
        /// </summary>
        /// <param name="MCode"></param>
        /// <param name="Center"></param>
        /// <param name="BD"></param>
        /// <param name="ED"></param>
        /// <param name="BED"></param>
        /// <param name="EED"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetMCodetInfo(string MCode, string Center, string BD, string ED, string BED, string EED, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string sSt = string.Empty;

            if (BD == "")
            {
                sSt = "";
            }
            else
            {
                sSt = " and PublishDate>= '" + BD + "' ";
            }

            if (ED == "")
            {
                sSt = sSt;
            }
            else
            {
                sSt = sSt + " and PublishDate<= '" + ED + "'   ";
            }

            if (BED == "")
            {
                sSt = sSt;
            }
            else
            {
                sSt = sSt + " and ExpiryDate>= '" + BED + "' ";
            }

            if (EED == "")
            {
                sSt = sSt;
            }
            else
            {
                sSt = sSt + " and ExpiryDate<= '" + EED + "' ";
            }

            sSQL = " SELECT  *,CONVERT(char(16),InDate,120) as CInDate  FROM T_MCode where CompanyNo = '" + sComp + "' " +
                   " and MCode  like '" + MCode + "%' and CodeCenter like '%" + Center + "%'" + sSt + " order by InDate desc ";

            DataTable sdt = DBHelper.GetDataTable(sSQL);

            return sdt;
        }


        /// <summary>
        /// 新增，修改，删除 厂商识别码
        /// </summary>
        /// <param name="MCode"></param>
        /// <param name="Center"></param>
        /// <param name="PD"></param>
        /// <param name="ED"></param>
        /// <param name="InCharge"></param>
        /// <param name="Remark"></param>
        /// <param name="sComp"></param>
        /// <param name="sInMan"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string OPMCodetInfo(string MCode, string Center, string MCodeType, string NowNum, string LNum, string PD, string ED, string InCharge, string Remark, string sComp, string sInMan, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sstr = string.Empty;


            if (sFlag == "1") // 新增
            {
                sSQL = " insert into T_MCode(MCode,CodeCenter,MCodeType,NowNum,LNum,PublishDate,ExpiryDate,InCharge,CompanyNo,InMan,Remark)  " +
                       " values('" + MCode + "','" + Center + "','" + MCodeType + "','" + NowNum + "','" + LNum + "','" + PD + "','" + ED + "','" + InCharge + "','" + sComp + "','" + sInMan + "','" + Remark + "') ";
            }
            else if (sFlag == "2") // 修改
            {
                sSQL = " update  T_MCode set CodeCenter='" + Center + "',MCodeType='" + MCodeType + "',NowNum='" + NowNum + "',LNum='" + LNum + "',PublishDate='" + PD + "',ExpiryDate='" + ED + "', " +
                       " InCharge='" + InCharge + "',Remark='" + Remark + "' where MCode='" + MCode + "' ";
            }
            else if (sFlag == "3") // 删除
            {
                sSQL = " delete  T_MCode  where MCode='" + MCode + "' ";
            }
            else if (sFlag == "4") // 禁用
            {
                sSQL = " update T_MCode set IsUse='否' where MCode='" + MCode + "' ";
            }
            else if (sFlag == "5") // 启用
            {
                sSQL = " update T_MCode set IsUse='是' where MCode='" + MCode + "' ";
            }


            List<string> links = new List<string>();
            links.Add(sSQL);

            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }

        /// <summary>
        /// 获取发号物料信息
        /// </summary>
        /// <param name="MNo"></param>
        /// <param name="MName"></param>
        /// <param name="Row"></param>
        /// <param name="num"></param>
        /// <param name="LoginMan"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetSerielMater(string PMNo, string PMName, string Model, string MNo, string MName, string A, string B, string C, string D, string BDate, string EDate, int Row, int num, string LoginMan, string sComp, string sFlag)
        {
            string sSQL = string.Empty;

            DataTable sdt = new DataTable();

            if ((sFlag == "21") || (sFlag == "27") || (sFlag == "28") || (sFlag == "29"))  // 21:查询发号的物料；28：产品对应标贴信息 29：标贴模板 27:已发放的DI
            {

                SqlParameter[] parameters = {
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnModel", SqlDbType.NVarChar,60),
                    new SqlParameter("@lnOrderNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnSerial", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,10),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
                parameters[0].Value = PMNo;
                parameters[1].Value = PMName;
                parameters[2].Value = Model;
                parameters[3].Value = MNo;
                parameters[4].Value = MName;
                parameters[5].Value = A;
                parameters[6].Value = B;
                parameters[7].Value = C;
                parameters[8].Value = D;
                parameters[9].Value = BDate;
                parameters[10].Value = EDate;
                parameters[11].Value = Row;
                parameters[12].Value = num;
                parameters[13].Value = LoginMan;
                parameters[14].Value = sFlag;
                parameters[15].Value = "";

                DataSet DS = DBHelper.RunProcedureForDS("P_GetSerielMaterForPage", parameters);
                sdt = DS.Tables[0];

                return sdt;
            }
            else if (sFlag == "21-1") // 预览序列号
            {
                SqlParameter[] parameters = {
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnOrderNo", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnOrderFlag", SqlDbType.NVarChar,10),
                    new SqlParameter("@lnNum", SqlDbType.NVarChar,10),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnFlag", SqlDbType.NVarChar,10),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
                parameters[0].Value = PMNo;
                parameters[1].Value = "";
                parameters[2].Value = "";
                parameters[3].Value = "1";  // 
                parameters[4].Value = A;  // 
                parameters[5].Value = B;  // 
                parameters[6].Value = C;  // 
                parameters[7].Value = LoginMan;
                parameters[8].Value = "1"; // 1 预览
                parameters[9].Value = "";

                DataSet DS = DBHelper.RunProcedureForDS("P_CreateSerialTwo", parameters);
                sdt = DS.Tables[0];

                return sdt;
            }
            else if (sFlag == "28-2") // 查询产品编码下的标贴
            {
                var sStr = string.Empty;
                if (PMNo != "")
                {
                    sStr = sStr + " MaterNo='" + PMNo + "' ";
                }
                if (MNo != "")
                {// 存放序列号了
                    if (sStr == "")
                    {
                        sStr = sStr + " SerielNo='" + MNo + "' ";
                    }
                    else
                    {
                        sStr = sStr + " and SerielNo='" + MNo + "' ";
                    }

                }
                if (MName != "")
                {
                    if (sStr == "")
                    {
                        sStr = sStr + " OrderNo='" + MName + "' ";
                    }
                    else
                    {
                        sStr = sStr + " and OrderNo='" + MName + "' ";
                    }
                }
                if (sStr != "")
                {
                    sSQL = " select ROW_NUMBER() over(order by PMaterNo) Rn,a.PMaterNo,PMaterName,a.MaterNo,a.TPNo,a.TPVer,a.TemplateName,b.PathName,b.TPPath " +
                           " from T_ProductAndLabel a left join T_LabelTemplate b on a.TPNo=b.TPNo and a.TPVer=b.TPVer " +
                           " where a.CompanyNo='" + sComp + "' and a.Status='启用' and a.PMaterNo = (select top 1 MaterNo from T_SerielInfo where " + sStr + ") ";
                }
            }
            else if (sFlag == "28-3") // 查询产品编码下的标贴
            {
                sSQL = " select * from T_LabelTemplate where CompanyNo='" + sComp + "' and Status='启用' ";
            }


            sdt = DBHelper.GetDataTable(sSQL);

            return sdt;
        }


        // 操作发号物料表的数据
        public static string OPSerielMater(string MNo, string MName, string Model, string S1, string S2, string S3, string S4, string S5, string S6, string S7, string IValue, string NValue, string UseFlag, string BNum, string sComp, string sInMan, string Remark, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sstr = string.Empty;


            if (sFlag == "1") // 新增
            {
                sSQL = " insert into T_SerielMater(SMNo,DeptNo,MaterNo,MaterVer,MaterName,Model,SerielRule,SectionOne,SectionTwo,SectionThr,SectionFour,SectionFive,SectionSix,SectionSev,InitialValue,NowValue,BatchNum,OrderBatch,CompanyNo,InMan,Remark)  " +
                       " values('SM0001','','" + MNo + "','','" + MName + "','" + Model + "','','" + S1 + "','" + S2 + "','" + S3 + "','" + S4 + "','" + S5 + "','" + S6 + "','" + S7 + "','" + IValue + "','" + NValue + "','" + BNum + "','" + UseFlag + "','" + sComp + "','" + sInMan + "','" + Remark + "') ";
            }
            else if (sFlag == "2") // 修改
            {
                sSQL = " update  T_SerielMater set MaterName='" + MName + "',Model='" + Model + "',SectionOne='" + S1 + "',SectionTwo='" + S2 + "',SectionThr='" + S3 + "',SectionFour='" + S4 + "',SectionFive='" + S5 + "',SectionSix='" + S6 + "', " +
                       " SectionSev='" + S7 + "',InitialValue='" + IValue + "',NowValue='" + NValue + "',BatchNum='" + BNum + "',OrderBatch='" + UseFlag + "' where CompanyNo='" + sComp + "' and MaterNo='" + MNo + "' ";
            }
            else if (sFlag == "3") // 删除
            {
                sSQL = " delete  T_SerielMater where CompanyNo='" + sComp + "' and MaterNo='" + MNo + "' ";
            }
            else if (sFlag == "4") // 禁用
            {
                sSQL = " update T_SerielMater set UseFlag='否' where CompanyNo='" + sComp + "' and MaterNo='" + MNo + "' ";
            }
            else if (sFlag == "5") // 启用
            {
                sSQL = " update T_SerielMater set UseFlag='是' where CompanyNo='" + sComp + "' and MaterNo='" + MNo + "' ";
            }


            List<string> links = new List<string>();
            links.Add(sSQL);

            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }

        // 获取已发放的序列号信息,序列号，DI码
        public static DataTable GetSerielInfo(string MNo, string Model, string Serial, string OrderNo, string BDate, string EDate, int Row, int num, string LoginMan, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sStr1 = string.Empty;
            string sStr2 = string.Empty;
            string sStr3 = string.Empty;
            string iFlag = string.Empty;

            DataTable sdt = new DataTable();

            if (sFlag == "27-1")  // 查询DI-包装信息
            {
                sSQL = " select *,'<label onclick=''addRow(1)''class=''LabelAddBtn''>编辑</label>' as EE,'<label onclick=''DelDIOtherInfo(1)''class=''LabelDelBtn''>删除</label>' as DEL " +
                       " from T_DIPackInfo where ZXXSDYCPBS='" + Serial + "' order by BZCPBS ";
            }
            else if (sFlag == "27-2")  // 查询DI-储存条件
            {
                sSQL = " select *,'<label onclick=''addRow(2)''class=''LabelAddBtn''>编辑</label>' as EE,'<label onclick=''DelDIOtherInfo(2)''class=''LabelDelBtn''>删除</label>' as DEL " +
                       " from T_DIStorageInfo where ZXXSDYCPBS='" + Serial + "' order by InDate desc ";
            }
            else if (sFlag == "27-3")  // 查询DI-临床尺寸
            {
                sSQL = " select *,'<label onclick=''addRow(3)''class=''LabelAddBtn''>编辑</label>' as EE,'<label onclick=''DelDIOtherInfo(3)''class=''LabelDelBtn''>删除</label>' as DEL " +
                       " from T_DIClinicallyInfo where ZXXSDYCPBS='" + Serial + "' order by InDate desc ";
            }
            else if (sFlag == "27-4")  // 查询DI-包装信息--导出EXCEL用
            {
                sSQL = " select * from T_DIPackInfo where ZXXSDYCPBS in (select ZXXSDYCPBS from T_DIInfo where CompanyNo='" + sComp + "' and CPHHHBH like '" + MNo + "%' and GGXH like '" + Model + "%' " +
                       " and ZXXSDYCPBS  like '%" + Serial + "%' and CONVERT(char(10),InDate,120)>='" + BDate + "' and CONVERT(char(10),InDate,120)<='" + EDate + "')order by ZXXSDYCPBS ";
            }
            else if (sFlag == "27-5")  // 查询DI-储存条件--导出EXCEL用
            {
                sSQL = " select * from T_DIStorageInfo where ZXXSDYCPBS in (select ZXXSDYCPBS from T_DIInfo where CompanyNo='" + sComp + "' and CPHHHBH like '" + MNo + "%' and GGXH like '" + Model + "%' " +
                       " and ZXXSDYCPBS  like '%" + Serial + "%' and CONVERT(char(10),InDate,120)>='" + BDate + "' and CONVERT(char(10),InDate,120)<='" + EDate + "')order by ZXXSDYCPBS ";
            }
            else if (sFlag == "27-6")  // 查询DI-临床尺寸--导出EXCEL用
            {
                sSQL = " select * from T_DIClinicallyInfo where ZXXSDYCPBS in (select ZXXSDYCPBS from T_DIInfo where CompanyNo='" + sComp + "' and CPHHHBH like '" + MNo + "%' and GGXH like '" + Model + "%' " +
                       " and ZXXSDYCPBS  like '%" + Serial + "%' and CONVERT(char(10),InDate,120)>='" + BDate + "' and CONVERT(char(10),InDate,120)<='" + EDate + "')order by ZXXSDYCPBS "; ;
            }
            else
            {

                SqlParameter[] parameters = {
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnModel", SqlDbType.NVarChar,60),
                    new SqlParameter("@lnOrderNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnSerial", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,10),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
                parameters[0].Value = MNo;
                parameters[1].Value = "";
                parameters[2].Value = Model;
                parameters[3].Value = OrderNo;
                parameters[4].Value = Serial;
                parameters[5].Value = "";
                parameters[6].Value = "";
                parameters[7].Value = "";
                parameters[8].Value = "";
                parameters[9].Value = BDate;
                parameters[10].Value = EDate;
                parameters[11].Value = Row;
                parameters[12].Value = num;
                parameters[13].Value = LoginMan;
                parameters[14].Value = sFlag;
                parameters[15].Value = "";

                DataSet DS = DBHelper.RunProcedureForDS("P_GetSerielMaterForPage", parameters);
                sdt = DS.Tables[0];

                return sdt;
            }


            sdt = DBHelper.GetDataTable(sSQL);

            return sdt;
        }



        // 发放序列号，或禁用一些序列号
        public static string OPSerielInfo(string OrderNo, string Serial, string MNo, string FNum, string BNum, string A, string B, string C, string sComp, string sInMan, string sOrderFlag, string sFlag)
        {
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sBStr = string.Empty;
            string sStr = string.Empty;
            string iFlag = string.Empty;
            string sNo = string.Empty;
            string sls = string.Empty;
            int iMax = 0;



            DataTable sdt = new DataTable();

            if (sFlag == "2") // 发放序列号
            {
                SqlParameter[] parameters = {
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnOrderNo", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnOrderFlag", SqlDbType.NVarChar,10),
                    new SqlParameter("@lnNum", SqlDbType.NVarChar,10),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnFlag", SqlDbType.NVarChar,10),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
                parameters[0].Value = MNo;
                parameters[1].Value = OrderNo;
                parameters[2].Value = sOrderFlag;
                parameters[3].Value = FNum;  // 
                parameters[4].Value = A;  // 
                parameters[5].Value = B;  // 
                parameters[6].Value = C;  // 
                parameters[7].Value = sInMan;
                parameters[8].Value = "2"; // 1 预览,2发放序列号
                parameters[9].Value = "";

                DataSet DS = DBHelper.RunProcedureForDS("P_CreateSerialTwo", parameters);
                sdt = DS.Tables[0];

                if (sdt.Rows.Count > 0)
                {
                    sBStr = sdt.Rows[0]["BackInfo"].ToString();
                }

                return sBStr;

            }
            else if (sFlag == "3") // 删除
            {
                sSQL = " delete  T_SerielInfo where CompanyNo='" + sComp + "' and OrderNo='" + OrderNo + "' ";

                List<string> links = new List<string>();
                links.Add(sSQL);

                try
                {
                    //DBHelper.ExecuteCommand(sSQL);

                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {

                    sBStr = ex.Message.ToString();

                    throw;
                }
            }
            else if (sFlag == "4") // WEBSERVICE，返回序列号，改写是否已烧录标识
            {
                string sSStrOK = string.Empty;
                string sSStrNG = string.Empty;
                string sStr2 = string.Empty;
                string sTNo = string.Empty;

                if (Serial.IndexOf(",") > 0) // 说明有多个序列号 S01/OK,S02/OK/S03/NG,S04/OK,S05/NG
                {
                    var arr = Serial.Split(',');
                    for (int i = 0; i < arr.Length; i++)
                    {
                        sTNo = arr[i];
                        if (sTNo.Substring(sTNo.Length - 2, 2) == "OK") // 取后两位) -- 表示烧录OK的
                        {
                            sSStrOK = sSStrOK + ",'" + sTNo.Substring(0, sTNo.Length - 3) + "'"; //分割后的字符输出
                        }
                        else  //== NG 表示烧录不OK的
                        {
                            sSStrNG = sSStrNG + ",'" + sTNo.Substring(0, sTNo.Length - 3) + "'"; //分割后的字符输出 
                        }
                    }
                    if (sSStrOK.Length > 1)
                    {
                        sSStrOK = sSStrOK.Substring(1, sSStrOK.Length - 1);// str.substr(1,str.length-1)或者str.substring(1,str.length)
                    }
                    else
                    {
                        sSStrOK = "'OWEWW6634643643RWER'";  // 随便赋值一个，要不执行语句出现错误
                    }
                    if (sSStrNG.Length > 1)
                    {
                        sSStrNG = sSStrNG.Substring(1, sSStrNG.Length - 1);// str.substr(1,str.length-1)或者str.substring(1,str.length)
                    }
                    else
                    {
                        sSStrNG = "'OWEWW6634643643RWER'";  // 随便赋值一个，要不执行语句出现错误
                    }
                }
                else
                {
                    if (Serial.Substring(Serial.Length - 2, 2) == "OK") // 取后两位) -- 表示烧录OK的
                    {
                        sSStrOK = "'" + Serial.Substring(0, Serial.Length - 3) + "'";
                    }
                    else
                    {
                        sSStrOK = "'OWEWW6634643643RWER'";  // 随便赋值一个，要不执行语句出现错误
                    }

                    if (Serial.Substring(Serial.Length - 2, 2) == "NG")  //== NG 表示烧录不OK的
                    {
                        sSStrNG = "'" + Serial.Substring(0, Serial.Length - 3) + "'";   // 截取
                    }
                    else
                    {
                        sSStrNG = "'OWEWW6634643643RWER'";  // 随便赋值一个，要不执行语句出现错误
                    }
                }

                sSQL = " update  T_SerielInfo set SPrintFlag='Y',SPrintDate=CONVERT(char(20),GETDATE(),120) where SerielNo in (" + sSStrOK + ") and CompanyNo='" + sComp + "' ";
                sSQLs = " update  T_SerielInfo set SGetFlag='',SGetDate='',SPrintFlag='',SPrintDate='' where SerielNo in (" + sSStrNG + ") and CompanyNo='" + sComp + "' ";



                // 保存烧录不成功的
                sStr2 = " insert into T_SerielOpInfo(SerielNo,Kind) " +
                        " select SerielNo,'保存烧录不成功的记录' from T_SerielInfo  where SerielNo in (" + sSStrNG + ") and CompanyNo='" + sComp + "' ";


                // 更新烧录成功的序列号生产信息为已完成；不成功的在这里不用处理，因为重新获取序列号时会删除
                sStr = " update T_ProductBatchInfo set EndUserNo='SL',EndDate=convert(char(20),Getdate(),120),Status='已完成'  where BatchNo in (" + sSStrOK + ") and ProcedureName='烧录' and Status='生产中' and CompanyNo='" + sComp + "' ";





                List<string> links = new List<string>();
                links.Add(sSQL);
                links.Add(sSQLs);
                links.Add(sStr2);
                links.Add(sStr);  // 更新烧录成功的序列号生产信息为已完成



                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    iFlag = "0"; //ex.Message.ToString();

                    throw;
                }
            }
            else if (sFlag == "5") // 插入准备打印的序列号
            {
                //string sMaxNo = DBHelper.GetMaxNo("T_PrePrintSeriel where SerielNo='" + Serial + "' and CompanyNo='" + sComp + "' ", "STime");//
                //if (sMaxNo == "") {
                //    sNo = "S001";
                //}
                //else {  //目前只支持3位流水号
                //    sls = sMaxNo.Substring(1, 3);
                //    iMax = int.Parse(sls) + 1;

                //    if (iMax <= 9)
                //    {
                //        sNo = "S00" + iMax.ToString();
                //    }
                //    else if ((iMax > 9) || (iMax <= 99)) {
                //        sNo = "S0" + iMax.ToString();
                //    }
                //    else {
                //        sNo = "S" + iMax.ToString();
                //    }
                //}

                // 判断存在该序列号，则更新未打印
                sSQLs = " select InMan from T_PrePrintSeriel where SerielNo='" + Serial + "' and STime='S001' ";  // and CompanyNo= '" + sComp + "' 
                DataTable dtc8 = DBHelper.GetDataTable(sSQLs);
                if (dtc8.Rows.Count > 0)
                {  // 说明存在该序列号了，更新状态，让其继续打印即可
                    sSQL = " update T_PrePrintSeriel set Flag='N',Remark='上传发送时间:' + convert(char(16),InDate,120),InDate=getdate() where SerielNo='" + Serial + "' and STime='S001' ";
                }
                else
                {   // 没有这个准备打印的序列号，则插入一个
                    sSQL = " insert into T_PrePrintSeriel(SerielNo,STime,GX,GW,Flag,InMan,CompanyNo,Remark) " +
                           " values('" + Serial + "','S001','" + MNo + "','','N','','" + sComp + "','') ";
                }

                // 保存返回打印序列号的记录
                sSQLs = " insert into T_SerielOpInfo(SerielNo,Kind) " +
                        " values('" + Serial + "','返回需要打印的序列号') ";


                List<string> links = new List<string>();
                links.Add(sSQL);
                links.Add(sSQLs);

                try
                {
                    iFlag = DBHelper.ExecuteSqlTranStr(links);
                }
                catch (Exception ex)
                {
                    iFlag = "0"; //ex.Message.ToString();

                    throw;
                }
            }


            return iFlag;
        }


        // 对UDI码进行操作
        public static string OPUDIInfo(string DI, string MNo, string Spec, string S1, string S2, string S3, string S4, string S5, string S6, string S7, string S8, string S9, string S10, string S11, string S12, string S13, string S14, string S15, string S16, string S17, string S18, string sComp, string sInMan, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sStr = string.Empty;
            string sStr1 = string.Empty;
            string sSQLs = string.Empty;
            string sSQLs1 = string.Empty;
            string sSQLs2 = string.Empty;
            string sSQLs3 = string.Empty;
            string sstr = string.Empty;
            int Num = 0;



            if (sFlag == "1") // 发放DI码--储存
            {


                Num = 1;
                sSQL = " insert into T_DIInfo(ZXXSDYCPBS,CPBSBMTXMC,ZXXSDYZSYDYDSL,SYDYCPBS,CPBSFBRQ,CPBSZT,SFYZCBACPBSYZ,ZCBACPBS,SFYBTZJBS,BTCPBSYZXXSDYCPBSSFYZ,BTCPBS,CPMCTYMC,SPMC,SFWBLZTLCP,GGXH,CPMS,CPHHHBH,QXLB,YFLBM,FLBM,YLQXZCRBARMC,YLQXZCRBARYWMC, " +
                       " ZCZBHHZBAPZBH,CPLB,CGZMRAQXGXX,SFBJWYCXSY,ZDCFSYCS,SFWWJBZ,SYQSFXYJXMJ,MJFS,QTXXDWZLJ,YBBM,TSRQ,SCBSSFBHPH,SCBSSFBHXLH,SCBSSFBHSCRQ,SCBSSFBHSXRQ,DIone,DItwo,DIthree,DIfour,TYSHXYDM,Status,CompanyNo,InMan) " +
                       " select '" + DI + "' ,'','','','','','','','','','',PName,MaterXH,Mount,MaterSpec,MaterName,MaterNo,MaterType,TechNo,FillMaterKind,MHeight,Diameter,Distortion,MaterKind,StockLacation,EfftMg,BatchMg,KaifengMg,LOSHFlag,Coating,StockPlace, " +
                       " BatchNo,LastOutDate,TUnit,LWUnit,HUnit,InspectMg,'" + S1 + "','" + S2 + "','" + S3 + "','" + S4 + "',TaxNumber,'启用','" + sComp + "','" + sInMan + "' " +
                       " from T_MaterInfo a left join (select TOP 1 CompID,TaxNumber from T_CompanyInfo WHERE CompID='C0010') b on a.CompanyNo=b.CompID " +
                       " WHERE MaterNo='" + MNo + "' and CompanyNo='" + sComp + "' ";


                if (S1 != "")
                { // 说明需要插入中包装DI
                    //Num = 2;   // 20221115 屏蔽说明：现在中包装，外部包装，其他包装的DI码流水号一样，因此流水号只要更新增加 1 即可，上面的 Num = 1 ,已赋值增加1了
                    sSQLs1 = " insert into T_DIPackInfo(ZXXSDYCPBS,BZCPBS,CPBZJB,BZNHXYJCPBSSL,BZNHXYJBZCPBS,CompanyNo,InMan,Remark) " +
                             " values('" + DI + "','" + S1 + "','','','','" + sComp + "','" + sInMan + "','') ";
                }
                if (S2 != "")  // 说明需要插入外包装DI
                {
                    //Num = 3;
                    sSQLs2 = " insert into T_DIPackInfo(ZXXSDYCPBS,BZCPBS,CPBZJB,BZNHXYJCPBSSL,BZNHXYJBZCPBS,CompanyNo,InMan,Remark) " +
                             " values('" + DI + "','" + S2 + "','','','','" + sComp + "','" + sInMan + "','') ";
                }
                if (S3 != "")  // 说明需要插入其他包装DI
                {
                    //Num = 4;
                    sSQLs3 = " insert into T_DIPackInfo(ZXXSDYCPBS,BZCPBS,CPBZJB,BZNHXYJCPBSSL,BZNHXYJBZCPBS,CompanyNo,InMan,Remark) " +
                             " values('" + DI + "','" + S3 + "','','','','" + sComp + "','" + sInMan + "','') ";
                }

                sSQLs = " update T_MCode set NowNum=NowNum+" + Num + " where IsUse='是' and CompanyNo='" + sComp + "' "; //  每次只能有一个在用的厂商识别码
            }
            else if (sFlag == "2")  // 修改DI码：主要新增或删除中，外，其他包装的DI码
            {
                if (DI != S7)
                {  // 说明修改主DI码，则先执行修改动作，下面那些删除的，先不操作。
                    sSQL = " update T_DIInfo set ZXXSDYCPBS='" + DI + "',DIone='" + S4 + "',DItwo='" + S5 + "',DIthree='" + S6 + "',Remark=Remark + '修改DI:'+'" + S7 + "；'+convert(char(20),getdate(),120) " +
                           " where ZXXSDYCPBS='" + S7 + "' ";
                    sSQLs = " update T_DIPackInfo set ZXXSDYCPBS='" + DI + "',BZCPBS='" + S4 + "' where ZXXSDYCPBS='" + S7 + "' and BZCPBS like '1%' " +
                            " update T_DIPackInfo set ZXXSDYCPBS='" + DI + "',BZCPBS='" + S5 + "' where ZXXSDYCPBS='" + S7 + "' and BZCPBS like '2%' " +
                            " update T_DIPackInfo set ZXXSDYCPBS='" + DI + "',BZCPBS='" + S6 + "' where ZXXSDYCPBS='" + S7 + "' and BZCPBS like '3%' ";
                    sSQLs1 = " update  T_DIStorageInfo  set ZXXSDYCPBS='" + DI + "'  where ZXXSDYCPBS='" + S7 + "' ";
                    sSQLs2 = " update  T_DIClinicallyInfo set ZXXSDYCPBS='" + DI + "' where ZXXSDYCPBS='" + S7 + "' ";
                }
                else  // 当没有修改主DI码时，执行删除，增加子DI的操作
                {
                    Num = 0;// 默认是 0 ，如果有新增的DI，流水号只增加一个

                    if (S1 == "删除")
                    {
                        sSQLs1 = " delete T_DIPackInfo where BZCPBS= '" + S4 + "' ";
                        sSQL = " update T_DIInfo set DIone='' where ZXXSDYCPBS= '" + DI + "' ";
                    }
                    else if (S1 != "")
                    {  // 说明需要插入中包装DI  DIone,DItwo,DIthree,DIfour
                        Num = 1;
                        sSQLs1 = " insert into T_DIPackInfo(ZXXSDYCPBS,BZCPBS,CPBZJB,BZNHXYJCPBSSL,BZNHXYJBZCPBS,CompanyNo,InMan,Remark) " +
                                 " values('" + DI + "','" + S1 + "','','','','" + sComp + "','" + sInMan + "','') ";
                        sSQL = " update T_DIInfo set DIone='" + S1 + "' where ZXXSDYCPBS= '" + DI + "' ";
                    }
                    else  // 其他情况，就直接修改
                    {
                        sSQLs1 = " update T_DIPackInfo set BZCPBS='" + S4 + "' where ZXXSDYCPBS='" + DI + "' and BZCPBS like '1%'  ";
                        sSQL = " update T_DIInfo set DIone='" + S4 + "'  where ZXXSDYCPBS= '" + DI + "' ";
                    }

                    if (S2 == "删除")
                    {
                        sSQLs2 = " delete T_DIPackInfo where BZCPBS= '" + S5 + "' ";
                        sStr1 = " update T_DIInfo set DItwo='' where ZXXSDYCPBS= '" + DI + "' ";
                    }
                    else if (S2 != "")
                    {  // 说明需要插入中包装DI
                        Num = 1;
                        sSQLs2 = " insert into T_DIPackInfo(ZXXSDYCPBS,BZCPBS,CPBZJB,BZNHXYJCPBSSL,BZNHXYJBZCPBS,CompanyNo,InMan,Remark) " +
                                 " values('" + DI + "','" + S2 + "','','','','" + sComp + "','" + sInMan + "','') ";
                        sStr1 = " update T_DIInfo set DItwo='" + S2 + "' where ZXXSDYCPBS= '" + DI + "' ";
                    }
                    else  // 其他情况，就直接修改
                    {
                        sSQLs2 = " update T_DIPackInfo set BZCPBS='" + S5 + "' where ZXXSDYCPBS='" + DI + "' and BZCPBS like '2%'  ";
                        sStr1 = " update T_DIInfo set DItwo='" + S5 + "'  where ZXXSDYCPBS= '" + DI + "' ";
                    }

                    if (S3 == "删除")
                    {
                        sSQLs3 = " delete T_DIPackInfo where BZCPBS= '" + S6 + "' ";
                        sStr = " update T_DIInfo set DIthree='' where ZXXSDYCPBS= '" + DI + "'";
                    }
                    else if (S3 != "")
                    {  // 说明需要插入中包装DI
                        Num = 1;
                        sSQLs3 = " insert into T_DIPackInfo(ZXXSDYCPBS,BZCPBS,CPBZJB,BZNHXYJCPBSSL,BZNHXYJBZCPBS,CompanyNo,InMan,Remark) " +
                                 " values('" + DI + "','" + S3 + "','','','','" + sComp + "','" + sInMan + "','') ";
                        sStr = " update T_DIInfo set DIthree='" + S3 + "' where ZXXSDYCPBS= '" + DI + "' ";
                    }
                    else  // 其他情况，就直接修改
                    {
                        sSQLs3 = " update T_DIPackInfo set BZCPBS='" + S6 + "' where ZXXSDYCPBS='" + DI + "' and BZCPBS like '3%'  ";
                        sStr = " update T_DIInfo set DIthree='" + S6 + "'  where ZXXSDYCPBS= '" + DI + "' ";
                    }

                    sSQLs = " update T_MCode set NowNum=NowNum+" + Num + " where IsUse='是' and CompanyNo='" + sComp + "' "; //  每次只能有一个在用的厂商识别码
                }

            }
            else if (sFlag == "2-1")  // DI管理，收集DI信息
            {
                sSQL = " update  T_DIInfo set CPBSBMTXMC='" + S4 + "',ZXXSDYZSYDYDSL='" + S5 + "',SYDYCPBS='" + S6 + "',CPBSFBRQ='" + S7 + "',CPBSZT='" + S8 + "',SFYZCBACPBSYZ='" + S9 + "',ZCBACPBS='" + S10 + "',SFYBTZJBS='" + S11 + "', " +
                       " BTCPBSYZXXSDYCPBSSFYZ='" + S12 + "',BTCPBS='" + S13 + "' where ZXXSDYCPBS='" + DI + "' ";
            }
            else if (sFlag == "3")  // 删除DI信息
            {
                sSQL = " delete  T_DIInfo  where ZXXSDYCPBS='" + DI + "' ";
                sSQLs = " delete  T_DIPackInfo  where ZXXSDYCPBS='" + DI + "' ";
                sSQLs1 = " delete  T_DIStorageInfo  where ZXXSDYCPBS='" + DI + "' ";
                sSQLs2 = " delete  T_DIClinicallyInfo  where ZXXSDYCPBS='" + DI + "' ";
            }
            else if (sFlag == "11")  // 插入DI包装信息  sComp, string sInMan,
            {
                sSQL = " insert into T_DIPackInfo(ZXXSDYCPBS,BZCPBS,CPBZJB,BZNHXYJCPBSSL,BZNHXYJBZCPBS,CompanyNo,InMan,Remark) " +
                       " values('" + DI + "','" + MNo + "','" + S4 + "','" + S6 + "','" + S7 + "','" + sComp + "','" + sInMan + "','') ";

                sSQLs = " update T_MCode set NowNum=NowNum+1 where IsUse='是' and CompanyNo='" + sComp + "' "; //  每次只能有一个在用的厂商识别码
            }
            else if (sFlag == "11-1")  // 修改DI包装信息
            {
                sSQL = " update T_DIPackInfo set CPBZJB='" + S4 + "',BZNHXYJCPBSSL='" + S6 + "',BZNHXYJBZCPBS='" + S7 + "' where ZXXSDYCPBS='" + DI + "' and BZCPBS='" + MNo + "'";
            }
            else if (sFlag == "11-2")  // 删除DI包装信息
            {
                sSQL = " delete T_DIPackInfo where ZXXSDYCPBS='" + DI + "' and BZCPBS='" + MNo + "'";
            }
            else if (sFlag == "12")  // 插入DI储存或操作条件
            {
                string sDate = DateTime.Now.ToString("yyyy-MM-dd");
                string sNo = string.Empty;
                int i = 0;
                int iMax = 0;

                string sMaxNo = DBHelper.GetMaxNo("T_DIStorageInfo where convert(char(10),InDate,120)='" + sDate + "' ", "STNo");// 210323 0001
                if (sMaxNo == "")
                {
                    sNo = CreateAllNo.CreateBillNo(2, 3) + "1";
                }
                else
                {
                    string sTemp = sMaxNo.Substring(6, 4);
                    iMax = int.Parse(sTemp) + 1;
                    int len = iMax.ToString().Length;
                    i = 4 - len;

                    sNo = CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
                }

                sSQL = " insert into T_DIStorageInfo(ZXXSDYCPBS,STNo,CCHCZTJ,ZDZ,ZGZ,JLDW,TSCCHCZTJ,CompanyNo,InMan,Remark) " +
                       " values('" + DI + "','" + sNo + "','" + S4 + "','" + S5 + "','" + S6 + "','" + S7 + "','" + S8 + "','" + sComp + "','" + sInMan + "','') ";
            }
            else if (sFlag == "12-1")  // 修改DI储存或操作条件
            {
                sSQL = " update T_DIStorageInfo set CCHCZTJ='" + S4 + "',ZDZ='" + S5 + "',ZGZ='" + S6 + "',JLDW='" + S7 + "',TSCCHCZTJ='" + S8 + "' where ZXXSDYCPBS='" + DI + "' and STNo='" + MNo + "'";
            }
            else if (sFlag == "12-2")  // 删除DI储存或操作条件
            {
                sSQL = " delete T_DIStorageInfo where ZXXSDYCPBS='" + DI + "' and STNo='" + MNo + "'";
            }
            else if (sFlag == "13")  // 插入DI 临床使用尺寸
            {
                string sDate = DateTime.Now.ToString("yyyy-MM-dd");
                string sNo = string.Empty;
                int i = 0;
                int iMax = 0;

                string sMaxNo = DBHelper.GetMaxNo("T_DIClinicallyInfo where convert(char(10),InDate,120)='" + sDate + "' ", "CLNo");// 210323 0001
                if (sMaxNo == "")
                {
                    sNo = CreateAllNo.CreateBillNo(2, 3) + "1";
                }
                else
                {
                    string sTemp = sMaxNo.Substring(6, 4);
                    iMax = int.Parse(sTemp) + 1;
                    int len = iMax.ToString().Length;
                    i = 4 - len;

                    sNo = CreateAllNo.CreateBillNo(2, i) + iMax.ToString();
                }

                sSQL = " insert into T_DIClinicallyInfo(ZXXSDYCPBS,CLNo,LCSYCCLX,CCZ,CCDW,TSCCSM,CompanyNo,InMan,Remark) " +
                       " values('" + DI + "','" + sNo + "','" + S4 + "','" + S5 + "','" + S6 + "','" + S7 + "','" + sComp + "','" + sInMan + "','') ";
            }
            else if (sFlag == "13-1")  // 修改DI 临床使用尺寸
            {
                sSQL = " update T_DIClinicallyInfo set LCSYCCLX='" + S4 + "',CCZ='" + S5 + "',CCDW='" + S6 + "',TSCCSM='" + S7 + "' where ZXXSDYCPBS='" + DI + "' and CLNo='" + MNo + "'";
            }
            else if (sFlag == "13-2")  // 删除DI 临床使用尺寸
            {
                sSQL = " delete T_DIClinicallyInfo where ZXXSDYCPBS='" + DI + "' and CLNo='" + MNo + "'";
            }


            List<string> links = new List<string>();
            links.Add(sSQL);
            links.Add(sStr);
            links.Add(sStr1);
            links.Add(sSQLs);
            links.Add(sSQLs1);
            links.Add(sSQLs2);
            links.Add(sSQLs3);




            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;


        }


        // 操作产品编码对应的标贴,标贴模板进行操作  S1: sTPNo, S2: sLVer, S3: sKind, S4: sTPName, S5: sLong, S6: sWide, S7: sProcNo, UseFlag: sCAuto,
        public static string OPProductLabel(string FMNo, string FMName, string MNo, string MName, string S1, string S2, string S3, string S4, string S5, string S6, string S7, string S8, string S9, string S10, string UseFlag, string sComp, string sInMan, string Remark, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sSQLs = string.Empty;
            string sNo = string.Empty;
            string sName = string.Empty;
            string sstr = string.Empty;


            if (sFlag == "1") // 新增
            {
                if (S7.Length > 3) // 说明选择了工序 (05)包装   // 截取  S1: sTPNo, S2: sLVer, S3: sKind, S4: sTPName, S5: sLong, S6: sWide, S7: sProcNo, UseFlag: sCAuto,
                {
                    sNo = S7.Substring(1, S7.IndexOf(")") - 1);
                    sName = S7.Substring(S7.IndexOf(")") + 1, S7.Length - S7.IndexOf(")") - 1);
                }

                sSQL = " insert into T_ProductAndLabel(PMaterNo,PMaterName,PSpec,MaterNo,MaterName,LabelVer,TPNo,TPVer,Kind,TemplateName, SizeLong,SizeWide,ProcedureNo,ProcedureName,AutoPrint,AutoPrintWEB,PathName,CompanyNo,InMan,Remark)  " +
                       " select MaterNo,MaterName,MaterSpec,'" + MNo + "','" + MName + "','" + S10 + "','" + S1 + "','" + S2 + "','" + S3 + "','" + S4 + "','" + S5 + "','" + S6 + "','" + sNo + "','" + sName + "','" + UseFlag + "','" + S9 + "', " +
                       " '" + S8 + "','" + sComp + "','" + sInMan + "','" + Remark + "' " +
                       " from T_MaterInfo where MaterNo='" + FMNo + "' and CompanyNo='" + sComp + "'  ";
            }
            else if (sFlag == "2") // 修改
            {
                if (S7.Length > 3) // 说明选择了工序 (05)包装   // 截取
                {
                    sNo = S7.Substring(1, S7.IndexOf(")") - 1);
                    sName = S7.Substring(S7.IndexOf(")") + 1, S7.Length - S7.IndexOf(")") - 1);
                }

                sSQL = " update T_ProductAndLabel set MaterNo='" + MNo + "',LabelVer='" + S10 + "',MaterName='" + MName + "',TPNo='" + S1 + "',TPVer='" + S2 + "',Kind='" + S3 + "',TemplateName='" + S4 + "', SizeLong='" + S5 + "',PathName='" + S8 + "', " +
                       " SizeWide='" + S6 + "',ProcedureNo='" + sNo + "',ProcedureName='" + sName + "',AutoPrint='" + UseFlag + "',AutoPrintWEB='" + S9 + "',Remark='修改：'+'" + Remark + "' " +
                       " where CompanyNo='" + sComp + "' and PMaterNo='" + FMNo + "' and MaterNo='" + MNo + "'";
            }
            else if (sFlag == "3") // 删除
            {
                sSQL = " delete T_ProductAndLabel where CompanyNo='" + sComp + "' and PMaterNo='" + FMNo + "' and MaterNo='" + MNo + "' and LabelVer='" + S4 + "' ";
            }
            else if (sFlag == "4") // 禁用
            {
                sSQL = " update T_ProductAndLabel set Status='禁用' where CompanyNo='" + sComp + "' and PMaterNo='" + FMNo + "' and MaterNo='" + MNo + "' and LabelVer='" + S4 + "' ";
            }
            else if (sFlag == "5") // 启用
            {
                sSQL = " update T_ProductAndLabel set Status='启用' where CompanyNo='" + sComp + "' and PMaterNo='" + FMNo + "' and MaterNo='" + MNo + "' and LabelVer='" + S4 + "' ";
            }
            else if (sFlag == "7") // 新增标贴模板 FMNo: sTPNo, FMName: sTPName, S1: sKind, S2: sLong, S3: sWide, S4: sDMan, S5: sDDate, S6: sFile, S7: sPath, S8: sLangua, S9: txtEChange
            {
                sSQL = " insert into T_LabelTemplate(TPNo,TPVer,TPName,Kind,DMan,DDate,PathName,TPPath,SizeLong,SizeWide,Langua,EChange,Status,CompanyNo,InMan,Remark) " +
                       " values('" + FMNo + "','" + MNo + "','" + FMName + "','" + S1 + "','" + S4 + "','" + S5 + "','" + S6 + "','" + S7 + "','" + S2 + "','" + S3 + "','" + S8 + "','" + S9 + "','草稿','" + sComp + "','" + sInMan + "','" + Remark + "') ";
            }
            else if (sFlag == "8") // 修改标贴模板
            {
                sSQL = " update T_LabelTemplate set TPName='" + FMName + "',Kind='" + S1 + "',DMan='" + S4 + "',DDate='" + S5 + "',PathName='" + S6 + "',TPPath='" + S7 + "',SizeLong='" + S2 + "', " +
                       " SizeWide='" + S3 + "',Langua='" + S8 + "',EChange='" + S9 + "',Remark='" + Remark + "' " +
                       " where TPNo='" + FMNo + "' and TPVer='" + MNo + "'  and CompanyNo='" + sComp + "' ";
            }
            else if (sFlag == "8-1") // 升版后：修改标贴模板
            {
                sSQL = " update T_LabelTemplate set TPVer='" + MNo + "', TPName='" + FMName + "',Kind='" + S1 + "',DMan='" + S4 + "',DDate='" + S5 + "',PathName='" + S6 + "',TPPath='" + S7 + "',SizeLong='" + S2 + "', " +
                       " SizeWide='" + S3 + "',Langua='" + S8 + "',EChange='" + S9 + "',Remark='" + Remark + "' " +
                       " where TPNo='" + FMNo + "' and TPVer='' and Status='草稿'  and CompanyNo='" + sComp + "' ";
            }
            else if (sFlag == "9") // 变更标贴模板
            {
                //sSQL = " update T_LabelTemplate set TPName='" + MName + "',Size='" + S2 + "',Kind='" + S3 + "',DMan='" + S4 + "',DDate='" + S5 + "',TPPath='" + S6 + "',PathName='" + S7 + "',Remark='" + Remark + "' " +
                //       " where TPNo='" + FMNo + "' and TPVer='" + MNo + "' ";
            }
            else if (sFlag == "10") // 删除标贴模板
            {
                sSQL = " delete T_LabelTemplate where TPNo='" + FMNo + "' and TPVer='" + MNo + "'  and CompanyNo='" + sComp + "' ";
            }
            else if (sFlag == "11") // 提交标贴模板
            {
                sSQL = " update T_LabelTemplate set Status='待审核' where TPNo='" + FMNo + "' and TPVer='" + MNo + "'  and CompanyNo='" + sComp + "' ";
            }
            else if (sFlag == "12") // 审核：审核后的模板直接启用   ,其他版本的，直接禁用
            {
                sSQL = " update T_LabelTemplate set Status='启用',AudMan='" + sInMan + "',AudDate=convert(char(16),getdate(),120) where TPNo='" + FMNo + "' and TPVer='" + MNo + "' and CompanyNo='" + sComp + "' ";

                sSQLs = " update T_LabelTemplate set Status='禁用' where TPNo='" + FMNo + "' and TPVer<>'" + MNo + "' and CompanyNo='" + sComp + "' ";
            }
            else if (sFlag == "13") // 驳回
            {
                sSQL = " update T_LabelTemplate set Status='草稿' where TPNo='" + FMNo + "' and TPVer='" + MNo + "' and CompanyNo='" + sComp + "' ";
            }
            else if (sFlag == "14") // 禁用：审核后的模板直接启用
            {
                sSQL = " update T_LabelTemplate set Status='禁用' where TPNo='" + FMNo + "' and TPVer='" + MNo + "' and CompanyNo='" + sComp + "' ";
            }
            else if (sFlag == "15") // 升版：直接复制上版本的模板，设置为草稿状态,版本为空，需要编写人员填写
            {
                sSQL = " insert into T_LabelTemplate(TPNo,TPVer,TPName,Kind,DMan,DDate,PathName,TPPath,SizeLong,SizeWide,Langua,EChange,OldVer,Status,CompanyNo,InMan,Remark) " +
                       " select TPNo,'',TPName,Kind,DMan,DDate,PathName,TPPath,SizeLong,SizeWide,Langua,'',TPVer,'草稿',CompanyNo,'" + sInMan + "',''  " +
                       " from T_LabelTemplate where TPNo='" + FMNo + "' and TPVer='" + MNo + "' and CompanyNo='" + sComp + "' ";
            }
            else if (sFlag == "16") // 可再次启用
            {
                sSQL = " update T_LabelTemplate set Status='启用' where TPNo='" + FMNo + "' and TPVer='" + MNo + "' and CompanyNo='" + sComp + "' ";

                sSQLs = " update T_LabelTemplate set Status='禁用' where TPNo='" + FMNo + "' and TPVer<>'" + MNo + "' and CompanyNo='" + sComp + "' ";
            }








            List<string> links = new List<string>();
            links.Add(sSQL);
            links.Add(sSQLs);

            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }



        // 获取打印标贴信息，如UDI信息，外箱标贴
        public static DataTable GetPrintLabelInfo(string MNo, string Model, string OrderNo, string Serial, string BDate, string EDate, int Row, int num, string LoginMan, string sComp, string sFlag)
        {
            string sSQL = string.Empty;

            DataTable sdt = new DataTable();

            if (sFlag == "27-1")  // 查询DI-包装信息
            {
                sSQL = " ";
            }
            else
            {

                SqlParameter[] parameters = {
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnModel", SqlDbType.NVarChar,60),
                    new SqlParameter("@lnOrder", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnSerial", SqlDbType.NVarChar,4000),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,20),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
                parameters[0].Value = MNo;
                parameters[1].Value = "";
                parameters[2].Value = Model;
                parameters[3].Value = OrderNo;
                parameters[4].Value = Serial;
                parameters[5].Value = "";
                parameters[6].Value = "";
                parameters[7].Value = BDate;
                parameters[8].Value = EDate;
                parameters[9].Value = Row;
                parameters[10].Value = num;
                parameters[11].Value = LoginMan;
                parameters[12].Value = sFlag;
                parameters[13].Value = "";

                DataSet DS = DBHelper.RunProcedureForDS("P_GetPrintLabelInfo", parameters);
                sdt = DS.Tables[0];

                return sdt;
            }


            sdt = DBHelper.GetDataTable(sSQL);

            return sdt;
        }


        // 操作公司基本信息
        public static string OPCompanyInfo(string CompID, string CName, string CJC, string CEN, string LMan, string Code, string Category, string BScope, string RAmount, string Number, string RAddr, string CAddr, string PAddr, string PCode, string CMan, string Phone, string Fax, string EMail, string Website, string APPID, string APPSECRET, string XKZ, string sComp, string sInMan, string Remark, string sFlag)
        {
            string iFlag = string.Empty;
            string sSQL = string.Empty;
            string sstr = string.Empty;


            if (sFlag == "1") // 新增
            {
                sSQL = " insert into T_CompanyInfo(CompID,CompName,NameJC,CompNameEN,LegalMan,OrganizationCode,Category,BusinessScope,RegAmount,TaxNumber,RegAddr,CompAddr,PAddr,PostalCode,CMan,Phone,Fax,EMail,Website,APPID,APPSECRET,PrdXKZ,CompanyNo,Status,InMan,Remark)  " +
                       " values('" + CompID + "','" + CName + "','" + CJC + "','" + CEN + "','" + LMan + "','" + Code + "','" + Category + "','" + BScope + "','" + RAmount + "','" + Number + "','" + RAddr + "','" + CAddr + "','" + PAddr + "','" + PCode + "','" + CMan + "','" + Phone + "','" + Fax + "','" + EMail + "','" + Website + "','" + APPID + "','" + APPSECRET + "','" + XKZ + "','" + CompID + "','启用','" + sInMan + "','" + Remark + "') ";
            }
            else if (sFlag == "2") // 修改
            {
                sSQL = " update  T_CompanyInfo set CompName='" + CName + "',NameJC='" + CJC + "',CompNameEN='" + CEN + "',LegalMan='" + LMan + "',OrganizationCode='" + Code + "',Category='" + Category + "',BusinessScope='" + BScope + "',RegAmount='" + RAmount + "',TaxNumber='" + Number + "',RegAddr='" + RAddr + "',CompAddr='" + CAddr + "',PAddr='" + PAddr + "',PostalCode='" + PCode + "', " +
                       " CMan='" + CMan + "',Phone='" + Phone + "',Fax='" + Fax + "',EMail='" + EMail + "',Website='" + Website + "',APPID='" + APPID + "',APPSECRET='" + APPSECRET + "',PrdXKZ='" + XKZ + "',Remark='" + Remark + "' where CompID='" + CompID + "'  ";
            }
            else if (sFlag == "3") // 删除
            {
                sSQL = " delete  T_CompanyInfo where CompID='" + CompID + "' ";
            }
            else if (sFlag == "4") // 禁用
            {
                sSQL = " update T_CompanyInfo set Status='禁用' where CompID='" + CompID + "' ";
            }
            else if (sFlag == "5") // 启用
            {
                sSQL = " update T_CompanyInfo set Status='启用' where CompID='" + CompID + "' ";
            }


            List<string> links = new List<string>();
            links.Add(sSQL);

            try
            {
                //DBHelper.ExecuteCommand(sSQL);

                iFlag = DBHelper.ExecuteSqlTranStr(links);
            }
            catch (Exception ex)
            {
                //iFlag = "0";

                sstr = ex.Message.ToString();

                throw;
            }

            return iFlag + sstr;
        }



        // 获取一些接口地址及法规信息
        public static DataTable GetURLAndBaseInfo(string No, string C1, string C2, string sComp, string sFlag)
        {
            string sSQL = string.Empty;

            DataTable sdt = new DataTable();

            if (sFlag == "1")
            {   // 获取URL  -- 每个系统只能有一个启用状态的
                sSQL = " select * from T_SysUDIurl where SysKind='药监局UDI提报地址' and Status='启用' and CompanyNo='" + sComp + "' ";
            }
            else if (sFlag == "2")
            {   // 获取公司信息
                sSQL = " SELECT * FROM T_CompanyInfo WHERE CompID='" + sComp + "' ";
            }



            sdt = DBHelper.GetDataTable(sSQL);

            return sdt;
        }


        //获取批序号信息
        public static DataTable GetBatchSerial(int limit, int page, string sMan, string MaterNo, string MaterName, string BatchNo, string Flag)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@lnNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnItem", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnStatus", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,10),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
            parameters[0].Value = BatchNo;
            parameters[1].Value = "";
            parameters[2].Value = "";
            parameters[3].Value = MaterNo;
            parameters[4].Value = MaterName;
            parameters[5].Value = "2021-01-01";
            parameters[6].Value = "2999-12-30";
            parameters[7].Value = "";
            parameters[8].Value = "";
            parameters[9].Value = "";
            parameters[10].Value = "";
            parameters[11].Value = "";
            parameters[12].Value = limit;
            parameters[13].Value = page;
            parameters[14].Value = sMan;
            parameters[15].Value = Flag;
            parameters[16].Value = "";

            DataSet DS = DBHelper.RunProcedureForDS("P_GetMaterFlowInfoForPage", parameters);
            DataTable dt = DS.Tables[0];
            return dt;
        }

        public static string OPBatchSerial(string BatchNo, string MaterNo, string MaterName, string Stock, string Comp, string Man, string OPFlag, string Flag, string BatchNoOld)
        {
            string sql = "";
            string result = "";
            if (Flag == "1")
            {
                if (OPFlag == "Add")
                {
                    string[] str = BatchNo.Split(',');
                    int num = 0;
                    for (int i = 0; i < str.Length; i++)
                    {
                        sql = "insert into T_BatchSerial(BatchNo,SerialNo,MaterNo,MaterName,Stock,Status,CompanyNo,InMan,InDate,Remark) values('" + str[i] + "','" + str[i] + "','" + MaterNo + "','" + MaterName + "','" + Stock + "','未使用','" + Comp + "','" + Man + "',default,'')";
                        num = num + DBHelper.ExecuteCommand(sql);
                    }
                    result = num.ToString();
                }
                else if (OPFlag == "Edit")
                {
                    sql = "update T_BatchSerial set BatchNo='" + BatchNo + "',SerialNo='" + BatchNo + "' where BatchNo='" + BatchNoOld + "'";
                    int num = DBHelper.ExecuteCommand(sql);
                    result = num.ToString();
                }
            }
            else if (Flag == "2")
            {
                sql = "delete from T_BatchSerial where BatchNo='" + BatchNo + "'";
                int num = DBHelper.ExecuteCommand(sql);
                result = num.ToString();
            }
            else
            {
                //验证是否有重复记录
                sql = "select * from T_BatchSerial where BatchNo='" + BatchNo + "'";
                DataTable dt = DBHelper.GetDataTable(sql);
                int num = dt.Rows.Count;
                result = num.ToString();
            }
            return result;
        }



        public static DataTable GetBaseInfoList(string No, string Name, string A, string B, string C, string D, string E, string F, string G, string H, string BDate, string EDate, int limit, int page, string sMan, string Flag)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@lnNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnE", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnF", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnG", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnH", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,10),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)};
            parameters[0].Value = No;
            parameters[1].Value = Name;
            parameters[2].Value = A;
            parameters[3].Value = B;
            parameters[4].Value = C;
            parameters[5].Value = D;
            parameters[6].Value = E;
            parameters[7].Value = F;
            parameters[8].Value = G;
            parameters[9].Value = H;
            parameters[10].Value = BDate;
            parameters[11].Value = EDate;
            parameters[12].Value = limit;
            parameters[13].Value = page;
            parameters[14].Value = sMan;
            parameters[15].Value = Flag;
            parameters[16].Value = "";

            DataSet DS = DBHelper.RunProcedureForDS("P_GetBaseInfoForPage", parameters);
            DataTable dt = DS.Tables[0];
            return dt;
        }

        //对基础数据相关操作
        public static string OPBaseInfoList(string No, string Name, string A, string B, string C, string D, string E, string F, string G, string H, string sMan, string sComp, string Flag, string Remark, string IP)
        {
            string iFlag = string.Empty;
            string sSQL1 = string.Empty;
            string sSQL2 = string.Empty;
            string sstr = string.Empty;

            //记录日志的参数
            string Message = string.Empty;      //日志信息
            string LogType = string.Empty;      //日志类型
            string NewValue = string.Empty;     //新数据
            string OldValue = string.Empty;     //旧数据
            string ModuleName = string.Empty;   //操作模块
            string TableName = string.Empty;    //表名
            string Condition = string.Empty;    //查询条件
            string Column = string.Empty;       //列名

            if (Flag == "1") // 新增
            {

                string sMaxNo = DBHelper.GetMaxNo("T_SoftwareVersion", "VNo");
                if (string.IsNullOrEmpty(sMaxNo))
                {
                    A = "RJBB0001";
                }
                else
                {
                    int iMax = int.Parse(sMaxNo.Substring(4)) + 1;
                    int len = 4 - iMax.ToString().Length;
                    A = "RJBB" + CreateAllNo.GetZoreNumString(len) + iMax.ToString();
                }

                sSQL1 = "INSERT INTO T_SoftwareVersion(VNo, Version,VersionType,VersionDesc,CompanyNo, InMan, Remark) VALUES ('" + A + "','" + No + "','" + C + "','" + Name + "','" + sComp + "','" + sMan + "','" + Remark + "')";

                Message = "添加软件版本";
                LogType = "添加";
                ModuleName = B;
                TableName = "T_SoftwareVersion";
                Condition = "VNo = '" + A + "'";
                Column = "*";
            }
            else if (Flag == "2") // 修改
            {
                sSQL1 = "UPDATE T_SoftwareVersion SET  Version='" + No + "',VersionType='" + C + "',VersionDesc = '" + Name + "', Remark = '" + Remark + "' WHERE VNo = '" + A + "' ";

                Message = "修改软件版本";
                LogType = "修改";
                ModuleName = B;
                TableName = "T_SoftwareVersion";
                Condition = "VNo = '" + A + "'";
                Column = "*";
            }
            else if (Flag == "3") // 删除
            {
                sSQL1 = "DELETE FROM T_SoftwareVersion  WHERE VNo = '" + No + "' ";

                Message = "删除软件版本";
                LogType = "删除";
                ModuleName = B;
                TableName = "T_SoftwareVersion";
                Condition = "VNo = '" + No + "'";
                Column = "*";
            }

            List<string> links = new List<string>() { sSQL1, sSQL2 };


            //删除、修改操作需要获取操作前的数据
            if (LogType == "删除" || LogType == "修改")
            {
                OldValue = LogHelper.GetValue(TableName, Condition, Column);
            }

            iFlag = DBHelper.ExecuteSqlTranStr(links);

            //新增、修改操作需要获取操作后的数据
            if (LogType == "添加" || LogType == "修改")
            {
                NewValue = LogHelper.GetValue(TableName, Condition, Column);
            }

            //记录操作日志
            if (!iFlag.Contains("Err"))
            {
                LogHelper.LogInfo(LogType, Message, IP, sComp, sMan, ModuleName, NewValue, OldValue);
                iFlag = "Success";
            }
            //记录错误日志
            else
            {
                Message = Message + "法生错误，错误原因：" + iFlag;
                sstr = String.Join("/", links.ToArray());
                LogHelper.LogError(LogType, Message, IP, sComp, sMan, ModuleName, sstr);
                iFlag = "Error";
            }

            return iFlag;
        }
































    }
}
