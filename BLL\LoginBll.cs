﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using DAL;

namespace BLL
{
    public static class LoginBll
    {
        /// <summary>
        /// 登录验证
        /// </summary>
        /// <param name="sLogin">账号</param>
        /// <param name="password">密码</param>
        /// <returns></returns>
        public static DataTable SDLogin(string sLogin, string password)
        {
            return LoginDal.SDLogin(sLogin, password);
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public static DataTable GetList(string strWhere)
        {
            return LoginDal.GetList(strWhere);
           
        }


        /// <summary>
        /// 获取已生成的 Token ，先判断是否过期，
        /// </summary>
        /// <param name="CorpID">企业号唯一号码</param>
        /// <param name="sFlag"> 备用</param>
        /// <returns></returns>
        public static string GetExistToken(string CorpID, string sFlag)
        {
            return LoginDal.GetExistToken(CorpID, sFlag);
        }


        /// <summary>
        ///  插入TokenID
        /// </summary>
        /// <param name="CorpID"></param>
        /// <param name="Secret"></param>
        /// <param name="Token"></param>
        /// <param name="sFlag">1:插入或更新；2：表示清空</param>
        /// <returns></returns>
        public static int InsertUpdateToken(string CorpID, string Secret, string Token, string sFlag)
        {
            return LoginDal.InsertUpdateToken(CorpID, Secret, Token, sFlag);
        }


        /// <summary>
        /// 返回登录人是否有操作模块的权限
        /// </summary>
        /// <param name="InMan"></param>
        /// <param name="Module"></param>
        /// <returns></returns>
        public static string JustOperateRight(string InMan, string Module)
        {
            return LoginDal.JustOperateRight(InMan, Module);
        }


        /// <summary>
        /// 插入用户手工填写的注册信息
        /// </summary>
        /// <param name="Phone"></param>
        /// <param name="WX"></param>
        /// <param name="CompName"></param>
        /// <param name="CMan"></param>
        /// <param name="HY"></param>
        /// <param name="Remark"></param>
        /// <param name="Code"></param>
        /// <param name="Kind"></param>
        /// <returns></returns>
        public static string InsertUserInfo(string Phone, string WX, string CompName, string Login, string CMan, string HY, string Remark, string Code, string Kind, string Comp)
        {
            return LoginDal.InsertUserInfo(Phone, WX, CompName, Login, CMan, HY, Remark, Code, Kind, Comp);
        }












    }
}
