﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>设备保养任务</title>

    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <link href="../css/orderinfo.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <script src="../layui/layui.js" type="text/javascript"></script>
    <link href="../js/skin/layer.css" rel="stylesheet" />

    <script src="../js/layer/layer.js" type="text/javascript"></script>
    <script src="../js/PrdAssist.js" type="text/javascript"></script>

    <link href="../css/XC.css" rel="stylesheet" />

    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }
        //
        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        $('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=28-1' });
                    }
                }
            })
        });

    </script>


    <script type="text/javascript">
        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#table_DeviceMaintainTaskHeadlist',
                id: 'table_DeviceMaintainTaskHeadlistID',
                url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=18',
                height: 'full-80',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'TNo', title: '保养记录编号', width: 150, sort: true },
                    { field: 'MaterNo', title: '分类编号', width: 100, sort: true },
                    { field: 'DeviceNo', title: '设备编码', width: 100, sort: true },
                    { field: 'DeviceName', title: '设备名称', width: 100 },
                    { field: 'DeviceKind', title: '设备分类', width: 80 },
                    { field: 'DeviceDesc', title: '使用场景', width: 150 },
                    { field: 'MaintainReq', title: '保养要求', width: 100 },
                    { field: 'MaintainMonth', title: '保养月份', width: 100 },
                    { field: 'Status', title: '状态', width: 100 },
                    { field: 'DeptNo', title: '部门编号', width: 100 },
                    { field: 'DeptName', title: '部门名称', width: 100 },
                    { field: 'InMan', title: '创建人', width: 90 },
                    { field: 'InDate2', title: '创建时间', width: 150, sort: true },
                    { field: 'op', title: '操作', width: 130, toolbar: '#barDemo', fixed: 'right' }
                ]],
                page: true,


            });


            //监听是否选中操作
            table.on('checkbox(table_DeviceMaintainTaskHeadlist)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);


            });


            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(table_DeviceMaintainTaskHeadlist)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

            });

            //监听单元格编辑
            table.on('edit(table_DeviceMaintainTaskHeadlist)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });


            //监听行工具事件
            table.on('tool(table_DeviceMaintainTaskHeadlist)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值
                if (layEvent === 'del') {
                    if (data.Status != "待保养") {
                        layer.msg('执行中/已完成保养数据，不可以删除！！');
                        return;
                    }

                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("您确定要删除该保养任务吗？ 保养记录编号：")

                    //设置删除的对象
                    $("#hint-value").html(data.TNo)

                    $("#txtDelTNo").val(data.TNo)

                }
                else if (layEvent === 'detail')//显示明细页面,可以新增
                {
                    $('#head-title1').html("保养任务详情");
                    $('#txtAEFlag').val("28");


                    $("#txtTNo").val(data.TNo);
                    $("#txtDCMaterNo").val(data.MaterNo);
                    $("#txtDCDeviceNo").val(data.DeviceNo);
                    $("#txtDCDeviceName").val(data.DeviceName);
                    $("#txtDCDeviceKind").val(data.DeviceKind);


                    $("#txtDCDeptNo").val(data.DeptNo);
                    $("#txtDCDeptName").val(data.DeptName);
                    $("#txtDCDeviceDesc").val(data.DeviceDesc);
                    $("#txtDCCheckReq").val(data.MaintainReq);
                    $("#txtCRStatus").val(data.Status);

                    $("#txtDCMaintainMonth").val(data.MaintainMonth);
                    $("#txtCRInMan").val(data.InMan);
                    $("#txtCRInDate").val(data.InDate);


                    $("#div_warning").html("");
                    $("#div_warning").hide();

                    //弹窗显示保养任务明细页
                    ShowDeviceMaintainTaskDetail(data.TNo);

                    //getCurrentDate();//获取当前日期

                    $('#btn_Device_open').hide()

                    /*document.getElementById('div_DeviceClassInfoSelect').style.display = 'block';*/

                    $("#txtDCDeviceNo").css("width", "90%")

                    $("#ShowOne").css("display", "block")
                    $("#ShowOne-fade").css("display", "block")

                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录

                    $("#div_txtISOK").hide()
                    $("#div_txtText").hide()
                    $("#div_txtDate").hide()
                }

                $("#div_DeviceMaintainPDFList").show()
                DeviceMaintain()

            });


            //  查询表头 --
            $('#DeviceMaintainTaskBut_open').click(function () {

                var sCPNo = $("#txtsTNo").val();  //保养任务编号
                var sCPVer = encodeURI($("#txtSDCMaterNo").val());  //更新


                var Data = '';
                var Params = { No: sCPNo, CPVer: "", Item: "", Status: "", BDate: "", EDate: "", A: sCPVer, B: "", C: "", D: "", E: "" };
                var Data = JSON.stringify(Params);

                table.reload('table_DeviceMaintainTaskHeadlistID', {
                    method: 'post',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=18&Data=' + Data,
                    where: {
                        'No': sCPNo,
                        'name': sCPVer
                    }, page: {
                        curr: 1
                    }
                });
            });

            $("#btn_DeviceMaintainTaskDetailDel").click(function () {
                //向服务端发送禁用指令
                var sCPNo = $("#txtDelTNo").val();
                //var sCPVer = data.CPVer;
                var sFlag = "29-2";

                var Data = '';
                var Params = { No: sCPNo, Name: "", A: "", B: "", C: "", D: "", E: "", F: "", I: "", J: "", K: "", L: "", Kind: "", Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);
                $.ajax({
                    type: "POST",
                    url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('删除成功！');

                            $("#ShowDel").css("display", "none")
                            $("#ShowDel-fade").css("display", "none")

                            $('#DeviceMaintainTaskBut_open').click();  // 重新查询

                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                            layer.msg('工序已用于工艺流程，不能删除！');

                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST1') {
                            layer.msg('工序已用于BOM设计，不能删除！');

                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST2') {
                            layer.msg('工序已用于产品对应标贴，不能删除！');

                        }
                        else {
                            layer.msg('删除失败，请重试！');
                            $('#DeviceMaintainTaskBut_open').click();  // 重新查询
                        }
                    },
                    error: function (data) {
                        layer.msg('删除失败2，请重试！');
                        $('#DeviceMaintainTaskBut_open').click();  // 重新查询
                    }
                });
            })



        });


        // 获取当前日期
        //function getCurrentDate() {
        //    var today = new Date();
        //    var year = today.getFullYear();
        //    var month = today.getMonth() + 1;
        //    //var day = today.getDate();
        //    var currentDate = year + "-" + month;// + "-" + day;
        //    document.getElementById("txtDCMaintainMonth").innerHTML = currentDate;
        //}

        function openDialog(n) {
            if (n == 1) { // 新增保养任务弹窗
                $('#head-title1').html("新增保养任务信息");
                $('#txtAEFlag').val("25");

                $("#txtTNo").val("系统自动产生");
                $("#txtDCDeviceNo").val("");
                $("#txtDCDeviceKind").val("");
                $("#txtDCDeviceName").val("");
                $("#txtCRStatus").val("");

                $("#txtDCMaterNo").val("");
                $("#txtDCDeviceDesc").val("");
                $("#txtDCDeptName").val("");
                $("#txtDCDeptNo").val("");
                $("#txtDCCheckReq").val("");

                $("#txtDCMaintainMonth").val("");
                //$("#txtCRInMan").val("");
                //$("#txtCRInDate").val("");
                $("#txtCRInMan").val($('#txtInMan').val());

                var date = new Date();
                var datetime = date.toLocaleString(); // 获取本地时间
                $("#txtCRInDate").val(datetime);

                $("#div_warning").html("");
                $("#div_warning").hide();

                ShowDeviceMaintainTaskDetail("");
                //getCurrentDate();//获取当前日期

                $('#btn_Device_open').show()

                $("#txtDCDeviceNo").css("width", "50%")
                $("#div_DeviceMaintainPDFList").hide()

                $("#ShowOne").css("display", "block")
                $("#ShowOne-fade").css("display", "block")
            }
            else if (n == 2)//选择设备信息弹窗层
            {
                $('#head-title2').html("选择设备信息");
                $('#txtAEFlag').val("0");
                $('#txtAddKind').val("Check");

                layui.use('table', function () {
                    var table = layui.table,
                        form = layui.form;
                    table.render({
                        elem: '#table_DeviceInfoSelectList',
                        id: 'DeviceInfoSelectListID',
                        url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=15-2',
                        height: 'full-265',
                        cellMinWidth: 80,
                        //toolbar: 'default',//工具栏
                        //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                        count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                        limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                        limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                        cols: [[
                            { type: 'numbers' },
                            { field: 'DeviceNo', title: '设备编号', width: 120 },
                            { field: 'DeviceName', title: '设备名称', width: 200 },
                            { field: 'DeviceDesc', title: '使用场景', width: 100 },
                            { field: 'DeviceSpec', title: '设备规格', width: 100 },
                            { field: 'DeviceKind', title: '设备类型', width: 100 },
                            { field: 'DeptNo', title: '管理部门编号', width: 80 },
                            { field: 'DeptName', title: '管理部门', width: 120 },
                            { field: 'Status', title: '设备状态', width: 80 },
                            { field: 'MaterNo', title: '设备分类编号', width: 100 },
                            { field: 'InventoryCycle', title: '有效期（年）', width: 80 },
                            { field: 'CheckReq', title: '点检要求', width: 100 },
                            { field: 'MaintainReq', title: '保养要求', width: 100 },
                            { field: 'Remark', title: '备注', width: 200 },
                            { field: 'InMan', title: '创建人', width: 90 },
                            { field: 'InDate', title: '创建时间', width: 100 },
                            { field: 'op', title: '操作', width: 130, toolbar: '#barDemo_DeviceInfoDetail', fixed: 'right' }
                        ]],
                        page: true,


                    });


                    //监听行工具事件
                    table.on('tool(table_DeviceInfoSelectList)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                        var data = obj.data, //获得当前行数据
                            layEvent = obj.event; //获得 lay-event 对应的值
                        if (layEvent === 'DeviceSelect') {//选择设备分类后，同步保存保养内容清单

                            $("#txtDCDeviceNo").val(data.DeviceNo);
                            $("#txtDCMaterNo").val(data.MaterNo);

                            $("#txtDCDeviceName").val(data.DeviceName);
                            $("#txtDCDeviceKind").val(data.DeviceKind);


                            $("#txtDCDeptNo").val(data.DeptNo);
                            $("#txtDCDeptName").val(data.DeptName);
                            $("#txtDCDeviceDesc").val(data.DeviceDesc);
                            $("#txtDCCheckReq").val(data.MaintainReq);
                            $("#txtCRStatus").val(data.Status);

                            $("#txtDCMaintainMonth").val(data.MaintainMonth);
                            $("#txtCRInMan").val(data.InMan);
                            $("#txtCRInDate").val(data.InDate);

                            ShowDeviceClassDetailAdd(data.MaterNo);

                            $('#ShowTow').css("display", "none");
                        }
                        else if (layEvent === 'DeviceDetail')//显示明细页面
                        {
                            $('#txtK').html("设备保养信息详情");
                            //$('#txtAEFlag').val("11");

                            $("#txtItemDeviceNo").val(data.DeviceNo);
                            $("#txtItemDeviceName").val(data.DeviceName);
                            $("#txtItemMaterNo").val(data.MaterNo);

                            $("#div_warningDetail").html("");
                            $("#div_warningDetail").hide();
                            //弹窗显示设备分类明细页
                            ShowDeviceClassDetailList(data.MaterNo);

                            $('#ShowThree').css("display", "block");



                            // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                        }

                    });

                    //  查询设备分类
                    $('#btn_DeviceInfo_open').click(function () {

                        var sCPNo = $("#txtSDeviceNo").val();  //
                        var sCPVer = $("#txtSMaterNO").val();  //


                        var Data = '';
                        var Params = { No: sCPNo, CPVer: "", Item: "", Status: "", BDate: "", EDate: "", A: sCPVer, B: "", C: "", D: "", E: "" };
                        var Data = JSON.stringify(Params);

                        table.reload('DeviceInfoSelectListID', {
                            method: 'post',
                            url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=15-2&Data=' + Data,
                            where: {
                                'No': sCPNo,
                                'name': sCPVer
                            }, page: {
                                curr: 1
                            }
                        });
                    });


                });

                //$("#div_warningDeviceClass").html("");
                //$("#div_warningDeviceClass").hide();

                $('#ShowTow').css("display", "block");

            }
        }

        function closeDialog(s) {
            if (s == 1)//关闭选择工单弹层页面
            {

                $("#ShowFour").css("display", "none")
                $("#ShowFour-fade").css("display", "none")

                $("#ShowTow").css("display", "none")

                $("#div_txtISOK").hide()
                $("#div_txtText").hide()
                $("#div_txtDate").hide()
            }
            else if (s == 2)//关闭详细页面
            {
                $("#ShowOne").css("display", "none")
                $("#ShowOne-fade").css("display", "none")
                $("#ShowTow").css("display", "none")
                //document.getElementById('div_OrderInfoSelect').style.display = 'none';

                $('#DeviceMaintainTaskBut_open').click();  // 重新查询
            }
            else {
                $("#ShowThree").css("display", "none")
            }
        }

        //自动更新单据状态
        function GetPrdAssistStatusForNoet(sENo) {
            //var txtENo = $("#txtENo").val();
            sURL = '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistStatusForNo&CFlag=69&CNO=' + sENo;
            var Data = '';
            $.ajax({
                url: sURL,
                data: { Data: Data },
                success: function (data) {                    //数据成功读取后的处理

                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.length > 0) {
                        //sCompNo = parsedJson[0].CompanyNo;
                        //sCompName = parsedJson[0].CompName;
                        $("#txtCRStatus").val(parsedJson[0].Status);
                        //$("#txtFWName").val(parsedJson[0].CompName);
                    }
                },
                error: function (xhr, status, error) {
                    //错误处理
                    $("body").append("<div>读取数据失败：" + error + "</div>");
                }
            });
        }
        /// 显示保养任务明细项信息
        ///daiwanshan
        function ShowDeviceMaintainTaskDetail(sTNo) {

            //var sCPNo = $("#txtCPDNo").val();  //
            //var sCPVer = encodeURI($("#txtCPDVer").val());  //

            var Data = '';
            var Params = {
                No: sTNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "", E: ""
            };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#table_DeviceMaintainTaskDetailList',
                    id: 'DeviceMaintainTaskDetaiID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=18-1&Data=' + Data,
                    height: '260',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers' },
                        { field: 'MItem', title: '序号', width: 40 },
                        { field: 'MaintainTxt', title: '保养内容', width: 230 },
                        { field: 'MaintainResult', title: '保养结果', width: 100 },
                        { field: 'Status', title: '保养状态', width: 100 },
                        { field: 'Type', title: '数据类型', minWidth: 90 },
                        //{ field: 'InDate2', title: '录入时间', width: 150 },
                        { field: 'op', title: '操作', width: 130, toolbar: '#bar_DeviceMaintainDetail', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(table_DeviceMaintainTaskDetailList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值

                    if (layEvent == 'DCCheck') {

                        if (data.Status == "已保养") {
                            layer.msg('已保养完成，不必重复确认！');
                            return;
                        }

                        var sNo = $("#txtTNo").val();
                        var sItemNo = data.MItem;

                        if (data.Type == "布尔型") {
                            $("#div_txtISOK").show()
                        }
                        else if (data.Type == "日期型") {
                            $("#div_txtDate").show()
                        }
                        else if (data.Type == "文本型" || data.Type == "数值型") {
                            $("#div_txtText").show()
                        }

                        $("#ShowFour").css("display", "block")
                        $("#ShowFour-fade").css("display", "block")


                        $("#txtItemType").val(data.Type);
                        $("#txtItemENO").val(sNo);
                        $("#txtItemItemNo").val(sItemNo);

                    }

                });


            });  // layui.use('table', function () {

            document.getElementById('div_table_DeviceMaintainTaskDetailAdd').style.display = 'none';
            document.getElementById('div_table_DeviceMaintainTaskDetailList').style.display = 'block';


            $('#btn_DeviceMaintainTaskDetailSave').hide()
        }

        /// 显示设备分类保养新增信息
        ///daiwanshan
        function ShowDeviceClassDetailAdd(sMaterNo) {

            var Data = '';
            var Params = { No: sMaterNo, CPVer: "", Item: "", Status: "", BDate: "", EDate: "", A: "Maintain", B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#table_DeviceMaintainTaskDetailAdd',
                    id: 'DeviceClassDetailAddID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=16-1&Data=' + Data,
                    height: '260',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers' },
                        { field: 'SNO', title: '序号', width: 40 },
                        { field: 'DCItem', title: '内容编号', width: 100 },
                        { field: 'CheckTxt', title: '保养内容', width: 230 },
                        { field: 'Type', title: '数据类型', width: 100 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate', title: '录入时间', width: 200 }
                        //{ field: 'op', title: '操作', width: 230, toolbar: '#barDemo_Detail', fixed: 'right' }
                    ]],
                    page: true
                });

            });  // layui.use('table', function () {

            document.getElementById('div_table_DeviceMaintainTaskDetailAdd').style.display = 'block';
            document.getElementById('div_table_DeviceMaintainTaskDetailList').style.display = 'none';

            $('#btn_DeviceMaintainTaskDetailSave').show()
        }

        /// 显示设备分类明细项信息
        ///daiwanshan
        function ShowDeviceClassDetailList(sMaterNo) {


            var Data = '';
            var Params = { No: sMaterNo, CPVer: "", Item: "", Status: "", BDate: "", EDate: "", A: "Maintain", B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#DeviceClassDetailList',
                    id: 'DeviceClassDetailID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=16-1&Data=' + Data,
                    height: '260',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers' },
                        { field: 'SNO', title: '序号', width: 40 },
                        { field: 'DCItem', title: '内容编号', width: 100 },
                        { field: 'CheckTxt', title: '保养内容', width: 230 },
                        { field: 'Type', title: '数据类型', width: 100 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate', title: '录入时间', width: 200 }
                        //{ field: 'op', title: '操作', width: 230, toolbar: '#barDemo_Detail', fixed: 'right' }
                    ]],
                    page: true
                });

            });  // layui.use('table', function () {


        }

        function DeviceMaintainPDF() {
            var sTNo = $("#txtTNo").val()
            var sDeviceNo = $("#txtDCDeviceNo").val()
            var sDeviceName = $("#txtDCDeviceName").val()
            var Params = { No: sTNo, Name: "(" + sDeviceNo + ")" + sDeviceName, Item: "", Status: "", A: "DeviceMaintain", B: "", C: "", D: "", Remark: "", Flag: "100" };
            var Data = JSON.stringify(Params);

            $("#DeviceMaintainPDF").attr("disabled", true)

            $.ajax({
                url: "../Service/PrdAssistAjax.ashx?OP=GenerateRecordFile",
                type: "POST",
                data: {
                    Data: Data
                },
                success: function (res) {
                    var parsedJson = jQuery.parseJSON(res);
                    if (parsedJson.Msg == "LoginError") {
                        ErrorMessage("登录超时，请重新登录！", 2000);
                        $("#DeviceMaintainPDF").removeAttr("disabled")
                    } else if (parsedJson.Msg == "ParamsError") {
                        ErrorMessage("请求参数出错，请重试！", 2000);
                        $("#DeviceMaintainPDF").removeAttr("disabled")
                    } else if (parsedJson.Msg == "Success") {  //如过是Success没有生成过就直接生成
                        DeviceMaintainCreatePDF()
                    } else if (parsedJson.Msg == "Y_EXIST") {  //如过是Y_EXIST就说明已经生成过了一次则提示一下是否再次生成PDF
                        layer.confirm('该编号已经生成过，确定再次生成吗？', {
                            btn: ['确定', '取消'], // 按钮
                            cancel: function () {
                                $("#DeviceMaintainPDF").removeAttr("disabled")
                            }
                        }, function () {
                            DeviceMaintainCreatePDF();
                        }, function () {
                            $("#DeviceMaintainPDF").removeAttr("disabled")
                        });
                    }
                },
                error: function () {
                    ErrorMessage("系统出错，请重试！", 2000);
                    $("#DeviceMaintainPDF").removeAttr("disabled")
                    return;
                }
            })
        }

        //设备保养记录生成PDF
        function DeviceMaintainCreatePDF() {
            var sTNo = $("#txtTNo").val()
            var sDeviceNo = $("#txtDCDeviceNo").val()
            var sDeviceName = $("#txtDCDeviceName").val()
            var Params = { No: sTNo, Name: "(" + sDeviceNo + ")" + sDeviceName, Item: "", Status: "", A: "", B: "", C: "", D: "", Remark: "", Flag: "BY" };
            var Data = JSON.stringify(Params);
            if (sTNo == "") {
                ErrorMessage("获取不到保养编号", 2000);
                return;
            }
            if (sDeviceNo == "") {
                ErrorMessage("获取不到设备编号", 2000);
                return;
            }
            if (sDeviceName == "") {
                ErrorMessage("获取不到设备名称", 2000);
                return;
            }

            $("#DeviceMaintainPDF").attr("disabled", true)

            $.ajax({
                url: "../Service/PrdAssistAjax.ashx?OP=GenerateRecordFile",
                type: "POST",
                data: {
                    Data: Data
                },
                success: function (res) {
                    var parsedJson = jQuery.parseJSON(res);
                    if (parsedJson.Msg == "ImportError") {
                        ErrorMessage("模板导入失败，请重试！", 2000);
                    } else if (parsedJson.Msg == "CreateError") {
                        ErrorMessage("生成失败！", 2000);
                    } else if (parsedJson.Msg == "Error") {
                        ErrorMessage("生成失败！" + parsedJson.ExceptionMessage, 2000);
                    } else if (parsedJson.Msg == "Success") {
                        layer.msg("生成成功")
                        DeviceMaintain();
                    }
                    $("#DeviceMaintainPDF").removeAttr("disabled")
                },
                error: function () {
                    ErrorMessage("系统出错，请重试！", 2000);
                    $("#DeviceMaintainPDF").removeAttr("disabled")
                    return;
                }
            })
        }

        function DeviceMaintain() {
            var sTNo = $("#txtTNo").val()
            var Data = '';
            var Params = { No: sTNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: "DeviceMaintain", B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);
            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#DeviceMaintainPDFList',
                    id: 'DeviceMaintainPDFListID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=28&Data=' + Data,
                    height: '280',
                    cellMinWidth: 80,
                    count: 50,
                    limit: 20,
                    limits: [10, 20, 30, 40, 50],
                    cols: [[
                        { type: 'numbers' },
                        { field: 'No', title: '编号', width: 200 },
                        { field: 'Name', title: '名称' },
                        { field: 'InMan', title: '录入人', width: 100 },
                        { field: 'InDate2', title: '录入时间', width: 170 },
                        { field: 'op', title: '操作', width: 100, toolbar: '#bar_DeviceMaintainPDFList', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(DeviceMaintainPDFList)', function (obj) {
                    var data = obj.data,
                        layEvent = obj.event;
                    if (layEvent == 'view') {
                        window.open(data.FilePath)
                    }
                })
            })
        }


        //关闭删除提示框
        function closeDelDialog() {
            $("#ShowDel").css("display", "none")
            $("#ShowDel-fade").css("display", "none")
        }
    </script>


    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

        })


    </script>

    <style type="text/css">

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        /* .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: #bbbcc7;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=60);
        }*/

        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }

        .XCTYSZ input {
            height: 28px;
            width: 90%;
            font-size: 12px;
            border: 1px solid #E5E5E5;
            border-radius: 2px;
            padding: 0px 5px;
            margin-left: 5px
        }

        .XCTYSZ td {
            font-size: 12px;
            color: #808080;
        }
    </style>

</head>
<body>
    <div class="div_find">

        <p>
            <label class="find_labela">保养任务编号：</label> <input type="text" id="txtsTNo" class="find_input" />
            <label class="find_labela">设备编号：</label><input type="text" id="txtSDCMaterNo" class="find_input" />
            <!--<label class="find_labela">描述：</label><input type="text" id="txtSDeviceName" class="find_input" />-->
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="DeviceMaintainTaskBut_open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="javascript:openDialog(1)">添加</button>
        </p>
    </div>

    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="table_DeviceMaintainTaskHeadlist" lay-filter="table_DeviceMaintainTaskHeadlist"></table>

        <script type="text/html" id="barDemo">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detail">详情</button>
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>
        </script>

    </div>

    <!--保养任务详情弹窗层-->
    <div class="XC-modal XC-modal-xl" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1">设备保养任务详情</span>
            <span class="head-close" onclick="closeDialog(2)">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">

                <div style="border-bottom: solid 1px #ccc;padding-bottom:12px;margin-top:12px;margin-bottom:12px">
                    <span style="font-weight: bold; ">设备保养基本信息</span>
                </div>

                <table cellspacing="0" cellpadding="0" border='0' style="width: 98%; " class="XCTYSZ">
                    <tr style="height:40px;">
                        <td style=" width:100px; text-align:right;">
                            保养任务编号
                        </td>
                        <td>
                            <input type="text" id="txtTNo" name="txtTNo" style=" height:30px;" placeholder="系统自动产生" readonly=readonly />
                        </td>
                        <td style=" width:100px;text-align:right;">
                            设备编号
                        </td>
                        <td>
                            <input type="text" id="txtDCDeviceNo" name="txtDCDeviceNo" style=" height:30px;width:50%" readonly=readonly />
                            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="btn_Device_open" onclick="openDialog(2)" style="height:30px;">选择</button>
                        </td>
                        <td style=" width:100px;text-align:right;">
                            设备名称
                        </td>
                        <td>
                            <input type="text" id="txtDCDeviceName" name="txtDCDeviceName" style=" height:30px;" readonly=readonly />
                        </td>
                    </tr>
                    <tr style="height:40px;">
                        <td style=" width:100px;text-align:right;">
                            设备类型
                        </td>
                        <td>
                            <input type="text" id="txtDCDeviceKind" name="txtDCDeviceKind" style=" height:30px;" readonly=readonly />
                        </td>
                        <td style=" width:100px;text-align:right;">
                            部门名称
                        </td>
                        <td>
                            <input type="text" id="txtDCDeptName" name="txtDCDeptName" style=" height:30px;" readonly=readonly />
                        </td>
                        <td style=" width:100px;text-align:right;">
                            部门编号
                        </td>
                        <td>
                            <input type="text" id="txtDCDeptNo" name="txtDCDeptNo" style=" height:30px;" readonly=readonly />
                        </td>
                    </tr>
                    <tr style="height:40px;">
                        <td style=" width:100px;text-align:right;">
                            创建人
                        </td>
                        <td>
                            <input type="text" id="txtCRInMan" name="txtCRInMan" style=" height:30px;" readonly=readonly />
                        </td>
                        <td style=" width:100px;text-align:right;">
                            创建日期
                        </td>
                        <td>
                            <input type="text" id="txtCRInDate" name="txtCRInDate" style=" height:30px;" readonly=readonly />
                        </td>
                        <td style=" width:100px;text-align:right;">
                            设备分类编号
                        </td>
                        <td>
                            <input type="text" id="txtDCMaterNo" name="txtDCMaterNo" style=" height:30px;" readonly=readonly />
                        </td>
                    </tr>
                    <tr style="height:40px;">
                        <td style=" width:100px;text-align:right;">
                            设备使用场景
                        </td>
                        <td>
                            <input type="text" id="txtDCDeviceDesc" name="txtDCDeviceDesc" style=" height:30px;" readonly=readonly />
                        </td>
                        <td style=" width:100px;text-align:right;">
                            保养要求
                        </td>
                        <td>
                            <input type="text" id="txtDCCheckReq" name="txtDCCheckReq" style=" height:30px;" readonly=readonly />
                        </td>
                        <td style=" width:100px;text-align:right;">
                            状态
                        </td>
                        <td>
                            <input type="text" id="txtCRStatus" name="txtCRStatus" style=" height:30px;" readonly=readonly />
                        </td>
                    </tr>
                    <tr style="height:40px;">
                        <td style=" width:100px;text-align:right;">
                            保养月份
                        </td>
                        <td>
                            <input type="text" id="txtDCMaintainMonth" name="txtDCMaintainMonth" style=" height:30px;" readonly=readonly />
                        </td>
                    </tr>
                </table>

                <div style="border-bottom: solid 1px #ccc;padding-bottom:12px;margin-top:12px">
                    <span style="font-weight: bold; ">保养任务明细列表</span>
                </div>

                <div class="wangid_conbox" id="div_table_DeviceMaintainTaskDetailList" style="display:none; ">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="table_DeviceMaintainTaskDetailList" lay-filter="table_DeviceMaintainTaskDetailList"></table>

                    <script type="text/html" id="bar_DeviceMaintainDetail">
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="DCCheck" style="width:90px;">保养数据提交</button>
                    </script>
                </div>

                <div class="wangid_conbox" id="div_table_DeviceMaintainTaskDetailAdd" style="display:none; ">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="table_DeviceMaintainTaskDetailAdd" lay-filter="table_DeviceMaintainTaskDetailAdd"></table>
                </div>

                <div class="wangid_conbox" id="div_DeviceMaintainPDFList">
                    <div style="display:flex;justify-content:space-between;border-bottom: solid 1px #ccc;padding-bottom:3px;margin-top:15px">
                        <span style="font-weight: bold; ">设备保养记录文件</span>
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="DeviceMaintainPDF" onclick="DeviceMaintainPDF()" style="width:70px">生成PDF</button>
                    </div>
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="DeviceMaintainPDFList" lay-filter="DeviceMaintainPDFList"></table>

                    <script type="text/html" id="bar_DeviceMaintainPDFList">
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="view">查看</button>
                    </script>

                </div>

            </div>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="btn_DeviceMaintainTaskDetailSave">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="btn_DeviceMaintainTaskDetailClose" onclick='closeDialog(2)'>关闭</button>
            </div>
        </div>

    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>


    <!--选择设备信息弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowTow">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title2">设备信息搜索</span>
            <span class="head-close" onclick="closeDialog(2)">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <div>
                    <div style=" font-weight: bold; padding-bottom: 10px;margin-bottom:10px;border-bottom: solid 1px #ccc;">
                        查询
                    </div>
                    <form class="XC-Form-inline-block" style="margin:0px;">
                        <div class="XC-Form-Item" style="margin-bottom:2px">
                            <span class="XC-Span-Input-block">设备编号</span>
                            <input type="text" class="XC-Input-block" id="txtSDeviceNo" name="txtSDeviceNo" value="" />
                        </div>
                        <div class="XC-Form-Item">
                            <span class="XC-Span-Input-block">设备分类编号</span>
                            <input type="text" class="XC-Input-block" id="txtSMaterNO" name="txtSMaterNO" value="" />
                        </div>
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="btn_DeviceInfo_open">搜索</button>
                    </form>
                </div>
                <div style=" font-weight: bold;padding-bottom:10px;">
                    设备清单
                </div>
                <div class="wangid_conbox">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="table_DeviceInfoSelectList" lay-filter="table_DeviceInfoSelectList"></table>

                    <script type="text/html" id="barDemo_DeviceInfoDetail">
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="DeviceDetail">详情</button>
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="DeviceSelect">选择</button>
                    </script>
                </div>
            </div>
        </div>
        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="btn_SelectDeviceClassInfoClose" onclick='closeDialog(1)'>关闭</button>
            </div>
        </div>
    </div>


    <!--设备分类保养内容清单弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowThree">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title3">设备保养内容详情</span>
            <span class="head-close" onclick="closeDialog(3)">X</span>
        </div>
        <div class="XC-modal-body">
            <div style=" font-weight: bold;padding-bottom:10px;">
                设备信息
            </div>
            <div class="XC-modal-xl-center">
                <form class="XC-Form-block" style="margin:0px;">
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block" style="width:75px">设备编号</span>
                        <input type="text" class="XC-Input-block" id="txtItemDeviceNo" name="txtItemDeviceNo" readonly=readonly value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block" style="width:75px">设备名称</span>
                        <input type="text" class="XC-Input-block" id="txtItemDeviceName" name="txtItemDeviceName" readonly=readonly value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block" style="width:75px">设备分类编号</span>
                        <input type="text" class="XC-Input-block" id="txtItemMaterNo" name="txtItemMaterNo" readonly=readonly value="" />
                    </div>
                </form>
                <div class="wangid_conbox">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="DeviceClassDetailList" lay-filter="DeviceClassDetailList"></table>
                </div>
            </div>
        </div>
    </div>



    <!--设备分类保养内容结果录入并提交-->
    <div class="XC-modal XC-modal-md" id="ShowFour" style="height:300px;z-index:1004">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title4">设备保养数据提交</span>
            <span class="head-close" onclick="closeDialog(1)">X</span>
        </div>
        <div class="XC-modal-body">
            <div style=" font-weight: bold; padding-bottom: 10px;margin-bottom:10px;border-bottom: solid 1px #ccc;">
                输入保养数据
            </div>
            <form class="XC-Form-block">
                <!--<div class="XC-Form-Item">
                    <span class="XC-Span-Input-block"></span>
                    <input type="checkbox" class="XC-Input-block" id="txtISOK" name="txtISOK" value="是否OK" />
                </div>-->
                <div class="XC-Form-block-Item" id="div_txtISOK">
                    <select class="XC-Select-block" id="txtISOK" name="txtISOK" style="width:100%">
                        <option></option>
                        <option>OK</option>
                        <option>NG</option>
                    </select>
                </div>
                <!--<div class="XC-Form-Item">
                    <span class="XC-Span-Input-block"></span>
                    <input type="checkbox" class="XC-Input-block" id="txtYes" name="txtYes" value="OK" />
                </div>-->
                <div class="XC-Form-block-Item" id="div_txtText">
                    <input type="text" class="XC-Input-block" id="txtText" name="txtText" value="" style=" width: 100%" />
                </div>
                <div class="XC-Form-block-Item" id="div_txtDate">
                    <input type="date" class="XC-Input-block" id="txtDate" name="txtDate" value="" style=" width: 100%" />
                </div>
            </form>
            <input type="text" class="XC-Input-block" id="txtItemType" name="txtItemType" value="" style="display: none; width: 100%" />
            <input type="text" class="XC-Input-block" id="txtItemENO" name="txtItemENO" value="" style="display: none; width: 100%" />
            <input type="text" class="XC-Input-block" id="txtItemItemNo" name="txtItemItemNo" value="" style="display: none; width: 100%" />
        </div>
        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="btn_DeviceMaintainTaskResultCommit">提交</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="btn_DeviceMaintainTaskResultCommitClose" onclick='closeDialog(1)'>关闭</button>
            </div>
        </div>
    </div>
    <div id="ShowFour-fade" class="black_overlay" style="z-index:1002">
    </div>

    <!--操作提示框 删除设备信息-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDelDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelTNo" name="txtDelTNo" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="btn_DeviceMaintainTaskDetailDel">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDelDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>

    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>
</html>