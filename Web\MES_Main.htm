﻿<!DOCTYPE html>
<html>
<head>
    <title>制程无纸化平台</title>

    <link href="css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="css/font-awesome.min.css" type="text/css" />
    <link rel="stylesheet" href="css/indexSRM.css" type="text/css" />
    <link rel="stylesheet" href="css/skins/_all-skins.css" type="text/css" />

    <script type="text/javascript" src="js/jQuery-2.2.0.min.js"></script>
    <script type="text/javascript" src="js/bootstrap.min.js"></script>
    <script type="text/javascript" src="js/index.js"></script>

    <!--生成二维码-->
    <script src="./js/qrcode.js"></script>

    <script type="text/javascript">
        let AppUrl;
        $(function () {
            var sCorpID = "we";  // GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=2&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == "loginError") {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "Login.htm?RA=" + Num;
                    }
                    else {
                        //$('#Usertable').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetUserInfo&sFlag=10' });
                        $('#LoginUser').html(parsedJson.InName);
                        $('#Company').html(parsedJson.COMPANY);
                        AppUrl = parsedJson.AppUrl
                    }
                },
                error: function (data) {
                    var Num = parseInt(Math.random() * 1000);
                    window.location.href = "Login.htm?RA=" + Num;

                }
            })

            var data = getQueryParams()

            var index = parseInt(data.index)

            var url = ["JXC_Main.htm", "/BaseModule/Personal.htm"]
            var title = ["主页", "用户信息"]

            $("#PageNav").attr("src", url[index])
            $("#PageNav").attr("data-id", url[index])
            $("#TitleNav").attr("data-id", url[index])
            $("#TitleNav").html(title[index])
            GetSoftwareVer()
        });

        function getQueryParams() {
            var params = {};
            var queryString = window.location.search.substring(1);
            var queryArray = queryString.split('&');
            for (var i = 0; i < queryArray.length; i++) {
                var pair = queryArray[i].split('=');
                params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
            }
            return params;
        }

        function ExitApp() {
            location.href = "Login.htm"
            sessionStorage.clear()
        }

        function GetSoftwareVer() {
            var Params = { No: "", Name: "", A: "WEB", B: "", C: "", D: "", E: "", F: "", G: "", H: "", BDate: "", EDate: "", };
            var Data = JSON.stringify(Params);

            $.ajax({
                type: "GET",
                url: "../Service/BaseModuleAjax.ashx?OP=GetSoftwareVersionInfoList&CFlag=12&limit=1&page=1&Data=" + encodeURIComponent(Data),
                data: {},
                success: function (res) {
                    var parsedJson = jQuery.parseJSON(res);
                    if (parsedJson.data.length > 0) {
                        $("#VersionNo").html(parsedJson.data[0].Version)
                    }
                }
            })
        }

        function Download() {
            const qrcode = document.getElementById("qrcode")
            qrcode.innerHTML = "";
            new QRCode(qrcode, {
                text: encodeURI(AppUrl), // 二维码内容
                width: 256,  // 宽度
                height: 256, // 高度
                colorDark: "#000000", // 二维码颜色
                colorLight: "#ffffff", // 背景色
                correctLevel: 2 
            });
        }
    </script>

    <style>
        .roll-nav:hover, .tabClose:hover {
            background-color: #f1f4f4 !important;
        }

        .logo:hover, .sidebar-toggle:hover {
            background-color: #0b7937 !important;
        }

        .main-footer {
            height: 40px;
            background-color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05) /* 上阴影 */
        }

        .active-menu-item {
            color: #fff !important;
            background-color: #0c873d !important;
        }

        #content-main {
            background-color: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), /* 柔和的主阴影 */
            0 2px 4px rgba(0, 0, 0, 0.05); /* 更浅的次阴影 */
            width: 100%;
            height: 100%;
            padding: 8px
        }

        .MaterNav {
            margin-left: 5px
        }
    </style>
</head>
<body class="hold-transition skin-blue sidebar-mini" style="overflow:hidden;">

    <div class="wrapper">
        <!--头部信息-->
        <header class="main-header" style="background-color: #0c873d">
            <div class="logo">
                <span class="logo-mini">.</span>
                <span class="logo-lg"><img src="images/logo.png" alt="" height="45px" /> <label id="Company" style=" margin-bottom:0px;"> </label> </span>
            </div>
            <nav class="navbar navbar-static-top">
                <table cellspacing="0" cellpadding="0" border='0' style="width: 100%;">
                    <tr style=" height:50%;">
                        <td width="40px" height="50px">
                            <a class="sidebar-toggle">
                                <span class="sr-only">切换</span>
                            </a>
                        </td>
                        <td height="50px">
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td style=" width:50%; height:25px; font-size:16px; font-weight:bold; color:White; ">
                                        <!--<label id="Company" style=" margin-bottom:0px;"> </label>-->
                                    </td>
                                    <td>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="50%" height="25">
                                    </td>
                                    <td>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td width="250px" height="50px">
                            <ul class="nav navbar-nav">
                                <li class="dropdown messages-menu">
                                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                        <!--                                     <i class="fa fa-envelope-o "></i>
                                        <span class="label label-success" >0</span>-->
                                    </a>
                                </li>
                                <li class="dropdown notifications-menu">
                                    <a href="#" data-toggle="modal" data-target="#myModal" onclick="Download()">
                                        <i class="fa fa-download"></i>
                                    </a>
                                    <!--<a href="app/麦视.apk" download="麦视.apk">
                                        <i class="fa fa-download"></i>
                                    </a>-->
                                </li>
                                <li class="dropdown tasks-menu">
                                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                        <!--                                     <i class="fa fa-flag-o"></i>
                                        <span class="label label-danger">0</span>-->
                                    </a>
                                </li>
                                <li class="dropdown user user-menu">
                                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                        <img src="img/user2-160x160.jpg" class="user-image" alt="User Image">
                                        <span class="hidden-xs" id="LoginUser">User</span>
                                    </a>
                                    <ul class="dropdown-menu pull-right">
                                        <li><a class="menuItem" data-id="userInfo" href="/BaseModule/Personal.htm"><i class="fa fa-user"></i>个人信息</a></li>
                                        <!--                                 <li><a href="javascript:void();"><i class="fa fa-trash-o"></i>清空缓存</a></li>
                                        <li><a href="javascript:void();"><i class="fa fa-paint-brush"></i>皮肤设置</a></li>-->
                                        <li class="divider"></li>
                                        <li><a onclick="ExitApp()"><i class="ace-icon fa fa-power-off"></i>安全退出</a></li>
                                    </ul>
                                </li>
                            </ul>
                        </td>
                    </tr>
                </table>
            </nav>
        </header>

        <!--新增弹窗结构 -->
        <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
            <div class="modal-dialog" role="document" style="width:286px">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel">浏览器扫码下载APP</h4>
                    </div>
                    <div class="modal-body">
                        <div id="qrcode" style="width:100%"></div>
                    </div>
                </div>
            </div>
        </div>


        <!--左边导航-->
        <div class="main-sidebar">
            <div class="sidebar">

                <form action="#" method="get" class="sidebar-form">
                    <!--这里搜索-->
                    <div style="border-bottom:solid 1px #cccccc; height:41px;"></div>
                </form>
                <ul class="sidebar-menu" id="sidebar-menu">
                    <!--<li class="header">导航菜单</li>-->
                </ul>
            </div>
        </div>
        <!--中间内容-->
        <div id="content-wrapper" class="content-wrapper">
            <div class="content-tabs">
                <button class="roll-nav roll-left tabLeft">
                    <i class="fa fa-backward"></i>
                </button>
                <nav class="page-tabs menuTabs">
                    <div class="page-tabs-content" style="margin-left: 0px;">
                        <!--<a href="javascript:;" class="menuTab active MaterNav" data-id="JXC_Main.htm" id="TitleNav" data-modouleid="TitleNav" data-modoulename="控制台>主页"></a>-->

                    </div>
                </nav>
                <button class="roll-nav roll-right tabRight">
                    <i class="fa fa-forward" style="margin-left: 3px;"></i>
                </button>
                <div class="btn-group roll-nav roll-right">
                    <button class="dropdown tabClose" data-toggle="dropdown">
                        页签操作<i class="fa fa-caret-down" style="padding-left: 3px;"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-right">
                        <li><a class="tabReload" href="javascript:void();">刷新当前</a></li>
                        <li><a class="tabCloseCurrent" href="javascript:void();">关闭当前</a></li>
                        <li><a class="tabCloseAll" href="javascript:void();">全部关闭</a></li>
                        <li><a class="tabCloseOther" href="javascript:void();">除此之外全部关闭</a></li>
                    </ul>
                </div>
                <button class="roll-nav roll-right fullscreen"><i class="fa fa-arrows-alt"></i></button>
            </div>
            <div class="content-iframe" style="overflow: hidden; background-color: white;padding:8px">
                <div class="mainContent" id="content-main">
                    <!--<iframe class="LRADMS_iframe " width="100%" height="100%" src="JXC_Main.htm" frameborder="0" data-id="JXC_Main.htm" id="PageNav"></iframe>-->
                </div>
            </div>


            <!--页脚-->
            <footer class="main-footer">
                <div style="padding-left: 25px">
                    Copyright © 2019 深圳纤草技术有限公司 版权所有 <!--<a href="https://beian.miit.gov.cn/" target=_blank rel="nofollow" style="text-decoration:none;color:black">粤ICP备19086409号</a>-->
                </div>
                <div style="padding-right: 25px">
                    <strong>Version:</strong> <span id="VersionNo"></span><!--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>在线人数:</strong><span id="PersonCount">20</span>-->
                </div>
            </footer>
        </div>


    </div>
</body>
</html>