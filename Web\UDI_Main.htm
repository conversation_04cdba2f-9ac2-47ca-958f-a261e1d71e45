﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" >
<head>
    <title>合规管理平台</title>
    
    <link href="css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="css/font-awesome.min.css"type="text/css" />
    <link rel="stylesheet" href="css/indexSRM.css" type="text/css" />
    <link rel="stylesheet" href="css/skins/_all-skins.css"type="text/css" />
    
    <script type="text/javascript" src="js/jQuery-2.2.0.min.js"></script>
    <script type="text/javascript" src="js/bootstrap.min.js"></script>
    <script type="text/javascript" src="js/index.js"></script>
    
    
    <script type="text/javascript">
        $(function() {
            var sCorpID = "we";  // GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=2&CorpID=" + sCorpID,
                success: function(data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == "loginError") {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "Login.htm?RA=" + Num;
                    }
                    else {
                        //$('#Usertable').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetUserInfo&sFlag=10' });
                        $('#LoginUser').html(parsedJson.InName);
                        $('#Company').html(parsedJson.COMPANY);
                    }
                },
                error: function(data) {
                    var Num = parseInt(Math.random() * 1000);
                    window.location.href = "Login.htm?RA=" + Num;

                }
            })
        });
   </script>
    
    
</head>
<body class="hold-transition skin-blue sidebar-mini" style="overflow:hidden;">

    <div class="wrapper">
        <!--头部信息-->
        <header class="main-header" style=" background-image: url('images/top_bg2.jpg')">
            <div class="logo">
                <span class="logo-mini">UDI</span>
                <span class="logo-lg" ><img src="images/logo.png" alt="" height="45px" /> <label id="Company" style=" margin-bottom:0px;"> </label> </span>
            </div>
            <nav class="navbar navbar-static-top">
                   <table  cellspacing="0" cellpadding="0" border='0' style="width: 100%;">
                     <tr style=" height:50%;">
                       <td  width="40px" height="50px">
                         <a class="sidebar-toggle">
                           <span class="sr-only">切换</span>
                         </a>
                       </td>
                       <td height="50px">
                           <table width="100%" border="0" cellspacing="0" cellpadding="0">
                             <tr>
                               <td style=" width:50%; height:25px; font-size:16px; font-weight:bold; color:White; ">
                                 <!--<label id="Company" style=" margin-bottom:0px;"> </label>-->
                               </td>
                               <td>

                               </td>
                             </tr>
                             <tr>
                               <td width="50%" height="25">
                               </td>
                               <td>

                               </td>
                             </tr>
                          </table>
                       </td>
                       <td  width="250px" height="50px">
                         <ul class="nav navbar-nav">
                             <li class="dropdown messages-menu">
                                 <a href="#" class="dropdown-toggle" data-toggle="dropdown">
<!--                                     <i class="fa fa-envelope-o "></i>
                                     <span class="label label-success" >0</span>-->
                                 </a>
                             </li>
                             <li class="dropdown notifications-menu">
                                 <a href="#" class="dropdown-toggle" data-toggle="dropdown">
<!--                                     <i class="fa fa-bell-o"></i>
                                     <span class="label label-warning">0</span>-->
                                 </a>
                             </li>
                             <li class="dropdown tasks-menu">
                                 <a href="#" class="dropdown-toggle" data-toggle="dropdown">
<!--                                     <i class="fa fa-flag-o"></i>
                                     <span class="label label-danger">0</span>-->
                                 </a>
                             </li>
                             <li class="dropdown user user-menu">
                                 <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                     <img src="img/user2-160x160.jpg" class="user-image" alt="User Image">
                                     <span class="hidden-xs" id="LoginUser">User</span>
                                 </a>
                                 <ul class="dropdown-menu pull-right">
                                     <li><a class="menuItem" data-id="userInfo" href="/BaseModule/Personal.htm"><i class="fa fa-user"></i>个人信息</a></li>
<!--                                 <li><a href="javascript:void();"><i class="fa fa-trash-o"></i>清空缓存</a></li>
                                     <li><a href="javascript:void();"><i class="fa fa-paint-brush"></i>皮肤设置</a></li>-->
                                     <li class="divider"></li>
                                     <li><a href="Login.htm"><i class="ace-icon fa fa-power-off"></i>安全退出</a></li>
                                 </ul>
                              </li>
                          </ul>
                       </td>
                     </tr>
                   </table>
            </nav>
        </header>
        <!--左边导航-->
        <div class="main-sidebar">
            <div class="sidebar">

                <form action="#" method="get" class="sidebar-form">
                    <!--这里搜索-->
                    <div style="border:solid 1px gray; height:41px;"></div>
                </form>
                <ul class="sidebar-menu" id="sidebar-menu">
                    <!--<li class="header">导航菜单</li>-->    
                </ul>
            </div>
        </div>
        <!--中间内容-->
        <div id="content-wrapper" class="content-wrapper">
            <div class="content-tabs">
                <button class="roll-nav roll-left tabLeft">
                    <i class="fa fa-backward"></i>
                </button>
                <nav class="page-tabs menuTabs">
                    <div class="page-tabs-content" style="margin-left: 0px;">
                        <a href="javascript:;" class="menuTab active" data-id="JXC_Main.htm">主页</a>

                    </div>
                </nav>
                <button class="roll-nav roll-right tabRight">
                    <i class="fa fa-forward" style="margin-left: 3px;"></i>
                </button>
                <div class="btn-group roll-nav roll-right">
                    <button class="dropdown tabClose" data-toggle="dropdown">
                        页签操作<i class="fa fa-caret-down" style="padding-left: 3px;"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-right">
                        <li><a class="tabReload" href="javascript:void();">刷新当前</a></li>
                        <li><a class="tabCloseCurrent" href="javascript:void();">关闭当前</a></li>
                        <li><a class="tabCloseAll" href="javascript:void();">全部关闭</a></li>
                        <li><a class="tabCloseOther" href="javascript:void();">除此之外全部关闭</a></li>
                    </ul>
                </div>
                <button class="roll-nav roll-right fullscreen"><i class="fa fa-arrows-alt"></i></button>
            </div>
            <div class="content-iframe" style="overflow: hidden;">
                <div class="mainContent" id="content-main" style="margin: 0px; margin-bottom: 0px; padding: 0px; padding-left:5px; background-color: white; border-right: solid 1px #26d0a1; ">
                    <iframe class="LRADMS_iframe" width="100%" height="100%" src="JXC_Main.htm" frameborder="0" data-id="JXC_Main.htm"></iframe>
                </div>
            </div>
        </div>
    </div>
</body>
</html>