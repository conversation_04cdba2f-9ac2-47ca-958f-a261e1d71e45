﻿using Common;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Text;
using System.IO;

namespace DAL
{
    public class DHRDal
    {
        /// 判断信息是否存在 <summary>
        /// 判断信息是否存在
        /// </summary>
        /// <param name="Kind"></param>
        /// <param name="KindList"></param>
        /// <param name="QT"></param>
        /// <param name="sComp"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string JudgeObjectExist(string Kind, string KindList, string QT, string sComp, string sFlag)
        {
            string sSQL = string.Empty;
            string sStatus = string.Empty;
            DataTable dt = new DataTable();

            if (sFlag == "1") // 判断工单是否被关闭
            {
                sSQL = " select InMan from T_OrderInfo where OrderNo='" + Kind + "' and PrdctStatus in ('已完成','已关闭') and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "2")  // 判断序列号是否被关闭
            {
                sSQL = " select InMan from T_SerielInfo where OrderNo='" + Kind + "' and SerielNo in (" + KindList + ") and Status = '已完成'  and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "3")  // 判断工序是否已完成
            {
                sSQL = " select * from T_ProductBatchInfo where OrderNo= '" + Kind + "' and BatchNo ='" + KindList + "' and FlowOrder='" + QT + "'  and Status = '已完成' and CompanyNo= '" + sComp + "' ";
            }
            else if (sFlag == "4")  // 记录在线更改判断是不是维修工序
            {
                sSQL = " select top 1 b.ProcAct from T_SerialBaseFlow a JOIN T_ProcedureAction b on a.ConditionNo=b.PANo " +
                    " where a.OrderNo= '" + Kind + "' and a.SerialNo ='" + KindList + "' and a.FlowOrder='" + QT + "' and b.ProcAct='维修' and a.CompanyNo= '" + sComp + "'  ";
            }
            else if (sFlag == "5")  // 找到维修工序对应得不合格处理单
            {
                sSQL = " SELECT TOP 1 PECode FROM (" +
                            " select OrderNo,BatchNo,ProcedureNo,EXENo from T_ProductBatchInfo where OrderNo='" + Kind + "' and BatchNo ='" + KindList + "' and FlowOrder=CONVERT(INT,'" + QT + "')-1 " +
                       " ) a JOIN T_PrdExceptionInfo b " +
                       " on a.OrderNo = b.OrderNo and a.BatchNo=b.BatchNo and a.ProcedureNo=b.ProcedureNo and a.EXENo = b.EXENo " +
                       " WHERE CompanyNo='" + sComp + "' ";
                dt = DBHelper.GetDataTable(sSQL);
                if (dt.Rows.Count > 0)
                {
                    return dt.Rows[0]["PECode"].ToString();
                }
                else
                {
                    return "";
                }
            }
            else if (sFlag == "6")  // 找到维修工序得顺序号
            {
                sSQL = " SELECT CONVERT(INT,MAX(FlowOrder))+1 as FlowOrder from T_ProductBatchInfo where BatchNo='" + KindList + "' and OrderNo='" + Kind + "' and ProcedureNo='" + QT + "' AND CompanyNo = '" + sComp + "' ";
                dt = DBHelper.GetDataTable(sSQL);
                if (dt.Rows.Count > 0)
                {
                    return dt.Rows[0]["FlowOrder"].ToString();
                }
                else
                {
                    return "";
                }  
            }

            dt = DBHelper.GetDataTable(sSQL);
            if (dt.Rows.Count > 0)
            {
                sStatus = "Y";
            }
            else
            {
                sStatus = "N";
            }

            return sStatus;
        }


        public static DataTable GetExportConditions(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string Remark, string Flag, string sMan, string sComp, string IP)
        {
            string sSQL = string.Empty;
            DataTable dt = new DataTable();
            if (Flag == "18-1")//关闭序列号生成dhr(单个、多个)
            {
                try
                {
                    sSQL = "select EXENo,OrderNo,BatchNo as SerialNo,ProcedureName,ProcedureNo,ProcedureVer,FlowOrder,ProductNo,'DHR' OPFlag " +
                       "from T_ProductBatchInfo a where BatchNo in (" + No + ") and OrderNo='" + Item + "' and Status='生产中' and CompanyNo='" + sComp + "' " +
                       "and NOT EXISTS(select 1 from T_DHRFile b where b.OrderNo=a.OrderNo and b.SerialNo=a.BatchNo and b.FlowOrder=a.FlowOrder and b.Remark='DHR' and b.CreateWay = '生产执行' ) " +
                       "UNION  all " +
                      "select '' as EXENo,OrderNo,SerielNo as SerialNo,'' ProcedureName,'' ProcedureNo,'' ProcedureVer,0 FlowOrder,'' ProductNo,'HZ' OPFlag " +
                        "from T_SerielInfo a where SerielNo in (" + No + ") and OrderNo='" + Item + "' and Status in ('','生产中','未开始') and CompanyNo='" + sComp + "' " +
                        "and NOT EXISTS(select 1 from T_DHRFile b where b.OrderNo=a.OrderNo and b.SerialNo=a.SerielNo and b.Remark='HZ' and b.CreateWay = '生产执行' ) ";
                    dt = DBHelper.GetDataTable(sSQL);
                }
                catch (Exception ex)
                {
                    LogHelper.LogError("获取", "关闭序列号获取需要生成序列号失败！序列号：" + No + "  原因：" + ex.Message.ToString(), IP, sComp, sMan, Name, sSQL);
                    throw ex;
                }

            }
            else if (Flag == "3")//关闭工单生成dhr
            {
                try
                {
                    sSQL = "select EXENo,OrderNo,BatchNo as SerialNo,ProcedureName,ProcedureNo,ProcedureVer,FlowOrder,ProductNo,'DHR' OPFlag " +
                        "from  T_ProductBatchInfo a  where OrderNo='" + Item + "' and Status='生产中' and CompanyNo='" + sComp + "' " +
                        "and NOT EXISTS(select 1 from T_DHRFile b where b.OrderNo=a.OrderNo and b.SerialNo=a.BatchNo and b.FlowOrder=a.FlowOrder and b.Remark='DHR' and b.CreateWay = '生产执行' ) " +
                        "UNION  all " +
                        "select '' as EXENo,OrderNo,SerielNo as SerialNo,'' ProcedureName,'' ProcedureNo,'' ProcedureVer,0 FlowOrder,'' ProductNo,'HZ' OPFlag " +
                        "from T_SerielInfo a where OrderNo='" + Item + "' and Status in ('','生产中','未开始') and CompanyNo='" + sComp + "' " +
                        "and NOT EXISTS(select 1 from T_DHRFile b where b.OrderNo=a.OrderNo and b.SerialNo=a.SerielNo and b.Remark='HZ' and b.CreateWay = '生产执行' ) ";
                    dt = DBHelper.GetDataTable(sSQL);
                }
                catch (Exception ex)
                {
                    LogHelper.LogError("获取", "关闭工单获取需要生成序列号失败！工单号：" + Item + "  原因：" + ex.Message.ToString(), IP, sComp, sMan, Name, sSQL);
                    throw ex;
                }
            }
            else if (Flag == "4-1")//批量作业多个工序完工生成
            {
                try
                {
                    int sFlowOrder = DBHelper.GetScalar("select ISNULL(max(FlowOrder),0) from T_SerialBaseFlow where SerialNo='" + No + "' and OrderNo='" + Item + "' ");

                    sSQL = "select EXENo,OrderNo,BatchNo as SerialNo,ProcedureName,ProcedureNo,ProcedureVer,FlowOrder,ProductNo,'DHR' OPFlag  " +
                    "from T_ProductBatchInfo a " +
                    "where BatchNo in (select BatchNo from T_MaterConsumeInfo WHERE MaterBatchNo='" + A + "') and ProcedureNo='" + B + "' and FlowOrder='" + E + "' " +
                    "and NOT EXISTS(select 1 from T_DHRFile b where b.OrderNo=a.OrderNo and b.SerialNo=a.BatchNo and b.FlowOrder=a.FlowOrder and b.Remark='DHR' and b.CreateWay = '生产执行' ) " +
                    "union all " +
                    "select EXENo,OrderNo,BatchNo as SerialNo,'' ProcedureName,'' ProcedureNo,'' ProcedureVer,0 FlowOrder,'' ProductNo,'HZ' OPFlag " +
                    "from T_ProductBatchInfo a " +
                    "where BatchNo in (select BatchNo from T_MaterConsumeInfo WHERE MaterBatchNo='" + A + "' ) and ProcedureNo='" + B + "'  and FlowOrder = '" + sFlowOrder + "' " +
                    "and NOT EXISTS(select 1 from T_DHRFile b where b.OrderNo=a.OrderNo and b.SerialNo=a.BatchNo and b.Remark='HZ' and b.CreateWay = '生产执行' ) ";

                    dt = DBHelper.GetDataTable(sSQL);
                }
                catch (Exception ex)
                {
                    LogHelper.LogError("获取", "批量生成获取需要生成序列号失败！批号：" + A + " 原因：" + ex.Message.ToString(), IP, sComp, sMan, Name, sSQL);
                    throw ex;
                }
            }
            else if (Flag == "5-1")//单个工序完工生成
            {
                try
                {
                    sSQL = "select EXENo,OrderNo,BatchNo as SerialNo,ProcedureName,ProcedureNo,ProcedureVer,FlowOrder,ProductNo,'DHR' OPFlag " +
                     "from T_ProductBatchInfo a where OrderNo='" + Item + "' and BatchNo='" + No + "' and ProcedureNo='" + B + "' and FlowOrder='" + E + "' and CompanyNo='" + sComp + "' " +
                     "and NOT EXISTS(select 1 from T_DHRFile b where b.OrderNo=a.OrderNo and b.SerialNo=a.BatchNo and b.FlowOrder=a.FlowOrder and b.Remark='DHR' and b.CreateWay in ('生产执行','记录在线更改') ) " +
                     "union all " +
                     "select EXENo,OrderNo,BatchNo as SerialNo,'' ProcedureName,'' ProcedureNo,'' ProcedureVer,0 FlowOrder,'' ProductNo,'HZ' OPFlag " +
                     "from T_ProductBatchInfo a where OrderNo='" + Item + "' and BatchNo='" + No + "' and ProcedureNo='" + B + "'  and " +
                     "FlowOrder = (select max(FlowOrder) from T_SerialBaseFlow where OrderNo='" + Item + "' and SerialNo='" + No + "' and CompanyNo='" + sComp + "') " +
                     "and NOT EXISTS(select 1 from T_DHRFile b where b.OrderNo=a.OrderNo and b.SerialNo=a.BatchNo and b.Remark='HZ' and b.CreateWay in ('生产执行','记录在线更改') ) ";
                    dt = DBHelper.GetDataTable(sSQL);
                }
                catch (Exception ex)
                {
                    LogHelper.LogError("获取", "单个生成获取需要生成序列号失败！序列号：" + No + "，工序：" + B + " 原因：" + ex.Message.ToString(), IP, sComp, sMan, Name, sSQL);
                    throw ex;
                }
            }
            else if (Flag == "6-1")//提交不良获取需要生的序列号
            {
                try
                {
                    sSQL = "select EXENo,OrderNo,BatchNo as SerialNo,ProcedureName,ProcedureNo,ProcedureVer,FlowOrder,ProductNo,'DHR' OPFlag " +
                     "from T_ProductBatchInfo a where OrderNo='" + Item + "' and BatchNo='" + No + "' and ProcedureNo='" + B + "' and FlowOrder='" + E + "' and CompanyNo='" + sComp + "' " +
                     "and NOT EXISTS(select 1 from T_DHRFile b where b.OrderNo=a.OrderNo and b.SerialNo=a.BatchNo and b.FlowOrder=a.FlowOrder and b.Remark='DHR' and b.CreateWay = '生产执行' ) ";
                    dt = DBHelper.GetDataTable(sSQL);
                }
                catch (Exception ex)
                {
                    LogHelper.LogError("获取", "不良提交生成获取需要生成序列号失败！序列号：" + No + "，工序：" + B + " 原因：" + ex.Message.ToString(), IP, sComp, sMan, Name, sSQL);
                    throw ex;
                }
            }
            else if (Flag == "7-1")//抽检批量作业多个工序完工生成
            {
                try
                {
                    sSQL = "select a.EXENo,a.OrderNo,a.BatchNo as SerialNo,ProcedureName,a.ProcedureNo,a.ProcedureVer,a.FlowOrder,ProductNo,'DHR' OPFlag  " +
                    " from T_ProductBatchInfo a  join  T_MaterConsumeInfo b on a.OrderNo=b.OrderNo and a.BatchNo=b.BatchNo and a.ProcedureNo=b.ProcedureNo and a.ProcedureVer=b.ProcedureVer and a.FlowOrder=b.FlowOrder " +
                    " where b.MaterBatchNo='" + A + "' " +
                    " and NOT EXISTS(select 1 from T_DHRFile c where c.OrderNo=a.OrderNo and c.SerialNo=a.BatchNo and c.FlowOrder=a.FlowOrder and c.Remark='DHR' and c.CreateWay = '生产执行' ) " +
                    "union all " +
                    " select a.EXENo,a.OrderNo,a.BatchNo as SerialNo,'' ProcedureName,'' ProcedureNo,'' ProcedureVer,0 FlowOrder,'' ProductNo,'HZ' OPFlag " +
                    " from T_ProductBatchInfo a  join  T_MaterConsumeInfo b on a.OrderNo=b.OrderNo and a.BatchNo=b.BatchNo and a.ProcedureNo=b.ProcedureNo and a.ProcedureVer=b.ProcedureVer and a.FlowOrder=b.FlowOrder " +
                    " where b.MaterBatchNo='" + A + "' and a.FlowOrder = (select max(FlowOrder) from T_SerialBaseFlow where OrderNo=a.OrderNo and SerialNo=a.BatchNo) " +
                    " and NOT EXISTS(select 1 from T_DHRFile c where c.OrderNo=a.OrderNo and c.SerialNo=a.BatchNo and c.Remark='HZ' and c.CreateWay = '生产执行' ) ";

                    dt = DBHelper.GetDataTable(sSQL);
                }
                catch (Exception ex)
                {
                    LogHelper.LogError("获取", "批量生成获取需要生成序列号失败！批号：" + A + " 原因：" + ex.Message.ToString(), IP, sComp, sMan, Name, sSQL);
                    throw ex;
                }
            }
            else if (Flag == "29-6-2" || Flag == "29-6-3")//测试项修改获取需要生的序列号
            {
                try
                {
                    string sBatchNo = string.Empty;
                    sSQL = "select top 1 MaterBatchNo from T_MaterConsumeInfo where OrderNo='" + Item + "' and BatchNo='" + No + "' and ProcedureNo='" + B + "' and FlowOrder='" + E + "' and CompanyNo='" + sComp + "' ";
                    dt = DBHelper.GetDataTable(sSQL);
                    if (dt.Rows.Count > 0)
                    {
                        sBatchNo = dt.Rows[0]["MaterBatchNo"].ToString();
                    }

                    sSQL = "select EXENo,OrderNo,BatchNo as SerialNo,ProcedureName,ProcedureNo,ProcedureVer,FlowOrder,ProductNo,'DHR' OPFlag,'" + sBatchNo + "' as MaterBatchNo  " +
                     "from T_ProductBatchInfo a where OrderNo='" + Item + "' and BatchNo='" + No + "' and ProcedureNo='" + B + "' and FlowOrder='" + E + "' and CompanyNo='" + sComp + "' ";
                    dt = DBHelper.GetDataTable(sSQL);
                }
                catch (Exception ex)
                {
                    LogHelper.LogError("获取", "记录在线更改获取需要生成序列号失败！序列号：" + No + "，工序：" + B + " 原因：" + ex.Message.ToString(), IP, sComp, sMan, Name, sSQL);
                    throw ex;
                }
            }
            else if (Flag == "30-2-6" || Flag == "30-2-8")//维修生成DHR
            {
                try
                {
                    int sFlowOrder = DBHelper.GetScalar("select CONVERT(int,max(FlowOrder))+1 from T_ProductBatchInfo where BatchNo='" + No + "' and OrderNo='" + Item + "' and ProcedureNo='" + B + "'");

                    sSQL = "select OrderNo,BatchNo as SerialNo,ProcedureName,ProcedureNo,ProcedureVer,FlowOrder,ProductNo,'" + F + "' BHGNo,'WX' OPFlag " +
                          "from T_ProductBatchInfo where OrderNo='" + Item + "' and BatchNo='" + No + "' and FlowOrder='" + sFlowOrder + "' and CompanyNo='" + sComp + "' order by FlowOrder ";

                    dt = DBHelper.GetDataTable(sSQL);
                }
                catch (Exception ex)
                {
                    LogHelper.LogError("获取", "维修生成获取需要生成序列号失败！序列号：" + No + "，工序：" + B + " 原因：" + ex.Message.ToString(), IP, sComp, sMan, Name, sSQL);
                    throw ex;
                }
            }

            else if (Flag == "CreateDHR")
            {
                sSQL = " SELECT top 10 a.MaterBatchNo,a.EXENo,a.OrderNo,a.BatchNo as SerialNo,a.ProductNo,a.ProcedureName,a.ProcedureNo,a.ProcedureVer,a.FlowOrder,(case when a.ProcedureNo='WX' then 'WX' else 'DHR' end) as OPFlag " +
                    " from ( " +
                    " SELECT p.EXENo,p.OrderNo,p.BatchNo,p.ProductNo,p.ProcedureNo,p.ProcedureVer,p.ProcedureName,p.FlowOrder,m.MaterBatchNo " +
                    " FROM T_ProductBatchInfo p left join T_MaterConsumeInfo m on p.OrderNo=m.OrderNo and p.BatchNo=m.BatchNo and p.ProcedureNo=m.ProcedureNo and p.ProcedureVer=m.ProcedureVer and p.FlowOrder=m.FlowOrder  and m.ConsumeFlag NOT IN ('物料消耗','装箱检查') " +
                    " WHERE p.OrderNo='" + Item + "' AND p.Status='已完成' " +
                    " ) a LEFT JOIN ( " +
                    " SELECT FileNo,OrderNo,SerialNo,ProcedureNo,ProcedureName,FlowOrder FROM T_DHRFile WHERE OrderNo='" + Item + "'  AND Remark='DHR' " +
                    " ) b on a.OrderNo=b.OrderNo AND a.BatchNo=b.SerialNo AND a.ProcedureNo=b.ProcedureNo AND a.FlowOrder=b.FlowOrder " +
                    " WHERE FileNo IS NULL ";

                dt = DBHelper.GetDataTable(sSQL);
            }

            else if (Flag == "CreateHZ")
            {
                sSQL = " SELECT top 10 '' as EXENo,a.OrderNo,a.SerialNo,'' as ProcedureName,'' as ProcedureNo,'' as ProcedureVer,0 as FlowOrder,'' as ProductNo,'HZ' as OPFlag " +
                    " FROM ( " +
                    " SELECT OrderNo,SerialNo FROM ( " +
                    " SELECT a.OrderNo,a.SerialNo,MAX(a.FlowOrder) as SFlowOrder,max(b.FlowOrder) as PFlowOrder " +
                    " FROM T_SerialBaseFlow a left join T_ProductBatchInfo b on a.OrderNo=b.OrderNo and a.SerialNo=b.BatchNo and a.ProcedureNo=b.ProcedureNo and a.ProcedureVer = b.ProcedureVer and a.FlowOrder = b.FlowOrder " +
                    " WHERE a.OrderNo='" + Item + "' group by a.OrderNo,a.SerialNo " +
                    " ) as c where SFlowOrder=PFlowOrder " +
                    " ) a LEFT JOIN ( " +
                    " SELECT FileNo,OrderNo,SerialNo FROM T_DHRFile WHERE OrderNo='" + Item + "' AND Remark='HZ' " +
                    " ) b on a.OrderNo=b.OrderNo AND a.SerialNo=b.SerialNo  " +
                    " WHERE FileNo IS NULL ";

                dt = DBHelper.GetDataTable(sSQL);
            }

            else if (Flag == "9")
            {
                //找出所有的组件
                try
                {
                    SqlParameter[] parameters = {
                    new SqlParameter("@lnNo", SqlDbType.NVarChar,2000),
                    new SqlParameter("@lnItem", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnStatus", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnE", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnF", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,30),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)
                };
                    parameters[0].Value = No;
                    parameters[1].Value = Item;
                    parameters[2].Value = Name;
                    parameters[3].Value = MNo;
                    parameters[4].Value = MName;
                    parameters[5].Value = "";
                    parameters[6].Value = "";
                    parameters[7].Value = "";
                    parameters[8].Value = A;
                    parameters[9].Value = B;
                    parameters[10].Value = C;
                    parameters[11].Value = D;
                    parameters[12].Value = "Merge";
                    parameters[13].Value = F;
                    parameters[14].Value = 1;
                    parameters[15].Value = 20;
                    parameters[16].Value = sMan;
                    parameters[17].Value = Flag;
                    parameters[18].Value = "";
                    DataSet DS = DBHelper.RunProcedureForDS("P_GetDHRInfoForPage", parameters);
                    dt = DS.Tables[0];
                }
                catch (Exception ex)
                {
                    //写入日志
                    LogHelper.LogError("获取", "序列号合并获取合并的文件发生错误！" + No + " 原因：" + ex.Message.ToString(), IP, sComp, sMan, Name);
                    throw ex;
                }
            }
            else if (Flag == "10")
            {
                try
                {
                    //DHR查询 更具选择的文件编码找出对应的文件
                    sSQL = "select RPath+FileName FilePath from T_DHRFile where FileNo in (" + A + ") order by InDate,CONVERT(int,FlowOrder)";
                    dt = DBHelper.GetDataTable(sSQL);
                }
                catch (Exception ex)
                {
                    //写入日志
                    LogHelper.LogError("获取", "自定义合并获取合并的文件发生错误！ 原因：" + ex.Message.ToString(), IP, sComp, sMan, Name, sSQL);
                    throw ex;
                }
            }
            else if (Flag == "10")
            {

            }

            //else if (Flag == "8")
            //{
            //    //更具文件批号生成
            //    sSQL = "select distinct FileBatchNo from T_DHRFile where BNo = '" + No + "'";
            //    dt = DBHelper.GetDataTable(sSQL);
            //}
            //else if (A == "")
            //{    //暂时用不上
            //    sSQL = "select OrderNo,BatchNo as SerialNo,ProcedureName,ProcedureNo,ProcedureVer,FlowOrder,ProductNo,'DHR' OPFlag from T_ProductBatchInfo " +
            //          " where BatchNo in (select BatchNo from T_MaterConsumeInfo WHERE MaterBatchNo='" + sNo + "') and CompanyNo='" + Comp + "' order by FlowOrder ";

            //    DataTable sdt1 = DBHelper.GetDataTable(sSQL);

            //    //查询汇总页
            //    sSQL = "select OrderNo,BatchNo as SerialNo,'' ProcedureName,'' ProcedureNo,'' ProcedureVer,0 FlowOrder,'' ProductNo,'HZ' OPFlag " +
            //        "from T_ProductBatchInfo  where BatchNo in (select BatchNo from T_MaterConsumeInfo WHERE MaterBatchNo='" + sNo + "') and CompanyNo='" + Comp + "' " +
            //        "group by OrderNo,BatchNo order by FlowOrder";

            //    DataTable sdt2 = DBHelper.GetDataTable(sSQL);

            //    DataTable mergedDataTable = sdt1.Clone();
            //    mergedDataTable.Merge(sdt1);
            //    mergedDataTable.Merge(sdt2);


            //    sSQL = "select OrderNo,BatchNo as SerialNo,MaterBatchNo,ProcedureNo,'' ProcedureName,ProcedureVer,FlowOrder,'DHR' OPFlag  " +
            //       "from T_MaterConsumeInfo  where ConsumeFlag='物料消耗' ";

            //    DataTable sdt3 = new DataTable();
            //    DataTable res = new DataTable();

            //    DataTable distinctDataTable = sdt2.DefaultView.ToTable(true, "SerialNo");

            //    foreach (DataRow row in distinctDataTable.Rows)
            //    {
            //        sdt3 = DBHelper.GetDataTable(sSQL);
            //        sdt3.Rows.Add(null, null, row["SerialNo"].ToString(), null, null, null, null, null);
            //        res = DHRHelper.ConvertDataTableToTree(sdt3, row["SerialNo"].ToString());
            //        mergedDataTable.Merge(res);
            //    }

            //    list.Add(mergedDataTable);
            //}

            return dt;
        }


        // 获取生成DHR需要用到的数据
        public static Dictionary<string, object> GetExportData(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string Remark, string Flag, string sMan, string sComp, string IP)
        {
            string sSQL = string.Empty;
            string sPath = string.Empty;
            DataTable sdt = new DataTable();
            Dictionary<string, object> dic = new Dictionary<string, object>();
            try
            {
                //获取签名图片
                sSQL = " select SysValue from T_SysConfig where SysKind='签名图片路径' and CompanyNo='" + sComp + "' ";
                sdt = DBHelper.GetDataTable(sSQL);
                if (sdt.Rows.Count > 0)
                {
                    sPath = sdt.Rows[0]["SysValue"].ToString();
                }
            }
            catch (Exception ex)
            {
                //写入日志
                LogHelper.LogError("获取", "未能获取到签名图片！工单：" + Item + "，序列号：" + No + "，工序：" + A + "，顺序号：" + E + " 原因：" + ex.Message.ToString(), IP, sComp, sMan, Name, sSQL);
                return dic;
            }

            try
            {
                SqlParameter[] parameters = {
                    new SqlParameter("@lnNo", SqlDbType.NVarChar,2000),
                    new SqlParameter("@lnItem", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnStatus", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnE", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnF", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,30),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)
                };
                parameters[0].Value = No;
                parameters[1].Value = Item;
                parameters[2].Value = Name;
                parameters[3].Value = MNo;
                parameters[4].Value = MName;
                parameters[5].Value = "";
                parameters[6].Value = "";
                parameters[7].Value = "";
                parameters[8].Value = A; // A
                parameters[9].Value = B;
                parameters[10].Value = C;
                parameters[11].Value = D;
                parameters[12].Value = E;
                parameters[13].Value = F;
                parameters[14].Value = 1;
                parameters[15].Value = 20;
                parameters[16].Value = sMan;
                parameters[17].Value = Flag;
                parameters[18].Value = "";

                DataSet DS = DBHelper.RunProcedureForDS("P_GetDHRInfoForPage", parameters);
                foreach (DataTable item in DS.Tables)
                {
                    sdt = CheckData(item, sPath);
                    dic.Add(item.TableName, sdt);
                }
                return dic;
            }
            catch (Exception ex)
            {
                //写入日志
                LogHelper.LogError("获取", "获取生成数据发生错误！" + Item + "-" + No + "-" + A + "-" + E + " 原因：" + ex.Message.ToString(), IP, sComp, sMan, Name);
                dic.Clear();
                return dic;
            }
        }


        private static DataTable CheckData(DataTable dt, string sPath)
        {
            if (dt.Rows.Count == 0)
            {
                DataTable newDt = new DataTable();
                foreach (DataColumn item in dt.Columns)
                {
                    newDt.Columns.Add(item.ColumnName, typeof(string));
                }

                DataRow newRow = newDt.NewRow();

                foreach (DataColumn column in newDt.Columns)
                {
                    if (column.ColumnName == "ImgPath")
                    {
                        newRow[column.ColumnName] = sPath + "\\UpPicFile\\Default\\empty.png";
                    }
                    else
                    {
                        newRow[column.ColumnName] = "/";
                    }
                }

                newDt.Rows.Add(newRow);

                return newDt;
            }
            else
            {
                foreach (DataRow row in dt.Rows)
                {
                    foreach (DataColumn col in dt.Columns)
                    {
                        var value = row[col].ToString().Trim();
                        // 判断当前列的值是否为空
                        if (value == null || string.IsNullOrEmpty(value))
                        {
                            // 如果为空值，替换为斜杠/
                            row[col] = "/";
                        }
                    }
                }
                return dt;
            }
        }

        public static DataTable CheckDHR(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string Remark, string Flag, int limit, int page, string sMan, string sComp, string IP)
        {
            DataTable dt = new DataTable();
            SqlParameter[] parameters = {
                    new SqlParameter("@lnNo", SqlDbType.NVarChar,2000),
                    new SqlParameter("@lnItem", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnStatus", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnE", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnF", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,30),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)
                };
            parameters[0].Value = No;
            parameters[1].Value = Item;
            parameters[2].Value = Name;
            parameters[3].Value = MNo;
            parameters[4].Value = MName;
            parameters[5].Value = "";
            parameters[6].Value = "";
            parameters[7].Value = "";
            parameters[8].Value = A; // A
            parameters[9].Value = B;
            parameters[10].Value = C;
            parameters[11].Value = D;
            parameters[12].Value = E;
            parameters[13].Value = F;
            parameters[14].Value = limit;
            parameters[15].Value = page;
            parameters[16].Value = sMan;
            parameters[17].Value = Flag;
            parameters[18].Value = "";

            DataSet Ds = DBHelper.RunProcedureForDS("P_GetDHRInfoForPage", parameters);

            if (Ds != null && Ds.Tables.Count > 0)
            {
                dt = Ds.Tables[0];
            }

            return dt;
        }


        // 获取生成DHR需要用到的数据
        public static Dictionary<string, DataTable> CreateDHRPDF(string No, string Name, string Item, string MNo, string MName, string A, string B, string C, string D, string E, string F, string Remark, string Flag, string sMan, string sComp, string IP)
        {
            Dictionary<string, DataTable> dic = new Dictionary<string, DataTable>();
            try
            {
                SqlParameter[] parameters = {
                    new SqlParameter("@lnNo", SqlDbType.NVarChar,2000),
                    new SqlParameter("@lnItem", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnMNo", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnMName", SqlDbType.NVarChar,100),
                    new SqlParameter("@lnStatus", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnBDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnEDate", SqlDbType.NVarChar,20),
                    new SqlParameter("@lnA", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnB", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnC", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnD", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnE", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnF", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnRow", SqlDbType.Int),
                    new SqlParameter("@lnNum", SqlDbType.Int),
                    new SqlParameter("@lnInMan", SqlDbType.NVarChar,30),
                    new SqlParameter("@lnInFlag", SqlDbType.NVarChar,30),
                    new SqlParameter("@loErrgs", SqlDbType.NVarChar,200)
                };
                parameters[0].Value = No;
                parameters[1].Value = Item;
                parameters[2].Value = Name;
                parameters[3].Value = MNo;
                parameters[4].Value = MName;
                parameters[5].Value = "";
                parameters[6].Value = "";
                parameters[7].Value = "";
                parameters[8].Value = A; // A
                parameters[9].Value = B;
                parameters[10].Value = C;
                parameters[11].Value = D;
                parameters[12].Value = E;
                parameters[13].Value = F;
                parameters[14].Value = 1;
                parameters[15].Value = 20;
                parameters[16].Value = sMan;
                parameters[17].Value = Flag;
                parameters[18].Value = "";

                DataSet DS = DBHelper.RunProcedureForDS("P_GetDHRInfoForPage", parameters);

                if (DS == null) return dic;

                foreach (DataTable item in DS.Tables)
                {
                    dic.Add(item.TableName, item);
                }
            }
            catch (Exception ex)
            {
                dic.Clear();
                //写入日志
                LogHelper.LogError("获取", "获取生成数据发生错误！" + Item + "-" + No + "-" + A + "-" + E + " 原因：" + ex.Message.ToString(), IP, sComp, sMan, Name);
            }

            return dic;
        }
    }
}
