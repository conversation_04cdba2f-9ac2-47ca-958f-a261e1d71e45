﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>工艺文件库</title>
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <script src="../js/ajaxfileupload.js" type="text/javascript"></script>
    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all">
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/PrdAssist.js" type="text/javascript"></script>

    <link href="../css/XC.css" rel="stylesheet" />



    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        table_ProcessFile
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        //$('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=28-1' });
                    }
                }
            })
        });



    </script>


    <script type="text/javascript">


        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#table_ProcessFile',
                id: 'table_ProcessFileID',
                url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=25',//初始化界面查询对应的存过程标记信息，获取部门信息
                height: 'full-80',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[

                    { type: 'numbers' },
                    { field: 'FileNo', title: '文件编号', width: 120, sort: true },
                    { field: 'FileName', title: '文件名称', width: 200, sort: true },
                    { field: 'FileVer', title: '文件版本', width: 200 },
                    { field: 'UserArea', title: '使用范围', width: 120 },
                    { field: 'Kind', title: '类别', width: 200 },
                    { field: 'Status', title: '状态', width: 200 },
                    { field: 'Language', title: '语言', width: 200 },
                    { field: 'DOCName', title: '文档原始名称', width: 200 },
                    { field: 'FilePath', title: '文件路径', width: 200 },
                    { field: 'ECN', title: '工程变更', width: 200 },
                    { field: 'Remark', title: '备注', width: 300 },
                    { field: 'InMan', title: '录入人', width: 90 },
                    { field: 'InDate', title: '录入时间', width: 200 },
                    { field: 'op', title: '操作', width: 120, toolbar: '#barDemo_ProcessFile', fixed: 'right' }
                ]],
                page: true,
                //  done: function(res, curr, count) {
                //     var that = this.elem.next();
                //     res.data.forEach(function(item, index) {
                //         if (item.Flag == "0") { // 说明评估日期已到期
                //             $(".layui-table-body tbody tr[data-index='" + index + "']").css({ 'backgroundColor': "Pink" });
                //             $(".layui-table-body tbody tr[data-index='" + index + "']").find('td[data-field="LastContactTime"]').css({ 'color': "red" });
                //
                //         }
                //     });
                // }


            });




            //监听是否选中操作
            table.on('checkbox(table_ProcessFile)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);

            });


            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(table_ProcessFile)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

            });

            //监听单元格编辑
            table.on('edit(table_ProcessFile)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });


            //监听行工具事件
            table.on('tool(table_ProcessFile)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值
                if (layEvent === 'del') {

                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("您确定要删除该文件信息吗？ 文件编号：")
                    //设置删除的对象
                    $("#hint-value").html(data.FileNo)

                    $("#txtFileNo").val(data.FileNo)

                } else if (layEvent === 'edit') {
                    $('.head-title1').html("修改工艺文件信息");
                    $('#txtAEFlag').val("46");//修改标记


                    $("#txtFileNo").val(data.FileNo);
                    $("#txtFileName").val(data.FileName);
                    $("#txtFileVer").val(data.FileVer);
                    $("#txtUserArea").val(data.UserArea);
                    $("#txtKind").val(data.Kind);

                    $("#txtStatus").val(data.Status);
                    $("#txtLanguage").val(data.Language);
                    $("#txtDOCName").val(data.DOCName);
                    $("#txtFilePath").val(data.FilePath);
                    $("#txtECN").val(data.ECN);



                    $("#txtRemark").val(data.Remark);

                    $("#txtFileNo").attr({ "disabled": "disabled" });
                    $("#btn_ProcessFileSave").removeAttr("disabled");

                    $("#div_warningFile").html("");
                    $("#div_warningFile").hide();

                    $('#ShowOne').css("display", "block")
                    $('#ShowOne-fade').css("display", "block")

                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }

            });
            //  查询 --
            $('#btn_ProcessFile_Open').click(function () {

                var sWNo = $("#txtSFileNo").val();  //
                var sName = encodeURI($("#txtSFileName").val());  //


                var Data = '';
                var Params = { No: sWNo, Name: sName, Item: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "", E: "" };
                var Data = JSON.stringify(Params);

                table.reload('table_ProcessFileID', {
                    method: 'post',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=25&Data=' + Data,
                    where: {
                        'No': sWNo,
                        'name': sName
                    }, page: {
                        curr: 1
                    }
                });
            });

            $("#btn_ProcessFileDel").click(function () {
                //向服务端发送禁用指令
                var sFileNo = $("#txtFileNo").val();
                var sFlag = "47";//删除标记

                var Data = '';
                var Params = { No: sFileNo, Name: "", Item: "", A: "", B: "", C: "", D: "", E: "", F: "", I: "", J: "", K: "", L: "", Kind: "", Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);
                $.ajax({
                    type: "POST",
                    url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('删除成功！');

                            $("#ShowDel").css("display", "none")
                            $("#ShowDel-fade").css("display", "none")

                            $('#btn_ProcessFile_Open').click();  // 重新查询


                        }
                        //else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                        //    layer.msg('工序已用于工艺流程，不能删除！');

                        //} else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST1') {
                        //    layer.msg('工序已用于BOM设计，不能删除！');

                        //} else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST2') {
                        //    layer.msg('工序已用于产品对应标贴，不能删除！');

                        //}
                        else {
                            layer.msg('删除失败，请重试！');
                            $('#btn_ProcessFile_Open').click();  // 重新查询
                        }
                    },
                    error: function (data) {
                        layer.msg('删除失败2，请重试！');
                        $('#btn_ProcessFile_Open').click();  // 重新查询
                    }
                });
            })

        });

        function openDialog(n) {  // 新增
            $('.head-title1').html("新增工艺文件信息");
            $('#txtAEFlag').val("45");//新增的类型标记

            $("#txtFileNo").val("");
            $("#txtFileName").val("");
            $("#txtFileVer").val("");
            $("#txtUserArea").val("");
            $("#txtKind").val("");

            $("#txtStatus").val("");
            $("#txtLanguage").val("");
            $("#txtDOCName").val("");
            $("#txtFilePath").val("");
            $("#txtECN").val("");

            $("#txtRemark").val("");

            $("#div_warningFile").html("");
            $("#div_warningFile").hide();

            $("#txtFileNo").removeAttr("disabled");

            $("#btn_ProcessFileSave").attr({ "disabled": "disabled" });

            $('#ShowOne').css("display","block")
            $('#ShowOne-fade').css("display", "block")
        }

        function closeDialog() {
            $('#ShowOne').css("display", "none")
            $('#ShowOne-fade').css("display", "none")
            $("#ShowDel").css("display", "none")
            $("#ShowDel-fade").css("display", "none")

        }


    </script>


    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

        })



    </script>

    <style type="text/css">

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        /*.black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: #bbbcc7;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=60);
        }
*/
        .LabelDelBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #da542e;
        }

        .LabelAddBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #579fd8;
        }

        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }
    </style>


</head>
<body>
    <div class="div_find">

        <p>
            <label class="find_labela">工艺文件编号</label> <input type="text" id="txtSFileNo" class="find_input" />
            <label class="find_labela">工艺文件名称</label><input type="text" id="txtSFileName" class="find_input" />
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="btn_ProcessFile_Open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="javascript:openDialog(1)">添加</button>
        </p>
    </div>

    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="table_ProcessFile" lay-filter="table_ProcessFile"></table>

        <script type="text/html" id="barDemo_ProcessFile">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="edit">修改</button>
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>
        </script>

    </div>

    <div class="XC-modal XC-modal-xl" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title1">工艺文件维护</span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">

                <form class="XC-Form-block">
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">文件编号<span class="XC-Font-Red">*</span></span>
                        <input type="text" class="XC-Input-block" id="txtFileNo" name="txtFileNo" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">文件名称<span class="XC-Font-Red">*</span></span>
                        <input type="text" class="XC-Input-block" id="txtFileName" name="txtFileName" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">文件版本<span class="XC-Font-Red">*</span></span>
                        <input type="text" class="XC-Input-block" id="txtFileVer" name="txtFileVer" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">适用范围</span>
                        <input type="text" class="XC-Input-block" id="txtUserArea" name="txtUserArea" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">类别</span>
                        <input type="text" class="XC-Input-block" id="txtKind" name="txtKind" />
                    </div>
                    <!--<div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">状态</span>
                        <input type="text" class="XC-Input-block" id="txtStatus" name="txtStatus" />
                    </div>-->
                    <div class="XC-Form-block-Item" style="margin-bottom:10px">
                        <span class="XC-Span-Input-block">语言</span>
                        <input type="text" class="XC-Input-block" id="txtLanguage" name="txtLanguage" />
                    </div>
                    <!--<div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">文档原名</span>
                        <input type="text" class="XC-Input-block" id="txtDOCName" name="txtDOCName" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">文件路径</span>
                        <input type="text" class="XC-Input-block" id="txtFilePath" name="txtFilePath" />
                    </div>-->
                    <div class="XC-Form-block-Item" style="margin-bottom:0px">
                        <span class="XC-Span-Input-block" style="width:515px;color:red">输入文件相关信息--->选择好PDF文件后--->请点击 “上传”--->最后点击“保存”</span>
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">上传文件</span>
                        <input class="XC-Input-block" type="file" id="UpFile" name="UpFile" value="" accept=".pdf" style="width: 92%; float: left;padding:3.5px 2px 0px 3.5px" />
                        <input type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="btnPrdFileload" value="上传" name="btnPrdFileload" style="float: right; width: 8%;height:28.5px;margin-left:5px;" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block"></span>
                        <input type="text" class="XC-Input-block" id="txtFilePath" name="txtFilePath" readonly="readonly" /><!--style="display:none;"-->
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block"></span>
                        <input type="text" class="XC-Input-block" id="txtDOCName" name="txtDOCName" readonly="readonly" /><!--style="display:none;"-->
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">工程变更</span>
                        <input type="text" class="XC-Input-block" id="txtECN" name="txtECN" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Textarea-block">备注</span>
                        <textarea class="XC-Textarea-block" id="txtRemark" name="txtRemark" style="height: 100px; width: 99%;"> </textarea>
                    </div>
                </form>
            </div>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="btn_ProcessFileSave">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="ProcessFileSaveClose" onclick='closeDialog()'>关闭</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>

    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtFileNo" name="txtFileNo" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="btn_ProcessFileDel">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>

    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>

</body>
</html>