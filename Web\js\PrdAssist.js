﻿
$(function () {

    function EntityMsg(item, Mesage) {
        $("#div_warning").show();
        item.focus();
        $("#sMessage").html(Mesage);

    }
    // 重新登录
    $('#BtnRelationLogin').click(function () {
        var Num = parseInt(Math.random() * 1000);
        location.href = "../Login.htm?CFlag=30&RA" + Num;

    });


    //  根据客户编码获取简称
    $('#txtBCustNo').blur(function () {
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();
        var sCNo = $('#txtBCustNo').val();
        var Flag = "41-2";
        var time = new Date();
        var sM = time.getMonth() + 1;
        if (sM < 10) {
            sM = "0" + sM
        }
        var sD = time.getDate()
        if (sD < 10) {
            sD = "0" + sD
        }

        var s1 = time.getFullYear() + "-" + sM + "-" + sD;
        $('#txtPPDate').val(s1);

        if (sCNo == "") {
            $("#div_warning").html("请输入客户编码！")
            $("#divsuccess").show();
            return;
        }


        var Data = '';
        var Params = { CNo: sCNo, Flag: Flag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/BaseModuleAjax.ashx?OP=GetCustInfoByNo&CFlag=41-2",
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson.length > 0) {
                    // var sStr = eval(parsedJson.json)[0];
                    //$('#txtRNo').val(row.ReceiveNo);
                    $('#txtNameEn').val(parsedJson[0].CustEn);
                    $('#txtPPNo').val(parsedJson[0].CustNo);
                    $('#txtPPNo').focus();


                } else {
                    $("#txtNameEn").html("系统不存在该客户信息");
                }
            },
            error: function (data) {
                $("#div_warning2").html("系统出错，请重试2！")
                $("#div_warning2").show();
            }
        });

    });









    ///  保存:不良现象信息
    $('#PrdAssistSave_Btn').click(function () {
        $("#PrdAssistSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();


        var sWNo = $("#txtWNo").val();
        var sWDesc = $("#txtWDesc").val();
        var sWKind = $("#txtWKind").val();
        var sRemark = $("#txtRemark").val();
        var sFlag = $("#txtAEFlag").val();  // 1标识新增，2标识修改


        if (sWDesc == "") {
            ErrorMessage("请输入不良现象！", 2000)
            $("#PrdAssistSave_Btn").removeAttr("disabled");
            return;
        }

        if (sWKind == "") {
            ErrorMessage("请选择不良类别！", 2000)
            $("#PrdAssistSave_Btn").removeAttr("disabled");
            return;
        }


        // (No, Name, Item, A, B, C, D, E, F, G, H, I, J, K, L, Kind, sComp, InMan, Remark, sFlag);
        var Data = '';
        var Params = { No: sWNo, Name: sWDesc, Item: "", A: "", B: "", C: "", D: "", E: "", F: "", I: "", J: "", K: "", L: "", Kind: sWKind, Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    layer.msg("提交成功！")

                    $("#PrdAssistSave_Btn").removeAttr("disabled");

                    $('#DefectsBut_open').click();

                    closeDialog()

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#PrdAssistSave_Btn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#PrdAssistSave_Btn").removeAttr("disabled");
                    ErrorMessage("该描述已存在！", 2000)
                } else {
                    $("#PrdAssistSave_Btn").removeAttr("disabled");
                    ErrorMessage(parsedJson.Msg, 2000)
                }
            },
            error: function (data) {
                $("#PrdAssistSave_Btn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });


    ///  保存:不良原因信息
    $('#DefectsCauseSave_Btn').click(function () {
        $("#DefectsCauseSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();


        var sWNo = $("#txtWNo").val();
        var sWDesc = $("#txtWDesc").val();
        var sWKind = $("#txtWKind").val();
        var sRemark = $("#txtRemark").val();
        var sFlag = $("#txtAEFlag").val();  // 1标识新增，2标识修改


        if (sWDesc == "") {
            ErrorMessage("请输入不良原因！", 2000)
            $("#DefectsCauseSave_Btn").removeAttr("disabled");
            return;
        }

        if (sWKind == "") {
            ErrorMessage("请选择不良类别！", 2000)
            $("#DefectsCauseSave_Btn").removeAttr("disabled");
            return;
        }


        // (No, Name, Item, A, B, C, D, E, F, G, H, I, J, K, L, Kind, sComp, InMan, Remark, sFlag);
        var Data = '';
        var Params = { No: sWNo, Name: sWDesc, Item: "", A: "", B: "", C: "", D: "", E: "", F: "", I: "", J: "", K: "", L: "", Kind: sWKind, Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    layer.msg("提交成功！")

                    $("#DefectsCauseSave_Btn").removeAttr("disabled");

                    $('#DefectsBut_open').click();

                    closeDialog()

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#DefectsCauseSave_Btn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#DefectsCauseSave_Btn").removeAttr("disabled");
                    ErrorMessage("该描述已存在！", 2000)
                } else {
                    $("#DefectsCauseSave_Btn").removeAttr("disabled");
                    ErrorMessage(parsedJson.Msg, 2000)
                }
            },
            error: function (data) {
                $("#DefectsCauseSave_Btn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });


    ///  保存:部门信息
    $('#DeptInfoSave_Btn').click(function () {
        $("#DeptInfoSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();


        var sWNo = $("#txtWNo").val();
        var sWDesc = $("#txtWDesc").val();
        var sWKind = $("#txtWKind").val();
        var sRemark = $("#txtRemark").val();
        var sFlag = $("#txtAEFlag").val();  // 1标识新增，2标识修改


        if (sWDesc == "") {
            ErrorMessage("请输入部门名称！",2000)
            $("#DeptInfoSave_Btn").removeAttr("disabled");
            return;
        }

        if (sWKind == "") {
            ErrorMessage("请输入部门地址！", 2000)
            $("#DeptInfoSave_Btn").removeAttr("disabled");
            return;
        }


        // (No, Name, Item, A, B, C, D, E, F, G, H, I, J, K, L, Kind, sComp, InMan, Remark, sFlag);
        var Data = '';
        var Params = { No: sWNo, Name: sWDesc, Item: "", A: "", B: "", C: "", D: "", E: "", F: "", I: "", J: "", K: "", L: "", Kind: sWKind, Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#DeptInfoSave_Btn").removeAttr("disabled");

                    $('#DefectsBut_open').click();

                    $('#ShowOne').css("display", "none")
                    $('#ShowOne-fade').css("display", "none")

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#DeptInfoSave_Btn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#DeptInfoSave_Btn").removeAttr("disabled");
                    ErrorMessage("你输入的部门名称已存在，请重新输入！", 2000)
                } else {
                    $("#DeptInfoSave_Btn").removeAttr("disabled");
                    ErrorMessage(parsedJson.Msg, 2000)
                }
            },
            error: function (data) {
                $("#DeptInfoSave_Btn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });

    ///  保存:清场计划表头
    $('#ClearPlanHeadSave_Btn').click(function () {
        $("#ClearPlanHeadSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();


        var sWNo = $("#txtCPNo").val();
        var sCPVer = $("#txtCPVer").val();
        //var sWKind = $("#txtWKind").val();
        var sRemark = $("#txtRemark").val();
        var sFlag = $("#txtAEFlag").val();  // 1标识新增，2标识修改

        var sCPVerOld = $("#txtCPVerOld").val();

        if (sCPVer == "") {
            $("#div_warning").html("请输入清场计划版本！")
            $("#div_warning").show();
            $("#ClearPlanHeadSave_Btn").removeAttr("disabled");
            return;
        }

        if (sFlag == '10-2' && sCPVerOld == sCPVer) {
            $("#div_warning").html("输入的新旧版本号相同，请重新输入！")
            $("#div_warning").show();
            $("#ClearPlanHeadSave_Btn").removeAttr("disabled");
            return;
        }


        // (No, Name, Item, A, B, C, D, E, F, G, H, I, J, K, L, Kind, sComp, InMan, Remark, sFlag);
        var Data = '';
        var Params = { No: sWNo, Name: "", Item: "", A: sCPVer, B: "", C: "", D: "", E: "", F: "", I: "", J: "", K: "", L: "", Kind: sCPVerOld, Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#div_warning").html("提交成功！")
                    $("#div_warning").show();
                    $("#ClearPlanHeadSave_Btn").removeAttr("disabled");

                    $('#ClearPlanBut_open').click();
                    document.getElementById('light').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#ClearPlanHeadSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("您未登陆系统，请先登录！")
                    $("#div_warning").show();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#ClearPlanHeadSave_Btn").removeAttr("disabled");
                    $("#div_warning").html("当前清场计划编号和版本号对应的数据已存在，请重新输入！")
                    $("#div_warning").show();
                } else {
                    $("#ClearPlanHeadSave_Btn").removeAttr("disabled");
                    $("#div_warning").html(parsedJson.Msg);
                    $("#div_warning").show();
                }
            },
            error: function (data) {
                $("#ClearPlanHeadSave_Btn").removeAttr("disabled");
                $("#div_warning").html("系统出错，请重试2！")
                $("#div_warning").show();
            }
        });
    });

    //保存，清场计划明细
    $('#ClearPlanDetailSave_Btn').click(function () {
        $("#ClearPlanDetailSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warningDetailAdd").hide();

        var sWNo = $("#txtCPDNoADD").val();
        var sCPVer = $("#txtCPDVerADD").val();
        var sRemark = $("#txtDRemarkADD").val();
        var sItemNo = $("#txtItemNo").val();
        var sCPTxt = $("#txtCPTxt").val();
        var sType = $("#txtType").val();
        var sFlag = $("#txtAEFlag").val();  // 1标识新增，2标识修改
        //var sFlag = $("#txtAddKind").val();  // 1标识新增，2标识修改


        if (sCPVer == "") {
            $("#div_warningDetailAdd").html("请输入清场计划版本！")
            $("#div_warningDetailAdd").show();
            $("#ClearPlanDetailSave_Btn").removeAttr("disabled");
            return;
        }
        if (sCPTxt == "") {
            $("#div_warningDetailAdd").html("请输入清场内容！")
            $("#div_warningDetailAdd").show();
            $("#ClearPlanDetailSave_Btn").removeAttr("disabled");
            return;
        }
        if (sType == "") {
            $("#div_warningDetailAdd").html("请选择数值类型！")
            $("#div_warningDetailAdd").show();
            $("#ClearPlanDetailSave_Btn").removeAttr("disabled");
            return;
        }

        // (No, Name, Item, A, B, C, D, E, F, G, H, I, J, K, L, Kind, sComp, InMan, Remark, sFlag);
        var Data = '';
        var Params = { No: sWNo, Name: sCPTxt, Item: sItemNo, A: sCPVer, B: "", C: "", D: "", E: "", F: "", I: "", J: "", K: "", L: "", Kind: sType, Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#div_warningDetailAdd").html("提交成功！")
                    $("#div_warningDetailAdd").show();
                    $("#ClearPlanDetailSave_Btn").removeAttr("disabled");

                    //$('#DefectsBut_open').click();
                    ShowClearPlanDetail(sWNo, sCPVer);//获取数据

                    document.getElementById('div_ClearPlanDetailADD').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#ClearPlanDetailSave_Btn").removeAttr("disabled");
                    $("#div_warningDetailAdd").html("您未登陆系统，请先登录！")
                    $("#div_warningDetailAdd").show();
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#ClearPlanDetailSave_Btn").removeAttr("disabled");
                    $("#div_warningDetailAdd").html("该描述已存在！")
                    $("#div_warningDetailAdd").show();
                } else {
                    $("#ClearPlanDetailSave_Btn").removeAttr("disabled");
                    $("#div_warningDetailAdd").html(parsedJson.Msg);
                    $("#div_warningDetailAdd").show();
                }
            },
            error: function (data) {
                $("#ClearPlanDetailSave_Btn").removeAttr("disabled");
                $("#div_warningDetailAdd").html("系统出错，请重试2！")
                $("#div_warningDetailAdd").show();
            }
        });
    });


    //保存清场记录主数据
    $('#btn_OrderClearRecDetailSave').click(function () {
        var sCPNo = $("#txtCRCPNo").val();
        var sCPVer = $("#txtCRCPVer").val();
        var sCRNo = $("#txtCRNo").val();
        var sOrderNo = $("#txtCROrderNo").val();
        var sMaterNo = $("#txtCRMaterNo").val();
        var sMaterName = $("#txtCRMaterName").val();
        var sMaterSpec = $("#txtCRMaterSpec").val();
        var sDealMan = "";
        var sDeptName = $("#txtCRDeptName").val();
        var sGroupName = $("#txtCRGroupName").val();

        if (sOrderNo == "") {
            //$("#div_warningDetailAdd").html("工单不可以为空，请选择工单，再选择清场计划！")
            //$("#div_warningDetailAdd").show();
            //$("#ClearPlanDetailSave_Btn").removeAttr("disabled");
            layer.msg('工单不可以为空，请选择工单，再选择清场计划！！');
            closeDialog(1);
            return;
        }
        if (sDeptName == "") {
            //$("#div_warningDetailAdd").html("工单不可以为空，请选择工单，再选择清场计划！")
            //$("#div_warningDetailAdd").show();
            //$("#ClearPlanDetailSave_Btn").removeAttr("disabled");
            layer.msg('部门不可以为空，请选择部门，再选择清场计划！！');
            closeDialog(1);
            return;
        }
        if (sGroupName == "") {
            //$("#div_warningDetailAdd").html("工单不可以为空，请选择工单，再选择清场计划！")
            //$("#div_warningDetailAdd").show();
            //$("#ClearPlanDetailSave_Btn").removeAttr("disabled");
            layer.msg('组别不可以为空，输入组别，再选择清场计划！！');
            closeDialog(1);
            return;
        }
        if (sCPNo == "") {
            //$("#div_warningDetailAdd").html("工单不可以为空，请选择工单，再选择清场计划！")
            //$("#div_warningDetailAdd").show();
            //$("#ClearPlanDetailSave_Btn").removeAttr("disabled");
            layer.msg('清场计划编号不可以为空，请选择清场计划！！');
            closeDialog(1);
            return;
        }
        if (sCPVer == "") {
            //$("#div_warningDetailAdd").html("工单不可以为空，请选择工单，再选择清场计划！")
            //$("#div_warningDetailAdd").show();
            //$("#ClearPlanDetailSave_Btn").removeAttr("disabled");
            layer.msg('组清场计划版本不可以为空，请确认！！');
            closeDialog(1);
            return;
        }
        var sFlag = "16";

        var Data = '';
        var Params = { No: sCRNo, Name: sOrderNo, Item: sMaterNo, A: sMaterName, B: sMaterSpec, C: sDealMan, D: "", E: sDeptName, F: sGroupName, G: sCPNo, H: sCPVer, I: "", J: "", K: "", L: "", Kind: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);
        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    layer.msg('选择清场计划清单并保存成功！');

                    $('#OrderClearRecBut_open').click();  // 重新查询

                    document.getElementById('div_OrderClearRecDetail').style.display = 'none';
                    document.getElementById('fade').style.display = 'none';

                    closeDialog(1);
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    layer.msg('你选择的清场计划【' + sCPNo + '--' + sCPVer + '】清场内容明细项未维护数据，请确认！！');
                    $('#OrderClearRecBut_open').click();  // 重新查询
                }
                else {
                    layer.msg('操作失败1，请重试！！');
                    $('#OrderClearRecBut_open').click();  // 重新查询
                }
            },
            error: function (data) {
                layer.msg('操作失败2，请重试！');
                $('#OrderClearRecBut_open').click();  // 重新查询
            }
        });
    });


    //  保存:仓库信息
    $('#WHSaveBtn').click(function () {
        $("#WHSaveBtn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();
        $("#divsuccess").hide();
        $("#loginError").hide();

        var sFNo = $("#txtFWNo").val();   // FWNo,FWName,WNo,WName,Kind
        var sFName = $("#txtFWName").val();
        var sCNo = $("#txtWNo").val();
        var sCName = $("#txtWName").val();
        var sKind = $("#txtKind").val();
        var sRemark = $("#txtRemark").val();  //  
        var sFlag = $("#txtAEFlag").val();  // 10 新增   11修改
        var sDeptNo = $("#txtDept").val();  //获取部门编号
        // 获取部门名称，使用正则匹配 ) 后的内容
        var sDeptNameMatch = $("#txtDept option:selected").text().match(/\)(.*)/);
        var sDeptName = sDeptNameMatch ? sDeptNameMatch[1] : "";
        var IsAutoSubmitAndAudit = $("#IsAutoSubmitAndAudit").is(':checked') ? "true" : "false"

        if (sFNo == "") {
            ErrorMessage("请输入上级仓编码！", 2000)
            $("#WHSaveBtn").removeAttr("disabled");
            return;
        }
        if (sFName == "") {
            ErrorMessage("请输入上级仓库名称！", 2000)
            $("#WHSaveBtn").removeAttr("disabled");
            return;
        }

        if (sCNo == "") {
            $("#txtWNo").val("");
            ErrorMessage("请输入子仓库编码！", 2000)
            $("#WHSaveBtn").removeAttr("disabled");
            return;
        }

        //if (sCName == "") {
        //    $("#div_warning").html("获取不到子零件名称，光标放在子编码内再离开！")
        //    $("#div_warning").show();
        //    $("#WHSaveBtn").removeAttr("disabled");
        //    return;
        //}

        if (sKind == "") {
            ErrorMessage("请选择类别！", 2000)
            $("#WHSaveBtn").removeAttr("disabled");
            return;
        }

        if (sCNo == sFNo) {
            ErrorMessage("父子编码不可以重复，请重新输入子编码！", 2000)
            $("#WHSaveBtn").removeAttr("disabled");
            return;
        }

        var Data = '';
        var Params = { No: sFNo, Name: sFName, Item: sCNo, A: sKind, B: sCName, C: sDeptNo, D: sDeptName, E: "", F: IsAutoSubmitAndAudit, Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    //var DataControl = new FConfigInfo();
                    //DataControl.clareDataFromControl();
                    $("#WHSaveBtn").removeAttr("disabled");
                    $("#divsuccess").show();

                    $("#IsAutoSubmitAndAudit").removeAttr("checked")
                    closeDialog()

                    $('#WHBut_open').click();


                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#WHSaveBtn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                    //location.href = "Login.htm";
                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#WHSaveBtn").removeAttr("disabled");
                    $("#txtCNo").val("");
                    $("#txtCName").val("");
                    ErrorMessage("上层仓库下已有下层仓库，请确认", 2000)
                }
                else {
                    $("#WHSaveBtn").removeAttr("disabled");
                    ErrorMessage("系统出错，请重试1！", 2000)
                }
            },
            error: function (data) {
                $("#WHSaveBtn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });


    //保存设备信息
    $('#btn_DeviceInfoSave').click(function () {
        $("#btn_DeviceInfoSave").attr({ "disabled": "disabled" });
        $("#div_warning").hide();


        var sDeviceNo = $("#txtDeviceNo").val();
        var sDeviceName = $("#txtDeviceName").val();
        var sDeviceDesc = $("#txtDeviceDesc").val();
        var sDeviceSpec = $("#txtDeviceSpec").val();
        var sDeviceKind = $("#txtDeviceKind").val();

        var sDeptNo = $("#txtDeptNo").val();
        var sDeptName = $("#txtDeptName").val();
        var sStatus = $("#txtStatus").val();
        var sMaterNo = $("#txtMaterNo").val();
        var sInventoryCycle = $("#txtInventoryCycle").val();
        var sUseDate = $("#txtDCUseDate").val();

        var sCheckReq = $("#txtCheckReq").val();
        var sMaintainReq = $("#txtMaintainReq").val();
        var sRemark = $("#txtRemark").val();


        var sFlag = $("#txtAEFlag").val();  // 1标识新增，2标识修改


        if (sDeviceNo == "") {
            $("#btn_DeviceInfoSave").removeAttr("disabled");
            ErrorMessage("设备编号不可以为空，请输入设备编号！", 2000)
            return;
        }

        if (sDeviceName == "") {
            $("#btn_DeviceInfoSave").removeAttr("disabled");
            ErrorMessage("设备名称不可以为空，请输入设备名称！", 2000)
            return;
        }

        if (sInventoryCycle == "") {
            $("#btn_DeviceInfoSave").removeAttr("disabled");
            ErrorMessage("有效期不可以为空，请输入有效期！", 2000)
            return;
        }

        // (No, Name, Item, A, B, C, D, E, F, G, H, I, J, K, L, Kind, sComp, InMan, Remark, sFlag);
        var Data = '';
        var Params = { No: sDeviceNo, Name: sDeviceName, Item: sDeviceDesc, A: sDeviceSpec, B: sDeviceKind, C: sDeptNo, D: sDeptName, E: sStatus, F: sMaterNo, G: sInventoryCycle, H: sCheckReq, I: sMaintainReq, J: sUseDate, K: "", L: "", Kind: "", Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#btn_DeviceInfoSave").removeAttr("disabled");

                    $('#btn_DeviceNo_Open').click();  // 重新查询

                    $('#ShowOne').css("display", "none")
                    $('#ShowOne-fade').css("display", "none")

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#btn_DeviceInfoSave").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#btn_DeviceInfoSave").removeAttr("disabled");
                    ErrorMessage("该设备编号已存在，请重新输入！", 2000)
                }
                else {
                    $("#btn_DeviceInfoSave").removeAttr("disabled");
                    ErrorMessage(parsedJson.Msg, 2000)
                }
            },
            error: function (data) {
                $("#btn_DeviceInfoSave").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });



    ///  保存:设备分类规则表头保存
    $('#DeviceClassHeadSave_Btn').click(function () {
        $("#DeviceClassHeadSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();


        var sMaterNo = $("#txtMaterNo").val();
        var sMaterName = $("#txtMaterName").val();
        var sDeptNo = $("#txtDeptNo").val();
        var sDeptName = $("#txtDeptName").val();
        var sRemark = $("#txtRemark").val();
        var sDeviceKind = $("#txtDeviceKind").val();
        var sCheckReq = $("#txtCheckReq").val();
        var sMaintainReq = $("#txtMaintainReq").val();

        var sFlag = $("#txtAEFlag").val();  // 1标识新增，2标识修改

        if (sMaterNo == "") {
            ErrorMessage("请输入分类编号！", 2000)
            $("#DeviceClassHeadSave_Btn").removeAttr("disabled");
            return;
        }

        if (sMaterName == "") {
            ErrorMessage("请输入分类规则名称！", 2000)
            $("#DeviceClassHeadSave_Btn").removeAttr("disabled");
            return;
        }

        //if (sWKind == "") {
        //    $("#div_warning").html("请选择不良类别！")
        //    $("#div_warning").show();
        //    $("#DeviceClassHeadSave_Btn").removeAttr("disabled");
        //    return;
        //}

        // (No, Name, Item, A, B, C, D, E, F, G, H, I, J, K, L, Kind, sComp, InMan, Remark, sFlag);
        var Data = '';
        var Params = { No: sMaterNo, Name: sMaterName, Item: sDeptNo, A: sDeptName, B: sDeviceKind, C: sCheckReq, D: sMaintainReq, E: "", F: "", G: "", H: "", I: "", J: "", K: "", L: "", Kind: "", Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#DeviceClassHeadSave_Btn").removeAttr("disabled");

                    $('#DeviceClassBut_open').click();

                    $('#ShowOne').css("display", "none")
                    $('#ShowOne-fade').css("display", "none")

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#DeviceClassHeadSave_Btn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#DeviceClassHeadSave_Btn").removeAttr("disabled");
                    ErrorMessage("该设备分类编号已存在，请重新输入！", 2000)
                } else {
                    $("#DeviceClassHeadSave_Btn").removeAttr("disabled");
                    ErrorMessage(parsedJson.Msg, 2000)
                }
            },
            error: function (data) {
                $("#DeviceClassHeadSave_Btn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });

    //保存，设备分类规则明细
    $('#DeviceClassDetailSave_Btn').click(function () {
        $("#DeviceClassDetailSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warningDetailAdd").hide();

        var sMaterNo = $("#txtDMaterNoAdd").val();
        var sMaterName = $("#txtDMaterNameAdd").val();
        var sDeptNo = $("#txtDeptNoAdd").val();
        var sDeptName = $("#txtDeptNameAdd").val();
        var sRemark = $("#txtRemarkADD").val();

        var sDCItem = $("#txtDCItem").val();
        var sCheckTxt = $("#txtCheckTxt").val();
        var sType = $("#txtType").val();

        var sKind = $("#txtAddKind").val();//保存的类型，点检信息：Check  或者  维护信息：Maintain
        var sFlag = $("#txtAEFlag").val();  // 1标识新增，2标识修改



        if (sMaterName == "") {
            ErrorMessage("请输入设备分类名称！", 2000)
            $("#DeviceClassDetailSave_Btn").removeAttr("disabled");
            return;
        }
        if (sCheckTxt == "") {
            ErrorMessage("请输入点检/维护内容！", 2000)
            $("#DeviceClassDetailSave_Btn").removeAttr("disabled");
            return;
        }
        if (sType == "") {
            ErrorMessage("请选择数据类型！", 2000)
            $("#DeviceClassDetailSave_Btn").removeAttr("disabled");
            return;
        }

        // (No, Name, Item, A, B, C, D, E, F, G, H, I, J, K, L, Kind, sComp, InMan, Remark, sFlag);

        var Data = '';
        var Params = {
            No: sMaterNo, Name: sMaterName, Item: sDCItem, A: sCheckTxt, B: sType, C: sDeptNo, D: sDeptName, E: "", F: "", G: "", H: "", I: "", J: "", K: "", L: "", Kind: sKind, Remark: sRemark, Flag: sFlag
        };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#DeviceClassDetailSave_Btn").removeAttr("disabled");

                    //$('#DefectsBut_open').click();
                    ShowDeviceClassDetail(sMaterNo, sKind);//获取数据
                    $("#txtCheckTxt").val("");
                    $("#txtType").val("");

                    layer.msg("提交成功！")
                    //$('#ShowThree').css("display", "none")
                    //$('#ShowThree-fade').css("display", "none")

                    //closeDialog(2);

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#DeviceClassDetailSave_Btn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#DeviceClassDetailSave_Btn").removeAttr("disabled");
                    ErrorMessage("该设备分类编号已存在，请重新输入！", 2000)
                } else {
                    $("#DeviceClassDetailSave_Btn").removeAttr("disabled");
                    ErrorMessage(parsedJson.Msg, 2000)
                }
            },
            error: function (data) {
                $("#DeviceClassDetailSave_Btn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });

    ///保存：设备点检任务，点检保存
    $('#btn_DeviceCheckTaskDetailSave').click(function () {
        var sTNo = $("#txtTNo").val();
        var sDeviceNo = $("#txtDCDeviceNo").val();
        var sMaterNo = $("#txtDCMaterNo").val();
        //var sMaterName = $("#txtDCCheckReq").val();
        var sFlag = "25";

        var Data = '';
        var Params = { No: sTNo, Name: sDeviceNo, Item: sMaterNo, A: "", B: "", C: "", D: "", E: "", F: "", G: "", H: "", I: "", J: "", K: "", L: "", Kind: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);
        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    layer.msg('保存成功！');

                    $('#DeviceCheckTaskBut_open').click();  // 重新查询

                    closeDialog(2);
                }
                else {
                    layer.msg('修改操作失败，请重试！');
                    $('#DeviceCheckTaskBut_open').click();  // 重新查询
                }
            },
            error: function (data) {
                layer.msg('修改操作失败2，请重试！');
                $('#DeviceCheckTaskBut_open').click();  // 重新查询
            }
        });
    });

    ///设备点检任务，点检结果提交
    $('#btn_DeviceCheckTaskResultCommit').click(function () {
        $("#btn_DeviceCheckTaskResultCommit").attr({ "disabled": "disabled" });
        $("#div_warningCKResultCommit").hide();

        //$("#").val(sNo);
        //$("#").val(sItemNo);
        var sNo = $("#txtItemCKENO").val();
        var sItemNo = $("#txtItemCKItemNo").val();
        var sType = $("#txtItemCKType").val();

        var sResult = $("#txtCKText").val();
        var sStatus = "已点检"

        if (sType == "布尔型") {
            sResult = $("#txtCKISOK").val();
        }
        else if (sType == "日期型") {
            sResult = $("#txtCKDate").val();
        }
        else if (sType == "文本型") {
            sResult = $("#txtCKText").val();
        }

        if (sResult == "") {
            ErrorMessage("设备点检结果不可以为空，请输入设备点检结果！", 2000)
            $("#btn_DeviceCheckTaskResultCommit").removeAttr("disabled");
            return;
        }

        var sFlag = "26-1";

        var Data = '';
        var Params = { No: sNo, Name: "", Item: sItemNo, MNo: "", MName: "", A: sResult, B: sStatus, C: "", D: "", E: "", F: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#div_warningCKResultCommit").html("设备点检结果提交成功！")
                    $("#div_warningCKResultCommit").show();
                    $('#DeviceCheckTaskBut_open').click();//重新刷新主界面内容
                    $("#txtCKISOK").val("");
                    $("#txtCKDate").val("");
                    $("#txtCKText").val("");

                    GetPrdAssistStatusForNoet(sNo);
                    ShowDeviceCheckTaskDetail(sNo);//重新显示点检内容清单
                    $("#btn_DeviceCheckTaskResultCommit").removeAttr("disabled");
                    closeDialog(1);
                }
                else {
                    layer.msg('点检结果提交失败，请重试！');
                    $("#btn_DeviceCheckTaskResultCommit").removeAttr("disabled");
                    // $('#Fin_SearchOpen').click();
                }
            },
            error: function (data) {
                layer.msg('点检结果提交失败2，请重试！');
                $("#btn_DeviceCheckTaskResultCommit").removeAttr("disabled");
                // $('#Fin_SearchOpen').click();
            }
        });

    });


    ///保存：设备保养任务，保养养新增
    $('#btn_DeviceMaintainTaskDetailSave').click(function () {
        //向服务端发送禁用指令
        var sTNo = $("#txtTNo").val();
        var sDeviceNo = $("#txtDCDeviceNo").val();
        var sMaterNo = $("#txtDCMaterNo").val();

        var sFlag = "28";

        var Data = '';
        var Params = { No: sTNo, Name: sDeviceNo, Item: sMaterNo, A: "", B: "", C: "", D: "", E: "", F: "", G: "", H: "", I: "", J: "", K: "", L: "", Kind: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);
        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    layer.msg('选择设备分类清单并保存成功！');

                    $('#DeviceMaintainTaskBut_open').click();  // 重新查询

                    closeDialog(2);
                }
                else {
                    layer.msg('修改操作失败，请重试！');
                    $('#DeviceMaintainTaskBut_open').click();  // 重新查询
                }
            },
            error: function (data) {
                layer.msg('修改操作失败2，请重试！');
                $('#DeviceMaintainTaskBut_open').click();  // 重新查询
            }
        });
    });
    ///设备保养任务，保养养结果提交
    $('#btn_DeviceMaintainTaskResultCommit').click(function () {
        $("#btn_DeviceMaintainTaskResultCommit").attr({ "disabled": "disabled" });
        $("#div_warningResultCommit").hide();

        //$("#").val(sNo);
        //$("#").val(sItemNo);
        var sNo = $("#txtItemENO").val();
        var sItemNo = $("#txtItemItemNo").val();
        var sType = $("#txtItemType").val();
        var sResult = $("#txtText").val();
        var sStatus = "已保养"

        if (sType == "布尔型") {
            sResult = $("#txtISOK").val();
        }
        else if (sType == "日期型") {
            sResult = $("#txtDate").val();
        }
        else if (sType == "文本型") {
            sResult = $("#txtText").val();
        }

        if (sResult == "") {
            ErrorMessage("保养结果不可以为空，请输入设备保养结果！", 2000)
            $("#btn_DeviceMaintainTaskResultCommit").removeAttr("disabled");
            return;
        }



        var sFlag = "29-1";

        var Data = '';
        var Params = { No: sNo, Name: "", Item: sItemNo, MNo: "", MName: "", A: sResult, B: sStatus, C: "", D: "", E: "", F: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#div_warningResultCommit").html("设备保养结果提交成功！")
                    $("#div_warningResultCommit").show();
                    $('#DeviceMaintainTaskBut_open').click();//重新刷新主界面内容

                    $("#txtISOK").val("");
                    $("#txtDate").val("");
                    $("#txtText").val("");

                    GetPrdAssistStatusForNoet(sNo);
                    ShowDeviceMaintainTaskDetail(sNo);//重新显示保养内容清单
                    $("#btn_DeviceMaintainTaskResultCommit").removeAttr("disabled");
                    closeDialog(1);
                }
                else {
                    layer.msg('保养结果提交失败，请重试！');
                    $("#btn_DeviceMaintainTaskResultCommit").removeAttr("disabled");
                    // $('#Fin_SearchOpen').click();
                }
            },
            error: function (data) {
                layer.msg('保养结果提交失败2，请重试！');
                $("#btn_DeviceMaintainTaskResultCommit").removeAttr("disabled");
                // $('#Fin_SearchOpen').click();
            }
        });

    });



    ///  保存:质量控制计划表头
    $('#QCControlPlanHeadSave_Btn').click(function () {
        $("#QCControlPlanHeadSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();


        var sQPNo = $("#txtQPNo").val();
        var sQPVer = $("#txtQPVer").val();
        var sSpec = $("#txtQPSpec").val();
        var sRemark = $("#txtQPRemark").val();

        var sQPVerOld = $("#txtQPVerOld").val();

        var sFlag = $("#txtAEFlag").val();  // 1标识新增，2标识修改

        if (sQPVer == "") {
            ErrorMessage("请输入质量控制计划版本！", 2000)
            $("#QCControlPlanHeadSave_Btn").removeAttr("disabled");
            return;
        }

        if (sSpec == "") {
            ErrorMessage("请输入质量控制计划类型！", 2000)
            $("#QCControlPlanHeadSave_Btn").removeAttr("disabled");
            return;
        }

        if (sFlag == "30-2" && sQPVer == sQPVerOld) {
            ErrorMessage("新输入版本号与原来相同，请输入新的版本号！", 2000)
            $("#QCControlPlanHeadSave_Btn").removeAttr("disabled");
            return;
        }

        // (No, Name, Item, A, B, C, D, E, F, G, H, I, J, K, L, Kind, sComp, InMan, Remark, sFlag);
        var Data = '';
        var Params = { No: sQPNo, Name: sQPVer, Item: "", A: sSpec, B: "", C: "", D: "", E: "", F: "", I: "", J: "", K: "", L: "", Kind: sQPVerOld, Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#QCControlPlanHeadSave_Btn").removeAttr("disabled");

                    $('#QCControlPlanBut_open').click();

                    $('#ShowOne').css("display", "none")
                    $('#ShowOne-fade').css("display", "none")

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#QCControlPlanHeadSave_Btn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#QCControlPlanHeadSave_Btn").removeAttr("disabled");
                    ErrorMessage("该质量控制计划编号和版本对应的数据已存在！", 2000)
                } else {
                    $("#QCControlPlanHeadSave_Btn").removeAttr("disabled");
                    ErrorMessage(parsedJson.Msg, 2000)
                }
            },
            error: function (data) {
                $("#QCControlPlanHeadSave_Btn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });


    //保存，质量控制计划明细
    $('#btn_QCControlPlanDetailSave_Btn').click(function () {
        $("#btn_QCControlPlanDetailSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warningDetailAdd").hide();

        var sQPNo = $("#txtQPDNoADD").val();
        var sQPVer = $("#txtQPDVerADD").val();
        var sQPSpec = $("#txtQPDSpecADD").val();


        var txtItemNo = $("#txtItemNo").val();

        var txtTechNode = $("#txtTechNode").val();
        var txtTechStep = $("#txtTechStep").val();
        var txtTSource = $("#txtTSource").val();
        var txtPFMEA = $("#txtPFMEA").val();

        var txtDevice = $("#txtDevice").val();
        var txtIDNO = $("#txtIDNO").val();
        var txtProduct = $("#txtProduct").val();
        var txtProcess = $("#txtProcess").val();
        var txtProcessTX = $("#txtProcessTX").val();



        var txtMeasurement = $("#txtMeasurement").val();
        var txtSampleNum = $("#txtSampleNum").val();
        var txtSampleFreq = $("#txtSampleFreq").val();
        var txtControlWay = $("#txtControlWay").val();
        var txtResponsePlan = $("#txtResponsePlan").val();

        var txtThisValue = $("#txtThisValue").val();
        var txtIncludeOne = "否";
        var txtToValue = $("#txtToValue").val();
        var txtIncludeTwo = "否";
        var txtRangeKind = $("#txtRangeKind").val();

        var txtDataType = $("#txtDataType").val();
        /*var txtStatus = $("#txtStatus").val();*/
        var sRemark = $("#txtDRemarkAdd").val();


        //$("#txtIncludeOne").removeProp("checked");
        //$("#txtIncludeTwo").removeProp("checked");

        if ($("#txtIncludeOne").is(':checked')) {
            txtIncludeOne = "是";
        }
        if ($("#txtIncludeTwo").is(':checked')) {
            txtIncludeTwo = "是";
        }



        var sFlag = $("#txtAEFlag").val();  // 1标识新增，2标识修改
        //var sFlag = $("#txtAddKind").val();  // 1标识新增，2标识修改


        if (sQPVer == "") {
            ErrorMessage("请输入质量控制计划版本！", 2000);
            $("#btn_QCControlPlanDetailSave_Btn").removeAttr("disabled");
            return;
        }
        if (sQPSpec == "") {
            ErrorMessage("请输入质量控制内容！", 2000);
            $("#btn_QCControlPlanDetailSave_Btn").removeAttr("disabled");
            return;
        }
        if (txtDataType == "") {
            ErrorMessage("请选择数据类型！", 2000);
            $("#btn_QCControlPlanDetailSave_Btn").removeAttr("disabled");
            return;
        }

        // (No, Name, Item, A, B, C, D, E, F, G, H, I, J, K, L, Kind, sComp, InMan, Remark, sFlag);
        var Data = '';
        var Params = {
            No: sQPNo, Name: sQPVer, Item: txtItemNo, SNO: "", A: sQPSpec, B: txtTechNode, C: txtTechStep, D: txtTSource, E: txtPFMEA, F: txtDevice, G: txtIDNO, H: txtProduct, I: txtProcess, J: txtProcessTX, K: txtMeasurement, L: txtSampleNum,
            M: txtSampleFreq, N: txtControlWay, O: txtResponsePlan, P: txtThisValue, Q: txtIncludeOne, R: txtToValue, S: txtIncludeTwo, T: txtRangeKind, U: txtDataType, V: "", W: "", X: "", Kind: "", Remark: sRemark, Flag: sFlag
        };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);


                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    layer.msg('提交成功，可继续新增质量控制计划项！');

                    $("#btn_QCControlPlanDetailSave_Btn").removeAttr("disabled");

                    //$('#DefectsBut_open').click();
                    ShowQCControlPlanDetail(sQPNo, sQPVer);//获取数据


                    //提交之后清空文本框==================================================

                    $("#txtItemNo").val(""); // 内容编号
                    $("#txtTechNode").val(""); // 工艺节点
                    $("#txtTechStep").val(""); // 工艺步骤
                    $("#txtTSource").val(""); // 来源
                    $("#txtPFMEA").val(""); // PFMEA关联

                    $("#txtDevice").val(""); // 工装及夹具
                    $("#txtIDNO").val(""); // ID/NO
                    $("#txtProduct").val(""); // 产品
                    $("#txtProcess").val(""); // 过程
                    $("#txtProcessTX").val(""); // 过程特性/公差

                    $("#txtMeasurement").val(""); // 测量技术
                    $("#txtSampleNum").val(""); // 样品数量
                    $("#txtSampleFreq").val(""); // 样品频度
                    $("#txtControlWay").val(""); // 控制方法
                    $("#txtResponsePlan").val(""); // 反应计划

                    $("#txtThisValue").val(""); // 下限
                    //$("#txtIncludeOne").val(""); // 是否包含下限
                    $("#txtToValue").val(""); // 上限
                    $("#txtIncludeTwo").val(""); // 是否包含上限
                    $("#txtRangeKind").val(""); // 类别

                    $("#txtDataType").val(""); // 数据类型
                    $("#txtStatus").val(""); // 状态
                    $("#txtDRemarkAdd").val(""); // 备注


                    $("#txtIncludeOne").removeProp("checked");
                    $("#txtIncludeTwo").removeProp("checked");
                    //=============================================================

                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#btn_QCControlPlanDetailSave_Btn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000);
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#btn_QCControlPlanDetailSave_Btn").removeAttr("disabled");
                    ErrorMessage("该描述已存在！", 2000)
                } else {
                    $("#btn_QCControlPlanDetailSave_Btn").removeAttr("disabled");
                    ErrorMessage(parsedJson.Msg, 2000)
                }
            },
            error: function (data) {
                $("#btn_QCControlPlanDetailSave_Btn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });


    //保存，质量控制执行表头和明细信息保存
    $('#btn_QCControlExeDetailSave').click(function () {

        var sQPNo = $("#txtQCRQPNo").val();
        var sVer = $("#txtQCRVer").val();
        var sENo = $("#txtENo").val();
        var sOrderNo = $("#txtQCROrderNo").val();
        var sMaterNo = $("#txtQCRMaterNo").val();
        var sMaterName = $("#txtQCRMaterName").val();
        var sMaterSpec = $("#txtQCRMaterSpec").val();

        if (sOrderNo == "") {
            layer.msg('工单信息不可以为空，请选择工单！');
            closeDialog(1);

            return;
        }

        if (sQPNo == "") {
            layer.msg('质量控制计划不可以为空，请先选择质量控制计划！');
            closeDialog(1);
            return;
        }
        if (sVer == "") {
            layer.msg('质量控制计划版本不可以为空，请确认！！');
            closeDialog(1);
            return;
        }

        var sFlag = "33";

        var Data = '';
        var Params = { No: sENo, Name: sQPNo, Item: sVer, A: sOrderNo, B: sMaterSpec, C: sMaterNo, D: sMaterName, E: "", F: "", G: "", H: "", I: "", J: "", K: "", L: "", Kind: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);
        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    layer.msg('保存成功！');

                    $('#QCControlExeBut_open').click();  // 重新查询

                    closeDialog(2);
                }
                else {
                    layer.msg('修改操作失败，请重试！');
                    $('#QCControlExeBut_open').click();  // 重新查询
                }
            },
            error: function (data) {
                layer.msg('修改操作失败2，请重试！');
                $('#QCControlExeBut_open').click();  // 重新查询
            }
        });

    });
    //保存，质量控制执行明细结果保存
    $('#btn_QCResultEditSave').click(function () {
        $("#btn_QCResultEditSave").attr({ "disabled": "disabled" });
        $("#div_warningSelectQCControlDetailRsultEdit").hide();


        var txtItemEditENo = $("#txtItemEditENo").val();
        var txtEditItemNo = $("#txtEditItemNo").val();

        var txtItemEditTimes = $("#txtItemEditTimes").val();
        var txtItemEditSerialNo = $("#txtItemEditSerialNo").val();
        var txtItemEditEXEValue = $("#txtItemEditEXEValue").val();
        var txtItemEditEXEResull = $("#txtItemEditEXEResult").val();

        var txtItemEditDataType = $("#txtItemEditDataType").val();

        var sFlag = $("#txtAEFlag").val();  // 1标识新增，2标识修改
        //var sFlag = $("#txtAddKind").val();  // 1标识新增，2标识修改
        var sRemark = "";
        if (txtItemEditDataType == "布尔型") {
            var txtItemEditEXEValue = "OK";
            if (txtItemEditEXEResull == "异常") {
                var txtItemEditEXEValue = "NG";
            }
        }

        if (txtItemEditENo == "") {
            ErrorMessage("请输入质量控制执行编号！",2000)
            $("#btn_QCResultEditSave").removeAttr("disabled");
            return;
        }
        if (txtItemEditSerialNo == "") {
            ErrorMessage("请输入序列号", 2000)
            $("#btn_QCResultEditSave").removeAttr("disabled");
            return;
        }
        if (txtItemEditEXEValue == "") {
            ErrorMessage("请输入检验数据值！", 2000)
            $("#btn_QCResultEditSave").removeAttr("disabled");
            return;
        }
        if (txtItemEditEXEResull == "") {
            ErrorMessage("请输入执行结果！", 2000)
            $("#btn_QCResultEditSave").removeAttr("disabled");
            return;
        }
        if (txtItemEditDataType != "布尔型" && txtItemEditDataType != "文本型") {
            if ((txtItemEditEXEValue < $("#txtItemEditThisValue").val() || txtItemEditEXEValue > $("#txtItemEditToValue").val()) && txtItemEditEXEResull == "正常") {
                ErrorMessage("测量值不在上下限范围，请选择【异常】!",2000)
                $("#btn_QCResultEditSave").removeAttr("disabled");
                return;
            }
            if ((txtItemEditEXEValue > $("#txtItemEditThisValue").val() && txtItemEditEXEValue < $("#txtItemEditToValue").val()) && txtItemEditEXEResull == "异常") {
                ErrorMessage("测量值在上下限范围，请选择【正常】！",2000)
                $("#btn_QCResultEditSave").removeAttr("disabled");
                return;
            }
        }
        // (No, Name, Item, A, B, C, D, E, F, G, H, I, J, K, L, Kind, sComp, InMan, Remark, sFlag);
        var Data = '';
        var Params = {
            No: txtItemEditENo, Name: "", Item: txtEditItemNo, SNO: "", A: txtItemEditSerialNo, B: txtItemEditTimes, C: txtItemEditEXEValue, D: txtItemEditEXEResull, E: txtItemEditSerialNo, F: "", G: "", H: "", I: "", J: "", K: "", L: "",
            M: "", N: "", O: "", P: "", Q: "", R: "", S: "", T: "", U: "", V: "", W: "", X: "", Kind: "", Remark: sRemark, Flag: sFlag
        };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#btn_QCResultEditSave").removeAttr("disabled");

                    $('#QCControlExeBut_open').click();//查询主表
                    GetPrdAssistStatusForNo(txtItemEditENo);//查询明细表抬头，更新单据状态
                    ShowQCControlExeDetail(txtItemEditENo);//产需明细表清单，更新状态
                    GetPrdAssistStatusForNoItem(txtItemEditENo, txtEditItemNo);//查询执行记录表头，更新状态
                    ShowQCControlExeDetailResult(txtItemEditENo, txtEditItemNo);//查询执行记录记录明细，更新数据状态获取数据

                    $('#ShowSix').css("display", "none");

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#btn_QCResultEditSave").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！",2000)
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#btn_QCResultEditSave").removeAttr("disabled");
                    ErrorMessage("该序列号的执行记录已提交，请确认！",2000)
                } else {
                    $("#btn_QCResultEditSave").removeAttr("disabled");
                    ErrorMessage(parsedJson.Msg,2000)
                }
            },
            error: function (data) {
                $("#btn_QCResultEditSave").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！",2000)
            }
        });
    });









    ///  保存:IPQC巡查计划表头
    $('#InspectPlanHeadSave_Btn').click(function () {
        $("#InspectPlanHeadSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warning").hide();


        var sIPNo = $("#txtIPNo").val();
        var sIPVer = $("#txtIPVer").val();
        var sType = $("#txtIPType").val();
        var sRemark = $("#txtIPRemark").val();

        var sIPVerOld = $("#txtIPVerOld").val();

        var sFlag = $("#txtAEFlag").val();  // 1标识新增，2标识修改


        if (sIPVer == "") {
            ErrorMessage("请输入巡查计划版本！", 2000)
            $("#InspectPlanHeadSave_Btn").removeAttr("disabled");
            return;
        }

        if (sType == "") {
            ErrorMessage("请选择巡察类型！", 2000)
            $("#InspectPlanHeadSave_Btn").removeAttr("disabled");
            return;
        }
        if (sFlag == '36-2' && sIPVerOld == sIPVer) {
            ErrorMessage("输入的版本号和旧版本号重复，请重新输入！", 2000)
            $("#InspectPlanHeadSave_Btn").removeAttr("disabled");
            return;
        }

        // (No, Name, Item, A, B, C, D, E, F, G, H, I, J, K, L, Kind, sComp, InMan, Remark, sFlag);
        var Data = '';
        var Params = { No: sIPNo, Name: sIPVer, Item: "", A: sType, B: "", C: "", D: "", E: "", F: "", I: "", J: "", K: "", L: "", Kind: sIPVerOld, Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#InspectPlanHeadSave_Btn").removeAttr("disabled");

                    $('#InspectPlanBut_open').click();

                    $("#ShowOne").css("display", "none")
                    $("#ShowOne-fade").css("display", "none")

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#InspectPlanHeadSave_Btn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#InspectPlanHeadSave_Btn").removeAttr("disabled");
                    ErrorMessage("该IPQC巡查计划编号和版本号对应的信息已存在，请重新输入版本号！", 2000)
                } else {
                    $("#InspectPlanHeadSave_Btn").removeAttr("disabled");
                    ErrorMessage(parsedJson.Msg, 2000)
                }
            },
            error: function (data) {
                $("#InspectPlanHeadSave_Btn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });


    //保存，IPQC巡查计划明细
    $('#btn_InspectPlanDetailSave_Btn').click(function () {
        $("#btn_InspectPlanDetailSave_Btn").attr({ "disabled": "disabled" });
        $("#div_warningDetailAdd").hide();

        var sIPNo = $("#txtIPDNoADD").val();
        var sIPVer = $("#txtIPDVerADD").val();
        var sIPType = $("#txtIPDTypeADD").val();


        var txtItemNo = $("#txtItemNo").val();

        var txtItemName = $("#txtItemName").val();
        var txtCFPoint = $("#txtCFPoint").val();
        var txtBuildFloor = $("#txtBuildFloor").val();
        var txtRange = $("#txtRange").val();

        var txtProof = $("#txtProof").val();
        var txtCompliant = $("#txtCompliant").val();
        var txtCause = $("#txtCause").val();
        var sRemark = $("#txtDRemarkAdd").val();


        var sFlag = $("#txtAEFlag").val();  // 1标识新增，2标识修改
        //var sFlag = $("#txtAddKind").val();  // 1标识新增，2标识修改


        if (sIPVer == "") {
            ErrorMessage("请输入巡查计划划版本！", 2000)
            $("#btn_InspectPlanDetailSave_Btn").removeAttr("disabled");
            return;
        }
        if (sIPType == "") {
            ErrorMessage("请输入巡查类型！", 2000)
            $("#btn_InspectPlanDetailSave_Btn").removeAttr("disabled");
            return;
        }
        if (txtItemName == "") {
            ErrorMessage("请输入巡查项目名称！", 2000)
            $("#btn_InspectPlanDetailSave_Btn").removeAttr("disabled");
            return;
        }

        // (No, Name, Item, A, B, C, D, E, F, G, H, I, J, K, L, Kind, sComp, InMan, Remark, sFlag);
        var Data = '';
        var Params = {
            No: sIPNo, Name: sIPVer, Item: txtItemNo, SNO: "", A: sIPType, B: txtItemName, C: txtCFPoint, D: txtBuildFloor, E: txtRange, F: txtProof, G: txtCompliant, H: txtCause, I: "", J: "", K: "", L: "",
            M: "", N: "", O: "", P: "", Q: "", R: "", S: "", T: "", U: "", V: "", W: "", X: "", Kind: "", Remark: sRemark, Flag: sFlag
        };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    layer.msg("提交成功！")

                    $("#btn_InspectPlanDetailSave_Btn").removeAttr("disabled");

                    //$('#DefectsBut_open').click();
                    ShowInspectPlanDetail(sIPNo, sIPVer);//获取数据


                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#btn_InspectPlanDetailSave_Btn").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#btn_InspectPlanDetailSave_Btn").removeAttr("disabled");
                    ErrorMessage("该描述已存在！", 2000)
                } else {
                    $("#btn_InspectPlanDetailSave_Btn").removeAttr("disabled");
                    ErrorMessage(parsedJson.Msg, 2000)
                }
            },
            error: function (data) {
                $("#btn_InspectPlanDetailSave_Btn").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });


    //保存，IPQC巡查执行主数据保存
    $('#btn_InspectEXEDetailSave').click(function () {
        //向服务端发送禁用指令
        var sIPNo = $("#txtIERIPNo").val();
        var sVer = $("#txtIERVer").val();
        var sENo = $("#txtENo").val();


        var sFlag = "39";

        var Data = '';
        var Params = { No: sENo, Name: sIPNo, Item: sVer, A: "", B: "", C: "", D: "", E: "", F: "", G: "", H: "", I: "", J: "", K: "", L: "", Kind: "", Remark: "", Flag: sFlag };
        var Data = JSON.stringify(Params);
        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    layer.msg('选择IPQC巡查计划并保存成功！');

                    $('#InspectEXEBut_open').click();  // 重新查询

                    closeDialog(2);
                }
                else {
                    layer.msg('保存操作失败，请重试！');
                    $('#InspectEXEBut_open').click();  // 重新查询
                }
            },
            error: function (data) {
                layer.msg('保存操作失败2，请重试！');
                $('#InspectEXEBut_open').click();  // 重新查询
            }
        });
    });

    //保存，IPQC巡查执行结果保存细
    $('#btn_InspectExtResultEditSave').click(function () {
        $("#btn_InspectExtResultEditSave").attr({ "disabled": "disabled" });
        $("#div_warningDetailAdd").hide();


        var txtItemENo = $("#txtItemENo").val();
        var txtItemNo = $("#txtItemNo").val();
        var txtItemName = $("#txtItemName").val();
        var txtItemBuildFloor = $("#txtItemBuildFloor").val();
        var txtItemRange = $("#txtItemRange").val();

        var txtItemProof = $("#txtItemProof").val();
        var txtItemCompliant = "否";
        var txtItemCause = $("#txtItemCause").val();
        var txtItemCFPoint = $("#txtItemCFPoint").val();

        if ($("#txtItemCompliant").is(':checked')) {
            txtItemCompliant = "是";
        }


        var sFlag = $("#txtAEFlag").val();  // 1标识新增，2标识修改
        //var sFlag = $("#txtAddKind").val();  // 1标识新增，2标识修改

        if (txtItemCFPoint == "") {
            $("#btn_InspectExtResultEditSave").removeAttr("disabled");
            ErrorMessage("巡查确认点不得为空，请确认！", 2000)
            return;
        }
        if (txtItemENo == "") {
            $("#btn_InspectExtResultEditSave").removeAttr("disabled");
            ErrorMessage("巡查执行编号不得为空，请确认！", 2000)
            return;
        }
        if (txtItemNo == "") {
            $("#btn_InspectExtResultEditSave").removeAttr("disabled");
            ErrorMessage("项目编号不得为空，请确认！", 2000)
            return;
        }
        if (txtItemName == "") {
            $("#btn_InspectExtResultEditSave").removeAttr("disabled");
            ErrorMessage("项目内容不得为空，请确认！", 2000)
            return;
        }
        if (txtItemBuildFloor == "") {
            $("#btn_InspectExtResultEditSave").removeAttr("disabled");
            ErrorMessage("楼层不得为空，请确认！", 2000)
            return;
        }
        if (txtItemRange == "") {
            $("#btn_InspectExtResultEditSave").removeAttr("disabled");
            ErrorMessage("区域/线体不得为空，请确认！", 2000)
            return;
        }
        if (txtItemProof == "") {
            $("#btn_InspectExtResultEditSave").removeAttr("disabled");
            ErrorMessage("证据不得为空，请确认！", 2000)
            return;
        }
        if (txtItemCause == "") {
            $("#btn_InspectExtResultEditSave").removeAttr("disabled");
            ErrorMessage("不符合原因不得为空，请确认！", 2000)
            return;
        }
       

        // (No, Name, Item, A, B, C, D, E, F, G, H, I, J, K, L, Kind, sComp, InMan, Remark, sFlag);
        var Data = '';
        var Params = {
            No: txtItemENo, Name: "", Item: txtItemNo, SNO: "", A: txtItemName, B: txtItemBuildFloor, C: txtItemRange, D: txtItemProof, E: txtItemCompliant, F: txtItemCause, G: txtItemCFPoint, H: "", I: "", J: "", K: "", L: "",
            M: "", N: "", O: "", P: "", Q: "", R: "", S: "", T: "", U: "", V: "", W: "", X: "", Kind: "", Remark: "", Flag: sFlag
        };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#btn_InspectExtResultEditSave").removeAttr("disabled");
                    GetPrdAssistStatusForNoet(txtItemENo);
                    ShowInspectEXEDetail(txtItemENo);

                    closeDialog(3)

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#btn_InspectExtResultEditSave").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#btn_InspectExtResultEditSave").removeAttr("disabled");
                    ErrorMessage("该描述已存在！", 2000)
                } else {
                    $("#btn_InspectExtResultEditSave").removeAttr("disabled");
                    ErrorMessage(parsedJson.Msg, 2000)
                }
            },
            error: function (data) {
                $("#btn_InspectExtResultEditSave").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });



    ///  保存:试剂/质控品库信息
    $('#btn_QCWarehouseSave').click(function () {
        $("#btn_QCWarehouseSave").attr({ "disabled": "disabled" });
        $("#div_warning").hide();


        var txtLotNo = $("#txtLotNo").val();
        var txtMaterNo = $("#txtMaterNo").val();
        var txtMaterName = $("#txtMaterName").val();
        var txtSpec = $("#txtSpec").val();
        var txtQty = $("#txtQty").val();
        var txtStorageConditions = $("#txtStorageConditions").val();
        var txtExpirationDate = $("#txtExpirationDate").val();
        var sRemark = $("#txtRemark").val();
        var sFlag = $("#txtAEFlag").val();  // 1标识新增，2标识修改


        if (txtLotNo == "") {
            ErrorMessage("物料批号不可以为空，请输入！", 2000)
            $("#btn_QCWarehouseSave").removeAttr("disabled");
            return;
        }

        if (txtMaterNo == "") {
            ErrorMessage("物料编码不可以为空，请输入！", 2000)
            $("#btn_QCWarehouseSave").removeAttr("disabled");
            return;
        }


        // (No, Name, Item, A, B, C, D, E, F, G, H, I, J, K, L, Kind, sComp, InMan, Remark, sFlag);
        var Data = '';
        var Params = { No: txtLotNo, Name: txtMaterNo, Item: txtMaterName, sNO: "", A: txtSpec, B: txtQty, C: txtStorageConditions, D: txtExpirationDate, E: "", F: "", I: "", J: "", K: "", L: "", Kind: "", Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                    $("#btn_QCWarehouseSave").removeAttr("disabled");

                    $('#btn_QCWarehouse_Open').click();

                    $('#ShowOne').css("display", "none")
                    $('#ShowOne-fade').css("display", "none")

                    if (sFlag == "42") {
                        $("#txtQty").val("");
                        $("#txtMaterNo").val("");
                        $("#txtMaterName").val("");
                        $("#txtSpec").val("");

                        $("#txtLotNo").val("");
                        $("#txtStorageConditions").val("");
                        $("#txtExpirationDate").val("");
                        $("#txtRemark").val("");
                    }

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#btn_QCWarehouseSave").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#btn_QCWarehouseSave").removeAttr("disabled");
                    ErrorMessage("该批号信息已存在，请确认！", 2000)
                } else {
                    $("#btn_QCWarehouseSave").removeAttr("disabled");
                    ErrorMessage(parsedJson.Msg, 2000)
                }
            },
            error: function (data) {
                $("#btn_QCWarehouseSave").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });







    // 工艺文件管理界面，上传工艺文件
    $('#btnPrdFileload').click(function () {
        $("#div_warningFile").val("");
        var p = $("#txtFileNo");
        if (p.val() == "") {
            ErrorMessage("无法获取文件编码！", 2000)
            return;
        }

        var sTNo = $("#txtFileName").val();  //工序  (05)总
        var sName = sTNo.substr(sTNo.indexOf(")") + 1, sTNo.length);
        var sNo = sTNo.substr(1, sTNo.indexOf(")") - 1);  //工序  (05)总装 截取字符串，字符位置

        var sPVer = $("#txtFileVer").val();  //文件版本
        var sStr = p.val() + " " + sNo + " " + sPVer;

        var arr = new Array();
        var sPath = $("#txtFilePath").val();
        var sUpFile = $("#UpFile").val();  // ,用下面这种方式，解决中文乱码问题。
        arr = sPath.split(';/'); //注split可以用字符或字符串分割
        if (arr.length > 1) {
            ErrorMessage("每次只能上传一份文件！", 2000)
            return;
        }
        else {
            $("#div_Loading").show();
            $.ajaxFileUpload({
                url: '../Service/ApplyUpPicFile.aspx?NNo=' + sStr + '&sFlag=20-1&sUpFile=' + encodeURI(sUpFile),
                fileElementId: 'UpFile',
                dataType: 'json',
                success: function (data) {
                    //                 var parsedJson = jQuery.parseJSON(data);
                    data = eval(data);
                    var sPath = $("#txtFilePath").val();
                    $("#txtFilePath").val(sPath + ";" + data.Path);
                    var sFile = $("#txtDOCName").val();
                    $("#txtDOCName").val(sFile + ";" + data.FileName);

                    $("#div_Loading").hide();

                    $("#btn_ProcessFileSave").removeAttr("disabled");

                    //$("#div_warningFile").html("上传成功")
                    //alert("上传成功");

                }
            });
        }
    });



    ///  保存:工艺文件信息
    $('#btn_ProcessFileSave').click(function () {
        $("#btn_ProcessFileSave").attr({ "disabled": "disabled" });
        $("#div_warningFile").hide();


        var txtFileNo = $("#txtFileNo").val();
        var txtFileName = $("#txtFileName").val();
        var txtFileVer = $("#txtFileVer").val();
        var txtUserArea = $("#txtUserArea").val();
        var txtKind = $("#txtKind").val();

        var txtStatus = $("#txtStatus").val();
        var txtLanguage = $("#txtLanguage").val();
        var txtDOCName = $("#txtDOCName").val();
        var txtFilePath = $("#txtFilePath").val();
        var txtECN = $("#txtECN").val();

        var sRemark = $("#txtRemark").val();
        var sFlag = $("#txtAEFlag").val();  // 1标识新增，2标识修改


        if (txtFileNo == "") {
            ErrorMessage("工艺文件编号不可以为空，请输入！", 2000)
            $("#btn_ProcessFileSave").removeAttr("disabled");
            return;
        }

        if (txtFileName == "") {
            ErrorMessage("工艺文件名称不可以为空，请输入！", 2000)
            $("#btn_ProcessFileSave").removeAttr("disabled");
            return;
        }

        if (txtFileVer == "") {
            ErrorMessage("工艺文件版本不可以为空，请输入！", 2000)
            $("#btn_ProcessFileSave").removeAttr("disabled");
            return;
        }





        // (No, Name, Item, A, B, C, D, E, F, G, H, I, J, K, L, Kind, sComp, InMan, Remark, sFlag);
        var Data = '';
        var Params = { No: txtFileNo, Name: txtFileName, Item: txtFileVer, sNO: "", A: txtUserArea, B: txtKind, C: txtStatus, D: txtLanguage, E: txtDOCName, F: txtFilePath, G: txtECN, H: "", I: "", J: "", K: "", L: "", Kind: "", Remark: sRemark, Flag: sFlag };
        var Data = JSON.stringify(Params);

        $.ajax({
            type: "POST",
            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
            data: { Data: Data },
            success: function (data) {
                var parsedJson = jQuery.parseJSON(data);

                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                    $("#btn_ProcessFileSave").removeAttr("disabled");

                    $('#btn_ProcessFile_Open').click();

                    $('#ShowOne').css("display", "none")
                    $('#ShowOne-fade').css("display", "none")

                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                    $("#btn_ProcessFileSave").removeAttr("disabled");
                    ErrorMessage("您未登陆系统，请先登录！", 2000)
                }
                else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                    $("#btn_ProcessFileSave").removeAttr("disabled");
                    ErrorMessage("该文件编号已存在！", 2000)
                } else {
                    $("#btn_ProcessFileSave").removeAttr("disabled");
                    ErrorMessage(parsedJson.Msg, 2000)
                }
            },
            error: function (data) {
                $("#btn_ProcessFileSave").removeAttr("disabled");
                ErrorMessage("系统出错，请重试2！", 2000)
            }
        });
    });








    // 这里增加其他按钮，空间事件方法












});







// 用这个方法，主要是保证勾选不需要人勾，不设置disabled=disabled  ，这样用户看见的勾选比较清晰点。  2019  
function CheckBox_OnClick(ob) {
    var id = $(ob).attr("id");
    var sCD = id.substr(2, id.length);  // CB0  CB1  CB2


    if ($('#CB' + sCD).is(':checked')) {
        // $('#CB' + sCD).prop("checked", "checked");
        $('#CB' + sCD).removeProp("checked"); //设置为选中状态
    }
    else {
        $('#CB' + sCD).prop("checked", "checked");
    }


}


//消息提示
function SuccessMessage(text, time) {
    var Message = $('#Success');
    Message.html(text)
    Message.show()
    setTimeout(function () {
        Message.hide()
    }, time);
}
function ErrorMessage(text, time) {
    var Message = $('#Error');
    Message.html(text)
    Message.show()
    setTimeout(function () {
        Message.hide()
    }, time);
}
function WarningMessage(text, time) {
    var Message = $('#Warning');
    Message.html(text)
    Message.show()
    setTimeout(function () {
        Message.hide()
    }, time);
}
