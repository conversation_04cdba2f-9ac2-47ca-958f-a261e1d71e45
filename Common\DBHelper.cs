﻿/*****************************
 * Create :wudong
 * Desc:数据库操作
 * ****************************/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Data.SqlClient;

namespace Common
{
    public class DBHelper
    {
        //数据库连接属性
        private static SqlConnection connection;
        private static string connectionString = System.Configuration.ConfigurationSettings.AppSettings["SqlConnectionStr"];
        public static SqlConnection Connection
        {
            get
            {
                //string connectionString = ConfigurationManager.ConnectionStrings["SqlConnectionStr"].ConnectionString;
                if (connection == null)
                {
                    connection = new SqlConnection(connectionString);
                    connection.Open();
                }
                else if (connection.State == System.Data.ConnectionState.Closed)
                {
                    connection.Open();
                }
                else if (connection.State == System.Data.ConnectionState.Broken)
                {
                    connection.Close();
                    connection.Open();
                }
                return connection;
            }
        }

        /// <summary>
        /// 执行无参SQL语句
        /// </summary>
        public static int ExecuteCommand(string safeSql)
        {
            SqlCommand cmd = new SqlCommand(safeSql, Connection);
            int result = cmd.ExecuteNonQuery();
            return result;


            //int result = 0;

            //using (SqlConnection conn = new SqlConnection(connectionString))
            //{
            //    conn.Open();

            //    SqlCommand cmd = new SqlCommand(safeSql, Connection);
            //    result = cmd.ExecuteNonQuery();

            //    conn.Close();
            //}
            //return result;

        }

        /// <summary>
        /// 执行有参SQL语句
        /// </summary>
        public static int ExecuteCommand(string sql, params SqlParameter[] values)
        {
            SqlCommand cmd = new SqlCommand(sql, Connection);
            cmd.Parameters.AddRange(values);
            return cmd.ExecuteNonQuery();
        }

        /// <summary>
        /// 执行无参SQL语句，并返回执行行数
        /// </summary>
        public static int GetScalar(string safeSql)
        {
            SqlCommand cmd = new SqlCommand(safeSql, Connection);
            int result = Convert.ToInt32(cmd.ExecuteScalar());
            return result;
        }

        /// <summary>
        /// 执行有参SQL语句，并返回执行行数
        /// </summary>
        public static int GetScalar(string sql, params SqlParameter[] values)
        {
            SqlCommand cmd = new SqlCommand(sql, Connection);
            cmd.Parameters.AddRange(values);
            int result = Convert.ToInt32(cmd.ExecuteScalar());
            return result;
        }

        /// <summary>
        /// 执行无参SQL语句，并返SqlDataReader
        /// </summary>
        public static SqlDataReader GetReader(string safeSql)
        {
            SqlCommand cmd = new SqlCommand(safeSql, Connection);
            SqlDataReader reader = cmd.ExecuteReader();
            return reader;
        }

        /// <summary>
        /// 执行有参SQL语句，并返SqlDataReader
        /// </summary>
        public static SqlDataReader GetReader(string sql, params SqlParameter[] values)
        {
            SqlCommand cmd = new SqlCommand(sql, Connection);
            cmd.Parameters.AddRange(values);
            SqlDataReader reader = cmd.ExecuteReader();
            return reader;
        }

        /// <summary>
        /// 返回DataTable
        /// </summary>
        /// <param name="sSQL"></param>
        /// <returns></returns>
        public static DataTable GetDataTable(string sSQL)
        {
            //DataTable dt = new DataTable();
            //SqlDataAdapter da = new SqlDataAdapter(sSQL, Connection);
            //da.Fill(dt);
            //return dt;


            DataTable dt = new DataTable(); 
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                conn.Open();
                SqlDataAdapter da = new SqlDataAdapter(sSQL, conn);
                da.Fill(dt);
                conn.Close();
            }
            return dt;

        }


        /// <summary>
        /// 执行有参返回 DataTable
        /// </summary>
        /// <param name="sql">sql语句</param>
        /// <param name="commandType"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public static DataTable GetDataTable(string sql, CommandType commandType = CommandType.Text, params SqlParameter[] parameters)
        {
            DataTable dataTable = new DataTable();

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                using (SqlCommand command = new SqlCommand(sql, connection))
                {
                    command.CommandType = commandType;

                    // 添加参数（如果有的话）
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters);
                    }

                    // 执行查询并填充DataTable
                    using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                    {
                        adapter.Fill(dataTable);
                    }
                }
            }

            return dataTable;
        }

        /// <summary>
        /// 执行多条SQL语句，实现数据库事务。
        /// </summary>
        /// <param name="SQLStringList">多条SQL语句</param>		
        public static int ExecuteSqlTran(List<String> SQLStringList)
        {
            string sConn = System.Configuration.ConfigurationSettings.AppSettings["SqlConnectionStr"].ToString();
            using (SqlConnection conn = new SqlConnection(sConn))
            {
                conn.Open();
                SqlCommand cmd = new SqlCommand();
                cmd.Connection = conn;
                SqlTransaction tx = conn.BeginTransaction();
                cmd.Transaction = tx;
                try
                {
                    int count = 0;
                    for (int n = 0; n < SQLStringList.Count; n++)
                    {
                        string strsql = SQLStringList[n];
                        if (strsql.Trim().Length > 1)
                        {
                            cmd.CommandText = strsql;
                            count += cmd.ExecuteNonQuery();
                        }
                    }
                    tx.Commit();
                    return count;
                }
                catch
                {
                    tx.Rollback();
                    return 0;
                }
                finally
                {
                    if (conn != null)
                    {
                        conn.Close();
                    } 
                }

            }
        }

        /// <summary>
        /// 执行多条SQL语句，实现数据库事务。返回错误字符串
        /// </summary>
        /// <param name="SQLStringList">多条SQL语句</param>		
        public static string ExecuteSqlTranStr(List<String> SQLStringList)
        {
            string sConn = System.Configuration.ConfigurationSettings.AppSettings["SqlConnectionStr"].ToString();
            using (SqlConnection conn = new SqlConnection(sConn))
            {
                conn.Open();
                SqlCommand cmd = new SqlCommand();
                cmd.Connection = conn;
                SqlTransaction tx = conn.BeginTransaction();
                cmd.Transaction = tx;
                try
                {
                    int count = 0;
                    for (int n = 0; n < SQLStringList.Count; n++)
                    {
                        string strsql = SQLStringList[n];
                        if (strsql.Trim().Length > 1)
                        {
                            cmd.CommandText = strsql;
                            count += cmd.ExecuteNonQuery();
                        }
                    }
                    tx.Commit();
                    return count.ToString();
                }
                catch (Exception ex)
                {
                    string ss ="Err:"+ ex.Message.ToString();
                    tx.Rollback();
                    return ss;
                }
                finally
                {
                    if (conn != null)
                    {
                        conn.Close();
                    }
                }

            }
        }

        /// <summary>
        /// 获取表的最大编号
        /// </summary>
        /// <param name="sTable">表名</param>
        /// <param name="sField">字段名</param>
        /// <returns></returns>
        public static string GetMaxNo(string sTable, string sField)
        {
            string reStr = string.Empty;

            string strsql = "select max(" + sField + ") as MaxNo from " + sTable;

            object obj = GetSingle(strsql);

            if (obj != null)
            {
                reStr = obj.ToString();
            }
            else
            {
                reStr = "";
            }

            return reStr;
        }


        /// <summary>
        /// 执行一条计算查询结果语句，返回查询结果（object）。
        /// </summary>
        /// <param name="SQLString">计算查询结果语句</param>
        /// <returns>查询结果（object）</returns>
        public static object GetSingle(string SQLString)
        {
            string sConn = System.Configuration.ConfigurationSettings.AppSettings["SqlConnectionStr"].ToString();
            using (SqlConnection connection = new SqlConnection(sConn))
            {
                using (SqlCommand cmd = new SqlCommand(SQLString, connection))
                {
                    try
                    {
                        connection.Open();
                        object obj = cmd.ExecuteScalar();
                        if ((Object.Equals(obj, null)) || (Object.Equals(obj, System.DBNull.Value)))
                        {
                            return null;
                        }
                        else
                        {
                            return obj;
                        }
                    }
                    catch (System.Data.SqlClient.SqlException e)
                    {
                        connection.Close();
                        throw e;
                    }
                    finally
                    {
                        if (connection != null)
                        {
                            connection.Close();
                        }
                    }

                }
            }
        }


        /// <summary>
        /// 执行存储过程，返回DataSet
        /// </summary>
        /// <param name="storedProcName"></param>
        /// <param name="sqlPara"></param>
        /// <returns></returns>
        public static DataSet RunProcedureForDS(string storedProcName, SqlParameter[] sqlPara)
        {
            DataSet ds = null;

            string sConn = System.Configuration.ConfigurationSettings.AppSettings["SqlConnectionStr"].ToString();
            using (SqlConnection conn = new SqlConnection(sConn))
            {
                try
                {
                    conn.Open();
                    SqlCommand cmd = new SqlCommand(storedProcName, conn);


                    foreach (SqlParameter parameter in sqlPara)
                    {
                        if ((parameter.Direction == ParameterDirection.InputOutput || parameter.Direction == ParameterDirection.Input) &&
                            (parameter.Value == null))
                        {
                            parameter.Value = DBNull.Value;
                        }
                        cmd.Parameters.Add(parameter);
                    }



                    cmd.CommandType = CommandType.StoredProcedure;
                    SqlDataAdapter da = new SqlDataAdapter(cmd);
                    ds = new DataSet();
                    da.Fill(ds);
                }
                catch
                {
                    //tx.Rollback();
                    return null;
                }
                finally
                {
                    if (conn != null)
                    {
                        conn.Close();
                    }
                }
            }

            return ds;
        }


        /// <summary>
        /// 批量添加数据
        /// </summary>
        /// <param name="dt"></param>
        public static int BulkInsert(DataTable dt, string TableName)
        {
            string sConn = System.Configuration.ConfigurationSettings.AppSettings["SqlConnectionStr"].ToString();
            SqlTransaction tranProducts = null;
            SqlConnection connection = null;
            int Resualt = -1;
            try
            {
                using (connection = new SqlConnection(sConn))
                {
                    if (connection.State != ConnectionState.Open)
                    {
                        connection.Open();
                    }

                    tranProducts = connection.BeginTransaction();

                    using (SqlBulkCopy bulk = new SqlBulkCopy(sConn))
                    {
                        bulk.BatchSize = 100000;
                        bulk.DestinationTableName = TableName;
                        string dtColumns = string.Empty;

                        if (dt != null && dt.Rows.Count > 0)
                        {
                            string temp = "";
                            for (int i = 0; i < dt.Columns.Count; i++)
                            {
                                dtColumns = dt.Columns[i].ColumnName;
                                temp += dtColumns + ",";
                                bulk.ColumnMappings.Add(dt.Columns[i].ColumnName, dt.Columns[i].ColumnName);
                            }
                        }
                        bulk.WriteToServer(dt);
                        tranProducts.Commit();
                        Resualt = 1;
                    }
                }
            }
            catch (Exception ex)
            {
                tranProducts.Rollback();
                Resualt = -1;
            }
            finally
            {
                if (connection != null)
                {
                    connection.Close();
                }
            }
            return Resualt;
        }

        /// <summary>
        /// 执行参数化SQL事务
        /// </summary>
        /// <param name="sqlParamsList">SQL语句和参数的列表</param>
        /// <returns>执行结果</returns>
        public static string ExecuteSqlTranWithParams(List<KeyValuePair<string, SqlParameter[]>> sqlParamsList)
        {
            string sConn = System.Configuration.ConfigurationSettings.AppSettings["SqlConnectionStr"].ToString();
            using (SqlConnection conn = new SqlConnection(sConn))
            {
                conn.Open();
                SqlCommand cmd = new SqlCommand();
                cmd.Connection = conn;
                SqlTransaction tx = conn.BeginTransaction();
                cmd.Transaction = tx;
                try
                {
                    int count = 0;
                    foreach (var sqlParams in sqlParamsList)
                    {
                        string sql = sqlParams.Key;
                        SqlParameter[] parameters = sqlParams.Value;

                        if (!string.IsNullOrEmpty(sql))
                        {
                            cmd.Parameters.Clear();
                            cmd.CommandText = sql;
                            if (parameters != null)
                            {
                                cmd.Parameters.AddRange(parameters);
                            }
                            count += cmd.ExecuteNonQuery();
                        }
                    }
                    tx.Commit();
                    return count.ToString();
                }
                catch (Exception ex)
                {
                    tx.Rollback();
                    return "Err:" + ex.Message;
                }
                finally
                {
                    if (conn != null)
                    {
                        conn.Close();
                    }
                }
            }
        }


    }
    
}
