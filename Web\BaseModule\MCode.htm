﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" >
<head>
<meta http-equiv="Content-Type" content="text/html"; charset="utf-8" />
<title>识别码建档</title>
    <link href="../css/all.css" rel="stylesheet" type="text/css"/>
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css"/>
    <script src="../js/jquery-1.9.1.min.js" type="text/javascript"></script>
    <script type="text/javascript" src="../js/jQuery-2.2.0.min.js"></script>
    <script type="text/javascript" src="../js/bstable/js/bootstrap.min.js"></script>
    <script  type="text/javascript" src="../js/bstable/js/bootstrap-table.js"></script>
    <script  type="text/javascript" src="../js/bstable/js/bootstrap-table-zh-CN.min.js"></script>
    <script  type="text/javascript" src="../js/date/js/laydate.js"></script>
    <script type="text/javascript"  src="../js/layer/layer.js"></script>
    <script src="../js/BaseModule.js" type="text/javascript"></script>
    
    
    <script type="text/javascript">


        $(function() {

            var sCorpID = "we";  // GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&sFlag=10&CorpID=" + sCorpID,
                success: function(data) {
                    var parsedJson = jQuery.parseJSON(data);
                    //$('#Usertable').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetUserInfo&sFlag=10' });
                }
            })
        });

   </script>
   
   <script type="text/javascript">
       !function() {
           laydate.skin('danlan'); //切换皮肤，请查看skins下面皮肤库
           laydate({ elem: '#demo' });
           laydate({ elem: '#demo1' }); //绑定元素
       } ();
  </script>

  <script type="text/javascript">
      $(function() {
          $('#MCodetable').bootstrapTable({
              method: "get",
              striped: true,
              singleSelect: false,
              url: "../Service/BaseModuleAjax.ashx?OP=GetMCodetInfo&sFlag=20",
              dataType: "json",
              pagination: true, //分页
              pageSize: 10,
              pageNumber: 1,
              search: false, //显示搜索框
              height: window.screen.height - 280,
              contentType: "application/x-www-form-urlencoded",
              queryParams: null,
              columns: [
                {
                    checkbox: "true",
                    field: 'check',
                    align: 'center',
                    valign: 'middle'
                }
                ,
                {
                    title: "厂商识别码",
                    field: 'MCode',
                    width: 90,
                    align: 'center',
                    valign: 'middle'
                },
                {
                    title: '编码中心',
                    field: 'CodeCenter',
                    width: 140,
                    align: 'center',
                    valign: 'middle'
                },
                {
                    title: '当前值',
                    width: 80,
                    field: 'NowNum',
                    align: 'center',
                    valign: 'middle'
                },
                {
                    title: '颁布日期',
                    width: 100,
                    field: 'PublishDate',
                    align: 'center',
                    valign: 'middle'
                },
                {
                    title: '有效期日期',
                    width: 100,
                    field: 'ExpiryDate',
                    align: 'center'
                },
                {
                    title: '负责人',
                    field: 'InCharge',
                    align: 'center'
                },
                {
                    title: '在用',
                    field: 'IsUse',
                    align: 'center'
                },
                {
                    title: '备注',
                    width: 100,
                    field: 'Remark',
                    align: 'center'
                },
                {
                    title: '录入人',
                    field: 'InMan',
                    align: 'center'
                },
                {
                    title: '录入时间',
                    field: 'CInDate',
                    width: 150,
                    align: 'center'
                },
                {
                    title: '操作',
                    width: 180,
                    field: 'opear',
                    align: 'center',
                    formatter: function(value, row) {
                    var e = '<label class="find_labela" onclick="openDialog(\'' + row.MCode + '\')" style="color:White;font-size:10px;width: 35px; cursor:pointer;border-radius: 5px;padding: 3px 5px;background: #579fd8;">修改</label> ' +
                            '<label class="find_labela" onclick="DelCMater(\'' + row.MCode + '\',3)" style="color:White;font-size:10px;width: 35px; cursor:pointer;border-radius: 5px;padding: 3px 5px;background: #da542e;">删除</label> ' +
                            '<label class="find_labela" onclick="DelCMater(\'' + row.MCode + '\',4)" style="color:White;font-size:10px;width: 35px; cursor:pointer;border-radius: 5px;padding: 3px 5px;background: #da542e;">禁用</label> ' +
                            '<label class="find_labela" onclick="DelCMater(\'' + row.MCode + '\',5)" style="color:White;font-size:10px;width: 35px; cursor:pointer;border-radius: 5px;padding: 3px 5px;background: #579fd8;">启用</label> ';

                        return e;
                    }
                }
            ]
          });
      })

  </script>


  <script type="text/javascript">
      function openlayer(id) {   // 这个方法是在本界面打开一个新的界面，放入一个层，不过有点慢
          layer.open({
              type: 2,
              title: '添加信息',
              shadeClose: true,
              shade: 0.5,
              skin: 'layui-layer-rim',
              //            maxmin: true,
              closeBtn: 1,
              area: ['98%', '92%'],
              shadeClose: true,
              closeBtn: 1,
              content: 'AddCMater.htm'
              //iframe的url
          });
      }
  </script>
  

  <script type="text/javascript">
      function handleEvent(id) {

          alert(id);
      }
  </script>

  <script type="text/javascript">
      $(function() {
          $(".find_span").click(function() {
              $("#open").show();
              $(this).hide();
              $(".find_span1").show();
              $("#but_close02").hide();
              $("#but_open").hide();

          })
          $(".find_span1").click(function() {
              $("#open").hide();
              $(this).hide();
              $(".find_span").show();
              $("#but_open").show();
              $("#but_close02").show();

          })

      })
  </script>
  

  <script type="text/javascript">

      function openDialog(n) {
          var sFlag = "";

          if (n == "1") {   // 点击新增 
              sFlag = "20-1";
              $("#NewEdit").html("录入编码信息")  // 这几个字不能随便修改，JS那边用到  MCode,CodeCenter,PublishDate,ExpiryDate,InCharge
              $("#MCodeSaveBtn").removeAttr("disabled");
              $("#txtMCode").removeAttr("disabled");
              $('#txtAEFlag').val("1");

              $('#txtMCode').val("");
              $('#txtMCodeType').val("");
              $('#txtMCodeTypeR').val("");
              $('#txtCodeCenter').val("");
              $('#txtNowNum').val("");
              $('#txtPublishDate').val("");
              $('#txtExpiryDate').val("");
              $('#txtInCharge').val("");
              $('#txtRemark').val("");

              $("#div_warning").html("");
              $("#div_warning").hide();
              
              GetMCodeTypeList();   // 下拉选择系统类别--厂商识别码类别
          }
          else {  //  
              sFlag = "20-2";
              $("#NewEdit").html("修改编码信息")
              $("#MCodeSaveBtn").removeAttr("disabled");
              $("#txtMCode").attr({ "disabled": "disabled" });
              $('#txtAEFlag').val("2");

              $("#div_warning").html("");
              $("#div_warning").hide();

              GetMCodeTypeList();   // 下拉选择系统类别--厂商识别码类别

          }

          document.getElementById('light').style.display = 'block';
          document.getElementById('fade').style.display = 'block'
      }



  </script>

    <style type="text/css">
        #HDiv
        {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }
        .table > tbody > tr > td
        {
            border: 0px;
        }
        .black_overlay
        {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: black;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=88);
        }
    </style>
    

    <script type="text/javascript">
        $(function() {
            var $result = $('#events-result');
            $('#MCodetable').bootstrapTable({
                search: true,
                pagination: false,
                pageSize: 5,
                pageList: [5, 10, 15, 20],
                showColumns: true,
                showRefresh: false,
                showToggle: true,
                locale: "zh-CN",
                striped: true

            }).on('all.bs.table', function(e, name, args) {
                console.log('Event:', name, ', data:', args);
            }).on('click-row.bs.table', function(e, row, $element) {
            var sKind = encodeURI(row.MCode);


                $('#txtCodeCenter').val(row.CodeCenter);
                $('#txtMCodeType').val(row.MCodeType);
                $('#txtMCodeTypeR').val(row.MCodeType);
                $('#txtMCode').val(row.MCode);
                $('#txtNowNum').val(row.NowNum);
                $('#txtPublishDate').val(row.PublishDate);
                $('#txtExpiryDate').val(row.ExpiryDate);
                $('#txtInCharge').val(row.InCharge);
                $('#txtRemark').val(row.Remark);

                $('#txtDelNo').val(row.MCode);
                $('#txtDelName').val(row.CodeCenter);

                $result.text('Event: click-row.bs.table, data: ' + sKind);
            }).on('dbl-click-row.bs.table', function(e, row, $element) {
                $result.text('Event: dbl-click-row.bs.table, data: ' + JSON.stringify(row));
            }).on('search.bs.table', function(e, text) {
                $result.text('Event: search.bs.table, data: ' + text);
            });


        });

    </script>


    <script type="text/javascript">

        function DelCMater(n,i) {

            if (i == 3) {
                $("#Lab_Msg").html("*您即将 删除 下面这个厂商识别码，请确认！");
            }
            else if (i == 4) {
                $("#Lab_Msg").html("*您即将 禁用 下面这个厂商识别码，请确认！");
            }
            else {
                $("#Lab_Msg").html("*您即将 启用 下面这个厂商识别码，请确认！");
            }
            $("#txtAEFlag").val(i);
            document.getElementById('Div_Del').style.display = 'block';
            document.getElementById('fade').style.display = 'block'
        }


        function GetMCodeTypeList() {  // 下拉选择系统类别--厂商识别码类别  
            var CNo = "";
            var CKind = encodeURI("厂商识别前缀码");

            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/BaseModuleAjax.ashx?OP=GetMCodeTypeList&CFlag=39&CMNO=" + CNo + "&CKind=" + CKind,
                success: function(data) {
                    var parsedJson = eval(data).Msg;

                    var sKong = "<option value=''> </option>";
                    $("#txtMCodeType").empty();
                    $("#txtMCodeType").append(sKong + parsedJson);
                    $('#txtMCodeType').val($('#txtMCodeTypeR').val());
                }
            });
        }
        
        

        function closeDialog() {
            document.getElementById('light').style.display = 'none';
            document.getElementById('fade').style.display = 'none'
            document.getElementById('Div_Del').style.display = 'none';


            $("#div_warning").html("");
            $("#div_warning").hide();
            $("#divsuccess").hide();
            $("#MCodeSaveBtn").removeAttr("disabled");
        }
    

    </script>
	


</head>
<body>

<div class="div_find">

    <p style=" height:18px;">
      <label class="find_labela">厂商识别码：</label> <input type="text" id="txtSNo" class="find_input"/>
      <label class="find_labela">编码中心：</label>
         <select class="find_input" style=" height:25px;" id="txtSCenter">
            <option></option>
            <option value="GS1">GS1</option>
            <option value="MA码(IDCODE)">MA码(IDCODE)</option>
            <option value="AHM">AHM</option>
         </select>
      <label class="find_labela" id = "QDate">颁布日期：</label><input  type="date" id="txtSBDate" class="find_input"/> --- <input  type="date" id="txtSEDate" class="find_input"/>
      <input type="button" value="搜索" class="find_but" id="MCodeBut_open"> 
      <span class="find_span" ><i class="i_open01"></i>展开</span>
      <span class="find_span1" ><i class="i_close01"></i>收起</span> 
    </p>
    <p id="open" style="display:none;height:18px;" >
       <label class="find_labela" id = "Label1">有效日期：</label><input  type="date" id="txtSBEDate" class="find_input"/> --- <input  type="date" id="txtSEEDate" class="find_input"/> 
    </p>
</div>
    <p class="p_but" style="text-align:right">
      <!--  <i class="down_i" ></i><a href="#" class="add_a">导出</a>-->
        <i class="add2_i" ></i><a href="JavaScript:void(0)" onclick="openDialog(1)" style="color: Blue">添加</a>
   </p>
<p>
    <table data-url="json/data_alae_list.json" id="MCodetable" class="table_style" style="margin: 0 auto" >
    </table>
</p>

  <div id="light" style="display: none;position: absolute;top: 2%;left: 15%;right:15%;width:70%;height: 480px;border: 2px solid #21b6b4; background-color: white;z-index: 1002;overflow: auto;">
           <div  id="lightHead"  style="height:35px;">
               <label id="NewEdit"  style=" padding-top:5px;color:White;"></label>
               <label onclick="closeDialog()" style="float:right; margin:10px 10px 0 0; cursor:pointer;">X</label>
           </div>
            
            <table cellspacing="0" cellpadding="0" border='0' style="width: 98%;" class="table">
                <tr>
                    <td align="right" style=" width:120px;">编码中心<span style="color: Red">*</span></td>
                    <td>
                      <select class="form-control" style=" height:25px;" id="txtCodeCenter">
                        <option></option>
                        <option value="GS1">GS1</option>
                        <option value="MA码(IDCODE)">MA码(IDCODE)</option>
                        <option value="AHM">AHM</option>
                      </select>
                    </td>
                </tr>
                <tr>
                    <td align="right">前缀码<span style="color: Red"></span></td>
                    <td>
                      <select class="form-control" style="height:28px;" id="txtMCodeType">
                      <option></option>
                      </select>
                      <input type="text" class="form-control" id="txtMCodeTypeR" name="txtMCodeTypeR" style=" display:none;"/>
                    </td>
                </tr>
                <tr>
                    <td align="right">厂商识别码<span style="color: Red">*</span></td>
                    <td>
                      <input type="text" id="txtMCode" class="form-control"" />
                </tr>
                <tr>
                    <td align="right">当前流水值</td>
                    <td>
                       <input type="text" id="txtNowNum"  class="form-control"/>
                    </td>
                </tr>
                <tr>
                    <td align="right">颁布日期</td>
                    <td>
                       <input type="date" id="txtPublishDate" class="form-control"/>
                    </td>
                </tr>
                <tr>
                    <td align="right">有效日期</span></td>
                    <td>
                       <input type="date" id="txtExpiryDate" class="form-control"/>
                    </td>
                </tr>
                <tr>
                    <td align="right">负责人</span></td>
                    <td>
                       <input type="text" id="txtInCharge" class="form-control"/>
                    </td>
                </tr>
                <tr>
                  <td style=" width:100px; text-align:right;">
                    备注: 
                  </td>
                  <td>
                    <textarea class="form-control" id="txtRemark" name="txtRemark"  style="height:60px;"> </textarea>
                  </td>
                </tr>
            </table>
            <div id="div_warning" role="alert" style="text-align: center; display: none; color: Red">
                <strong id="divsuccess" style="color: Red"></strong>
            </div>
            <div class="input-group" style="display:none; ">
                  <span class="input-group-addon">.</span>
                  <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
                  <span class="input-group-addon">.</span>
                  <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
                  <span class="input-group-addon">.</span>
                  <input type="text" class="form-control" id="txtInName" name="txtInName" />
            </div>
            <div align="center">
                <input type='button' id="MCodeSaveBtn" value='保存' style="width: 50px; height: 30px;" />
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <input type='button' id="MCodeSaveClose" value='关闭' onclick='closeDialog()' style="width: 50px;  height: 30px;" />
            </div>
   </div>

   <div id="Div_Del"  style="display: none;position: absolute;top: 25%;left: 25%;right: 25%;width: 50%;height: 220px;border: 2px solid orange; background-color: white;z-index: 1002;overflow: auto;">
           <div style="background-color:Red; height:40px; text-align:center; ">
              <label style="font-size: 14px; color:White; padding:10px;" id="Lab_Msg">*您即将删除下面这个厂商识别码，请确认！</label>
           </div>
           
           <label id="Label_Type" style="font-size: 10px; color: Red;"> </label>
           <label id="LMID" style="font-size: 10px; color: Red;"> </label>
           <div class="input-group"style="width: 98%;" >
               <span class="input-group-addon">厂商识别码信息：</span>
                    <input type="text" class="form-control" id="txtDelName" name="txtDelName" readonly=readonly />
                    <input type="text" class="form-control" id="txtDelNo" name="txtDelNo" readonly=readonly/>
           </div>
           <div id="div_warning2" role="alert" style="text-align: center; display: none; color: Red">
                    <strong id="divsuccess2" style="color: Red"></strong>
           </div>
           <br />
           <div align="center">
                    <input type='button' id="MCode_Del_Btn" value='提交' style="width: 50px; height: 30px;font-size: 16px; color: Red;" />
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input type='button' id="Button2" value='取消' onclick='closeDialog()' style="width: 50px;height: 30px;" />
           </div>
    </div>
    
    
   <div id="fade" class="black_overlay">
   </div>

</body>
</html>