﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>质量控制执行记录</title>

    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <link href="../css/orderinfo.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <script src="../layui/layui.js" type="text/javascript"></script>
    <link href="../js/skin/layer.css" rel="stylesheet" />

    <script src="../js/layer/layer.js" type="text/javascript"></script>
    <script src="../js/PrdAssist.js" type="text/javascript"></script>

    <link href="../css/XC.css" rel="stylesheet" />

    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }
        //
        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        $('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=28-1' });
                    }
                }
            })
        });

    </script>


    <script type="text/javascript">
        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#QCControlExelist',
                id: 'QCControlExelistID',
                url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=20',
                height: 'full-80',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'ENo', title: '执行编号', width: 150, sort: true },
                    { field: 'Spec', title: '型号', width: 100 },
                    { field: 'OrderNo', title: '工单号', width: 100, sort: true },
                    { field: 'Status', title: '状态', width: 80 },
                    { field: 'QPNo', title: '质量控制计划', width: 150 },
                    { field: 'Ver', title: '版本', width: 50 },
                    { field: 'MaterNo', title: '物料编码', width: 150 },
                    { field: 'MaterName', title: '物料名称', width: 100 },
                    { field: 'InMan', title: '创建人', width: 90 },
                    { field: 'InDate', title: '创建时间', width: 150 },
                    { field: 'op', title: '操作', width: 130, toolbar: '#barDemo', fixed: 'right' }
                ]],
                page: true,


            });

            //监听是否选中操作
            table.on('checkbox(QCControlExelist)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);

            });


            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(QCControlExelist)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

            });

            //监听单元格编辑
            table.on('edit(QCControlExelist)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });


            //监听行工具事件
            table.on('tool(QCControlExelist)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值
                if (layEvent === 'del') {

                    if (data.Status != "未执行") {
                        layer.msg('执行中/已完成数据，不可以删除！！');
                        return;
                    }

                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("确定要删除该质量控制执行记录吗？执行编号：")

                    //设置删除的对象
                    $("#hint-value").html(data.ENo)

                    $("#txtDelENo").val(data.ENo)

                    $("#txtDelOP").val("35")

                }
                else if (layEvent === 'detail')//显示明细页面
                {
                    $('#head-title1').html("质量控制执行详情");
                    $('#txtAEFlag').val("20-1");

                    $("#txtENo").val(data.ENo);
                    $("#txtQCROrderNo").val(data.OrderNo);
                    $("#txtQCRMaterNo").val(data.MaterNo);
                    $("#txtQCRMaterName").val(data.MaterName);
                    $("#txtQCRMaterSpec").val(data.Spec);
                    $("#txtQCRStatus").val(data.Status);

                    $("#txtQCRQPNo").val(data.QPNo);
                    $("#txtQCRVer").val(data.Ver);
                    $("#txtQCRInMan").val(data.InMan);

                    $("#txtQCRInDate").val(data.InDate);

                    $("#div_warning").html("");
                    $("#div_warning").hide();

                    //弹窗显示质量控制执行记录明细页
                    ShowQCControlExeDetail(data.ENo);

                    //if (data.Status == "未执行" || data.Status == "") {
                    //    document.getElementById('btn_CRCP_open').style.display = 'block';
                    //    document.getElementById('OrderBut_open').style.display = 'block';

                    //}
                    //else
                    //{

                    document.getElementById('btn_CRCP_open').style.display = 'none';
                    document.getElementById('OrderBut_open').style.display = 'none';
                    $('#btn_QCControlExeDetailSave').hide();
                    //}

                    document.getElementById('div_table_QCControlPlanDetailAdd').style.display = 'none';
                    document.getElementById('div_table_QCControlExeDetailList').style.display = 'block';

                    $("#ShowOne").css("display", "block")
                    $("#ShowOne-fade").css("display", "block")


                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }


            });


            //  查询表头 --
            $('#QCControlExeBut_open').click(function () {

                var sQPNo = $("#txtSENo").val();  //质量控制执行记录编号
                var sVer = encodeURI($("#txtSCROrderNo").val());  //工单号


                var Data = '';
                var Params = { No: sQPNo, Ver: sVer, Item: "", Status: "", BDate: "", EDate: "", A: sVer, B: "", C: "", D: "", E: "" };
                var Data = JSON.stringify(Params);

                table.reload('QCControlExelistID', {
                    method: 'post',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=20&Data=' + Data,
                    where: {
                        'No': sQPNo,
                        'name': sVer
                    }, page: {
                        curr: 1
                    }
                });
            });



        });


        //var currentTime = new Date();
        //var currentDate = currentTime.getDate(); //获取当前日期（1-31）
        //var currentMonth = currentTime.getMonth() + 1; //获取当前月份（0-11）
        //var currentYear = currentTime.getFullYear(); //获取当前年份（例如2019）
        //var currentHour = currentTime.getHours(); //获取当前小时数（0-23）
        //var currentMinute = currentTime.getMinutes(); //获取当前分钟数（0-59）
        //var currentSecond = currentTime.getSeconds(); //获取当前秒数（0-59）通过上述代码，我们可以轻松地获取当前时间的各个部分，并在我们的应用程序中使用它们。 格式化时间： 有时，我们需要以特定格式显示时间，这时候我们可以使用以下代码将时间格式化：
        function formatDate(date) {
            var hours = date.getHours();
            var minutes = date.getMinutes();
            var seconds = date.getSeconds();
            var ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12;
            hours = hours ? hours : 12;
            minutes = minutes < 10 ? '0' + minutes : minutes;
            seconds = seconds < 10 ? '0' + seconds : seconds;
            var strTime = hours + ':' + minutes + ':' + seconds + ' ' + ampm;
            return strTime;
        }

        function openDialog(n) {
            if (n == 1) { // 新增质量控制执行记录弹窗
                $('#head-title1').html("新增质量控制执行记录");
                $('#txtAEFlag').val("16");

                $("#txtENo").val("系统自动产生");
                $("#txtQCROrderNo").val("");
                $("#txtQCRMaterNo").val("");
                $("#txtQCRMaterSpec").val("");
                $("#txtQCRStatus").val("");
                $("#txtQCRMaterName").val("");

                $("#txtQCRQPNo").val("");
                $("#txtQCRVer").val("");
                $("#txtQCRInMan").val($("#txtInMan").val());

                var date = new Date();
                var datetime = date.toLocaleString(); // 获取本地时间
                $("#txtQCRInDate").val(datetime);

                $("#div_warning").html("");
                $("#div_warning").hide();
                ShowQCControlPlanDetailAdd("", "");
                //ShowQCControlExeDetail("");

                document.getElementById('div_table_QCControlPlanDetailAdd').style.display = 'block';
                document.getElementById('div_table_QCControlExeDetailList').style.display = 'none';

                document.getElementById('btn_CRCP_open').style.display = 'block';
                document.getElementById('OrderBut_open').style.display = 'block';

                $('#btn_QCControlExeDetailSave').show();

                $("#ShowOne").css("display", "block")
                $("#ShowOne-fade").css("display", "block")

            }
            else if (n == 2)//选择工单信息弹窗层
            {
                $('#head-title2').html("选择工单信息");
                $('#txtAEFlag').val("0");

                layui.use('table', function () {
                    var table = layui.table,
                        form = layui.form;
                    table.render({
                        elem: '#table__OrderInfoSelectList',
                        id: 'MaterID',
                        url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=150',
                        height: '400',
                        cellMinWidth: 80,
                        count: 50, //数据总数 服务端获得
                        limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                        limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                        cols: [[
                            { type: 'numbers' },
                            { field: 'OrderNo', title: '工单编号', width: 150 },
                            { field: 'MaterNo', title: '物料编码', width: 150 },
                            { field: 'MaterName', title: '物料描述', width: 200 },
                            { field: 'MaterSpec', title: '型号', width: 90 },
                            { field: 'DeptNo', title: '部门', width: 90 },
                            { field: 'BOMNo', title: 'BOM版本', width: 90 },
                            { field: 'RequireDate', title: '需求时间', width: 200 },
                            { field: 'PlanStartDate', title: '计划开始时间', width: 200 },
                            { field: 'PlanEndDate', title: '计划结束时间', width: 200 },
                            { field: 'OrderNum', title: '工单数量', minWidth: 80 },
                            { field: 'Unit', title: '单位', width: 50 },
                            { field: 'OrderKind', title: '工单类型', width: 80 },
                            { field: 'OPStatus', title: '启用状态', width: 90 },
                            { field: 'PrdctStatus', title: '执行状态', width: 90 },
                            /* { field: 'OrderType', title: '工单属性', width: 80 },*/
                            { field: 'op', title: '操作', width: 80, toolbar: '#barDemo_OrderDetail', fixed: 'right' }
                        ]],
                        page: true,
                        even: true
                    });

                    //监听行工具事件
                    table.on('tool(table__OrderInfoSelectList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                        var data = obj.data, //获得当前行数据
                            layEvent = obj.event; //获得 lay-event 对应的值

                        if (layEvent == 'OrderSelect') {

                            $('#txtQCROrderNo').val(data.OrderNo);
                            $('#txtQCRMaterNo').val(data.MaterNo);
                            $('#txtQCRMaterSpec').val(data.MaterSpec);
                            $('#txtQCRMaterName').val(data.MaterName);

                            // ShowOrderRelevInfo(data.OrderNo, "", data.MaterNo);
                            closeDialog(1);
                            //document.getElementById('divOrderDetail').style.display = 'block';
                            //document.getElementById('fade').style.display = 'block'
                        }

                    });

                    //  查询工单选择弹窗界面- 工单信息
                    $('#OrderInfBut_open').click(function () {

                        var sSNo = $("#txtSOrderNo").val();  //工单或产品编码
                        var sBDate = "";  //
                        var sEDate = ""; //
                        var sMNoe = $("#txtSMaterNo").val();;  //
                        var sMName = $("#txtSMaterSpec").val();; //

                        var Data = '';
                        var Params = { No: "", Item: "", Name: "", MNo: sMNoe, MName: sMName, Status: "", BDate: "", EDate: "", A: sSNo, B: "", C: "", D: "" };
                        var Data = JSON.stringify(Params);

                        table.reload('MaterID', {
                            method: 'post',
                            url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=150&Data=' + Data,
                            where: {
                                'No': sSNo,
                                'name': sMNoe
                            }, page: {
                                curr: 1
                            }
                        });
                    });


                });


                /*table__OrderInfoSelectList*/

                $("#div_warningSelectOrderInfo").html("");
                $("#div_warningSelectOrderInfo").hide();

                $('#ShowTow').css("display", "block");
            }
            else if (n == 3)//选择质量控制计划弹窗层
            {
                $('#head-title3').html("选择质量控制计划");
                $('#txtAEFlag').val("0");

                //ShowQCControlPlanDetailAdd(sQPNo, sVer);

                layui.use('table', function () {
                    var table = layui.table,
                        form = layui.form;
                    table.render({
                        elem: '#table_QCControlPlanSelectList',
                        id: 'QCControlPlanSelectListID',
                        url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=19',
                        height: '400',
                        cellMinWidth: 80,
                        //toolbar: 'default',//工具栏
                        //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                        count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                        limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                        limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                        cols: [[
                            { type: 'numbers' },
                            { field: 'QPNo', title: '质量控制计划编号', width: 200 },
                            { field: 'Ver', title: '版本', width: 100 },
                            { field: 'Spec', title: '型号', width: 200 },
                            { field: 'Remark', title: '备注', width: 300 },
                            { field: 'InMan', title: '创建人', width: 90 },
                            { field: 'InDate', title: '创建时间', width: 200 },
                            { field: 'op', title: '操作', width: 180, toolbar: '#barDemo_QPDetail', fixed: 'right' }
                        ]],
                        page: true,


                    });


                    //监听行工具事件
                    table.on('tool(table_QCControlPlanSelectList)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                        var data = obj.data, //获得当前行数据
                            layEvent = obj.event; //获得 lay-event 对应的值
                        if (layEvent === 'QPSelect') {//选择质量控制计划后，同步保存质量控制执行内容清单

                            $("#txtQCRQPNo").val(data.QPNo);
                            $("#txtQCRVer").val(data.Ver);
                            ShowQCControlPlanDetailAdd(data.QPNo, data.Ver);

                            $('#ShowThree').css("display", "none")
                        }
                        else if (layEvent === 'QPDetail')//显示明细页面
                        {
                            $('#head-title4').html("质量控制计划信息详情");
                            //$('#txtAEFlag').val("11");

                            $("#txtItemCPDNo").val(data.QPNo);
                            $("#txtItemCPDVer").val(data.Ver);


                            $("#div_warningDetail").html("");
                            $("#div_warningDetail").hide();
                            //弹窗显示质量控制计划明细页
                            ShowQCControlPlanDetailList(data.QPNo, data.Ver);

                            $('#ShowFour').css("display", "block")


                            // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                        }

                    });

                    //  查询质量控制计划
                    $('#QCControlPlanBut_open').click(function () {

                        var sQPNo = $("#txtSQPNo").val();  //
                        var sVer = encodeURI($("#txtSVer").val());  //

                        var Data = '';
                        var Params = { No: sQPNo, Ver: sVer, Item: "", Status: "", BDate: "", EDate: "", A: sVer, B: "", C: "", D: "", E: "" };
                        var Data = JSON.stringify(Params);

                        table.reload('QCControlPlanSelectListID', {
                            method: 'post',
                            url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=19&Data=' + Data,
                            where: {
                                'No': sQPNo,
                                'name': sVer
                            }, page: {
                                curr: 1
                            }
                        });
                    });


                });

                $("#div_warningQCControlPlan").html("");
                $("#div_warningQCControlPlan").hide();

                $('#ShowThree').css("display", "block")
            }
            else if (n = 4)//新增质量控制结果，数据录入弹窗
            {
                $('#head-title6').html("新增质量控制执行结果");

                $("#txtAEFlag").val("33-1");

                $("#txtItemEditIncludeOne").removeProp("checked");
                $("#txtItemEditIncludeTwo").removeProp("checked");

                if ($("#txtItemIncludeOne").is(':checked')) {
                    $("#txtItemEditIncludeOne").prop('checked', true);
                }

                if ($("#txtItemIncludeTwo").is(':checked')) {
                    $("#txtItemEditIncludeTwo").prop('checked', true);
                }

                //if (data.IncludeOne == "是") {
                //    $("#txtItemIncludeOne").prop('checked', true);
                //}
                //if (data.IncludeTwo == "是") {
                //    $("#txtItemIncludeTwo").prop('checked', true);
                //}

                $("#txtItemEditENo").val($("#txtItemENo").val());
                $("#txtEditItemNo").val($("#txtItemNo").val());
                $("#txtItemEditThisValue").val($("#txtItemThisValue").val());
                $("#txtItemEditToValue").val($("#txtItemToValue").val());

                $("#txtItemEditRangeKind").val($("#txtItemRangeKind").val());
                $("#txtItemEditDataType").val($("#txtItemDataType").val());

                $("#txtItemEditTimes").val("");
                $("#txtItemEditSerialNo").val("");
                $("#txtItemEditEXEValue").val("");
                $("#txtItemEditEXEResult").val("");

                $("#div_warningSelectQCControlDetailRsultEdit").html("");
                $("#div_warningSelectQCControlDetailRsultEdit").hide();

                if ($("#txtItemEditDataType").val() == "布尔型") {

                    $("#txtItemEditEXEValue").attr({ "disabled": "disabled" });
                }
                else {
                    $("#txtItemEditEXEValue").removeAttr("disabled");
                }

                $('#ShowSix').css("display", "block");

            }
        }

        function closeDialog(s) {
            if (s == 1)//关闭选择工单弹层页面
            {
                $('#ShowTow').css("display", "none");

                $('#ShowThree').css("display", "none")

                $('#ShowSix').css("display", "none");
            }
            else if (s == 2)//关闭详细页面
            {
                $("#ShowOne").css("display", "none")
                $("#ShowOne-fade").css("display", "none")

                $('#ShowThree').css("display", "none")

                $('#ShowTow').css("display", "none");


            }
            else if (s = 3) {

                $('#ShowFour').css("display", "none")

                $('#ShowFive').css("display", "none")
            }


        }

        //自动更新单据明细项状态
        function GetPrdAssistStatusForNoItem(sENo, sItem) {
            //var txtENo = $("#txtENo").val();
            sURL = '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistStatusForNo&CFlag=66-1&CNO=' + sENo + '&CMNO=' + sItem;
            var Data = '';
            $.ajax({
                url: sURL,
                data: { Data: Data },
                success: function (data) {                    //数据成功读取后的处理

                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.length > 0) {
                        //sCompNo = parsedJson[0].CompanyNo;
                        //sCompName = parsedJson[0].CompName;
                        $("#txtItemStatus").val(parsedJson[0].Status);
                        //$("#txtFWName").val(parsedJson[0].CompName);
                    }
                },
                error: function (xhr, status, error) {
                    //错误处理
                    $("body").append("<div>读取数据失败：" + error + "</div>");
                }
            });
        }

        //自动更新单据状态
        function GetPrdAssistStatusForNo(sENo) {
            //var txtENo = $("#txtENo").val();
            sURL = '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistStatusForNo&CFlag=66&CNO=' + sENo;
            var Data = '';
            $.ajax({
                url: sURL,
                data: { Data: Data },
                success: function (data) {                    //数据成功读取后的处理

                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.length > 0) {
                        //sCompNo = parsedJson[0].CompanyNo;
                        //sCompName = parsedJson[0].CompName;
                        $("#txtQCRStatus").val(parsedJson[0].Status);
                        //$("#txtFWName").val(parsedJson[0].CompName);
                    }
                },
                error: function (xhr, status, error) {
                    //错误处理
                    $("body").append("<div>读取数据失败：" + error + "</div>");
                }
            });
        }


        /// 显示质量控制执行执行记录明细项信息
        ///daiwanshan
        function ShowQCControlExeDetail(sENo) {
            $('#txtAEFlag').val("0");
            //var sQPNo = $("#txtCPDNo").val();  //
            //var sVer = $("#txtCPDVer").val();  //

            /* var Data = '';*/
            var Params = { No: sENo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#table_QCControlExeDetailList',
                    id: 'QCControlExeDetaiID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=20-1&Data=' + Data,
                    height: '360',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    //ItemNo, [TechNode], [TechStep] , [TSource], [PFMEA],     [Device], [IDNO], [Product], [Process], [ProcessTX] ,
                    //[Measurement], [SampleNum], [SampleFreq], [ControlWay], [ResponsePlan],         [ThisValue], [IncludeOne], [ToValue], [IncludeTwo] ,           [RangeKind], [DataType], [SNo]
                    cols: [[
                        { type: 'numbers' },
                        { field: 'ItemNo', title: '内容编号', width: 100 },
                        { field: 'TechNode', title: '工艺节点', width: 100 },
                        { field: 'TechStep', title: '工艺步骤', width: 100 },
                        { field: 'TSource', title: '来源', width: 100 },
                        { field: 'PFMEA', title: 'PFMEA关联', width: 100 },
                        { field: 'Device', title: '工装及夹具', width: 100 },
                        { field: 'IDNO', title: 'ID/NO', width: 100 },
                        { field: 'Product', title: '产品', width: 100 },

                        { field: 'Process', title: '过程', width: 100 },
                        { field: 'ProcessTX', title: '过程特性/公差', width: 100 },
                        { field: 'Measurement', title: '测量技术', width: 100 },
                        { field: 'SampleNum', title: '样品数量', width: 100 },
                        { field: 'SampleFreq', title: '样品频度', width: 100 },
                        { field: 'ControlWay', title: '控制方法', width: 100 },
                        { field: 'ResponsePlan', title: '反应计划', width: 100 },
                        { field: 'ThisValue', title: '下限', width: 100 },


                        { field: 'IncludeOne', title: '是否包含下限', width: 100 },
                        { field: 'ToValue', title: '上限', width: 100 },
                        { field: 'IncludeTwo', title: '是否包含上限', width: 100 },
                        { field: 'RangeKind', title: '类别', width: 100 },
                        { field: 'DataType', title: '数据类型', width: 100 },
                        { field: 'ItemStatus', title: '状态', width: 100 },
                        { field: 'SNo', title: '序号', width: 40 },



                        //{ field: 'InMan', title: '录入人', minWidth: 90 },
                        //{ field: 'InDate2', title: '录入时间', width: 150 },
                        { field: 'op', title: '操作', width: 180, toolbar: '#bar_OrderQCControlPlanRocDetail', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(table_QCControlExeDetailList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值

                    if (layEvent == 'QCResult') {//质量控制结果录入

                        var sNo = $("#txtENo").val();
                        //var sItemNo = data.ItemNo;
                        var sResult = "OK";
                        $("#txtItemIncludeOne").removeProp("checked");
                        $("#txtItemIncludeTwo").removeProp("checked");

                        if (data.IncludeOne == "是") {
                            $("#txtItemIncludeOne").prop('checked', true);
                        }
                        if (data.IncludeTwo == "是") {
                            $("#txtItemIncludeTwo").prop('checked', true);
                        }

                        $("#txtItemENo").val(sNo);
                        $("#txtItemNo").val(data.ItemNo);
                        $("#txtItemThisValue").val(data.ThisValue);
                        $("#txtItemToValue").val(data.ToValue);

                        $("#txtItemRangeKind").val(data.RangeKind);
                        $("#txtItemDataType").val(data.DataType);
                        $("#txtItemStatus").val(data.ItemStatus);

                        if (data.ItemStatus == "已完成") {
                            document.getElementById('btn_SelectQCControlPlanInfoADD').style.display = 'none';
                        }
                        else {
                            document.getElementById('btn_SelectQCControlPlanInfoADD').style.display = 'block';
                        }

                        ShowQCControlExeDetailResult(sNo, data.ItemNo);

                    }
                    else if (layEvent == 'QCEnd')//质量控制结果结果确认
                    {

                        if (data.ItemStatus == "已完成") {
                            layer.msg('已确认完成，不必重复操作！');
                            return;
                        }

                        $("#ShowDel").css("display", "block")
                        $("#ShowDel-fade").css("display", "block")

                        $("#XC-Icon").removeClass()
                        $("#hint-value").removeClass()
                        $("#XC-Icon").addClass("XC-Icon XC-Btn-Green")
                        $("#hint-value").addClass("XC-Font-Green")

                        //设置删除的标题
                        $("#hint-title").html("确定要结束该质量控制执行内容吗？编号：")

                        //设置删除的对象
                        $("#hint-value").html(data.ItemNo)

                        $("#txtDelItemNo").val(data.ItemNo)

                        $("#txtDelOP").val("34")

                    }


                });


            });  // layui.use('table', function () {


        }


        //显示质量控制执行结果
        function ShowQCControlExeDetailResult(sENo, sItemNo) {
            $('#txtAEFlag').val("0");
            //var sQPNo = $("#txtCPDNo").val();  //
            //var sVer = encodeURI($("#txtCPDVer").val());  //

            /* var Data = '';*/
            var Params = { No: sENo, Name: "", Item: sItemNo, Status: "", BDate: "", EDate: "", A: sItemNo, B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#table_QCControlExeDetailResult',
                    id: 'QCControlExeDetailResultID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=20-2&Data=' + Data,
                    height: '260',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    //ItemNo, [TechNode], [TechStep] , [TSource], [PFMEA],     [Device], [IDNO], [Product], [Process], [ProcessTX] ,
                    //[Measurement], [SampleNum], [SampleFreq], [ControlWay], [ResponsePlan],         [ThisValue], [IncludeOne], [ToValue], [IncludeTwo] ,           [RangeKind], [DataType], [SNo]
                    //b.ThisValue, b.IncludeOne, b.ToValue, b.IncludeTwo, b.RangeKind, b.DataType, a.ENo, a.ItemNo, a.SerialNo, a.Times, a.EXEMan, a.EXEDate, a.EXEValue, a.EXEResult, a.CompanyNo, a.InMan, a.InDate, a.Remark
                    //a.ENo, a.ItemNo, a.Times, b.ThisValue, b.IncludeOne, b.ToValue, b.IncludeTwo, b.RangeKind, b.DataType, b.ItemStatus, a.SerialNo, a.EXEMan, a.EXEDate, a.EXEValue, a.EXEResult
                    cols: [[
                        { type: 'numbers' },
                        //{ field: 'ENo', title: '控制执行编号', width: 100 },
                        //{ field: 'ItemNo', title: '内容编号', width: 100 },
                        //{ field: 'ThisValue', title: '下限', width: 100 },
                        //{ field: 'IncludeOne', title: '是否包含下限', width: 100 },
                        //{ field: 'ToValue', title: '上限', width: 100 },

                        //{ field: 'IncludeTwo', title: '是否包含上限', width: 100 },
                        //{ field: 'RangeKind', title: '类别', width: 100 },
                        //{ field: 'DataType', title: '数据类型', width: 100 },
                        { field: 'Times', title: '执行次数', width: 100 },
                        //{ field: 'ItemStatus', title: '状态', width: 100 },

                        { field: 'SerialNo', title: '抽样对象', width: 100 },
                        { field: 'EXEMan', title: '负责人', width: 100 },
                        { field: 'EXEDate', title: '检验日期', width: 100 },
                        { field: 'EXEValue', title: '检验值', width: 100 },
                        { field: 'EXEResult', title: '结果', width: 100 },

                        //{ field: 'InMan', title: '录入人', minWidth: 90 },
                        //{ field: 'InDate2', title: '录入时间', width: 150 },
                        { field: 'op', title: '操作', width: 130, toolbar: '#bar_QCControlExeDetailResult', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(table_QCControlExeDetailResult)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值

                    if (layEvent == 'QCRstDel')//删除质量控制执行结果
                    {
                        if ($("#txtItemStatus").val() == "已完成") {
                            layer.msg('当前检测项已确认完成，不可以删除！');
                            return;
                        }


                        $("#ShowDel").css("display", "block")
                        $("#ShowDel-fade").css("display", "block")

                        $("#XC-Icon").removeClass()
                        $("#hint-value").removeClass()
                        $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                        $("#hint-value").addClass("XC-Font-Red")

                        //设置删除的标题
                        $("#hint-title").html("确定要删除该质量控制执行结果吗？序列号：")

                        //设置删除的对象
                        $("#hint-value").html(data.SerialNo)

                        $("#txtDelSerialNo").val(data.SerialNo)

                        $("#txtDelOP").val("35-1")

                    }
                    else if (layEvent == 'QCRstEdit')//修改控制执行结果信息
                    {
                        if ($("#txtItemStatus").val() == "已完成") {
                            layer.msg('当前检测想已确认完成，测量数据不可以修改！');
                            return;
                        }


                        $("#txtAEFlag").val("34-1");

                        $("#txtItemEditIncludeOne").removeProp("checked");
                        $("#txtItemEditIncludeTwo").removeProp("checked");

                        if ($("#txtItemIncludeOne").is(':checked')) {
                            $("#txtItemEditIncludeOne").prop('checked', true);
                        }

                        if ($("#txtItemIncludeTwo").is(':checked')) {
                            $("#txtItemEditIncludeTwo").prop('checked', true);
                        }

                        //if (data.IncludeOne == "是") {
                        //    $("#txtItemIncludeOne").prop('checked', true);
                        //}
                        //if (data.IncludeTwo == "是") {
                        //    $("#txtItemIncludeTwo").prop('checked', true);
                        //}

                        $("#txtItemEditENo").val($("#txtItemENo").val());
                        $("#txtEditItemNo").val($("#txtItemNo").val());
                        $("#txtItemEditThisValue").val($("#txtItemThisValue").val());
                        $("#txtItemEditToValue").val($("#txtItemToValue").val());

                        $("#txtItemEditRangeKind").val($("#txtItemRangeKind").val());
                        $("#txtItemEditDataType").val($("#txtItemDataType").val());

                        $("#txtItemEditTimes").val(data.Times);
                        $("#txtItemEditEXEResult").val(data.EXEResult);


                        $("#txtItemEditEXEValue").val(data.EXEValue);
                        $("#txtItemEditSerialNo").val(data.SerialNo);

                        if ($("#txtItemEditDataType").val() == "布尔型") {

                            $("#txtItemEditEXEValue").attr({ "disabled": "disabled" });
                        }
                        else {
                            $("#txtItemEditEXEValue").removeAttr("disabled");
                        }

                        $("#div_warningSelectQCControlDetailRsultEdit").html("");
                        $("#div_warningSelectQCControlDetailRsultEdit").hide();

                        $('#ShowSix').css("display", "block");
                    }

                });


                $('#ShowFive').css("display", "block");

            });  // layui.use('table', function () {
        }

        /// 显示质量控制计划明细项信息
        ///daiwanshan
        function ShowQCControlPlanDetailList(sQPNo, sVer) {

            //var sQPNo = $("#txtCPDNo").val();  //
            //var sVer = encodeURI($("#txtCPDVer").val());  //


            var Data = '';
            var Params = { No: sQPNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: sVer, B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#table_QCControlPlanDetailList',
                    id: 'QCControlPlanDetailID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=19-1&Data=' + Data,
                    height: '500',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers' },
                        { field: 'ItemNo', title: '内容编号', width: 100 },
                        { field: 'TechNode', title: '工艺节点', width: 100 },
                        { field: 'TechStep', title: '工艺步骤', width: 100 },
                        { field: 'TSource', title: '来源', width: 100 },
                        { field: 'PFMEA', title: 'PFMEA关联', width: 100 },
                        { field: 'Device', title: '工装及夹具', width: 100 },
                        { field: 'IDNO', title: 'ID/NO', width: 100 },
                        { field: 'Product', title: '产品', width: 100 },

                        { field: 'Process', title: '过程', width: 100 },
                        { field: 'ProcessTX', title: '过程特性/公差', width: 100 },
                        { field: 'Measurement', title: '测量技术', width: 100 },
                        { field: 'SampleNum', title: '样品数量', width: 100 },
                        { field: 'SampleFreq', title: '样品频度', width: 100 },
                        { field: 'ControlWay', title: '控制方法', width: 100 },
                        { field: 'ResponsePlan', title: '反应计划', width: 100 },
                        { field: 'ThisValue', title: '下限', width: 100 },


                        { field: 'IncludeOne', title: '是否包含下限', width: 100 },
                        { field: 'ToValue', title: '上限', width: 100 },
                        { field: 'IncludeTwo', title: '是否包含上限', width: 100 },
                        { field: 'RangeKind', title: '类别', width: 100 },
                        { field: 'DataType', title: '数据类型', width: 100 },
                        { field: 'Status', title: '状态', width: 100 },
                        { field: 'Remark', title: '备注', width: 100 },

                        { field: 'SNo', title: '序号', width: 40 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate', title: '录入时间', width: 150 }
                        //{ field: 'op', title: '操作', width: 230, toolbar: '#barDemo_Detail', fixed: 'right' }
                    ]],
                    page: true
                });


            });  // layui.use('table', function () {


        }


        /// 显示质量控制计划明细项信息
        ///daiwanshan
        function ShowQCControlPlanDetailAdd(sQPNo, sVer) {

            var Data = '';
            var Params = { No: sQPNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: sVer, B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#table_QCControlPlanDetailAdd',
                    id: 'QCControlPlanDetailAddID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=19-1&Data=' + Data,
                    height: '370',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers' },
                        { field: 'ItemNo', title: '内容编号', width: 100 },
                        { field: 'TechNode', title: '工艺节点', width: 100 },
                        { field: 'TechStep', title: '工艺步骤', width: 100 },
                        { field: 'TSource', title: '来源', width: 100 },
                        { field: 'PFMEA', title: 'PFMEA关联', width: 100 },
                        { field: 'Device', title: '工装及夹具', width: 100 },
                        { field: 'IDNO', title: 'ID/NO', width: 100 },
                        { field: 'Product', title: '产品', width: 100 },

                        { field: 'Process', title: '过程', width: 100 },
                        { field: 'ProcessTX', title: '过程特性/公差', width: 100 },
                        { field: 'Measurement', title: '测量技术', width: 100 },
                        { field: 'SampleNum', title: '样品数量', width: 100 },
                        { field: 'SampleFreq', title: '样品频度', width: 100 },
                        { field: 'ControlWay', title: '控制方法', width: 100 },
                        { field: 'ResponsePlan', title: '反应计划', width: 100 },
                        { field: 'ThisValue', title: '下限', width: 100 },


                        { field: 'IncludeOne', title: '是否包含下限', width: 100 },
                        { field: 'ToValue', title: '上限', width: 100 },
                        { field: 'IncludeTwo', title: '是否包含上限', width: 100 },
                        { field: 'RangeKind', title: '类别', width: 100 },
                        { field: 'DataType', title: '数据类型', width: 100 },
                        { field: 'Status', title: '状态', width: 100 },
                        { field: 'Remark', title: '备注', width: 100 },

                        { field: 'SNo', title: '序号', width: 40 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate', title: '录入时间', width: 150 }
                        //{ field: 'op', title: '操作', width: 230, toolbar: '#barDemo_Detail', fixed: 'right' }
                    ]],
                    page: true
                });


            });  // layui.use('table', function () {


        }

        function btn_QCControlExeDetailDel() {

            var OPFlag = $("#txtDelOP").val()

            if (OPFlag == "35") {
                //向服务端发送禁用指令
                var sQPNo = $("#txtDelENo").val();
                //var sVer = data.Ver;
                var sFlag = "35";

                var Data = '';
                var Params = { No: sQPNo, Name: "", A: "", B: "", C: "", D: "", E: "", F: "", I: "", J: "", K: "", L: "", Kind: "", Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);
                $.ajax({
                    type: "POST",
                    url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);


                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('删除成功！');

                            $('#QCControlExeBut_open').click();  // 重新查询
                            closeDelDialog()

                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                            layer.msg('执行中/已完成，不可以删除！！');

                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST1') {
                            layer.msg('工序已用于BOM设计，不能删除！');

                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST2') {
                            layer.msg('工序已用于产品对应标贴，不能删除！');
                        }
                        else {
                            layer.msg('删除失败，请重试！');
                            $('#QCControlExeBut_open').click();  // 重新查询
                        }
                    },
                    error: function (data) {
                        layer.msg('删除失败2，请重试！');
                        $('#QCControlExeBut_open').click();  // 重新查询
                    }
                });
            } else if (OPFlag == "35-1") {
                var sNo = $("#txtItemENo").val();
                var sItemNo = $("#txtItemNo").val();
                var sSerialNo = $("#txtDelSerialNo").val();
                var sResult = "OK"


                var sFlag = "35-1";

                /*var Data = '';*/
                var Params = { No: sNo, Name: "", Item: sItemNo, MNo: "", MName: "", A: sSerialNo, B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);

                $.ajax({
                    type: "POST",
                    url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('删除质量控制执行结果操作成功！');
                            ShowQCControlExeDetailResult(sNo, sItemNo);//重新显示质量控制执行内容清单
                            //$('#Fin_SearchOpen').click();
                            //obj.del(); //删除对应行（tr）的结构，并更新缓存
                            closeDelDialog()
                        }
                        else {
                            layer.msg('删除质量控制执行结果操作失败，请重试！');
                            // $('#Fin_SearchOpen').click();
                        }
                    },
                    error: function (data) {
                        layer.msg('删除质量控制执行结果操作失败2，请重试！');
                        // $('#Fin_SearchOpen').click();
                    }
                });
            } else if (OPFlag == "34") {
                var sNo = $("#txtENo").val();
                var sItemNo = $("#txtDelItemNo").val();
                var sResult = "OK"

                var sFlag = "34";

                /*var Data = '';*/
                var Params = { No: sNo, Name: "", Item: sItemNo, MNo: "", MName: "", A: sResult, B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);

                $.ajax({
                    type: "POST",
                    url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistQCInfo&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('质量控制执行确认操作成功！');
                            GetPrdAssistStatusForNo(sNo);
                            ShowQCControlExeDetail(sNo, sItemNo);//重新显示质量控制执行内容清单
                            $('#QCControlExeBut_open').click();
                            //obj.del(); //删除对应行（tr）的结构，并更新缓存
                            closeDelDialog()
                        }
                        else {
                            layer.msg('质量控制执行确认操作失败，请重试！');
                            // $('#Fin_SearchOpen').click();
                        }
                    },
                    error: function (data) {
                        layer.msg('质量控制执行确认操作失败2，请重试！');
                        // $('#Fin_SearchOpen').click();
                    }
                });
            }
        }

        function closeDelDialog() {
            $('#ShowDel').css("display", "none")
            $('#ShowDel-fade').css("display", "none")
        }
    </script>


    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

        })



    </script>

    <style type="text/css">

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        /* .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: #bbbcc7;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=60);
        }*/


        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }


        #ShowOne .XC-Form-block-Item span, #ShowFive .XC-Form-block-Item span, #ShowSix .XC-Form-block-Item span {
            width: 85px;
        }
    </style>

</head>
<body>
    <div class="div_find">
        <p>
            <label class="find_labela">质量控制执行记录编号</label> <input type="text" id="txtSENo" class="find_input" />
            <label class="find_labela">工单号</label><input type="text" id="txtSCROrderNo" class="find_input" />
            <!--<label class="find_labela">描述</label><input type="text" id="txtSCPTxt" class="find_input" />-->
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="QCControlExeBut_open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="openDialog(1)">添加</button>
        </p>
    </div>

    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="QCControlExelist" lay-filter="QCControlExelist"></table>

        <script type="text/html" id="barDemo">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detail">详情</button>
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>
        </script>

    </div>

    <!--质量控制执行记录详情弹窗层-->
    <div class="XC-modal XC-modal-xl" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1">质量控制执行记录详情</span>
            <span class="head-close" onclick="closeDialog(2)">X</span>
        </div>
        <div class="XC-modal-body" style="padding:10px">
            <div class="XC-modal-xl-center">
                <div style="border-bottom: solid 1px #ccc;padding-bottom:12px">
                    <span style="font-weight: bold; ">质量控制执行记录基本信息</span>
                </div>

                <div style="display:flex;margin-top:10px">
                    <div style="width:100%">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">编号</span>
                                <input type="text" class="XC-Input-block" id="txtENo" name="txtENo" readonly=readonly placeholder="系统自动产生" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">型号</span>
                                <input type="text" class="XC-Input-block" id="txtQCRMaterSpec" name="txtQCRMaterSpec" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">物料名称</span>
                                <input type="text" class="XC-Input-block" id="txtQCRMaterName" name="txtQCRMaterName" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">版本</span>
                                <input type="text" class="XC-Input-block" id="txtQCRVer" name="txtQCRVer" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">创建人</span>
                                <input type="text" class="XC-Input-block" id="txtQCRInMan" name="txtQCRInMan" readonly=readonly />
                            </div>
                        </form>
                    </div>
                    <div style="width:100%">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">工单</span>
                                <input type="text" class="XC-Input-block" id="txtQCROrderNo" name="txtQCROrderNo" readonly=readonly />
                                <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="OrderBut_open" onclick="openDialog(2)" style="margin-left:5px">选择</button>
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">物料编码</span>
                                <input type="text" class="XC-Input-block" id="txtQCRMaterNo" name="txtQCRMaterNo" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">质量控制计划</span>
                                <input type="text" class="XC-Input-block" id="txtQCRQPNo" name="txtQCRQPNo" readonly=readonly />
                                <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="btn_CRCP_open" onclick="openDialog(3)" style="margin-left:5px">选择</button>
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">状态</span>
                                <input type="text" class="XC-Input-block" id="txtQCRStatus" name="txtQCRStatus" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">创建日期</span>
                                <input type="text" class="XC-Input-block" id="txtQCRInDate" name="txtQCRInDate" readonly=readonly />
                            </div>
                        </form>
                    </div>
                </div>

                <div style="border-bottom: solid 1px #ccc;padding-bottom:12px">
                    <span style="font-weight: bold; ">质量控制执行记录内容列表</span>
                </div>
                <div class="wangid_conbox" id="div_table_QCControlExeDetailList" style="display:none; ">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="table_QCControlExeDetailList" lay-filter="table_QCControlExeDetailList"></table>

                    <script type="text/html" id="bar_OrderQCControlPlanRocDetail">
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="QCResult" style="width:70px;">检验记录</button>
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="QCEnd" style="width:70px;">完成确认</button>
                    </script>
                </div>

                <div class="wangid_conbox" id="div_table_QCControlPlanDetailAdd" style="display:none; ">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="table_QCControlPlanDetailAdd" lay-filter="table_QCControlPlanDetailAdd"></table>
                    <!--<script type="text/html" id="bar_OrderQCControlPlanRocDetail">
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="QCResult" style="width:70px;">检验记录</button>
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="QCEnd" style="width:70px;">完成确认</button>
                    </script>-->
                </div>
            </div>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="btn_QCControlExeDetailSave">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="btn_QCControlExeDetailClose" onclick="closeDialog(2)">取消</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>


    <!--选择质量控制计划弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowThree">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title3">质量控制计划搜索</span>
            <span class="head-close" onclick="closeDialog(1)">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <div>
                    <div style=" font-weight: bold; padding-bottom: 10px;margin-bottom:10px;border-bottom: solid 1px #ccc;">
                        查询
                    </div>
                    <div class="div_find">
                        <p>
                            <label class="find_labela">质量控制计划编号</label> <input type="text" id="txtSQPNo" class="find_input" />
                            <label class="find_labela">版本号</label><input type="text" id="txtSVer" class="find_input" />
                            <!--<label class="find_labela">描述</label><input type="text" id="txtSCPTxt" class="find_input" />-->
                            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="QCControlPlanBut_open">搜索</button>
                        </p>
                    </div>
                </div>
                <div style=" font-weight: bold;padding-bottom:10px;margin-top:10px;">
                    质量控制计划列表
                </div>

                <div class="wangid_conbox">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="table_QCControlPlanSelectList" lay-filter="table_QCControlPlanSelectList"></table>

                    <script type="text/html" id="barDemo_QPDetail">
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="QPDetail">详情</button>
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="QPSelect">选择</button>
                    </script>
                </div>
            </div>
        </div>
        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="btn_SelectQCControlPlanInfoClose" onclick='closeDialog(1)'>关闭</button>
            </div>
        </div>
    </div>

    <!--选择订单信息弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowTow">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title2">工单搜索</span>
            <span class="head-close" onclick="closeDialog(1)">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <div>
                    <div style=" font-weight: bold; padding-bottom: 10px;margin-bottom:10px;border-bottom: solid 1px #ccc;">
                        查询
                    </div>
                    <div class="div_find">
                        <p>
                            <label class="find_labela">工单编号</label> <input type="text" id="txtSOrderNo" class="find_input" />
                            <label class="find_labela">物料编码</label><input type="text" id="txtSMaterNo" class="find_input" />
                            <label class="find_labela">物料描述</label><input type="text" id="txtSMaterSpec" class="find_input" />
                            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="OrderInfBut_open">搜索</button>
                        </p>
                    </div>
                </div>
                <div style=" font-weight: bold;padding-bottom:10px;margin-top:10px;">
                    工单信息列表
                </div>
                <div class="wangid_conbox">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="table__OrderInfoSelectList" lay-filter="table__OrderInfoSelectList"></table>

                    <script type="text/html" id="barDemo_OrderDetail">
                        <!--<button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="OrderDetail">详情</button>-->
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="OrderSelect">选择</button>
                    </script>
                </div>
            </div>
        </div>
        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="btn_SelectOrderInfoClose" onclick='closeDialog(1)'>关闭</button>
            </div>
        </div>
    </div>

    <!--质量控制计划内容清单弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowFour">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title4">质量控制计划详情</span>
            <span class="head-close" onclick="closeDialog(3)">X</span>
        </div>
        <div class="XC-modal-body">
            <div style=" font-weight: bold;padding-bottom:10px;">
                质量控制计划
            </div>
            <div class="XC-modal-xl-center">
                <form class="XC-Form-block" style="margin:0px;">
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">计划编号</span>
                        <input type="text" class="XC-Input-block" id="txtItemCPDNo" name="txtItemCPDNo" readonly="readonly" placeholder="系统自动产生" value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">版本</span>
                        <input type="text" class="XC-Input-block" id="txtItemCPDVer" name="txtItemCPDDVer" readonly=readonly value="" />
                    </div>
                </form>
                <div class="wangid_conbox">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="table_QCControlPlanDetailList" lay-filter="table_QCControlPlanDetailList"></table>
                </div>
            </div>
        </div>
    </div>

    <!--显示质量执行结果弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowFive">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title5">质量控制执行结果</span>
            <span class="head-close" onclick="closeDialog(3)">X</span>
        </div>
        <div class="XC-modal-body" style="padding:10px">
            <div class="XC-modal-xl-center">

                <!--<div style="border-bottom: solid 1px #ccc;padding-bottom:12px">
                    <span style="font-weight: bold; ">查询</span>
                </div>-->
                <!--ThisValue，IncludeOne，ToValue，IncludeTwo，RangeKind，DataType-->
                <div style="display:flex;margin-top:10px">
                    <div style="width:100%">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">控制执行编号</span>
                                <input type="text" class="XC-Input-block" id="txtItemENo" name="txtItemENo" readonly=readonly placeholder="系统自动产生" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">下限</span>
                                <input type="text" class="XC-Input-block" id="txtItemThisValue" name="txtItemThisValue" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">上限</span>
                                <input type="text" class="XC-Input-block" id="txtItemToValue" name="txtItemToValue" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">范围类型</span>
                                <input type="text" class="XC-Input-block" id="txtItemRangeKind" name="txtItemRangeKind" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">状态</span>
                                <input type="text" class="XC-Input-block" id="txtItemStatus" name="txtItemStatus" readonly=readonly />
                            </div>
                        </form>
                    </div>
                    <div style="width:100%">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">执行内容编号</span>
                                <input type="text" class="XC-Input-block" id="txtItemNo" name="txtItemNo" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">是否包含</span>
                                <input type="checkbox" id="txtItemIncludeOne" name="txtItemIncludeOne" readonly=readonly style="margin:0px" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">是否包含</span>
                                <input type="checkbox" id="txtItemIncludeTwo" name="txtItemIncludeTwo" readonly=readonly style="margin:0px" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">数据类型</span>
                                <input type="text" class="XC-Input-block" id="txtItemDataType" name="txtItemDataType" readonly=readonly />
                            </div>
                            <!--<div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block"></span>
                                <input type="text" class="XC-Input-block" id="txtItemDataType" name="txtItemDataType" readonly=readonly />
                            </div>-->
                        </form>
                    </div>
                </div>

                <!--<div style=" font-weight: bold; padding: 5px; height: 35px; line-height: 25px; margin-top: 5px; border: 0px; border-bottom: solid 1px #ccc;">
                质量控制执行结果
                <div align="right">-->
                <!--<i class="add2_i"></i>--><!--<a href="JavaScript:void(0)" onclick="openDialog(4)" style="color: Blue">添加</a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-->
                <!--<input type="button" value="添加" class="find_but" id="btn_SelectQCControlPlanInfoADD" onclick="openDialog(4)" style="height:30px;" />-->
                <!-- <i class="down_i" ></i><a href="JavaScript:void(0)" onclick="openDialog(1)" class="add_a">导出</a> -->
                <!--</div>
                </div>-->

                <div style="display:flex;justify-content:space-between;border-bottom: solid 1px #ccc;padding-bottom:3px">
                    <span style="font-weight: bold; ">质量控制执行结果</span>
                    <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="btn_SelectQCControlPlanInfoADD" onclick="openDialog(4)">添加</button>
                </div>

                <div class="wangid_conbox">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="table_QCControlExeDetailResult" lay-filter="table_QCControlExeDetailResult" List"></table>

                    <script type="text/html" id="bar_QCControlExeDetailResult">
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="QCRstEdit">修改</button>
                        <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="QCRstDel">删除</button>
                    </script>
                </div>
            </div>
        </div>


        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="btn_SelectQCControlPlanInfoClose" onclick="closeDialog(3)">取消</button>
            </div>
        </div>
    </div>


    <!--显示质量执行结果编辑弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowSix">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title6">质量控制执行结果维护</span>
            <span class="head-close" onclick="closeDialog(1)">X</span>
        </div>

        <div class="XC-modal-body" style="padding:10px">
            <div class="XC-modal-xl-center">
                <form class="XC-Form-block">
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">控制执行编号</span>
                        <input type="text" class="XC-Input-block" id="txtItemEditENo" name="txtItemEditENo" readonly=readonly placeholder="系统自动产生" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">执行内容编号</span>
                        <input type="text" class="XC-Input-block" id="txtEditItemNo" name="txtEditItemNo" readonly=readonly />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">下限</span>
                        <input type="text" class="XC-Input-block" id="txtItemEditThisValue" name="txtItemEditThisValue" readonly=readonly />
                        <span class="XC-Span-Input-block">是否包含</span>
                        <input type="checkbox" id="txtItemEditIncludeOne" name="txtItemEditIncludeOne" readonly=readonly style="margin:0px;" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">上限</span>
                        <input type="text" class="XC-Input-block" id="txtItemEditToValue" name="txtItemEditToValue" readonly=readonly />
                        <span class="XC-Span-Input-block">是否包含</span>
                        <input type="checkbox" id="txtItemEditIncludeTwo" name="txtItemEditIncludeTwo" readonly=readonly style="margin:0px;" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">范围类型</span>
                        <input type="text" class="XC-Input-block" id="txtItemEditRangeKind" name="txtItemEditRangeKind" readonly=readonly />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">数据类型</span>
                        <input type="text" class="XC-Input-block" id="txtItemEditDataType" name="txtItemEditDataType" readonly=readonly />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">抽检次数</span>
                        <input type="text" class="XC-Input-block" id="txtItemEditTimes" name="txtItemEditTimes" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">抽样对象<span class="XC-Font-Red">*</span></span>
                        <input type="text" class="XC-Input-block" id="txtItemEditSerialNo" name="txtItemEditSerialNo" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">检验值<span class="XC-Font-Red">*</span></span>
                        <input type="text" class="XC-Input-block" id="txtItemEditEXEValue" name="txtItemEditEXEValue" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Select-block">检验结果<span class="XC-Font-Red">*</span></span>
                        <select class="XC-Select-block" id="txtItemEditEXEResult">
                            <option></option>
                            <option>正常</option>
                            <option>异常</option>

                        </select>
                        <!--<input type="text" class="XC-Input-block" id="txtItemEditEXEResult" name="txtItemEditEXEResult"  />-->
                    </div>
                </form>
            </div>
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="btn_QCResultEditSave">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="btn_QCResultEditsaveClose" onclick="closeDialog(1)">关闭</button>
            </div>
        </div>
    </div>



    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel" style="z-index:1003">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDelDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelENo" name="txtDelENo" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelSerialNo" name="txtDelSerialNo" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelItemNo" name="txtDelItemNo" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDelOP" name="txtDelOP" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" onclick="btn_QCControlExeDetailDel()">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDelDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay" style="z-index:1002">
    </div>



    <!--消息提示-->
    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>
</html>