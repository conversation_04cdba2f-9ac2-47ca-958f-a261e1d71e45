﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>套餐定价管理</title>
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <script src="../js/ajaxfileupload.js" type="text/javascript"></script>
    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all">
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/PrdAssist.js" type="text/javascript"></script>
    <script src="../js/layer/layer.js" type="text/javascript"></script>

    <link href="../css/XC.css" rel="stylesheet" />

    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        //$('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=28-1' });
                    }
                }
            })
        });



    </script>


    <script type="text/javascript">
        // 添加价格梯度行
        function addPriceRow(btn) {
            var tbody = document.querySelector('#priceTable tbody');
            var newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td><input type="number" class="layui-input price-input" name="start" min="0" /></td>
                <td><input type="number" class="layui-input price-input" name="end" min="0" /></td>
                <td><input type="number" class="layui-input price-input" name="price" min="0" step="0.01" /></td>
                <td>
                    <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="addPriceRow(this)">添加</button>
                    <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" onclick="deletePriceRow(this)">删除</button>
                </td>
            `;
            tbody.appendChild(newRow);
            updateStartValues();
        }

        // 删除价格梯度行
        function deletePriceRow(btn) {
            var tbody = document.querySelector('#priceTable tbody');
            if (tbody.children.length > 1) {
                var row = btn.closest('tr');
                row.remove();
                updateStartValues();
            } else {
                layer.msg('至少保留一行价格梯度');
            }
        }

        // 更新所有行的起始值
        function updateStartValues() {
            var rows = document.querySelectorAll('#priceTable tbody tr');
            rows.forEach(function(row, index) {
                var startInput = row.querySelector('[name="start"]');
                var endInput = row.querySelector('[name="end"]');
                
                if (index === 0) {
                    startInput.value = "0";
                } else {
                    var prevEndInput = rows[index - 1].querySelector('[name="end"]');
                    startInput.value = prevEndInput.value ? (parseInt(prevEndInput.value) + 1) : '';
                }

                // 添加结束数量变化事件
                if (!endInput.hasEventListener) {
                    endInput.addEventListener('change', function() {
                        updateStartValues();
                    });
                    endInput.hasEventListener = true;
                }
            });
        }

        // 获取价格梯度数据
        function getPriceData() {
            var rows = document.querySelectorAll('#priceTable tbody tr');
            var data = [];
            rows.forEach(function(row, index) {
                var startVal = row.querySelector('[name="start"]').value;
                var endVal = row.querySelector('[name="end"]').value;
                var priceVal = row.querySelector('[name="price"]').value;

                if (startVal !== '' && priceVal !== '') {
                    data.push({
                        start: parseInt(startVal),
                        end: endVal ? parseInt(endVal) : null,
                        price: parseFloat(priceVal)
                    });
                }
            });
            return data;
        }

        // 设置价格梯度数据
        function setPriceData(jsonStr) {
            if (!jsonStr) {
                // 如果没有数据，至少显示一行空行
                var tbody = document.querySelector('#priceTable tbody');
                tbody.innerHTML = `
                    <tr>
                        <td><input type="number" class="layui-input price-input" name="start" min="0" value="0" /></td>
                        <td><input type="number" class="layui-input price-input" name="end" min="0" /></td>
                        <td><input type="number" class="layui-input price-input" name="price" min="0" step="0.01" /></td>
                        <td>
                            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="addPriceRow(this)">添加</button>
                            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" onclick="deletePriceRow(this)">删除</button>
                        </td>
                    </tr>
                `;
                return;
            }
            
            try {
                var data = JSON.parse(jsonStr);
                var tbody = document.querySelector('#priceTable tbody');
                tbody.innerHTML = ''; // 清空现有行
                
                data.forEach(function(item, index) {
                    var row = document.createElement('tr');
                    row.innerHTML = `
                        <td><input type="number" class="layui-input price-input" name="start" min="0" value="${item.start}" /></td>
                        <td><input type="number" class="layui-input price-input" name="end" min="0" value="${item.end || ''}" /></td>
                        <td><input type="number" class="layui-input price-input" name="price" min="0" step="0.01" value="${item.price}" /></td>
                        <td>
                            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="addPriceRow(this)">添加</button>
                            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" onclick="deletePriceRow(this)">删除</button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
                updateStartValues();
            } catch (e) {
                console.error('Invalid price data:', e);
                setPriceData(); // 显示一行空行
            }
        }

        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#Defectslist',
                id: 'DefectslistID',
                url: '../Service/ProductPricing.ashx?OP=GetSetMenuInfo',
                height: 'full-80',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'MenuNo', title: '套餐编号', width: 120, sort: true },
                    { field: 'MenuName', title: '套餐名称', width: 120, sort: true },
                    { field: 'MenuLevel', title: '套餐等级', width: 100, sort: true },
                    { field: 'BasePrice', title: '基础价格(月费)', width: 120 },
                    { field: 'BUP', title: '包含活跃用户数', width: 120 },
                    { field: 'TVA', title: '包含工单数', width: 120 },
                    { field: 'AUserPrice', title: '额外用户单价', width: 120 },
                    { field: 'AWOPrice', title: '工单超量价格', width: 200, templet: function(d) {
                        try {
                            var priceData = JSON.parse(d.AWOPrice);
                            var formattedPrice = priceData.map(function(item, index) {
                                if (index === priceData.length - 1) {
                                    return item.start + '以上:' + item.price;
                                } else {
                                    return item.start + '-' + item.end + ':' + item.price;
                                }
                            }).join('，');
                            return formattedPrice;
                        } catch (e) {
                            return d.AWOPrice || '';
                        }
                    }},
                    { field: 'DFunction', title: '核心功能差异', width: 200 },
                    { field: 'SLA', title: '标准在线支持', width: 120, templet: function(d) { return d.SLA ? '是' : '否'; } },
                    { field: 'DepthTrain', title: '深度培训', width: 120, templet: function(d) { return d.DepthTrain ? '是' : '否'; } },
                    { field: 'IMServices', title: '实施服务', width: 120, templet: function(d) { return d.IMServices ? '是' : '否'; } },
                    { field: 'CustomDev', title: '高级定制开发', width: 120, templet: function(d) { return d.CustomDev ? '是' : '否'; } },
                    { field: 'InterfaceDev', title: '特定接口开发', width: 120, templet: function(d) { return d.InterfaceDev ? '是' : '否'; } },
                    { field: 'OnsiteSV', title: '专人驻场服务', width: 120, templet: function(d) { return d.OnsiteSV ? '是' : '否'; } },
                    { field: 'Status', title: '状态', width: 80, templet: function(d) { 
                        var color = d.Status === '已禁用' ? 'red' : 'green';
                        return '<span style="color: ' + color + ';">' + d.Status + '</span>';
                    }},
                    { field: 'Remark', title: '备注', width: 200 },
                    { field: 'InMan', title: '登录账号', width: 100 },
                    { field: 'InDate', title: '创建时间', width: 150 },
                    { field: 'op', title: '操作', width: 220, toolbar: '#barDemo', fixed: 'right' }
                ]],
                page: true,
                //  done: function(res, curr, count) {
                //     var that = this.elem.next();
                //     res.data.forEach(function(item, index) {
                //         if (item.Flag == "0") { // 说明评估日期已到期
                //             $(".layui-table-body tbody tr[data-index='" + index + "']").css({ 'backgroundColor': "Pink" });
                //             $(".layui-table-body tbody tr[data-index='" + index + "']").find('td[data-field="LastContactTime"]').css({ 'color': "red" });
                //
                //         }
                //     });
                // }


            });




            //监听是否选中操作
            table.on('checkbox(Defectslist)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);
            });

            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(Defectslist)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');
            });

            //监听单元格编辑
            table.on('edit(Defectslist)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });

            //监听行工具事件
            table.on('tool(Defectslist)', function(obj) {
                var data = obj.data;
                var layEvent = obj.event;

                if (layEvent === 'jy') {  // 禁用
                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置禁用的标题
                    $("#hint-title").html("确定要禁用该套餐吗？套餐编号：")

                    //设置禁用的对象
                    $("#hint-value").html(data.MenuNo)

                    $("#txtMenuNo").val(data.MenuNo)
                    $("#txtOPFlag").val("jy")

                } else if (layEvent === 'qy') {  // 启用
                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Green")
                    $("#hint-value").addClass("XC-Font-Green")

                    //设置启用的标题
                    $("#hint-title").html("确定要启用该套餐吗？套餐编号：")

                    //设置启用的对象
                    $("#hint-value").html(data.MenuNo)

                    $("#txtMenuNo").val(data.MenuNo)
                    $("#txtOPFlag").val("qy")

                } else if (layEvent === 'edit') {
                    $('#head-title1').html("修改套餐信息");
                    $('#txtAEFlag').val("2");

                    $("#txtMenuNo").val(data.MenuNo);
                    $("#txtMenuName").val(data.MenuName);
                    $("#txtMenuLevel").val(data.MenuLevel);
                    $("#txtBasePrice").val(data.BasePrice);
                    $("#txtBUP").val(data.BUP);
                    $("#txtTVA").val(data.TVA);
                    $("#txtAUserPrice").val(data.AUserPrice);
                    setPriceData(data.AWOPrice);
                    $("#txtDFunction").val(data.DFunction);
                    $("#txtSLA").prop("checked", data.SLA === true);
                    $("#txtDepthTrain").prop("checked", data.DepthTrain === true);
                    $("#txtIMServices").prop("checked", data.IMServices === true);
                    $("#txtCustomDev").prop("checked", data.CustomDev === true);
                    $("#txtInterfaceDev").prop("checked", data.InterfaceDev === true);
                    $("#txtOnsiteSV").prop("checked", data.OnsiteSV === true);
                    $("#txtRemark").val(data.Remark);

                    $("#txtMenuNo").attr({ "disabled": "disabled" });

                    $("#div_warning").html("");
                    $("#div_warning").hide();

                    $("#ShowOne").css("display", "block")
                    $("#ShowOne-fade").css("display", "block")
                } else if (layEvent === 'del') {  // 删除
                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("确定要删除该套餐吗？套餐编号：")

                    //设置删除的对象
                    $("#hint-value").html(data.MenuNo)

                    $("#txtMenuNo").val(data.MenuNo)
                    $("#txtOPFlag").val("del")
                }
            });

            //禁用\启用\删除
            $("#MenuOPBtn").click(function () {
                var Flag = $("#txtOPFlag").val()
                var sMenuNo = $("#txtMenuNo").val()

                var Data = '';
                var Params = {};
                var url = '';

                if (Flag === "del") {
                    Params = {
                        MenuNo: sMenuNo
                    };
                    url = "../Service/ProductPricing.ashx?OP=DeleteSetMenuInfo";
                } else {
                    var sStatus = Flag == "jy" ? "已禁用" : "未禁用";
                    Params = {
                        MenuNo: sMenuNo,
                        Status: sStatus
                    };
                    url = "../Service/ProductPricing.ashx?OP=UpdateSetMenuStatus";
                }

                Data = JSON.stringify(Params);

                $.ajax({
                    type: "POST",
                    url: url,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson.Msg == 'Success') {
                            if (Flag === "del") {
                                layer.msg('删除成功！');
                            } else {
                                layer.msg(Flag == "jy" ? '禁用成功！' : '启用成功！');
                            }
                            $('#MenuBut_open').click();  // 重新查询
                            closeDialog();
                        }
                        else if (parsedJson.Msg == 'LoginError') {
                            layer.msg('登录已过期，请重新登录！');
                            setTimeout(function() {
                                window.location.href = "../Login.htm";
                            }, 1500);
                        }
                        else if (parsedJson.Msg == 'CannotDelete') {
                            layer.msg('该套餐已被使用，不能删除！');
                        }
                        else {
                            var operation = Flag === "del" ? "删除" : (Flag == "jy" ? "禁用" : "启用");
                            layer.msg(operation + '失败：' + parsedJson.Msg);
                        }
                    },
                    error: function (data) {
                        var operation = Flag === "del" ? "删除" : (Flag == "jy" ? "禁用" : "启用");
                        layer.msg(operation + '失败，请重试！');
                    }
                });
            });

            //  查询 -- 
            $('#MenuBut_open').click(function () {
                var sMenuNo = $("#txtSMenuNo").val();
                var sMenuName = $("#txtSMenuName").val();
                var sStatus = $("#txtSStatus").val();

                var Data = '';
                var Params = { MenuNo: sMenuNo, MenuName: sMenuName, Status: sStatus };
                var Data = JSON.stringify(Params);

                table.reload('DefectslistID', {
                    method: 'post',
                    url: '../Service/ProductPricing.ashx?OP=GetSetMenuInfo',
                    where: {
                        Data: Data
                    }, 
                    page: {
                        curr: 1
                    }
                });
            });

            // 修改保存按钮事件
            $("#MenuSaveBtn").click(function () {
                var sMenuNo = $("#txtMenuNo").val();
                var sMenuName = $("#txtMenuName").val();
                var sMenuLevel = $("#txtMenuLevel").val();
                var sBasePrice = $("#txtBasePrice").val();
                var sBUP = $("#txtBUP").val();
                var sTVA = $("#txtTVA").val();
                var sAUserPrice = $("#txtAUserPrice").val();
                var sDFunction = $("#txtDFunction").val();
                var sSLA = $("#txtSLA").prop("checked");
                var sDepthTrain = $("#txtDepthTrain").prop("checked");
                var sIMServices = $("#txtIMServices").prop("checked");
                var sCustomDev = $("#txtCustomDev").prop("checked");
                var sInterfaceDev = $("#txtInterfaceDev").prop("checked");
                var sOnsiteSV = $("#txtOnsiteSV").prop("checked");
                var sRemark = $("#txtRemark").val();
                var sAction = $("#txtAEFlag").val() == "1" ? "ADD" : "EDIT";

                if (sMenuName == "") {
                    layer.msg('请输入套餐名称！');
                    return;
                }
                if (sMenuLevel == "") {
                    layer.msg('请输入套餐等级！');
                    return;
                }
                if (sBasePrice == "") {
                    layer.msg('请输入基础价格！');
                    return;
                }
                if (sBUP == "") {
                    layer.msg('请输入包含活跃用户数！');
                    return;
                }
                if (sTVA == "") {
                    layer.msg('请输入包含工单数！');
                    return;
                }
                if (sAUserPrice == "") {
                    layer.msg('请输入额外用户单价！');
                    return;
                }

                // 获取并验证价格梯度数据
                var priceData = getPriceData();
                if (!priceData.length) {
                    layer.msg('请至少添加一个工单超量价格梯度！');
                    return;
                }

                // 验证梯度数据的合法性
                for (var i = 0; i < priceData.length; i++) {
                    if (!priceData[i].price || priceData[i].price <= 0) {
                        layer.msg('请输入有效的价格！');
                        return;
                    }
                    if (i < priceData.length - 1 && !priceData[i].end) {
                        layer.msg('除最后一个梯度外，其他梯度必须设置结束数量！');
                        return;
                    }
                    if (i > 0 && priceData[i].start <= priceData[i-1].start) {
                        layer.msg('每个梯度的起始数量必须大于前一个梯度！');
                        return;
                    }
                }

                var Params = {
                    MenuNo: sMenuNo,
                    MenuName: sMenuName,
                    MenuLevel: parseInt(sMenuLevel),
                    BasePrice: parseFloat(sBasePrice),
                    BUP: parseInt(sBUP),
                    TVA: parseInt(sTVA),
                    AUserPrice: parseFloat(sAUserPrice),
                    AWOPrice: JSON.stringify(priceData),
                    DFunction: sDFunction,
                    SLA: sSLA,
                    DepthTrain: sDepthTrain,
                    IMServices: sIMServices,
                    CustomDev: sCustomDev,
                    InterfaceDev: sInterfaceDev,
                    OnsiteSV: sOnsiteSV,
                    Status: "未禁用",
                    Remark: sRemark
                };
                var Data = JSON.stringify(Params);
                $.ajax({
                    type: "POST",
                    url: "../Service/ProductPricing.ashx?OP=" + (sAction == "ADD" ? "AddSetMenuInfo" : "UpdateSetMenuInfo"),
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson.Msg == 'Success') {
                            layer.msg('保存成功！');
                            $('#MenuBut_open').click();  // 重新查询
                            closeDialog();
                        }
                        else if (parsedJson.Msg == 'MenuNameExists') {
                            layer.msg('套餐名称已存在！');
                        }
                        else if (parsedJson.Msg == 'LoginError') {
                            layer.msg('登录已过期，请重新登录！');
                            setTimeout(function() {
                                window.location.href = "../Login.htm";
                            }, 1500);
                        }
                        else {
                            layer.msg('保存失败：' + parsedJson.Msg);
                        }
                    },
                    error: function (data) {
                        layer.msg('保存失败，请重试！');
                    }
                });
            });




        });




        function openDialog(n) {  // 新增
            $('#head-title1').html("新增套餐信息");
            $('#txtAEFlag').val("1");

            $("#txtMenuNo").val("");
            $("#txtMenuName").val("");
            $("#txtMenuLevel").val("");
            $("#txtBasePrice").val("");
            $("#txtBUP").val("");
            $("#txtTVA").val("");
            $("#txtAUserPrice").val("");
            $("#txtAWOPrice").val(""); // 新增
            $("#txtDFunction").val("");
            $("#txtSLA").prop("checked", false);
            $("#txtDepthTrain").prop("checked", false);
            $("#txtIMServices").prop("checked", false);
            $("#txtCustomDev").prop("checked", false);
            $("#txtInterfaceDev").prop("checked", false);
            $("#txtOnsiteSV").prop("checked", false);
            $("#txtRemark").val("");

            $("#txtMenuNo").removeAttr("disabled");

            $("#div_warning").html("");
            $("#div_warning").hide();

            $("#ShowOne").css("display", "block")
            $("#ShowOne-fade").css("display", "block")
        }

        function closeDialog() {
            $("#ShowOne").css("display", "none")
            $("#ShowOne-fade").css("display", "none")
            $("#ShowDel").css("display", "none")
            $("#ShowDel-fade").css("display", "none")
        }





    </script>


    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

        })



    </script>

    <style type="text/css">

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        /* .black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: #bbbcc7;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=60);
        }*/

        .wangid_conbox td, .wangid_conbox th, #ShowOne td {
            font-size: 12px
        }

        #ShowOne tr {
            height: 40px;
        }

        #ShowOne .XC-Input-block {
            width: 100%;
            margin-left: 5px
        }

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
            padding: 0 8px;
        }

        select.find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
            padding: 0 8px;
            background-color: white;
        }

        .LabelDelBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #da542e;
        }

        .LabelAddBtn {
            color: White;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            border-radius: 5px;
            padding: 3px 5px;
            background: #579fd8;
        }
        .price-table-container {
            max-height: 300px;
            overflow-y: auto;
        }
        .price-input {
            width: 100px !important;
            display: inline-block !important;
        }
        .price-table-container {
            margin: 10px 0;
            border: 1px solid #e6e6e6;
            padding: 10px;
            background: #fff;
        }
        .price-input {
            width: 90% !important;
            display: inline-block !important;
            height: 30px !important;
        }
        #priceTable th {
            text-align: center;
            background-color: #f2f2f2;
        }
        #priceTable td {
            text-align: center;
            padding: 5px !important;
        }
        #priceTable .XC-Btn-md {
            margin: 0 3px;
        }
    </style>




</head>
<body>
    <div class="div_find">
        <p>
            <label class="find_labela">套餐编号</label> <input type="text" id="txtSMenuNo" class="find_input" />
            <label class="find_labela">套餐名称</label><input type="text" id="txtSMenuName" class="find_input" />
            <label class="find_labela">状态</label>
            <select id="txtSStatus" class="find_input">
                <option value="">全部</option>
                <option value="未禁用">未禁用</option>
                <option value="已禁用">已禁用</option>
            </select>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="MenuBut_open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="javascript:openDialog(1)">添加</button>
        </p>
    </div>
                
    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="Defectslist" lay-filter="Defectslist"></table>

        <script type="text/html" id="barDemo">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="edit">修改</button>
            {{# if(d.Status === '未禁用'){ }}
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="jy">禁用</button>
            {{# } else { }}
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="qy">启用</button>
            {{# } }}
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>
        </script>
                </div>
                
    <!--弹出层-->
    <div class="XC-modal XC-modal-xl" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title1" id="head-title1">套餐信息</span>
            <span class="head-close" onclick="closeDialog()">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <table cellspacing="20" cellpadding="20" border='0' style="width:99%; ">
                    <tr>
                        <td style="width:120px; text-align:right;">
                            套餐编号<span class="XC-Font-Red">*</span>
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtMenuNo" name="txtMenuNo" readonly=readonly placeholder="系统自动产生" />
                        </td>
                        <td style="width:120px; text-align:right;">
                            套餐名称<span class="XC-Font-Red">*</span>
                        </td>
                        <td>
                            <input type="text" class="XC-Input-block" id="txtMenuName" name="txtMenuName" />
                        </td>
                    </tr>
                    <tr>
                        <td style="width:120px; text-align:right;">
                            套餐等级<span class="XC-Font-Red">*</span>
                        </td>
                        <td>
                            <input type="number" class="XC-Input-block" id="txtMenuLevel" name="txtMenuLevel" />
                        </td>
                        <td style="width:120px; text-align:right;">
                            基础价格(月费)<span class="XC-Font-Red">*</span>
                        </td>
                        <td>
                            <input type="number" class="XC-Input-block" id="txtBasePrice" name="txtBasePrice" />
                        </td>
                    </tr>
                    <tr>
                        <td style="width:120px; text-align:right;">
                            包含活跃用户数<span class="XC-Font-Red">*</span>
                        </td>
                        <td>
                            <input type="number" class="XC-Input-block" id="txtBUP" name="txtBUP" />
                        </td>
                        <td style="width:120px; text-align:right;">
                            包含工单数<span class="XC-Font-Red">*</span>
                        </td>
                        <td>
                            <input type="number" class="XC-Input-block" id="txtTVA" name="txtTVA" />
                        </td>
                    </tr>
                    <tr>
                        <td style="width:120px; text-align:right;">
                            额外用户单价<span class="XC-Font-Red">*</span>
                        </td>
                        <td>
                            <input type="number" class="XC-Input-block" id="txtAUserPrice" name="txtAUserPrice" />
                        </td>
                        <td style="width:120px; text-align:right;">
                            核心功能差异
                        </td>
                        <td>
                            <textarea class="XC-Input-block" id="txtDFunction" name="txtDFunction" style="height:60px; padding: 8px;"></textarea>
                        </td>
                    </tr>
                    <tr>
                        <td style="width:120px; text-align:right; vertical-align:top;">
                            工单超量价格<span class="XC-Font-Red">*</span>
                        </td>
                        <td colspan="3">
                            <div class="price-table-container">
                                <table class="layui-table" id="priceTable">
                                    <thead>
                                        <tr>
                                            <th width="25%">起始数量</th>
                                            <th width="25%">结束数量</th>
                                            <th width="25%">单价(元/工单)</th>
                                            <th width="25%">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><input type="number" class="layui-input price-input" name="start" min="0" /></td>
                                            <td><input type="number" class="layui-input price-input" name="end" min="0" /></td>
                                            <td><input type="number" class="layui-input price-input" name="price" min="0" step="0.01" /></td>
                                            <td>
                                                <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="addPriceRow(this)">添加</button>
                                                <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" onclick="deletePriceRow(this)">删除</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <input type="hidden" id="txtAWOPrice" name="txtAWOPrice" />
                        </td>
                    </tr>
                    <tr>
                        <td style="width:120px; text-align:right;">
                            服务选项
                        </td>
                        <td colspan="3" style="padding-left: 15px;">
                            <div class="XC-Checkbox-group" style="padding-top:10px;">
                                <input type="checkbox" id="txtSLA" name="txtSLA" /> <label for="txtSLA" style="margin-right:15px;">标准在线支持</label>
                                <input type="checkbox" id="txtDepthTrain" name="txtDepthTrain" /> <label for="txtDepthTrain" style="margin-right:15px;">深度培训</label>
                                <input type="checkbox" id="txtIMServices" name="txtIMServices" /> <label for="txtIMServices" style="margin-right:15px;">实施服务</label>
                                <input type="checkbox" id="txtCustomDev" name="txtCustomDev" /> <label for="txtCustomDev" style="margin-right:15px;">高级定制开发</label>
                                <input type="checkbox" id="txtInterfaceDev" name="txtInterfaceDev" /> <label for="txtInterfaceDev" style="margin-right:15px;">特定接口开发</label>
                                <input type="checkbox" id="txtOnsiteSV" name="txtOnsiteSV" /> <label for="txtOnsiteSV" style="margin-right:15px;">专人驻场服务</label>
                                </div>
                        </td>
                    </tr>
                    <tr>
                        <td style="width:120px; text-align:right;">
                            备注
                        </td>
                        <td colspan="3">
                            <textarea class="XC-Input-block" id="txtRemark" name="txtRemark" style="height:60px; padding: 8px;"></textarea>
                        </td>
                    </tr>
                </table>
                </div>
                </div>
                
        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="MenuSaveBtn">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="MenuSaveClose" onclick="closeDialog()">取消</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>

    <!--操作提示框-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
                </div>
                
            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtMenuNo" name="txtMenuNo" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtOPFlag" name="txtOPFlag" />
            </div>
            
            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="MenuOPBtn">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDialog()">取消</button>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowDel-fade" class="black_overlay">
    </div>

    <!--消息提示-->
    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>
</html>