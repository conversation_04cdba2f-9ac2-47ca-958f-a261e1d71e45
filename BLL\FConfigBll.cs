﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using System.Text;

namespace BLL
{
    public static class FConfigBll
    {

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public static DataTable GetList(string strWhere)
        {
            return DAL.FConfigDal.GetList(strWhere);

        }

        /// <summary>
        /// 返回登录人是否有操作模块的权限
        /// </summary>
        /// <param name="InMan"></param>
        /// <param name="Module"></param>
        /// <returns></returns>
        public static string JustOperateRight(string InMan, string Module)
        {
            return DAL.FConfigDal.JustOperateRight(InMan, Module);
        }


        /// <summary>
        /// 根据传入的查询条件，显示各种数据的列表
        /// </summary>
        /// <param name="Con1">条件一</param>
        /// <param name="Con2">条件二</param>
        /// <param name="Con3">条件三</param>
        /// <param name="Con4">条件四</param>
        /// <param name="CompNo">企业编号</param>
        /// <param name="sFlag">标识</param>
        /// <returns></returns>
        public static DataTable GetListByCondition(string Con1, string Con2, string Con3, string Con4, string CompNo, string sFlag)
        {
            return DAL.FConfigDal.GetListByCondition(Con1, Con2, Con3, Con4, CompNo, sFlag);

        }


        /// <summary>
        /// 插入或更新流程对象字段记录
        /// </summary>
        /// <param name="OBF"> 流程对象字段食堂</param>
        /// <param name="sNEFlag">N:新增；E:更新</param>
        /// <returns></returns>
        public static string InsertOrUpdateObjectField(Model.ObjectField OBF, string sNEFlag)
        {
          
            return DAL.FConfigDal.InsertOrUpdateObjectField(OBF, sNEFlag);
        }


        /// <summary>
        /// 获取流程信息
        /// </summary>
        /// <param name="strWhere">查询条件</param>
        /// <param name="sFlag">1：流程定义信息；2：流程节点信息；</param>
        /// <returns></returns>
        public static DataTable GetFlowInfo(string strWhere, string sNo, string InMan, string sFlag)
        {
            return DAL.FConfigDal.GetFlowInfo(strWhere,sNo,InMan, sFlag);

        }


        /// <summary>
        /// 插入或更新业务数据，如采购数据，
        /// </summary>
        /// <param name="OBF"> 流程对象字段食堂</param>
        /// <param name="sNEFlag">N:新增；E:更新</param>
        /// <returns></returns>
        public static string SaveBSFlowDataInfo(Model.BusinessData BSData, string sNEFlag)
        {
            return DAL.FConfigDal.SaveBSFlowDataInfo(BSData, sNEFlag);
        }


        /// <summary>
        /// 显示流程申请的业务数据清单，如采购申请的物料清单
        /// </summary>
        /// <param name="strWhere">查询条件</param>
        /// <param name="sFlag">1：显示业务数据清单；</param>
        /// <returns></returns>
        public static DataTable ShowBusinessDataList(string strWhere,string sComp, string sFlag)
        {
            return DAL.FConfigDal.ShowBusinessDataList(strWhere,sComp, sFlag);

        }


        /// <summary>
        /// 根据传入的查询条件，显示申请的流程
        /// </summary>
        /// <param name="BSName"></param>
        /// <param name="BSNo"></param>
        /// <param name="Status"></param>
        /// <param name="CompNo"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static DataTable GetBSFlowListByCondition(string sCon, string BSName, string BSNo, string sBDate, string sEDate, string CompNo, string sFlag)
        {
            return DAL.FConfigDal.GetBSFlowListByCondition(sCon, BSName, BSNo, sBDate, sEDate, CompNo, sFlag);

        }


        /// <summary>
        /// 删除申请流程填写的业务数据
        /// </summary>
        /// <param name="OBF"> 流程对象字段</param>
        /// <param name="sNEFlag">N:新增；E:更新</param>
        /// <returns></returns>
        public static string DeleteBSFlowData(string sBSNo, string sFlowNo, string sF01, string sF02, string sLogin, string sNEFlag)
        {
            return DAL.FConfigDal.DeleteBSFlowData(sBSNo, sFlowNo, sF01, sF02, sLogin, sNEFlag);
        }


        /// <summary>
        /// 返回单据状态
        /// </summary>
        /// <param name="sCLNo"></param>
        /// <returns></returns>
        public static string GetApplyStatus(string sNo, string sSt, string IsPass, string sLogin, string sFlag)
        {
            return DAL.FConfigDal.GetApplyStatus(sNo, sSt,IsPass,sLogin, sFlag);
        }


        /// <summary>
        /// 更新申请流程填写过程审批状态
        /// </summary>
        /// <param name="OBF"> 流程对象字段</param>
        /// <param name="sNEFlag">N:新增；E:更新</param>
        /// <returns></returns>
        public static string UpdateBSFlowData(string sDelStr,string sPicStr,string sSQLs, string sBSNo, string sFlowNo, string sLogin, string sFullName, string Remark, string sNowAud,string sFlag)
        {
            return DAL.FConfigDal.UpdateBSFlowData(sDelStr, sPicStr, sSQLs, sBSNo, sFlowNo, sLogin, sFullName, Remark, sNowAud,sFlag);
        }


        /// <summary>
        /// 通过申请单号和所在功能模块获取图片路径
        /// </summary>
        /// <param name="sAPLNo"></param>
        /// <param name="sKind"></param>
        /// <returns></returns>
        public static DataTable GetFlowInfoPicPath(string sBSNo, string sFlowNo, string sKind)
        {
            return DAL.FConfigDal.GetFlowInfoPicPath(sBSNo, sFlowNo, sKind);
        }



        /// <summary>
        /// 插入流程节点及每个流程审核人
        /// </summary>
        /// <param name="sSQL"></param>
        /// <param name="sSQLs"></param>
        /// <param name="sLogin"></param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string InsertFlowInfo(string sNo,string sSQL, string sSQLs, string sLogin, string sFlag)
        {
            return DAL.FConfigDal.InsertFlowInfo(sNo,sSQL, sSQLs, sLogin, sFlag);
        }


        /// <summary>
        /// 获取发送微信的信息，
        /// </summary>
        /// <param name="sBSNo"></param>
        /// <param name="sStatus"></param>
        /// <param name="NStatus">下一个节点的状态</param>
        /// <param name="sTime">第几次审批</param>
        /// <param name="sFlag"></param>
        /// <returns></returns>
        public static string GetSendWeiXinMan(string sBSNo, string sStatus, string NStatus, string sTime, string sFlag)
        {
            return DAL.FConfigDal.GetSendWeiXinMan(sBSNo, sStatus, NStatus, sTime, sFlag);
        }










    }
}
