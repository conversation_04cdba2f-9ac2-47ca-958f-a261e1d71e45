﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>维修</title>
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <script src="../js/ajaxfileupload.js" type="text/javascript"></script>
    <script src="../js/BaseModule.js"></script>
    <!--<script src="../js/Order.js"></script>-->
    <!-- layui css -->
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <!-- layui js -->
    <script src="../layui/layui.js" type="text/javascript"></script>
    <script src="../js/Order.js" type="text/javascript"></script>



    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=232&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        $('#txtWXMan').val(parsedJson.Man + "(" + parsedJson.InName + ")");
                        $('#txtInMan').val(parsedJson.Man);
                        $('#txtInName').val(parsedJson.InName);

                        ShowBLXXInfo("8888888");// 不良现象 只是打开，不显示数据
                        ShowBLYYInfo("8888888", "8888888");  // 不良原因 只是打开，不显示数据
                        ShowMaterInfo("8888888", "8888888"); // 已安装的物料 只是打开，不显示数据

                        $('#txtWXUnit').focus();
                    }
                }
            })
        });



    </script>


    <script type="text/javascript">
        //获取当前所在的模块、菜单
        const currentTabId = sessionStorage.getItem("currentTabId");
        const ModuleName = currentTabId != null ? JSON.parse(currentTabId).ModuleName : null;

        // 输入 扫描 序列号，回车
        function ScanSNCodeCode_keydown(event) {

            if (event.keyCode == 13) {
                $("#L_Mgs").html("");
                var sFlag = "30-2"

                var sCode = $('#txtSNCode').val();
                var sUnit = $('#txtWXUnit').val();  // 作业单元
                if (sCode == "") {
                    return;
                }
                if (sUnit == "") {
                    $('#L_Mgs').html("请输入作业单元。");
                    return;
                }


                var Data = '';
                var Params = { No: sCode, Name: "", Item: "", MNo: "", MName: "", A: "", B: "", C: "", D: "", E: sUnit, F: "", Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);

                $.ajax({
                    type: "POST",
                    url: "../Service/OrderAjax.ashx?OP=GetWXInfo&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                            var Str = eval(parsedJson.json)[0];
                            $("#txtPECode").val(Str.PECode);  // eval(parsedJson.json)[0].PECode
                            $("#txtEXENo").val(Str.EXENo);
                            $("#txtOrderNo").val(Str.OrderNo);
                            $("#txtSerialNo").val(Str.BatchNo);
                            $("#txtFNo").val(Str.MaterNo);
                            $("#txtFName").val(Str.MaterName);
                            $("#txtModel").val(Str.Model);
                            $("#txtGX").val("(" + Str.ProcedureNo + ")" + Str.ProcedureName);
                            $("#L_NowGX").html("(" + Str.ProcedureNo + ")" + Str.ProcedureName);
                            $("#txtFDate").val(Str.FDate);
                            $("#txtStatus").val(Str.Status);
                            $("#txtFMan").val(Str.FMan);
                            $("#txtOrderKind").val(Str.OrderKind);
                            $("#txtDeptName").val(Str.FDeptName);

                            ShowBLXXInfo(Str.PECode);
                            ShowMaterInfo(Str.BatchNo, Str.EXENo); // 已安装的物料
                            GetProcedureList(Str.BatchNo, Str.OrderNo); // 加载当前序列号已生产的工序

                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                            $("#L_Mgs").html("您未登陆系统，请先登录！")
                            //location.href = "Login.htm";
                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_NOTRepair') {
                            $('#L_Mgs').html("该序列号不需要维修！");
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_CREATE') {
                            $('#L_Mgs').html("该序列号对应不合格单还在创建状态，请到作业执行提交。");
                        }
                        else {
                            $("#L_Mgs").html("请确认扫描的序列号是否正确！");
                        }
                    },
                    error: function (data) {
                        $("#div_warningAddBLXX").html("系统出错，请重试2！");
                        $('#div_warningAddBLXX').show();
                    }
                });



            }  // if (event.keyCode == 13)

        }


        //显示不良现象
        function ShowBLXXInfo(sNo) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#BLXXList',
                    id: 'BLXXID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-16&CNO=' + sNo,
                    height: '250',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'ECode', title: '不良现象代码', width: 150 },
                        { field: 'EName', title: '不良现象名称', width: 300 },
                        { field: 'EDesc', title: '不良现象描述', width: 150 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate2', title: '录入时间', width: 120 }
                        //{ field: 'op', title: '操作', width: 130, toolbar: '#barDemo_BLXX', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行单击事件（双击事件为：rowDouble）
                table.on('row(BLXXList)', function (obj) {
                    obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                    obj.tr.addClass('layui-table-click');

                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值

                    $("#txtAECode").val(data.ECode);
                    $("#txtAEName").val(data.EName);

                    ShowBLYYInfo(data.PECode, data.ECode);

                });


                //监听行工具事件
                table.on('tool(BLXXList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值


                    if (layEvent == 'edit') {
                        $('#txtAddKind').val("88888");
                        $("#L_WOFile").html("修改不良信息");
                    }
                    else if (layEvent == 'del') {

                    }

                });

            });  // layui.use('table', function () {


        }



        //显示不良原因
        function ShowBLYYInfo(sNo, sENo) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#BLYYList',
                    id: 'BLYYID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-17&CNO=' + sNo + "&Item=" + sENo,
                    height: '250',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'CNo', title: '不良原因代码', width: 150 },
                        { field: 'CName', title: '不良原因名称', width: 150 },
                        { field: 'CDesc', title: '不良原因描述', width: 200 },
                        { field: 'Location', title: '位号', width: 200 },
                        { field: 'CType', title: '不良原因类型', width: 120 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate2', title: '录入时间', width: 120 },
                        { field: 'op', title: '操作', width: 130, toolbar: '#barDemo_BLYY', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(BLYYList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值


                    if (layEvent == 'edit') {
                        $('#txtAEFlag').val("30-2-2");
                        $("#L_AddBLYY").html("修改不良原因信息");

                        if ($('#txtStatus').val() == "已完成") {
                            layer.msg('不合格处理单已完成，无需操作！');
                            return;
                        }

                        $('#txtAYYCode').val(data.CNo);
                        $('#txtCKind').val(data.CName);
                        $('#txtCDesc').val(data.CDesc);
                        $('#txtLocation').val(data.Location);
                        $('#txtCType').val(data.CType);
                        $("#txtAYYCode").attr({ "disabled": "disabled" });

                        var scrollTop = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
                        document.getElementById('Div_AddBLYY').style.top = (scrollTop + 10) + "px";

                        document.getElementById('Div_AddBLYY').style.display = 'block';
                    }
                    else if (layEvent == 'del') {  // 删除：和作业执行信息更改界面代码一样

                        if ($('#txtStatus').val() == "已完成") {
                            layer.msg('不合格处理单已完成，无需操作！');
                            return;
                        }

                        layer.confirm('您确实要删除该记录么？', function (index) {

                            //向服务端发送禁用指令
                            var sPECode = $('#txtPECode').val();
                            var sCNo = data.CNo;
                            var sECode = data.ECode;
                            var sFlag = "30-2-3";

                            var Data = '';
                            var Params = { No: sPECode, Name: "", Item: sCNo, MNo: "", MName: "", A: "", B: "", C: "", D: "", E: sECode, F: "", Remark: "", Flag: sFlag };
                            var Data = JSON.stringify(Params);

                            $.ajax({
                                type: "POST",
                                url: "../Service/OrderAjax.ashx?OP=OPWXInfo&CFlag=30-2-3",
                                data: { Data: Data },
                                success: function (data) {
                                    var parsedJson = jQuery.parseJSON(data);

                                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                        layer.msg('删除成功！');

                                        obj.del(); //删除对应行（tr）的DOM结构
                                        layer.close(index);

                                    } else {
                                        layer.msg('删除失败，请重试！')
                                    }
                                },
                                error: function (data) {
                                    layer.msg('删除失败2，请重试！')
                                }
                            });

                        }); // 删除
                    }

                });

            });  // layui.use('table', function () {


        }



        //添加不良原因
        function AddBLYYDialog() {

            if ($('#txtStatus').val() == "已完成") {
                layer.msg('不合格处理单已完成，无需操作！');
                return;
            }
            if ($('#txtPECode').val() == "") {
                layer.msg('获取不到不合格处理单！');
                return;
            }
            if ($('#txtSerialNo').val() == "") {
                layer.msg('请选择不良的序列号');
                return;
            }
            if ($('#txtAECode').val() == "") {
                layer.msg('请先选择要分析的不良现象');
                return;
            }
            if ($('#txtWXUnit').val() == "") {
                layer.msg('作业单元不能为空');
                return;
            }
            if ($('#txtWXUnitName').val() == "") {
                layer.msg('请输入正确的作业单元');
                return;
            }

            $('#L_AddBLYY').html("添加不良原因");
            $('#txtAEFlag').val("30-2-1");
            $('#txtAYYCode').val("");
            $('#txtCKind').val("");
            $('#txtCDesc').val("");
            $('#txtLocation').val("");
            $('#txtCType').val("");
            $('#div_warningAddBLYY').html("");
            $('#div_warningAddBLYY').hide();
            $("#txtAYYCode").removeAttr("disabled");

            var scrollTop = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
            document.getElementById('Div_AddBLYY').style.top = (scrollTop + 10) + "px";

            document.getElementById('Div_AddBLYY').style.display = 'block';
            $('#txtAYYCode').focus();
        }

        // 输入不良原因代码，回车
        function ScanEYYCode_keydown(event) {

            if (event.keyCode == 13) {
                $('#div_warningAddBLYY').html("");
                var sFlag = "111-18"

                var sCode = $('#txtAYYCode').val();
                if (sCode == "") {
                    return;
                }

                var Data = '';
                var Params = { No: sCode, Item: "", Name: "", MNo: "", MName: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "" };
                var Data = JSON.stringify(Params);

                $.ajax({
                    type: "POST",
                    url: "../Service/OrderAjax.ashx?OP=GetDefectsCauseByNo&CNO=" + sCode + "&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        $("#txtCKind").val("");
                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            var sStr = eval(parsedJson.json)[0];
                            $("#txtCKind").val(sStr.CDesc);
                            $('#div_warningAddBLYY').html("不良原因添加成功，请继续添加");
                        }
                        else if (parsedJson != undefined && parsedJson != '') {
                            $("#div_warningAddBLYY").html(parsedJson.Msg);
                            $("#div_warningAddBLYY").show();
                        }
                    },
                    error: function (data) {
                        $("#div_warningAddBLYY").html("系统出错，请重试2！");
                        $("#div_warningAddBLYY").show();
                    }
                });
            }  // if (event.keyCode == 13)

        }

        //显示这个序列号已安装的物料
        function ShowMaterInfo(sNo, sEXENo) {

            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#MaterList',
                    id: 'MaterID',
                    url: '../Service/OrderAjax.ashx?OP=GetOrderInfo&CFlag=111-19&CNO=' + sNo + "&Item=" + sEXENo,
                    height: '350',
                    cellMinWidth: 80,
                    count: 200, //数据总数 服务端获得
                    limit: 200, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [50, 100, 150, 200], //分页显示每页条目下拉选择
                    cols: [[
                        { type: 'numbers', title: '序号', width: 40 },
                        { field: 'ProcedureName', title: '工序', width: 80 },
                        { field: 'MaterNo', title: '物料编码', width: 150 },
                        { field: 'MaterName', title: '物料名称', width: 180 },
                        // { field: 'OldSN', title: '旧序列号', width: 120 },
                        { field: 'MaterBatch', title: '物料序列号', width: 120 },
                        { field: 'ConsumeNum', title: '数量', width: 50 },
                        { field: 'Kind', title: '类别', width: 50 },
                        { field: 'DealType', title: '旧料处理意见', minWidth: 100, edit: 'text', style: 'color: red;background-color:#d5f1c0;' },
                        { field: 'op', title: '操作', width: 130, toolbar: '#barDemo_Mater', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(MaterList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值


                    if (layEvent == 'edit') {
                        if ($('#txtStatus').val() == "已完成") {
                            layer.msg('不合格处理单已完成，无需操作！');
                            return;
                        }

                        $('#L_SCMater').html("");
                        $('#txtAEFlag').val("30-2-5");
                        $("#txtCHGX").val(data.ProcedureNo);
                        $('#TR_SCMater').show();
                        $('#txtSCMater').focus();
                    }
                    else if (layEvent == 'del') {
                        if ($('#txtStatus').val() == "已完成") {
                            layer.msg('不合格处理单已完成，无需操作！');
                            return;
                        }

                        var sDeal = data.DealType.trim();  // 处理意见
                        if (sDeal == "") {
                            layer.msg('请填写处理意见');
                            return;
                        }
                        if (data.Kind == "拆解") {
                            layer.msg('此料为拆解下来的，不可再拆解');
                            return;
                        }
                        $('#L_SCMater').html("");

                        layer.confirm('您确实要拆解该物料么？', function (index) {

                            //向服务端发送禁用指令
                            var sPECode = $('#txtPECode').val();
                            var sEXENo = data.EXENo;
                            var sMSC = data.ConsumeBatch;  // 作业执行扣减物料唯一编号
                            var sMName = data.MaterName;
                            var sMNo = data.MaterNo;
                            var sMBatch = data.MaterBatch;  // 物料扣减的序列号
                            var sGXName = data.ProcedureName;
                            var sSN = data.BatchNo;   // 整机序列号
                            var sGX = data.ProcedureNo;
                            var sFlag = "30-2-4";

                            var Data = '';
                            var Params = { No: sPECode, Name: "", Item: sSN, MNo: sMNo, MName: sMName, A: sEXENo, B: sMSC, C: sDeal, D: sGXName, E: sGX, F: sMBatch, Remark: "", Flag: sFlag };
                            var Data = JSON.stringify(Params);

                            $.ajax({
                                type: "POST",
                                url: "../Service/OrderAjax.ashx?OP=OPWXInfo&CFlag=30-2-4",
                                data: { Data: Data },
                                success: function (data) {
                                    var parsedJson = jQuery.parseJSON(data);

                                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                        layer.msg('删除成功！');

                                        ShowMaterInfo(sSN, sEXENo);

                                    }
                                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_NOTRepair') {
                                        layer.msg("该不合格处理单已完成，不能再操作");
                                    }
                                    else {
                                        layer.msg('删除失败，请重试！')
                                    }
                                },
                                error: function (data) {
                                    layer.msg('删除失败2，请重试！')
                                }
                            });

                        }); // 删除
                    }

                });

            });  // layui.use('table', function () {


        }

        // 输入物料编码，回车
        function SCMater_keydown(event) {

            if (event.keyCode == 13) {
                $('#L_SCMater').html("");
                var sFlag = "30-2-5"

                var sWO = $("#txtOrderNo").val();  // 工单号   
                var sUnit = $('#txtWXUnit').val();  // 维修作单元
                var sPECode = $('#txtPECode').val();
                var sEXENo = $('#txtEXENo').val();
                var sSN = $('#txtSerialNo').val();
                //var sGX = $('#txtCHGX').val();
                var sTNo = $('#txtCHGX option:selected').text(); //选中的文本 获取下拉值
                var sGX = sTNo.substring(sTNo.indexOf("(") + 1, sTNo.indexOf(")"));  //工序  (05)总装-V1.0 截取字符串，字符位置
                var sGXName = sTNo.substr(sTNo.indexOf(")") + 1, sTNo.length); //工序  (05)总装-V1.0 截取字符串，字符位置
                var sVer = sTNo.substr(sTNo.indexOf("-") + 1, sTNo.length);
                var sCODENo = $('#txtSCMater').val();

                if (sCODENo == "") {
                    return;
                }

                if (sSN == "") {
                    $('#L_SCMater').html("获取不到序列号");
                    return;
                }
                if (sGX == "") {
                    $('#L_SCMater').html("请选择安装物料所在工序");
                    return;
                }


                var Data = '';
                var Params = { No: sPECode, Name: sUnit, Item: sSN, MNo: "", MName: "", A: sEXENo, B: sGX, C: sVer, D: sCODENo, E: sGXName, F: sWO, Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);

                $.ajax({
                    type: "POST",
                    url: "../Service/OrderAjax.ashx?OP=OPWXInfo&CFlag=30-2-5",
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {

                            $('#txtSCMater').val("");
                            ShowMaterInfo(sSN, sEXENo);

                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                            $("#L_SCMater").html("您未登陆系统，请先登录！")
                            //location.href = "Login.htm";
                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTSOVER') {
                            $("#L_SCMater").html("序列号已完工，无需再扫描！");
                            $('#txtSCMater').val("");
                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'N_EXISTOVER') {
                            $("#L_SCMater").html("该批号/序列号不存在或已使用！");
                            $('#txtSCMater').val("");
                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTMATER') {
                            $("#L_SCMater").html("该物料已扫描完成，无需再扫描！");
                            $('#txtSCMater').val("");
                        }
                        else {
                            $("#SerialBOMSaveBtn").removeAttr("disabled");
                            $("#L_SCMater").html("系统出错，请重试1！")
                        }
                    },
                    error: function (data) {
                        $("#L_SCMater").html("系统出错，请重试2！");
                    }
                });



            }  // if (event.keyCode == 13)

        }


        function GetProcedureList(sNo,sWO) {  // 加载工序

            var sSs = "工序";

            var keywords = encodeURI(sSs);  // 解决中文乱码的问题。
            $.ajax({
                type: "get",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                url: "../Service/OrderAjax.ashx?OP=GetProcedureList&CFlag=111-2-1&CNO=" + sNo + "&Item=" + sWO,
                success: function (data) {
                    var parsedJson = eval(data).Msg;

                    var sKong = "<option value=''> </option>";
                    $("#txtCHGX").empty();
                    $("#txtCHGX").append(sKong + parsedJson);

                    $("#txtBackGX").empty();
                    $("#txtBackGX").append(sKong + parsedJson);
                }
            });
        }

        function export_pdf_custom(value) {
            // 找到需要打印的序列号+工序
            var Params = { No: value.MNo, Name: ModuleName, Item: value.G, MNo: "", MName: "", A: "", B: value.Item, C: "", D: "", E: "", F: value.No, Remark: "维修", Flag: "WX" };
            var Data = JSON.stringify(Params);
            $.ajax({
                url: "../Service/DHRAjax.ashx?OP=CreateDHRPDF",
                data: {
                    Data: Data
                },
                type: "POST",
                success: function (res) {
                    var parsedJson = jQuery.parseJSON(res);
                    if (parsedJson.Msg == "LoginError") {
                        layer.msg("登录超时，请重新登录！")
                    }
                    else if (parsedJson.Msg == "N_DHR") {
                        layer.msg("未找到需要生成的序列号！")
                    }
                    else if (parsedJson.Msg == "N_FLOWNO") {
                        layer.msg("未获取到对应的不合格处理单，生成失败！")
                    }
                    else if (parsedJson.Msg == "Success") {
                        if (parsedJson.Data.length == 0) {
                            layer.msg("未找到需要生成的序列号，生成失败！")
                        }
                        else if (parsedJson.SumCount == parsedJson.Data.length) {
                            layer.msg("生成成功！")
                        }
                        else {
                            layer.msg("生成失败！")
                        }
                    } else {
                        layer.msg("生成失败！")
                    }
                },
                error: function () {
                    layer.msg("系统出错，请稍后再试！")
                }
            })
        }



        function closeDialog() {
            document.getElementById('light').style.display = 'none';
            document.getElementById('Div_AddBLYY').style.display = 'none';
            document.getElementById('fade').style.display = 'none';
        }

        function AddBLYYcloseDialog() {
            document.getElementById('Div_AddBLYY').style.display = 'none';

            $("#txtAECode").val("");
            $("#txtAEName").val("");
        }



        function SaveMater() {
            var sMaterNo = $("#txtRepMaterNo").val().trim();
            var sPECode = $("#txtPECode").val().trim();

            if (sPECode == "") {
                layer.msg("获取不到不合格处理单！")
                return
            }

            if (sMaterNo == "") {
                $("#txtRepMaterName").val("")
                return
            }

            var Params = { No: sPECode, Name: "", Item: "", MNo: sMaterNo, MName: "", A: "", B: "", C: "", D: "", E: "", F: "", Remark: "", Flag: "30-2-7" };
            var Data = JSON.stringify(Params);

            $.ajax({
                url: "../Service/OrderAjax.ashx?OP=OPWXInfo&CFlag=30-2-7",
                type: "POST",
                data: {
                    Data: Data
                },
                success: function (res) {
                    var parsedJson = jQuery.parseJSON(res);

                    if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                        $('#txtRepMaterName').val(parsedJson.RNo);
                    }
                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'loginError') {
                        $("#L_SCMater").html("您未登陆系统，请先登录！")
                    }
                    else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'NO_Mater') {
                        layer.msg("系统无此产品编码，请确认！")
                        $('#txtRepMaterName').val("");
                    }
                    else {
                        layer.msg("系统出错，请重试1")
                    }
                },
                error: function (err) {
                    layer.msg("系统出错，请重试2")
                }
            })
        }


    </script>


    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })

            $("#txtDealType").change(function () {

                const flag = $(this).val() == "维修拆解"

                $("#txtBackGX, #txtMaintainType, #txtMaintainDesc, #txtRepMaterNo").prop("disabled", flag).val("");

                $("#TR_SCMater").hide()

                $("div[lay-id='MaterID']")
                    .find(".layui-table-box")
                    .children()                // 获取所有子元素
                    .filter(":has(button)")    // 筛选包含按钮的容器
                    .find("button")            // 二次定位
                    .prop("disabled", flag);


            })

        })
    </script>

    <style type="text/css">

             #HDiv {
                 position: fixed;
                 top: 0;
                 width: 100%;
                 height: 40px;
             }

             #light table {
                 margin: 0px;
             }
             /*
             .wangid_conbox {
                 width:90%;
                 background-color:red;
             }
             */
             /*     调整网格的宽度
             .layui-table-view {
                 width: 96%;
             }
        */
             .table > tbody > tr > td {
                 border: 0px;
             }

             .black_overlay {
                 display: none;
                 position: absolute;
                 top: 0%;
                 left: 0%;
                 width: 100%;
                 height: 100%;
                 background-color: #bbbcc7;
                 z-index: 1001;
                 -moz-opacity: 0.8;
                 opacity: .80;
                 filter: alpha(opacity=60);
             }

             .LabelDelBtn {
                 color: White;
                 font-size: 12px;
                 font-weight: bold;
                 cursor: pointer;
                 border-radius: 5px;
                 padding: 3px 5px;
                 background: #da542e;
             }

             .LabelAddBtn {
                 color: White;
                 font-size: 12px;
                 font-weight: bold;
                 cursor: pointer;
                 border-radius: 5px;
                 padding: 3px 5px;
                 background: #579fd8;
             }
    </style>




</head>
<body>
    <div class="div_find">
        <p>
            <label class="find_labela">维修人：</label> <input type="text" id="txtWXMan" class="find_input" readonly="readonly" />
            <label class="find_labela">作业单元：</label><input type="text" id="txtWXUnit" class="find_input" onkeydown="ScanWXUnit_keydown(event)" />
            <label class="find_labela">单元名称：</label><input type="text" id="txtWXUnitName" class="find_input" style=" width:130px;" readonly="readonly" />
            <label class="find_labela">扫描信息：</label>
            <input type="text" id="txtSNCode" class="find_input" onkeydown="ScanSNCodeCode_keydown(event)" style=" color: Blue; font-weight: bold; font-size: 14px; width: 180px;" disabled=disabled />
        </p>

        <hr />
    </div>

    <div style="width:100%;">
        <table cellspacing="0" cellpadding="0" border='0' style="width: 100%;  ">
            <tr style=" height:30px;">
                <td style=" width:100px;font-weight:bold;color:blue;">
                    基本信息
                </td>
                <td>
                </td>
                <td colspan="3">
                    <label id="L_Mgs" style=" font-weight:bold;color:red;"></label>
                </td>
                <td>
                </td>
            </tr>
            <tr style=" height: 30px; ">
                <td style=" width:100px; text-align:right;">
                    不合格处理单:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtPECode" name="txtPECode" style="height: 28px;" readonly=readonly />
                    <input type="text" class="form-control" id="txtEXENo" name="txtEXENo" style="height: 28px;display:none;" readonly=readonly />
                </td>
                <td style=" width:100px; text-align:right;">
                    序列号:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtSerialNo" name="txtSerialNo" style="height: 28px;color:red;font-weight:bold;" readonly=readonly />
                </td>
                <td style=" width:100px; text-align:right;">
                    工单号:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtOrderNo" name="txtOrderNo" style="height: 28px;" readonly=readonly />
                </td>
            </tr>
            <tr style=" height: 30px;">
                <td style=" width:100px; text-align:right;">
                    产品编码:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtFNo" name="txtFNo" style="height: 28px;" readonly=readonly />
                </td>
                <td style=" width:100px; text-align:right;">
                    产品描述:
                </td>
                <td colspan="3">
                    <input type="text" class="form-control" id="txtFName" name="txtFName" style="height: 28px; width: 96%;" readonly=readonly />
                </td>
            </tr>
            <tr style=" height: 30px;">
                <td style=" width:100px; text-align:right;">
                    型号:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtModel" name="txtModel" style="height: 28px;" />
                </td>
                <td style=" width:100px; text-align:right;">
                    发生环节:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtGX" name="txtGX" style="height: 28px;" readonly=readonly />
                </td>
                <td style=" width:100px; text-align:right;">
                    日期:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtFDate" name="txtFDate" style="height: 28px;" readonly=readonly />
                </td>
            </tr>
            <tr style=" height: 30px;">
                <td style=" width:100px; text-align:right;">
                    状态:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtStatus" name="txtStatus" style="height: 28px;" readonly=readonly />
                </td>
                <td style=" width:100px; text-align:right;">
                    发现人:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtFMan" name="txtFMan" style="height: 28px;" readonly=readonly />
                </td>
                <td style=" width:100px; text-align:right;">
                    作业类型:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtOrderKind" name="txtOrderKind" style="height: 28px;" readonly=readonly />
                </td>
            </tr>
            <tr style=" height: 30px;">
                <td style=" width:100px; text-align:right;">
                    部门:
                </td>
                <td>
                    <input type="text" class="form-control" id="txtDeptName" name="txtDeptName" style="height:28px;" readonly=readonly />
                </td>
                <td style=" width:100px; text-align:right;">
                </td>
                <td>
                </td>
                <td style=" width:100px; text-align:right;">
                </td>
                <td>
                </td>
            </tr>
            <tr>
                <!--  下面是各个执行明细了:不良现象-->
                <td colspan="6">
                    <table cellspacing="0" cellpadding="0" border='0' style="width:100%; ">
                        <tr style=" height: 30px;">
                            <td style="font-weight: bold; color: blue;">
                                不良现象
                            </td>
                        </tr>
                        <tr style=" height:220px;vertical-align:top;">
                            <td>
                                <div class="wangid_conbox">
                                    <!-- 下面写内容   -->
                                    <table class="layui-hide" id="BLXXList" lay-filter="BLXXList"></table>
                                    <script type="text/html" id="barDemo_BLXX">
                                        <a class="layui-btn layui-btn-xs" lay-event="edit">修改</a>
                                        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                                    </script>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <!--  下面是各个执行明细了:不良原因-->
                <td colspan="6" style="font-weight: bold; color: blue; ">
                    不良原因
                    <div style="float: right;">
                        <input type='button' id="AddBLYY_Btn" value='添加' class="find_but" onclick="AddBLYYDialog()" onblur="AddBLYYDialog()" />
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                </td>
            </tr>
        </table>
    </div>
    <!--  下面是各个执行明细了:不良原因-->
    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="BLYYList" lay-filter="BLYYList"></table>
        <script type="text/html" id="barDemo_BLYY">
            <a class="layui-btn layui-btn-xs" lay-event="edit">修改</a>
            <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
        </script>
    </div>
    <!--  处理方式，维修方式-->
    <div style="width:100%;">
        <table cellspacing="0" cellpadding="0" border='0' style="width:100%; ">
            <tr style=" height: 30px;">
                <td style="font-weight: bold; color: blue;">
                    处理方式
                </td>
                <td>
                </td>
            </tr>
            <tr style=" height: 30px;">
                <td style="width:200px;">
                    <select class="form-control" id="txtDealType" style="height: 28px;">
                        <option value="维修">维修</option>
                        <option value="在线处理">在线处理</option>
                        <option value="品质异常">品质异常</option>
                        <option value="样机反馈">样机反馈</option>
                        <option value="维修拆解">维修拆解</option>
                        <option value="其他">其他</option>
                    </select>
                </td>
                <td>
                    <input type="text" class="form-control" id="txtYUNNo" name="txtYUNNo" placeholder="请填写填写云之家的单号" style="height:28px;" />
                </td>
            </tr>
            <tr style=" height: 30px;">
                <td style="font-weight: bold; color: blue;">
                    维修方式
                </td>
                <td>
                </td>
            </tr>
            <tr style=" height: 30px;">
                <td style="width:200px;">
                    <select class="form-control" id="txtMaintainType" style="height: 28px;">
                        <option value="维修">维修</option>
                        <option value="更换物料">更换物料</option>
                        <option value="飞线">飞线</option>
                        <option value="重焊">重焊</option>
                        <option value="重新烧录">重新烧录</option>
                        <option value="重调">重调</option>
                        <option value="其他">其他</option>
                    </select>
                </td>
                <td>
                    <input type="text" class="form-control" id="txtMaintainDesc" name="txtMaintainDesc" placeholder="请填写详细维修信息" style="height: 28px; width: 29.5%; display: inline-block" />
                    <input type="text" class="form-control" id="txtRepMaterNo" name="txtRepMaterNo" onblur="SaveMater()" placeholder="请输入物料编码" style="height: 28px; width: 29.5%; display: inline-block " />
                    <input type="text" class="form-control" id="txtRepMaterName" readonly="readonly" name="txtRepMaterName" placeholder="请输入物料名称" style="height: 28px; width: 29.5%; display: inline-block " />
                </td>
            </tr>
            <tr id="TR_SCMater" style=" height: 50px; display:none;">
                <td colspan="2">
                    <table cellspacing="0" cellpadding="0" border='0' style="width:100%; ">
                        <tr>
                            <td style="width:180px;font-weight:bold;">
                                请扫描序列号/批号：
                            </td>
                            <td style="width:180px;">
                                <select class="form-control" id="txtCHGX" style="height: 28px;">
                                    <option></option>
                                </select>
                            </td>
                            <td style="width:300px;">
                                <input type="text" class="form-control" id="txtSCMater" name="txtSCMater" onkeydown="SCMater_keydown(event)" style="height: 35px; width: 300px; color: Blue; font-weight: bold; font-size: 14px; border: 1px solid Blue;" />
                            </td>
                            <td>
                                <label id="L_SCMater" style="color:red;font-weight:bold;"></label>
                            </td>
                        </tr>
                    </table>
                </td>




            </tr>
        </table>
    </div>
    <!--  展示这个序列号装配包装的所有物料-->
    <div class="wangid_conbox" id="Div_MaterInfo">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="MaterList" lay-filter="MaterList"></table>
        <script type="text/html" id="barDemo_Mater">
            <button class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">拆解</button>
            <button class="layui-btn layui-btn-xs" lay-event="edit">绑定</button>
        </script>
    </div>
    <div style="width:100%;">
        <table cellspacing="0" cellpadding="0" border='0' style="width:100%; ">
            <tr style=" height: 10px;">
                <td colspan="3">
                </td>
            </tr>
            <tr style=" height: 30px;">
                <td style="font-weight: bold; color: blue; width: 100px;">
                    返回工序
                </td>
                <td style="text-align:left;">
                    <select class="form-control" id="txtBackGX" style="width: 300px; height: 25px; ">
                        <option></option>
                    </select>
                </td>
                <td style="width:60%;">
                    异常发生工序： <label id="L_NowGX"></label>
                </td>
            </tr>
            <tr style=" height: 30px;">
                <td colspan="3">
                </td>
            </tr>
            <tr style=" height: 30px;">
                <td colspan="3">
                    <input type='button' id="WXSave_Btn" value='提交' style="height: 30px;width: 65px;background-color: #56dcaf;color: white;border: 0px;border-radius: 5px;" />
                    <label id="L_SaveMgs" style="font-size:12px;color:red"></label>
                </td>

            </tr>

        </table>
        <br />
    </div>


    <div id="Div_AddBLYY" style="display: none;position: absolute;top: 2%;left: 10%;right: 10%;width: 80%;height: 450px;border: 1px solid #26d0a1; background-color: white;z-index: 1002;overflow: auto;">
        <div style="background-color: #6ea797; height: 35px;">
            <label id="L_AddBLYY" style=" padding:5px;font-size: 14px; color:White; "></label>
            <label onclick="AddBLYYcloseDialog()" style="float:right; margin:10px 10px 0 0; cursor:pointer;">X</label>
        </div>
        <table cellspacing="0" cellpadding="0" border='0' style="width:98%; ">
            <tr style=" height:35px;">
                <td style=" width:120px; text-align:center;">
                    不良现象代码
                </td>
                <td>
                    <input type="text" class="form-control" id="txtAECode" name="txtAECode" style=" height:30px; width:99%;" readonly="readonly" />
                </td>
            </tr>
            <tr style=" height:35px;">
                <td style=" width:120px; text-align:center;">
                    不良现象名称
                </td>
                <td>
                    <input type="text" class="form-control" id="txtAEName" name="txtAEName" style=" height:30px; width:99%;" readonly="readonly" />
                </td>
            </tr>
            <tr style=" height:35px;">
                <td style=" width:120px; text-align:center;">
                    不良原因代码
                </td>
                <td>
                    <input type="text" class="form-control" id="txtAYYCode" name="txtAYYECode" style=" height:30px; width:99%;" onkeydown="ScanEYYCode_keydown(event)" />
                </td>
            </tr>
            <tr style=" height: 35px;">
                <td style=" width:120px; text-align:center;">
                    不良原因名称
                </td>
                <td>
                    <input type="text" class="form-control" id="txtCKind" name="txtCKind" style=" height:30px; width:99%;" readonly="readonly" />
                </td>
            </tr>
            <tr style=" height: 35px;">
                <td style=" width:120px; text-align:center;">
                    原因描述
                </td>
                <td>
                    <textarea class="form-control" id="txtCDesc" name="txtCDesc" style="height: 120px; width: 99%;"> </textarea>
                </td>
            </tr>
            <tr style=" height: 35px;">
                <td style=" width:120px; text-align:center;">
                    位号
                </td>
                <td>
                    <input type="text" class="form-control" id="txtLocation" name="txtLocation" style=" height:30px; width:99%;" />
                </td>
            </tr>
            <tr style=" height: 35px;">
                <td style=" width:120px; text-align:center;">
                    不良原因类型
                </td>
                <td>
                    <input type="text" class="form-control" id="txtCType" name="txtCType" style=" height:30px; width:99%;" />
                </td>
            </tr>
        </table>
        <br />
        <div align="center">
            <input type='button' id="AddBLYYSave_Btn" value='提交' style="height: 30px;width: 65px;background-color: #56dcaf;color: white;border: 0px;border-radius: 5px;" />
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <input type='button' id="AddBLYYSave_Close" value='关闭' onclick='AddBLYYcloseDialog()' style="height: 30px;width: 65px;background-color: #56dcaf;color: white;border: 0px;border-radius: 5px;" />
        </div>
        <div id="div_warningAddBLYY" role="alert" style="text-align: center; display:none;color: Red ">
            <strong id="divsuccessAddBLYY" style="color: Red"></strong>
        </div>
    </div>



    <div class="input-group" style="display:none; ">
        <span class="input-group-addon">.</span>
        <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
        <span class="input-group-addon">.</span>
        <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
        <span class="input-group-addon">.</span>
        <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
        <span class="input-group-addon">.</span>
        <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
        <span class="input-group-addon">.</span>
        <input type="text" class="form-control" id="txtInName" name="txtInName" />
    </div>







    <div id="fade" class="black_overlay">
    </div>

    <div id="Loading" style="display: none; z-index: 1000; width:100%; text-align:right;">
        导出中... <img src="../fonts/loading.gif" width="60px" height="12px" />
    </div>


</body>
</html>