﻿using System;
using System.Collections;
using System.Data;
using System.Web;
using System.Web.SessionState;
using BLL;
using Common;
using Newtonsoft.Json;
using System.Collections.Generic; // Added for List
using System.Data.SqlClient; // Added for SqlParameter
using System.Text; // Added for StringBuilder
using System.Linq; // Added for Concat

namespace Web.Service
{
    /// <summary>
    /// CompanyCombo 的摘要说明 - 客户套餐管理
    /// 用于处理客户套餐相关的所有操作，包括：
    /// 1. 获取客户套餐信息列表
    /// 2. 添加新的客户套餐
    /// 3. 更新现有客户套餐信息
    /// 4. 更新客户套餐状态
    /// 5. 删除客户套餐信息
    /// </summary>
    public class CompanyCombo : IHttpHandler, IRequiresSessionState
    {
        // 登录用户信息
        private string _loginUser;    // 登录账号
        private string _loginName;    // 登录用户全名
        private string _companyNo;    // 所属公司编号

        #region 处理请求
        /// <summary>
        /// 处理HTTP请求
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "text/plain";
            string Result = string.Empty;
            string Operate = context.Request.Params["OP"];      // 操作类型
            string DataParams = context.Request.Params["Data"]; // 操作参数

            // 验证登录状态
            if (!CheckLogin())
            {
                context.Response.Write(JsonConvert.SerializeObject(new { Msg = "LoginError", Login = _loginName }));
                return;
            }

            switch (Operate)
            {
                case "GetCustMenuList": // 获取客户套餐信息
                    int slimit = int.Parse(context.Request.Params["limit"] ?? "10");  // 每页记录数
                    int spage = int.Parse(context.Request.Params["page"] ?? "1");     // 当前页码
                    GetCustMenuInfo(DataParams, slimit, spage);
                    break;

                case "AddCustMenuInfo":      // 添加客户套餐信息
                case "UpdateCustMenuInfo":   // 修改客户套餐信息
                case "UpdateCustMenuStatus": // 更新客户套餐状态
                case "DeleteCustMenuInfo":   // 删除客户套餐信息
                case "RenewCustMenuInfo":    // 续费客户套餐
                    if (string.IsNullOrEmpty(DataParams))
                    {
                        context.Response.Write(JsonConvert.SerializeObject(new { Msg = "无效的参数" }));
                        return;
                    }
                    Result = ProcessOperation(Operate, DataParams);
                    context.Response.Write(Result);
                    break;

                case "DecryptCode":    // 解密注册码
                    if (string.IsNullOrEmpty(DataParams))
                    {
                        context.Response.Write(JsonConvert.SerializeObject(new { Msg = "无效的参数" }));
                        return;
                    }
                    try 
                    {
                        var paramObj = DeserializeParams(DataParams);
                        if (paramObj == null || !paramObj.ContainsKey("Code") || string.IsNullOrEmpty(paramObj["Code"].ToString()))
                        {
                            context.Response.Write(JsonConvert.SerializeObject(new { Msg = "无效的注册码" }));
                            return;
                        }

                        string encryptedCode = paramObj["Code"].ToString().Trim();
                        string decryptedData = SecureDecrypt(encryptedCode);
                        
                        // 格式化解密后的数据以提高可读性
                        string formattedData = FormatDecryptedData(decryptedData);
                        
                        context.Response.Write(JsonConvert.SerializeObject(new { Msg = "Success", DecryptedData = formattedData }));
                    }
                    catch (Exception ex)
                    {
                        context.Response.Write(JsonConvert.SerializeObject(new { Msg = ex.Message }));
                    }
                    break;

                case "GenerateCode":    // 生成激活码/续费码
                    if (string.IsNullOrEmpty(DataParams))
                    {
                        context.Response.Write(JsonConvert.SerializeObject(new { Msg = "无效的参数" }));
                        return;
                    }
                    try 
                    {
                        var paramObj = DeserializeParams(DataParams);
                        if (paramObj == null)
                        {
                            context.Response.Write(JsonConvert.SerializeObject(new { Msg = "无效的参数格式" }));
                            return;
                        }

                        // 生成激活码或续费码
                        string code = GenerateCode(paramObj);
                        context.Response.Write(JsonConvert.SerializeObject(new { Msg = "Success", Code = code }));
                    }
                    catch (Exception ex)
                    {
                        context.Response.Write(JsonConvert.SerializeObject(new { Msg = ex.Message }));
                    }
                    break;

                default:
                    context.Response.Write(JsonConvert.SerializeObject(new { Msg = "无效的操作类型" }));
                    break;
            }
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 检查用户登录状态并获取用户信息
        /// </summary>
        /// <returns>true: 已登录; false: 未登录</returns>
        private bool CheckLogin()
        {
            if (HttpContext.Current.Session["LoginName"] != null)
            {
                _loginUser = HttpContext.Current.Session["LoginName"].ToString();
                _loginName = HttpContext.Current.Session["FullName"].ToString();
                _companyNo = HttpContext.Current.Session["CompanyNo"].ToString();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 处理各种操作请求
        /// </summary>
        /// <param name="operation">操作类型</param>
        /// <param name="jsonParams">JSON格式的参数</param>
        /// <returns>JSON格式的处理结果</returns>
        private string ProcessOperation(string operation, string jsonParams)
        {
            try
            {
                var paramObj = DeserializeParams(jsonParams);
                if (paramObj == null)
                {
                    return JsonConvert.SerializeObject(new { Msg = "无效的参数格式" });
                }

                string result = "";
                switch (operation)
                {
                    case "AddCustMenuInfo":
                        result = HandleAddOperation(paramObj);
                        break;
                    case "UpdateCustMenuInfo":
                        result = HandleUpdateOperation(paramObj);
                        break;
                    case "UpdateCustMenuStatus":
                        result = HandleStatusOperation(paramObj);
                        break;
                    case "DeleteCustMenuInfo":
                        result = HandleDeleteOperation(paramObj);
                        break;
                    case "RenewCustMenuInfo":
                        result = HandleRenewalOperation(paramObj);
                        break;
                }

                return JsonConvert.SerializeObject(new { Msg = string.IsNullOrEmpty(result) ? "Success" : result });
            }
            catch (Exception ex)
            {
                return JsonConvert.SerializeObject(new { Msg = ex.Message });
            }
        }

        /// <summary>
        /// 将JSON字符串反序列化为Hashtable对象
        /// </summary>
        /// <param name="jsonParams">JSON字符串</param>
        /// <returns>Hashtable对象，失败返回null</returns>
        private Hashtable DeserializeParams(string jsonParams)
        {
            try
            {
                return JsonConvert.DeserializeObject<Hashtable>(jsonParams);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 获取参数值并转换为指定类型
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="paramObj">参数对象</param>
        /// <param name="key">参数键名</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>转换后的参数值，失败返回默认值</returns>
        private T GetParamValue<T>(Hashtable paramObj, string key, T defaultValue = default)
        {
            if (paramObj == null || !paramObj.ContainsKey(key) || paramObj[key] == null)
                return defaultValue;

            try
            {
                // 如果是字符串类型，去除前后空格
                if (typeof(T) == typeof(string))
                {
                    string value = paramObj[key].ToString().Trim();
                    return value.Length == 0 ? defaultValue : (T)(object)value;
                }
                if (typeof(T) == typeof(bool))
                    return (T)(object)Convert.ToBoolean(paramObj[key]);
                if (typeof(T) == typeof(int))
                    return (T)(object)Convert.ToInt32(paramObj[key]);
                if (typeof(T) == typeof(decimal))
                    return (T)(object)Convert.ToDecimal(paramObj[key]);
                if (typeof(T) == typeof(DateTime))
                    return (T)(object)Convert.ToDateTime(paramObj[key]);
                return (T)Convert.ChangeType(paramObj[key], typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }
        #endregion

        #region 业务操作处理
        /// <summary>
        /// 生成申请编号
        /// </summary>
        private string GenerateApplyNo()
        {
            return CompanyComboBll.CreateApplyNo();
        }

        /// <summary>
        /// 处理添加客户套餐信息的操作
        /// </summary>
        /// <param name="paramObj">包含套餐信息的参数对象</param>
        /// <returns>处理结果消息</returns>
        private string HandleAddOperation(Hashtable paramObj)
        {
            string custNo = GetParamValue<string>(paramObj, "CustNo");
            if (string.IsNullOrEmpty(custNo))
            {
                return "客户编号不能为空";
            }

            // 验证必填字段
            string accountType = GetParamValue<string>(paramObj, "AccountType");
            if (string.IsNullOrEmpty(accountType))
            {
                return "结算方式不能为空";
            }

            DateTime effectiveDate = GetParamValue<DateTime>(paramObj, "EffectiveDate");
            if (effectiveDate == DateTime.MinValue)
            {
                return "生效日期不能为空";
            }

            string menuNo = GetParamValue<string>(paramObj, "MenuNo");
            if (string.IsNullOrEmpty(menuNo))
            {
                return "套餐编号不能为空";
            }

            // 检查客户编码+系统版本是否已存在
            if (CompanyComboBll.CheckCustMenuExists(custNo, menuNo, "", _companyNo))
            {
                return "该客户已存在此系统版本的套餐，请选择升级或降级操作";
            }

            // 生成申请编号
            string applyNo = GenerateApplyNo();

            // 生成激活码
            var codeParams = new Hashtable
            {
                { "ApplyNo", applyNo },  // 使用生成的申请编号
                { "CustNo", custNo },
                { "MenuNo", menuNo },
                { "RegType", "Z1" }, // Z1=激活
                { "AccountType", ConvertAccountTypeToCode(accountType) },
                { "EffectiveDate", effectiveDate },
                { "ExpiringDate", GetParamValue<DateTime>(paramObj, "ExpiringDate") }
            };
            string code = GenerateCode(codeParams);

            // 获取并验证工单超量价格JSON
            string awoPrice = GetParamValue<string>(paramObj, "AWOPrice");
            if (string.IsNullOrEmpty(awoPrice))
            {
                return "工单超量价格不能为空";
            }

            // 验证JSON格式是否正确
            try
            {
                var priceData = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(awoPrice);
                if (priceData == null || priceData.Count == 0)
                {
                    return "无效的工单超量价格格式";
                }

                // 验证价格梯度数据的完整性和合法性
                for (int i = 0; i < priceData.Count; i++)
                {
                    if (!priceData[i].ContainsKey("start") || !priceData[i].ContainsKey("price"))
                    {
                        return "工单超量价格数据格式不完整";
                    }
                    if (i < priceData.Count - 1 && !priceData[i].ContainsKey("end"))
                    {
                        return "除最后一个梯度外，其他梯度必须设置结束数量";
                    }
                }
            }
            catch
            {
                return "工单超量价格数据格式错误";
            }

            return CompanyComboBll.AddCustMenuInfo(
                Guid.NewGuid().ToString(),  // Id
                applyNo,                    // 使用生成的申请编号
                custNo,                     // CustNo
                GetParamValue<string>(paramObj, "CustName"),
                accountType,                // AccountType
                GetParamValue<DateTime>(paramObj, "SettlementStartTime"),
                GetParamValue<DateTime>(paramObj, "SettlementEndTime"),
                "激活",                      // RegType
                code,                       // 自动生成的激活码
                effectiveDate,              // EffectiveDate
                GetParamValue<DateTime>(paramObj, "ExpiringDate"),
                GetParamValue<int>(paramObj, "SettlementCycle"),
                menuNo,                     // MenuNo
                GetParamValue<string>(paramObj, "MenuName"),
                GetParamValue<decimal>(paramObj, "BasePrice"),
                GetParamValue<int>(paramObj, "BUP"),
                GetParamValue<int>(paramObj, "TVA"),
                GetParamValue<decimal>(paramObj, "AUserPrice"),
                awoPrice,                   // 工单超量价格(JSON格式)
                GetParamValue<string>(paramObj, "DFunction"),
                GetParamValue<bool>(paramObj, "SLA"),
                GetParamValue<bool>(paramObj, "DepthTrain"),
                GetParamValue<bool>(paramObj, "IMServices"),
                GetParamValue<bool>(paramObj, "CustomDev"),
                GetParamValue<bool>(paramObj, "InterfaceDev"),
                GetParamValue<bool>(paramObj, "OnsiteSV"),
                true,                       // NowVer - 新增时默认为是
                "已激活",                    // Status
                _companyNo,                 // CompanyNo
                _loginUser,                 // InMan
                DateTime.Now,               // InDate
                GetParamValue<string>(paramObj, "Remark")
            );
        }

        /// <summary>
        /// 处理更新客户套餐信息的操作
        /// </summary>
        /// <param name="paramObj">包含套餐信息的参数对象</param>
        /// <returns>处理结果消息</returns>
        private string HandleUpdateOperation(Hashtable paramObj)
        {
            string applyNo = GetParamValue<string>(paramObj, "ApplyNo");
            string custNo = GetParamValue<string>(paramObj, "CustNo");

            if (string.IsNullOrEmpty(applyNo))
                return "申请编号不能为空";
            if (string.IsNullOrEmpty(custNo))
                return "客户编号不能为空";

            // 验证必填字段
            string accountType = GetParamValue<string>(paramObj, "AccountType");
            if (string.IsNullOrEmpty(accountType))
            {
                return "结算方式不能为空";
            }

            DateTime effectiveDate = GetParamValue<DateTime>(paramObj, "EffectiveDate");
            if (effectiveDate == DateTime.MinValue)
            {
                return "生效日期不能为空";
            }

            string menuNo = GetParamValue<string>(paramObj, "MenuNo");
            if (string.IsNullOrEmpty(menuNo))
            {
                return "套餐编号不能为空";
            }

            // 检查是否在生效期内
            DateTime now = DateTime.Now.Date; // 只取日期部分
            DateTime expiringDate = GetParamValue<DateTime>(paramObj, "ExpiringDate", DateTime.MaxValue).Date;
            DateTime effectiveDateOnly = effectiveDate.Date;
            
            // 修改判断条件：如果当前日期等于失效日期，也允许修改
            if (now > effectiveDateOnly && now < expiringDate)
            {
                return "套餐在生效期内，不能修改任何信息";
            }

            // 检查客户编码+系统版本是否已存在
            if (CompanyComboBll.CheckCustMenuExists(custNo, menuNo, applyNo, _companyNo))
            {
                return "该客户已存在此系统版本的套餐，请选择升级或降级操作";
            }

            // 生成新的激活码
            var codeParams = new Hashtable
            {
                { "ApplyNo", applyNo },
                { "CustNo", custNo },
                { "MenuNo", menuNo },
                { "RegType", "Z1" }, // Z1=激活
                { "AccountType", ConvertAccountTypeToCode(accountType) },
                { "EffectiveDate", effectiveDate },
                { "ExpiringDate", expiringDate }
            };
            string code = GenerateCode(codeParams);

            // 获取并验证工单超量价格JSON
            string awoPrice = GetParamValue<string>(paramObj, "AWOPrice");
            if (string.IsNullOrEmpty(awoPrice))
            {
                return "工单超量价格不能为空";
            }

            // 验证JSON格式是否正确
            try
            {
                var priceData = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(awoPrice);
                if (priceData == null || priceData.Count == 0)
                {
                    return "无效的工单超量价格格式";
                }

                // 验证价格梯度数据的完整性和合法性
                for (int i = 0; i < priceData.Count; i++)
                {
                    if (!priceData[i].ContainsKey("start") || !priceData[i].ContainsKey("price"))
                    {
                        return "工单超量价格数据格式不完整";
                    }
                    if (i < priceData.Count - 1 && !priceData[i].ContainsKey("end"))
                    {
                        return "除最后一个梯度外，其他梯度必须设置结束数量";
                    }
                }
            }
            catch
            {
                return "工单超量价格数据格式错误";
            }

            return CompanyComboBll.UpdateCustMenuInfo(
                GetParamValue<string>(paramObj, "Id"),
                applyNo,                    // ApplyNo
                custNo,                     // CustNo
                GetParamValue<string>(paramObj, "CustName"),
                accountType,                // AccountType
                GetParamValue<DateTime>(paramObj, "SettlementStartTime"),
                GetParamValue<DateTime>(paramObj, "SettlementEndTime"),
                "激活",                      // RegType
                code,                       // 新生成的激活码
                effectiveDate,              // EffectiveDate
                expiringDate,               // ExpiringDate
                GetParamValue<int>(paramObj, "SettlementCycle"),
                menuNo,                     // MenuNo
                GetParamValue<string>(paramObj, "MenuName"),
                GetParamValue<decimal>(paramObj, "BasePrice"),
                GetParamValue<int>(paramObj, "BUP"),
                GetParamValue<int>(paramObj, "TVA"),
                GetParamValue<decimal>(paramObj, "AUserPrice"),
                awoPrice,                   // 工单超量价格(JSON格式)
                GetParamValue<string>(paramObj, "DFunction"),
                GetParamValue<bool>(paramObj, "SLA"),
                GetParamValue<bool>(paramObj, "DepthTrain"),
                GetParamValue<bool>(paramObj, "IMServices"),
                GetParamValue<bool>(paramObj, "CustomDev"),
                GetParamValue<bool>(paramObj, "InterfaceDev"),
                GetParamValue<bool>(paramObj, "OnsiteSV"),
                GetParamValue<bool>(paramObj, "NowVer"),
                "已激活",                    // Status
                _companyNo,                 // CompanyNo
                _loginUser,                 // InMan
                DateTime.Now,               // InDate
                GetParamValue<string>(paramObj, "Remark")
            );
        }

        /// <summary>
        /// 处理更新客户套餐状态的操作
        /// </summary>
        /// <param name="paramObj">包含状态信息的参数对象</param>
        /// <returns>处理结果消息</returns>
        private string HandleStatusOperation(Hashtable paramObj)
        {
            string applyNo = GetParamValue<string>(paramObj, "ApplyNo");
            string status = GetParamValue<string>(paramObj, "Status");

            if (string.IsNullOrEmpty(applyNo))
                return "申请编号不能为空";
            if (string.IsNullOrEmpty(status))
                return "状态不能为空";

            return CompanyComboBll.UpdateCustMenuStatus(applyNo, status, _companyNo, _loginUser);
        }

        /// <summary>
        /// 处理删除客户套餐信息的操作
        /// </summary>
        /// <param name="paramObj">包含删除信息的参数对象</param>
        /// <returns>处理结果消息</returns>
        private string HandleDeleteOperation(Hashtable paramObj)
        {
            string applyNo = GetParamValue<string>(paramObj, "ApplyNo");
            if (string.IsNullOrEmpty(applyNo))
                return "申请编号不能为空";

            // 获取套餐信息以检查生效日期
            DataTable dt = CompanyComboBll.GetCustMenuInfo(applyNo, "", "", "", 1, 1, _loginUser, _companyNo);
            if (dt != null && dt.Rows.Count > 0)
            {
                DateTime effectiveDate = Convert.ToDateTime(dt.Rows[0]["EffectiveDate"]).Date;
                DateTime expiringDate = dt.Rows[0]["ExpiringDate"] != DBNull.Value 
                    ? Convert.ToDateTime(dt.Rows[0]["ExpiringDate"]).Date
                    : DateTime.MaxValue;
                DateTime now = DateTime.Now.Date;

                // 修改判断条件：如果当前日期等于失效日期，也允许删除
                if (now > effectiveDate && now < expiringDate)
                {
                    return "该套餐在生效期内，不能删除";
                }
            }

            return CompanyComboBll.DeleteCustMenuInfo(applyNo, _companyNo, _loginUser);
        }

        /// <summary>
        /// 处理续费客户套餐信息的操作
        /// </summary>
        /// <param name="paramObj">包含续费信息的参数对象</param>
        /// <returns>处理结果消息</returns>
        private string HandleRenewalOperation(Hashtable paramObj)
        {
            string custNo = GetParamValue<string>(paramObj, "CustNo");
            if (string.IsNullOrEmpty(custNo))
            {
                return "客户编号不能为空";
            }

            string originalApplyNo = GetParamValue<string>(paramObj, "OriginalApplyNo");
            if (string.IsNullOrEmpty(originalApplyNo))
            {
                return "原申请编号不能为空";
            }

            // 验证必填字段
            string accountType = GetParamValue<string>(paramObj, "AccountType");
            if (string.IsNullOrEmpty(accountType))
            {
                return "结算方式不能为空";
            }

            DateTime effectiveDate = GetParamValue<DateTime>(paramObj, "EffectiveDate");
            if (effectiveDate == DateTime.MinValue)
            {
                return "生效日期不能为空";
            }

            string menuNo = GetParamValue<string>(paramObj, "MenuNo");
            if (string.IsNullOrEmpty(menuNo))
            {
                return "套餐编号不能为空";
            }

            // 生成申请编号
            string applyNo = GenerateApplyNo();

            // 生成续费码
            var codeParams = new Hashtable
            {
                { "ApplyNo", applyNo },  // 使用生成的申请编号
                { "CustNo", custNo },
                { "MenuNo", menuNo },
                { "RegType", "Z2" }, // Z2=续费
                { "AccountType", ConvertAccountTypeToCode(accountType) },
                { "EffectiveDate", effectiveDate },
                { "ExpiringDate", GetParamValue<DateTime>(paramObj, "ExpiringDate") }
            };
            string code = GenerateCode(codeParams);

            // 获取并验证工单超量价格JSON
            string awoPrice = GetParamValue<string>(paramObj, "AWOPrice");
            if (string.IsNullOrEmpty(awoPrice))
            {
                return "工单超量价格不能为空";
            }

            // 验证JSON格式是否正确
            try
            {
                var priceData = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(awoPrice);
                if (priceData == null || priceData.Count == 0)
                {
                    return "无效的工单超量价格格式";
                }

                // 验证价格梯度数据的完整性和合法性
                for (int i = 0; i < priceData.Count; i++)
                {
                    if (!priceData[i].ContainsKey("start") || !priceData[i].ContainsKey("price"))
                    {
                        return "工单超量价格数据格式不完整";
                    }
                    if (i < priceData.Count - 1 && !priceData[i].ContainsKey("end"))
                    {
                        return "除最后一个梯度外，其他梯度必须设置结束数量";
                    }
                }
            }
            catch
            {
                return "工单超量价格数据格式错误";
            }

            return CompanyComboBll.RenewCustMenuInfo(
                Guid.NewGuid().ToString(),  // Id
                applyNo,                    // 使用生成的申请编号
                custNo,                     // CustNo
                GetParamValue<string>(paramObj, "CustName"),
                accountType,                // AccountType
                GetParamValue<DateTime>(paramObj, "SettlementStartTime"),
                GetParamValue<DateTime>(paramObj, "SettlementEndTime"),
                "续费",                      // RegType 
                code,                       // 新生成的续费码
                effectiveDate,              // EffectiveDate
                GetParamValue<DateTime>(paramObj, "ExpiringDate"),
                GetParamValue<int>(paramObj, "SettlementCycle"),
                menuNo,                     // MenuNo
                GetParamValue<string>(paramObj, "MenuName"),
                GetParamValue<decimal>(paramObj, "BasePrice"),
                GetParamValue<int>(paramObj, "BUP"),
                GetParamValue<int>(paramObj, "TVA"),
                GetParamValue<decimal>(paramObj, "AUserPrice"),
                awoPrice,                   // 工单超量价格(JSON格式)
                GetParamValue<string>(paramObj, "DFunction"),
                GetParamValue<bool>(paramObj, "SLA"),
                GetParamValue<bool>(paramObj, "DepthTrain"),
                GetParamValue<bool>(paramObj, "IMServices"),
                GetParamValue<bool>(paramObj, "CustomDev"),
                GetParamValue<bool>(paramObj, "InterfaceDev"),
                GetParamValue<bool>(paramObj, "OnsiteSV"),
                true,                       // NowVer - 新增的续费记录设为在用版本
                "已激活",                    // Status 
                _companyNo,                 // CompanyNo
                _loginUser,                 // InMan
                DateTime.Now,               // InDate
                GetParamValue<string>(paramObj, "Remark"),
                originalApplyNo             // 原申请编号
            );
        }

        /// <summary>
        /// 将结算方式转换为代码
        /// </summary>
        private string ConvertAccountTypeToCode(string accountType)
        {
            switch(accountType)
            {
                case "月度":
                    return "J1";
                case "年度":
                    return "J2";
                case "一次性":
                    return "J3";
                default:
                    return "J1"; // 默认月度
            }
        }
        #endregion

        #region 获取客户套餐信息
        /// <summary>
        /// 获取客户套餐信息列表
        /// </summary>
        /// <param name="Params">查询参数（JSON格式）</param>
        /// <param name="rows">每页记录数</param>
        /// <param name="page">当前页码</param>
        private void GetCustMenuInfo(string Params, int rows, int page)
        {
            string sReturn = string.Empty;

            try
            {
                string sApplyNo = "";  // 申请编号
                string sCustNo = "";   // 客户编号
                string sCustName = ""; // 客户名称
                string sStatus = "";    // 状态
                string sMenuNo = "";    // 套餐编号
                string sNowVer = "";    // 在用版本

                // 解析查询参数
                if (!string.IsNullOrEmpty(Params))
                {
                    var queryParams = DeserializeParams(Params);
                    if (queryParams != null)
                    {
                        sApplyNo = GetParamValue<string>(queryParams, "ApplyNo");
                        sCustNo = GetParamValue<string>(queryParams, "CustNo");
                        sCustName = GetParamValue<string>(queryParams, "CustName");
                        sStatus = GetParamValue<string>(queryParams, "Status");
                        sMenuNo = GetParamValue<string>(queryParams, "MenuNo");
                        sNowVer = GetParamValue<string>(queryParams, "NowVer");
                    }
                }

                // 调用BLL层获取数据
                DataTable dt = CompanyComboBll.GetCustMenuInfo(sApplyNo, sCustNo, sCustName, sStatus, rows, page, _loginUser, _companyNo, sMenuNo, sNowVer);
                
                if (dt != null && dt.Rows.Count > 0)
                {
                    int iSumCount = int.Parse(dt.Rows[0]["NumCount"].ToString());
                    string sJson = JsonConvert.SerializeObject(dt);
                    sReturn = "{\"code\":0,\"msg\":\"\",\"count\":" + iSumCount + ",\"data\":" + sJson + "}";
                }
                else
                {
                    sReturn = "{\"code\":0,\"msg\":\"无数据\",\"count\":0,\"data\":[]}";
                }
            }
            catch (Exception ex)
            {
                sReturn = "{\"code\":1,\"msg\":\"" + ex.Message + "\",\"count\":0,\"data\":[]}";
            }

            HttpContext.Current.Response.Write(sReturn);
        }
        #endregion

        /// <summary>
        /// 生成激活码或续费码
        /// </summary>
        private string GenerateCode(Hashtable paramObj)
        {
            // 获取所需参数
            string applyNo = GetParamValue<string>(paramObj, "ApplyNo");
            string custNo = GetParamValue<string>(paramObj, "CustNo");
            string menuNo = GetParamValue<string>(paramObj, "MenuNo");
            string regType = GetParamValue<string>(paramObj, "RegType"); // Z1=激活, Z2=续费
            string accountType = GetParamValue<string>(paramObj, "AccountType"); // J1=月度, J2=年度, J3=一次性
            DateTime effectiveDate = GetParamValue<DateTime>(paramObj, "EffectiveDate");
            DateTime expiringDate = GetParamValue<DateTime>(paramObj, "ExpiringDate", DateTime.MaxValue);

            if (string.IsNullOrEmpty(menuNo) || string.IsNullOrEmpty(custNo) || effectiveDate == DateTime.MinValue)
            {
                throw new Exception("缺少必要参数");
            }

            // 确保注册类型和结算方式有值
            if (string.IsNullOrEmpty(regType))
            {
                regType = "Z1"; // 默认为激活
            }

            if (string.IsNullOrEmpty(accountType))
            {
                accountType = "J1"; // 默认为月度
            }

            // 生成激活码/续费码的基本格式：申请编码_客户编码_套餐代码_注册类型_结算方式_生效日期_失效日期
            string baseCode = $"{applyNo}_{custNo}_{menuNo}_{regType}_{accountType}_{effectiveDate:yyyyMMdd}";
            
            // 添加失效日期（如果有）
            if (expiringDate != DateTime.MaxValue)
            {
                baseCode += $"_{expiringDate:yyyyMMdd}";
            }
            else
            {
                baseCode += "_********"; // 如果没有失效日期，使用占位符
            }

            // 使用增强的加密方法
            return SecureEncrypt(baseCode);
        }

        /// <summary>
        /// 使用PBKDF2的增强加密方法
        /// </summary>
        /// <param name="input">要加密的字符串</param>
        /// <returns>加密后的字符串</returns>
        private string SecureEncrypt(string input)
        {
            // 生成随机盐和时间戳
            byte[] randomSalt = new byte[16];
            using (var rng = new System.Security.Cryptography.RNGCryptoServiceProvider())
            {
                rng.GetBytes(randomSalt);
            }
            string timestamp = DateTime.Now.ToString("yyyyMMddHHmmssfff");
            
            // 将时间戳和原始数据组合
            string dataWithTimestamp = $"{timestamp}|{input}";
            
            // 使用固定的主密钥
            string masterKey = "MES_SECURE_KEY_2025_PRODUCT_PRICING";
            string baseSalt = "MES_COMPANY_COMBO_SALT_2025";
            
            // 组合固定盐和随机盐
            byte[] combinedSalt = System.Text.Encoding.UTF8.GetBytes(baseSalt).Concat(randomSalt).ToArray();
            
            // 从密钥和组合盐生成加密密钥
            using (var deriveBytes = new System.Security.Cryptography.Rfc2898DeriveBytes(
                masterKey,
                combinedSalt,
                10000)) // 迭代次数
            {
                byte[] keyBytes = deriveBytes.GetBytes(32); // 256位密钥
                byte[] ivBytes = deriveBytes.GetBytes(16);  // 128位IV
                
                // 使用AES进行加密
                using (var aes = System.Security.Cryptography.Aes.Create())
                {
                    aes.Key = keyBytes;
                    aes.IV = ivBytes;
                    aes.Mode = System.Security.Cryptography.CipherMode.CBC;
                    aes.Padding = System.Security.Cryptography.PaddingMode.PKCS7;
                    
                    using (var encryptor = aes.CreateEncryptor(keyBytes, ivBytes))
                    using (var memoryStream = new System.IO.MemoryStream())
                    {
                        // 写入随机盐
                        memoryStream.Write(randomSalt, 0, randomSalt.Length);
                        
                        using (var cryptoStream = new System.Security.Cryptography.CryptoStream(
                            memoryStream,
                            encryptor,
                            System.Security.Cryptography.CryptoStreamMode.Write))
                        {
                            byte[] plainBytes = System.Text.Encoding.UTF8.GetBytes(dataWithTimestamp);
                            cryptoStream.Write(plainBytes, 0, plainBytes.Length);
                            cryptoStream.FlushFinalBlock();
                        }
                        
                        // 转换为URL安全的Base64字符串
                        return Convert.ToBase64String(memoryStream.ToArray())
                               .Replace('+', '-')  // URL安全
                               .Replace('/', '_')
                               .Replace("=", "");  // 去掉填充
                    }
                }
            }
        }

        /// <summary>
        /// 解密激活码
        /// </summary>
        /// <param name="encryptedCode">加密的代码</param>
        /// <returns>解密后的原始字符串</returns>
        public static string SecureDecrypt(string encryptedCode)
        {
            try
            {
                // 修复URL安全的Base64字符串
                encryptedCode = encryptedCode
                    .Replace('-', '+')
                    .Replace('_', '/');
                
                // 添加回被移除的填充
                switch (encryptedCode.Length % 4)
                {
                    case 2: encryptedCode += "=="; break;
                    case 3: encryptedCode += "="; break;
                }
                
                // 解码Base64
                byte[] encryptedBytes = Convert.FromBase64String(encryptedCode);
                
                // 提取随机盐(前16字节)
                byte[] randomSalt = new byte[16];
                Array.Copy(encryptedBytes, 0, randomSalt, 0, 16);
                
                // 获取加密数据
                byte[] encryptedData = new byte[encryptedBytes.Length - 16];
                Array.Copy(encryptedBytes, 16, encryptedData, 0, encryptedData.Length);
                
                // 使用固定的主密钥
                string masterKey = "MES_SECURE_KEY_2025_PRODUCT_PRICING";
                string baseSalt = "MES_COMPANY_COMBO_SALT_2025";
                
                // 组合固定盐和随机盐
                byte[] combinedSalt = System.Text.Encoding.UTF8.GetBytes(baseSalt).Concat(randomSalt).ToArray();
                
                // 从密钥和组合盐生成相同的加密密钥和IV
                using (var deriveBytes = new System.Security.Cryptography.Rfc2898DeriveBytes(
                    masterKey,
                    combinedSalt,
                    10000))
                {
                    byte[] keyBytes = deriveBytes.GetBytes(32); // 256位密钥
                    byte[] ivBytes = deriveBytes.GetBytes(16);  // 128位IV
                    
                    // 使用AES进行解密
                    using (var aes = System.Security.Cryptography.Aes.Create())
                    {
                        aes.Key = keyBytes;
                        aes.IV = ivBytes;
                        aes.Mode = System.Security.Cryptography.CipherMode.CBC;
                        aes.Padding = System.Security.Cryptography.PaddingMode.PKCS7;
                        
                        using (var decryptor = aes.CreateDecryptor(keyBytes, ivBytes))
                        using (var memoryStream = new System.IO.MemoryStream(encryptedData))
                        using (var cryptoStream = new System.Security.Cryptography.CryptoStream(
                            memoryStream,
                            decryptor,
                            System.Security.Cryptography.CryptoStreamMode.Read))
                        {
                            using (var streamReader = new System.IO.StreamReader(cryptoStream))
                            {
                                string decryptedData = streamReader.ReadToEnd();
                                // 分离时间戳和原始数据
                                string[] parts = decryptedData.Split('|');
                                if (parts.Length != 2)
                                    throw new Exception("Invalid data format");
                                    
                                // 验证时间戳格式
                                if (!DateTime.TryParseExact(parts[0], "yyyyMMddHHmmssfff", 
                                    System.Globalization.CultureInfo.InvariantCulture, 
                                    System.Globalization.DateTimeStyles.None, 
                                    out DateTime timestamp))
                                {
                                    throw new Exception("Invalid timestamp format");
                                }
                                
                                return parts[1]; // 返回原始数据
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception("解密失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 格式化解密后的数据以提高可读性
        /// </summary>
        /// <param name="decryptedData">解密后的原始字符串</param>
        /// <returns>格式化后的字符串</returns>
        private string FormatDecryptedData(string decryptedData)
        {
            if (string.IsNullOrEmpty(decryptedData))
                return string.Empty;

            try
            {
                // 解析数据格式：申请编码_客户编码_套餐代码_注册类型_结算方式_生效日期_失效日期
                string[] parts = decryptedData.Split('_');
                if (parts.Length < 6)
                    return decryptedData; // 如果格式不符合预期，返回原始数据

                StringBuilder sb = new StringBuilder();
                sb.AppendLine("解密数据详情:");
                sb.AppendLine("-------------------------------------");
                
                sb.AppendLine("申请编码: " + parts[0]);
                sb.AppendLine("客户编码: " + parts[1]);
                sb.AppendLine("套餐代码: " + parts[2]);
                
                // 注册类型
                string regType = parts[3];
                string regTypeDesc = "未知";
                if (regType == "Z1") regTypeDesc = "激活";
                else if (regType == "Z2") regTypeDesc = "续费";
                sb.AppendLine("注册类型: " + regTypeDesc + " (" + regType + ")");
                
                // 结算方式
                string accountType = parts[4];
                string accountTypeDesc = "未知";
                if (accountType == "J1") accountTypeDesc = "月度";
                else if (accountType == "J2") accountTypeDesc = "年度";
                else if (accountType == "J3") accountTypeDesc = "一次性";
                sb.AppendLine("结算方式: " + accountTypeDesc + " (" + accountType + ")");
                
                // 日期格式化
                if (parts.Length > 5 && parts[5].Length == 8)
                {
                    try
                    {
                        string effectiveDate = parts[5];
                        string formattedDate = effectiveDate.Substring(0, 4) + "-" + 
                                              effectiveDate.Substring(4, 2) + "-" + 
                                              effectiveDate.Substring(6, 2);
                        sb.AppendLine("生效日期: " + formattedDate);
                    }
                    catch
                    {
                        sb.AppendLine("生效日期: " + parts[5] + " (格式错误)");
                    }
                }
                
                // 失效日期
                if (parts.Length > 6 && parts[6].Length == 8)
                {
                    if (parts[6] == "********")
                    {
                        sb.AppendLine("失效日期: 永久有效");
                    }
                    else
                    {
                        try
                        {
                            string expiringDate = parts[6];
                            string formattedDate = expiringDate.Substring(0, 4) + "-" + 
                                                  expiringDate.Substring(4, 2) + "-" + 
                                                  expiringDate.Substring(6, 2);
                            sb.AppendLine("失效日期: " + formattedDate);
                        }
                        catch
                        {
                            sb.AppendLine("失效日期: " + parts[6] + " (格式错误)");
                        }
                    }
                }
                
                sb.AppendLine("-------------------------------------");
                sb.AppendLine("原始数据: " + decryptedData);
                
                return sb.ToString();
            }
            catch
            {
                // 如果解析过程中出现任何错误，返回原始数据
                return "解析失败，原始数据: " + decryptedData;
            }
        }

        /// <summary>
        /// 获取处理程序是否可重用
        /// </summary>
        public bool IsReusable
        {
            get { return false; }
        }
    }
}