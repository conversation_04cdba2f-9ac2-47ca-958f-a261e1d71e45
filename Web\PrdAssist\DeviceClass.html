﻿<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <title>设备点检计划</title>

    <link href="../css/all.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="../css/bootstrap-table.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../css/layuiM.css" media="all" />
    <link href="../css/orderinfo.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="../js/jquery-1.11.0.min.js"></script>
    <script src="../layui/layui.js" type="text/javascript"></script>
    <link href="../js/skin/layer.css" rel="stylesheet" />

    <script src="../js/layer/layer.js" type="text/javascript"></script>
    <script src="../js/PrdAssist.js" type="text/javascript"></script>

    <link href="../css/XC.css" rel="stylesheet" />



    <script type="text/javascript">

        // 获取界面传入的变量:js 获取URL的参数
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }

        $(function () {
            var h = $(window).height() - 40;
            //$('#dgDataList').bootstrapTable('resetView', { height: h });  // 固定表头，
            // $('#dgDataList1').bootstrapTable('resetView', { height: h });
            // $('#dgDataList').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列
            // $('#dgDataList1').bootstrapTable('hideColumn', 'ModuleID');  // 隐藏列

            var sCorpID = GetQueryString("CorpID");  // 企业号ID
            $.ajax({
                url: "../Service/LoginAjax.ashx?OP=JustRight&CFlag=110&CorpID=" + sCorpID,
                success: function (data) {
                    var parsedJson = jQuery.parseJSON(data);
                    if (parsedJson.Msg == 'loginError') {
                        var Num = parseInt(Math.random() * 1000);
                        window.location.href = "../Login.htm?CFlag=110&RA=" + Num;
                    }
                    else if (parsedJson.Msg == 'NO') {
                        $("#divShow").attr("style", "display:block");
                        $("#divList").attr("style", "display:none");
                        $("#divSubmit").attr("style", "display:none");
                        return;
                    }
                    else {
                        //$('#txtInMan').val(parsedJson.Man);
                        //$('#txtInName').val(parsedJson.InName);
                        // $('#dgDataList').bootstrapTable('refresh', { url: '../Service/BaseModuleAjax.ashx?OP=GetRoleInfo&CFlag=28-1' });
                    }
                }
            })
        });



    </script>


    <script type="text/javascript">


        layui.use('table', function () {
            var table = layui.table,
                form = layui.form;
            table.render({
                elem: '#DeviceClassHeadlist',
                id: 'DeviceClassHeadlistID',
                url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=16',
                height: 'full-80',
                cellMinWidth: 80,
                //toolbar: 'default',//工具栏
                //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                cols: [[
                    { type: 'numbers' },
                    { field: 'MaterNo', title: '设备分类编号', width: 200, sort: true },
                    { field: 'MaterName', title: '设备分类名称', width: 100, sort: true },
                    { field: 'DeviceKind', title: '设备类型', width: 100 },
                    { field: 'CheckReq', title: '点检要求', width: 100 },
                    { field: 'MaintainReq', title: '保养要求', width: 100 },
                    { field: 'DeptNo', title: '管理部门编号', width: 200 },
                    { field: 'DeptName', title: '管理部门名称', width: 200 },
                    /*                    { field: 'Status', title: '状态', width: 200 },*/
                    { field: 'Remark', title: '备注', width: 300 },
                    { field: 'InMan', title: '创建人', width: 90 },
                    { field: 'InDate2', title: '创建时间', width: 200, sort: true },
                    { field: 'op', title: '操作', width: 280, toolbar: '#barDemo_Head', fixed: 'right' }
                ]],
                page: true,
            });

            //监听是否选中操作
            table.on('checkbox(DeviceClassHeadlist)', function (obj) {
                var checked = obj.checked;
                var id = obj.data.MaterNo;
                var rowIndex = $(obj.tr).attr("data-index"); // 获取选中行的ID

                if (checked) {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#e31d3b" });
                }
                else {
                    // $(".layui-table-body tbody tr[data-index='" + rowIndex + "'").find('td[data-field="InitialValue"]').data('edit', true); // 指定修改具体的列
                    $(".layui-table-body tbody tr[data-index='" + rowIndex + "']").css({ 'color': "#" });
                }
                //alert(id);
            });


            //监听行单击事件（双击事件为：rowDouble）
            table.on('row(DeviceClassHeadlist)', function (obj) {
                obj.tr.siblings().removeClass('layui-table-click');  // 选中行颜色加深
                obj.tr.addClass('layui-table-click');

            });

            //监听单元格编辑
            table.on('edit(DeviceClassHeadlist)', function (obj) {
                var value = obj.value //得到修改后的值
                    , data = obj.data //得到所在行所有键值
                    , field = obj.field; //得到字段
                layer.msg('[ID: ' + data.id + '] ' + field + ' 字段更改为：' + value);
            });

            //监听行工具事件
            table.on('tool(DeviceClassHeadlist)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data, //获得当前行数据
                    layEvent = obj.event; //获得 lay-event 对应的值
                if (layEvent === 'del') {

                    $("#ShowDel").css("display", "block")
                    $("#ShowDel-fade").css("display", "block")

                    $("#XC-Icon").removeClass()
                    $("#hint-value").removeClass()
                    $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                    $("#hint-value").addClass("XC-Font-Red")

                    //设置删除的标题
                    $("#hint-title").html("您确定要删除该设备分类规则吗？ 分类编号：")
                    //设置删除的对象
                    $("#hint-value").html(data.MaterNo)

                    $("#txtMaterNo").val(data.MaterNo)

                    $("#txtOP").val("class")
                }
                else if (layEvent === 'edit') {
                    $('#head-title1').html("修改设备分类规则信息");
                    $('#txtAEFlag').val("23");

                    $("#txtMaterNo").val(data.MaterNo);
                    $("#txtMaterName").val(data.MaterName);
                    $("#txtDeviceKind").val(data.DeviceKind);
                    $("#txtCheckReq").val(data.CheckReq);
                    $("#txtMaintainReq").val(data.MaintainReq);

                    $("#txtDeptNo").val(data.DeptNo);
                    $("#txtDeptName").val(data.DeptName);
                    $("#txtRemark").val(data.Remark);


                    $("#txtMaterNo").attr({ "disabled": "disabled" });

                    $("#div_warning").html("");
                    $("#div_warning").hide();

                    $('#ShowOne').css("display", "block")
                    $('#ShowOne-fade').css("display", "block")
                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }

                else if (layEvent === 'detailC')//显示明细页面
                {
                    $('#head-title2').html("设备分类详情（点检）");
                    //$('#txtAEFlag').val("11");
                    $("#txtAddKind").val("Check");
                    var sKind = $("#txtAddKind").val();//获取点检清单

                    //var sKind = "Maintain";//获取保养清单
                    $("#txtDMaterNo").val(data.MaterNo);
                    $("#txtDMaterName").val(data.MaterName);
                    $("#txtDDeviceKind").val(data.DeviceKind);
                    $("#txtDCheckReq").val(data.CheckReq);
                    $("#txtDMaintainReq").val(data.MaintainReq);

                    $("#txtDDeptNo").val(data.DeptNo);
                    $("#txtDDeptName").val(data.DeptName);
                    $("#txtDRemark").val(data.Remark);


                    $("#txtDMaterNo").attr({ "disabled": "disabled" });

                    $("#div_warningDetail").html("");
                    $("#div_warningDetail").hide();
                    //弹窗显示设备分类明细页
                    ShowDeviceClassDetail(data.MaterNo, sKind);

                    $('#ShowTow').css("display", "block")
                    $('#ShowTow-fade').css("display", "block")

                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }
                else if (layEvent === 'detailM')//显示明细页面
                {
                    $('#head-title2').html("设备分类详情（保养）");
                    //$('#txtAEFlag').val("11");
                    $("#txtAddKind").val("Maintain");
                    var sKind = $("#txtAddKind").val();//获取点检清单

                    //var sKind = "Maintain";//获取保养清单
                    $("#txtDMaterNo").val(data.MaterNo);
                    $("#txtDMaterName").val(data.MaterName);
                    $("#txtDDeviceKind").val(data.DeviceKind);
                    $("#txtDCheckReq").val(data.CheckReq);
                    $("#txtDMaintainReq").val(data.MaintainReq);

                    $("#txtDDeptNo").val(data.DeptNo);
                    $("#txtDDeptName").val(data.DeptName);
                    $("#txtDRemark").val(data.Remark);


                    $("#txtDMaterNo").attr({ "disabled": "disabled" });

                    $("#div_warningDetail").html("");
                    $("#div_warningDetail").hide();
                    //弹窗显示设备分类明细页
                    ShowDeviceClassDetail(data.MaterNo, sKind);

                    $('#ShowTow').css("display", "block")
                    $('#ShowTow-fade').css("display", "block")

                    // layer.alert('编辑行：<br>' + JSON.stringify(data))  //显示所有的行记录
                }

            });


            //  查询表头 --
            $('#DeviceClassBut_open').click(function () {

                var sMaterNo = $("#txtSMaterNo").val();  //
                var sCPVer = encodeURI($("#txtSMaterName").val());  //

                var Data = '';
                var Params = { No: sMaterNo, CPVer: sCPVer, Item: "", Status: "", BDate: "", EDate: "", A: sCPVer, B: "", C: "", D: "", E: "" };
                var Data = JSON.stringify(Params);

                table.reload('DeviceClassHeadlistID', {
                    method: 'post',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=16&Data=' + Data,
                    where: {
                        'No': sMaterNo,
                        'name': sCPVer
                    }, page: {
                        curr: 1
                    }
                });
            });

        });

        function openDialog(n) {
            if (n == 1) { // 新增表头弹窗层显示
                $('#head-title1').html("新增设备分类规则信息");
                $('#txtAEFlag').val("22");


                $("#txtMaterNo").val("");
                $("#txtMaterName").val("");
                $("#txtDeviceKind").val("");
                $("#txtCheckReq").val("");
                $("#txtMaintainReq").val("");

                $("#txtDeptNo").val("");
                $("#txtDeptName").val("");
                $("#txtRemark").val("");


                $("#div_warning").html("");
                $("#div_warning").hide();

                $("#txtMaterNo").removeAttr("disabled");

                $('#ShowOne').css("display", "block")
                $('#ShowOne-fade').css("display", "block")
            }
            else if (n == 2)//新增明细弹窗层显示
            {
                $('#head-title3').html("新增设备分类规则明细");
                $('#txtAEFlag').val("22-1");


                //$("#txtDMaterNoAdd").val(data.CPNo); // 序号
                //$("#txtDMaterNameAdd").val(data.CPVer);//描述
                //$("#txtDRemarkADD").val(data.Remark);//字段类型

                $("#txtDMaterNoAdd").val($("#txtDMaterNo").val()); // 设备分类编号
                $("#txtDMaterNameAdd").val($("#txtDMaterName").val());//设备分类版本
                $("#txtDRemarkADD").val($("#txtDRemark").val());//备注
                $("#txtDeptNoAdd").val($("#txtDDeptNo").val()); // 序号
                $("#txtDeptNameAdd").val($("#txtDDeptName").val());//描述


                $("#txtDCItem").val(""); // 序号
                $("#txtCheckTxt").val("");//描述
                $("#txtType").val("");//字段类型

                $("#div_warningDetailAdd").html("");
                $("#div_warningDetailAdd").hide();

                $('#ShowThree').css("display", "block")
                $('#ShowThree-fade').css("display", "block")
            }
            else//部门选择弹层内容显示
            {
                layui.use('table', function () {
                    var table = layui.table,
                        form = layui.form;
                    table.render({
                        elem: '#table_SelectDeptInfo',
                        id: 'SelectDeptInfoID',
                        url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=12',//初始化界面查询对应的存过程标记信息，获取部门信息
                        height: 'full-195',
                        cellMinWidth: 80,
                        //toolbar: 'default',//工具栏
                        //defaultToolbar:['filter', 'exports'],  //可以过滤网格列的字段
                        count: 50, //数据总数 服务端获得 WNo,WDesc,WKind,Status,CompanyNo,InMan,InDate,Remark
                        limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                        limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择
                        cols: [[
                            { type: 'numbers' },
                            { field: 'DeptNo', title: '部门编号', width: 120 },
                            { field: 'DeptName', title: '部门名称', width: 200 },
                            { field: 'DeptAddr', title: '部门地址', width: 200 },
                            { field: 'Remark', title: '备注', width: 300 },
                            { field: 'op', title: '操作', width: 85, toolbar: '#barDemo_SelectDeptInfo', fixed: 'right' }
                        ]],
                        page: true,


                    });

                    //监听行工具事件
                    table.on('tool(table_SelectDeptInfo)', function (obj) { //注：tool 是工具条事件名，Supplierlist 是 table 原始容器的属性 lay-filter="对应的值"
                        var data = obj.data, //获得当前行数据
                            layEvent = obj.event; //获得 lay-event 对应的值
                        if (layEvent === 'slc') {

                            $("#txtDeptNo").val(data.DeptNo);
                            $("#txtDeptName").val(data.DeptName);

                            /* $("#txtWNo").attr({ "disabled": "disabled" });*/

                            $("#div_warning").html("");
                            $("#div_warning").hide();

                            closeDialog(3);
                        }

                    });


                    //  查询 --
                    $('#btn_DeptInfo_open').click(function () {

                        var sWNo = $("#txtSWNo").val();  //
                        var sName = encodeURI($("#txtSWDesc").val());  //

                        var Data = '';
                        var Params = { No: sWNo, Name: sName, Item: "", Status: "", BDate: "", EDate: "", A: "", B: "", C: "", D: "", E: "" };
                        var Data = JSON.stringify(Params);

                        table.reload('SelectDeptInfoID', {
                            method: 'post',
                            url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=12&Data=' + Data,
                            where: {
                                'No': sWNo,
                                'name': sName
                            }, page: {
                                curr: 1
                            }
                        });
                    });


                });   // table 方法体

                //监听工具条
                $('#ShowFour').css("display", "block")
            }
        }

        function closeDialog(s) {
            if (s == 1) {
                $('#ShowThree').css("display", "none")
                $('#ShowThree-fade').css("display", "none")
            }
            else if (s == 2) {
                $('#ShowOne').css("display", "none")
                $('#ShowOne-fade').css("display", "none")
                $('#ShowTow').css("display", "none")
                $('#ShowTow-fade').css("display", "none")
                //$("#txtAddKind").val("");//获取点检清单或者保养清单内容状态置空
            }
            else {
                $('#ShowFour').css("display", "none")
            }
        }


        /// 显示设备分类明细项信息
        ///daiwanshan
        function ShowDeviceClassDetail(sMaterNo, sKind) {

            //var sMaterNo = $("#txtDMaterNo").val();  //
            //var sCPVer = encodeURI($("#txtDMaterName").val());  //

            var Data = '';
            var Params = { No: sMaterNo, Name: "", Item: "", Status: "", BDate: "", EDate: "", A: sKind, B: "", C: "", D: "", E: "" };
            var Data = JSON.stringify(Params);

            //信息详细信息
            layui.use('table', function () {
                var table = layui.table,
                    form = layui.form;
                table.render({
                    elem: '#DeviceClassDetailList',
                    id: 'DeviceClassDetailID',
                    url: '../Service/PrdAssistAjax.ashx?OP=GetPrdAssistInfo&CFlag=16-1&Data=' + Data,
                    height: '260',
                    cellMinWidth: 80,
                    count: 50, //数据总数 服务端获得
                    limit: 20, //每页显示条数 注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
                    limits: [10, 20, 30, 40, 50], //分页显示每页条目下拉选择 ,ProductVer,SectionNo,SectionName,ProcedureNo,ProcedureName,TargetNo,WorkUnit,ConditionNo,FlowOrder,FlowKind,CompanyNo,InMan,Remark
                    cols: [[
                        { type: 'numbers' },
                        //{ field: 'SNO', title: '序号', width: 40 },
                        { field: 'DCItem', title: '编号', width: 100 },
                        { field: 'CheckTxt', title: '内容', width: 230 },
                        { field: 'Type', title: '数据类型', width: 100 },
                        //{ field: 'Status', title: '状态', width: 100 },
                        //{ field: 'Kind', title: '分类', minWidth: 90 },
                        { field: 'InMan', title: '录入人', minWidth: 90 },
                        { field: 'InDate', title: '录入时间', width: 150 },
                        { field: 'op', title: '操作', width: 230, toolbar: '#barDemo_Detail', fixed: 'right' }
                    ]],
                    page: true
                });

                //监听行工具事件
                table.on('tool(DeviceClassDetailList)', function (obj) { //注：tool 是工具条事件名，orderList 是 table 原始容器的属性 lay-filter="对应的值"
                    var data = obj.data, //获得当前行数据
                        layEvent = obj.event; //获得 lay-event 对应的值


                    if (layEvent == 'detailEdit') {
                        $("#txtAddKind").val(sKind);//装填Check，Maintain

                        $('#txtAEFlag').val("23-1");
                        $("#head-title3").html("修改设备分类明细");

                        $("#txtDMaterNoAdd").val($("#txtDMaterNo").val()); // 设备分类编号
                        $("#txtDMaterNameAdd").val($("#txtDMaterName").val());//设备分类版本
                        $("#txtDRemarkADD").val($("#txtDRemark").val());//备注
                        $("#txtDeptNoAdd").val($("#txtDDeptNo").val());//设备分类版本
                        $("#txtDeptNameAdd").val($("#txtDDeptName").val());//备注

                        $("#txtDCItem").val(data.DCItem); // 序号
                        $("#txtCheckTxt").val(data.CheckTxt);//清场内容
                        $("#txtType").val(data.Type);//字段类型

                        $("#div_warningDetailAdd").html("");

                        //$("#txtWOFileNo").attr({ "disabled": "disabled" });
                        //$("#txtWOFileVer").attr({ "disabled": "disabled" });

                        $('#ShowThree').css("display", "block")
                        $('#ShowThree-fade').css("display", "block");

                    }
                    else if (layEvent == 'detailDel') {

                        $("#ShowDel").css("display", "block")
                        $("#ShowDel-fade").css("display", "block")

                        $("#XC-Icon").removeClass()
                        $("#hint-value").removeClass()
                        $("#XC-Icon").addClass("XC-Icon XC-Btn-Red")
                        $("#hint-value").addClass("XC-Font-Red")

                        //设置删除的标题
                        $("#hint-title").html("您确定要删除该分类项吗？ 编号：")
                        //设置删除的对象
                        $("#hint-value").html(data.DCItem)

                        $("#txtDCItem").val(data.DCItem)

                        $("#txtOP").val("item")

                    }
                    else if (layEvent == 'detailUpm')// 上↑移
                    {
                        var sNo = $("#txtDMaterNo").val();
                        //var sPKind = "Check";
                        var sItemNo = data.DCItem;
                        var sCPTxt = data.CheckTxt;
                        var sSNO = data.SNO;

                        var sFlag = "23-2";
                        var Data = '';
                        var Params = { No: sNo, Name: sCPTxt, Item: sItemNo, A: "", B: "", C: "", D: "", E: "", F: "up", G: "", H: "", I: "", J: "", K: "", L: sSNO, Kind: sKind, Remark: "", Flag: sFlag };
                        var Data = JSON.stringify(Params);
                        $.ajax({
                            type: "POST",
                            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
                            data: { Data: Data },
                            success: function (data) {
                                var parsedJson = jQuery.parseJSON(data);

                                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                    layer.msg('移动成功！');
                                    ShowDeviceClassDetail(sNo, sKind);  // 重新查询


                                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                                    layer.msg('该设备分类项已禁用，不能操作！');
                                }
                                else {
                                    layer.msg('移动失败，请重试！')
                                }
                            },
                            error: function (data) {
                                layer.msg('移动失败2，请重试！')
                            }
                        });

                    }
                    else if (layEvent == 'detailDown')// 下↓移
                    {
                        var sNo = $("#txtDMaterNo").val();
                        //var sKind = "Check";
                        var sItemNo = data.DCItem;
                        var sCPTxt = data.CheckTxt;
                        var sSNO = data.SNO;

                        var sFlag = "23-2";
                        var Data = '';
                        var Params = { No: sNo, Name: sCPTxt, Item: sItemNo, A: "", B: "", C: "", D: "", E: "", F: "dow", G: "", H: "", I: "", J: "", K: "", L: sSNO, Kind: sKind, Remark: "", Flag: sFlag };
                        var Data = JSON.stringify(Params);
                        $.ajax({
                            type: "POST",
                            url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
                            data: { Data: Data },
                            success: function (data) {
                                var parsedJson = jQuery.parseJSON(data);

                                if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                                    layer.msg('移动成功！');

                                    ShowDeviceClassDetail(sNo, sKind);  // 重新查询

                                } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                                    layer.msg('该设备分类项已禁用，不能操作！');
                                }
                                else {
                                    layer.msg('移动失败，请重试！')
                                }
                            },
                            error: function (data) {
                                layer.msg('移动失败2，请重试！')
                            }
                        });

                    }





                });

            });  // layui.use('table', function () {


        }

        function DeviceClassDetailDel_Btn() {

            var sOPFlag = $("#txtOP").val()

            //向服务端发送删除指令 A: sPVer, B: sPath, C: sFile, D: sFNo, E: sFVer, F: sFName,

            //var sWONo = data.CPNo;
            //var sNo = data.CPNo;
            //var sPVer = data.CPVer;

            if (sOPFlag == "class") {
                //向服务端发送禁用指令
                var sMaterNo = $("#txtMaterNo").val();
                //var sKind = data.Kind;
                var sFlag = "24";

                var Data = '';
                var Params = { No: sMaterNo, Name: "", A: "", B: "", C: "", D: "", E: "", F: "", G: "", H: "", I: "", J: "", K: "", L: "", Kind: "", Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);
                $.ajax({
                    type: "POST",
                    url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('删除成功！');
                            $("#ShowDel").css("display", "none")
                            $("#ShowDel-fade").css("display", "none")
                            $('#DeviceClassBut_open').click();  // 重新查询


                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXIST') {
                            layer.msg('该设备分类已用于设备信息，不能删除！');

                        }
                        else {
                            layer.msg('删除失败，请重试！');
                            $('#DeviceClassBut_open').click();  // 重新查询
                        }
                    },
                    error: function (data) {
                        layer.msg('删除失败2，请重试！');
                        $('#DeviceClassBut_open').click();  // 重新查询
                    }
                });
            } else if (sOPFlag == "item") {
                var sNo = $("#txtDMaterNo").val();

                var sKind = $("#txtAddKind").val();//获取点检清单


                /* var sKind = "Check";*/
                var sItemNo = $("#txtDCItem").val();
                //var sFVer = data.FileVer;
                var sFlag = "24-1";

                var Data = '';
                var Params = { No: sNo, Name: "", Item: sItemNo, MNo: "", MName: "", A: "", B: "", C: "", D: "", E: "", F: "", G: "", H: "", I: "", J: "", K: "", L: "", Kind: sKind, Remark: "", Flag: sFlag };
                var Data = JSON.stringify(Params);

                $.ajax({
                    type: "POST",
                    url: "../Service/PrdAssistAjax.ashx?OP=OPPrdAssistInfo&CFlag=" + sFlag,
                    data: { Data: Data },
                    success: function (data) {
                        var parsedJson = jQuery.parseJSON(data);

                        if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Success') {
                            layer.msg('删除成功！');
                            $("#ShowDel").css("display", "none")
                            $("#ShowDel-fade").css("display", "none")
                            ShowDeviceClassDetail(sNo, sKind);  // 重新查询

                        }
                        else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_EXISTPRD') {
                            layer.msg('该文件已使用，不能操作！');
                            // $('#Fin_SearchOpen').click();
                        } else if (parsedJson != undefined && parsedJson != '' && parsedJson.Msg == 'Y_Status') {
                            layer.msg('该工序版本已禁用，不能操作！');
                            // $('#Fin_SearchOpen').click();
                        }
                        else {
                            layer.msg('删除失败，请重试！');
                            // $('#Fin_SearchOpen').click();
                        }
                    },
                    error: function (data) {
                        layer.msg('删除失败2，请重试！');
                        // $('#Fin_SearchOpen').click();
                    }
                });
            }
        }

        function closeDelDialog() {
            $("#ShowDel").css("display", "none")
            $("#ShowDel-fade").css("display", "none")
        }
    </script>

    <script type="text/javascript">
        $(function () {
            $(".find_span").click(function () {
                $("#open").show();
                $(this).hide();
                $(".find_span1").show();
                $("#but_close02").hide();
                $("#but_open").hide();

            })
            $(".find_span1").click(function () {
                $("#open").hide();
                $(this).hide();
                $(".find_span").show();
                $("#but_open").show();
                $("#but_close02").show();

            })
        })
    </script>

    <style type="text/css">

        #HDiv {
            position: fixed;
            top: 0;
            width: 100%;
            height: 40px;
        }

        .table > tbody > tr > td {
            border: 0px;
        }

        /*.black_overlay {
            display: none;
            position: absolute;
            top: 0%;
            left: 0%;
            width: 100%;
            height: 100%;
            background-color: #bbbcc7;
            z-index: 1001;
            -moz-opacity: 0.8;
            opacity: .80;
            filter: alpha(opacity=60);
        }*/

        .wangid_conbox td, .wangid_conbox th {
            font-size: 12px
        }

        .find_input {
            width: 13%;
            height: 28px;
            border: solid 1px #ccc;
            border-radius: 2px;
            margin-right: 1%;
            margin-left: 0.5%;
        }

        #ShowOne .XC-Form-block-Item > span {
            width: 78px;
            line-height: 30px;
        }

        #ShowTow .XC-Form-block-Item > span {
            width: 78px;
        }

        #ShowThree .XC-Form-block-Item > span {
            width: 85px;
        }
    </style>

</head>
<body>
    <div class="div_find">
        <p>
            <label class="find_labela">设备分类编号</label> <input type="text" id="txtSMaterNo" class="find_input" />
            <label class="find_labela">设备分类名称</label><input type="text" id="txtSMaterName" class="find_input" />
            <!--<label class="find_labela">描述：</label><input type="text" id="txtSCPTxt" class="find_input" />-->
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="DeviceClassBut_open">搜索</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" onclick="javascript:openDialog(1)">添加</button>
        </p>
    </div>

    <div class="wangid_conbox">
        <!-- 下面写内容   -->
        <table class="layui-hide" id="DeviceClassHeadlist" lay-filter="DeviceClassHeadlist"></table>

        <script type="text/html" id="barDemo_Head">
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="edit">修改</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detailC" style="width:70px">点检详情</button>
            <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detailM" style="width:70px">保养详情</button>
            <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="del">删除</button>
        </script>

    </div>

    <!--设备分类规则表头新增和修改增弹层-->
    <div class="XC-modal XC-modal-xl" id="ShowOne">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title1">标题</span>
            <span class="head-close" onclick="closeDialog(2)">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <form class="XC-Form-block">
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">设备分类编号<span class="XC-Font-Red">*</span></span>
                        <input type="text" class="XC-Input-block" id="txtMaterNo" name="txtMaterNo" value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">设备分类名称<span class="XC-Font-Red">*</span></span>
                        <input type="text" class="XC-Input-block" id="txtMaterName" name="txtMaterName" value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">管理部门编号</span>
                        <input type="text" class="XC-Input-block" id="txtDeptNo" readonly="readonly" name="txtDeptNo" value="" />
                        <input type="button" value="选择" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="btn_Dept_open" onclick="openDialog(3)" style="height:30px;margin-left:5px;" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">管理部门名称</span>
                        <input type="text" class="XC-Input-block" id="txtDeptName" name="txtDeptName" value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Select-block">点检要求</span>
                        <select class="XC-Select-block" id="txtCheckReq">
                            <option></option>
                            <option>工作日点检</option>
                            <option>使用前点检</option>
                            <option>周点检</option>
                        </select>
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Select-block">保养要求</span>
                        <select class="XC-Select-block" id="txtMaintainReq">
                            <option></option>
                            <option>年度</option>
                            <option>季度</option>
                            <option>月度</option>
                            <option>周度</option>
                        </select>
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Select-block">设备类型</span>
                        <select class="XC-Select-block" id="txtDeviceKind">
                            <option></option>
                            <option>设备</option>
                            <option>工装</option>
                            <option>仪器</option>
                        </select>
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Textarea-block">备注</span>
                        <textarea type="text" class="XC-Textarea-block" id="txtRemark" name="txtRemark" value=""></textarea>
                    </div>
                </form>
            </div>
        </div>

        <div class="input-group" style="display:none; ">
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAddKind" name="txtAddKind" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtDKL" name="txtDKL" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtAEFlag" name="txtAEFlag" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInMan" name="txtInMan" />
            <span class="input-group-addon">.</span>
            <input type="text" class="form-control" id="txtInName" name="txtInName" />
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="DeviceClassHeadSave_Btn">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="DeviceClassHeadSaveClose" onclick='closeDialog(2)'>关闭</button>
            </div>
        </div>
    </div>
    <div id="ShowOne-fade" class="black_overlay">
    </div>

    <!--显示设备分类规则明细弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowTow">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title2">设备分类详情</span>
            <span class="head-close" onclick="closeDialog(2)">X</span>
        </div>
        <div class="XC-modal-body" style="padding:10px">
            <div class="XC-modal-xl-center">
                <div style="border-bottom: solid 1px #ccc;padding-bottom:12px">
                    <span style="font-weight: bold; ">设备分类</span>
                </div>

                <div style="display:flex;margin-top:10px">
                    <div style="width:100%">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">设备分类编号</span>
                                <input type="text" class="XC-Input-block" id="txtDMaterNo" name="txtDMaterNo" readonly=readonly placeholder="系统自动产生" />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">管理部门编号</span>
                                <input type="text" class="XC-Input-block" id="txtDDeptNo" name="txtDDeptNo" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">点检要求</span>
                                <input type="text" class="XC-Input-block" id="txtDCheckReq" name="txtDCheckReq" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">设备类型</span>
                                <input type="text" class="XC-Input-block" id="txtDDeviceKind" name="txtDDeviceKind" readonly=readonly />
                            </div>
                        </form>
                    </div>
                    <div style="width:100%">
                        <form class="XC-Form-block">
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">设备分类名称</span>
                                <input type="text" class="XC-Input-block" id="txtDMaterName" name="txtDMaterName" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">管理部门名称</span>
                                <input type="text" class="XC-Input-block" id="txtDDeptName" name="txtDDeptName" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Input-block">保养要求</span>
                                <input type="text" class="XC-Input-block" id="txtDMaintainReq" name="txtDMaintainReq" readonly=readonly />
                            </div>
                            <div class="XC-Form-block-Item">
                                <span class="XC-Span-Textarea-block">备注</span>
                                <textarea class="XC-Textarea-block" id="txtDRemark" name="txtDRemark" readonly=readonly style="max-height:45px"> </textarea>
                            </div>
                        </form>
                    </div>
                </div>

                <div style="display:flex;justify-content:space-between;border-bottom: solid 1px #ccc;padding-bottom:3px">
                    <span style="font-weight: bold; ">明细内容</span>
                    <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="DeviceClassDetailAdd_Btn" onclick='openDialog(2)'>添加</button>
                </div>

                <div class="wangid_conbox">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="DeviceClassDetailList" lay-filter="DeviceClassDetailList"></table>

                    <script type="text/html" id="barDemo_Detail">
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detailEdit">修改</button>
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detailUpm">上↑移</button>
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="detailDown">下↓移</button>
                        <button type="button" class="XC-Btn-md XC-Btn-Red XC-Size-xs" lay-event="detailDel">删除</button>
                    </script>
                </div>
            </div>
        </div>
    </div>
    <div id="ShowTow-fade" class="black_overlay">
    </div>

    <!--设备分类规则明细项修改和新增弹窗-->
    <div class="XC-modal XC-modal-xl" id="ShowThree">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title3">点检/保养信息</span>
            <span class="head-close" onclick="closeDialog(1)">X</span>
        </div>
        <div class="XC-modal-body">
            <div class="XC-modal-xl-center">
                <form class="XC-Form-block">
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">设备分类编号</span>
                        <input type="text" class="XC-Input-block" id="txtDMaterNoAdd" name="txtDMaterNoAdd" readonly=readonly placeholder="系统自动产生" value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">设备分类名称</span>
                        <input type="text" class="XC-Input-block" id="txtDMaterNameAdd" name="txtDMaterNameAdd" readonly=readonly value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">管理部门编号</span>
                        <input type="text" class="XC-Input-block" id="txtDeptNoAdd" name="txtDeptNoAdd" readonly=readonly value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">管理部门名称</span>
                        <input type="text" class="XC-Input-block" id="txtDeptNameAdd" name="txtDeptNameAdd" readonly=readonly value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Textarea-block">备注</span>
                        <textarea type="text" class="XC-Textarea-block" id="txtDRemarkADD" name="txtDRemarkADD" readonly="readonly" value=""></textarea>
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">序号</span>
                        <input type="text" class="XC-Input-block" id="txtDCItem" name="txtDCItem" readonly=readonly placeholder="系统自动产生" value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">点检/保养内容<span class="XC-Font-Red">*</span></span>
                        <input type="text" class="XC-Input-block" id="txtCheckTxt" name="txtCheckTxt" value="" />
                    </div>
                    <div class="XC-Form-block-Item">
                        <span class="XC-Span-Input-block">数据类型<span class="XC-Font-Red">*</span></span>
                        <select class="XC-Select-block" id="txtType">
                            <option></option>
                            <option>布尔型</option>
                            <option>文本型</option>
                            <option>数值型</option>
                            <option>日期型</option>
                        </select>
                    </div>
                </form>
            </div>
        </div>

        <div class="XC-modal-footer">
            <div class="OPBtn">
                <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="DeviceClassDetailSave_Btn">保存</button>
                <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" id="DeviceClassHeadDetailSaveClose" onclick='closeDialog(1)'>关闭</button>
            </div>
        </div>
    </div>
    <div id="ShowThree-fade" class="black_overlay">
    </div>

    <!--选择部门-->
    <div class="XC-modal XC-modal-xl" id="ShowFour">
        <div class="XC-modal-head">
            <span class="head-title" id="head-title4">选择部门信息</span>
            <span class="head-close" onclick="closeDialog(3)">X</span>
        </div>
        <div class="XC-modal-body" style="padding:5px;">
            <div>
                <form class="XC-Form-inline-block" style="margin-top:10px">
                    <div class="XC-Form-Item" style="margin-bottom:2px">
                        <span class="XC-Span-Input-block">部门编号</span>
                        <input type="text" class="XC-Input-block" id="txtSWNo" name="txtSWNo" value="" />
                    </div>
                    <div class="XC-Form-Item">
                        <span class="XC-Span-Input-block">部门名称</span>
                        <input type="text" class="XC-Input-block" id="txtSWDesc" name="txtSWDesc" value="" />
                    </div>
                    <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" id="btn_DeptInfo_open">搜索</button>
                </form>
            </div>
            <div class="XC-modal-xl-center">
                <div style=" font-weight: bold;padding-bottom:10px;">
                    基本信息
                </div>
                <div class="wangid_conbox">
                    <!-- 下面写内容   -->
                    <table class="layui-hide" id="table_SelectDeptInfo" lay-filter="table_SelectDeptInfo"></table>

                    <script type="text/html" id="barDemo_SelectDeptInfo">
                        <button type="button" class="XC-Btn-md XC-Btn-Green XC-Size-xs" lay-event="slc">选择</button>
                    </script>
                </div>
            </div>
        </div>
    </div>

    <!--操作提示框 删除设备分类详情\设备分类规则-->
    <div class="XC-center">
        <div class="XC-modal XC-modal-xs" id="ShowDel" style="z-index:1003">
            <div class="XC-modal-head">
                <span class="head-title">操作提示</span>
                <span class="head-close" onclick="closeDelDialog()">X</span>
            </div>
            <div class="XC-modal-body">
                <div class="XC-modal-content XC-Size-sm">
                    <span id="XC-Icon">!</span> <span id="hint-title"></span><span id="hint-value"></span>
                </div>
            </div>

            <div class="input-group" style="display:none; ">
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtDCItem" name="txtDCItem" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtMaterNo" name="txtMaterNo" />
                <span class="input-group-addon">.</span>
                <input type="text" class="form-control" id="txtOP" name="txtOP" />
            </div>

            <div class="XC-modal-footer">
                <div class="OPBtn">
                    <button type='button' class="XC-Btn-lg XC-Btn-Green XC-Size-sm" id="DeviceClassDetailDel_Btn" onclick="DeviceClassDetailDel_Btn()">确定</button>
                    <button type='button' class="XC-Btn-lg XC-Btn-Gray XC-Size-sm" onclick="closeDelDialog()">取消</button>
                </div>
            </div>
        </div>
        <div id="ShowDel-fade" class="black_overlay" style="z-index:1002">
        </div>
    </div>




    <!--消息提示-->
    <div class="XC-center">
        <div id="Success" class="XC-success"></div>
        <div id="Error" class="XC-error"></div>
        <div id="Warning" class="XC-warning"></div>
    </div>
</body>
</html>